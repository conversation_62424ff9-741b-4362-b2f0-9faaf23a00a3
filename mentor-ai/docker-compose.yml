version: '3.9'

services:
  # API Gateway
  kong:
    image: kong:3.4-alpine
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-db
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kongpass
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_DECLARATIVE_CONFIG: /etc/kong/kong.yml
    ports:
      - "8000:8000"  # Proxy
      - "8443:8443"  # Proxy SSL
      - "8001:8001"  # Admin API
      - "8444:8444"  # Admin API SSL
    volumes:
      - ./infrastructure/kong/kong.yml:/etc/kong/kong.yml
    depends_on:
      - kong-db
    networks:
      - mentor-ai-network

  kong-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: kong
      POSTGRES_USER: kong
      POSTGRES_PASSWORD: kongpass
    volumes:
      - kong-db-data:/var/lib/postgresql/data
    networks:
      - mentor-ai-network

  # Microservices
  flashcards-service:
    build:
      context: ./backend/microservices/flashcards
      dockerfile: Dockerfile
    environment:
      MONGODB_URL: mongodb://mongodb:27017/flashcards
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      REDIS_URL: redis://redis:6379
      SERVICE_NAME: flashcards-service
    depends_on:
      - mongodb
      - kafka
      - redis
    networks:
      - mentor-ai-network
    deploy:
      replicas: 2

  ai-service:
    build:
      context: ./backend/microservices/ai
      dockerfile: Dockerfile
    environment:
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      GROQ_API_KEY: ${GROQ_API_KEY}
      MONGODB_URL: mongodb://mongodb:27017/ai
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      REDIS_URL: redis://redis:6379
      SERVICE_NAME: ai-service
    depends_on:
      - mongodb
      - kafka
      - redis
    networks:
      - mentor-ai-network
    deploy:
      replicas: 2
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  user-service:
    build:
      context: ./backend/microservices/user
      dockerfile: Dockerfile
    environment:
      POSTGRES_URL: ********************************************/users
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      REDIS_URL: redis://redis:6379
      SERVICE_NAME: user-service
      JWT_SECRET: ${JWT_SECRET}
    depends_on:
      - postgres
      - kafka
      - redis
    networks:
      - mentor-ai-network

  analytics-service:
    build:
      context: ./backend/microservices/analytics
      dockerfile: Dockerfile
    environment:
      CLICKHOUSE_URL: clickhouse://clickhouse:9000/analytics
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      REDIS_URL: redis://redis:6379
      SERVICE_NAME: analytics-service
    depends_on:
      - clickhouse
      - kafka
      - redis
    networks:
      - mentor-ai-network

  # Frontend (Micro-frontends)
  frontend-shell:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    environment:
      VITE_API_URL: http://localhost:8000/api
    ports:
      - "5000:5000"
    networks:
      - mentor-ai-network

  flashcards-module:
    build:
      context: ./frontend/src/micro-frontends/flashcards
      dockerfile: Dockerfile
    ports:
      - "5001:5001"
    networks:
      - mentor-ai-network

  # Databases
  mongodb:
    image: mongo:7.0
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: adminpass
    volumes:
      - mongodb-data:/data/db
    ports:
      - "27017:27017"
    networks:
      - mentor-ai-network

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: mentor_ai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - mentor-ai-network

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - mentor-ai-network

  clickhouse:
    image: clickhouse/clickhouse-server:23-alpine
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: password
    volumes:
      - clickhouse-data:/var/lib/clickhouse
    ports:
      - "8123:8123"  # HTTP
      - "9000:9000"  # Native
    networks:
      - mentor-ai-network

  # Event Streaming
  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper
    networks:
      - mentor-ai-network

  kafka:
    image: confluentinc/cp-kafka:7.5.0
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    volumes:
      - kafka-data:/var/lib/kafka/data
    ports:
      - "29092:29092"
    networks:
      - mentor-ai-network

  # Observability Stack
  prometheus:
    image: prom/prometheus:v2.47.0
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    volumes:
      - ./infrastructure/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - mentor-ai-network

  grafana:
    image: grafana/grafana:10.1.0
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: "false"
    volumes:
      - ./infrastructure/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./infrastructure/grafana/datasources:/etc/grafana/provisioning/datasources
      - grafana-data:/var/lib/grafana
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - mentor-ai-network

  jaeger:
    image: jaegertracing/all-in-one:1.49
    environment:
      COLLECTOR_OTLP_ENABLED: "true"
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Collector HTTP
      - "4317:4317"    # OTLP gRPC
      - "4318:4318"    # OTLP HTTP
    networks:
      - mentor-ai-network

  elasticsearch:
    image: elasticsearch:8.10.2
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - mentor-ai-network

  kibana:
    image: kibana:8.10.2
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - mentor-ai-network

  logstash:
    image: logstash:8.10.2
    volumes:
      - ./infrastructure/logstash/pipeline:/usr/share/logstash/pipeline
    ports:
      - "5044:5044"
      - "5000:5000"
    depends_on:
      - elasticsearch
    networks:
      - mentor-ai-network

volumes:
  kong-db-data:
  mongodb-data:
  postgres-data:
  redis-data:
  clickhouse-data:
  zookeeper-data:
  kafka-data:
  prometheus-data:
  grafana-data:
  elasticsearch-data:

networks:
  mentor-ai-network:
    driver: bridge