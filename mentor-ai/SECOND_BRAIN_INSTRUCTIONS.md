# Instruções do Second Brain

## Visão Geral

O Second Brain é um assistente de IA integrado ao Mentor AI que ajuda com dúvidas médicas, organização de estudos e revisão de conteúdo. Ele utiliza processamento de linguagem natural avançado para fornecer respostas relevantes e contextuais.

## Como Acessar

1. Inicie o sistema Mentor AI usando o script principal:
   ```bash
   ./MENTOR_AI_ULTRA_SYSTEM_FIXED.sh
   ```

2. Escolha a opção 2 para iniciar todos os serviços.

3. Acesse o Second Brain através do navegador:
   ```
   http://localhost:8082/second-brain
   ```

## Recursos Disponíveis

- **Chat Interativo**: Faça perguntas e receba respostas em tempo real
- **Histórico de Conversas**: Suas conversas são salvas para referência futura
- **Streaming de Respostas**: As respostas são exibidas gradualmente, como em uma conversa natural
- **Botão de Teste**: Verifique se a conexão com o backend está funcionando corretamente

## Exemplos de Uso

Você pode fazer perguntas como:

- "Como funciona o ciclo cardíaco?"
- "Quais são os principais sintomas da diabetes tipo 2?"
- "Explique o mecanismo de ação dos antibióticos beta-lactâmicos"
- "Como posso organizar meus estudos para o ENEM?"
- "Quais são as melhores técnicas de revisão espaçada para medicina?"

## Solução de Problemas

Se você encontrar problemas com o Second Brain, tente as seguintes soluções:

1. **Verifique se todos os serviços estão rodando**:
   ```bash
   ./CHECK_STATUS.sh
   ```

2. **Corrija problemas específicos do Second Brain**:
   ```bash
   ./SECOND_BRAIN_FIX.sh
   ```

3. **Corrija problemas de dependências do frontend**:
   ```bash
   ./FIX_FRONTEND_DEPS.sh
   ```

4. **Verifique se o Second Brain está funcionando corretamente**:
   ```bash
   ./VERIFY_SECOND_BRAIN_WORKING.sh
   ```

5. **Reinicie todo o sistema**:
   ```bash
   ./MENTOR_AI_ULTRA_SYSTEM_FIXED.sh
   ```

## Logs e Depuração

Os logs do sistema estão disponíveis em:

- **Frontend**: `logs/frontend.log`
- **FastAPI**: `logs/fastapi.log`
- **Django**: `logs/django.log`

Para ver os logs em tempo real:

```bash
tail -f logs/fastapi.log
```

## Endpoints da API

O Second Brain expõe os seguintes endpoints:

- **Teste**: `http://localhost:8001/api/chat/test`
- **Histórico**: `http://localhost:8001/api/chat/history`
- **Limpar Histórico**: `http://localhost:8001/api/chat/clear` (POST)
- **Chat Stream**: `http://localhost:8001/api/chat/stream?message=sua_pergunta` (GET)

## Arquitetura

O Second Brain é composto por:

1. **Frontend**: Interface Vue.js que permite interagir com o assistente
2. **Backend FastAPI**: API que processa as solicitações e gera respostas
3. **Modelo de IA**: Claude da Anthropic (quando configurado) ou simulação local

## Configuração Avançada

Para configurar o Claude da Anthropic:

1. Crie um arquivo `.env` no diretório `backend/fastapi_app` com:
   ```
   ANTHROPIC_API_KEY=sua_chave_api_aqui
   ```

2. Reinicie o sistema:
   ```bash
   ./MENTOR_AI_ULTRA_SYSTEM_FIXED.sh
   ```

## Suporte

Se você encontrar problemas persistentes, verifique os logs para obter mais detalhes ou execute o script de verificação completa:

```bash
./VERIFY_SECOND_BRAIN_WORKING.sh
``` 