#!/bin/bash

# Frontend Cleanup Script for Mentor AI
# This script removes conflicting, duplicate, and unused files identified in the analysis

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_section() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Check if we're in the right directory
if [ ! -d "frontend/src/components" ]; then
    print_error "This script must be run from the mentor-ai root directory"
    exit 1
fi

print_section "Frontend Cleanup - Mentor AI"
print_status "Starting cleanup of conflicting and unused files..."

# Create backup directory
BACKUP_DIR="frontend_cleanup_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
print_status "Created backup directory: $BACKUP_DIR"

# Function to safely remove file with backup
safe_remove() {
    local file_path="$1"
    local reason="$2"
    
    if [ -f "$file_path" ]; then
        # Create backup
        local backup_path="$BACKUP_DIR/$(basename "$file_path")"
        cp "$file_path" "$backup_path"
        
        # Remove original
        rm "$file_path"
        print_status "Removed: $file_path ($reason)"
    else
        print_warning "File not found: $file_path"
    fi
}

# Phase 1: Remove Backup Files (Safest)
print_section "Phase 1: Removing Explicit Backup Files"

# Home component backups
safe_remove "frontend/src/components/Home-backup.vue" "backup file"
safe_remove "frontend/src/components/Home-Enhanced.vue" "backup file"
safe_remove "frontend/src/components/HomeRedesigned.vue" "backup file"
safe_remove "frontend/src/components/HomeUltimateArchitecture.vue" "backup file"
safe_remove "frontend/src/components/HomeUltraAdvanced.vue" "backup file"
safe_remove "frontend/src/components/Home_backup.vue" "backup file"

# App Header/Footer backups
safe_remove "frontend/src/components/AppHeader-Enhanced.vue" "backup file"
safe_remove "frontend/src/components/AppHeader-backup.vue" "backup file"
safe_remove "frontend/src/components/AppHeaderFixed.vue" "backup file"
safe_remove "frontend/src/components/AppFooter-Enhanced.vue" "backup file"
safe_remove "frontend/src/components/AppFooter-backup.vue" "backup file"

# Other explicit backups
safe_remove "frontend/src/components/SecondBrainChat.vue.bak" "backup file"

# Phase 2: Remove Unused Thanos Components
print_section "Phase 2: Removing Unused Thanos Components"

safe_remove "frontend/src/components/ThanosFixed.vue" "unused variant"
safe_remove "frontend/src/components/ThanosRedesigned.vue" "unused variant"
safe_remove "frontend/src/components/ThanosSimple.vue" "unused variant"
# Note: Keeping ThanosUltra.vue as it might be reactivated

# Phase 3: Remove Unused Session Components
print_section "Phase 3: Removing Unused Session Components"

safe_remove "frontend/src/components/SessionModal.vue" "unused variant"
safe_remove "frontend/src/components/SessionModalOptimized.vue" "unused variant"
safe_remove "frontend/src/components/SessionTimerModal.vue" "unused variant"

# Phase 4: Remove Unused Study Components
print_section "Phase 4: Removing Unused Study Components"

safe_remove "frontend/src/components/StudySessionsAdvanced.vue" "unused variant"
safe_remove "frontend/src/components/StudySessionsFixed.vue" "unused variant"
safe_remove "frontend/src/components/StudySessionsOptimized.vue" "unused variant"

# Phase 5: Remove Unused Analytics Components
print_section "Phase 5: Removing Unused Analytics Components"

safe_remove "frontend/src/components/AnalyticsDashboard.vue" "unused variant"
safe_remove "frontend/src/components/AdvancedAnalytics.vue" "unused variant"
safe_remove "frontend/src/components/AdvancedAnalyticsDashboard.vue" "unused variant"
safe_remove "frontend/src/components/SuperPerformanceDashboard.vue" "unused variant"

# Phase 6: Remove Debug/Test Components
print_section "Phase 6: Removing Debug and Test Components"

safe_remove "frontend/src/components/HelloWorld.vue" "default Vue component"
safe_remove "frontend/src/components/PurpleScreenKiller.vue" "debug component"

# Phase 7: Remove Unused Study Assistant Variants
print_section "Phase 7: Removing Unused Study Assistant Variants"

safe_remove "frontend/src/components/StudyAssistant3DVisualizer.vue" "unused variant"
safe_remove "frontend/src/components/StudyAssistantAR.vue" "unused variant"
safe_remove "frontend/src/components/StudyAssistantCollaboration.vue" "unused variant"
safe_remove "frontend/src/components/StudyAssistantNeural.vue" "unused variant"
safe_remove "frontend/src/components/StudyAssistantUltra.vue" "unused variant"
safe_remove "frontend/src/components/StudyAssistantVoice.vue" "unused variant"

# Phase 8: Remove Unused UI Components
print_section "Phase 8: Removing Unused UI Components"

safe_remove "frontend/src/components/InteractiveCard.vue" "unused component"
safe_remove "frontend/src/components/FloatingActionButton.vue" "unused component"
safe_remove "frontend/src/components/ScrollAnimator.vue" "unused component"
safe_remove "frontend/src/components/ParticlesBackground.vue" "unused component"
safe_remove "frontend/src/components/NotificationBellDesigns.vue" "unused variant"

# Phase 9: Remove Unused Specialized Components
print_section "Phase 9: Removing Unused Specialized Components"

safe_remove "frontend/src/components/RelationshipGraph.vue" "unused component"
safe_remove "frontend/src/components/ContentAnalyzer.vue" "unused component"
safe_remove "frontend/src/components/DocumentProcessor.vue" "unused component"
safe_remove "frontend/src/components/DocumentViewer.vue" "unused component"
safe_remove "frontend/src/components/ModelConfigurator.vue" "unused component"

# Phase 10: Remove Unused Services
print_section "Phase 10: Removing Unused Services"

safe_remove "frontend/src/services/MockAIService.js" "development only"
safe_remove "frontend/src/services/whisperService.js" "unused service"
safe_remove "frontend/src/services/caseAnalyzerService.js" "unused service"
safe_remove "frontend/src/services/studyAssistantService.js" "unused service"

# Phase 11: Remove Unused Styles
print_section "Phase 11: Removing Unused Style Files"

safe_remove "frontend/src/styles/second-brain-clean.css" "unused style"
safe_remove "frontend/src/styles/second-brain-compact.css" "unused style"
safe_remove "frontend/src/styles/second-brain-elegant.css" "unused style"
safe_remove "frontend/src/styles/second-brain-modern.css" "unused style"
safe_remove "frontend/src/styles/dashboard-fix.css" "unused style"
safe_remove "frontend/src/styles/app-header-fix.css" "unused style"
safe_remove "frontend/src/styles/app-header-fix-final.css" "unused style"
safe_remove "frontend/src/styles/app-header-ultimate-fix.css" "unused style"
safe_remove "frontend/src/styles/app-header-definitive-fix.css" "unused style"

# Summary
print_section "Cleanup Summary"
print_status "Cleanup completed successfully!"
print_status "Backup created in: $BACKUP_DIR"

# Count files in backup
backup_count=$(find "$BACKUP_DIR" -type f | wc -l)
print_status "Files backed up: $backup_count"

print_warning "To restore any file, copy it back from the backup directory"
print_warning "To permanently delete backups: rm -rf $BACKUP_DIR"

print_section "Next Steps"
echo "1. Test the application to ensure everything works correctly"
echo "2. Run 'npm run build' to verify the build process"
echo "3. If everything works, you can delete the backup directory"
echo "4. Consider implementing the AI recommender enhancements outlined in the analysis report"

print_status "Frontend cleanup completed!"
