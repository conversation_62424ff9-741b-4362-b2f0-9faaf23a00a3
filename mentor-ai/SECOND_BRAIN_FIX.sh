#!/bin/bash

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                  MENTOR AI - SECOND BRAIN FIX                                ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

# Cores para formatação
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para exibir mensagens com formatação
log_info() {
    echo -e "${BLUE}ℹ ${NC}$1"
}

log_success() {
    echo -e "${GREEN}✓ ${NC}$1"
}

log_warning() {
    echo -e "${YELLOW}⚠️ ${NC}$1"
}

log_error() {
    echo -e "${RED}✗ ${NC}$1"
}

# Função para exibir cabeçalho de seção
section() {
    echo -e "\n${BLUE}[$(($1))/${TOTAL_STEPS}] ${NC}${YELLOW}$2${NC}"
    echo "───────────────────────────────────────────────"
}

# Número total de etapas
TOTAL_STEPS=5

# Diretório base
BASE_DIR="$(dirname "$0")"
cd "$BASE_DIR"

echo -e "${YELLOW}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${YELLOW}║                  MENTOR AI - SECOND BRAIN FIX                                ║${NC}"
echo -e "${YELLOW}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Etapa 1: Verificar status do sistema
section 1 "VERIFICANDO STATUS DO SISTEMA"

# Verificar FastAPI
log_info "Verificando FastAPI..."
if curl -s http://localhost:8001/health | grep -q "healthy"; then
    log_success "FastAPI está rodando"
else
    log_error "FastAPI não está rodando. Por favor, inicie o FastAPI primeiro."
    log_info "Execute: ./MENTOR_AI_ULTRA_SYSTEM_FIXED.sh"
    exit 1
fi

# Verificar Frontend
log_info "Verificando Frontend..."
if curl -s http://localhost:8082 > /dev/null; then
    log_success "Frontend está rodando"
else
    log_error "Frontend não está rodando. Por favor, inicie o Frontend primeiro."
    log_info "Execute: ./MENTOR_AI_ULTRA_SYSTEM_FIXED.sh"
    exit 1
fi

# Etapa 2: Testando API do Second Brain
section 2 "TESTANDO API DO SECOND BRAIN"

log_info "Testando endpoint de teste do chat..."
TEST_RESPONSE=$(curl -s http://localhost:8001/api/chat/test)
if [[ $TEST_RESPONSE == *"Test stream working"* ]]; then
    log_success "Endpoint de teste do chat está funcionando"
else
    log_error "Endpoint de teste do chat não está funcionando"
    log_info "Resposta: $TEST_RESPONSE"
    exit 1
fi

log_info "Testando endpoint de histórico do chat..."
HISTORY_RESPONSE=$(curl -s http://localhost:8001/api/chat/history)
if [[ $? -eq 0 ]]; then
    log_success "Endpoint de histórico do chat está funcionando"
else
    log_error "Endpoint de histórico do chat não está funcionando"
    exit 1
fi

log_info "Testando endpoint de limpeza do histórico..."
CLEAR_RESPONSE=$(curl -s -X POST http://localhost:8001/api/chat/clear)
if [[ $? -eq 0 ]]; then
    log_success "Endpoint de limpeza do histórico está funcionando"
else
    log_error "Endpoint de limpeza do histórico não está funcionando"
    exit 1
fi

# Etapa 3: Verificando configuração do frontend
section 3 "VERIFICANDO CONFIGURAÇÃO DO FRONTEND"

log_info "Verificando arquivo SecondBrainService.js..."
if grep -q "streamMessage" frontend/src/services/SecondBrainService.js; then
    log_success "SecondBrainService.js está configurado corretamente"
else
    log_error "SecondBrainService.js não está configurado corretamente"
    exit 1
fi

log_info "Verificando arquivo SecondBrainChat.vue..."
if grep -q "testStream" frontend/src/components/SecondBrainChat.vue; then
    log_success "SecondBrainChat.vue está configurado corretamente"
else
    log_error "SecondBrainChat.vue não está configurado corretamente"
    exit 1
fi

# Etapa 4: Verificando rotas do frontend
section 4 "VERIFICANDO ROTAS DO FRONTEND"

log_info "Verificando rotas do frontend..."
if grep -q "second-brain" frontend/src/router/index.js; then
    log_success "Rota do Second Brain está configurada corretamente"
else
    log_warning "Rota do Second Brain pode não estar configurada corretamente"
    log_info "Verifique o arquivo frontend/src/router/index.js"
fi

# Etapa 5: Testando integração completa
section 5 "TESTANDO INTEGRAÇÃO COMPLETA"

log_info "Testando integração completa..."
log_info "Enviando mensagem de teste para o Second Brain..."

TEST_MESSAGE="Teste de integração do Second Brain"
STREAM_TEST=$(curl -s -N -H "Accept: text/event-stream" "http://localhost:8001/api/chat/stream?message=${TEST_MESSAGE// /%20}")

if [[ $? -eq 0 ]]; then
    log_success "Integração completa testada com sucesso"
else
    log_error "Falha no teste de integração completa"
    exit 1
fi

echo -e "\n${GREEN}═══════════════════════════════════════════════════════════════════════════════${NC}"
echo -e "${GREEN}                    SECOND BRAIN CORRIGIDO COM SUCESSO!                         ${NC}"
echo -e "${GREEN}═══════════════════════════════════════════════════════════════════════════════${NC}"

echo -e "\nPara acessar o Second Brain:"
echo -e "${BLUE}http://localhost:8082/second-brain${NC}"

echo -e "\nPara verificar o status do sistema a qualquer momento:"
echo -e "${BLUE}./CHECK_STATUS.sh${NC}"

echo -e "\nSistema pronto e funcionando! 🚀" 