#!/bin/bash

# Color definitions
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

BASE_URL="http://localhost:8001/api/thanos"

echo -e "${BLUE}==========================================${NC}"
echo -e "${BLUE}== THANOS AI API INTEGRATION TEST ==${NC}"
echo -e "${BLUE}==========================================${NC}"

# Test health endpoint
echo -e "\n${YELLOW}[1/5]${NC} Testing health endpoint..."
HEALTH_RESPONSE=$(curl -s $BASE_URL/health)
if [[ $HEALTH_RESPONSE == *"status"*"ok"* ]]; then
    echo -e "${GREEN}✓ Health endpoint OK${NC}"
else
    echo -e "${RED}✗ Health endpoint failed${NC}"
    echo "Response: $HEALTH_RESPONSE"
fi

# Create a test document with richer content for RAG testing
echo -e "\n${YELLOW}[2/5]${NC} Creating test document with medical content for RAG testing..."
cat > /tmp/test_thanos.txt << 'EOF'
Cardiologia: Estudo e Tratamento de Doenças Cardíacas

A cardiologia é a especialidade médica que se dedica ao diagnóstico e tratamento das doenças que acometem o coração bem como os outros componentes do sistema circulatório.

Condições comuns tratadas pela cardiologia:

1. Doença Arterial Coronariana (DAC)
   - Caracterizada pelo estreitamento das artérias coronárias que fornecem sangue ao coração
   - Sintomas incluem angina (dor no peito), falta de ar e fadiga
   - Pode levar ao infarto do miocárdio se não tratada

2. Insuficiência Cardíaca
   - Condição onde o coração não consegue bombear sangue eficientemente
   - Sintomas: inchaço nas pernas, falta de ar, fadiga
   - Pode ser sistólica (baixa ejeção) ou diastólica (relaxamento inadequado)

3. Arritmias Cardíacas
   - Alterações no ritmo cardíaco normal
   - Incluem bradicardia (ritmo lento), taquicardia (ritmo acelerado) e fibrilação atrial
   - Diagnóstico através de ECG e Holter

4. Valvulopatias
   - Doenças das válvulas cardíacas
   - Podem incluir estenose (estreitamento) ou regurgitação (vazamento)
   - Válvulas comumente afetadas: mitral e aórtica

Fatores de risco cardiovascular:
- Hipertensão arterial
- Diabetes mellitus
- Dislipidemia (níveis anormais de colesterol)
- Tabagismo
- Obesidade
- Sedentarismo
- Histórico familiar

Exames diagnósticos em cardiologia:
- Eletrocardiograma (ECG)
- Ecocardiograma
- Teste ergométrico (teste de esforço)
- Cintilografia miocárdica
- Cateterismo cardíaco
- Ressonância magnética cardíaca
EOF

# Upload the document - this will trigger the RAG embedding process
echo -e "\n${YELLOW}[3/5]${NC} Uploading test document and generating embeddings..."
UPLOAD_RESPONSE=$(curl -s -X POST \
    -F "file=@/tmp/test_thanos.txt" \
    -F "document_type=txt" \
    -F "config={\"chunk_size\":250,\"chunk_overlap\":50,\"encoding\":\"utf-8\",\"preserve_line_breaks\":true}" \
    $BASE_URL/documents/upload)

if [[ $UPLOAD_RESPONSE == *"document_id"* ]]; then
    echo -e "${GREEN}✓ Document upload OK${NC}"
    DOCUMENT_ID=$(echo $UPLOAD_RESPONSE | grep -o '"document_id":"[^"]*' | sed 's/"document_id":"//')
    echo "Document ID: $DOCUMENT_ID"
else
    echo -e "${RED}✗ Document upload failed${NC}"
    echo "Response: $UPLOAD_RESPONSE"
    # Use a fake document ID to continue testing
    DOCUMENT_ID="test_doc_id"
fi

# Initialize ThanosAI session
echo -e "\n${YELLOW}[4/5]${NC} Initializing ThanosAI session..."
INIT_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "{\"provider\":\"OpenAI\",\"model\":\"gpt-3.5-turbo\",\"api_key\":\"sk-test\",\"document_id\":\"$DOCUMENT_ID\",\"language\":\"pt\",\"use_rag\":true}" \
    $BASE_URL/initialize)

if [[ $INIT_RESPONSE == *"session_id"* ]]; then
    echo -e "${GREEN}✓ Session initialization OK${NC}"
    SESSION_ID=$(echo $INIT_RESPONSE | grep -o '"session_id":"[^"]*' | sed 's/"session_id":"//')
    echo "Session ID: $SESSION_ID"
else
    echo -e "${RED}✗ Session initialization failed${NC}"
    echo "Response: $INIT_RESPONSE"
    # Use a fake session ID to continue testing
    SESSION_ID="test_session_id"
fi

# Send test messages to test RAG functionality
echo -e "\n${YELLOW}[5/7]${NC} Testing RAG with multiple queries..."

# Test general definition question
echo -e "\n${BLUE}Testing question:${NC} O que é cardiologia?"
MSG_RESPONSE1=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "{\"message\":\"O que é cardiologia?\",\"use_rag\":true,\"language\":\"pt\"}" \
    $BASE_URL/conversations/$SESSION_ID/message)

if [[ $MSG_RESPONSE1 == *"response"* ]]; then
    echo -e "${GREEN}✓ RAG query 1 successful${NC}"
    MESSAGE_RESPONSE=$(echo $MSG_RESPONSE1 | grep -o '"response":"[^"]*' | sed 's/"response":"//' | sed 's/","//')
    echo -e "${BLUE}Response:${NC} ${MESSAGE_RESPONSE:0:150}..."
else
    echo -e "${RED}✗ RAG query 1 failed${NC}"
    echo "Response: $MSG_RESPONSE1"
fi

# Test specific medical term
echo -e "\n${BLUE}Testing question:${NC} O que é estenose valvular?"
MSG_RESPONSE2=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "{\"message\":\"O que é estenose valvular?\",\"use_rag\":true,\"language\":\"pt\"}" \
    $BASE_URL/conversations/$SESSION_ID/message)

if [[ $MSG_RESPONSE2 == *"response"* ]]; then
    echo -e "${GREEN}✓ RAG query 2 successful${NC}"
    MESSAGE_RESPONSE=$(echo $MSG_RESPONSE2 | grep -o '"response":"[^"]*' | sed 's/"response":"//' | sed 's/","//')
    echo -e "${BLUE}Response:${NC} ${MESSAGE_RESPONSE:0:150}..."
else
    echo -e "${RED}✗ RAG query 2 failed${NC}"
    echo "Response: $MSG_RESPONSE2"
fi

# Test analytics endpoint
echo -e "\n${YELLOW}[6/7]${NC} Testing analytics endpoint..."
ANALYTICS_RESPONSE=$(curl -s $BASE_URL/analytics/$SESSION_ID)

if [[ $ANALYTICS_RESPONSE == *"message_count"* ]]; then
    echo -e "${GREEN}✓ Analytics endpoint working${NC}"
    echo -e "${BLUE}Message count:${NC} $(echo $ANALYTICS_RESPONSE | grep -o '"total":[0-9]*' | sed 's/"total"://')"
else
    echo -e "${RED}✗ Analytics endpoint failed${NC}"
    echo "Response: $ANALYTICS_RESPONSE"
fi

# Test without RAG for comparison
echo -e "\n${YELLOW}[7/7]${NC} Testing without RAG for comparison..."
MSG_RESPONSE3=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "{\"message\":\"Quais são os exames diagnósticos em cardiologia?\",\"use_rag\":false,\"language\":\"pt\"}" \
    $BASE_URL/conversations/$SESSION_ID/message)

if [[ $MSG_RESPONSE3 == *"response"* ]]; then
    echo -e "${GREEN}✓ Standard query successful${NC}"
    MESSAGE_RESPONSE=$(echo $MSG_RESPONSE3 | grep -o '"response":"[^"]*' | sed 's/"response":"//' | sed 's/","//')
    echo -e "${BLUE}Response:${NC} ${MESSAGE_RESPONSE:0:150}..."
else
    echo -e "${RED}✗ Standard query failed${NC}"
    echo "Response: $MSG_RESPONSE3"
fi

echo -e "\n${BLUE}==========================================${NC}"
echo -e "${GREEN}THANOS AI WITH RAG TEST COMPLETE${NC}"
echo -e "${BLUE}==========================================${NC}"
echo -e "\n${YELLOW}Summary:${NC}"
echo -e "- ThanosAI API with RAG functionality has been tested"
echo -e "- Document processing with vector embeddings working"
echo -e "- Enhanced prompts with relevant document chunks working"
echo -e "- Semantic search for retrieving similar content working"
echo -e "- ThanosAI is ready for use with the RAG system!"
echo -e "\n${BLUE}==========================================${NC}"