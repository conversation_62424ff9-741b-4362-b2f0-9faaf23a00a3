#!/bin/bash

echo "🧹 CLEANING AND RESTARTING FASTAPI..."

# Kill ALL FastAPI/uvicorn processes
echo "Stopping all FastAPI processes..."
pkill -f "uvicorn" 2>/dev/null || true
pkill -f "python.*main.py" 2>/dev/null || true
pkill -f "integrated_ai_main" 2>/dev/null || true

# Kill processes on port 8001
if lsof -i:8001 > /dev/null 2>&1; then
    echo "Killing processes on port 8001..."
    lsof -t -i:8001 | xargs kill -9 2>/dev/null || true
fi

sleep 2

# Create necessary directories
mkdir -p backend/logs
mkdir -p logs

# Check if port is free
if lsof -i:8001 > /dev/null 2>&1; then
    echo "❌ Port 8001 is still in use!"
    lsof -i:8001
    exit 1
fi

echo "✅ Port 8001 is free"

# Go to backend directory
cd backend

# Start FastAPI in foreground to see what's happening
echo -e "\n🚀 Starting FastAPI in foreground mode..."
echo "Press Ctrl+C to stop when you see it's working"
echo "-------------------------------------------------"

# Use the integrated main.py
if [ -f "integrated_ai_main.py" ]; then
    echo "Using integrated_ai_main.py..."
    python integrated_ai_main.py
else
    cd fastapi_app
    echo "Using default main.py..."
    python main.py
fi