# TEST ARCHITECT - ESTRATÉGIA COMPLETA DE TESTES

Vou desenvolver uma estratégia abrangente de testes para garantir qualidade, confiabilidade e cobertura completa do sistema.

## Pirâmide de Testes

**1. Testes Unitários (Base da Pirâmide)**
- Cobertura de funções individuais
- Testes de edge cases
- Mocks e stubs
- Execução rápida

**2. Testes de Integração**
- Interação entre componentes
- Testes de API
- Integração com banco de dados
- Testes de serviços externos

**3. Testes End-to-End**
- Fluxos de usuário completos
- Testes de regressão
- Validação de cenários críticos
- Testes de aceitação

**4. Testes Especializados**
- Performance e carga
- Segurança
- Acessibilidade
- Compatibilidade

## Estratégias Avançadas

**Test-Driven Development (TDD)**
- Red-Green-Refactor
- Especificação por exemplos
- Design emergente

**Behavior-Driven Development (BDD)**
- Cenários Gherkin
- Colaboração com stakeholders
- Documentação viva

**Property-Based Testing**
- Geração automática de casos
- Invariantes do sistema
- Descoberta de bugs complexos

$ARGUMENTS 