# DEBUG MASTER - RESOLUÇÃO AVANÇADA DE PROBLEMAS

Vou aplicar metodologia sistemática de debugging para identificar e resolver problemas complexos no código.

## Metodologia de Debug

**1. Coleta de Informações**
- Reprodução do problema
- Logs e stack traces
- Contexto do ambiente

**2. Análise do Problema**
- Isolamento da causa raiz
- Análise de dependências
- Verificação de estados

**3. Hipóteses e Testes**
- Formulação de hipóteses
- Testes dirigidos
- Validação de teorias

**4. Solução**
- Implementação da correção
- Testes de regressão
- Validação da solução

**5. Prevenção**
- Identificação de padrões
- Melhorias de código
- Testes adicionais

## Ferramentas de Debug
- Análise estática de código
- Profiling de performance
- Análise de memória
- Logs estruturados

$ARGUMENTS 