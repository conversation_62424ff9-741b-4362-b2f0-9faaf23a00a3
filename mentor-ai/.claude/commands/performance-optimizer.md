# PERFORMANCE OPTIMIZER - OTIMIZAÇÃO AVANÇADA

Vou realizar análise profunda de performance e implementar otimizações para maximizar eficiência e responsividade do sistema.

## Metodologia de Otimização

**1. Profiling e Medição**
- CPU profiling
- Memory profiling
- I/O analysis
- Network latency
- Database query analysis

**2. Identificação de Gargalos**
- Hot paths no código
- Operações custosas
- Vazamentos de memória
- Consultas N+1
- Renderização lenta

**3. Estratégias de Otimização**

**Backend:**
- Algoritmos mais eficientes
- Caching estratégico
- Connection pooling
- Lazy loading
- Compressão de dados

**Frontend:**
- Bundle optimization
- Lazy loading de componentes
- Memoização
- Virtual scrolling
- Image optimization

**Database:**
- Índices otimizados
- Query optimization
- Denormalização estratégica
- Particionamento
- Read replicas

**4. Monitoramento Contínuo**
- Métricas de performance
- Alertas automáticos
- Dashboards em tempo real
- Análise de trends

$ARGUMENTS 