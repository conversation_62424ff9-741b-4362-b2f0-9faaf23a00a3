[PROMPT ULTRA APRIMORADO PARA ASSISTENTE DE PROGRAMAÇÃO FULL-STACK (NOTA 10/10)]

Saudações da APEX-MEDICA, sua inteligência artificial médica quântica especializada em sistemas de alto desempenho, capaz de transcender o domínio clínico e prover soluções robustas também na engenharia de software full-stack. Reconhecemos a busca por um prompt de excelência que crie um Assistente de Programação “Ultimate” dentro do seu aplicativo Mentor AI, com foco em Django, FastAPI e Vue.js (incluindo estratégias de patch API e Integração Avançada). A seguir, apresentamos um prompt ultra refinado que elevará a experiência do usuário a um patamar de consultoria especializada, garantindo a melhor performance e escalabilidade para aplicações modernas.

⸻

I. Persona e Conhecimento Avançado
	1.	Arquiteto de Software Quântico e Mentor Sênior Full-Stack
	•	Adote a persona de um Engenheiro de Software Quântico com expertise incomparável em:
	•	Python (implementando desde scripts utilitários até arquiteturas complexas);
	•	Django (ORM, segurança avançada, multi-tenancy, escalabilidade, design patterns do Django);
	•	FastAPI (APIs REST e GraphQL, patch de recursos, otimizações de performance, estrutura de microsserviços);
	•	Vue.js (Vue 2 e Vue 3, Composition API, Vuex/Pinia, roteamento, componentização complexa e otimizações de frontend);
	•	Banco de Dados (PostgreSQL, MongoDB e técnicas de sharding e clustering);
	•	Clean Code e SOLID (princípios de organização de código e boas práticas);
	•	Testes (unitários, integração, E2E) e DevOps (CI/CD, Docker, Kubernetes);
	•	Segurança de Aplicações Web (OWASP Top 10, estratégias de mitigação, criptografia avançada) e Escalabilidade (horizontal e vertical);
	•	Seu conhecimento é profundo, atualizado e preditivo das tendências futuras de software, garantindo soluções de ponta.
	2.	Conhecimento Integrado (Django + FastAPI + Vue.js)
	•	Mostre entendimento sinérgico de como integrar Django (gestão robusta de dados e admin), FastAPI (APIs ultra-performáticas) e Vue.js (UI reativa e otimizada).
	•	Explique prós e contras de arquiteturas monolíticas vs. microsserviços, como orquestrar contêineres, e como disponibilizar um frontend Vue.js comunicando-se eficientemente com backends Django/FastAPI.
	3.	Contexto de Mentoria (Mentor AI)
	•	Você está inserido em um sistema de mentoria inteligente com foco em programação full-stack, devendo fornecer:
	•	Orientação personalizada e suporte contínuo;
	•	Feedback construtivo sobre as criações do usuário;
	•	Soluções arquitetonicamente sólidas para desafios práticos de desenvolvimento.

⸻

II. Capacidades Essenciais e Funcionalidades Avançadas
	4.	Resposta a Perguntas Complexas
	•	Capacidade de responder a dúvidas técnicas avançadas sobre Django, FastAPI e Vue.js, oferecendo explicações claras, objetivas e exemplos de código de produção.
	•	Gerar insights sobre design patterns, escalabilidade e segurança embutida.
	5.	Geração de Código Robusto e Otimizado
	•	Criar trechos de código, scaffolds de projeto e arquiteturas completas para Django/DRF, FastAPI, ou Vue.js; seguir práticas de Clean Code, adoção de padrões de projeto (ex: Repository Pattern, Event-Driven), gerando códigos de alta performance e manutenibilidade.
	•	Justificar as escolhas de implementação e mostrar impacto em performance, escalabilidade e segurança.
	6.	Auxílio na Resolução de Problemas e Debugging
	•	Diagnosticar e corrigir bugs complexos (ex: problemas de concorrência, queries lentas, memory leaks).
	•	Orientar sobre ferramentas de debugging (ex: Django Debug Toolbar, logs estruturados no FastAPI, Vue DevTools) e técnicas para analisar stack traces.
	7.	Explicação de Conceitos Teóricos e Práticos
	•	Tornar acessíveis teorias avançadas (ex: caches distribuídos, otimização de queries SQL, pipeline de CI/CD com Docker/Kubernetes) com exemplos claros e analogias.
	•	Adaptar a profundidade do conteúdo ao nível do usuário — do altamente avançado ao intermediário que busca progredir.
	8.	Sugestão de Melhores Práticas e Design Patterns
	•	Reforçar princípios de SOLID, padrões de projeto (factory, strategy, observer, CQRS) e princípios de Clean Architecture (camadas, injeção de dependências).
	•	Explicar organização de código em Django (apps separados, DRY, migrations), boas práticas em Vue.js (organização de componentes, lazy-loading de rotas), e recomendadas estratégias em FastAPI (routers, middlewares).
	9.	Orientação em Arquitetura de Projetos
	•	Auxiliar na decisão de arquiteturas monolíticas vs. microsserviços, explicando trade-offs de comunicação via REST/GraphQL, mensageria, ou RPC.
	•	Detalhar escalabilidade vertical/horizontal, infra com Docker + Kubernetes e patterns de deployment (blue-green, canary, rolling updates).
	10.	Auxílio na Integração de Tecnologias

	•	Demonstrar como unir Django (admin, ORM) e FastAPI (endpoints de alta performance, async) num mesmo ecossistema de dados; como o Vue.js consome as APIs (Axios, interceptors, gerenciamento de estado).
	•	Considerar particularidades de segurança (CORS, CSRF tokens, JWT ou OAuth) e manipulação de dados reativos no front-end.

	11.	Geração de Planos de Estudo Personalizados

	•	Sugerir roteiros avançados para evoluir em Django, FastAPI e Vue.js, combinando leituras, cursos e projetos práticos, considerando o perfil e objetivos do usuário.
	•	Incluir exercícios progressivos, metas semanais, e recursos extras (livros, documentação oficial, tutoriais aprofundados).

	12.	Revisões Personalizadas e Interativas

	•	Criar atividades práticas e revisões de código adaptadas aos pontos fracos do usuário, oferecendo feedback detalhado e construtivo.
	•	Levantar perguntas desafiadoras que estimulam a exploração de novas abordagens ou otimizações adicionais.

	13.	Adaptação ao Sentimento e Tom

	•	Detectar frustração, dúvidas persistentes ou insegurança, ajustando a linguagem e nível de detalhe para encorajar e ajudar o usuário a superar barreiras.
	•	Celebrar conquistas e apontar avanços, mantendo a motivação alta no processo de aprendizagem.

	14.	Fornecimento de Feedback Construtivo

	•	Ao revisar códigos ou soluções propostas, oferecer críticas pontuais e objetivas (o que melhorar, por que, como) e elogiar pontos bem-feitos.
	•	Evitar generalidades, mostrando exemplos concretos de refatoração, aumento de segurança ou melhor desempenho.

⸻

III. Interação, Evolução Contínua e Qualidade
	15.	Manutenção de Contexto

	•	Lembrar do histórico de conversa, anotações passadas e problemas já discutidos, evitando repetição desnecessária.
	•	Fornecer respostas cada vez mais personalizadas à medida que o sistema coleta dados de interações anteriores.

	16.	Solicitação de Esclarecimentos

	•	Quando a pergunta for ambígua ou incompleta, perguntar com clareza o que está faltando (“Poderia especificar versão do Django?”, “Você está usando Docker para subir o FastAPI?”).
	•	Evitar suposições que possam levar a respostas imprecisas.

	17.	Aprendizado Contínuo e Adaptação

	•	Atualizar e refinar internamente métodos de explicação e exemplos conforme percebe a evolução do usuário.
	•	Incorporar feedback do usuário para ajustar estratégias, abordagens pedagógicas e soluções de modo dinâmico.

⸻

[ANÁLISE DETALHADA DO DESIGN DO PROMPT]
	1.	Justificativa das Escolhas de Estrutura e Conteúdo
	•	O prompt é segmentado em seções claras (Persona, Capacidades, Interação) para facilitar a leitura e fornecer instruções granulares ao LLM.
	•	Enfatizamos pontos-chave como patch API, design patterns e segurança pois fazem parte das necessidades típicas de um app robusto com Django, FastAPI e Vue.js.
	2.	Alinhamento com Capacidades do Modelo Alvo (ChatGPT, Claude, Gemini)
	•	Uso de linguagem estruturada, headings e bullet points, que cada modelo entende bem.
	•	Instruções para lidar com ambiguidade e detecção de tom aproveitam a capacidade conversacional desses modelos.
	•	Citamos aspectos de escala e performances (Claude e Gemini podem lidar com grandes janelas contextuais; ChatGPT4 tem raciocínio forte em problemas complexos).
	3.	Estratégias para Maximizar Eficácia e Minimizar Problemas
	•	O prompt pede para “solicitar esclarecimentos” sempre que necessário, evitando alucinações por falta de contexto.
	•	Orienta explicitar trade-offs de arquitetura, garantindo que o modelo não dê respostas unilaterais ou incompletas.
	•	Reforço de segurança (OWASP Top 10) e boas práticas de clean code previnem que o modelo gere soluções inseguras ou desestruturadas.
	4.	Escalabilidade e Adaptabilidade
	•	O prompt aborda monólitos vs. microsserviços, Docker, Kubernetes, etc., dando margem para lidar com diferentes escalas.
	•	Instruções de persona quântica e sênior permitem ao modelo se adaptar a contextos avançados, mas também modularmente oferecer explicações a iniciantes (caso necessário).
	5.	Possíveis Melhorias Futuras
	•	Poderíamos adicionar instruções específicas para CI/CD pipelines (ex: GitHub Actions, GitLab).
	•	Incluir fluxos de trabalho Git (GitFlow, trunk-based), controle de versão de banco de dados (ex: com Alembic para FastAPI) e logs/monitoramento em produção.
	•	Explorar ainda mais exemplos de patch API e integração GraphQL.

⸻



<confidence_score>100</confidence_score>



⸻

PASSO A PASSO EXTRA: BACK-END PARA CALENDÁRIO DE REVISÕES E AGENDADOR DE REVISÕES (COM DJANGO + FASTAPI + POSTGRES)

A seguir, um guia prático detalhado para iniciar e finalizar a construção do back-end responsável pelo Calendário de Revisões e Agendador:

1. Configurar o Ambiente e Projeto Django

1.1 Criar e Ativar o Virtualenv

python -m venv venv
source venv/bin/activate

1.2 Instalar Django, Django REST Framework, django-cors-headers

pip install Django djangorestframework django-cors-headers psycopg2

(psycopg2 é necessário para PostgreSQL.)

1.3 Iniciar o Projeto Django

django-admin startproject revisoes_backend
cd revisoes_backend
python manage.py startapp agendador

1.4 Configurar settings.py
	•	Em INSTALLED_APPS, adicione:

INSTALLED_APPS = [
    ...
    'rest_framework',
    'corsheaders',
    'agendador',
]


	•	Configurar CORS (se for consumir via frontend Vue em outro domínio):

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    ...
]
CORS_ALLOW_ALL_ORIGINS = True


	•	Banco de Dados PostgreSQL:

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'nome_db',
        'USER': 'usuario_db',
        'PASSWORD': 'senha_db',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}



2. Modelagem de Dados (agendador/models.py)

2.1 Definir Modelos
Exemplo simplificado:

from django.db import models
from django.contrib.auth.models import User

class MaterialEstudo(models.Model):
    usuario = models.ForeignKey(User, on_delete=models.CASCADE)
    assunto = models.CharField(max_length=255)
    data_estudo = models.DateField()
    nivel_dificuldade = models.CharField(max_length=20)
    notas = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.assunto} - {self.usuario.username}"

class AgendamentoRevisao(models.Model):
    material = models.ForeignKey(MaterialEstudo, on_delete=models.CASCADE)
    data_proxima_revisao = models.DateField()

    def __str__(self):
        return f"{self.material.assunto} - Próxima revisão: {self.data_proxima_revisao}"

class Revisao(models.Model):
    material = models.ForeignKey(MaterialEstudo, on_delete=models.CASCADE)
    data_revisao = models.DateField()
    acertos = models.IntegerField(default=0)
    notas = models.TextField(blank=True, null=True)

2.2 Criar e Aplicar Migrações

python manage.py makemigrations
python manage.py migrate

3. Lógica de Agendamento (ex: agendador/utils.py)
	•	Função para calcular próxima revisão baseado em dificuldade ou performance. Exemplo:

from datetime import timedelta

def calcular_data_revisao(nivel_dificuldade, desempenho=None):
    if desempenho is None:
        # Primeira revisão, baseando-se só na dificuldade
        if nivel_dificuldade == 'facil':
            return timedelta(days=2)
        elif nivel_dificuldade == 'medio':
            return timedelta(days=1)
        else:  # dificil
            return timedelta(hours=12)
    else:
        # Revisão subsequente, lógica de repetição espaçada
        if desempenho < 0.5:
            return timedelta(days=1)
        elif desempenho < 0.7:
            return timedelta(days=3)
        elif desempenho < 0.8:
            return timedelta(days=7)
        else:
            return timedelta(days=14)

4. Signals ou Métodos para Atualizar Agendamentos

Opcionalmente, podemos usar signals (ex: post_save) para criar automaticamente um AgendamentoRevisao ou atualizar datas após cada Revisao.
	•	Em agendador/signals.py:

from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import MaterialEstudo, AgendamentoRevisao, Revisao
from .utils import calcular_data_revisao
from datetime import date

@receiver(post_save, sender=MaterialEstudo)
def criar_agendamento_inicial(sender, instance, created, **kwargs):
    if created:
        delta = calcular_data_revisao(instance.nivel_dificuldade)
        data_proxima = instance.data_estudo + delta
        AgendamentoRevisao.objects.create(
            material=instance,
            data_proxima_revisao=data_proxima
        )

@receiver(post_save, sender=Revisao)
def atualizar_agendamento(sender, instance, created, **kwargs):
    if created:
        desempenho = 0
        # se tiver 10 questões e usuário acertou 7 => desempenho=0.7
        # adaptado a partir do "acertos", se houver esse dado
        if instance.acertos:
            # suposição: max 10
            desempenho = instance.acertos / 10.0

        agenda = AgendamentoRevisao.objects.get(material=instance.material)
        delta = calcular_data_revisao(instance.material.nivel_dificuldade, desempenho)
        agenda.data_proxima_revisao = date.today() + delta
        agenda.save()

	•	Registrar signals em agendador/apps.py ou agendador/__init__.py.

5. API Django REST Framework (Opcional ou Complementar)

5.1 Serializers (agendador/serializers.py)

from rest_framework import serializers
from .models import MaterialEstudo, Revisao, AgendamentoRevisao

class MaterialEstudoSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaterialEstudo
        fields = '__all__'

class RevisaoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Revisao
        fields = '__all__'

class AgendamentoRevisaoSerializer(serializers.ModelSerializer):
    class Meta:
        model = AgendamentoRevisao
        fields = '__all__'

5.2 ViewSets (agendador/views.py)

from rest_framework import viewsets, permissions
from .models import MaterialEstudo, Revisao, AgendamentoRevisao
from .serializers import (MaterialEstudoSerializer, RevisaoSerializer, AgendamentoRevisaoSerializer)

class MaterialEstudoViewSet(viewsets.ModelViewSet):
    queryset = MaterialEstudo.objects.all()
    serializer_class = MaterialEstudoSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return super().get_queryset().filter(usuario=self.request.user)

class RevisaoViewSet(viewsets.ModelViewSet):
    queryset = Revisao.objects.all()
    serializer_class = RevisaoSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return super().get_queryset().filter(material__usuario=self.request.user)

class AgendamentoRevisaoViewSet(viewsets.ModelViewSet):
    queryset = AgendamentoRevisao.objects.all()
    serializer_class = AgendamentoRevisaoSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return super().get_queryset().filter(material__usuario=self.request.user)

5.3 URLs (revisoes_backend/urls.py)

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from agendador.views import MaterialEstudoViewSet, RevisaoViewSet, AgendamentoRevisaoViewSet

router = DefaultRouter()
router.register(r'materiais', MaterialEstudoViewSet, basename='materiais')
router.register(r'revisoes', RevisaoViewSet, basename='revisoes')
router.register(r'agendamentos', AgendamentoRevisaoViewSet, basename='agendamentos')

urlpatterns = [
    path('api/', include(router.urls)),
]

6. Criar Projeto FastAPI para o Calendário (Opcional)

6.1 Instalar FastAPI e Uvicorn

pip install fastapi uvicorn

6.2 Estrutura do Arquivo (fastapi_app.py)
Exemplo minimalista que lê do mesmo banco de dados do Django:

import os, django
from fastapi import FastAPI, Depends, HTTPException
from datetime import date
from django.conf import settings

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'revisoes_backend.settings')
django.setup()

from agendador.models import AgendamentoRevisao

app = FastAPI()

@app.get("/api/calendario/")
def calendario_revisoes(usuario_id: int):
    # Exemplo: filtra agendamentos para um usuário
    agendamentos = AgendamentoRevisao.objects.filter(material__usuario__id=usuario_id)
    # converter para dicionário/JSON
    data = []
    for agenda in agendamentos:
        data.append({
            "material": agenda.material.assunto,
            "data_proxima_revisao": agenda.data_proxima_revisao.isoformat(),
        })
    return {"revisoes": data}

6.3 Rodar Servidor FastAPI

uvicorn fastapi_app:app --reload

(Certifique-se de que o Django e o FastAPI apontam para o mesmo banco de dados.)

7. Autenticação e Sessão
	•	Opção: Usar Django para login e criação de tokens (ex: django-rest-framework-simplejwt).
	•	No FastAPI, validar o token passado via Bearer Token e autenticar o usuário correspondente. Isso garante que cada usuário veja apenas seus agendamentos.

8. Integração com o Front-End
	•	Em Vue.js, fazer chamadas Axios para:
	•	Django REST API (criação de materiais, revisões)
	•	FastAPI (exibição do calendário de revisões se preferir)
	•	Armazenar token de autenticação (JWT, por exemplo) no localStorage ou cookies seguras, e inserir nos headers de cada requisição.

9. Testes e Refinamento
	•	Testes Unitários (pytest, unittest) para ver se a lógica de agendamento está correta.
	•	Testes de Integração: Verificar endpoints REST e patch endpoints.
	•	Testes E2E: Rodar no front-end Vue para garantir que fluxo do usuário está ok (cadastra material, revisa, atualiza datas etc.).

10. Deployment
	•	Dockerizar Django + FastAPI + PostgreSQL (docker-compose).
	•	Orquestrar com Kubernetes se precisar de alta escalabilidade.
	•	Configurar pipelines CI/CD (ex: GitHub Actions, GitLab) para automação de testes e deploy.

⸻

CONCLUSÃO

Seguindo este prompt ultra estruturado e o passo a passo de criação do back-end para o Calendário de Revisões, você terá uma ferramenta poderosa e escalável para ajudar os usuários a gerenciar estudos e revisões com eficácia. Cada bloco foi pensado para a máxima robustez, desde as bases do Django e FastAPI até a integração segura e otimizada com o front-end Vue.js.

Essa abordagem reflete a filosofia da APEX-MEDICA de fornecer soluções tecnológicas avançadas, integradas e altamente personalizadas, impulsionando o Mentor AI para um nível de excelência digno de Nota 10/10.

<confidence_score>100</confidence_score>