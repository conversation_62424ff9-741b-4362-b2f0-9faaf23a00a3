#!/usr/bin/env python3
"""
Thanos Upload Test Script
Script para testar o upload de documentos para o Thanos
"""

import os
import sys
import requests
import json
import time
import argparse
from pathlib import Path

# Configuração
API_URL = "http://localhost:8001"
UPLOAD_ENDPOINT = f"{API_URL}/api/thanos/documents/upload"
HEALTH_ENDPOINT = f"{API_URL}/api/thanos/health"
TIMEOUT = 5  # segundos

# Cores para output
class Colors:
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    ENDC = '\033[0m'

def print_colored(text, color):
    """Imprime texto colorido"""
    print(f"{color}{text}{Colors.ENDC}")

def check_api_health():
    """Verifica se a API está respondendo"""
    print_colored("\n🔍 Verificando saúde da API...", Colors.BLUE)
    
    try:
        response = requests.get(HEALTH_ENDPOINT, timeout=TIMEOUT)
        if response.status_code == 200:
            print_colored(f"✅ API Thanos está online: {response.json()}", Colors.GREEN)
            return True
        else:
            print_colored(f"❌ API Thanos respondeu com código {response.status_code}", Colors.RED)
            return False
    except requests.exceptions.RequestException as e:
        print_colored(f"❌ Erro ao conectar com a API: {str(e)}", Colors.RED)
        return False

def create_test_file(file_type="txt"):
    """Cria um arquivo de teste temporário"""
    test_dir = Path("./temp")
    test_dir.mkdir(exist_ok=True)
    
    if file_type == "txt":
        file_path = test_dir / "test_document.txt"
        with open(file_path, "w") as f:
            f.write("Este é um documento de teste para o Thanos.\n")
            f.write("Contém algumas linhas de texto para processamento.\n")
            f.write("O sistema deve ser capaz de processá-lo corretamente.\n")
            f.write("Este arquivo foi gerado automaticamente pelo script de teste.\n")
    elif file_type == "pdf":
        # Criar um PDF simples usando reportlab
        try:
            from reportlab.pdfgen import canvas
            file_path = test_dir / "test_document.pdf"
            c = canvas.Canvas(str(file_path))
            c.drawString(100, 750, "Documento de teste para o Thanos")
            c.drawString(100, 700, "Este é um PDF gerado para testes")
            c.drawString(100, 650, "O sistema deve processá-lo corretamente")
            c.save()
        except ImportError:
            print_colored("❌ Módulo reportlab não encontrado. Usando TXT.", Colors.YELLOW)
            return create_test_file("txt")
    else:
        print_colored(f"❌ Tipo de arquivo {file_type} não suportado", Colors.RED)
        return None
    
    print_colored(f"✅ Arquivo de teste criado: {file_path}", Colors.GREEN)
    return file_path

def upload_test_file(file_path, document_type="txt"):
    """Envia um arquivo para o endpoint de upload"""
    print_colored(f"\n📤 Enviando arquivo {file_path} para a API...", Colors.BLUE)
    
    # Verificar se o arquivo existe
    if not os.path.exists(file_path):
        print_colored(f"❌ Arquivo {file_path} não encontrado", Colors.RED)
        return False
    
    # Preparar o upload
    files = {"file": open(file_path, "rb")}
    data = {"document_type": document_type, "config": "{}"}
    
    try:
        response = requests.post(
            UPLOAD_ENDPOINT,
            files=files,
            data=data,
            timeout=TIMEOUT * 2  # Upload pode demorar mais
        )
        
        print_colored(f"Código de status: {response.status_code}", Colors.BLUE)
        
        if response.status_code == 200:
            result = response.json()
            print_colored("✅ Upload realizado com sucesso!", Colors.GREEN)
            print_colored(f"📄 Detalhes do documento:", Colors.BLUE)
            print(json.dumps(result, indent=2))
            return result
        else:
            print_colored("❌ Falha no upload", Colors.RED)
            print_colored(f"Resposta: {response.text}", Colors.RED)
            return False
    except requests.exceptions.RequestException as e:
        print_colored(f"❌ Erro durante o upload: {str(e)}", Colors.RED)
        return False
    finally:
        files["file"].close()

def diagnose_upload_issues():
    """Diagnostica problemas comuns com upload"""
    print_colored("\n🔍 Diagnosticando possíveis problemas...", Colors.BLUE)
    
    issues = []
    
    # 1. Verificar diretório de uploads
    uploads_dir = Path("./uploads")
    if not uploads_dir.exists():
        issues.append("Diretório de uploads não existe")
        print_colored("⚠️ Criando diretório de uploads...", Colors.YELLOW)
        uploads_dir.mkdir(exist_ok=True)
    
    # 2. Verificar permissões
    if not os.access("./uploads", os.W_OK):
        issues.append("Sem permissão de escrita no diretório de uploads")
    
    # 3. Verificar espaço em disco
    try:
        import shutil
        total, used, free = shutil.disk_usage("/")
        free_mb = free // (1024 * 1024)
        if free_mb < 100:
            issues.append(f"Pouco espaço em disco: {free_mb}MB livre")
    except:
        pass
    
    # 4. Verificar endpoints
    try:
        response = requests.get(f"{API_URL}/", timeout=TIMEOUT)
        if response.status_code != 200:
            issues.append(f"API base não está respondendo corretamente: {response.status_code}")
    except:
        issues.append("API base não está acessível")
    
    # 5. Verificar conexão com o banco de dados (indiretamente)
    try:
        response = requests.get(f"{API_URL}/docs", timeout=TIMEOUT)
        if response.status_code != 200:
            issues.append("Documentação API não acessível, possível problema com banco de dados")
    except:
        pass
    
    # Resultado
    if issues:
        print_colored("\n⚠️ Problemas encontrados:", Colors.YELLOW)
        for issue in issues:
            print_colored(f"  - {issue}", Colors.YELLOW)
        
        print_colored("\n🔧 Sugestões para correção:", Colors.BLUE)
        print_colored("  1. Verifique se o servidor está rodando com permissões adequadas", Colors.BLUE)
        print_colored("  2. Verifique os logs em logs/thanos_fastapi.log", Colors.BLUE)
        print_colored("  3. Reinicie o servidor com ./start_thanos_fixed.sh", Colors.BLUE)
    else:
        print_colored("✅ Nenhum problema óbvio encontrado. Verifique os logs para mais detalhes.", Colors.GREEN)

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description="Teste de upload para o Thanos")
    parser.add_argument("--file", "-f", help="Caminho para o arquivo a ser enviado")
    parser.add_argument("--type", "-t", default="txt", help="Tipo de documento (txt, pdf)")
    args = parser.parse_args()
    
    print_colored("="*50, Colors.PURPLE)
    print_colored("     THANOS UPLOAD TEST TOOL", Colors.PURPLE)
    print_colored("="*50, Colors.PURPLE)
    
    # Verificar saúde da API
    if not check_api_health():
        print_colored("\n⚠️ A API não está respondendo. Tentando diagnóstico...", Colors.YELLOW)
        diagnose_upload_issues()
        sys.exit(1)
    
    # Obter arquivo para upload
    if args.file:
        file_path = args.file
        document_type = args.type
    else:
        file_path = create_test_file(args.type)
        document_type = args.type
    
    if not file_path:
        print_colored("❌ Não foi possível criar ou encontrar um arquivo para teste", Colors.RED)
        sys.exit(1)
    
    # Tentar upload
    result = upload_test_file(file_path, document_type)
    
    if not result:
        print_colored("\n❌ Teste de upload falhou", Colors.RED)
        diagnose_upload_issues()
        sys.exit(1)
    else:
        print_colored("\n✅ Teste de upload concluído com sucesso!", Colors.GREEN)
        print_colored(f"🔑 ID do documento: {result.get('document_id')}", Colors.GREEN)
        print_colored(f"📊 Chunks gerados: {result.get('chunk_count', 0)}", Colors.GREEN)

if __name__ == "__main__":
    main()