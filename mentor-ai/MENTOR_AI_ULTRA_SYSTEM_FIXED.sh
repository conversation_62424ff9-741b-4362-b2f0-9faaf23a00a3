#!/bin/bash

# ===================================================================
# MENTOR AI - ULTRA SYSTEM SCRIPT INTEGRADO - VERSÃO CORRIGIDA
# Versão: Ultra-5.0.0 (Complete Robust Edition)
# ===================================================================

# Cores para melhorar a visualização
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
ORANGE='\033[0;33m'
BOLD='\033[1m'
NC='\033[0m'

# Definir diretórios principais
BASE_DIR="$(cd "$(dirname "$0")" && pwd)"
DJANGO_DIR="$BASE_DIR/backend/django_app"
FASTAPI_DIR="$BASE_DIR/backend/fastapi_app"
FRONTEND_DIR="$BASE_DIR/frontend"
LOG_DIR="$BASE_DIR/logs"
PID_FILE="$BASE_DIR/.ultra_pids"
TEMP_DIR="$BASE_DIR/temp"
VENV_DIR="$BASE_DIR/venv"

# ===================================================================
# FUNÇÕES UTILITÁRIAS
# ===================================================================

show_header() {
    clear
    echo -e "${BLUE}=====================================================================${NC}"
    echo -e "${BOLD}${BLUE}||             MENTOR AI - ULTRA SYSTEM INTEGRADO               ||${NC}"
    echo -e "${BLUE}=====================================================================${NC}"
    echo -e "${PURPLE}Versão Ultra-5.0.0 (Complete Robust Edition) - $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo -e "${BLUE}=====================================================================${NC}\n"
}

show_menu() {
    echo -e "${YELLOW}MENU DE OPERAÇÕES:${NC}"
    echo -e "${CYAN}1)${NC} Verificar status do sistema"
    echo -e "${CYAN}2)${NC} Iniciar todos os serviços (Recomendado)"
    echo -e "${CYAN}3)${NC} Instalação completa de dependências"
    echo -e "${CYAN}4)${NC} Corrigir FastAPI (modo completo)"
    echo -e "${CYAN}5)${NC} Parar todos os serviços"
    echo -e "${CYAN}6)${NC} Iniciar apenas o FastAPI"
    echo -e "${CYAN}7)${NC} Iniciar apenas o Django"
    echo -e "${CYAN}8)${NC} Iniciar apenas o Frontend"
    echo -e "${CYAN}9)${NC} Ver logs em tempo real"
    echo -e "${CYAN}0)${NC} Sair"
    echo -e "${YELLOW}----------------------------------------${NC}"
    echo -ne "${BOLD}Escolha uma opção:${NC} "
    read -r option
    
    return $option
}

show_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

show_error() {
    echo -e "${RED}✗ $1${NC}"
}

show_warning() {
    echo -e "${ORANGE}⚠ $1${NC}"
}

show_info() {
    echo -e "${CYAN}ℹ $1${NC}"
}

# ===================================================================
# VERIFICAR DEPENDÊNCIAS DO SISTEMA
# ===================================================================

check_system_dependencies() {
    echo -e "\n${BOLD}${YELLOW}VERIFICANDO DEPENDÊNCIAS DO SISTEMA${NC}"
    echo -e "${YELLOW}===================================${NC}"
    
    local missing_deps=0
    
    # Check Python
    echo -ne "${BOLD}Python 3.8+:${NC} "
    if command -v python3 &> /dev/null; then
        python_version=$(python3 --version 2>&1 | awk '{print $2}')
        echo -e "${GREEN}✅ Instalado (v$python_version)${NC}"
    else
        echo -e "${RED}❌ Não instalado${NC}"
        missing_deps=1
    fi
    
    # Check Node.js
    echo -ne "${BOLD}Node.js 18+:${NC} "
    if command -v node &> /dev/null; then
        node_version=$(node --version)
        echo -e "${GREEN}✅ Instalado ($node_version)${NC}"
    else
        echo -e "${RED}❌ Não instalado${NC}"
        missing_deps=1
    fi
    
    # Check npm
    echo -ne "${BOLD}npm:${NC} "
    if command -v npm &> /dev/null; then
        npm_version=$(npm --version)
        echo -e "${GREEN}✅ Instalado (v$npm_version)${NC}"
    else
        echo -e "${RED}❌ Não instalado${NC}"
        missing_deps=1
    fi
    
    # Check PostgreSQL
    echo -ne "${BOLD}PostgreSQL:${NC} "
    if command -v psql &> /dev/null; then
        echo -e "${GREEN}✅ Instalado${NC}"
    else
        echo -e "${ORANGE}⚠ Não instalado (usando SQLite como fallback)${NC}"
    fi
    
    return $missing_deps
}

# ===================================================================
# INSTALAÇÃO COMPLETA DE DEPENDÊNCIAS
# ===================================================================

install_all_dependencies() {
    echo -e "\n${BOLD}${YELLOW}INSTALANDO TODAS AS DEPENDÊNCIAS${NC}"
    echo -e "${YELLOW}===============================${NC}"
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "$VENV_DIR" ]; then
        show_info "Criando ambiente virtual Python..."
        python3 -m venv "$VENV_DIR"
        show_success "Ambiente virtual criado"
    fi
    
    # Activate virtual environment
    source "$VENV_DIR/bin/activate"
    
    # Upgrade pip
    show_info "Atualizando pip..."
    pip install --upgrade pip
    
    # Install backend dependencies
    show_info "Instalando dependências do backend..."
    
    # FastAPI dependencies
    cd "$FASTAPI_DIR"
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        show_success "Dependências do FastAPI instaladas"
    else
        show_warning "requirements.txt do FastAPI não encontrado. Instalando pacotes essenciais..."
        pip install fastapi uvicorn sqlalchemy psycopg2-binary python-multipart \
                    python-jose passlib bcrypt python-dotenv cors \
                    anthropic openai langchain chromadb pypdf2 \
                    pandas numpy scikit-learn
    fi
    
    # Django dependencies
    cd "$DJANGO_DIR"
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        show_success "Dependências do Django instaladas"
    else
        show_warning "requirements.txt do Django não encontrado. Instalando Django..."
        pip install django djangorestframework django-cors-headers psycopg2-binary
    fi
    
    # Install frontend dependencies
    show_info "Instalando dependências do frontend..."
    cd "$FRONTEND_DIR"
    
    # Clean npm cache
    npm cache clean --force
    
    # Remove node_modules and package-lock.json for fresh install
    rm -rf node_modules package-lock.json
    
    # Install dependencies
    npm install --legacy-peer-deps
    
    show_success "Todas as dependências instaladas com sucesso!"
}

# ===================================================================
# VERIFICAR STATUS DO SISTEMA
# ===================================================================

check_system_status() {
    echo -e "\n${BOLD}${YELLOW}VERIFICANDO STATUS DO SISTEMA MENTOR AI${NC}"
    echo -e "${YELLOW}=========================================${NC}"

    # Check PostgreSQL
    echo -ne "${BOLD}PostgreSQL:${NC} "
    if pg_isready -h localhost > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Rodando${NC}"
    else
        echo -e "${ORANGE}⚠ Parado (usando SQLite)${NC}"
    fi

    # Check Django
    echo -ne "${BOLD}Django (8003):${NC} "
    if curl -s http://localhost:8003/ > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Rodando${NC}"
    else
        echo -e "${RED}❌ Parado${NC}"
    fi

    # Check FastAPI
    echo -ne "${BOLD}FastAPI (8001):${NC} "
    if curl -s http://localhost:8001/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Rodando${NC}"
    else
        echo -e "${RED}❌ Parado${NC}"
    fi

    # Check Frontend
    echo -ne "${BOLD}Frontend (8082):${NC} "
    if curl -s http://localhost:8082 > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Rodando${NC}"
    else
        echo -e "${RED}❌ Parado${NC}"
    fi

    echo -e "\n${BOLD}${YELLOW}STATUS DOS SERVIÇOS DE IA:${NC}"
    echo -e "${YELLOW}-------------------------${NC}"
    
    # Check Second Brain
    echo -ne "${BOLD}Second Brain Chat:${NC} "
    if curl -s "http://localhost:8001/api/chat/test" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Disponível${NC}"
    else
        echo -e "${RED}❌ Indisponível${NC}"
    fi

    # Check Thanos
    echo -ne "${BOLD}Thanos AI:${NC} "
    if curl -s http://localhost:8001/api/thanos/health | grep -q "healthy" 2>/dev/null; then
        echo -e "${GREEN}✅ Disponível${NC}"
    else
        echo -e "${RED}❌ Indisponível${NC}"
    fi

    echo -e "\n${BOLD}${YELLOW}PONTOS DE ACESSO:${NC}"
    echo -e "${YELLOW}----------------${NC}"
    echo -e "${BOLD}Aplicação Principal:${NC} ${CYAN}http://localhost:8082${NC}"
    echo -e "${BOLD}Documentação API:${NC} ${CYAN}http://localhost:8001/docs${NC}"
    echo -e "${BOLD}Django Admin:${NC} ${CYAN}http://localhost:8003/admin/${NC}"
    echo -e "${BOLD}Second Brain:${NC} ${CYAN}http://localhost:8082/second-brain${NC}"
    echo -e "${BOLD}Thanos:${NC} ${CYAN}http://localhost:8082/thanos${NC}"

    echo -e "\n${BOLD}${YELLOW}PROCESSOS EM EXECUÇÃO:${NC}"
    echo -e "${YELLOW}---------------------${NC}"
    ps aux | grep -E "python.*manage.py|uvicorn|integrated_ai_main|npm.*serve" | grep -v grep | awk '{print "- " $11 " (PID: " $2 ")"}'
}

# ===================================================================
# PARAR TODOS OS SERVIÇOS
# ===================================================================

stop_all_services() {
    echo -e "\n${BOLD}${YELLOW}PARANDO TODOS OS SERVIÇOS MENTOR AI${NC}"
    echo -e "${YELLOW}===================================${NC}"
    
    # Kill ALL processes
    show_info "Encerrando todos os processos..."
    
    # Kill Python processes
    pkill -f "python manage.py runserver" 2>/dev/null || true
    pkill -f "uvicorn" 2>/dev/null || true
    pkill -f "python.*main.py" 2>/dev/null || true
    pkill -f "integrated_ai_main" 2>/dev/null || true
    
    # Kill Node processes
    pkill -f "npm run serve" 2>/dev/null || true
    pkill -f "vue-cli-service" 2>/dev/null || true
    
    # Kill by PID file
    if [[ -f "$PID_FILE" ]]; then
        while read -r pid_line; do
            pid=$(echo "$pid_line" | cut -d':' -f2)
            if [[ -n "$pid" ]] && kill -0 "$pid" 2>/dev/null; then
                kill -9 "$pid" 2>/dev/null
                show_info "Processo $pid encerrado"
            fi
        done < "$PID_FILE"
        rm -f "$PID_FILE"
    fi
    
    # Limpar arquivos de PID individuais
    rm -f "$BASE_DIR/.django.pid" "$BASE_DIR/.fastapi.pid" "$BASE_DIR/.frontend.pid"
    
    # Kill processes on specific ports
    for port in 8001 8003 8082; do
        if lsof -i:$port > /dev/null 2>&1; then
            show_warning "Porta $port ainda em uso. Forçando liberação..."
            lsof -t -i:$port | xargs kill -9 2>/dev/null || true
        else
            show_success "Porta $port liberada"
        fi
    done
    
    show_success "Todos os serviços encerrados com sucesso"
}

# ===================================================================
# INICIAR DJANGO
# ===================================================================

start_django() {
    echo -e "\n${BOLD}${YELLOW}INICIANDO DJANGO${NC}"
    echo -e "${YELLOW}================${NC}"
    
    # Kill existing Django processes
    pkill -f "python manage.py runserver" 2>/dev/null || true
    
    # Check if port is in use
    if lsof -i:8003 > /dev/null 2>&1; then
        show_warning "Porta 8003 já está em uso. Tentando liberar..."
        lsof -t -i:8003 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # Go to Django directory
    cd "$DJANGO_DIR"
    
    # Activate virtual environment
    if [ -d "$VENV_DIR" ]; then
        source "$VENV_DIR/bin/activate"
    fi
    
    # Run migrations
    show_info "Executando migrações do Django..."
    python manage.py migrate --noinput 2>/dev/null || true
    
    # Collect static files
    show_info "Coletando arquivos estáticos..."
    python manage.py collectstatic --noinput 2>/dev/null || true
    
    # Start Django
    show_info "Iniciando servidor Django..."
    nohup python manage.py runserver 0.0.0.0:8003 > "$LOG_DIR/django.log" 2>&1 &
    DJANGO_PID=$!
    echo $DJANGO_PID > "$BASE_DIR/.django.pid"
    echo "django:$DJANGO_PID" >> "$PID_FILE"
    
    sleep 3
    
    # Check if it's running
    if kill -0 $DJANGO_PID 2>/dev/null; then
        show_success "Django iniciado com sucesso (PID: $DJANGO_PID)"
        echo -e "${CYAN}Django está disponível em:${NC} http://localhost:8003/"
        echo -e "${CYAN}Django Admin:${NC} http://localhost:8003/admin/"
        echo -e "${CYAN}Para ver logs:${NC} tail -f $LOG_DIR/django.log"
    else
        show_error "Django falhou ao iniciar. Verificando logs:"
        tail -n 20 "$LOG_DIR/django.log"
    fi
}

# ===================================================================
# INICIAR FASTAPI
# ===================================================================

start_fastapi() {
    echo -e "\n${BOLD}${YELLOW}INICIANDO FASTAPI${NC}"
    echo -e "${YELLOW}================${NC}"
    
    # Create necessary directories
    mkdir -p "$BASE_DIR/backend/logs"
    mkdir -p "$BASE_DIR/backend/fastapi_app/logs"
    mkdir -p "$LOG_DIR"
    
    # Kill existing FastAPI processes
    show_info "Encerrando processos FastAPI existentes..."
    pkill -f "uvicorn" 2>/dev/null || true
    pkill -f "python.*main.py" 2>/dev/null || true
    pkill -f "integrated_ai_main" 2>/dev/null || true
    
    # Kill processes on port 8001
    if lsof -i:8001 > /dev/null 2>&1; then
        show_info "Liberando porta 8001..."
        lsof -t -i:8001 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # Check if port is free
    if lsof -i:8001 > /dev/null 2>&1; then
        show_error "Porta 8001 ainda está em uso! Não foi possível liberar."
        return 1
    fi
    
    # Set environment variables
    export PYTHONPATH="$BASE_DIR/backend:$PYTHONPATH"
    export DATABASE_URL="postgresql://localhost/mentor_ai"
    
    # Activate virtual environment
    if [ -d "$VENV_DIR" ]; then
        source "$VENV_DIR/bin/activate"
    fi
    
    # Go to backend directory
    cd "$BASE_DIR/backend"
    
    # Find the appropriate main file
    if [ -f "integrated_ai_main.py" ]; then
        show_info "Iniciando FastAPI com integrated_ai_main.py..."
        nohup python -m uvicorn integrated_ai_main:app --host 0.0.0.0 --port 8001 --reload > "$LOG_DIR/fastapi.log" 2>&1 &
    elif [ -f "fastapi_app/main.py" ]; then
        cd fastapi_app
        show_info "Iniciando FastAPI com main.py..."
        nohup python -m uvicorn main:app --host 0.0.0.0 --port 8001 --reload > "$LOG_DIR/fastapi.log" 2>&1 &
    else
        show_error "Nenhum arquivo principal do FastAPI encontrado!"
        return 1
    fi
    
    FASTAPI_PID=$!
    echo $FASTAPI_PID > "$BASE_DIR/.fastapi.pid"
    echo "fastapi:$FASTAPI_PID" >> "$PID_FILE"
    show_info "FastAPI PID: $FASTAPI_PID"
    
    # Wait for FastAPI to start
    show_info "Aguardando FastAPI iniciar..."
    sleep 5
    
    # Test if it's working
    if curl -s http://localhost:8001/health > /dev/null 2>&1; then
        show_success "FastAPI está rodando!"
        echo -e "${CYAN}Pontos de acesso:${NC}"
        echo -e "   - API Docs: ${CYAN}http://localhost:8001/docs${NC}"
        echo -e "   - Health: ${CYAN}http://localhost:8001/health${NC}"
        echo -e "   - Flashcards: ${CYAN}http://localhost:8001/api/flashcards${NC}"
        echo -e "   - Second Brain: ${CYAN}http://localhost:8001/api/chat${NC}"
        echo -e "   - Thanos: ${CYAN}http://localhost:8001/api/thanos${NC}"
        echo -e "\n${CYAN}Para ver logs:${NC} tail -f $LOG_DIR/fastapi.log"
    else
        show_error "FastAPI não está respondendo. Verificando logs:"
        tail -n 20 "$LOG_DIR/fastapi.log"
        return 1
    fi
    
    return 0
}

# ===================================================================
# INICIAR FRONTEND
# ===================================================================

start_frontend() {
    echo -e "\n${BOLD}${YELLOW}INICIANDO FRONTEND${NC}"
    echo -e "${YELLOW}=================${NC}"
    
    # Kill existing Frontend processes
    pkill -f "npm run serve" 2>/dev/null || true
    pkill -f "vue-cli-service" 2>/dev/null || true
    
    # Check if port is in use
    if lsof -i:8082 > /dev/null 2>&1; then
        show_warning "Porta 8082 já está em uso. Tentando liberar..."
        lsof -t -i:8082 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # Go to Frontend directory
    cd "$FRONTEND_DIR"
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        show_warning "node_modules não encontrado. Instalando dependências..."
        npm install --legacy-peer-deps
    fi
    
    # Start Frontend
    show_info "Iniciando servidor Frontend..."
    nohup npm run serve > "$LOG_DIR/frontend.log" 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$BASE_DIR/.frontend.pid"
    echo "frontend:$FRONTEND_PID" >> "$PID_FILE"
    
    # Wait for frontend to start
    show_info "Aguardando Frontend iniciar..."
    sleep 8
    
    # Check if it's running
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        show_success "Frontend iniciado com sucesso (PID: $FRONTEND_PID)"
        echo -e "${CYAN}Frontend está disponível em:${NC} http://localhost:8082/"
        echo -e "${CYAN}Para ver logs:${NC} tail -f $LOG_DIR/frontend.log"
    else
        show_error "Frontend falhou ao iniciar. Verificando logs:"
        tail -n 20 "$LOG_DIR/frontend.log"
    fi
}

# ===================================================================
# INICIAR TODOS OS SERVIÇOS
# ===================================================================

start_all_services() {
    echo -e "\n${BOLD}${YELLOW}INICIANDO TODOS OS SERVIÇOS MENTOR AI${NC}"
    echo -e "${YELLOW}===================================${NC}"
    
    # Check system dependencies first
    check_system_dependencies
    if [ $? -ne 0 ]; then
        show_error "Algumas dependências estão faltando!"
        echo -ne "\n${BOLD}Deseja instalar as dependências agora? (s/n):${NC} "
        read -r install_deps
        if [[ "$install_deps" == "s" || "$install_deps" == "S" ]]; then
            install_all_dependencies
        else
            show_warning "Continuando sem instalar dependências..."
        fi
    fi
    
    # Stop all services first for clean start
    stop_all_services
    
    # Create log directory
    mkdir -p "$LOG_DIR"
    
    # Start PostgreSQL (if not running)
    echo -ne "${BOLD}PostgreSQL:${NC} "
    if ! pg_isready -h localhost > /dev/null 2>&1; then
        show_warning "PostgreSQL não está rodando. O sistema usará SQLite."
    else
        show_success "PostgreSQL está rodando"
    fi
    
    # Start Django
    start_django
    
    # Start FastAPI
    start_fastapi
    
    # Start Frontend
    start_frontend
    
    # Final status check
    sleep 3
    echo -e "\n${BOLD}${YELLOW}VERIFICAÇÃO FINAL DO SISTEMA${NC}"
    echo -e "${YELLOW}===========================${NC}"
    
    local all_ok=1
    
    # Check Django
    if curl -s http://localhost:8003/ > /dev/null 2>&1; then
        show_success "Django está operacional"
    else
        show_error "Django não está respondendo"
        all_ok=0
    fi
    
    # Check FastAPI
    if curl -s http://localhost:8001/health > /dev/null 2>&1; then
        show_success "FastAPI está operacional"
    else
        show_error "FastAPI não está respondendo"
        all_ok=0
    fi
    
    # Check Frontend
    if curl -s http://localhost:8082 > /dev/null 2>&1; then
        show_success "Frontend está operacional"
    else
        show_error "Frontend não está respondendo"
        all_ok=0
    fi
    
    if [ $all_ok -eq 1 ]; then
        echo -e "\n${BOLD}${GREEN}TODOS OS SERVIÇOS MENTOR AI INICIADOS COM SUCESSO!${NC}"
        echo -e "${YELLOW}--------------------------------------------------${NC}"
        echo -e "${CYAN}Acesse a aplicação em:${NC} ${BOLD}http://localhost:8082/${NC}"
        echo -e "${CYAN}Documentação API:${NC} ${BOLD}http://localhost:8001/docs${NC}"
        echo -e "${CYAN}Django Admin:${NC} ${BOLD}http://localhost:8003/admin/${NC}"
        echo -e "\n${GREEN}Sistema pronto para uso! 🚀${NC}"
    else
        echo -e "\n${ORANGE}Alguns serviços não iniciaram corretamente.${NC}"
        echo -e "${YELLOW}Verifique os logs para mais detalhes.${NC}"
    fi
}

# ===================================================================
# VER LOGS EM TEMPO REAL
# ===================================================================

view_logs() {
    echo -e "\n${BOLD}${YELLOW}LOGS DO SISTEMA${NC}"
    echo -e "${YELLOW}===============${NC}"
    echo -e "${CYAN}1)${NC} FastAPI logs"
    echo -e "${CYAN}2)${NC} Django logs"
    echo -e "${CYAN}3)${NC} Frontend logs"
    echo -e "${CYAN}4)${NC} Todos os logs (múltiplas janelas)"
    echo -ne "${BOLD}Escolha uma opção:${NC} "
    read -r log_option
    
    case $log_option in
        1)
            show_info "Mostrando logs do FastAPI (Ctrl+C para sair)..."
            tail -f "$LOG_DIR/fastapi.log"
            ;;
        2)
            show_info "Mostrando logs do Django (Ctrl+C para sair)..."
            tail -f "$LOG_DIR/django.log"
            ;;
        3)
            show_info "Mostrando logs do Frontend (Ctrl+C para sair)..."
            tail -f "$LOG_DIR/frontend.log"
            ;;
        4)
            show_info "Abrindo todos os logs em modo split..."
            # Use multitail if available, otherwise fallback to multiple tail commands
            if command -v multitail &> /dev/null; then
                multitail "$LOG_DIR/fastapi.log" "$LOG_DIR/django.log" "$LOG_DIR/frontend.log"
            else
                show_warning "multitail não instalado. Mostrando logs sequencialmente..."
                tail -f "$LOG_DIR/fastapi.log" "$LOG_DIR/django.log" "$LOG_DIR/frontend.log"
            fi
            ;;
        *)
            show_error "Opção inválida!"
            ;;
    esac
}

# ===================================================================
# FUNÇÃO PRINCIPAL
# ===================================================================

main() {
    # Create necessary directories
    mkdir -p "$LOG_DIR" "$TEMP_DIR"
    mkdir -p "$BASE_DIR/backend/logs"
    mkdir -p "$BASE_DIR/backend/fastapi_app/logs"
    
    # Show header
    show_header
    
    # Main menu loop
    while true; do
        show_menu
        option=$?
        
        case $option in
            1) check_system_status ;;
            2) start_all_services ;;
            3) install_all_dependencies ;;
            4) start_fastapi ;;
            5) stop_all_services ;;
            6) start_fastapi ;;
            7) start_django ;;
            8) start_frontend ;;
            9) view_logs ;;
            0) 
                echo -e "\n${GREEN}Obrigado por usar o MENTOR AI ULTRA SYSTEM! 👋${NC}"
                exit 0 
                ;;
            *) 
                show_error "Opção inválida!"
                ;;
        esac
        
        echo -e "\n${YELLOW}Pressione ENTER para continuar...${NC}"
        read -r
    done
}

# Execute main function
main