#!/bin/bash

echo "🔍 MENTOR AI SYSTEM STATUS CHECK"
echo "================================"

# Check PostgreSQL
echo -n "PostgreSQL: "
if pg_isready -h localhost > /dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not running"
fi

# Check Django
echo -n "Django (8003): "
if curl -s http://localhost:8003/ > /dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not running"
fi

# Check FastAPI
echo -n "FastAPI (8001): "
if curl -s http://localhost:8001/health > /dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not running"
fi

# Check Frontend
echo -n "Frontend (8082): "
if curl -s http://localhost:8082 > /dev/null 2>&1; then
    echo "✅ Running"
else
    echo "❌ Not running"
fi

echo -e "\n📡 AI Services Status:"
# Check Second Brain
echo -n "Second Brain Chat: "
if curl -s "http://localhost:8001/api/chat/test" > /dev/null 2>&1; then
    echo "✅ Ready"
else
    echo "❌ Not available"
fi

# Check Thanos
echo -n "Thanos AI: "
if curl -s http://localhost:8001/api/thanos/health | grep -q "healthy" 2>/dev/null; then
    echo "✅ Ready"
else
    echo "❌ Not available"
fi

echo -e "\n🌐 Access Points:"
echo "- Main App: http://localhost:8082"
echo "- API Docs: http://localhost:8001/docs"
echo "- Django Admin: http://localhost:8003/admin/"
echo "- Second Brain: http://localhost:8082/second-brain"
echo "- Thanos: http://localhost:8082/thanos"

echo -e "\n📊 Running Processes:"
ps aux | grep -E "python.*manage.py|uvicorn|integrated_ai_main|npm.*serve" | grep -v grep | awk '{print "- " $11 " (PID: " $2 ")"}'