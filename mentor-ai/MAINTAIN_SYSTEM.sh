#!/bin/bash

# ===================================================================
# MENTOR AI - SYSTEM MAINTENANCE & MONITORING SCRIPT
# Keeps everything updated and running perfectly
# ===================================================================

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Directories
BASE_DIR="$(cd "$(dirname "$0")" && pwd)"
DJANGO_DIR="$BASE_DIR/backend/django_app"
FASTAPI_DIR="$BASE_DIR/backend"
FRONTEND_DIR="$BASE_DIR/frontend"
LOG_DIR="$BASE_DIR/logs"
BACKUP_DIR="$BASE_DIR/backups/$(date +%Y%m%d_%H%M%S)"

# ===================================================================
# FUNCTIONS
# ===================================================================

show_header() {
    clear
    echo -e "${BLUE}=====================================================================${NC}"
    echo -e "${BOLD}${BLUE}     MENTOR AI - SYSTEM MAINTENANCE & MONITORING${NC}"
    echo -e "${BLUE}=====================================================================${NC}"
    echo -e "${CYAN}Time: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo -e "${BLUE}=====================================================================${NC}\n"
}

check_service() {
    local service_name=$1
    local port=$2
    local url=$3
    
    if lsof -i:$port > /dev/null 2>&1; then
        if [ -n "$url" ] && curl -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name (Port $port) - Running & Responsive${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️  $service_name (Port $port) - Running but not responding${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ $service_name (Port $port) - Not running${NC}"
        return 2
    fi
}

backup_database() {
    echo -e "\n${CYAN}📦 Creating database backup...${NC}"
    mkdir -p "$BACKUP_DIR"
    
    # Backup PostgreSQL
    if pg_dump -U postgres -d postgres > "$BACKUP_DIR/mentor_ai_backup.sql" 2>/dev/null; then
        echo -e "${GREEN}✅ Database backed up to: $BACKUP_DIR/mentor_ai_backup.sql${NC}"
    else
        echo -e "${YELLOW}⚠️  Could not backup database${NC}"
    fi
}

check_dependencies() {
    echo -e "\n${CYAN}📋 Checking dependencies...${NC}"
    
    # Check Python packages
    cd "$FASTAPI_DIR"
    if pip list --format=freeze | grep -q "fastapi"; then
        echo -e "${GREEN}✅ Python dependencies installed${NC}"
    else
        echo -e "${YELLOW}⚠️  Installing Python dependencies...${NC}"
        pip install -r requirements.txt
    fi
    
    # Check Node packages
    cd "$FRONTEND_DIR"
    if [ -d "node_modules" ]; then
        echo -e "${GREEN}✅ Node dependencies installed${NC}"
    else
        echo -e "${YELLOW}⚠️  Installing Node dependencies...${NC}"
        npm install
    fi
}

fix_common_issues() {
    echo -e "\n${CYAN}🔧 Checking for common issues...${NC}"
    
    # Fix CORS issues
    if ! grep -q "allow_origins" "$FASTAPI_DIR/integrated_ai_main.py" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  CORS might not be configured properly${NC}"
    fi
    
    # Check for port conflicts
    for port in 8001 8003 8082; do
        if lsof -i:$port | grep -v "python\|node" > /dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  Port $port has conflicting process${NC}"
        fi
    done
    
    # Check disk space
    DISK_USAGE=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt 90 ]; then
        echo -e "${RED}⚠️  Disk usage is high: ${DISK_USAGE}%${NC}"
    else
        echo -e "${GREEN}✅ Disk usage is healthy: ${DISK_USAGE}%${NC}"
    fi
}

restart_service() {
    local service=$1
    echo -e "\n${CYAN}🔄 Restarting $service...${NC}"
    
    case $service in
        "django")
            pkill -f "python manage.py runserver" 2>/dev/null
            cd "$DJANGO_DIR"
            nohup python manage.py runserver 0.0.0.0:8003 > "$LOG_DIR/django.log" 2>&1 &
            ;;
        "fastapi")
            pkill -f "uvicorn\|integrated_ai_main" 2>/dev/null
            cd "$FASTAPI_DIR"
            nohup python integrated_ai_main.py > "$LOG_DIR/fastapi_integrated.log" 2>&1 &
            ;;
        "frontend")
            pkill -f "npm run serve" 2>/dev/null
            cd "$FRONTEND_DIR"
            nohup npm run serve > "$LOG_DIR/frontend.log" 2>&1 &
            ;;
    esac
    
    sleep 5
    echo -e "${GREEN}✅ $service restarted${NC}"
}

monitor_logs() {
    echo -e "\n${CYAN}📊 Recent log entries:${NC}"
    echo -e "${YELLOW}--- FastAPI ---${NC}"
    tail -5 "$LOG_DIR/fastapi_integrated.log" 2>/dev/null | sed 's/^/  /'
    
    echo -e "\n${YELLOW}--- Frontend ---${NC}"
    tail -5 "$LOG_DIR/frontend.log" 2>/dev/null | grep -v "webpack" | sed 's/^/  /'
}

test_api_endpoints() {
    echo -e "\n${CYAN}🧪 Testing API endpoints...${NC}"
    
    # Test health endpoint
    if curl -s http://localhost:8001/health | grep -q "healthy"; then
        echo -e "${GREEN}✅ Health check passed${NC}"
    else
        echo -e "${RED}❌ Health check failed${NC}"
    fi
    
    # Test flashcards API
    DECK_COUNT=$(curl -s http://localhost:8001/api/flashcards/decks/ | jq length 2>/dev/null || echo "0")
    echo -e "${GREEN}✅ Flashcards API: $DECK_COUNT decks found${NC}"
    
    # Test database connection
    python3 -c "
import sys
sys.path.append('$FASTAPI_DIR')
from fastapi_app.db.database import SessionLocal
db = SessionLocal()
try:
    db.execute('SELECT 1')
    print('✅ Database connection successful')
except:
    print('❌ Database connection failed')
finally:
    db.close()
" 2>/dev/null || echo -e "${RED}❌ Could not test database${NC}"
}

generate_status_report() {
    echo -e "\n${CYAN}📊 Generating status report...${NC}"
    
    REPORT_FILE="$BASE_DIR/status_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "MENTOR AI - System Status Report"
        echo "Generated: $(date)"
        echo "================================"
        echo ""
        echo "Services Status:"
        check_service "PostgreSQL" 5432 ""
        check_service "Django" 8003 "http://localhost:8003"
        check_service "FastAPI" 8001 "http://localhost:8001/health"
        check_service "Frontend" 8082 "http://localhost:8082"
        echo ""
        echo "Database Stats:"
        python3 -c "
import sys
sys.path.append('$FASTAPI_DIR')
from fastapi_app.db.database import SessionLocal
from sqlalchemy import text
db = SessionLocal()
try:
    users = db.execute(text('SELECT COUNT(*) FROM users')).scalar()
    decks = db.execute(text('SELECT COUNT(*) FROM decks')).scalar()
    cards = db.execute(text('SELECT COUNT(*) FROM flashcards')).scalar()
    print(f'  Users: {users}')
    print(f'  Decks: {decks}')
    print(f'  Flashcards: {cards}')
except Exception as e:
    print(f'  Error: {e}')
finally:
    db.close()
" 2>/dev/null
        echo ""
        echo "Disk Usage: $(df -h . | awk 'NR==2 {print $5}')"
        echo "Memory Usage: $(free -h | awk 'NR==2 {print $3 "/" $2}')"
    } > "$REPORT_FILE"
    
    echo -e "${GREEN}✅ Report saved to: $REPORT_FILE${NC}"
}

auto_fix_mode() {
    echo -e "\n${PURPLE}🤖 AUTO-FIX MODE ACTIVATED${NC}"
    
    # Check each service and restart if needed
    if ! check_service "PostgreSQL" 5432 "" > /dev/null 2>&1; then
        echo -e "${YELLOW}Attempting to start PostgreSQL...${NC}"
        brew services start postgresql@14 2>/dev/null || true
    fi
    
    if ! check_service "Django" 8003 "http://localhost:8003" > /dev/null 2>&1; then
        restart_service "django"
    fi
    
    if ! check_service "FastAPI" 8001 "http://localhost:8001/health" > /dev/null 2>&1; then
        restart_service "fastapi"
    fi
    
    if ! check_service "Frontend" 8082 "http://localhost:8082" > /dev/null 2>&1; then
        restart_service "frontend"
    fi
    
    echo -e "${GREEN}✅ Auto-fix completed${NC}"
}

# ===================================================================
# MAIN MENU
# ===================================================================

main_menu() {
    while true; do
        show_header
        
        echo -e "${YELLOW}MAINTENANCE OPTIONS:${NC}"
        echo -e "${CYAN}1)${NC} Full System Check"
        echo -e "${CYAN}2)${NC} Auto-Fix Issues"
        echo -e "${CYAN}3)${NC} Restart Service"
        echo -e "${CYAN}4)${NC} Backup Database"
        echo -e "${CYAN}5)${NC} Check Dependencies"
        echo -e "${CYAN}6)${NC} Monitor Logs"
        echo -e "${CYAN}7)${NC} Test API Endpoints"
        echo -e "${CYAN}8)${NC} Generate Status Report"
        echo -e "${CYAN}9)${NC} View Recent Errors"
        echo -e "${CYAN}0)${NC} Exit"
        echo -e "${YELLOW}----------------------------------------${NC}"
        echo -ne "${BOLD}Choose an option:${NC} "
        read -r choice
        
        case $choice in
            1)
                echo -e "\n${CYAN}🔍 PERFORMING FULL SYSTEM CHECK${NC}"
                check_service "PostgreSQL" 5432 ""
                check_service "Django" 8003 "http://localhost:8003"
                check_service "FastAPI" 8001 "http://localhost:8001/health"
                check_service "Frontend" 8082 "http://localhost:8082"
                check_dependencies
                fix_common_issues
                test_api_endpoints
                ;;
            2)
                auto_fix_mode
                ;;
            3)
                echo -e "\n${CYAN}Which service to restart?${NC}"
                echo "1) Django"
                echo "2) FastAPI"
                echo "3) Frontend"
                echo "4) All services"
                read -r service_choice
                
                case $service_choice in
                    1) restart_service "django" ;;
                    2) restart_service "fastapi" ;;
                    3) restart_service "frontend" ;;
                    4) 
                        restart_service "django"
                        restart_service "fastapi"
                        restart_service "frontend"
                        ;;
                esac
                ;;
            4)
                backup_database
                ;;
            5)
                check_dependencies
                ;;
            6)
                monitor_logs
                ;;
            7)
                test_api_endpoints
                ;;
            8)
                generate_status_report
                ;;
            9)
                echo -e "\n${RED}📋 Recent Errors:${NC}"
                echo -e "${YELLOW}--- FastAPI Errors ---${NC}"
                grep -i "error\|exception" "$LOG_DIR/fastapi_integrated.log" 2>/dev/null | tail -10 | sed 's/^/  /'
                echo -e "\n${YELLOW}--- Frontend Errors ---${NC}"
                grep -i "error\|failed" "$LOG_DIR/frontend.log" 2>/dev/null | tail -10 | sed 's/^/  /'
                ;;
            0)
                echo -e "\n${GREEN}Exiting maintenance mode. Stay healthy! 🚀${NC}"
                exit 0
                ;;
            *)
                echo -e "\n${RED}Invalid option!${NC}"
                ;;
        esac
        
        echo -e "\n${YELLOW}Press ENTER to continue...${NC}"
        read -r
    done
}

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Start main menu
main_menu