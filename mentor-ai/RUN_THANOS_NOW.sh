#!/bin/bash

# RUN THANOS NOW - Quick Start Script
# Script para iniciar o Thanos rapidamente

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

clear

echo -e "${PURPLE}╔════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║         THANOS AI - QUICK START        ║${NC}"
echo -e "${PURPLE}╚════════════════════════════════════════╝${NC}"
echo ""

cd /Users/<USER>/Projetos/meds-AI-Maketing_Site/mentor-ai

# Verificar se o script completo existe
if [ -f "THANOS_COMPLETE_INTEGRATION.sh" ]; then
    echo -e "${GREEN}Starting Thanos with complete integration...${NC}"
    ./THANOS_COMPLETE_INTEGRATION.sh
else
    echo -e "${RED}Integration script not found!${NC}"
    echo -e "${YELLOW}Creating and running it now...${NC}"
    
    # Criar o script se não existir
    curl -s https://raw.githubusercontent.com/your-repo/mentor-ai/main/THANOS_COMPLETE_INTEGRATION.sh > THANOS_COMPLETE_INTEGRATION.sh
    chmod +x THANOS_COMPLETE_INTEGRATION.sh
    ./THANOS_COMPLETE_INTEGRATION.sh
fi 