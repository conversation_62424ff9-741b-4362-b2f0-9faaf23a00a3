#!/bin/bash
set -e

echo "🚀 Initializing Mentor AI Infrastructure..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "📝 Creating .env from .env.example..."
    cp .env.example .env
    echo "⚠️  Please update .env with your API keys and configurations!"
    echo "Press any key to continue after updating .env..."
    read -n 1 -s
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p data/{mongodb,postgres,redis,clickhouse,kafka,zookeeper,prometheus,grafana,elasticsearch}
mkdir -p logs

# Initialize Kong database
echo "🔧 Initializing Kong database..."
docker-compose up -d kong-db
sleep 10

# Run Kong migrations
echo "🔧 Running Kong migrations..."
docker run --rm \
    --network mentor-ai_mentor-ai-network \
    -e "KONG_DATABASE=postgres" \
    -e "KONG_PG_HOST=kong-db" \
    -e "KONG_PG_USER=kong" \
    -e "KONG_PG_PASSWORD=kongpass" \
    kong:3.4-alpine kong migrations bootstrap

# Start core infrastructure
echo "🏗️ Starting core infrastructure..."
docker-compose up -d mongodb postgres redis clickhouse zookeeper kafka elasticsearch

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Start remaining services
echo "🚀 Starting all services..."
docker-compose up -d

# Wait for everything to be ready
echo "⏳ Waiting for all services to be ready..."
sleep 20

# Check service health
echo "🏥 Checking service health..."
./scripts/health-check.sh

echo "✅ Mentor AI is ready!"
echo ""
echo "🌐 Access points:"
echo "  - API Gateway: http://localhost:8000"
echo "  - Frontend: http://localhost:5000"
echo "  - Grafana: http://localhost:3001 (admin/admin)"
echo "  - Prometheus: http://localhost:9090"
echo "  - Jaeger: http://localhost:16686"
echo "  - Kibana: http://localhost:5601"
echo ""
echo "📚 Documentation: http://localhost:8000/docs"