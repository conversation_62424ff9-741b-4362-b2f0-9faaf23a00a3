#!/bin/bash

echo "🏥 Running health checks..."

# Function to check service health
check_service() {
    local service_name=$1
    local url=$2
    
    if curl -f -s "$url" > /dev/null; then
        echo "✅ $service_name is healthy"
        return 0
    else
        echo "❌ $service_name is not responding"
        return 1
    fi
}

# Check services
check_service "Kong API Gateway" "http://localhost:8001/status"
check_service "Flashcards Service" "http://localhost:8001/health"
check_service "MongoDB" "http://localhost:27017"
check_service "PostgreSQL" "localhost:5432"
check_service "Redis" "localhost:6379"
check_service "Prometheus" "http://localhost:9090/-/healthy"
check_service "Grafana" "http://localhost:3001/api/health"
check_service "Elasticsearch" "http://localhost:9200/_cluster/health"

echo ""
echo "🏥 Health check complete!"