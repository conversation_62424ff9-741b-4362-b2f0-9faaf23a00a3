"""
Script de teste da integração do Thanos
"""
import requests
import json
import time

BASE_URL = "http://localhost:8001"

def test_thanos():
    print("🧪 Testing Thanos Integration...")
    
    # 1. Health check
    print("\n1. Testing health check...")
    response = requests.get(f"{BASE_URL}/api/thanos/health")
    if response.status_code == 200:
        print("✅ Health check passed:", response.json())
    else:
        print("❌ Health check failed:", response.status_code)
    
    # 2. Initialize Thanos
    print("\n2. Testing Thanos initialization...")
    init_data = {
        "provider": "Anthropic",
        "model": "claude-3-haiku",
        "api_key": os.getenv("ANTHROPIC_API_KEY", "dummy_key"),
        "language": "pt",
        "use_rag": False,
        "temperature": 0.7,
        "max_tokens": 2000
    }
    
    response = requests.post(f"{BASE_URL}/api/thanos/initialize", json=init_data)
    if response.status_code == 200:
        session_data = response.json()
        print("✅ Initialization passed:", session_data)
        session_id = session_data["session_id"]
        
        # 3. Send message
        print("\n3. Testing message sending...")
        message_data = {
            "message": "Olá Thanos! Como você pode me ajudar?",
            "use_rag": False,
            "language": "pt"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/thanos/conversations/{session_id}/message", 
            json=message_data
        )
        if response.status_code == 200:
            print("✅ Message sent successfully:", response.json())
        else:
            print("❌ Message sending failed:", response.status_code, response.text)
    else:
        print("❌ Initialization failed:", response.status_code, response.text)

if __name__ == "__main__":
    # Aguardar servidor iniciar
    print("Waiting for server to start...")
    time.sleep(3)
    
    test_thanos()
