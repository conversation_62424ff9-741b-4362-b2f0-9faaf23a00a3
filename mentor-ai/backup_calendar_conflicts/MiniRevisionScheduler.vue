<template>
  <div class="mini-revision-scheduler">
    <!-- Background Effects -->
    <div class="scheduler-background">
      <div class="gradient-overlay"></div>
      <div class="floating-orbs">
        <div v-for="i in 5" :key="`orb-${i}`" :class="`orb orb-${i}`"></div>
      </div>
    </div>

    <!-- Content -->
    <div class="scheduler-content">
      <!-- Header -->
      <div class="scheduler-header">
        <h3 class="scheduler-title">
          <i class="fas fa-brain"></i>
          Agendador de Revisões Espaçadas
        </h3>
        <p class="scheduler-subtitle">
          Otimize sua retenção com revisões científicas
        </p>
      </div>

      <!-- Form Tabs -->
      <div class="form-tabs">
        <button 
          :class="['tab-button', { active: activeTab === 'teorico' }]"
          @click="activeTab = 'teorico'"
        >
          <i class="fas fa-book"></i>
          Estudo Teórico
        </button>
        <button 
          :class="['tab-button', { active: activeTab === 'pratico' }]"
          @click="activeTab = 'pratico'"
        >
          <i class="fas fa-tasks"></i>
          Revisão Prática
        </button>
        <div class="tab-indicator" :style="tabIndicatorStyle"></div>
      </div>

      <!-- Estudo Teórico Form -->
      <transition name="form-fade">
        <form v-if="activeTab === 'teorico'" @submit.prevent="agendarEstudoTeorico" class="scheduler-form">
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-bookmark"></i>
              Matéria / Tópico
            </label>
            <input 
              v-model="estudoTeorico.materia" 
              type="text" 
              required
              class="form-input"
              placeholder="Ex: Anatomia - Sistema Nervoso"
            />
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-graduation-cap"></i>
              Disciplina
            </label>
            <select v-model="estudoTeorico.disciplina" class="form-select" required>
              <option value="">Selecione uma disciplina</option>
              <option 
                v-for="subject in subjects" 
                :key="subject.id" 
                :value="subject.id"
              >
                {{ subject.name }}
              </option>
            </select>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-calendar"></i>
                Data do Estudo
              </label>
              <input 
                v-model="estudoTeorico.data" 
                type="date" 
                required
                class="form-input"
                :min="today"
              />
            </div>

            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-clock"></i>
                Horário
              </label>
              <input 
                v-model="estudoTeorico.hora" 
                type="time" 
                required
                class="form-input"
              />
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-signal"></i>
              Grau de Dificuldade
            </label>
            <div class="difficulty-options">
              <button 
                type="button"
                v-for="level in dificuldades"
                :key="level.nome"
                :class="['difficulty-btn', { active: estudoTeorico.dificuldade === level.nome }]"
                @click="estudoTeorico.dificuldade = level.nome"
              >
                <i :class="level.icon"></i>
                <span>{{ level.nome }}</span>
                <small>{{ level.descricao }}</small>
              </button>
            </div>
          </div>

          <div v-if="estudoTeorico.dificuldade" class="schedule-preview">
            <h4>
              <i class="fas fa-calendar-check"></i>
              Revisões Automáticas
            </h4>
            <div class="preview-timeline">
              <div 
                v-for="(revisao, index) in previsualizarRevisoes()" 
                :key="index"
                class="preview-item"
              >
                <div class="timeline-dot"></div>
                <div class="preview-content">
                  <strong>{{ revisao.titulo }}</strong>
                  <span>{{ revisao.data }}</span>
                </div>
              </div>
            </div>
          </div>

          <button type="submit" class="submit-button">
            <i class="fas fa-magic"></i>
            Agendar Estudo e Revisões
            <span class="button-glow"></span>
          </button>
        </form>
      </transition>

      <!-- Revisão Prática Form -->
      <transition name="form-fade">
        <form v-if="activeTab === 'pratico'" @submit.prevent="agendarRevisaoPratica" class="scheduler-form">
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-bookmark"></i>
              Matéria da Revisão
            </label>
            <input 
              v-model="revisaoPratica.materia" 
              type="text" 
              required
              class="form-input"
              placeholder="Ex: Farmacologia - Antibióticos"
            />
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-graduation-cap"></i>
              Disciplina
            </label>
            <select v-model="revisaoPratica.disciplina" class="form-select" required>
              <option value="">Selecione uma disciplina</option>
              <option 
                v-for="subject in subjects" 
                :key="subject.id" 
                :value="subject.id"
              >
                {{ subject.name }}
              </option>
            </select>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-calendar"></i>
                Data da Revisão
              </label>
              <input 
                v-model="revisaoPratica.data" 
                type="date" 
                required
                class="form-input"
                :min="today"
              />
            </div>

            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-clock"></i>
                Horário
              </label>
              <input 
                v-model="revisaoPratica.hora" 
                type="time" 
                required
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-check-circle"></i>
                Questões Acertadas
              </label>
              <input 
                v-model.number="revisaoPratica.acertos" 
                type="number" 
                min="0"
                required
                class="form-input"
              />
            </div>

            <div class="form-group">
              <label class="form-label">
                <i class="fas fa-times-circle"></i>
                Questões Erradas
              </label>
              <input 
                v-model.number="revisaoPratica.erros" 
                type="number" 
                min="0"
                required
                class="form-input"
              />
            </div>
          </div>

          <div v-if="desempenhoCalculado" class="performance-display">
            <div class="performance-meter">
              <div class="meter-fill" :style="{ width: desempenhoCalculado + '%' }"></div>
              <span class="meter-label">{{ desempenhoCalculado }}%</span>
            </div>
            <p class="performance-feedback">{{ feedbackDesempenho }}</p>
          </div>

          <button type="submit" class="submit-button">
            <i class="fas fa-calendar-plus"></i>
            Agendar Próxima Revisão
            <span class="button-glow"></span>
          </button>
        </form>
      </transition>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { format, addDays } from 'date-fns'
import { ptBR } from 'date-fns/locale'

export default {
  name: 'MiniRevisionScheduler',
  emits: ['close', 'revision-scheduled'],
  setup(props, { emit }) {
    const store = useStore()
    const activeTab = ref('teorico')
    const today = format(new Date(), 'yyyy-MM-dd')

    // Form data
    const estudoTeorico = ref({
      materia: '',
      disciplina: '',
      data: today,
      hora: '09:00',
      dificuldade: ''
    })

    const revisaoPratica = ref({
      materia: '',
      disciplina: '',
      data: today,
      hora: '14:00',
      acertos: 0,
      erros: 0
    })

    const dificuldades = [
      {
        nome: 'Fácil',
        icon: 'fas fa-smile',
        descricao: 'Primeira revisão em 2 dias',
        intervalos: [2, 7, 21, 60]
      },
      {
        nome: 'Difícil',
        icon: 'fas fa-frown',
        descricao: 'Primeira revisão em 1 dia',
        intervalos: [1, 3, 7, 14, 30]
      }
    ]

    // Computed
    const subjects = computed(() => store.getters['calendar/getSubjects'] || [])
    
    const tabIndicatorStyle = computed(() => ({
      transform: activeTab.value === 'teorico' ? 'translateX(0)' : 'translateX(100%)'
    }))

    const desempenhoCalculado = computed(() => {
      const total = revisaoPratica.value.acertos + revisaoPratica.value.erros
      if (total === 0) return null
      return Math.round((revisaoPratica.value.acertos / total) * 100)
    })

    const feedbackDesempenho = computed(() => {
      const perf = desempenhoCalculado.value
      if (perf === null) return ''
      if (perf >= 80) return 'Excelente! Próxima revisão em 7 dias'
      if (perf >= 60) return 'Bom desempenho! Próxima revisão em 3 dias'
      return 'Reforce o estudo! Próxima revisão amanhã'
    })

    // Methods
    const previsualizarRevisoes = () => {
      if (!estudoTeorico.value.dificuldade) return []
      
      const dificuldade = dificuldades.find(d => d.nome === estudoTeorico.value.dificuldade)
      const dataBase = new Date(estudoTeorico.value.data)
      
      return dificuldade.intervalos.map((dias, index) => ({
        titulo: `Revisão ${index + 1}`,
        data: format(addDays(dataBase, dias), "dd 'de' MMMM", { locale: ptBR })
      }))
    }

    const agendarEstudoTeorico = async () => {
      const dificuldade = dificuldades.find(d => d.nome === estudoTeorico.value.dificuldade)
      const dataBase = new Date(`${estudoTeorico.value.data}T${estudoTeorico.value.hora}`)
      
      // Criar evento inicial
      const eventoInicial = {
        title: `Estudo: ${estudoTeorico.value.materia}`,
        start: dataBase.toISOString(),
        end: new Date(dataBase.getTime() + 60 * 60 * 1000).toISOString(),
        subject: estudoTeorico.value.disciplina,
        type: 'Estudo',
        color: '#6366f1',
        description: 'Estudo teórico inicial'
      }

      await store.dispatch('calendar/addEvent', eventoInicial)

      // Criar revisões automáticas
      for (let i = 0; i < dificuldade.intervalos.length; i++) {
        const dias = dificuldade.intervalos[i]
        const dataRevisao = addDays(dataBase, dias)
        
        const eventoRevisao = {
          title: `Revisão ${i + 1}: ${estudoTeorico.value.materia}`,
          start: new Date(dataRevisao.setHours(9, 0, 0, 0)).toISOString(),
          end: new Date(dataRevisao.setHours(10, 0, 0, 0)).toISOString(),
          subject: estudoTeorico.value.disciplina,
          type: 'Revisão',
          color: '#8b5cf6',
          description: `Revisão espaçada ${i + 1}`,
          isRevision: true,
          priority: i < 2 ? 'Alta' : 'Média'
        }

        await store.dispatch('calendar/addEvent', eventoRevisao)
      }

      emit('revision-scheduled', {
        type: 'teorico',
        total: dificuldade.intervalos.length + 1
      })

      // Reset form
      estudoTeorico.value = {
        materia: '',
        disciplina: '',
        data: today,
        hora: '09:00',
        dificuldade: ''
      }
    }

    const agendarRevisaoPratica = async () => {
      const perf = desempenhoCalculado.value
      let proximoDias = 1
      
      if (perf >= 80) proximoDias = 7
      else if (perf >= 60) proximoDias = 3
      
      const dataRevisao = addDays(new Date(`${revisaoPratica.value.data}T${revisaoPratica.value.hora}`), proximoDias)
      
      const evento = {
        title: `Revisão: ${revisaoPratica.value.materia}`,
        start: dataRevisao.toISOString(),
        end: new Date(dataRevisao.getTime() + 60 * 60 * 1000).toISOString(),
        subject: revisaoPratica.value.disciplina,
        type: 'Revisão',
        color: '#f59e0b',
        description: `Desempenho anterior: ${perf}%`,
        isRevision: true,
        priority: perf < 60 ? 'Alta' : 'Média',
        performance: perf
      }

      await store.dispatch('calendar/addEvent', evento)

      emit('revision-scheduled', {
        type: 'pratico',
        total: 1
      })

      // Reset form
      revisaoPratica.value = {
        materia: '',
        disciplina: '',
        data: today,
        hora: '14:00',
        acertos: 0,
        erros: 0
      }
    }

    onMounted(() => {
      store.dispatch('calendar/fetchSubjects')
    })

    return {
      activeTab,
      today,
      estudoTeorico,
      revisaoPratica,
      dificuldades,
      subjects,
      tabIndicatorStyle,
      desempenhoCalculado,
      feedbackDesempenho,
      previsualizarRevisoes,
      agendarEstudoTeorico,
      agendarRevisaoPratica
    }
  }
}
</script>

<style scoped>
.mini-revision-scheduler {
  position: relative;
  width: 100%;
  max-width: 700px;
  background: rgba(15, 23, 42, 0.95);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

/* Background Effects */
.scheduler-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.1) 0%,
    rgba(139, 92, 246, 0.05) 50%,
    rgba(99, 102, 241, 0.1) 100%);
}

.floating-orbs {
  position: absolute;
  width: 100%;
  height: 100%;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.6;
  animation: float 20s infinite ease-in-out;
}

.orb-1 {
  width: 150px;
  height: 150px;
  background: rgba(99, 102, 241, 0.3);
  top: -50px;
  left: -50px;
  animation-delay: 0s;
}

.orb-2 {
  width: 100px;
  height: 100px;
  background: rgba(139, 92, 246, 0.3);
  top: 50%;
  right: -30px;
  animation-delay: 5s;
}

.orb-3 {
  width: 80px;
  height: 80px;
  background: rgba(168, 85, 247, 0.3);
  bottom: -20px;
  left: 30%;
  animation-delay: 10s;
}

.orb-4 {
  width: 120px;
  height: 120px;
  background: rgba(99, 102, 241, 0.2);
  top: 30%;
  left: -40px;
  animation-delay: 15s;
}

.orb-5 {
  width: 60px;
  height: 60px;
  background: rgba(139, 92, 246, 0.4);
  bottom: 20%;
  right: 10%;
  animation-delay: 7s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
}

/* Content */
.scheduler-content {
  position: relative;
  z-index: 1;
  padding: 2rem;
}

/* Header */
.scheduler-header {
  text-align: center;
  margin-bottom: 2rem;
}

.scheduler-title {
  font-size: 2rem;
  font-weight: 700;
  color: #f1f5f9;
  margin: 0 0 0.7rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.9rem;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.scheduler-title i {
  font-size: 1.8rem;
  color: #6366f1;
  filter: drop-shadow(0 0 8px rgba(99, 102, 241, 0.5));
  animation: pulse-icon 2s infinite;
}

@keyframes pulse-icon {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.scheduler-subtitle {
  font-size: 1.15rem;
  color: #94a3b8;
  margin: 0.3rem 0 0;
  font-weight: 500;
}

/* Tabs */
.form-tabs {
  display: flex;
  gap: 0;
  margin-bottom: 2rem;
  position: relative;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  padding: 4px;
}

.tab-button {
  flex: 1;
  padding: 1rem 1.8rem;
  background: transparent;
  border: none;
  color: #94a3b8;
  font-size: 1.05rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.7rem;
  border-radius: 8px;
  position: relative;
  z-index: 2;
}

.tab-button:hover {
  color: #e2e8f0;
}

.tab-button.active {
  color: #f1f5f9;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.tab-button i {
  font-size: 1.25rem;
}

.tab-indicator {
  position: absolute;
  top: 4px;
  left: 4px;
  width: calc(50% - 4px);
  height: calc(100% - 8px);
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 8px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Form */
.scheduler-form {
  display: flex;
  flex-direction: column;
  gap: 1.8rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  font-size: 1rem;
  font-weight: 600;
  color: #94a3b8;
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin-bottom: 0.3rem;
}

.form-label i {
  color: #6366f1;
  font-size: 1.1rem;
}

.form-input,
.form-select {
  width: 100%;
  padding: 1rem 1.2rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  color: #f1f5f9;
  font-size: 1.05rem;
  transition: all 0.3s;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(30, 41, 59, 0.8);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input::placeholder {
  color: #64748b;
}

/* Difficulty Options */
.difficulty-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.2rem;
}

.difficulty-btn {
  padding: 1.2rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.1);
  border-radius: 14px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.7rem;
  text-align: center;
}

.difficulty-btn:hover {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(148, 163, 184, 0.2);
}

.difficulty-btn.active {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.5);
}

.difficulty-btn i {
  font-size: 1.8rem;
  color: #6366f1;
}

.difficulty-btn span {
  font-weight: 600;
  font-size: 1.1rem;
  color: #f1f5f9;
}

.difficulty-btn small {
  font-size: 0.85rem;
  color: #94a3b8;
}

/* Schedule Preview */
.schedule-preview {
  background: rgba(99, 102, 241, 0.15);
  border: 1px solid rgba(99, 102, 241, 0.25);
  border-radius: 14px;
  padding: 1.8rem;
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.1);
}

.schedule-preview h4 {
  font-size: 1.15rem;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 1.2rem 0;
  display: flex;
  align-items: center;
  gap: 0.7rem;
}

.schedule-preview h4 i {
  color: #6366f1;
}

.preview-timeline {
  display: flex;
  flex-direction: column;
  gap: 1.3rem;
  position: relative;
  padding-left: 2rem;
}

.preview-timeline::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 10px;
  bottom: 10px;
  width: 3px;
  background: linear-gradient(to bottom, rgba(99, 102, 241, 0.7), rgba(139, 92, 246, 0.3));
  border-radius: 3px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.timeline-dot {
  position: absolute;
  left: -2rem;
  width: 18px;
  height: 18px;
  background: #6366f1;
  border: 3px solid #0f172a;
  border-radius: 50%;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 6px rgba(99, 102, 241, 0.3);
  }
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  padding: 0.5rem 0.8rem;
  background: rgba(30, 41, 59, 0.4);
  border-radius: 10px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.preview-content strong {
  font-size: 1rem;
  color: #f1f5f9;
  font-weight: 600;
}

.preview-content span {
  font-size: 0.85rem;
  color: #94a3b8;
}

/* Performance Display */
.performance-display {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 14px;
  padding: 1.8rem;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  border: 1px solid rgba(148, 163, 184, 0.15);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.performance-meter {
  position: relative;
  height: 38px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 19px;
  overflow: hidden;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.2);
}

.meter-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 19px;
  transition: width 0.8s ease;
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
  animation: pulse-fill 2s infinite;
}

@keyframes pulse-fill {
  0%, 100% {
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
  }
  50% {
    box-shadow: 0 0 25px rgba(99, 102, 241, 0.7);
  }
}

.meter-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: 700;
  font-size: 1.15rem;
  color: #f1f5f9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.performance-feedback {
  font-size: 1rem;
  color: #94a3b8;
  text-align: center;
  margin: 0.5rem 0 0;
  padding: 0.5rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

/* Submit Button */
.submit-button {
  width: 100%;
  padding: 1.2rem 2.5rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 14px;
  color: white;
  font-size: 1.15rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.7rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  margin-top: 0.5rem;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

.submit-button:active {
  transform: translateY(0);
}

.button-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s;
}

.submit-button:hover .button-glow {
  opacity: 1;
}

/* Transitions */
.form-fade-enter-active,
.form-fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.form-fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.form-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Responsive */
@media (max-width: 768px) {
  .scheduler-content {
    padding: 1.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .difficulty-options {
    grid-template-columns: 1fr;
  }
}
</style>