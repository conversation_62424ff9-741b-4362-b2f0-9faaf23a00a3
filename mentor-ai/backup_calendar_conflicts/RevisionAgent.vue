<template>
    <div class="revision-agent">
      <div class="agent-header">
      <p class="agent-description">
        O agente de revisão ajuda você a agendar revisões espaçadas com base na Curva de Esquecimento.
      </p>
        </div>
        
    <div class="agent-body">
      <div class="revision-form">
        <div class="form-row">
          <div class="form-group">
            <label for="revision-subject">Disciplina</label>
            <select id="revision-subject" v-model="newRevision.subject" required>
              <option value="" disabled>Selecione uma disciplina</option>
              <option v-for="subject in subjects" :key="subject" :value="subject">
                {{ subject }}
              </option>
            </select>
        </div>
        
          <div class="form-group">
            <label for="revision-title">T<PERSON><PERSON><PERSON> da Revisão</label>
            <input 
              type="text" 
              id="revision-title" 
              v-model="newRevision.title" 
              placeholder="Ex: Revisão de Anatomia - Sistema Nervoso" 
              required
            />
        </div>
      </div>
      
        <div class="form-row">
          <div class="form-group">
            <label for="revision-date">Data Inicial</label>
            <input 
              type="date" 
              id="revision-date" 
              v-model="newRevision.date" 
              :min="todayFormatted" 
              required
            />
            </div>
            
          <div class="form-group">
            <label for="revision-time">Horário</label>
                      <input 
              type="time" 
              id="revision-time" 
              v-model="newRevision.startTime" 
              required
            />
          </div>
          
              <div class="form-group">
            <label for="revision-duration">Duração (minutos)</label>
            <select id="revision-duration" v-model="newRevision.durationInMinutes">
              <option value="30">30 min</option>
              <option value="45">45 min</option>
              <option value="60">1 hora</option>
              <option value="90">1h 30min</option>
              <option value="120">2 horas</option>
                </select>
              </div>
              </div>
              
              <div class="form-group">
          <label for="revision-difficulty">Dificuldade do Conteúdo</label>
                <div class="difficulty-selector">
                  <button 
                    type="button" 
              @click="newRevision.difficulty = 'Fácil'"
              :class="{ active: newRevision.difficulty === 'Fácil' }"
              class="difficulty-btn easy"
                  >
                    Fácil
                  </button>
                  <button 
                    type="button" 
              @click="newRevision.difficulty = 'Médio'"
              :class="{ active: newRevision.difficulty === 'Médio' }"
              class="difficulty-btn medium"
                  >
                    Médio
                  </button>
                  <button 
                    type="button" 
              @click="newRevision.difficulty = 'Difícil'"
              :class="{ active: newRevision.difficulty === 'Difícil' }"
              class="difficulty-btn hard"
                  >
                    Difícil
                  </button>
                </div>
              </div>
              
                <div class="form-group">
          <label for="revision-description">Descrição/Tópicos</label>
          <textarea 
            id="revision-description" 
            v-model="newRevision.description" 
            placeholder="Descreva os tópicos que serão revisados..."
            rows="3"
          ></textarea>
                </div>
                
        <div class="revision-schedule">
          <h3>Cronograma de Revisão</h3>
          <p class="schedule-description">
            Com base na curva de esquecimento e na dificuldade informada, 
            sugerimos o seguinte cronograma:
          </p>
          
          <div class="schedule-dates">
            <div 
              v-for="(date, index) in revisionSchedule" 
                    :key="index"
              class="schedule-date"
            >
              <div class="date-number">{{ index + 1 }}</div>
              <div class="date-info">
                <div class="date-value">{{ formatDate(date) }}</div>
                <div class="date-label">
                  {{ index === 0 ? 'Primeira revisão' : `Revisão #${index + 1}` }}
                  </div>
                </div>
              </div>
          </div>
            </div>
            
        <div class="form-actions">
          <button type="button" @click="resetForm" class="btn-secondary">
            Limpar
            </button>
          <button type="button" @click="scheduleRevision" class="btn-primary">
            <font-awesome-icon icon="calendar-check" />
            Agendar Revisão
            </button>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
import { ref, computed, watch } from 'vue';
  
  export default {
    name: 'RevisionAgent',
  props: {
    selectedDate: {
      type: Date,
      default: () => new Date()
    },
    events: {
      type: Array,
      default: () => []
    }
  },
  emits: ['revision-scheduled'],
  setup(props, { emit }) {
    const subjects = [
      'Anatomia', 'Fisiologia', 'Bioquímica', 'Farmacologia', 
      'Patologia', 'Microbiologia', 'Imunologia', 'Cardiologia',
      'Neurologia', 'Psiquiatria', 'Pediatria', 'Ginecologia',
      'Cirurgia', 'Clínica Médica', 'Saúde Pública'
    ];
    
    const today = new Date();
    const todayFormatted = computed(() => {
      return today.toISOString().split('T')[0];
    });
    
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleDateString('pt-BR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };
    
    const newRevision = ref({
        title: '',
        subject: '',
      date: props.selectedDate.toISOString().split('T')[0],
      startTime: '14:00',
        durationInMinutes: 60,
      difficulty: 'Médio',
        description: '',
      color: '#2a9d8f' // Cor padrão
    });
    
    // Observar mudanças na data selecionada
    watch(() => props.selectedDate, (newDate) => {
      if (newDate) {
        newRevision.value.date = newDate.toISOString().split('T')[0];
      }
    });
    
    // Calcular cronograma de revisões baseado na curva de esquecimento
    const revisionSchedule = computed(() => {
      if (!newRevision.value.date) return [];
      
      const baseDate = new Date(newRevision.value.date);
      const schedule = [];
      
      // Primeira revisão (1 dia depois)
      const firstRevision = new Date(baseDate);
      firstRevision.setDate(firstRevision.getDate() + 1);
      schedule.push(firstRevision.toISOString().split('T')[0]);
      
      // Segunda revisão (7 dias depois)
      const secondRevision = new Date(baseDate);
      secondRevision.setDate(secondRevision.getDate() + 7);
      schedule.push(secondRevision.toISOString().split('T')[0]);
      
      // Terceira revisão (16 dias depois)
      const thirdRevision = new Date(baseDate);
      thirdRevision.setDate(thirdRevision.getDate() + 16);
      schedule.push(thirdRevision.toISOString().split('T')[0]);
      
      // Quarta revisão (35 dias depois)
      const fourthRevision = new Date(baseDate);
      fourthRevision.setDate(fourthRevision.getDate() + 35);
      schedule.push(fourthRevision.toISOString().split('T')[0]);
      
      return schedule;
    });
    
    // Agendar revisão
    const scheduleRevision = () => {
      // Validar formulário
      if (!newRevision.value.title || !newRevision.value.subject || 
          !newRevision.value.date || !newRevision.value.startTime) {
        alert('Por favor, preencha todos os campos obrigatórios.');
        return;
      }
      
      // Definir cor baseada na disciplina
      const subjectColors = {
        'Anatomia': '#2a9d8f',
        'Fisiologia': '#e9c46a',
        'Bioquímica': '#f4a261',
        'Farmacologia': '#e76f51',
        'Patologia': '#264653',
        'Microbiologia': '#023047',
        'Imunologia': '#219ebc',
        'Cardiologia': '#e63946',
        'Neurologia': '#457b9d',
        'Psiquiatria': '#7209b7',
        'Pediatria': '#ffb703',
        'Ginecologia': '#bc6c25',
        'Cirurgia': '#283618',
        'Clínica Médica': '#3a86ff',
        'Saúde Pública': '#8338ec'
      };
      
      const revisionData = {
        ...newRevision.value,
        color: subjectColors[newRevision.value.subject] || '#2a9d8f',
        id: Date.now(),
        createdAt: new Date().toISOString(),
        progress: 0,
        scheduleId: Date.now() + '-schedule',
        revisionDates: revisionSchedule.value
      };
      
      // Emitir evento para o componente pai
      emit('revision-scheduled', revisionData);
      
      // Adicionar também as revisões futuras como eventos
      revisionSchedule.value.forEach((date, index) => {
        const revisionEvent = {
          id: `${revisionData.id}-rev-${index + 1}`,
          title: `Revisão ${index + 1}: ${revisionData.title}`,
          date: date,
          startTime: revisionData.startTime,
          durationInMinutes: revisionData.durationInMinutes,
          subject: revisionData.subject,
          color: revisionData.color,
isRevision: true,
          isScheduledRevision: true,
          originalRevisionId: revisionData.id,
          revisionNumber: index + 1
        };
        
        emit('revision-scheduled', revisionEvent);
      });
      
      alert('Revisão agendada com sucesso!');
      resetForm();
    };
    
    // Resetar formulário
    const resetForm = () => {
      newRevision.value = {
title: '',
subject: '',
        date: props.selectedDate.toISOString().split('T')[0],
        startTime: '14:00',
durationInMinutes: 60,
        difficulty: 'Médio',
description: '',
        color: '#2a9d8f'
      };
    };

return {
      subjects,
      newRevision,
      todayFormatted,
      revisionSchedule,
formatDate,
      scheduleRevision,
      resetForm
    };
  }
}
</script>

<style scoped>
.revision-agent {
  background: white;
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.agent-header {
  padding: 1rem;
  background: rgba(42, 157, 143, 0.1);
  border-left: 4px solid var(--primary-color);
}

.agent-description {
margin: 0;
  color: var(--text-dark);
font-size: 0.95rem;
}

.agent-body {
  padding: 1.5rem;
}

.revision-form {
display: flex;
flex-direction: column;
gap: 1.5rem;
}

.form-row {
display: flex;
gap: 1rem;
  flex-wrap: wrap;
}

.form-group {
  flex: 1;
  min-width: 200px;
}

.form-group label {
display: block;
margin-bottom: 0.5rem;
font-weight: 500;
  color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #e1e1e1;
  border-radius: var(--border-radius-sm);
font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(42, 157, 143, 0.2);
}

.difficulty-selector {
display: flex;
gap: 0.5rem;
}

.difficulty-btn {
flex: 1;
  padding: 0.8rem;
  border: 1px solid #e1e1e1;
  background: white;
cursor: pointer;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  transition: all 0.2s ease;
}

.difficulty-btn.easy {
  color: #4caf50;
}

.difficulty-btn.medium {
  color: #ff9800;
}

.difficulty-btn.hard {
  color: #f44336;
}

.difficulty-btn.active {
  color: white;
  border-color: transparent;
}

.difficulty-btn.easy.active {
  background-color: #4caf50;
}

.difficulty-btn.medium.active {
  background-color: #ff9800;
}

.difficulty-btn.hard.active {
  background-color: #f44336;
}

.revision-schedule {
  background: #f9f9f9;
  padding: 1.5rem;
  border-radius: var(--border-radius-md);
margin-top: 1rem;
}

.revision-schedule h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
color: var(--primary-color);
font-size: 1.1rem;
}

.schedule-description {
margin-bottom: 1rem;
font-size: 0.9rem;
  color: #666;
}

.schedule-dates {
display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.schedule-date {
display: flex;
align-items: center;
  gap: 0.75rem;
  background: white;
  padding: 0.75rem;
  border-radius: var(--border-radius-sm);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  min-width: 220px;
}

.date-number {
width: 32px;
height: 32px;
display: flex;
align-items: center;
justify-content: center;
  background: var(--primary-color);
  color: white;
border-radius: 50%;
  font-weight: 600;
}

.date-info {
  flex: 1;
}

.date-value {
font-weight: 600;
  color: var(--text-dark);
}

.date-label {
  font-size: 0.8rem;
  color: #666;
}

.form-actions {
display: flex;
justify-content: flex-end;
gap: 1rem;
  margin-top: 1rem;
}

.btn-primary, .btn-secondary {
  padding: 0.8rem 1.5rem;
border: none;
  border-radius: var(--border-radius-sm);
font-weight: 600;
cursor: pointer;
display: flex;
align-items: center;
gap: 0.5rem;
  transition: all 0.2s ease;
}

.btn-primary {
background-color: var(--primary-color);
color: white;
}

.btn-secondary {
  background-color: #e1e1e1;
  color: #666;
}

.btn-primary:hover {
  background-color: #238079;
}

.btn-secondary:hover {
  background-color: #d1d1d1;
}

/* Suporte para tema escuro */
@media (prefers-color-scheme: dark) {
.revision-agent {
    background: var(--background-dark);
}
  
.agent-header {
    background: rgba(42, 157, 143, 0.2);
  }
  
  .agent-description {
    color: var(--text-light);
  }
  
  .form-group label {
    color: var(--text-light);
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    background: #2c3e50;
    border-color: #34495e;
    color: var(--text-light);
  }
  
  .difficulty-btn {
    background: #2c3e50;
    border-color: #34495e;
  }
  
  .revision-schedule {
    background: #1a252f;
  }
  
  .schedule-description {
    color: #bbb;
  }
  
  .schedule-date {
    background: #2c3e50;
  }
  
  .date-value {
    color: var(--text-light);
  }
  
  .date-label {
    color: #bbb;
  }
  
  .btn-secondary {
    background-color: #34495e;
    color: #ddd;
  }
}
</style>
