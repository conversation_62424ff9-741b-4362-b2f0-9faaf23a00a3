<template>
  <div class="revision-page">
    <!-- Background -->
    <div class="bg-gradient"></div>
    
    <!-- Main Container -->
    <div class="main-container">
      <!-- Left Panel -->
      <aside class="left-panel">
        <!-- Header -->
        <header class="panel-header">
          <div class="header-icon">
            <font-awesome-icon icon="brain" />
          </div>
          <div class="header-text">
            <h1>Agendador <span>Inteligente</span></h1>
            <p>Otimize seu aprendizado com IA</p>
          </div>
        </header>

        <!-- Stats -->
        <div class="stats-row">
          <div class="stat-card">
            <font-awesome-icon icon="calendar-check" />
            <strong>{{ totalRevisions }}</strong>
            <span>Revisões</span>
          </div>
          <div class="stat-card">
            <font-awesome-icon icon="chart-line" />
            <strong>{{ avgPerformance }}%</strong>
            <span>Performance</span>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <button @click="showAIAssistant = true" class="quick-btn">
            <font-awesome-icon icon="robot" />
            <span>Assistente IA</span>
          </button>
          <button @click="showStats = true" class="quick-btn">
            <font-awesome-icon icon="chart-pie" />
            <span>Estatísticas</span>
          </button>
          <button @click="showSettings = true" class="quick-btn">
            <font-awesome-icon icon="cog" />
            <span>Configurações</span>
          </button>
        </div>

        <!-- Form Card -->
        <div class="form-card">
          <h2>
            <font-awesome-icon icon="book-open" />
            Configurar Sessão
          </h2>

          <form @submit.prevent="adicionarRevisao" class="revision-form">
            <!-- Date -->
            <div class="form-field">
              <label>
                <font-awesome-icon icon="calendar-days" />
                Data do Estudo
              </label>
              <input 
                type="date" 
                v-model="dataEstudo" 
                @change="updateSelectedDate"
                required
              />
            </div>

            <!-- Difficulty -->
            <div class="form-field">
              <label>
                <font-awesome-icon icon="signal" />
                Nível de Dificuldade
              </label>
              <div class="difficulty-options">
                <button 
                  type="button"
                  :class="['diff-btn', { active: nivelDificuldade === 'Fácil' }]"
                  @click="nivelDificuldade = 'Fácil'"
                >
                  <font-awesome-icon icon="smile" />
                  Fácil
                </button>
                <button 
                  type="button"
                  :class="['diff-btn', { active: nivelDificuldade === 'Médio' }]"
                  @click="nivelDificuldade = 'Médio'"
                >
                  <font-awesome-icon icon="meh" />
                  Médio
                </button>
                <button 
                  type="button"
                  :class="['diff-btn', { active: nivelDificuldade === 'Difícil' }]"
                  @click="nivelDificuldade = 'Difícil'"
                >
                  <font-awesome-icon icon="frown" />
                  Difícil
                </button>
              </div>
            </div>

            <!-- Subject -->
            <div class="form-field">
              <label>
                <font-awesome-icon icon="graduation-cap" />
                Disciplina
              </label>
              <div class="custom-select" @click="showSubjectDropdown = !showSubjectDropdown">
                <div class="select-display">
                  <span class="color-dot" :style="{ backgroundColor: selectedSubjectColor }"></span>
                  <span>{{ selectedSubjectName || 'Selecione uma disciplina' }}</span>
                  <font-awesome-icon icon="chevron-down" />
                </div>
                <div v-if="showSubjectDropdown" class="select-dropdown">
                  <button
                    v-for="subject in subjects"
                    :key="subject.id"
                    type="button"
                    @click="selectSubject(subject)"
                    class="dropdown-item"
                  >
                    <span class="color-dot" :style="{ backgroundColor: subject.color }"></span>
                    {{ subject.name }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Topic -->
            <div class="form-field">
              <label>
                <font-awesome-icon icon="bookmark" />
                Tópico de Estudo
              </label>
              <input 
                type="text" 
                v-model="topico" 
                placeholder="Ex: Equações Diferenciais"
                required
              />
            </div>

            <!-- Submit -->
            <button type="submit" class="submit-btn">
              <font-awesome-icon icon="plus-circle" />
              Agendar Revisão
            </button>
          </form>
        </div>

        <!-- Calculator System -->
        <div class="calculator-card">
          <h2>
            <font-awesome-icon icon="calculator" />
            Calculadora de Revisões
          </h2>
          
          <div class="calc-content">
            <!-- Input Section -->
            <div class="input-section" style="padding: 20px 0;">
              <div class="inputs-row" style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                <div style="display: flex; flex-direction: column; align-items: center; width: 120px;">
                  <label for="correct" style="font-size: 12px; margin-bottom: 8px; color: rgba(255, 255, 255, 0.7);">Acertos</label>
                  <input 
                    type="number" 
                    id="correct"
                    v-model="calcCorrect" 
                    min="0"
                    :max="calcTotal || 100"
                    @input="handleInputChange"
                    placeholder="      0      "
                    style="width: 100%; height: 50px; font-size: 20px; padding-top: 13px; padding-bottom: 13px; padding-left: 15px; padding-right: 15px; background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 8px; color: #fff; text-align: center; -webkit-appearance: none; -moz-appearance: textfield; box-sizing: border-box;"
                  />
                </div>
                <div style="font-size: 16px; color: rgba(255, 255, 255, 0.5); margin-top: 30px;">de</div>
                <div style="display: flex; flex-direction: column; align-items: center; width: 120px;">
                  <label for="total" style="font-size: 12px; margin-bottom: 8px; color: rgba(255, 255, 255, 0.7);">Total</label>
                  <input 
                    type="number" 
                    id="total"
                    v-model="calcTotal" 
                    min="1"
                    @input="handleInputChange"
                    placeholder="      0      "
                    style="width: 100%; height: 50px; font-size: 20px; padding-top: 13px; padding-bottom: 13px; padding-left: 15px; padding-right: 15px; background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 8px; color: #fff; text-align: center; -webkit-appearance: none; -moz-appearance: textfield; box-sizing: border-box;"
                  />
                </div>
              </div>
            </div>
            
            <!-- Result Section -->
            <transition name="fade-up">
              <div v-if="autoResult" class="result-section">
                <div class="result-divider"></div>
                
                <!-- Stats Grid -->
                <div class="stats-grid">
                  <div class="stat-box">
                    <div class="stat-icon" :style="{ background: getPerformanceGradient(autoResult.percentage) }">
                      <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ autoResult.percentage }}%</div>
                      <div class="stat-label">Taxa de Acerto</div>
                    </div>
                  </div>
                  
                  <div class="stat-box">
                    <div class="stat-icon gradient-blue">
                      <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ autoResult.days }}</div>
                      <div class="stat-label">Dias até a Revisão</div>
                    </div>
                  </div>
                  
                  <div class="stat-box">
                    <div class="stat-icon gradient-pink">
                      <i class="fas fa-brain"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ getRetentionEstimate(autoResult.percentage) }}%</div>
                      <div class="stat-label">Retenção Estimada</div>
                    </div>
                  </div>
                </div>
                
                <!-- Performance Feedback -->
                <div class="performance-feedback">
                  <div class="feedback-badge" :class="getPerformanceClass(autoResult.percentage)">
                    <i :class="getPerformanceIcon(getPerformanceClass(autoResult.percentage))"></i>
                    <span>{{ getPerformanceLabel(autoResult.percentage) }}</span>
                  </div>
                  <p class="feedback-text">{{ getPerformanceFeedback(autoResult.percentage) }}</p>
                </div>
              </div>
            </transition>
          </div>
        </div>
        <!-- End of Calculator System -->

        <!-- Recent Revisions -->
        <div class="recent-card">
          <div class="card-header">
            <h3>
              <font-awesome-icon icon="clock" />
              Revisões Recentes
            </h3>
            <button class="link-btn">
              Ver todas
              <font-awesome-icon icon="arrow-right" />
            </button>
          </div>
          <div class="revision-list">
            <div 
              v-for="revision in recentRevisions" 
              :key="revision.id"
              class="revision-item"
            >
              <span class="rev-color" :style="{ backgroundColor: revision.color }"></span>
              <div class="rev-info">
                <strong>{{ revision.title }}</strong>
                <small>{{ revision.subject }} • {{ revision.date }}</small>
              </div>
              <font-awesome-icon :icon="getStatusIcon(revision.status)" class="rev-status" />
            </div>
          </div>
        </div>
      </aside>

      <!-- Right Panel - Calendar -->
      <main class="right-panel">
        <div class="calendar-card">
          <h2>Calendário de Revisões</h2>
          <SharedCalendar 
            :selected-date="selectedDate"
            @date-selected="handleDateSelected"
          />
        </div>
      </main>
    </div>
    <!-- End of main-container -->

    <!-- AI Assistant Modal -->
    <transition name="modal-fade">
      <div v-if="showAIAssistant" class="modal-overlay" @click.self="showAIAssistant = false">
        <div class="modal-container">
          <div class="modal-header">
            <h3>
              <font-awesome-icon icon="robot" />
              Assistente IA de Revisões
            </h3>
            <button @click="showAIAssistant = false" class="close-btn">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          <div class="modal-body">
            <div class="ai-chat">
              <div class="chat-messages">
                <div v-for="message in aiMessages" :key="message.id" :class="['message', message.type]">
                  <div class="message-content" v-html="formatMessage(message.text)"></div>
                </div>
              </div>
              <div class="chat-input">
                <input 
                  v-model="aiInput" 
                  @keyup.enter="sendAIMessage"
                  placeholder="Pergunte sobre técnicas de estudo, cronogramas..."
                  type="text"
                />
                <button @click="sendAIMessage" class="send-btn">
                  <font-awesome-icon icon="paper-plane" />
                </button>
              </div>
            </div>
            <div class="ai-suggestions">
              <h4>Sugestões Rápidas</h4>
              <div class="suggestion-pills">
                <button 
                  v-for="suggestion in aiSuggestions" 
                  :key="suggestion"
                  @click="useSuggestion(suggestion)"
                  class="suggestion-pill"
                >
                  {{ suggestion }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Statistics Modal -->
    <transition name="modal-fade">
      <div v-if="showStats" class="modal-overlay" @click.self="showStats = false">
        <div class="modal-container stats-modal">
          <div class="modal-header">
            <h3>
              <font-awesome-icon icon="chart-pie" />
              Estatísticas de Estudo
            </h3>
            <button @click="showStats = false" class="close-btn">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          <div class="modal-body">
            <div class="stats-grid">
              <div class="stat-box">
                <div class="stat-icon-lg">
                  <font-awesome-icon icon="book" />
                </div>
                <h4>156</h4>
                <p>Total de Revisões</p>
              </div>
              <div class="stat-box">
                <div class="stat-icon-lg">
                  <font-awesome-icon icon="clock" />
                </div>
                <h4>234h</h4>
                <p>Horas Estudadas</p>
              </div>
              <div class="stat-box">
                <div class="stat-icon-lg">
                  <font-awesome-icon icon="fire" />
                </div>
                <h4>45</h4>
                <p>Dias Consecutivos</p>
              </div>
              <div class="stat-box">
                <div class="stat-icon-lg">
                  <font-awesome-icon icon="trophy" />
                </div>
                <h4>89%</h4>
                <p>Taxa de Sucesso</p>
              </div>
            </div>
            
            <div class="chart-section">
              <h4>Progresso Semanal</h4>
              <div class="progress-bars">
                <div v-for="day in weekProgress" :key="day.name" class="day-progress">
                  <span class="day-name">{{ day.name }}</span>
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: day.progress + '%', background: day.color }"></div>
                  </div>
                  <span class="day-hours">{{ day.hours }}h</span>
                </div>
              </div>
            </div>

            <div class="subjects-performance">
              <h4>Performance por Matéria</h4>
              <div class="subject-list">
                <div v-for="subject in subjectStats" :key="subject.name" class="subject-stat">
                  <div class="subject-info">
                    <span class="subject-dot" :style="{ background: subject.color }"></span>
                    <span class="subject-name">{{ subject.name }}</span>
                  </div>
                  <div class="subject-score">
                    <span class="score-value">{{ subject.score }}%</span>
                    <font-awesome-icon 
                      :icon="subject.trend === 'up' ? 'arrow-up' : 'arrow-down'" 
                      :class="['trend-icon', subject.trend]"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Settings Modal -->
    <transition name="modal-fade">
      <div v-if="showSettings" class="modal-overlay" @click.self="showSettings = false">
        <div class="modal-container">
          <div class="modal-header">
            <h3>
              <font-awesome-icon icon="cog" />
              Configurações de Revisão
            </h3>
            <button @click="showSettings = false" class="close-btn">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          <div class="modal-body">
            <div class="settings-section">
              <h4>Notificações</h4>
              <div class="setting-item">
                <div class="setting-info">
                  <label>Lembrete de Revisão</label>
                  <small>Receba notificações antes das sessões</small>
                </div>
                <label class="toggle-switch">
                  <input type="checkbox" v-model="settings.notifications">
                  <span class="toggle-slider"></span>
                </label>
              </div>
              <div class="setting-item">
                <div class="setting-info">
                  <label>Relatório Semanal</label>
                  <small>Resumo do progresso por email</small>
                </div>
                <label class="toggle-switch">
                  <input type="checkbox" v-model="settings.weeklyReport">
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>

            <div class="settings-section">
              <h4>Preferências de Estudo</h4>
              <div class="setting-item">
                <label>Duração Padrão da Sessão</label>
                <select v-model="settings.defaultDuration" class="setting-select">
                  <option value="25">25 minutos (Pomodoro)</option>
                  <option value="50">50 minutos</option>
                  <option value="90">90 minutos</option>
                  <option value="120">2 horas</option>
                </select>
              </div>
              <div class="setting-item">
                <label>Intervalo entre Revisões</label>
                <select v-model="settings.reviewInterval" class="setting-select">
                  <option value="1">1 dia</option>
                  <option value="3">3 dias</option>
                  <option value="7">7 dias</option>
                  <option value="14">14 dias</option>
                </select>
              </div>
            </div>

            <div class="settings-section">
              <h4>Algoritmo de Repetição</h4>
              <div class="algorithm-options">
                <label class="algorithm-option" :class="{ active: settings.algorithm === 'sm2' }">
                  <input type="radio" v-model="settings.algorithm" value="sm2">
                  <div class="option-content">
                    <strong>SM-2</strong>
                    <small>SuperMemo 2 - Clássico e eficiente</small>
                  </div>
                </label>
                <label class="algorithm-option" :class="{ active: settings.algorithm === 'anki' }">
                  <input type="radio" v-model="settings.algorithm" value="anki">
                  <div class="option-content">
                    <strong>Anki</strong>
                    <small>Baseado em intervalos graduais</small>
                  </div>
                </label>
                <label class="algorithm-option" :class="{ active: settings.algorithm === 'leitner' }">
                  <input type="radio" v-model="settings.algorithm" value="leitner">
                  <div class="option-content">
                    <strong>Leitner</strong>
                    <small>Sistema de caixas simples</small>
                  </div>
                </label>
              </div>
            </div>

            <button @click="saveSettings" class="save-settings-btn">
              <font-awesome-icon icon="save" />
              Salvar Configurações
            </button>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import SharedCalendar from './SharedCalendar.vue';
import { ref, computed, onMounted } from 'vue';
import AIAssistantService from '@/services/AIAssistantService';
import { useStore } from 'vuex';

export default {
  components: {
    SharedCalendar
  },
  setup() {
    const store = useStore();
    
    // State
    const dataEstudo = ref('');
    const nivelDificuldade = ref('Médio');
    const topico = ref('');
    const showSubjectDropdown = ref(false);
    const selectedSubject = ref(null);
    const selectedDate = ref(new Date());
    const showAISuggestions = ref(false);
    
    // Modal states
    const showAIAssistant = ref(false);
    const showStats = ref(false);
    const showSettings = ref(false);
    
    // AI Assistant state
    const aiInput = ref('');
    const aiMessages = ref([
      { id: 1, type: 'ai', text: '👋 Olá! Sou seu assistente inteligente de estudos.\n\nPosso ajudar você com:\n• Técnicas de memorização e estudo\n• Criação de cronogramas personalizados\n• Métodos como Pomodoro e Cornell\n• Dicas para vencer a procrastinação\n• Estratégias para lidar com ansiedade\n\nO que você gostaria de saber?' }
    ]);
    
    // Settings state
    const settings = ref({
      notifications: true,
      weeklyReport: false,
      defaultDuration: '50',
      reviewInterval: '3',
      algorithm: 'sm2'
    });
    
    // Calculator state
    const calcCorrect = ref('');
    const calcTotal = ref('');
    const calcResult = ref(null);
    const showHistory = ref(false);
    const calcHistory = ref([]);
    const analysisResult = ref(null);
    const historyFilter = ref('all');
    const historyView = ref('list');
    const autoResult = ref(null);
    const focusField = ref(null);
    
    // Advanced calculator state
    const advancedCalc = ref({
      correct: '',
      total: '',
      subject: '',
      difficulty: 3,
      studyTime: '',
      previousRevisions: 0
    });
    const advancedResult = ref(null);
    
    // AI Analysis state
    const aiAnalysis = ref({
      correct: '',
      total: '',
      subject: '',
      cardType: '',
      difficulty: 3,
      confidence: 3,
      studyTime: '',
      previousAttempts: 0,
      isFirstTime: false,
      hadBreak: false,
      examPressure: false
    });
    
    // Batch calculator state
    const batchItems = ref([
      { subject: '', correct: '', total: '' }
    ]);
    const batchResults = ref([]);
    
    // Quick presets
    const quickPresets = ref([
      { label: '50% (15/30)', correct: 15, total: 30 },
      { label: '70% (21/30)', correct: 21, total: 30 },
      { label: '80% (24/30)', correct: 24, total: 30 },
      { label: '90% (27/30)', correct: 27, total: 30 },
      { label: '100% (30/30)', correct: 30, total: 30 }
    ]);
    
    // Calculator configuration
    const calculatorConfig = {
      name: 'Calculadora de Revisões',
      description: 'Cálculo simples e direto do intervalo de revisão',
      icon: 'fas fa-calculator'
    };
    
    // Medical subjects
    const medicalSubjects = [
      { id: 'anatomy', name: 'Anatomia' },
      { id: 'physiology', name: 'Fisiologia' },
      { id: 'pathology', name: 'Patologia' },
      { id: 'pharmacology', name: 'Farmacologia' },
      { id: 'microbiology', name: 'Microbiologia' },
      { id: 'biochemistry', name: 'Bioquímica' },
      { id: 'histology', name: 'Histologia' },
      { id: 'cardiology', name: 'Cardiologia' },
      { id: 'neurology', name: 'Neurologia' },
      { id: 'surgery', name: 'Cirurgia' }
    ];
    
    // Pattern analysis data
    const selectedPeriod = ref('week');
    const timePeriods = [
      { value: 'week', label: 'Última Semana' },
      { value: 'month', label: 'Último Mês' },
      { value: '3months', label: '3 Meses' },
      { value: 'year', label: 'Ano' }
    ];
    
    // Study pattern mock data
    const currentStreak = ref(12);
    const retentionRate = ref(87);
    const averageAccuracy = ref(82);
    const bestSubject = ref('Anatomia');
    const bestStudyTime = ref('10h - 12h');
    const topSubjects = ref([
      { name: 'Anatomia', percentage: 35, color: '#667eea' },
      { name: 'Fisiologia', percentage: 25, color: '#764ba2' },
      { name: 'Farmacologia', percentage: 20, color: '#f093fb' },
      { name: 'Patologia', percentage: 12, color: '#4facfe' },
      { name: 'Outros', percentage: 8, color: '#fa709a' }
    ]);
    
    // Heatmap data
    const studyHeatmap = ref(generateHeatmapData());
    const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    
    // Mock data
    const totalRevisions = ref(24);
    const avgPerformance = ref(87);
    
    const subjects = ref([
      { id: 1, name: 'Matemática', color: '#667eea' },
      { id: 2, name: 'Física', color: '#f093fb' },
      { id: 3, name: 'Química', color: '#4facfe' },
      { id: 4, name: 'Biologia', color: '#fa709a' },
      { id: 5, name: 'História', color: '#fee140' },
      { id: 6, name: 'Geografia', color: '#30cfd0' }
    ]);
    
    const recentRevisions = ref([
      { id: 1, title: 'Cálculo Integral', subject: 'Matemática', date: 'Hoje, 14:00', color: '#667eea', status: 'completed' },
      { id: 2, title: 'Termodinâmica', subject: 'Física', date: 'Hoje, 16:00', color: '#f093fb', status: 'pending' },
      { id: 3, title: 'Reações Orgânicas', subject: 'Química', date: 'Amanhã, 10:00', color: '#4facfe', status: 'pending' }
    ]);
    
    const aiSuggestions = ref([
      'Acertei 15 de 30 questões',
      'Como criar um cronograma eficiente?',
      'Acertei 85% das questões',
      'Métodos de estudo comprovados'
    ]);
    
    // Stats data
    const weekProgress = ref([
      { name: 'Seg', progress: 80, hours: 3.2, color: '#667eea' },
      { name: 'Ter', progress: 65, hours: 2.6, color: '#f093fb' },
      { name: 'Qua', progress: 90, hours: 3.6, color: '#4facfe' },
      { name: 'Qui', progress: 45, hours: 1.8, color: '#fa709a' },
      { name: 'Sex', progress: 70, hours: 2.8, color: '#fee140' },
      { name: 'Sáb', progress: 95, hours: 3.8, color: '#30cfd0' },
      { name: 'Dom', progress: 50, hours: 2.0, color: '#764ba2' }
    ]);
    
    const subjectStats = ref([
      { name: 'Matemática', score: 92, trend: 'up', color: '#667eea' },
      { name: 'Física', score: 78, trend: 'up', color: '#f093fb' },
      { name: 'Química', score: 85, trend: 'down', color: '#4facfe' },
      { name: 'Biologia', score: 88, trend: 'up', color: '#fa709a' },
      { name: 'História', score: 76, trend: 'down', color: '#fee140' }
    ]);
    
    // Computed
    const selectedSubjectName = computed(() => selectedSubject.value?.name);
    const selectedSubjectColor = computed(() => selectedSubject.value?.color || '#667eea');
    
    // Methods
    const selectSubject = (subject) => {
      selectedSubject.value = subject;
      showSubjectDropdown.value = false;
    };
    
    const updateSelectedDate = () => {
      if (dataEstudo.value) {
        selectedDate.value = new Date(dataEstudo.value + 'T00:00:00');
      }
    };
    
    const handleDateSelected = (date) => {
      selectedDate.value = date;
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      dataEstudo.value = `${year}-${month}-${day}`;
    };
    
    const adicionarRevisao = async () => {
      if (!selectedSubject.value) {
        alert('Por favor, selecione uma disciplina');
        return;
      }
      
      try {
        // Criar um novo flashcard/revisão
        const newRevision = {
          id: Date.now().toString(),
          front: topico.value || 'Revisão',
          back: `Revisão de ${selectedSubject.value.name}`,
          tags: [selectedSubject.value.name],
          difficulty: nivelDificuldade.value === 'Fácil' ? 5 : 
                     nivelDificuldade.value === 'Médio' ? 3 : 1,
          lastReviewed: dataEstudo.value,
          nextReview: null,
          history: [],
          stats: { attempts: 0, correct: 0 }
        };
        
        // Calcular próxima revisão usando SpacedRepetitionService
        const schedulingResult = SpacedRepetitionService.calculateNextReview(
          newRevision.difficulty,
          0, // consecutiveCorrect
          [], // cardHistory
          1.0 // efficiency
        );
        
        newRevision.nextReview = schedulingResult.nextReview;
        
        // Verificar se o deck existe, senão criar
        let deck = store.getters['flashcards/allFlashcards']
          .find(card => card.deckName === selectedSubject.value.name);
        
        let deckId;
        if (!deck) {
          // Criar novo deck
          const newDeck = await store.dispatch('flashcards/createDeck', {
            name: selectedSubject.value.name,
            color: selectedSubject.value.color,
            description: `Deck de revisões para ${selectedSubject.value.name}`
          });
          deckId = newDeck.id;
        } else {
          deckId = deck.deckId;
        }
        
        // Adicionar flashcard ao deck
        await store.dispatch('flashcards/addFlashcard', {
          deckId: deckId,
          ...newRevision
        });
        
        // Atualizar as revisões recentes
        recentRevisions.value.unshift({
          id: newRevision.id,
          title: topico.value || 'Revisão',
          subject: selectedSubject.value.name,
          date: new Date(dataEstudo.value).toLocaleDateString('pt-BR'),
          color: selectedSubject.value.color,
          status: 'pending'
        });
        
        // Limitar a 5 revisões recentes
        if (recentRevisions.value.length > 5) {
          recentRevisions.value = recentRevisions.value.slice(0, 5);
        }
        
        // Reset form
        topico.value = '';
        nivelDificuldade.value = 'Médio';
        showAISuggestions.value = false;
        
        // Feedback de sucesso
        alert('Revisão agendada com sucesso!');
      } catch (error) {
        console.error('Erro ao adicionar revisão:', error);
        alert('Erro ao agendar revisão. Tente novamente.');
      }
    };
    
    const getStatusIcon = (status) => {
      return status === 'completed' ? 'check-circle' : 'clock';
    };
    
    // AI Assistant methods
    const sendAIMessage = async () => {
      if (!aiInput.value.trim()) return;
      
      const userMessage = aiInput.value;
      console.log('Sending message:', userMessage);
      
      // Add user message
      aiMessages.value.push({
        id: Date.now(),
        type: 'user',
        text: userMessage
      });
      
      // Clear input immediately
      aiInput.value = '';
      
      // Check if it's a revision calculation question
      const isRevisionQuestion = checkIfRevisionCalculation(userMessage);
      
      if (isRevisionQuestion) {
        // Handle revision calculation locally
        const calculatedResponse = calculateRevisionSchedule(userMessage);
        aiMessages.value.push({
          id: Date.now() + 1,
          type: 'ai',
          text: calculatedResponse
        });
        return;
      }
      
      // Show typing indicator for other questions
      const typingIndicatorId = Date.now() + 1;
      aiMessages.value.push({
        id: typingIndicatorId,
        type: 'ai',
        text: '⏳ Pensando...'
      });
      
      try {
        // Get context from recent messages
        const context = aiMessages.value
          .slice(-5) // Last 5 messages
          .filter(msg => msg.type === 'user')
          .map(msg => msg.text)
          .join('\n');
        
        // Try to get real AI response first
        let response;
        try {
          console.log('Calling AI service with message:', userMessage);
          response = await AIAssistantService.getAIResponse(userMessage, context);
          console.log('AI service response:', response);
        } catch (error) {
          console.error('Error calling AI service:', error);
          console.log('Error details:', error.response?.data || error.message);
          console.log('Falling back to demo mode');
          // Fallback to demo response if API fails
          response = await AIAssistantService.getAIResponseDemo(userMessage);
        }
        
        // Remove typing indicator
        const typingIndex = aiMessages.value.findIndex(msg => msg.id === typingIndicatorId);
        if (typingIndex > -1) {
          aiMessages.value.splice(typingIndex, 1);
        }
        
        // Add AI response
        aiMessages.value.push({
          id: Date.now() + 2,
          type: 'ai',
          text: response
        });
        
        console.log('AI response received:', response);
        
      } catch (error) {
        console.error('Error getting AI response:', error);
        
        // Remove typing indicator
        const typingIndex = aiMessages.value.findIndex(msg => msg.id === typingIndicatorId);
        if (typingIndex > -1) {
          aiMessages.value.splice(typingIndex, 1);
        }
        
        // Show error message
        aiMessages.value.push({
          id: Date.now() + 3,
          type: 'ai',
          text: '❌ Desculpe, ocorreu um erro ao processar sua mensagem. Por favor, tente novamente.'
        });
      }
    };
    
    const useSuggestion = (suggestion) => {
      aiInput.value = suggestion;
      sendAIMessage();
    };
    
    // Check if message is about revision calculation
    const checkIfRevisionCalculation = (message) => {
      const keywords = ['revisão', 'revisao', 'acertei', 'acertos', 'errei', 'erros', 
                       'questões', 'questoes', 'perguntas', '%', 'porcentagem', 
                       'próxima', 'proxima', 'calcule', 'calcular', 'agendar'];
      const lowerMessage = message.toLowerCase();
      
      // Check for percentage or fraction patterns
      const hasPercentage = /\d+\s*%/.test(message);
      const hasFraction = /\d+\s*(de|\/)\s*\d+/.test(message);
      
      return keywords.some(keyword => lowerMessage.includes(keyword)) && 
             (hasPercentage || hasFraction);
    };
    
    // Calculate revision schedule based on performance
    const calculateRevisionSchedule = (message) => {
      // const lowerMessage = message.toLowerCase(); // Removido - não utilizado
      
      // Extract percentage or calculate from fraction
      let percentage = null;
      
      // Try to extract direct percentage
      const percentMatch = message.match(/(\d+)\s*%/);
      if (percentMatch) {
        percentage = parseInt(percentMatch[1]);
      } else {
        // Try to extract fraction (e.g., "15 de 30" or "15/30")
        const fractionMatch = message.match(/(\d+)\s*(de|\/)\s*(\d+)/);
        if (fractionMatch) {
          const correct = parseInt(fractionMatch[1]);
          const total = parseInt(fractionMatch[3]);
          percentage = Math.round((correct / total) * 100);
        }
      }
      
      if (percentage === null) {
        return '📊 Por favor, informe a quantidade de acertos e o total de questões, ou a porcentagem de acertos.\n\nExemplos:\n• "Acertei 15 de 30 questões"\n• "Acertei 75% das questões"\n• "Errei 10 de 40 perguntas"';
      }
      
      // Calculate next review interval based on percentage
      let daysUntilNextReview;
      let recommendation;
      let strategy;
      
      if (percentage <= 50) {
        daysUntilNextReview = 2;
        recommendation = 'Como você acertou 50% ou menos, é essencial revisar o conteúdo em breve para consolidar o aprendizado.';
        strategy = '🎯 **Estratégia Recomendada:**\n• Revise os conceitos fundamentais\n• Refaça os exercícios que errou\n• Busque material complementar\n• Faça mapas mentais dos tópicos';
      } else if (percentage <= 70) {
        daysUntilNextReview = 5;
        recommendation = 'Com um desempenho médio, você precisa reforçar alguns conceitos antes que sejam esquecidos.';
        strategy = '📚 **Estratégia Recomendada:**\n• Foque nas questões que errou\n• Revise as anotações principais\n• Pratique mais exercícios similares\n• Explique o conteúdo para alguém';
      } else if (percentage <= 85) {
        daysUntilNextReview = 7;
        recommendation = 'Bom desempenho! Uma revisão semanal será suficiente para manter o conhecimento.';
        strategy = '✅ **Estratégia Recomendada:**\n• Revise rapidamente os pontos principais\n• Foque apenas nos erros cometidos\n• Faça questões de nível mais avançado\n• Conecte com outros tópicos';
      } else if (percentage <= 95) {
        daysUntilNextReview = 14;
        recommendation = 'Excelente desempenho! O conteúdo está bem consolidado.';
        strategy = '🌟 **Estratégia Recomendada:**\n• Revisão rápida dos conceitos-chave\n• Resolva questões desafiadoras\n• Ajude colegas com dúvidas\n• Aprofunde em tópicos avançados';
      } else {
        daysUntilNextReview = 30;
        recommendation = 'Parabéns! Você domina este conteúdo. Apenas uma revisão mensal para manutenção.';
        strategy = '🏆 **Estratégia Recomendada:**\n• Revisão superficial mensal\n• Foque em aplicações práticas\n• Integre com novos conhecimentos\n• Considere avançar para o próximo nível';
      }
      
      // Calculate next review date
      const today = new Date();
      const nextReviewDate = new Date(today);
      nextReviewDate.setDate(today.getDate() + daysUntilNextReview);
      
      const formattedDate = nextReviewDate.toLocaleDateString('pt-BR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      // Build response
      const response = `📈 **Análise do seu Desempenho**\n\n` +
        `✏️ **Resultado:** ${percentage}% de acertos\n` +
        `📅 **Próxima Revisão:** ${daysUntilNextReview} dias (${formattedDate})\n\n` +
        `💡 **Análise:** ${recommendation}\n\n` +
        `${strategy}\n\n` +
        `⏰ **Lembrete:** Marque em seu calendário para revisar este conteúdo em ${daysUntilNextReview} dias!\n\n` +
        `💪 Continue assim e você verá resultados incríveis!`;
      
      return response;
    };
    
    // Format message for display
    const formatMessage = (text) => {
      // Convert markdown-like syntax to HTML
      return text
        .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
        .replace(/\n/g, '<br>')
        .replace(/• /g, '&bull; ');
    };
    
    // Settings methods
    const saveSettings = () => {
      localStorage.setItem('revisionSettings', JSON.stringify(settings.value));
      alert('Configurações salvas com sucesso!');
      showSettings.value = false;
    };
    
    // Load settings on mount
    const loadSettings = () => {
      const saved = localStorage.getItem('revisionSettings');
      if (saved) {
        settings.value = JSON.parse(saved);
      }
    };
    
    // Calculator methods
    
    // Generate revision schedule based on spaced repetition
    const generateRevisionSchedule = (firstInterval, percentage) => {
      const schedule = [];
      const baseDate = new Date();
      
      // First revision
      const firstDate = new Date(baseDate);
      firstDate.setDate(firstDate.getDate() + firstInterval);
      schedule.push({
        label: '1ª Revisão',
        date: firstDate.toLocaleDateString('pt-BR', { day: 'numeric', month: 'short' })
      });
      
      // Calculate subsequent intervals based on performance
      let multiplier = percentage > 80 ? 2.5 : 2;
      let currentInterval = firstInterval;
      
      // Generate 4 more revisions
      for (let i = 2; i <= 5; i++) {
        currentInterval = Math.round(currentInterval * multiplier);
        const revisionDate = new Date(baseDate);
        revisionDate.setDate(revisionDate.getDate() + currentInterval);
        
        schedule.push({
          label: `${i}ª Revisão`,
          date: revisionDate.toLocaleDateString('pt-BR', { day: 'numeric', month: 'short' })
        });
        
        // Decrease multiplier slightly for each iteration
        multiplier = Math.max(1.5, multiplier - 0.1);
      }
      
      return schedule;
    };
    
    // Advanced calculation with multiple factors
    const calculateAdvanced = () => {
      const correct = parseInt(advancedCalc.value.correct);
      const total = parseInt(advancedCalc.value.total);
      
      if (isNaN(correct) || isNaN(total) || total <= 0 || correct < 0 || correct > total) {
        advancedResult.value = null;
        return;
      }
      
      const percentage = Math.round((correct / total) * 100);
      const difficulty = Math.max(1, Math.round((percentage / 100) * 5));
      
      // Criar um card simulado para usar com o SpacedRepetitionService
      const simulatedCard = {
        difficulty: advancedCalc.value.difficulty,
        tags: advancedCalc.value.subject ? [advancedCalc.value.subject] : [],
        history: [],
        memoryStrength: 0.5,
        consecutiveCorrect: advancedCalc.value.previousRevisions || 0
      };
      
      // Calcular tempo de resposta normalizado
      const responseTime = advancedCalc.value.studyTime 
        ? Math.min(20000, advancedCalc.value.studyTime * 1000) 
        : 10000;
      
      // Usar o método integrado do SpacedRepetitionService
      const integrationResult = SpacedRepetitionService.integrateWithFlashcardReview(
        simulatedCard,
        difficulty,
        responseTime,
        {}, // sessionStats vazio
        0.5, // memoryStrength inicial
        {} // userProfile vazio
      );
      
      // Extrair dados do resultado
      const { neuralNetworkScheduling, memoryModel, learningAnalytics } = integrationResult;
      
      // Calcular eficiência baseada no modelo neural
      const efficiency = Math.round(
        neuralNetworkScheduling.retentionPrediction * 100 * 
        (1 - neuralNetworkScheduling.semanticInterference)
      );
      
      // Calcular score de retenção
      const retentionScore = Math.round(memoryModel.newStrength * 10);
      
      // Gerar recomendações personalizadas baseadas nos insights
      const recommendations = generateAdvancedRecommendations(
        percentage, 
        advancedCalc.value,
        learningAnalytics.insights
      );
      
      // Gerar plano de estudos otimizado
      const studyPlan = generateStudyPlan(
        percentage, 
        advancedCalc.value,
        neuralNetworkScheduling.optimalInterval
      );
      
      advancedResult.value = {
        percentage,
        adjustedDays: neuralNetworkScheduling.optimalInterval,
        efficiency,
        retentionScore: Math.min(10, Math.max(1, retentionScore)),
        recommendations,
        studyPlan,
        // Dados adicionais do modelo neural
        retentionPrediction: Math.round(neuralNetworkScheduling.retentionPrediction * 100),
        memoryStrength: Math.round(memoryModel.newStrength * 100),
        optimalHour: Math.round(neuralNetworkScheduling.optimalHours % 24)
      };
    };
    
    // Get base interval based on percentage
    const getBaseInterval = (percentage) => {
      if (percentage <= 50) return 2;
      if (percentage <= 70) return 5;
      if (percentage <= 85) return 7;
      if (percentage <= 95) return 14;
      return 30;
    };
    
    // Generate advanced recommendations
    const generateAdvancedRecommendations = (percentage, calcData, aiInsights = []) => {
      const recommendations = [];
      
      // Adicionar insights da IA primeiro (se disponíveis)
      if (aiInsights && aiInsights.length > 0) {
        aiInsights.forEach(insight => {
          if (insight.message) {
            recommendations.push(insight.message);
          }
        });
      }
      
      // Recomendações baseadas em desempenho
      if (percentage < 70) {
        recommendations.push('Revise os conceitos fundamentais antes de prosseguir');
        recommendations.push('Considere fazer resumos ou mapas mentais do conteúdo');
        if (calcData.studyTime < 60) {
          recommendations.push('Aumente o tempo de estudo para melhor absorção');
        }
      }
      
      if (calcData.difficulty >= 4) {
        recommendations.push('Divida o conteúdo em partes menores para facilitar o aprendizado');
        recommendations.push('Pratique com exercícios de dificuldade progressiva');
      }
      
      if (calcData.previousRevisions === 0) {
        recommendations.push('Esta é sua primeira revisão - foque nos pontos principais');
      } else if (calcData.previousRevisions > 3) {
        recommendations.push('Considere avançar para conteúdos mais complexos');
      }
      
      if (percentage >= 85) {
        recommendations.push('Excelente desempenho! Mantenha o ritmo de estudos');
        recommendations.push('Experimente ensinar o conteúdo para consolidar ainda mais');
      }
      
      // Limitar a 5 recomendações mais relevantes
      return recommendations.slice(0, 5);
    };
    
    // Generate study plan
    const generateStudyPlan = (percentage, calcData, optimalInterval = null) => {
      const plan = [];
      
      // Adicionar informação sobre o intervalo otimizado
      if (optimalInterval) {
        plan.push({
          icon: 'calendar-check',
          title: 'Próxima Revisão Otimizada',
          description: `Agende para daqui a ${optimalInterval} dias com base no algoritmo neural`
        });
      }
      
      if (percentage < 70) {
        plan.push({
          icon: 'book',
          title: 'Revisão Intensiva',
          description: 'Dedique 30-45 minutos para revisar os conceitos básicos'
        });
        plan.push({
          icon: 'pencil-alt',
          title: 'Prática Guiada',
          description: 'Resolva 10-15 questões com consulta ao material'
        });
      }
      
      plan.push({
        icon: 'brain',
        title: 'Técnica de Memorização',
        description: percentage > 80 ? 'Use flashcards para manter a memória ativa' : 'Aplique a técnica Feynman para melhor compreensão'
      });
      
      if (calcData.difficulty >= 4) {
        plan.push({
          icon: 'users',
          title: 'Estudo em Grupo',
          description: 'Discuta os tópicos difíceis com colegas'
        });
      }
      
      plan.push({
        icon: 'clock',
        title: 'Próxima Sessão',
        description: `Agende ${Math.round(calcData.studyTime * 0.8 || 45)} minutos para a próxima revisão`
      });
      
      return plan;
    };
    
    // Batch calculation
    const calculateBatch = () => {
      batchResults.value = batchItems.value
        .filter(item => item.subject && item.correct && item.total)
        .map(item => {
          const percentage = Math.round((item.correct / item.total) * 100);
          const days = getBaseInterval(percentage);
          
          return {
            subject: item.subject,
            correct: item.correct,
            total: item.total,
            percentage,
            days
          };
        });
    };
    
    // Helper methods
    const applyPreset = (preset) => {
      calcCorrect.value = preset.correct;
      calcTotal.value = preset.total;
      calculateRevision();
    };
    
    const resetCalculator = () => {
      calcCorrect.value = '';
      calcTotal.value = '';
      calcResult.value = null;
      advancedCalc.value = {
        correct: '',
        total: '',
        subject: '',
        difficulty: 3,
        studyTime: '',
        previousRevisions: 0
      };
      advancedResult.value = null;
      batchItems.value = [{ subject: '', correct: '', total: '' }];
      batchResults.value = [];
    };
    
    const addBatchItem = () => {
      batchItems.value.push({ subject: '', correct: '', total: '' });
    };
    
    const removeBatchItem = (index) => {
      if (batchItems.value.length > 1) {
        batchItems.value.splice(index, 1);
      }
    };
    
    const saveToHistory = () => {
      if (!calcResult.value) return;
      
      const historyItem = {
        date: new Date().toLocaleString('pt-BR'),
        correct: calcCorrect.value,
        total: calcTotal.value,
        percentage: calcResult.value.percentage,
        subject: advancedCalc.value.subject || null
      };
      
      calcHistory.value.unshift(historyItem);
      
      // Keep only last 20 items
      if (calcHistory.value.length > 20) {
        calcHistory.value = calcHistory.value.slice(0, 20);
      }
      
      // Save to localStorage
      localStorage.setItem('calcHistory', JSON.stringify(calcHistory.value));
      
      // Show success message
      alert('Cálculo salvo no histórico!');
    };
    
    const loadFromHistory = (item) => {
      calcCorrect.value = item.correct;
      calcTotal.value = item.total;
      calculateRevision();
      showHistory.value = false;
    };
    
    const clearHistory = () => {
      if (confirm('Deseja limpar todo o histórico?')) {
        calcHistory.value = [];
        localStorage.removeItem('calcHistory');
      }
    };
    
    const exportBatchResults = () => {
      if (batchResults.value.length === 0) return;
      
      let csv = 'Matéria,Acertos,Total,Porcentagem,Dias para Revisão\n';
      
      batchResults.value.forEach(result => {
        csv += `${result.subject},${result.correct},${result.total},${result.percentage}%,${result.days}\n`;
      });
      
      // Create and download CSV file
      const blob = new Blob([csv], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `revisoes_${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    };
    
    // Load history from localStorage
    const loadHistoryFromStorage = () => {
      const saved = localStorage.getItem('calcHistory');
      if (saved) {
        try {
          calcHistory.value = JSON.parse(saved);
        } catch (e) {
          console.error('Error loading history:', e);
        }
      }
    };
    
    const getPerformanceClass = (percentage) => {
      if (percentage <= 50) return 'poor';
      if (percentage <= 70) return 'average';
      if (percentage <= 85) return 'good';
      if (percentage <= 95) return 'excellent';
      return 'perfect';
    };
    
    const getPerformanceLabel = (percentage) => {
      if (percentage <= 50) return 'Precisa Melhorar';
      if (percentage <= 70) return 'Regular';
      if (percentage <= 85) return 'Bom';
      if (percentage <= 95) return 'Muito Bom';
      return 'Excelente';
    };
    
    // Helper function to generate heatmap data
    function generateHeatmapData() {
      const data = {};
      for (let hour = 0; hour < 24; hour++) {
        data[hour] = {};
        for (let day = 0; day < 7; day++) {
          // Random intensity between 0 and 10
          data[hour][day] = Math.floor(Math.random() * 11);
        }
      }
      return data;
    }
    
    // Computed Properties
    const livePercentage = computed(() => {
      const correct = parseInt(calcCorrect.value) || 0;
      const total = parseInt(calcTotal.value) || 0;
      return total > 0 ? Math.round((correct / total) * 100) : 0;
    });
    
    const canCalculate = computed(() => {
      return validateInputs();
    });
    
    const canPerformAIAnalysis = computed(() => {
      return calcResult.value !== null;
    });
    
    const canAnalyzeBatch = computed(() => {
      return batchItems.value.some(item => 
        item.subject && item.correct && item.total && 
        parseInt(item.total) > 0
      );
    });
    
    const filteredHistory = computed(() => {
      if (!historyFilter.value || historyFilter.value === 'all') return calcHistory.value;
      
      return calcHistory.value.filter(item => {
        const searchLower = historyFilter.value.toLowerCase();
        return (item.subject && item.subject.toLowerCase().includes(searchLower)) ||
               (item.date && item.date.includes(historyFilter.value));
      });
    });
    
    const streakProgress = computed(() => {
      const currentCorrect = parseInt(calcCorrect.value) || 0;
      const total = parseInt(calcTotal.value) || 0;
      if (total === 0) return 0;
      
      const percentage = (currentCorrect / total) * 100;
      return Math.min(100, percentage);
    });
    
    // Additional calculator methods
    const validateInputs = () => {
      const correct = parseInt(calcCorrect.value);
      const total = parseInt(calcTotal.value);
      return !isNaN(correct) && !isNaN(total) && total > 0 && correct >= 0 && correct <= total;
    };
    
    const handleInputChange = () => {
      if (validateInputs()) {
        const percentage = Math.round((calcCorrect.value / calcTotal.value) * 100);
        const days = getBaseInterval(percentage);
        
        autoResult.value = {
          percentage,
          days
        };
        
        // Salvar automaticamente no resultado da análise
        analysisResult.value = {
          percentage,
          nextReviewDays: days,
          performanceLevel: getPerformanceClass(percentage),
          title: getPerformanceLabel(percentage),
          subtitle: `Próxima revisão em ${days} dias`
        };
      } else {
        autoResult.value = null;
      }
    };
    
    const calculateRevision = () => {
      if (!validateInputs()) return;
      
      const percentage = Math.round((calcCorrect.value / calcTotal.value) * 100);
      const baseInterval = getBaseInterval(percentage);
      
      // Cálculo simples do intervalo de revisão
      calcResult.value = {
        percentage,
        days: baseInterval,
        nextDate: new Date(Date.now() + baseInterval * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
        performanceLevel: getPerformanceClass(percentage),
        recommendation: getPerformanceLabel(percentage)
      };
      
      // Atualizar resultado da análise para exibição
      analysisResult.value = {
        percentage,
        nextReviewDays: baseInterval,
        performanceLevel: getPerformanceClass(percentage),
        title: getPerformanceLabel(percentage),
        subtitle: `Próxima revisão em ${baseInterval} dias`
      };
      
      // Salvar no histórico
      saveToHistory();
    };
    
    const performDeepAnalysis = () => {
      // Executar análise avançada
      calculateAdvanced();
    };
    
    const analyzeBatch = () => {
      // Já implementado como calculateBatch
      calculateBatch();
    };
    
    // Lifecycle
    onMounted(async () => {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      dataEstudo.value = `${year}-${month}-${day}`;
      
      // Carregar configurações com tratamento de erro
      try {
        loadSettings();
      } catch (error) {
        console.warn('Erro ao carregar configurações:', error);
      }
      
      try {
        loadHistoryFromStorage();
      } catch (error) {
        console.warn('Erro ao carregar histórico:', error);
      }
      
      // Carregar flashcards do store com verificações
      try {
        if (store && typeof store.dispatch === 'function') {
          await store.dispatch('flashcards/loadFlashcards');
          
          // Verificar se o getter existe antes de usar
          if (store.getters && store.getters['flashcards/allFlashcards']) {
            const allFlashcards = store.getters['flashcards/allFlashcards'] || [];
            
            // Verificar se SpacedRepetitionService existe e tem o método
            if (SpacedRepetitionService && typeof SpacedRepetitionService.getCardsForToday === 'function') {
              const todaysCards = SpacedRepetitionService.getCardsForToday(allFlashcards);
              totalRevisions.value = todaysCards ? todaysCards.length : 0;
            }
            
            // Calcular performance média com verificações
            if (Array.isArray(allFlashcards) && allFlashcards.length > 0) {
              const totalCorrect = allFlashcards.reduce((sum, card) => 
                sum + (card?.stats?.correct || 0), 0);
              const totalAttempts = allFlashcards.reduce((sum, card) => 
                sum + (card?.stats?.attempts || 0), 0);
              
              if (totalAttempts > 0) {
                avgPerformance.value = Math.round((totalCorrect / totalAttempts) * 100);
              }
            }
            
            // Atualizar revisões recentes com verificações
            const recentCards = allFlashcards
              .filter(card => card && card.lastReviewed)
              .sort((a, b) => {
                try {
                  return new Date(b.lastReviewed) - new Date(a.lastReviewed);
                } catch {
                  return 0;
                }
              })
              .slice(0, 5)
              .map(card => {
                try {
                  return {
                    id: card.id || Math.random().toString(),
                    title: card.front || card.question || 'Revisão',
                    subject: card.deckName || (card.tags && card.tags[0]) || 'Geral',
                    date: new Date(card.lastReviewed).toLocaleDateString('pt-BR'),
                    color: subjects.find(s => s.name === card.deckName)?.color || '#4F46E5',
                    status: (card.consecutiveCorrect || 0) > 0 ? 'completed' : 'pending'
                  };
                } catch (error) {
                  console.warn('Erro ao processar card:', error);
                  return null;
                }
              })
              .filter(card => card !== null);
            
            if (recentCards.length > 0) {
              recentRevisions.value = recentCards;
            }
          }
        }
      } catch (error) {
        console.error('Erro ao carregar dados do store:', error);
        // Continuar execução mesmo com erro
      }
    });
    
    // Implementação dos métodos faltantes para evitar erros
    const calculateMemoryStrength = (percentage, previousAttempts = 0) => {
      try {
        const base = percentage / 100;
        const factor = Math.pow(0.9, previousAttempts);
        return Math.min(1, base * factor);
      } catch (error) {
        console.error('Erro em calculateMemoryStrength:', error);
        return 0.5;
      }
    };
    
    const calculateComplexMemoryStrength = (data) => {
      try {
        const percentage = data.percentage || 50;
        const difficulty = data.difficulty || 3;
        const studyTime = data.studyTime || 30;
        const previousRevisions = data.previousRevisions || 0;
        
        // Fórmula complexa considerando múltiplos fatores
        const performanceFactor = percentage / 100;
        const difficultyFactor = (6 - difficulty) / 5;
        const timeFactor = Math.min(1, studyTime / 60);
        const revisionFactor = Math.min(1, 1 + (previousRevisions * 0.1));
        
        return (performanceFactor * 0.4 + difficultyFactor * 0.3 + 
                timeFactor * 0.2 + revisionFactor * 0.1);
      } catch (error) {
        console.error('Erro em calculateComplexMemoryStrength:', error);
        return 0.5;
      }
    };
    
    const calculateAdjustedInterval = (baseInterval, factors = {}) => {
      try {
        let adjustedInterval = baseInterval;
        
        if (factors.difficulty) {
          adjustedInterval *= (factors.difficulty / 3);
        }
        
        if (factors.previousRevisions) {
          adjustedInterval *= Math.pow(1.5, factors.previousRevisions);
        }
        
        if (factors.memoryStrength) {
          adjustedInterval *= (2 - factors.memoryStrength);
        }
        
        return Math.round(Math.max(1, adjustedInterval));
      } catch (error) {
        console.error('Erro em calculateAdjustedInterval:', error);
        return baseInterval;
      }
    };
    
    const generateAIInsights = (data) => {
      try {
        const insights = [];
        const percentage = data.percentage || 0;
        
        if (percentage < 60) {
          insights.push({
            type: 'warning',
            message: 'Reforce os conceitos básicos antes de avançar',
            priority: 'high'
          });
        }
        
        if (percentage >= 80) {
          insights.push({
            type: 'success',
            message: 'Excelente performance! Considere espaçar mais as revisões',
            priority: 'medium'
          });
        }
        
        if (data.studyTime && data.studyTime < 30) {
          insights.push({
            type: 'info',
            message: 'Aumente o tempo de estudo para melhor retenção',
            priority: 'medium'
          });
        }
        
        return insights;
      } catch (error) {
        console.error('Erro em generateAIInsights:', error);
        return [];
      }
    };
    
    const generateDeepInsights = (analysisData) => {
      try {
        return generateAIInsights(analysisData).map(insight => ({
          ...insight,
          deepAnalysis: true,
          confidence: Math.random() * 0.3 + 0.7 // 70-100% confidence
        }));
      } catch (error) {
        console.error('Erro em generateDeepInsights:', error);
        return [];
      }
    };
    
    const generateRecommendations = (percentage, subject = '') => {
      try {
        const recommendations = [];
        
        if (percentage < 70) {
          recommendations.push('Revise o material com mais frequência');
          recommendations.push('Utilize técnicas de memorização como flashcards');
        } else if (percentage < 85) {
          recommendations.push('Mantenha o ritmo atual de estudos');
          recommendations.push('Foque nos pontos com maior dificuldade');
        } else {
          recommendations.push('Excelente desempenho! Espaçe mais as revisões');
          recommendations.push('Ajude colegas com o conteúdo para fixar ainda mais');
        }
        
        if (subject) {
          recommendations.push(`Continue praticando ${subject} regularmente`);
        }
        
        return recommendations;
      } catch (error) {
        console.error('Erro em generateRecommendations:', error);
        return ['Continue estudando regularmente'];
      }
    };
    
    const generateTimeline = (startDate, intervals) => {
      try {
        const timeline = [];
        let currentDate = new Date(startDate);
        
        intervals.forEach((interval, index) => {
          currentDate = new Date(currentDate);
          currentDate.setDate(currentDate.getDate() + interval);
          
          timeline.push({
            revision: index + 1,
            date: currentDate.toLocaleDateString('pt-BR'),
            interval: interval
          });
        });
        
        return timeline;
      } catch (error) {
        console.error('Erro em generateTimeline:', error);
        return [];
      }
    };
    
    const generateDetailedTimeline = (data) => {
      try {
        const baseIntervals = [1, 3, 7, 14, 30, 60];
        const timeline = generateTimeline(new Date(), baseIntervals);
        
        return timeline.map(item => ({
          ...item,
          description: `${item.revision}ª Revisão`,
          importance: item.revision <= 3 ? 'alta' : 'média',
          estimatedDuration: `${30 + (item.revision * 10)} minutos`
        }));
      } catch (error) {
        console.error('Erro em generateDetailedTimeline:', error);
        return [];
      }
    };
    
    const getPerformanceIcon = (level) => {
      try {
        const icons = {
          'perfect': 'fas fa-trophy',
          'excellent': 'fas fa-star',
          'good': 'fas fa-thumbs-up',
          'average': 'fas fa-check',
          'poor': 'fas fa-exclamation-triangle'
        };
        
        return icons[level] || icons['average'];
      } catch (error) {
        console.error('Erro em getPerformanceIcon:', error);
        return 'fas fa-check';
      }
    };
    
    const getPerformanceColor = (percentage) => {
      if (percentage >= 90) return '#a855f7'; // purple
      if (percentage >= 80) return '#22c55e'; // green
      if (percentage >= 70) return '#3b82f6'; // blue
      if (percentage >= 60) return '#f59e0b'; // orange
      return '#ef4444'; // red
    };
    
    const getPerformanceGradient = (percentage) => {
      if (percentage >= 90) return 'linear-gradient(135deg, #a855f7, #764ba2)';
      if (percentage >= 80) return 'linear-gradient(135deg, #22c55e, #16a34a)';
      if (percentage >= 70) return 'linear-gradient(135deg, #3b82f6, #2563eb)';
      if (percentage >= 60) return 'linear-gradient(135deg, #f59e0b, #d97706)';
      return 'linear-gradient(135deg, #ef4444, #dc2626)';
    };
    
    const getRetentionEstimate = (percentage) => {
      // Estimativa simples baseada na taxa de acerto
      if (percentage >= 90) return 95;
      if (percentage >= 80) return 85;
      if (percentage >= 70) return 75;
      if (percentage >= 60) return 65;
      return 50;
    };
    
    const getPerformanceFeedback = (percentage) => {
      if (percentage >= 90) return 'Excelente desempenho! Você domina bem o conteúdo. A próxima revisão pode ser mais espaçada.';
      if (percentage >= 80) return 'Muito bom! Continue assim e você consolidará o conhecimento de forma eficaz.';
      if (percentage >= 70) return 'Bom trabalho! Algumas áreas ainda precisam de atenção, mas você está no caminho certo.';
      if (percentage >= 60) return 'Regular. Recomendamos revisar os pontos que teve dificuldade antes da próxima sessão.';
      return 'Precisa melhorar. Considere revisar o material novamente e fazer anotações dos pontos principais.';
    };
    
    const getDayName = (date) => {
      try {
        const days = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
        const dateObj = new Date(date);
        return days[dateObj.getDay()];
      } catch (error) {
        console.error('Erro em getDayName:', error);
        return 'Seg';
      }
    };
    
    const executeInsightAction = (action) => {
      try {
        console.log('Executando ação do insight:', action);
        // Implementar ações específicas conforme necessário
        alert(`Ação executada: ${action}`);
      } catch (error) {
        console.error('Erro em executeInsightAction:', error);
      }
    };
    
    const initializeCharts = () => {
      try {
        console.log('Inicializando gráficos...');
        // Implementar inicialização de gráficos quando necessário
      } catch (error) {
        console.error('Erro em initializeCharts:', error);
      }
    };
    
    // Implementação dos métodos faltantes adicionais
    const getScoreClass = (score) => {
      try {
        if (score >= 90) return 'score-excellent';
        if (score >= 75) return 'score-good';
        if (score >= 60) return 'score-average';
        return 'score-poor';
      } catch (error) {
        console.error('Erro em getScoreClass:', error);
        return 'score-average';
      }
    };
    
    const getBatchItemPercentage = (item) => {
      try {
        if (!item || !item.correct || !item.total) return 0;
        return Math.round((parseInt(item.correct) / parseInt(item.total)) * 100);
      } catch (error) {
        console.error('Erro em getBatchItemPercentage:', error);
        return 0;
      }
    };
    
    const closeResults = () => {
      try {
        calcResult.value = null;
        advancedResult.value = null;
        analysisResult.value = null;
      } catch (error) {
        console.error('Erro em closeResults:', error);
      }
    };
    
    const saveAnalysis = () => {
      try {
        const analysis = {
          date: new Date().toISOString(),
          results: calcResult.value || analysisResult.value,
          mode: 'simple'
        };
        
        // Salvar no histórico
        const history = JSON.parse(localStorage.getItem('revisionHistory') || '[]');
        history.unshift(analysis);
        localStorage.setItem('revisionHistory', JSON.stringify(history.slice(0, 50)));
        
        alert('Análise salva com sucesso!');
      } catch (error) {
        console.error('Erro em saveAnalysis:', error);
        alert('Erro ao salvar análise');
      }
    };
    
    const shareAnalysis = () => {
      try {
        const results = calcResult.value || advancedResult.value || analysisResult.value;
        if (!results) return;
        
        const text = `Resultado da Análise de Revisão:\n${JSON.stringify(results, null, 2)}`;
        
        if (navigator.share) {
          navigator.share({
            title: 'Análise de Revisão',
            text: text
          }).catch(err => console.error('Erro ao compartilhar:', err));
        } else {
          navigator.clipboard.writeText(text);
          alert('Resultado copiado para a área de transferência!');
        }
      } catch (error) {
        console.error('Erro em shareAnalysis:', error);
      }
    };
    
    const exportAnalysis = () => {
      try {
        const results = calcResult.value || advancedResult.value || analysisResult.value;
        if (!results) return;
        
        const dataStr = JSON.stringify(results, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
        
        const exportFileDefaultName = `analise-revisao-${new Date().toISOString().split('T')[0]}.json`;
        
        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
      } catch (error) {
        console.error('Erro em exportAnalysis:', error);
      }
    };
    
    const loadHistoryItem = (item) => {
      try {
        if (item && item.mode === 'quick' && item.results) {
          calcCorrect.value = item.results.correct || '';
          calcTotal.value = item.results.total || '';
          calculateRevision();
        }
      } catch (error) {
        console.error('Erro em loadHistoryItem:', error);
      }
    };
    
    const deleteHistoryItem = (index) => {
      try {
        calcHistory.value.splice(index, 1);
        // Atualizar localStorage
        localStorage.setItem('revisionHistory', JSON.stringify(calcHistory.value));
      } catch (error) {
        console.error('Erro em deleteHistoryItem:', error);
      }
    };
    
    const toggleHistoryView = () => {
      try {
        historyView.value = historyView.value === 'list' ? 'grid' : 'list';
      } catch (error) {
        console.error('Erro em toggleHistoryView:', error);
      }
    };
    
    const generateStudyReport = () => {
      try {
        const report = {
          date: new Date().toLocaleDateString('pt-BR'),
          totalRevisions: totalRevisions.value,
          avgPerformance: avgPerformance.value,
          subjects: subjects.map(s => ({
            name: s.name,
            revisionsCount: recentRevisions.value.filter(r => r.subject === s.name).length
          })),
          recommendations: [
            'Mantenha a consistência nos estudos',
            'Revise os tópicos com menor desempenho',
            'Utilize técnicas de aprendizado ativo'
          ]
        };
        
        console.log('Relatório de Estudo:', report);
        return report;
      } catch (error) {
        console.error('Erro em generateStudyReport:', error);
        return null;
      }
    };
    
    const importBatch = () => {
      try {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (e) => {
          const file = e.target.files[0];
          if (!file) return;
          
          const reader = new FileReader();
          reader.onload = (event) => {
            try {
              const data = JSON.parse(event.target.result);
              if (Array.isArray(data)) {
                batchItems.value = data;
                alert('Dados importados com sucesso!');
              }
            } catch (error) {
              alert('Erro ao importar arquivo');
            }
          };
          reader.readAsText(file);
        };
        
        input.click();
      } catch (error) {
        console.error('Erro em importBatch:', error);
      }
    };
    
    const formatHour = (hour) => {
      try {
        return `${hour.toString().padStart(2, '0')}:00`;
      } catch (error) {
        console.error('Erro em formatHour:', error);
        return '00:00';
      }
    };
    
    return {
      dataEstudo,
      nivelDificuldade,
      topico,
      showSubjectDropdown,
      selectedSubject,
      selectedDate,
      showAISuggestions,
      totalRevisions,
      avgPerformance,
      subjects,
      recentRevisions,
      aiSuggestions,
      selectedSubjectName,
      selectedSubjectColor,
      selectSubject,
      updateSelectedDate,
      handleDateSelected,
      adicionarRevisao,
      getStatusIcon,
      // Modal states
      showAIAssistant,
      showStats,
      showSettings,
      // AI Assistant
      aiInput,
      aiMessages,
      sendAIMessage,
      useSuggestion,
      // Stats
      weekProgress,
      subjectStats,
      // Settings
      settings,
      saveSettings,
      // Utility
      formatMessage,
      checkIfRevisionCalculation,
      calculateRevisionSchedule,
      // Calculator
      calcCorrect,
      calcTotal,
      calcResult,
      showHistory,
      calcHistory,
      analysisResult,
      calculateRevision,
      getPerformanceClass,
      getPerformanceLabel,
      // Computed properties
      livePercentage,
      canCalculate,
      autoResult,
      focusField,
      // Calculator methods
      validateInputs,
      handleInputChange,
      getBaseInterval,
      getPerformanceIcon,
      getPerformanceColor,
      getPerformanceGradient,
      getRetentionEstimate,
      getPerformanceFeedback,
      closeResults,
      // Helper methods
      saveToHistory
    };
  }
};
</script>

<style scoped>
/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Main Page */
.revision-page {
  min-height: 100vh;
  background: #0f0e17;
  color: #fffffe;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Background */
.bg-gradient {
  position: fixed;
  inset: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(240, 147, 251, 0.15) 0%, transparent 50%),
    #0f0e17;
  z-index: 0;
}

/* Main Container */
.main-container {
  position: relative;
  z-index: 1;
  display: flex;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
}

/* Left Panel */
.left-panel {
  flex: 0 0 400px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Panel Header */
.panel-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.header-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.header-text h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.header-text h1 span {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-text p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Stats Row */
.stats-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-card svg {
  font-size: 1.25rem;
  color: #667eea;
}

.stat-card strong {
  font-size: 1.5rem;
  color: white;
}

.stat-card span {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.quick-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.quick-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #667eea;
  color: white;
  transform: translateY(-2px);
}

.quick-btn svg {
  font-size: 1.25rem;
}

/* Form Card */
.form-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
}

.form-card h2 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.form-card h2 svg {
  color: #667eea;
}

/* Form Fields */
.revision-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-field label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.form-field label svg {
  font-size: 0.875rem;
  color: #667eea;
}

.form-field input[type="date"],
.form-field input[type="text"] {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 0.875rem;
  transition: all 0.3s;
}

.form-field input:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

/* Difficulty Options */
.difficulty-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.diff-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.diff-btn.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

.diff-btn:hover {
  border-color: #667eea;
  color: white;
}

/* Custom Select */
.custom-select {
  position: relative;
  cursor: pointer;
}

.select-display {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 0.875rem;
  transition: all 0.3s;
}

.select-display:hover {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  background: #1a1a2e;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
  z-index: 10;
}

.dropdown-item {
  width: 100%;
  padding: 0.75rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  text-align: left;
  transition: all 0.2s;
}

.dropdown-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

/* Submit Button */
.submit-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 8px;
  padding: 0.875rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Calculator Card */
.calculator-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
}

.calculator-body {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.calc-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.calc-field label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.calc-field label svg {
  font-size: 0.875rem;
  color: #667eea;
}

.calc-field input {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 0.875rem;
  text-align: center;
  transition: all 0.3s;
}

.calc-field input:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

.calc-result {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-percentage {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.percentage-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s;
}

.percentage-circle::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  padding: 2px;
  background: linear-gradient(135deg, var(--circle-color-1), var(--circle-color-2));
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: source-out;
  mask-composite: exclude;
}

.percentage-circle.poor {
  --circle-color-1: #ef4444;
  --circle-color-2: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

.percentage-circle.average {
  --circle-color-1: #f59e0b;
  --circle-color-2: #d97706;
  background: rgba(245, 158, 11, 0.1);
}

.percentage-circle.good {
  --circle-color-1: #10b981;
  --circle-color-2: #059669;
  background: rgba(16, 185, 129, 0.1);
}

.percentage-circle.excellent {
  --circle-color-1: #3b82f6;
  --circle-color-2: #2563eb;
  background: rgba(59, 130, 246, 0.1);
}

.percentage-circle.perfect {
  --circle-color-1: #8b5cf6;
  --circle-color-2: #7c3aed;
  background: rgba(139, 92, 246, 0.1);
}

.percentage-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.performance-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
}

.result-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.info-item svg {
  color: #667eea;
  font-size: 0.875rem;
}

.info-item strong {
  color: white;
}

.result-recommendation {
  padding: 0.75rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
}

.result-recommendation p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  margin: 0;
}

/* Recent Card */
.recent-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-header h3 {
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-header h3 svg {
  color: #667eea;
}

.link-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.2s;
}

.link-btn:hover {
  color: #8099ff;
}

/* Revision List */
.revision-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.revision-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  transition: all 0.2s;
}

.revision-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.rev-color {
  width: 4px;
  height: 30px;
  border-radius: 2px;
}

.rev-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.rev-info strong {
  font-size: 0.875rem;
  color: white;
}

.rev-info small {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.rev-status {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.4);
}

/* Right Panel */
.right-panel {
  flex: 1;
  min-width: 0;
}

.calendar-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  height: 100%;
}

.calendar-card h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Responsive */
@media (max-width: 1024px) {
  .main-container {
    flex-direction: column;
  }
  
  .left-panel {
    flex: 1;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }
  
  .right-panel {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }
}

@media (max-width: 640px) {
  .main-container {
    padding: 1rem;
    gap: 1.5rem;
  }
  
  .stats-row {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .difficulty-options {
    grid-template-columns: 1fr;
  }
  
  .calc-form {
    grid-template-columns: 1fr;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-container {
  background: #1a1a2e;
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-container.stats-modal {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
}

.modal-header h3 svg {
  color: #667eea;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

/* AI Assistant Styles */
.ai-chat {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.chat-messages {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  max-width: 80%;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  line-height: 1.5;
}

.message.user {
  align-self: flex-end;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.message.ai {
  align-self: flex-start;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.9);
}

.chat-input {
  display: flex;
  gap: 0.5rem;
}

.chat-input input {
  flex: 1;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 0.875rem;
}

.chat-input input:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

.send-btn {
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.send-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.ai-suggestions h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
}

.suggestion-pills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.suggestion-pill {
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.suggestion-pill:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

/* Stats Modal Styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-box {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
}

.stat-icon-lg {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.25rem;
  color: white;
}

.stat-box h4 {
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  margin: 0 0 0.25rem;
}

.stat-box p {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.chart-section {
  margin-bottom: 2rem;
}

.chart-section h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.progress-bars {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.day-progress {
  display: grid;
  grid-template-columns: 40px 1fr 40px;
  align-items: center;
  gap: 0.75rem;
}

.day-name {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.day-hours {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: right;
}

.subjects-performance h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.subject-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.subject-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
}

.subject-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.subject-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.subject-name {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.subject-score {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.score-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

.trend-icon {
  font-size: 0.75rem;
}

.trend-icon.up {
  color: #4ade80;
}

.trend-icon.down {
  color: #ef4444;
}

/* Settings Styles */
.settings-section {
  margin-bottom: 2rem;
}

.settings-section h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  margin-bottom: 0.75rem;
}

.setting-info label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  margin-bottom: 0.25rem;
}

.setting-info small {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.toggle-switch {
  position: relative;
  width: 48px;
  height: 24px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  transition: all 0.3s;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s;
}

.toggle-switch input:checked + .toggle-slider {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

.setting-select {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.setting-select option {
  background: #1a1a2e;
  color: white;
}

.algorithm-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.algorithm-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.algorithm-option input {
  display: none;
}

.algorithm-option.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.option-content {
  flex: 1;
  margin-left: 0.75rem;
}

.option-content strong {
  display: block;
  font-size: 0.875rem;
  color: white;
  margin-bottom: 0.25rem;
}

.option-content small {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.save-settings-btn {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.save-settings-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* AI Chat Styles */
.ai-chat {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.chat-messages {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1rem;
  padding-right: 0.5rem;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.message {
  margin-bottom: 1rem;
  display: flex;
  align-items: flex-start;
}

.message.user {
  justify-content: flex-end;
}

.message.ai {
  justify-content: flex-start;
}

.message-content {
  max-width: 80%;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.message.user .message-content {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.ai .message-content {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom-left-radius: 4px;
}

.chat-input {
  display: flex;
  gap: 0.75rem;
}

.chat-input input {
  flex: 1;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 0.875rem;
  transition: all 0.3s;
}

.chat-input input:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

.send-btn {
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.ai-suggestions h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
}

.suggestion-pills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.suggestion-pill {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.suggestion-pill:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-1px);
}

/* Modal Transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active .modal-container,
.modal-fade-leave-active .modal-container {
  transition: transform 0.3s ease;
}

.modal-fade-enter-from .modal-container {
  transform: scale(0.9);
}

.modal-fade-leave-to .modal-container {
  transform: scale(0.9);
}

@media (max-width: 640px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .modal-container {
    max-width: 100%;
  }
}

/* Enhanced Calculator Styles */
.card-actions {
  display: flex;
  gap: 0.5rem;
}

.icon-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.icon-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Tabs */
.calc-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  padding: 0.25rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.tab-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 8px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tab-btn.active {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.tab-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

/* Mode Content */
.calc-mode-content {
  animation: fadeIn 0.3s ease-in-out;
}

/* Quick Presets */
.quick-presets {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.preset-label {
  display: block;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.5rem;
}

.preset-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem 0.75rem;
  margin: 0.25rem;
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.preset-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

/* Advanced Form */
.advanced-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.advanced-form select {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.advanced-form select option {
  background: #1a1a2e;
  color: white;
}

/* Difficulty Slider */
.difficulty-slider {
  margin-top: 0.5rem;
}

.difficulty-slider input[type="range"] {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.difficulty-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: #667eea;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
}

.difficulty-slider input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.difficulty-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Batch Mode */
.batch-info {
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.batch-info p {
  margin: 0;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.batch-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.batch-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.batch-input {
  flex: 1;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: white;
  font-size: 0.875rem;
}

.batch-input.small {
  flex: 0.3;
}

.batch-separator {
  color: rgba(255, 255, 255, 0.4);
  font-weight: 600;
}

.remove-btn {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  width: 32px;
  height: 32px;
  border-radius: 6px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background: rgba(239, 68, 68, 0.2);
}

.add-batch-btn,
.calculate-batch-btn {
  width: 100%;
  padding: 0.875rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.calculate-batch-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: white;
  font-weight: 600;
}

.add-batch-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #667eea;
}

.calculate-batch-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Enhanced Results */
.percentage-details {
  margin-top: 0.25rem;
}

.percentage-details small {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

.revision-schedule {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.revision-schedule h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
}

.revision-schedule h4 svg {
  color: #667eea;
}

.schedule-timeline {
  position: relative;
  padding-left: 2rem;
}

.schedule-timeline::before {
  content: '';
  position: absolute;
  left: 0.5rem;
  top: 0.75rem;
  bottom: 0.75rem;
  width: 2px;
  background: rgba(255, 255, 255, 0.1);
}

.timeline-item {
  position: relative;
  margin-bottom: 1rem;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: -1.5rem;
  top: 0.25rem;
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 50%;
  border: 2px solid #1a1a2e;
}

.timeline-content strong {
  display: block;
  font-size: 0.875rem;
  color: white;
  margin-bottom: 0.25rem;
}

.timeline-content small {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

.save-btn {
  width: 100%;
  margin-top: 1rem;
  padding: 0.875rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid #667eea;
  border-radius: 8px;
  color: #667eea;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.save-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Advanced Results */
.calc-result.advanced {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.result-header h4 {
  margin: 0;
  font-size: 1.125rem;
  color: white;
}

.result-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 700;
  font-size: 1rem;
}

.result-badge.poor {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.result-badge.average {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.result-badge.good {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.result-badge.excellent {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.result-badge.perfect {
  background: rgba(139, 92, 246, 0.2);
  color: #8b5cf6;
}

.advanced-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metric-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.metric-card svg {
  font-size: 1.25rem;
  color: #667eea;
}

.metric-card strong {
  font-size: 1.25rem;
  color: white;
}

.metric-card span {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

.advanced-recommendation {
  margin-bottom: 1.5rem;
}

.advanced-recommendation h5 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: white;
}

.advanced-recommendation ul {
  list-style: none;
  padding: 0;
}

.advanced-recommendation li {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-left: 3px solid #667eea;
  border-radius: 0 6px 6px 0;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
}

.study-plan {
  margin-top: 1.5rem;
}

.study-plan h5 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.plan-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  transition: all 0.2s;
}

.plan-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.plan-item svg {
  font-size: 1.25rem;
  color: #667eea;
}

.plan-item strong {
  display: block;
  font-size: 0.875rem;
  color: white;
  margin-bottom: 0.25rem;
}

.plan-item p {
  margin: 0;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Batch Results */
.batch-results {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
}

.batch-results h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.batch-result-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.batch-result-item {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.batch-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.batch-result-header strong {
  font-size: 0.875rem;
  color: white;
}

.batch-percentage {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 700;
}

.batch-result-info {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

.batch-result-info svg {
  margin-right: 0.25rem;
}

.export-btn {
  width: 100%;
  padding: 0.875rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid #667eea;
  border-radius: 8px;
  color: #667eea;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.export-btn:hover {
  background: #667eea;
  color: white;
}

/* History Panel */
.history-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  background: #1a1a2e;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
  z-index: 100;
  max-height: 400px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-header h4 {
  margin: 0;
  font-size: 1rem;
  color: white;
}

.clear-history-btn {
  background: rgba(239, 68, 68, 0.1);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-history-btn:hover {
  background: rgba(239, 68, 68, 0.2);
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.history-item {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.history-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: #667eea;
}

.history-date {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.25rem;
}

.history-details {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.history-percentage {
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 700;
}

.history-details span {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.history-empty {
  text-align: center;
  padding: 2rem;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.875rem;
}

/* Animations */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateY(10px);
  opacity: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .advanced-metrics {
    grid-template-columns: 1fr;
  }
  
  .calc-tabs {
    flex-direction: column;
  }
  
  .tab-btn {
    width: 100%;
  }
}

/* Revision Calculator Styles */
/* Calculator Card - matching other cards */
.calculator-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.calculator-card h2 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.calculator-card h2 svg {
  color: #667eea;
}

.calc-content {
  position: relative;
}

/* Input Section within Card */
.calculator-card .input-section {
  margin-bottom: 1.5rem;
}

.calculator-card .inputs-row {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 15px;
  flex-wrap: nowrap;
  margin: 0 auto;
  max-width: 400px;
}

.calculator-card .divider-text {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  padding-bottom: 15px;
  font-weight: 400;
  flex-shrink: 0;
}

.calculator-card .result-divider {
  margin: 1.5rem 0;
}

.calculator-card .stats-grid {
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  max-width: 400px;
  margin: 0 auto;
}

.calculator-card .stat-box {
  padding: 0.75rem;
  gap: 0.75rem;
}

.calculator-card .stat-icon {
  width: 36px;
  height: 36px;
  font-size: 1rem;
}

.calculator-card .stat-value {
  font-size: 1rem;
}

.calculator-card .stat-label {
  font-size: 0.7rem;
}

.calculator-card .performance-feedback {
  margin-top: 1rem;
  text-align: center;
}

.calculator-card .feedback-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
}

.calculator-card .feedback-text {
  font-size: 0.7rem;
  margin-top: 0.5rem;
}

/* Background Orbs */
.calc-bg-orbs {
  position: fixed;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.4;
}

.orb-1 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  top: -200px;
  right: -100px;
  animation: float 20s ease-in-out infinite;
}

.orb-2 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  bottom: -150px;
  left: -50px;
  animation: float 25s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  50% { transform: translate(30px, -30px) scale(1.1); }
}

/* Header Section */
.calc-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 1;
}

.header-badge {
  display: inline-block;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  letter-spacing: 0.1em;
  margin-bottom: 1rem;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.header-gradient-text h2 {
  font-size: 1.75rem;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.02em;
}

.header-icon {
  display: inline-flex;
  width: 60px;
  height: 60px;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  animation: icon-float 3s ease-in-out infinite;
}

@keyframes icon-float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.header-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.125rem;
  font-weight: 400;
  margin-bottom: 1.5rem;
  letter-spacing: 0.02em;
}

.header-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

.stat-item:hover {
  color: #667eea;
}

.stat-item i {
  font-size: 0.75rem;
  opacity: 0.8;
}

.stat-divider {
  opacity: 0.3;
}

/* Calculator Card */
.calc-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.calc-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

/* Input Section */
.input-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.inputs-row {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 1rem;
}

.calculator-card .input-wrapper {
  width: 120px;
  flex-shrink: 0;
}

.calculator-card .input-wrapper label {
  display: block;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.4rem;
  font-weight: 500;
  text-align: center;
  letter-spacing: 0.02em;
}

.calc-input {
  width: 100%;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #fffffe;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  -moz-appearance: textfield;
  -webkit-appearance: none;
  appearance: none;
  height: 50px;
  box-sizing: border-box;
  line-height: 1;
}

.calc-input::-webkit-outer-spin-button,
.calc-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.calc-input::placeholder {
  color: rgba(255, 255, 255, 0.3);
  opacity: 1;
}

/* Estilos específicos para inputs da calculadora */
.calculator-card input[type="number"]::-webkit-input-placeholder {
  text-align: center !important;
}
.calculator-card input[type="number"]::-moz-placeholder {
  text-align: center !important;
}
.calculator-card input[type="number"]::placeholder {
  text-align: center !important;
}
.calculator-card input[type="number"] {
  text-align: center !important;
}

.calc-input:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.calc-input::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.divider-text {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.4);
  margin: 0 0.5rem;
  padding-bottom: 0.5rem;
}

/* Result Section */
.result-section {
  animation: fadeInUp 0.6s ease;
}

.result-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  margin: 2rem 0;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-box {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-box:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.05);
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.gradient-blue {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.gradient-pink {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #fffffe;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
}

/* Performance Feedback */
.performance-feedback {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
}

.feedback-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.feedback-badge.poor {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.feedback-badge.average {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.feedback-badge.good {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.feedback-badge.excellent {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.feedback-badge.perfect {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.feedback-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-up-enter-active,
.fade-up-leave-active {
  transition: all 0.5s ease;
}

.fade-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-up-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Tech Calculator Styles */
.calculator-tech {
  position: relative;
  padding: 3rem 2rem;
  max-width: 600px;
  margin: 0 auto;
  perspective: 1000px;
}

/* Neural Background */
.neural-bg {
  position: absolute;
  inset: 0;
  overflow: hidden;
  opacity: 0.1;
  pointer-events: none;
}

.neural-line {
  position: absolute;
  height: 1px;
  width: 100%;
  background: linear-gradient(90deg, transparent, #667eea, transparent);
  animation: neural-flow 8s linear infinite;
}

.neural-line:nth-child(1) { top: 20%; }
.neural-line:nth-child(2) { top: 35%; }
.neural-line:nth-child(3) { top: 50%; }
.neural-line:nth-child(4) { top: 65%; }
.neural-line:nth-child(5) { top: 80%; }

@keyframes neural-flow {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Calculator Container */
.calc-container {
  position: relative;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 3rem;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 0 40px rgba(102, 126, 234, 0.1),
    inset 0 0 20px rgba(255, 255, 255, 0.02);
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.calc-container:hover {
  transform: rotateX(2deg) rotateY(-2deg);
}

/* Input Matrix */
.input-matrix {
  margin-bottom: 3rem;
}

.matrix-label {
  font-size: 0.75rem;
  color: rgba(102, 126, 234, 0.8);
  letter-spacing: 0.2em;
  margin-bottom: 1rem;
  font-weight: 500;
}

.matrix-grid {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.matrix-cell {
  position: relative;
}

.tech-input {
  width: 100px;
  height: 80px;
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #fffffe;
  font-size: 2.5rem;
  font-weight: 300;
  text-align: center;
  transition: all 0.3s ease;
  -moz-appearance: textfield;
  font-family: 'Space Mono', monospace;
}

.tech-input::-webkit-outer-spin-button,
.tech-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.tech-input:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.tech-input.active ~ .input-glow {
  opacity: 1;
}

.tech-input::placeholder {
  color: rgba(255, 255, 255, 0.2);
}

.input-glow {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3), transparent);
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  filter: blur(10px);
}

.matrix-operator {
  font-size: 2rem;
  color: rgba(255, 255, 255, 0.3);
  font-weight: 200;
}

/* Processing Animation */
.processing {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.process-dots {
  display: flex;
  gap: 0.5rem;
}

.process-dots span {
  width: 8px;
  height: 8px;
  background: #667eea;
  border-radius: 50%;
  animation: process-pulse 1.4s ease-in-out infinite;
}

.process-dots span:nth-child(2) { animation-delay: 0.2s; }
.process-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes process-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Output Matrix */
.output-matrix {
  animation: output-appear 0.6s ease;
}

@keyframes output-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.output-grid {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.output-metric {
  text-align: center;
}

.metric-value {
  font-size: 3rem;
  font-weight: 200;
  margin-bottom: 0.5rem;
  transition: color 0.5s ease;
  font-family: 'Space Mono', monospace;
}

.metric-unit {
  font-size: 1.5rem;
  opacity: 0.6;
}

.metric-bar {
  width: 100px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  transition: width 0.6s ease;
  border-radius: 2px;
}

.output-arrow {
  color: rgba(255, 255, 255, 0.3);
}

.arrow-path {
  animation: arrow-pulse 2s ease infinite;
}

@keyframes arrow-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

.output-result {
  text-align: center;
}

.result-value {
  font-size: 3rem;
  font-weight: 200;
  color: #fffffe;
  font-family: 'Space Mono', monospace;
}

.result-unit {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.5);
  letter-spacing: 0.1em;
}

.performance-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: glow 2s ease infinite;
}

@keyframes glow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; box-shadow: 0 0 10px currentColor; }
}

.indicator-text {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

/* Holographic Effect */
.holo-effect {
  position: absolute;
  inset: -1px;
  background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.calculator-tech:hover .holo-effect {
  opacity: 1;
  animation: holo-scan 3s linear infinite;
}

@keyframes holo-scan {
  0% { transform: translateX(-100%) translateY(-100%); }
  100% { transform: translateX(100%) translateY(100%); }
}

/* Animations */
.process-enter-active,
.process-leave-active {
  transition: all 0.3s ease;
}

.process-enter-from,
.process-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

.output-enter-active {
  transition: all 0.6s ease;
}

.output-enter-from {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
}


.header-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.system-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  position: relative;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  font-size: 1.5rem;
  color: white;
}

.pulse-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #667eea;
  border-radius: 14px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.title-text h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: white;
}

.ai-text {
  background: linear-gradient(90deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.title-text p {
  margin: 0.25rem 0 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.status-indicator.active {
  border-color: rgba(34, 197, 94, 0.5);
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.status-indicator i {
  font-size: 0.5rem;
}

.neural-activity {
  display: flex;
  gap: 0.25rem;
  align-items: flex-end;
  height: 20px;
}

.activity-bar {
  width: 3px;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 2px;
  animation: activity 1s ease-in-out infinite;
}

.activity-bar:nth-child(1) {
  height: 50%;
  animation-delay: 0s;
}

.activity-bar:nth-child(2) {
  height: 100%;
  animation-delay: 0.2s;
}

.activity-bar:nth-child(3) {
  height: 70%;
  animation-delay: 0.4s;
}

@keyframes activity {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.5;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* Mode Navigation */
.mode-navigation {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  background: rgba(255, 255, 255, 0.02);
  padding: 0.5rem;
  gap: 0.5rem;
  position: relative;
}

.mode-slider {
  position: absolute;
  top: 0.5rem;
  left: calc(0.5rem + var(--active-index) * 25%);
  width: calc(25% - 0.5rem);
  height: calc(100% - 1rem);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.mode-tab {
  position: relative;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  border-radius: 12px;
  text-align: center;
}

.mode-tab i {
  font-size: 1.5rem;
  transition: all 0.3s;
}

.mode-name {
  font-weight: 600;
  font-size: 0.875rem;
}

.mode-description {
  font-size: 0.75rem;
  opacity: 0.7;
}

.mode-tab.active {
  color: white;
}

.mode-tab.active i {
  transform: scale(1.1);
}

/* Analysis Panels */
.analysis-panel {
  padding: 2rem;
  min-height: 400px;
}

.panel-glow {
  position: absolute;
  top: -100px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
  filter: blur(60px);
  pointer-events: none;
}

/* Input Grid */
.input-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.input-group {
  position: relative;
}

.input-group.floating input,
.input-group.floating select {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s;
}

.input-group.floating label {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.875rem;
  transition: all 0.3s;
  pointer-events: none;
}

.input-group.floating input:focus,
.input-group.floating input:not(:placeholder-shown),
.input-group.floating select:focus,
.input-group.floating select:valid {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

.input-group.floating input:focus + label,
.input-group.floating input:not(:placeholder-shown) + label,
.input-group.floating select:focus + label,
.input-group.floating select:valid + label {
  top: -10px;
  left: 0.75rem;
  font-size: 0.75rem;
  background: #0f0f23;
  padding: 0 0.5rem;
  color: #667eea;
}

.input-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.3);
  font-size: 1.25rem;
}

/* Analyze Button */
.analyze-button {
  width: 100%;
  padding: 1.25rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.analyze-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.analyze-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.button-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transition: all 0.6s;
}

.analyze-button:active .button-glow {
  width: 300px;
  height: 300px;
  opacity: 0;
}

/* Live Preview */
.live-preview {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
}

.preview-percentage {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.percentage-ring {
  width: 100px;
  height: 100px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: conic-gradient(
    from 0deg,
    #667eea 0deg,
    #667eea calc(var(--percentage) * 3.6deg),
    rgba(255, 255, 255, 0.1) calc(var(--percentage) * 3.6deg)
  );
  border-radius: 50%;
}

.percentage-ring::before {
  content: '';
  position: absolute;
  inset: 10px;
  background: #0f0f23;
  border-radius: 50%;
}

.percentage-value {
  position: relative;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.preview-indicator {
  position: relative;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.indicator-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: var(--width);
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.indicator-label {
  display: block;
  text-align: center;
  margin-top: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}
</style>