<template>
  <div class="revision-form">
    <div class="form-header">
      <h2>{{ isTheoretical ? 'Registrar Estudo Teórico' : 'Registrar Revisão Prática' }}</h2>
      <div class="type-selector">
        <button 
          :class="['type-btn', { active: isTheoretical }]"
          @click="isTheoretical = true"
        >
          Estudo Teóric<PERSON>
        </button>
        <button 
          :class="['type-btn', { active: !isTheoretical }]"
          @click="isTheoretical = false"
        >
          Revisão Prática
        </button>
      </div>
    </div>

    <form @submit.prevent="handleSubmit" class="revision-form-content">
      <div class="form-group">
        <label>Assunto</label>
        <input 
          v-model="formData.subject"
          type="text"
          required
          placeholder="Nome do assunto"
        >
      </div>

      <div class="form-group">
        <label>Data</label>
        <input 
          v-model="formData.date"
          type="date"
          required
        >
      </div>

      <div class="form-group">
        <label>Dificuldade</label>
        <select v-model="formData.difficulty" required>
          <option value="easy">Fácil</option>
          <option value="medium">Médio</option>
          <option value="hard">Difícil</option>
        </select>
      </div>

      <!-- Campos específicos para revisão prática -->
      <template v-if="!isTheoretical">
        <div class="form-group">
          <label>Total de Questões</label>
          <input 
            v-model.number="formData.totalQuestions"
            type="number"
            min="1"
            required
          >
        </div>

        <div class="form-group">
          <label>Questões Corretas</label>
          <input 
            v-model.number="formData.correctAnswers"
            type="number"
            min="0"
            :max="formData.totalQuestions"
            required
          >
        </div>

        <div class="performance-indicator" v-if="performance">
          <span>Desempenho: {{ performance }}%</span>
          <div class="progress-bar">
            <div 
              class="progress-fill"
              :style="{ width: `${performance}%` }"
              :class="getPerformanceClass"
            ></div>
          </div>
        </div>
      </template>

      <div class="form-group">
        <label>Notas</label>
        <textarea 
          v-model="formData.notes"
          placeholder="Observações importantes..."
          rows="3"
        ></textarea>
      </div>

      <div class="form-actions">
        <button type="submit" class="submit-btn">
          Salvar Revisão
        </button>
      </div>
    </form>

    <!-- Próxima revisão (se aplicável) -->
    <div v-if="nextRevisionDate" class="next-revision-info">
      <h3>Próxima Revisão Agendada</h3>
      <p>{{ formatDate(nextRevisionDate) }}</p>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'RevisionForm',
  
  setup() {
    const store = useStore()
    const isTheoretical = ref(true)
    const nextRevisionDate = ref(null)

    const formData = ref({
      subject: '',
      date: new Date().toISOString().split('T')[0],
      difficulty: 'medium',
      totalQuestions: 0,
      correctAnswers: 0,
      notes: ''
    })

    const performance = computed(() => {
      if (!formData.value.totalQuestions) return 0
      return Math.round((formData.value.correctAnswers / formData.value.totalQuestions) * 100)
    })

    const getPerformanceClass = computed(() => {
      if (performance.value >= 80) return 'excellent'
      if (performance.value >= 60) return 'good'
      return 'needs-improvement'
    })

    const handleSubmit = async () => {
      try {
        const result = isTheoretical.value
          ? await store.dispatch('revisions/registerTheoretical', formData.value)
          : await store.dispatch('revisions/registerPractical', formData.value)

        nextRevisionDate.value = isTheoretical.value
          ? result.firstPracticeDate
          : result.nextRevisionDate

        // Resetar formulário
        formData.value = {
          subject: '',
          date: new Date().toISOString().split('T')[0],
          difficulty: 'medium',
          totalQuestions: 0,
          correctAnswers: 0,
          notes: ''
        }
      } catch (error) {
        console.error('Erro ao salvar revisão:', error)
      }
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    return {
      isTheoretical,
      formData,
      performance,
      getPerformanceClass,
      nextRevisionDate,
      handleSubmit,
      formatDate
    }
  }
}
</script>

<style scoped>
.revision-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--background-dark);
  border-radius: var(--border-radius-lg);
}

/* ... (estilos adicionais) ... */
</style> 