<template>
  <div class="revision-notifications">
    <div v-if="upcomingRevisions.length === 0" class="no-revisions">
      <font-awesome-icon icon="check-circle" class="icon-success" />
      <p>Você está em dia com suas revisões!</p>
    </div>
    
    <div v-else class="notifications-container">
      <h3>
        <font-awesome-icon icon="bell" class="icon-notification" />
        Revisões Agendadas
      </h3>
      
      <div class="notifications-list">
        <div 
          v-for="revision in upcomingRevisions" 
          :key="revision.id"
          class="notification-item"
          :class="`priority-${revision.priority}`"
        >
          <div class="notification-icon">
            <font-awesome-icon 
              :icon="getRevisionIcon(revision)" 
              :class="`icon-${revision.type}`" 
            />
          </div>
          
          <div class="notification-content">
            <div class="notification-title">
              {{ revision.title }}
            </div>
            <div class="notification-date">
              {{ formatDate(revision.date) }} 
              <span class="time-indicator">{{ getTimeIndicator(revision.date) }}</span>
            </div>
          </div>
          
          <div class="notification-actions">
            <button 
              @click="completeRevision(revision)" 
              class="action-button complete"
              title="Marcar como concluída"
            >
              <font-awesome-icon icon="check" />
            </button>
            <button 
              @click="postponeRevision(revision)" 
              class="action-button postpone"
              title="Adiar revisão"
            >
              <font-awesome-icon icon="arrow-right" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';
import { useStore } from 'vuex';
import { format, differenceInDays, differenceInHours, isToday, isTomorrow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export default {
  name: 'RevisionNotifications',
  
  setup() {
    const store = useStore();
    
    // Obter revisões próximas (máximo 7 dias)
    const upcomingRevisions = computed(() => {
      const revisions = store.getters['revisions/allRevisions'] || [];
      const now = new Date();
      
      return revisions
        .filter(revision => {
          const revisionDate = new Date(revision.date);
          // Filtrar apenas revisões futuras e dentro de 7 dias
          return revisionDate > now && differenceInDays(revisionDate, now) <= 7;
        })
        .map(revision => {
          // Calcular prioridade
          const revisionDate = new Date(revision.date);
          let priority = 'low';
          const daysUntil = differenceInDays(revisionDate, now);
          
          if (isToday(revisionDate)) {
            priority = 'high';
          } else if (isTomorrow(revisionDate)) {
            priority = 'medium';
          } else if (daysUntil <= 3) {
            priority = 'normal';
          }
          
          return {
            ...revision,
            priority
          };
        })
        .sort((a, b) => new Date(a.date) - new Date(b.date))
        .slice(0, 5); // Mostrar apenas 5 revisões
    });
    
    // Formatar data
    const formatDate = (dateString) => {
      try {
        const date = new Date(dateString);
        
        if (isToday(date)) {
          return 'Hoje';
        } else if (isTomorrow(date)) {
          return 'Amanhã';
        } else {
          return format(date, "EEEE, d 'de' MMMM", { locale: ptBR });
        }
      } catch (error) {
        return 'Data inválida';
      }
    };
    
    // Obter indicador de tempo
    const getTimeIndicator = (dateString) => {
      try {
        const date = new Date(dateString);
        const now = new Date();
        
        if (isToday(date)) {
          const hours = differenceInHours(date, now);
          if (hours <= 0) {
            return 'Agora';
          } else if (hours <= 1) {
            return 'Em breve';
          } else {
            return `Em ${hours} horas`;
          }
        } else {
          const days = differenceInDays(date, now);
          return `Em ${days} dias`;
        }
      } catch (error) {
        return '';
      }
    };
    
    // Obter ícone para o tipo de revisão
    const getRevisionIcon = (revision) => {
      const type = revision.type || (revision.isPractical ? 'practical' : 'theoretical');
      
      switch (type) {
        case 'practical':
          return 'brain';
        case 'theoretical':
          return 'book';
        default:
          return 'calendar-check';
      }
    };
    
    // Marcar revisão como concluída
    const completeRevision = (revision) => {
      if (confirm(`Marcar a revisão "${revision.title || revision.subject}" como concluída?`)) {
        store.dispatch('revisions/completeRevision', revision.id);
      }
    };
    
    // Adiar revisão
    const postponeRevision = (revision) => {
      const days = prompt('Adiar por quantos dias?', '1');
      if (days && !isNaN(days)) {
        store.dispatch('revisions/postponeRevision', { id: revision.id, days: parseInt(days, 10) });
      }
    };
    
    return {
      upcomingRevisions,
      formatDate,
      getTimeIndicator,
      getRevisionIcon,
      completeRevision,
      postponeRevision
    };
  }
};
</script>

<style scoped>
.revision-notifications {
  margin-bottom: 2rem;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg, 12px);
  padding: 1.5rem;
  box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
}

.no-revisions {
  text-align: center;
  padding: 1.5rem;
  color: var(--text-color-secondary);
}

.icon-success {
  font-size: 2rem;
  color: var(--success-color, #28a745);
  margin-bottom: 1rem;
}

.notifications-container h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.3rem;
  color: var(--text-color);
}

.icon-notification {
  color: var(--warning-color, #ffc107);
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--background-color);
  border-radius: var(--border-radius-md, 8px);
  border-left: 4px solid var(--border-color);
  transition: all 0.2s ease;
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1));
}

.notification-item.priority-high {
  border-left-color: var(--danger-color, #dc3545);
  background-color: rgba(var(--danger-rgb, 220, 53, 69), 0.05);
}

.notification-item.priority-medium {
  border-left-color: var(--warning-color, #ffc107);
  background-color: rgba(var(--warning-rgb, 255, 193, 7), 0.05);
}

.notification-item.priority-normal {
  border-left-color: var(--info-color, #17a2b8);
  background-color: rgba(var(--info-rgb, 23, 162, 184), 0.05);
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--background-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.icon-theoretical {
  color: var(--primary-color);
}

.icon-practical {
  color: var(--accent-color);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-color);
}

.notification-date {
  font-size: 0.9rem;
  color: var(--text-color-secondary);
}

.time-indicator {
  font-weight: 600;
  color: var(--text-color);
  margin-left: 0.5rem;
}

.notification-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--background-accent);
  color: var(--text-color-secondary);
}

.action-button.complete:hover {
  background-color: var(--success-color, #28a745);
  color: white;
}

.action-button.postpone:hover {
  background-color: var(--info-color, #17a2b8);
  color: white;
}
</style> 