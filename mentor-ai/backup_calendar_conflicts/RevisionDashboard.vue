<template>
  <div class="revision-dashboard">
    <section class="dashboard-header">
      <h2>Dashboard de Revisões</h2>
      <p class="subtitle">Análise completa do seu desempenho e padrões de estudo</p>
      
      <div class="period-selector">
        <button 
          v-for="period in periods" 
          :key="period.value"
          @click="selectedPeriod = period.value"
          :class="['period-btn', selectedPeriod === period.value ? 'active' : '']"
        >
          {{ period.label }}
        </button>
      </div>
    </section>
    
    <div class="metrics-grid">
      <!-- Performance Metrics -->
      <div class="metrics-card performance">
        <h3>Performance Geral</h3>
        
        <div class="performance-stats">
          <div class="stat-item">
            <div class="stat-value">{{ metrics.averagePerformance }}%</div>
            <div class="stat-label">Média de Acertos</div>
            <div class="stat-trend positive" v-if="metrics.performanceTrend > 0">
              <font-awesome-icon icon="arrow-up" /> {{ metrics.performanceTrend }}%
            </div>
            <div class="stat-trend negative" v-else>
              <font-awesome-icon icon="arrow-down" /> {{ Math.abs(metrics.performanceTrend) }}%
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-value">{{ metrics.adherenceScore }}%</div>
            <div class="stat-label">Aderência ao Cronograma</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-value">{{ metrics.retentionRate }}%</div>
            <div class="stat-label">Taxa de Retenção</div>
          </div>
        </div>
        
        <div class="performance-insights">
          <p v-if="metrics.averagePerformance >= 80">
            <font-awesome-icon icon="check-circle" class="insight-icon positive" />
            Excelente desempenho! Continue com a estratégia atual.
          </p>
          <p v-else-if="metrics.averagePerformance >= 60">
            <font-awesome-icon icon="info-circle" class="insight-icon neutral" />
            Bom desempenho, mas há espaço para melhorias nas revisões.
          </p>
          <p v-else>
            <font-awesome-icon icon="exclamation-circle" class="insight-icon negative" />
            Considere ajustar sua estratégia de estudos para melhorar a retenção.
          </p>
        </div>
      </div>
      
      <!-- Study Patterns -->
      <div class="metrics-card study-patterns">
        <h3>Padrões de Estudo</h3>
        
        <div class="patterns-content">
          <div class="pattern-item">
            <div class="pattern-icon">
              <font-awesome-icon icon="calendar-day" />
            </div>
            <div class="pattern-info">
              <div class="pattern-title">Dias Preferenciais</div>
              <div class="pattern-badges">
                <span 
                  v-for="(day, index) in studyPatterns.preferredDays" 
                  :key="index" 
                  class="day-badge"
                >
                  {{ weekdays[day] }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="pattern-item">
            <div class="pattern-icon">
              <font-awesome-icon icon="clock" />
            </div>
            <div class="pattern-info">
              <div class="pattern-title">Horários Produtivos</div>
              <div class="pattern-badges">
                <span 
                  v-for="(hour, index) in studyPatterns.preferredHours" 
                  :key="index" 
                  class="hour-badge"
                >
                  {{ formatHour(hour.hour) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="consistency-section">
          <div class="consistency-header">
            <h4>Índice de Consistência</h4>
            <div class="consistency-info">
              <font-awesome-icon icon="info-circle" />
              <div class="info-tooltip">
                Calculado com base na regularidade dos estudos, aderência ao cronograma e distribuição por assunto.
              </div>
            </div>
          </div>
          
          <div class="consistency-indicator">
            <div class="circle-progress">
              <svg width="100" height="100" viewBox="0 0 100 100">
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="#e6e6e6"
                  stroke-width="10"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  :stroke="getConsistencyColor(studyPatterns.consistency)"
                  stroke-width="10"
                  stroke-dasharray="283"
                  :stroke-dashoffset="283 - (283 * studyPatterns.consistency) / 100"
                  transform="rotate(-90 50 50)"
                />
              </svg>
              <div class="progress-value">{{ studyPatterns.consistency }}%</div>
            </div>
            <div class="consistency-description">
              <p v-if="studyPatterns.consistency >= 80">
                Excelente consistência! Você mantém um ritmo regular de estudos.
              </p>
              <p v-else-if="studyPatterns.consistency >= 60">
                Boa consistência. Tente manter um cronograma ainda mais regular.
              </p>
              <p v-else>
                Consistência abaixo do ideal. Recomendamos estabelecer uma rotina mais regular.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Distribution Efficiency -->
      <div class="metrics-card distribution">
        <h3>Distribuição de Esforço</h3>
        
        <div class="distribution-chart">
          <div class="chart-container">
            <canvas ref="distributionCanvas" height="220"></canvas>
          </div>
          
          <div class="distribution-legend">
            <div 
              v-for="(item, index) in effortDistribution" 
              :key="index"
              class="legend-item"
            >
              <div class="color-indicator" :style="{ backgroundColor: item.color }"></div>
              <div class="item-label">{{ item.subject }}</div>
              <div class="item-value">{{ item.hours }}h ({{ item.percentage }}%)</div>
            </div>
          </div>
        </div>
        
        <div class="distribution-insights">
          <h4>Insights e Recomendações</h4>
          <ul>
            <li v-if="getTopSubject">
              <strong>{{ getTopSubject.subject }}</strong> recebe a maior parte da sua atenção ({{ getTopSubject.percentage }}%).
              {{ getTopSubject.percentage > 40 ? 'Considere diversificar mais seus estudos.' : 'Boa distribuição de tempo.' }}
            </li>
            <li v-if="getLeastAttentionSubject">
              <strong>{{ getLeastAttentionSubject.subject }}</strong> está recebendo pouca atenção ({{ getLeastAttentionSubject.percentage }}%).
              {{ getLeastAttentionSubject.percentage < 10 ? 'Dedique mais tempo a este assunto para um aprendizado equilibrado.' : '' }}
            </li>
            <li>
              {{ getDistributionRecommendation }}
            </li>
          </ul>
        </div>
      </div>
    </div>
    
    <div class="action-buttons">
      <button class="action-btn primary" @click="navigateToCalendar">Ver Calendário</button>
      <button class="action-btn secondary" @click="generateReport">Gerar Relatório</button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import Chart from 'chart.js/auto';

export default {
  name: 'RevisionDashboard',
  
  setup() {
    const store = useStore();
    const router = useRouter();
    const distributionCanvas = ref(null);
    const selectedPeriod = ref('week');
    
    // Define available periods
    const periods = [
      { label: 'Semana', value: 'week' },
      { label: 'Mês', value: 'month' },
      { label: 'Trimestre', value: 'quarter' }
    ];
    
    // Days of the week
    const weekdays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    
    // Metrics data
    const metrics = ref({
      averagePerformance: 83,
      performanceTrend: 5.2,
      adherenceScore: 76,
      retentionRate: 85,
      subjectStrengths: [],
      weaknesses: []
    });
    
    // Study patterns data
    const studyPatterns = ref({
      preferredDays: [1, 3, 5], // Monday, Wednesday, Friday
      preferredHours: [
        { hour: 9, frequency: 12 },
        { hour: 15, frequency: 8 },
        { hour: 20, frequency: 10 }
      ],
      consistency: 78
    });
    
    // Effort distribution
    const effortDistribution = ref([
      { subject: 'Anatomia', hours: 28, percentage: 35, color: '#4CAF50' },
      { subject: 'Fisiologia', hours: 22, percentage: 27, color: '#2196F3' },
      { subject: 'Patologia', hours: 15, percentage: 19, color: '#9C27B0' },
      { subject: 'Farmacologia', hours: 10, percentage: 12, color: '#FF9800' },
      { subject: 'Neurologia', hours: 5, percentage: 7, color: '#f44336' }
    ]);
    
    // Get top and least attention subjects
    const getTopSubject = computed(() => {
      if (!effortDistribution.value.length) return null;
      return [...effortDistribution.value].sort((a, b) => b.percentage - a.percentage)[0];
    });
    
    const getLeastAttentionSubject = computed(() => {
      if (!effortDistribution.value.length) return null;
      return [...effortDistribution.value].sort((a, b) => a.percentage - b.percentage)[0];
    });
    
    const getDistributionRecommendation = computed(() => {
      if (!effortDistribution.value.length) return '';
      
      const max = getTopSubject.value.percentage;
      const min = getLeastAttentionSubject.value.percentage;
      
      if (max - min > 30) {
        return 'Sua distribuição de estudos está muito desbalanceada. Tente equalizar mais o tempo entre os assuntos.';
      } else if (max - min > 20) {
        return 'Sua distribuição é razoável, mas poderia ser mais equilibrada para um aprendizado mais completo.';
      } else {
        return 'Sua distribuição de tempo entre os assuntos está bem equilibrada. Continue assim!';
      }
    });
    
    // Format hour
    function formatHour(hour) {
      return `${hour}:00`;
    }
    
    // Get color for consistency score
    function getConsistencyColor(score) {
      if (score >= 80) return '#4CAF50'; // Green
      if (score >= 60) return '#FF9800'; // Orange
      return '#f44336'; // Red
    }
    
    // Navigation
    function navigateToCalendar() {
      router.push('/calendar');
    }
    
    // Generate report
    function generateReport() {
      // This would generate a detailed performance report
      alert('Relatório gerado com sucesso! Verifique sua área de downloads.');
    }
    
    // Render distribution chart
    function renderDistributionChart() {
      if (!distributionCanvas.value) return;
      
      const ctx = distributionCanvas.value.getContext('2d');
      
      new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: effortDistribution.value.map(item => item.subject),
          datasets: [{
            data: effortDistribution.value.map(item => item.percentage),
            backgroundColor: effortDistribution.value.map(item => item.color),
            borderWidth: 0,
            hoverOffset: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const item = effortDistribution.value[context.dataIndex];
                  return `${item.subject}: ${item.percentage}% (${item.hours}h)`;
                }
              }
            }
          },
          cutout: '70%'
        }
      });
    }
    
    // Calculate bar height for weekly charts
    function calculateBarHeight(hours) {
      const maxHours = 5; // Assuming 5 hours is the max
      return Math.min(hours / maxHours * 100, 100);
    }
    
    // Calculate hours by day for weekly view
    function calculateHoursByDay() {
      return [
        { day: 'Dom', hours: 1.2 },
        { day: 'Seg', hours: 3.8 },
        { day: 'Ter', hours: 2.5 },
        { day: 'Qua', hours: 4.2 },
        { day: 'Qui', hours: 2.0 },
        { day: 'Sex', hours: 3.5 },
        { day: 'Sáb', hours: 1.8 }
      ];
    }
    
    // Fetch data from store
    async function fetchData() {
      try {
        // In a real application, these would be fetched from the Vuex store
        await store.dispatch('learningMetrics/calculateMetrics');
        await store.dispatch('learningMetrics/analyzeStudyPatterns');
        
        // Set data from store
        // metrics.value = store.getters['learningMetrics/studyMetrics'];
        // studyPatterns.value = store.getters['learningMetrics/studyPatterns'];
        // effortDistribution.value = store.getters['learningMetrics/effortDistribution'];
        
        // Render chart with updated data
        renderDistributionChart();
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      }
    }
    
    // Lifecycle hooks
    onMounted(() => {
      fetchData();
      renderDistributionChart();
    });
    
    return {
      distributionCanvas,
      selectedPeriod,
      periods,
      weekdays,
      metrics,
      studyPatterns,
      effortDistribution,
      getTopSubject,
      getLeastAttentionSubject,
      getDistributionRecommendation,
      formatHour,
      getConsistencyColor,
      navigateToCalendar,
      generateReport,
      calculateBarHeight
    };
  }
};
</script>

<style scoped>
.revision-dashboard {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h2 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: var(--title-color);
}

.subtitle {
  color: var(--text-secondary);
  margin: 0 0 1.5rem 0;
}

.period-selector {
  display: flex;
  gap: 0.75rem;
}

.period-btn {
  padding: 0.5rem 1.25rem;
  border: none;
  border-radius: 8px;
  background-color: var(--bg-lighter);
  color: var(--text-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.period-btn.active {
  background-color: var(--primary-color);
  color: white;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metrics-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.metrics-card h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 1.25rem 0;
  color: var(--title-color);
}

/* Performance metrics */
.performance-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1.5rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--title-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.stat-trend {
  font-size: 0.85rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-trend.positive {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.stat-trend.negative {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.performance-insights {
  background-color: var(--bg-lighter);
  border-radius: 8px;
  padding: 1rem;
}

.performance-insights p {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  color: var(--text-color);
}

.insight-icon {
  font-size: 1.1rem;
}

.insight-icon.positive {
  color: #4CAF50;
}

.insight-icon.neutral {
  color: #FF9800;
}

.insight-icon.negative {
  color: #f44336;
}

/* Study patterns */
.patterns-content {
  margin-bottom: 1.5rem;
}

.pattern-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.pattern-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background-color: var(--bg-lighter);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.pattern-info {
  flex: 1;
}

.pattern-title {
  font-size: 0.95rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  color: var(--text-color);
}

.pattern-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.day-badge, .hour-badge {
  background-color: var(--bg-lighter);
  color: var(--text-color);
  border-radius: 6px;
  padding: 0.3rem 0.6rem;
  font-size: 0.85rem;
}

.consistency-section {
  background-color: var(--bg-lighter);
  border-radius: 10px;
  padding: 1.25rem;
}

.consistency-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.25rem;
}

.consistency-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--title-color);
}

.consistency-info {
  position: relative;
  cursor: help;
  color: var(--text-secondary);
}

.info-tooltip {
  position: absolute;
  bottom: 100%;
  right: 0;
  width: 250px;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 0.85rem;
  z-index: 10;
  visibility: hidden;
  opacity: 0;
  transition: all 0.2s ease;
}

.consistency-info:hover .info-tooltip {
  visibility: visible;
  opacity: 1;
}

.consistency-indicator {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.circle-progress {
  position: relative;
  width: 100px;
  height: 100px;
}

.progress-value {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--title-color);
}

.consistency-description {
  flex: 1;
}

.consistency-description p {
  margin: 0;
  font-size: 0.95rem;
  color: var(--text-color);
}

/* Distribution chart */
.distribution-chart {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.chart-container {
  height: 220px;
}

.distribution-legend {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.item-label {
  flex: 1;
  color: var(--text-color);
}

.item-value {
  color: var(--text-secondary);
}

.distribution-insights {
  background-color: var(--bg-lighter);
  border-radius: 8px;
  padding: 1.25rem;
}

.distribution-insights h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--title-color);
}

.distribution-insights ul {
  margin: 0;
  padding-left: 1.25rem;
}

.distribution-insights li {
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  color: var(--text-color);
}

.distribution-insights li:last-child {
  margin-bottom: 0;
}

/* Action buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

.action-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background-color: var(--primary-color);
  color: white;
}

.action-btn.secondary {
  background-color: var(--bg-lighter);
  color: var(--text-color);
}

/* Responsive styles */
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .performance-stats {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .stat-item {
    width: 100%;
  }
  
  .consistency-indicator {
    flex-direction: column;
    gap: 1rem;
  }
  
  .consistency-description {
    text-align: center;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .action-btn {
    width: 100%;
  }
}
</style> 