<template>
  <div class="modal-overlay" @click.self="handleClose">
    <div class="revision-scheduler-modal">
      <!-- Modal Header -->
      <div class="modal-header-ultra">
        <div class="header-content">
          <div class="logo-section">
            <div class="neural-logo">
              <i class="fas fa-brain"></i>
              <div class="neural-pulse"></div>
            </div>
            <h2 class="modal-title">Sistema de Revisões Espaçadas</h2>
          </div>
          <button @click="handleClose" class="close-button-ultra">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- Tab Navigation -->
      <div class="tab-navigation">
        <button 
          :class="['tab-button', { active: activeTab === 'teorico' }]"
          @click="activeTab = 'teorico'"
        >
          <i class="fas fa-book"></i>
          <span>Estudo Teórico</span>
        </button>
        <button 
          :class="['tab-button', { active: activeTab === 'pratico' }]"
          @click="activeTab = 'pratico'"
          :disabled="!hasEstudosTeoricos"
        >
          <i class="fas fa-tasks"></i>
          <span>Revisão Prática</span>
        </button>
        <div class="tab-indicator" :class="activeTab"></div>
      </div>

      <!-- Content -->
      <div class="modal-content-ultra">
        <!-- Estudo Teórico Tab -->
        <transition name="tab-transition">
          <div v-if="activeTab === 'teorico'" class="tab-content">
            <form @submit.prevent="registrarEstudoTeorico" class="neural-form">
              <div class="form-section">
                <h3 class="section-title">
                  <i class="fas fa-bookmark"></i>
                  Registre seu primeiro contato com o conteúdo
                </h3>

                <div class="form-field floating">
                  <input 
                    v-model="estudoTeorico.materia" 
                    type="text" 
                    required
                    class="neural-input"
                    id="materia"
                    placeholder=" "
                  />
                  <label for="materia" class="floating-label">
                    Matéria / Tópico
                  </label>
                  <div class="field-glow"></div>
                </div>

                <div class="form-field floating">
                  <input 
                    v-model="estudoTeorico.data" 
                    type="date" 
                    required
                    class="neural-input"
                    id="data"
                  />
                  <label for="data" class="floating-label">
                    Data do Estudo
                  </label>
                </div>

                <div class="form-field">
                  <label class="field-label">
                    <i class="fas fa-graduation-cap"></i>
                    Disciplina
                  </label>
                  <select v-model="estudoTeorico.disciplina" class="neural-select">
                    <option value="">Selecione uma disciplina</option>
                    <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                      {{ subject.name }}
                    </option>
                  </select>
                </div>

                <div class="form-field">
                  <label class="field-label">
                    <i class="fas fa-signal"></i>
                    Grau de Dificuldade
                  </label>
                  <div class="difficulty-selector">
                    <button 
                      type="button"
                      v-for="level in dificuldades"
                      :key="level.value"
                      :class="['diff-option', { active: estudoTeorico.dificuldade === level.value }]"
                      @click="estudoTeorico.dificuldade = level.value"
                    >
                      <i :class="level.icon"></i>
                      <span>{{ level.label }}</span>
                      <small>{{ level.description }}</small>
                    </button>
                  </div>
                </div>

                <div v-if="estudoTeorico.dificuldade" class="prediction-display glass-inner">
                  <div class="preview-card">
                    <i class="fas fa-calendar-check preview-icon"></i>
                    <div>
                      <strong>Primeira Revisão Agendada:</strong>
                      <p>{{ calcularPrimeiroContato() }}</p>
                      <small class="revision-info">
                        <i class="fas fa-info-circle"></i>
                        Seguindo o algoritmo de repetição espaçada
                      </small>
                    </div>
                  </div>
                </div>

                <div class="form-actions">
                  <button type="button" @click="handleClose" class="cancel-btn-ultra">
                    <i class="fas fa-times"></i>
                    Cancelar
                  </button>
                  <button type="submit" class="submit-btn-ultra" :disabled="!isFormValid">
                    <span class="btn-text">
                      <i class="fas fa-plus-circle"></i>
                      Registrar e Agendar
                    </span>
                    <div class="btn-glow"></div>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </transition>

        <!-- Revisão Prática Tab -->
        <transition name="tab-transition">
          <div v-if="activeTab === 'pratico'" class="tab-content">
            <form @submit.prevent="registrarRevisaoPratica" class="neural-form">
              <div class="form-section">
                <h3 class="section-title">
                  <i class="fas fa-chart-line"></i>
                  Registre seu desempenho em questões
                </h3>

                <div class="form-field">
                  <label class="field-label">
                    <i class="fas fa-bookmark"></i>
                    Matéria / Tópico
                  </label>
                  <select v-model="revisaoPratica.materiaId" required class="neural-select">
                    <option value="">Selecione a matéria</option>
                    <option 
                      v-for="estudo in estudosTeoricosPendentes" 
                      :key="estudo.id" 
                      :value="estudo.id"
                    >
                      {{ estudo.materia }} - {{ formatDate(estudo.primeiroContato) }}
                    </option>
                  </select>
                </div>

                <div class="form-row">
                  <div class="form-field">
                    <label class="field-label">
                      <i class="fas fa-clipboard-list"></i>
                      Total de Questões
                    </label>
                    <div class="number-input-fancy">
                      <button 
                        @click="revisaoPratica.totalQuestoes = Math.max(1, revisaoPratica.totalQuestoes - 1)" 
                        type="button"
                        class="number-btn"
                      >
                        <i class="fas fa-minus"></i>
                      </button>
                      <input 
                        v-model.number="revisaoPratica.totalQuestoes" 
                        type="number" 
                        min="1"
                        max="100"
                        class="number-display"
                      />
                      <button 
                        @click="revisaoPratica.totalQuestoes = Math.min(100, revisaoPratica.totalQuestoes + 1)" 
                        type="button"
                        class="number-btn"
                      >
                        <i class="fas fa-plus"></i>
                      </button>
                    </div>
                  </div>

                  <div class="form-field">
                    <label class="field-label">
                      <i class="fas fa-check-circle"></i>
                      Questões Corretas
                    </label>
                    <input 
                      v-model.number="revisaoPratica.acertos" 
                      type="number" 
                      min="0"
                      :max="revisaoPratica.totalQuestoes"
                      required
                      class="neural-input"
                    />
                  </div>
                </div>

                <div v-if="percentualCalculado > 0" class="performance-display">
                  <div class="circular-progress">
                    <svg viewBox="0 0 200 200">
                      <circle cx="100" cy="100" r="90" class="progress-bg"/>
                      <circle 
                        cx="100" cy="100" r="90" 
                        class="progress-fill"
                        :stroke-dasharray="`${percentualCalculado * 5.65} 565`"
                        :class="getPerformanceClass()"
                      />
                    </svg>
                    <div class="progress-center">
                      <span class="percentage-large">{{ percentualCalculado }}%</span>
                      <span class="percentage-label">Desempenho</span>
                    </div>
                  </div>
                  
                  <div class="next-revision-preview glass-inner">
                    <i class="fas fa-clock"></i>
                    <div>
                      <strong>Próxima Revisão:</strong>
                      <p>{{ proximaRevisaoTexto }}</p>
                    </div>
                  </div>
                </div>

                <div class="form-actions">
                  <button type="button" @click="handleClose" class="cancel-btn-ultra">
                    <i class="fas fa-times"></i>
                    Cancelar
                  </button>
                  <button 
                    type="submit" 
                    class="submit-btn-ultra"
                    :disabled="!percentualCalculado"
                  >
                    <span class="btn-text">
                      <i class="fas fa-chart-line"></i>
                      Registrar Desempenho
                    </span>
                    <div class="btn-glow"></div>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RevisionSchedulerModal',
  props: {
    subjects: {
      type: Array,
      default: () => []
    },
    initialDate: {
      type: Date,
      default: () => new Date()
    }
  },
  data() {
    return {
      activeTab: 'teorico',
      
      estudoTeorico: {
        materia: '',
        data: this.formatDateForInput(this.initialDate),
        dificuldade: '',
        disciplina: ''
      },
      
      revisaoPratica: {
        materiaId: '',
        totalQuestoes: 20,
        acertos: 0
      },
      
      dificuldades: [
        {
          value: 'Fácil',
          label: 'Fácil',
          icon: 'fas fa-smile',
          description: 'Primeira revisão em 2 dias'
        },
        {
          value: 'Difícil',
          label: 'Difícil',
          icon: 'fas fa-frown',
          description: 'Primeira revisão em 1 dia'
        }
      ],
      
      // Simula estudos teóricos pendentes (em produção viria do backend)
      estudosTeoricosPendentes: []
    };
  },
  
  computed: {
    isFormValid() {
      return this.estudoTeorico.materia && 
             this.estudoTeorico.data && 
             this.estudoTeorico.dificuldade;
    },
    
    hasEstudosTeoricos() {
      return this.estudosTeoricosPendentes.length > 0;
    },
    
    percentualCalculado() {
      if (this.revisaoPratica.totalQuestoes === 0) return 0;
      return Math.round((this.revisaoPratica.acertos / this.revisaoPratica.totalQuestoes) * 100);
    },
    
    proximaRevisaoTexto() {
      const intervalos = this.calcularIntervaloRevisao();
      return `Em ${intervalos} dias (${this.calcularDataProximaRevisao()})`;
    }
  },
  
  methods: {
    formatDateForInput(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('pt-BR');
    },
    
    calcularPrimeiroContato() {
      const dataEstudo = new Date(this.estudoTeorico.data);
      const dias = this.estudoTeorico.dificuldade === 'Fácil' ? 2 : 1;
      dataEstudo.setDate(dataEstudo.getDate() + dias);
      return dataEstudo.toLocaleDateString('pt-BR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    },
    
    calcularIntervaloRevisao() {
      const desempenho = this.percentualCalculado / 100;
      
      if (desempenho < 0.5) return 1;
      if (desempenho < 0.7) return 3;
      if (desempenho < 0.8) return 7;
      if (desempenho < 0.9) return 14;
      return 21;
    },
    
    calcularDataProximaRevisao() {
      const hoje = new Date();
      const dias = this.calcularIntervaloRevisao();
      hoje.setDate(hoje.getDate() + dias);
      return hoje.toLocaleDateString('pt-BR');
    },
    
    getPerformanceClass() {
      const perc = this.percentualCalculado;
      if (perc >= 80) return 'performance-excellent';
      if (perc >= 60) return 'performance-good';
      if (perc >= 40) return 'performance-regular';
      return 'performance-poor';
    },
    
    async registrarEstudoTeorico() {
      const dataEstudo = new Date(this.estudoTeorico.data);
      const diasAteRevisao = this.estudoTeorico.dificuldade === 'Fácil' ? 2 : 1;
      const dataRevisao = new Date(dataEstudo);
      dataRevisao.setDate(dataRevisao.getDate() + diasAteRevisao);
      
      // Criar evento para o calendário
      const evento = {
        id: Date.now().toString(),
        title: `Revisão: ${this.estudoTeorico.materia}`,
        type: 'Revisão',
        priority: this.estudoTeorico.dificuldade === 'Difícil' ? 'Alta' : 'Média',
        start: `${this.formatDateForInput(dataRevisao)}T08:00:00`,
        end: `${this.formatDateForInput(dataRevisao)}T09:00:00`,
        subject: this.estudoTeorico.disciplina,
        description: `Primeira revisão do estudo teórico realizado em ${this.formatDate(this.estudoTeorico.data)}`,
        isRevision: true,
        difficulty: this.estudoTeorico.dificuldade,
        estudoTeoricoId: Date.now() // ID temporário do estudo teórico
      };
      
      // Adicionar aos estudos pendentes (simulado)
      this.estudosTeoricosPendentes.push({
        id: evento.estudoTeoricoId,
        materia: this.estudoTeorico.materia,
        primeiroContato: dataRevisao.toISOString()
      });
      
      this.$emit('save', evento);
      this.resetForm();
    },
    
    async registrarRevisaoPratica() {
      const estudo = this.estudosTeoricosPendentes.find(e => e.id === this.revisaoPratica.materiaId);
      if (!estudo) return;
      
      const hoje = new Date();
      const diasProximaRevisao = this.calcularIntervaloRevisao();
      const dataProximaRevisao = new Date();
      dataProximaRevisao.setDate(hoje.getDate() + diasProximaRevisao);
      
      // Criar evento para próxima revisão
      const evento = {
        id: Date.now().toString(),
        title: `Revisão: ${estudo.materia} (${this.percentualCalculado}%)`,
        type: 'Revisão',
        priority: this.percentualCalculado < 60 ? 'Alta' : this.percentualCalculado < 80 ? 'Média' : 'Baixa',
        start: `${this.formatDateForInput(dataProximaRevisao)}T08:00:00`,
        end: `${this.formatDateForInput(dataProximaRevisao)}T09:00:00`,
        description: `Revisão após desempenho de ${this.percentualCalculado}% (${this.revisaoPratica.acertos}/${this.revisaoPratica.totalQuestoes} questões)`,
        isRevision: true,
        performance: this.percentualCalculado,
        estudoTeoricoId: estudo.id
      };
      
      this.$emit('save', evento);
      this.resetForm();
    },
    
    resetForm() {
      this.estudoTeorico = {
        materia: '',
        data: this.formatDateForInput(this.initialDate),
        dificuldade: '',
        disciplina: ''
      };
      this.revisaoPratica = {
        materiaId: '',
        totalQuestoes: 20,
        acertos: 0
      };
    },
    
    handleClose() {
      this.$emit('close');
    }
  }
};
</script>

<style scoped>
/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 2rem;
  overflow-y: auto;
}

/* Modal Container */
.revision-scheduler-modal {
  width: 100%;
  max-width: 800px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.98) 100%);
  border-radius: 24px;
  box-shadow: 
    0 24px 48px rgba(0, 0, 0, 0.4),
    0 0 80px rgba(99, 102, 241, 0.1),
    inset 0 0 120px rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.2);
  overflow: hidden;
  position: relative;
}

/* Modal Header */
.modal-header-ultra {
  background: rgba(30, 41, 59, 0.6);
  border-bottom: 1px solid rgba(99, 102, 241, 0.2);
  padding: 1.5rem 2rem;
  backdrop-filter: blur(12px);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.neural-logo {
  position: relative;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.neural-logo i {
  font-size: 24px;
  color: white;
}

.neural-pulse {
  position: absolute;
  inset: -8px;
  border-radius: 16px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.4) 0%, transparent 70%);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0; }
  50% { transform: scale(1.2); opacity: 1; }
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #e4e6eb 0%, #94a3b8 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.close-button-ultra {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  border: none;
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  font-size: 1.2rem;
}

.close-button-ultra:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: rgba(30, 41, 59, 0.4);
  padding: 0.5rem;
  position: relative;
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
}

.tab-button {
  flex: 1;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s;
  position: relative;
  z-index: 1;
}

.tab-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tab-button.active {
  color: #e4e6eb;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 100%;
  width: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-radius: 8px;
  transition: all 0.3s ease;
  z-index: 0;
}

.tab-indicator.teorico {
  left: 0;
}

.tab-indicator.pratico {
  left: 50%;
}

/* Content */
.modal-content-ultra {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Form Styles */
.neural-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section-title {
  font-size: 1.1rem;
  color: #e4e6eb;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 0.5rem 0;
}

.section-title i {
  color: #6366f1;
}

/* Form Fields */
.form-field {
  position: relative;
}

.form-field.floating {
  margin-top: 1rem;
}

.neural-input,
.neural-select {
  width: 100%;
  padding: 1rem 1.25rem;
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  color: #e4e6eb;
  font-size: 0.95rem;
  transition: all 0.3s;
  outline: none;
}

.neural-input:focus,
.neural-select:focus {
  border-color: #6366f1;
  background: rgba(30, 41, 59, 0.8);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.1);
}

.floating-label {
  position: absolute;
  left: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 0.95rem;
  font-weight: 500;
  pointer-events: none;
  transition: all 0.3s;
  background: transparent;
  padding: 0 0.5rem;
}

.neural-input:focus ~ .floating-label,
.neural-input:not(:placeholder-shown) ~ .floating-label {
  top: -0.5rem;
  font-size: 0.8rem;
  color: #6366f1;
  background: linear-gradient(to bottom, transparent 0%, transparent 40%, rgba(15, 23, 42, 0.98) 40%, rgba(15, 23, 42, 0.98) 60%, transparent 60%, transparent 100%);
}

.field-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.field-label i {
  color: #6366f1;
}

/* Difficulty Selector */
.difficulty-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.diff-option {
  padding: 1.25rem;
  background: rgba(30, 41, 59, 0.6);
  border: 2px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.diff-option:hover {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
}

.diff-option.active {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.2);
}

.diff-option i {
  font-size: 1.5rem;
  color: #6366f1;
}

.diff-option span {
  font-weight: 600;
  color: #e4e6eb;
}

.diff-option small {
  font-size: 0.75rem;
  color: #94a3b8;
}

/* Prediction Display */
.prediction-display {
  margin-top: 1rem;
}

.glass-inner {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  padding: 1.25rem;
  backdrop-filter: blur(8px);
}

.preview-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.preview-icon {
  font-size: 1.5rem;
  color: #6366f1;
}

.preview-card strong {
  color: #e4e6eb;
  display: block;
  margin-bottom: 0.5rem;
}

.preview-card p {
  color: #94a3b8;
  margin: 0;
}

.revision-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

/* Form Row */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Number Input */
.number-input-fancy {
  display: flex;
  align-items: center;
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  overflow: hidden;
}

.number-btn {
  width: 48px;
  height: 48px;
  background: none;
  border: none;
  color: #6366f1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.number-btn:hover {
  background: rgba(99, 102, 241, 0.1);
}

.number-display {
  flex: 1;
  text-align: center;
  background: none;
  border: none;
  color: #e4e6eb;
  font-size: 1.1rem;
  font-weight: 600;
  outline: none;
}

/* Performance Display */
.performance-display {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.circular-progress {
  position: relative;
  width: 200px;
  height: 200px;
}

.circular-progress svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-bg {
  fill: none;
  stroke: rgba(99, 102, 241, 0.1);
  stroke-width: 12;
}

.progress-fill {
  fill: none;
  stroke: #6366f1;
  stroke-width: 12;
  stroke-linecap: round;
  transition: all 0.5s ease;
}

.progress-fill.performance-excellent {
  stroke: #10b981;
}

.progress-fill.performance-good {
  stroke: #3b82f6;
}

.progress-fill.performance-regular {
  stroke: #f59e0b;
}

.progress-fill.performance-poor {
  stroke: #ef4444;
}

.progress-center {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.percentage-large {
  font-size: 2.5rem;
  font-weight: 700;
  color: #e4e6eb;
}

.percentage-label {
  font-size: 0.9rem;
  color: #94a3b8;
}

.next-revision-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  height: fit-content;
}

.next-revision-preview i {
  font-size: 1.5rem;
  color: #6366f1;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.cancel-btn-ultra,
.submit-btn-ultra {
  padding: 0.875rem 2rem;
  border-radius: 12px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.cancel-btn-ultra {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.cancel-btn-ultra:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-2px);
}

.submit-btn-ultra {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.submit-btn-ultra:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(99, 102, 241, 0.4);
}

.submit-btn-ultra:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-text {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-glow {
  position: absolute;
  inset: -50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s;
}

.submit-btn-ultra:hover .btn-glow {
  opacity: 1;
}

/* Scrollbar */
.modal-content-ultra::-webkit-scrollbar {
  width: 8px;
}

.modal-content-ultra::-webkit-scrollbar-track {
  background: rgba(99, 102, 241, 0.1);
  border-radius: 4px;
}

.modal-content-ultra::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.3);
  border-radius: 4px;
}

.modal-content-ultra::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.5);
}

/* Responsive */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 1rem;
  }
  
  .revision-scheduler-modal {
    max-width: 100%;
  }
  
  .modal-content-ultra {
    padding: 1.5rem;
  }
  
  .form-row,
  .difficulty-selector,
  .performance-display {
    grid-template-columns: 1fr;
  }
  
  .tab-button {
    font-size: 0.85rem;
    padding: 0.875rem 1rem;
  }
  
  .tab-button span {
    display: none;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .cancel-btn-ultra,
  .submit-btn-ultra {
    width: 100%;
    justify-content: center;
  }
}
</style>