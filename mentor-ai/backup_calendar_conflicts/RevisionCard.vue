<template>
  <div class="revision-card">
    <h3>{{ revision.title }}</h3>
    <p>{{ revision.subject }}</p>
    <p>{{ formatDate(revision.date) }}</p>
    <div class="progress-bar">
      <div class="progress" :style="{ width: revision.progress + '%' }"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RevisionCard',
  props: {
    revision: Object
  },
  methods: {
    formatDate(date) {
      return new Date(date).toLocaleDateString('pt-BR');
    }
  }
}
</script>

<style scoped>
.revision-card {
  background-color: var(--background-light);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 1rem;
  transition: transform 0.3s ease;
}

.revision-card:hover {
  transform: translateY(-5px);
}

.progress-bar {
  background-color: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
  height: 10px;
  margin-top: 0.5rem;
}

.progress {
  background-color: var(--primary-color);
  height: 100%;
  transition: width 0.3s ease;
}
</style> 