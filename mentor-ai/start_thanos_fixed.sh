#!/bin/bash

# THANOS FIXED STARTUP SCRIPT
# Script otimizado para iniciar o Thanos com melhor tratamento de erros

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Diretório base
BASEDIR="/Users/<USER>/Projetos/meds-AI-Maketing_Site/mentor-ai"
cd "$BASEDIR"

# Função para verificar dependências
check_dependencies() {
    echo -e "${BLUE}Checking dependencies...${NC}"
    
    # Verificar Python
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3 not found! Please install Python 3.8+${NC}"
        exit 1
    fi
    
    # Verificar Node/NPM
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ NPM not found! Please install Node.js${NC}"
        exit 1
    fi
    
    # Verificar PostgreSQL
    if ! pg_isready -h localhost -p 5432 &> /dev/null; then
        echo -e "${YELLOW}⚠️ PostgreSQL not running. Attempting to start...${NC}"
        if command -v brew &> /dev/null; then
            brew services start postgresql &> /dev/null || true
        else
            sudo service postgresql start &> /dev/null || true
        fi
        sleep 2
        if ! pg_isready -h localhost -p 5432 &> /dev/null; then
            echo -e "${YELLOW}⚠️ PostgreSQL could not be started automatically. Will use SQLite fallback.${NC}"
        else
            echo -e "${GREEN}✅ PostgreSQL started${NC}"
        fi
    else
        echo -e "${GREEN}✅ PostgreSQL running${NC}"
    fi
    
    echo -e "${GREEN}✅ All system dependencies checked${NC}"
}

# Função para parar serviços existentes
stop_services() {
    echo -e "${YELLOW}Stopping existing services...${NC}"
    
    if [ -f "stop_ultra.sh" ]; then
        ./stop_ultra.sh &> /dev/null || true
    else
        pkill -f "python.*fastapi" &> /dev/null || true
        pkill -f "python.*uvicorn" &> /dev/null || true
        pkill -f "npm.*serve" &> /dev/null || true
    fi
    
    sleep 2
    echo -e "${GREEN}✅ Services stopped${NC}"
}

# Função para iniciar o backend
start_backend() {
    echo -e "${BLUE}Starting backend...${NC}"
    
    # Verificar virtual env
    if [ ! -d "backend/venv" ]; then
        echo -e "${YELLOW}Creating virtual environment...${NC}"
        cd backend
        python3 -m venv venv
        cd ..
    fi
    
    # Ativar venv
    source backend/venv/bin/activate
    
    # Instalar dependências essenciais
    echo -e "${BLUE}Installing backend dependencies...${NC}"
    pip install fastapi uvicorn sqlalchemy asyncpg anthropic httpx requests &> /dev/null
    
    # Verificar/criar .env
    if [ ! -f "backend/.env" ]; then
        echo -e "${YELLOW}Creating .env file...${NC}"
        cat > backend/.env << EOF
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/mentor_ai_db
ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-dummy_key}
OPENAI_API_KEY=${OPENAI_API_KEY:-dummy_key}
PORT=8001
EOF
    fi
    
    # Iniciar backend
    echo -e "${GREEN}Starting FastAPI with Thanos...${NC}"
    cd backend
    if [ -f "thanos_integrated_main.py" ]; then
        nohup python thanos_integrated_main.py > ../logs/thanos_fastapi.log 2>&1 &
    else
        echo -e "${RED}❌ thanos_integrated_main.py not found!${NC}"
        echo -e "${YELLOW}Falling back to standard FastAPI startup...${NC}"
        nohup uvicorn fastapi_app.main:app --host 0.0.0.0 --port 8001 > ../logs/fastapi.log 2>&1 &
    fi
    FASTAPI_PID=$!
    echo $FASTAPI_PID > ../.fastapi.pid
    cd ..
    
    # Verificar se iniciou corretamente
    sleep 5
    if curl -s http://localhost:8001/health &> /dev/null; then
        echo -e "${GREEN}✅ Backend started successfully${NC}"
    else
        echo -e "${YELLOW}⚠️ Backend may not have started correctly. Check logs/thanos_fastapi.log${NC}"
    fi
}

# Função para iniciar o frontend
start_frontend() {
    echo -e "${BLUE}Starting frontend...${NC}"
    
    cd frontend
    nohup npm run serve > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../.frontend.pid
    cd ..
    
    # Verificar se iniciou corretamente
    sleep 10
    if curl -s http://localhost:8082 &> /dev/null; then
        echo -e "${GREEN}✅ Frontend started successfully${NC}"
    else
        echo -e "${YELLOW}⚠️ Frontend may not have started correctly. Check logs/frontend.log${NC}"
    fi
}

# Função para verificar a integração
test_integration() {
    echo -e "${PURPLE}Testing Thanos integration...${NC}"
    
    if [ -f "test_thanos_integration.py" ]; then
        python test_thanos_integration.py
    else
        echo -e "${YELLOW}⚠️ test_thanos_integration.py not found, skipping test${NC}"
    fi
}

# Script principal
clear
echo -e "${PURPLE}╔════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║      THANOS AI - IMPROVED STARTUP      ║${NC}"
echo -e "${PURPLE}╚════════════════════════════════════════╝${NC}"
echo ""

# Executar funções
check_dependencies
stop_services
start_backend
start_frontend
test_integration

echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}   THANOS INTEGRATION COMPLETE!${NC}"
echo -e "${GREEN}========================================${NC}"
echo ""
echo -e "${BLUE}Access points:${NC}"
echo -e "  - Frontend: ${GREEN}http://localhost:8082${NC}"
echo -e "  - Thanos UI: ${GREEN}http://localhost:8082/thanos${NC}"
echo -e "  - API Docs: ${GREEN}http://localhost:8001/docs${NC}"
echo -e "  - Thanos Health: ${GREEN}http://localhost:8001/api/thanos/health${NC}"
echo ""
echo -e "${YELLOW}To stop all services:${NC} ./stop_ultra.sh"
echo ""
echo -e "${PURPLE}Thanos is ready to process your documents!${NC}"