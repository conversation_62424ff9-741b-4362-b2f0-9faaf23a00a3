# Resources Folder Cleanup Report

## Cleanup Actions Performed

### 1. Removed System Files
- Deleted all `.DS_Store` files (macOS system files)
- These files are automatically created by macOS and serve no purpose in the repository

### 2. Removed Backup Flashcards Folder
- Deleted `/Resources/backupflshs/` directory
- This folder contained duplicate versions of flashcard components:
  - FlashcardReview.vue / FlashcardReview2.vue
  - FlashcardsPage.vue / FlashcardsPage2.vue
  - flashcards.js / flashcards2.js
  - flashcardsPage.js / flashcardsPage2.js
- The main flashcard system is fully functional in the main components directory

### 3. Removed Untitled Files
- Deleted 19 "Sem título" (untitled) markdown files
- These appeared to be placeholder files from Notion export with no meaningful content

## Files Preserved

### 1. Medical Content from Notion Export
- Kept all medical study materials in the Notion export folders
- These contain valuable medical information even if some duplication exists
- Folders preserved:
  - `/Registro ET c9a73a001bbd47e6b0ebdb7c342179d5/`
  - `/Resumos R+ 55d02a9322a74ab1a34c7dbd82555f82/`
  - `/Revisões R+ 543b3e8c2cfd494d989e89d8dcd7368d/`

### 2. Requirements Files
- Kept both requirements.txt files as they may have different dependencies:
  - `/Resources/requirements.txt`
  - `/Resources/MDPhD_Chat/requirements.txt`

### 3. All Other Educational Resources
- Preserved all PDFs, images, and other study materials
- Maintained the folder structure for easy navigation

## Space Saved
- Removed unnecessary backup files and system files
- Cleaned up duplicate components that were already implemented in the main codebase
- Maintained all educational and medical content

## Recommendations
1. Consider adding `.DS_Store` to `.gitignore` to prevent future accumulation
2. Medical content could benefit from better organization in the future
3. Consider creating a dedicated backup strategy instead of keeping backups in the Resources folder