/* CORREÇÃO CSS PARA O CALENDÁRIO - CLASSES ULTRA FALTANTES */

/* Ultra Header Styles */
.ultra-header {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
  padding: 1.5rem 2rem;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.ultra-header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.ultra-header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.ultra-page-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.ultra-page-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.ultra-page-info h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #f1f5f9, #6366f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ultra-page-info p {
  font-size: 1.125rem;
  color: #94a3b8;
  margin: 0;
  font-weight: 500;
}

/* Ultra Stats Section */
.stats-section {
  padding: 2rem 2rem 0;
  max-width: 1400px;
  margin: 0 auto;
}

/* Ultra Date Navigation */
.ultra-date-navigation {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 20px;
  padding: 1.5rem 2rem;
  margin: 2rem auto 1rem;
  max-width: 1400px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  flex-wrap: wrap;
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.ultra-nav-btn {
  width: 50px;
  height: 50px;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
}

.ultra-nav-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: #6366f1;
  color: #6366f1;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.current-period-display {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 16px;
  padding: 1rem 1.5rem;
}

.period-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
}

.period-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.period-text {
  font-size: 1.125rem;
  font-weight: 700;
  color: #f1f5f9;
}

.period-subtitle {
  font-size: 0.875rem;
  color: #94a3b8;
  font-weight: 500;
}

.nav-actions {
  display: flex;
  gap: 1rem;
}

.ultra-today-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #22c55e, #10b981);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.ultra-today-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
}

/* Responsividade para elementos Ultra */
@media (max-width: 768px) {
  .ultra-header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .ultra-header-left {
    width: 100%;
    justify-content: center;
  }
  
  .ultra-date-navigation {
    flex-direction: column;
    padding: 1rem;
  }
  
  .nav-controls {
    width: 100%;
    justify-content: center;
  }
  
  .current-period-display {
    width: 100%;
    justify-content: center;
  }
  
  .nav-actions {
    width: 100%;
    justify-content: center;
  }
}

/* Garantir visibilidade do conteúdo */
.ultra-page-container > * {
  position: relative;
  z-index: 1;
}

/* Fix para garantir que o calendário apareça */
.calendar-wrapper {
  position: relative;
  z-index: 1;
  min-height: 600px;
}

.loading-container,
.error-container,
.offline-banner {
  position: relative;
  z-index: 10;
}