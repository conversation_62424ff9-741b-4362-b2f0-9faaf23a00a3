#!/bin/bash

echo "Stopping Thanos Ultra services..."

# Stop backend
if [ -f .thanos_backend.pid ]; then
    kill $(cat .thanos_backend.pid) 2>/dev/null
    rm .thanos_backend.pid
fi

# Stop frontend
if [ -f .thanos_frontend.pid ]; then
    kill $(cat .thanos_frontend.pid) 2>/dev/null
    rm .thanos_frontend.pid
fi

# Kill any remaining processes
pkill -f "uvicorn.*thanos_integrated_main" || true
pkill -f "npm.*dev.*--port 8082" || true

echo "Thanos Ultra services stopped."
