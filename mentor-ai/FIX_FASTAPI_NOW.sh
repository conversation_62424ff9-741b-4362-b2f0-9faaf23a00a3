#!/bin/bash

echo "🔧 FIXING FASTAPI IMMEDIATELY..."

# Kill existing FastAPI processes
pkill -f "uvicorn" 2>/dev/null || true
pkill -f "python.*main.py" 2>/dev/null || true

# Create necessary directories
mkdir -p backend/logs
mkdir -p backend/fastapi_app/logs
mkdir -p logs

# Go to backend directory
cd backend

# Use the integrated main.py with AI services
if [ -f "integrated_ai_main.py" ]; then
    echo "✅ Starting FastAPI with integrated AI services..."
    nohup python integrated_ai_main.py > ../logs/fastapi_integrated.log 2>&1 &
    echo $! > ../.fastapi.pid
    echo "FastAPI PID: $!"
else
    echo "⚠️ integrated_ai_main.py not found, creating it..."
    # Create the file from our previous working version
    cp /Users/<USER>/Projetos/meds-AI-Maketing_Site/mentor-ai/backend/integrated_ai_main.py . 2>/dev/null || echo "Could not copy integrated file"
    
    # Try FastAPI default
    cd fastapi_app
    echo "Starting FastAPI with default main.py..."
    nohup python main.py > ../../logs/fastapi_default.log 2>&1 &
    echo $! > ../../.fastapi.pid
    echo "FastAPI PID: $!"
fi

sleep 3

# Test if it's working
echo -e "\n📊 Testing FastAPI..."
if curl -s http://localhost:8001/health > /dev/null; then
    echo "✅ FastAPI is running!"
    echo "📍 Access points:"
    echo "   - API Docs: http://localhost:8001/docs"
    echo "   - Health: http://localhost:8001/health"
    echo "   - Thanos: http://localhost:8001/api/thanos/health"
else
    echo "❌ FastAPI not responding. Check logs:"
    echo "   - tail -f logs/fastapi_integrated.log"
    echo "   - tail -f logs/fastapi_default.log"
fi