#!/bin/bash

# Quick health check script for MentorAI

GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== MENTOR AI HEALTH CHECK ===${NC}"
echo -e "${BLUE}$(date '+%Y-%m-%d %H:%M:%S')${NC}\n"

# Check PostgreSQL
echo -ne "PostgreSQL (5432): "
if pg_isready -h localhost > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Running${NC}"
else
    echo -e "${RED}❌ Down${NC}"
fi

# Check Django
echo -ne "Django (8003): "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8003 | grep -q "200"; then
    echo -e "${GREEN}✅ Running${NC}"
else
    echo -e "${RED}❌ Down${NC}"
fi

# Check FastAPI
echo -ne "FastAPI (8001): "
if curl -s http://localhost:8001/health | grep -q "healthy"; then
    echo -e "${GREEN}✅ Running${NC}"
else
    echo -e "${RED}❌ Down${NC}"
fi

# Check Frontend
echo -ne "Frontend (8082): "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8082 | grep -q "200"; then
    echo -e "${GREEN}✅ Running${NC}"
else
    echo -e "${RED}❌ Down${NC}"
fi

# Database stats
echo -e "\n${BLUE}Database Stats:${NC}"
python3 -c "
import sys
sys.path.append('backend')
from fastapi_app.db.database import SessionLocal
from sqlalchemy import text
db = SessionLocal()
try:
    users = db.execute(text('SELECT COUNT(*) FROM users')).scalar()
    decks = db.execute(text('SELECT COUNT(*) FROM decks')).scalar()
    cards = db.execute(text('SELECT COUNT(*) FROM flashcards')).scalar()
    print(f'  Users: {users}')
    print(f'  Decks: {decks}')
    print(f'  Flashcards: {cards}')
except Exception as e:
    print(f'  Error: {e}')
finally:
    db.close()
" 2>/dev/null || echo "  Database connection error"

# API test
echo -e "\n${BLUE}API Tests:${NC}"
DECK_COUNT=$(curl -s http://localhost:8001/api/flashcards/decks/ | jq length 2>/dev/null || echo "0")
echo -e "  Flashcards API: ${GREEN}$DECK_COUNT decks available${NC}"

# Memory and disk
echo -e "\n${BLUE}System Resources:${NC}"
echo -e "  Disk: $(df -h . | awk 'NR==2 {print $5}' | sed 's/^/Used /')"
echo -e "  Memory: $(ps aux | awk '{sum+=$6} END {printf "%.1f MB used\n", sum/1024}')"

echo -e "\n${BLUE}Quick Links:${NC}"
echo -e "  Main App: ${GREEN}http://localhost:8082${NC}"
echo -e "  Flashcards: ${GREEN}http://localhost:8082/flashcards${NC}"
echo -e "  API Docs: ${GREEN}http://localhost:8001/docs${NC}"
echo -e "  Overview: ${GREEN}http://localhost:8082/app-overview.html${NC}"