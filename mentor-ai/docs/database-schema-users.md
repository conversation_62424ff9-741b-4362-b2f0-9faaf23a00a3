# Esquema da Tabela de Usuários - Mentor AI

## Tabela: `users`

### Descrição
Tabela principal para armazenamento de dados dos usuários do sistema Mentor AI.

### Estrutura da Tabela

| Campo | Tipo | Constraints | Descrição |
|-------|------|-------------|-----------|
| `id` | String | PRIMARY KEY | UUID único do usuário (gerado automaticamente) |
| `username` | String | UNIQUE, INDEX | Nome de usuário único para login |
| `email` | String | UNIQUE, INDEX | Email único do usuário |
| `hashed_password` | String | NOT NULL | Senha criptografada do usuário |
| `first_name` | String | - | Primeiro nome do usuário |
| `last_name` | String | - | Sobrenome do usuário |
| `created_at` | DateTime | DEFAULT: UTC NOW | Data/hora de criação do registro |
| `updated_at` | DateTime | DEFAULT: UTC NOW, ON UPDATE | Data/hora da última atualização |

### Relacionamentos

#### One-to-Many
- **decks**: Um usuário pode ter múltiplos baralhos de flashcards
  - Foreign Key em `decks.user_id`
  - Cascade: Manter baralhos ao deletar usuário (configurável)

- **study_sessions**: Um usuário pode ter múltiplas sessões de estudo
  - Foreign Key em `study_sessions.user_id`
  - Cascade: Deletar sessões ao deletar usuário

### Exemplo de Modelo SQLAlchemy

```python
class User(Base):
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    first_name = Column(String)
    last_name = Column(String)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # Relationships
    decks = relationship("Deck", back_populates="user")
    study_sessions = relationship("StudySession", back_populates="user")
```

### Índices
- PRIMARY KEY em `id`
- UNIQUE INDEX em `username`
- UNIQUE INDEX em `email`

### Considerações de Segurança
- Senhas são sempre armazenadas como hash (nunca em texto plano)
- Recomenda-se usar bcrypt ou argon2 para hashing de senhas
- Implementar rate limiting para tentativas de login
- Validar formato de email antes de inserir

### Queries Comuns

```sql
-- Buscar usuário por username
SELECT * FROM users WHERE username = 'exemplo_usuario';

-- Buscar usuário por email
SELECT * FROM users WHERE email = '<EMAIL>';

-- Listar usuários criados nos últimos 30 dias
SELECT * FROM users 
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY created_at DESC;

-- Contar total de usuários
SELECT COUNT(*) FROM users;
```

### Migrações
Para criar esta tabela, use o Alembic com SQLAlchemy:

```bash
alembic revision --autogenerate -m "Create users table"
alembic upgrade head
```