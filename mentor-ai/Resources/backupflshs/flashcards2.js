export default {
  namespaced: true,
  
  state: () => ({
    flashcards: [],
    sessionCards: [],
    cardToEdit: null,
    deckName: 'Medicina - Flashcards'
  }),
  
  getters: {
    allFlashcards(state) {
      return state.flashcards
    },
    currentFlashcard(state, getters, rootState, rootGetters) {
      return state.sessionCards[0] || null
    },
    cardToEdit(state) {
      return state.cardToEdit
    }
  },
  
  mutations: {
    SET_FLASHCARDS(state, flashcards) {
      state.flashcards = flashcards
    },
    SET_SESSION_CARDS(state, cards) {
      state.sessionCards = cards
    },
    SET_CARD_TO_EDIT(state, cardId) {
      state.cardToEdit = cardId
    },
    ADD_FLASHCARD(state, card) {
      state.flashcards.push(card)
    },
    UPDATE_FLASHCARD(state, updatedCard) {
      const index = state.flashcards.findIndex(card => card.id === updatedCard.id)
      if (index !== -1) {
        state.flashcards.splice(index, 1, updatedCard)
      }
    },
    REMOVE_FLASHCARD(state, cardId) {
      state.flashcards = state.flashcards.filter(card => card.id !== cardId)
    }
  },
  
  actions: {
    loadFlashcards({ commit }) {
      // Simular carregamento de dados do backend
      const storedCards = localStorage.getItem('flashcards')
      if (storedCards) {
        commit('SET_FLASHCARDS', JSON.parse(storedCards))
      }
    },
    
    saveFlashcards({ state }) {
      localStorage.setItem('flashcards', JSON.stringify(state.flashcards))
    },
    
    addFlashcard({ commit, dispatch }, card) {
      commit('ADD_FLASHCARD', card)
      dispatch('saveFlashcards')
    },
    
    updateFlashcard({ commit, dispatch }, card) {
      commit('UPDATE_FLASHCARD', card)
      dispatch('saveFlashcards')
    },
    
    removeFlashcard({ commit, dispatch }, cardId) {
      commit('REMOVE_FLASHCARD', cardId)
      dispatch('saveFlashcards')
    }
  }
}