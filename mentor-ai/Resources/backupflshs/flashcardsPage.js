export default 
{
  namespaced: true,

  state: () => ({
    flashcards: [], // { id, question, answer, difficulty, nextReviewDate, stats: { attempts: number, correct: number } }
    currentIndex: 0,
    reviewMode: false, // se estamos em modo de revisão ou criação
    deckName: 'Meu Deck de Flashcards'
  }),

  getters: {
    allFlashcards(state) {
      return state.flashcards
    },
    currentFlashcard(state) {
      return state.flashcards[state.currentIndex] || null
    },
    flashcardsCount(state) {
      return state.flashcards.length
    },
    reviewMode(state) {
      return state.reviewMode
    },
    deckName(state) {
      return state.deckName
    }
  },

  mutations: {
    SET_FLASHCARDS(state, payload) {
      state.flashcards = payload
    },
    ADD_FLASHCARD(state, newCard) {
      state.flashcards.push(newCard)
    },
    REMOVE_FLASHCARD(state, cardId) {
      state.flashcards = state.flashcards.filter(fc => fc.id !== cardId)
    },
    UPDATE_FLASHCARD(state, updated) {
      const index = state.flashcards.findIndex(fc => fc.id === updated.id)
      if (index !== -1) {
        state.flashcards.splice(index, 1, updated)
      }
    },
    SET_CURRENT_INDEX(state, idx) {
      state.currentIndex = idx
    },
    SET_REVIEW_MODE(state, bool) {
      state.reviewMode = bool
    },
    SET_DECK_NAME(state, name) {
      state.deckName = name
    }
  },

  actions: {
    loadFlashcards({ commit }) {
      // Exemplo: buscar do localStorage (ou API)
      const stored = JSON.parse(localStorage.getItem('flashcards_deck')) || []
      commit('SET_FLASHCARDS', stored)
    },
    saveFlashcards({ state }) {
      localStorage.setItem('flashcards_deck', JSON.stringify(state.flashcards))
    },

    addFlashcard({ commit, dispatch }, newCardData) {
      const newCard = {
        id: Date.now().toString(),
        question: newCardData.question,
        answer: newCardData.answer,
        difficulty: newCardData.difficulty || 'normal',
        nextReviewDate: null,
        stats: {
          attempts: 0,
          correct: 0
        }
      }
      commit('ADD_FLASHCARD', newCard)
      dispatch('saveFlashcards')
    },

    removeFlashcard({ commit, dispatch }, cardId) {
      commit('REMOVE_FLASHCARD', cardId)
      dispatch('saveFlashcards')
    },

    updateFlashcard({ commit, dispatch }, updated) {
      commit('UPDATE_FLASHCARD', updated)
      dispatch('saveFlashcards')
    },

    setCurrentIndex({ commit }, idx) {
      commit('SET_CURRENT_INDEX', idx)
    },

    toggleReviewMode({ commit }, bool) {
      commit('SET_REVIEW_MODE', bool)
    },

    setDeckName({ commit }, name) {
      commit('SET_DECK_NAME', name)
    },

    handleAnswer({ state, commit, dispatch }, { cardId, isCorrect }) {
      // Busca o card e atualiza estatísticas
      const found = state.flashcards.find(fc => fc.id === cardId)
      if (!found) return
      found.stats.attempts++
      if (isCorrect) found.stats.correct++

      // Lógica de repetição espaçada (super simplificada):
      // Se acertar -> nextReviewDate = +2 dias
      // Se errar -> nextReviewDate = +1 dia
      const now = new Date()
      let daysToAdd = isCorrect ? 2 : 1
      if (found.difficulty === 'hard') {
        daysToAdd = isCorrect ? 1 : 0.5 // se for "hard", altera a lógica
      }

      const nextDate = new Date(now.getTime() + daysToAdd * 24 * 60 * 60 * 1000)
      found.nextReviewDate = nextDate.toISOString().split('T')[0]

      commit('UPDATE_FLASHCARD', found)
      dispatch('saveFlashcards')
    }
  }
} 