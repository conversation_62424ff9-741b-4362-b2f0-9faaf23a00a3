<template>
  <div class="flashcards-container">
    <header class="flashcards-header">
      <h1>{{ deckName }}</h1>
      <div class="header-actions">
        <button @click="toggleReviewMode" :class="{ active: reviewMode }">
          <span v-if="!reviewMode">Iniciar <PERSON><PERSON></span>
          <span v-else>Modo Criação</span>
        </button>
        <button @click="changeDeckName" class="deck-name-button">
          Editar Nome do Deck
        </button>
      </div>
    </header>

    <transition name="fade" mode="out-in">
      <section v-if="reviewMode" class="review-mode">
        <div v-if="currentFlashcard" class="card-review">
          <div class="card-content">
            <div class="question-side" v-if="!showAnswer">
              <h2 style="color: #42b983;">Pergunta</h2>
              <p>{{ currentFlashcard.question }}</p>
            </div>
            <div class="answer-side" v-else>
              <h2>Resposta</h2>
              <p>{{ currentFlashcard.answer }}</p>
            </div>
          </div>
          <div class="strategies">
            <button @click="showAnswer = !showAnswer" class="reveal-button">
              {{ showAnswer ? 'Esconder' : 'Revelar' }}
            </button>

            <div class="answer-actions" v-if="showAnswer">
              <button class="correct-btn" @click="handleCorrect">
                Acertar
              </button>
              <button class="wrong-btn" @click="handleWrong">
                Errar
              </button>
            </div>

            <div v-if="currentIndex < flashcardsCount - 1" class="nav-actions">
              <button @click="nextCard">Próximo</button>
            </div>
            <div v-else class="nav-actions">
              <p>Você chegou ao final do deck!</p>
            </div>
          </div>
        </div>
        <div v-else class="no-cards">
          <p>Nenhum flashcard adicionado ou acabou a revisão. Adicione mais!</p>
        </div>
      </section>
      <section v-else class="creation-mode">
        <h2>Criar/Editar Flashcards</h2>

        <form @submit.prevent="addNewCard" class="new-card-form">
          <div class="form-group">
            <label>Pergunta</label>
            <input
              v-model="newCard.question"
              type="text"
              required
              placeholder="Digite a pergunta..."
            />
          </div>
          <div class="form-group">
            <label>Resposta</label>
            <input
              v-model="newCard.answer"
              type="text"
              required
              placeholder="Digite a resposta..."
            />
          </div>
          <div class="form-group">
            <label>Dificuldade</label>
            <select v-model="newCard.difficulty">
              <option value="easy">Fácil</option>
              <option value="normal">Normal</option>
              <option value="hard">Difícil</option>
            </select>
          </div>
          <button type="submit" class="create-btn">Adicionar Flashcard</button>
        </form>

        {/* Lista de Flashcards */}
        <div class="card-list" v-if="allFlashcards.length">
          <h3>Seus Flashcards ({{ allFlashcards.length }})</h3>
          <table class="flashcard-table">
            <thead>
              <tr>
                <th>Pergunta</th>
                <th>Resposta</th>
                <th>Dificuldade</th>
                <th>Estatísticas</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="card in allFlashcards"
                :key="card.id"
              >
                <td>{{ card.question }}</td>
                <td>{{ card.answer }}</td>
                <td>{{ card.difficulty }}</td>
                <td>
                  <span>Tentativas: {{ card.stats.attempts }}</span> |
                  <span>Acertos: {{ card.stats.correct }}</span>
                </td>
                <td>
                  <button @click="removeCard(card.id)" class="delete-btn">
                    Remover
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>
    </transition>
  </div>
</template>

<script>
import { computed, ref, onMounted } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'FlashcardsPage2',
  setup() {
    const store = useStore()
    const newCard = ref({
      question: '',
      answer: '',
      difficulty: 'normal'
    })
    const showAnswer = ref(false) // exibe ou oculta resposta no modo de revisão

    const flashcardsCount = computed(() => store.getters['flashcards/flashcardsCount'])
    const currentIndex = computed(() => store.state.flashcards.currentIndex)
    const currentFlashcard = computed(() => store.getters['flashcards/currentFlashcard'])
    const allFlashcards = computed(() => store.getters['flashcards/allFlashcards'])
    const reviewMode = computed(() => store.getters['flashcards/reviewMode'])
    const deckName = computed(() => store.getters['flashcards/deckName'])

    const toggleReviewMode = () => {
      store.dispatch('flashcards/toggleReviewMode', !reviewMode.value)
      if (!reviewMode.value) {
        // Re-embaralha / reseta índice
        store.dispatch('flashcards/setCurrentIndex', 0)
      }
    }

    const changeDeckName = () => {
      const newName = prompt('Digite o nome do Deck:', deckName.value)
      if (newName) {
        store.dispatch('flashcards/setDeckName', newName)
      }
    }

    const addNewCard = () => {
      store.dispatch('flashcards/addFlashcard', newCard.value)
      newCard.value = { question: '', answer: '', difficulty: 'normal' }
    }

    const removeCard = (id) => {
      if (confirm('Tem certeza que deseja remover este flashcard?')) {
        store.dispatch('flashcards/removeFlashcard', id)
      }
    }

    const nextCard = () => {
      store.dispatch('flashcards/setCurrentIndex', currentIndex.value + 1)
      showAnswer.value = false
    }

    const handleCorrect = () => {
      if (!currentFlashcard.value) return
      store.dispatch('flashcards/handleAnswer', {
        cardId: currentFlashcard.value.id,
        isCorrect: true
      })
    }

    const handleWrong = () => {
      if (!currentFlashcard.value) return
      store.dispatch('flashcards/handleAnswer', {
        cardId: currentFlashcard.value.id,
        isCorrect: false
      })
    }

    onMounted(() => {
      store.dispatch('flashcards/loadFlashcards')
    })

    return {
      newCard,
      showAnswer,
      flashcardsCount,
      currentIndex,
      currentFlashcard,
      allFlashcards,
      reviewMode,
      deckName,
      toggleReviewMode,
      addNewCard,
      removeCard,
      nextCard,
      handleCorrect,
      handleWrong,
      changeDeckName
    }
  }
}
</script>

<style scoped lang="css">
.flashcards-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  background: rgba(255,255,255,0.05);
  border-radius: 8px;
}

/* Header */
.flashcards-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
}
.flashcards-header h1 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.8rem;
}
.header-actions {
  display: flex;
  gap: 1rem;
}
.header-actions button {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}
.header-actions .active {
  background: var(--accent-color);
  color: #fff;
}
.deck-name-button {
  background: rgba(255,255,255,0.1);
  color: var(--text-light);
}

/* Review Mode */
.review-mode {
  text-align: center;
}
.card-review {
  margin: 0 auto;
  max-width: 600px;
  background: rgba(0,0,0,0.2);
  padding: 1rem;
  border-radius: 6px;
  position: relative;
}
.card-content {
  background: rgba(255,255,255,0.1);
  padding: 1rem;
  border-radius: 6px;
  min-height: 150px;
}
.card-content h2 {
  margin-bottom: 0.5rem;
  color: var(--secondary-color);
}
.card-content p {
  font-size: 1.1rem;
  line-height: 1.5;
  color: var(--text-light);
}

.strategies {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.reveal-button {
  background: var(--primary-color);
  color: var(--text-light);
  border: none;
  border-radius: 6px;
  padding: 0.6rem 1rem;
  cursor: pointer;
}
.answer-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}
.correct-btn {
  background: #4caf50; /* verde */
  color: #fff;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
}
.wrong-btn {
  background: #ff4f4f; /* vermelho */
  color: #fff;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
}
.nav-actions {
  margin-top: 1rem;
}
.nav-actions button {
  background: rgba(255,255,255,0.1);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

/* Creation Mode */
.creation-mode {
  margin-top: 2rem;
}
.new-card-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: rgba(0,0,0,0.2);
  padding: 1rem;
  border-radius: 8px;
}
.form-group label {
  margin-bottom: 0.3rem;
  font-weight: 600;
}
.form-group input,
.form-group select {
  border-radius: 6px;
  border: 1px solid rgba(255,255,255,0.1);
  background: rgba(255,255,255,0.05);
  color: var(--text-light);
  padding: 0.6rem;
}
.create-btn {
  background: var(--accent-color);
  color: #fff;
  font-weight: bold;
}

.card-list {
  margin-top: 2rem;
}
.flashcard-table {
  width: 100%;
  border-collapse: collapse;
}
.flashcard-table th,
.flashcard-table td {
  padding: 0.75rem;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}
.flashcard-table th {
  text-align: left;
  background: rgba(0,0,0,0.3);
}
.delete-btn {
  background: #ff5555;
  color: #fff;
  border: none;
  padding: 0.4rem 1rem;
  border-radius: 6px;
  cursor: pointer;
}

/* Animations for transitions */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
.slide-left-enter-active {
  animation: slide-left-in 0.4s forwards;
}
.slide-left-leave-active {
  animation: slide-left-out 0.4s forwards;
}
@keyframes slide-left-in {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes slide-left-out {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-20px);
  }
}

.no-cards {
  text-align: center;
  margin-top: 2rem;
  font-size: 1.1rem;
  color: rgba(255,255,255,0.8);
}
</style>