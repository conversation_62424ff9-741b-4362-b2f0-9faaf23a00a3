<template>
  <div class="flashcard-review">
    <h3>Flashcards do Dia</h3>
    <div v-if="currentCard" class="card">
      <div v-if="!showAnswer">
        <p>{{ currentCard.question }}</p>
      </div>
      <div v-else>
        <p>{{ currentCard.answer }}</p>
      </div>
      <button @click="toggleAnswer">{{ showAnswer ? 'Esconder' : 'Mostrar' }} Resposta</button>
      <div v-if="showAnswer">
        <button @click="markCorrect">Acertei</button>
        <button @click="markIncorrect">Errei</button>
      </div>
    </div>
    <p v-else>Nenhum flashcard para revisar hoje.</p>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'FlashcardReview2',
  setup() {
    const store = useStore()
    const showAnswer = ref(false)

    // Exemplo: Pegar 3 flashcards aleatórios (ou usar lógica de revisão)
    const flashcardsToReview = computed(() => {
      const allCards = store.getters['flashcards/allFlashcards'] // Ajuste o getter
      return allCards.slice(0, 3) // Pegar os 3 primeiros (mude a lógica)
    })

    const currentCardIndex = ref(0)
    const currentCard = computed(() => flashcardsToReview.value[currentCardIndex.value])

    const toggleAnswer = () => {
      showAnswer.value = !showAnswer.value
    }

    const markCorrect = () => {
      // Lógica para marcar como correto (chamar action do Vuex)
      // store.dispatch('flashcards/markCorrect', currentCard.value.id)
      nextCard()
    }

    const markIncorrect = () => {
      // Lógica para marcar como incorreto
      // store.dispatch('flashcards/markIncorrect', currentCard.value.id)
      nextCard()
    }

    const nextCard = () => {
      showAnswer.value = false
      currentCardIndex.value++
      if (currentCardIndex.value >= flashcardsToReview.value.length) {
        currentCardIndex.value = 0 // Volta para o início
      }
    }

    return {
      currentCard,
      showAnswer,
      toggleAnswer,
      markCorrect,
      markIncorrect
    }
  }
}
</script>

<style scoped>
/* Estilos para o componente de revisão de flashcards */
.flashcard-review {
  background: rgba(0, 0, 0, 0.2);
  padding: 1rem;
  border-radius: var(--border-radius-md);
  margin-bottom: 1rem;
}

.card {
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: var(--border-radius-sm);
  margin-bottom: 1rem;
}
</style>