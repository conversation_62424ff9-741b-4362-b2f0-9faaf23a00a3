# METOD<PERSON><PERSON>GIA DO AGENDAMENTO DE PRIMEIRO CONTATO E REVISÕES ESPAÇADAS #

PRIMEIRA PARTE:
    
    > REGISTRO DO ESTUDO TEORICO E AGENDAMENTO DOS PRIMEIROS CONTATOS:
        > TEORIA: BASEADO NA AVALIAÇÃO SUB<PERSON>ETIVA DO ALUNO QUE DEVE CLASSIFICAR O CONTEUDO TEORICO EM QUESTÃO COMO UM CONTEUDO FACIL OU DIFICIL E ESSA AVALIAÇÃO DETERMINARÁ A DATA DO PRIMEIRO CONTATO COM QUESTÕES OU OUTRA FORMA DE ESTUDO PRATICO 
    
    > FÓRMULA DO ALGORTIMO: if(prop("Grau de Dificuldade") == "Fácil",dateAdd(prop("Estudo Teórico"),2,"days"), if(prop("Grau de Dificuldade") == "Difícil", dateAdd(prop("Estudo Teórico"),1,"days"), "Fazer <PERSON>es!"))


SEGUNDA PARTE:  
    
    > TEORIA:NO DIA DO PRIMEIRO DO CONTATO CONFORME AGENDADO PELA LÓGICA ACIMA, O MÉTODO DE ESTUDOS POR QUESTOES AQUI É FAZER BLOCOS DE QUESTOES ENTRE 20-30 QUESTOES (IDEAL 30) E A PARTIR DO DESEMPENHO PERCENTUAL DO ALUNO ISSO GERA UMA NOVA REVISAO PRATICA ESPAÇADA SEGUINDO A FORMULA DO ALGORTIMO ABAIXO, DEPENDENDO EXCLUSIVAMENTE DO DESEMPENHO DO ALUNO
    
     FÓRMULA: if(or(empty(prop("Total de Questões")), empty(prop("Acertos "))), toNumber(""), if(prop("Percentual") <= 0.5, 2, if(and(prop("Percentual") > 0.5, prop("Percentual") <= 0.55), 7, if(and(prop("Percentual") > 0.55,prop("Percentual")<= 0.6), 14, if(and(prop("Percentual") > 0.6, prop("Percentual") <= 0.65), 18, if(and(prop("Percentual") > 0.65, prop("Percentual") <= 0.75), 24, if(and(prop("Percentual") > 0.75, prop("Percentual") <= 0.8), 30, 35)))))))

TERCEIRA PARTE:

    > ESSA PARTE NO NOSSO APP VAI SER AUTOMATIZADO, MAS O PROXIMO SEGUINTE SERIA COLOCAR AS REVISOES NO NO CALENDAROIO DE REVISOES QUE VISUALMENTE ORGANIZA O ESTUDO DA SEMANA, ALEM DE SER PRATICO