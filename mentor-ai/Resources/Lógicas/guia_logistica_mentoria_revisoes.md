# ULTRA GUIA DE LOGÍSTICA DE AGENDAMENTO E REVISÕES - MENTORIA NOTION

## INTRODUÇÃO

Este guia detalhado apresenta a metodologia completa de agendamento, estudo e revisão espaçada implementada na plataforma Notion para maximizar a retenção de conhecimento e otimizar o aprendizado. O sistema é baseado em princípios científicos de aprendizado espaçado e gerenciamento de conhecimento, criando um ciclo virtuoso de estudo contínuo com intervalos estratégicos.

---

## PARTE 1: ARQUITETURA DO SISTEMA

### 1.1 ESTRUTURA DE BANCOS DE DADOS PRINCIPAIS

O sistema utiliza dois bancos de dados interconectados:

| Banco de Dados | Função | Localização |
|----------------|--------|-------------|
| **Registro ET** | Controla o primeiro contato com o material (Estudo Teórico) | Base de Dados (NÃO MEXER)/Registro ET |
| **Revisões R+** | Gerencia todas as revisões subsequentes | Base de Dados (NÃO MEXER)/Revisões R+ |

### 1.2 HIERARQUIA ORGANIZACIONAL

A organização do conteúdo segue uma estrutura hierárquica:

```
Especialidade (ex: Cardiologia)
  └── Matéria (opcional) (ex: CLM 19)
      └── Item Principal (ex: Cardiologia)
          └── Subitem (ex: 1 - HAS e Valvopatias)
```

### 1.3 PROPRIEDADES ESSENCIAIS DO SISTEMA

#### 1.3.1 Propriedades do Registro ET

| Propriedade | Descrição | Valores Possíveis |
|-------------|-----------|-------------------|
| Especialidades | Área médica principal | Texto (ex: Cardiologia) |
| Matérias | Referência do tópico/aula | Texto (ex: CLM 19) |
| Status | Situação atual do estudo | Previstas, Concluído, Atrasadas |
| Estudo Teórico | Data do primeiro contato | Data (ex: 18/08/2024) |
| Concluído | Marcador de conclusão | Yes/No |
| Grau de Dificuldade | Complexidade percebida | Fácil, Médio, Difícil |
| Necessidade revisão teórica | Urgência de revisão | Urgente, Matéria |

#### 1.3.2 Propriedades do Revisões R+

| Propriedade | Descrição | Valores Possíveis |
|-------------|-----------|-------------------|
| Acertos | Quantidade de acertos na revisão | Número (ex: 19) |
| Percentual | Taxa de acerto | Decimal (ex: 0,63) |
| Dias | Intervalo até a próxima revisão | Número (ex: 24) |
| Próxima Revisão | Data calculada para revisão subsequente | Data (ex: 30 de maio de 2024) |
| R1, R2, etc. | Marcação de qual revisão é | Data |
| Total de Questões | Número de questões na revisão | Número (ex: 30) |

---

## PARTE 2: FLUXO OPERACIONAL COMPLETO

### 2.1 METODOLOGIA DO PRIMEIRO CONTATO

#### 2.1.1 Processo de Planejamento
1. Identifique a especialidade e subtópico a ser estudado
2. Crie um novo registro na tabela "Registro ET"
3. Preencha os campos de Especialidade, Matéria e Status (inicial: "Previstas")
4. Agende a data para o estudo inicial no campo "Estudo Teórico"

#### 2.1.2 Execução do Primeiro Contato
1. Na data programada, estude o conteúdo detalhadamente
2. Faça anotações dos pontos-chave e áreas de dificuldade
3. Realize questões de teste para validar a compreensão inicial
4. Registre eventuais materiais complementares no campo "Recurso na Internet"

#### 2.1.3 Finalização do Primeiro Contato
1. Marque o estudo como "Concluído" (campo Concluído = "Yes")
2. Avalie e registre o "Grau de Dificuldade" encontrado
3. Determine a "Necessidade revisão teórica" (Urgente/Matéria)
4. Programe a primeira revisão (R1) com base no desempenho inicial

### 2.2 CICLO DE REVISÕES ESPAÇADAS

#### 2.2.1 Agendamento da Primeira Revisão (R1)
- Tipicamente programada para 7 dias após o estudo inicial
- Registre a data no campo "Próxima Revisão" do banco Revisões R+
- Crie uma entrada relacionada ao estudo original no banco Revisões R+

#### 2.2.2 Execução das Revisões
1. Realize a revisão na data programada utilizando questões ou exercícios práticos
2. Registre o número de questões no campo "Total de Questões"
3. Contabilize os acertos no campo "Acertos"
4. Calcule e registre o percentual de acerto (Acertos ÷ Total)
5. Utilize o comando específico: "Fiz uma revisão em [DATA] e acertei [PORCENTAGEM]%, calcule a data da minha próxima revisao"

#### 2.2.3 Algoritmo para Cálculo de Intervalos
O sistema utiliza um algoritmo baseado em desempenho para determinar o intervalo ideal:

| Faixa de Desempenho | Intervalo Sugerido |
|---------------------|-------------------|
| <50% | 2-7 dias |
| 50-60% | 7-14 dias |
| 60-70% | 14-18 dias |
| 70-80% | 18-21 dias |
| 80-90% | 21-24 dias |
| >90% | >24 dias |

#### 2.2.4 Monitoramento das Revisões Subsequentes
- Cada revisão é registrada sequencialmente (R1, R2, R3...)
- Os intervalos aumentam progressivamente com o desempenho consistente
- Reduções de intervalo são aplicadas automaticamente quando o desempenho cai

---

## PARTE 3: PAINÉIS DE CONTROLE E VISUALIZAÇÕES

### 3.1 PAINEL PRINCIPAL DE REVISÕES

A página "Revisões" contém diversos elementos essenciais:

#### 3.1.1 Área de Acesso Rápido
Links diretos para:
- Tarefas
- Metas
- Resumo e Anotações
- Simulados e Provas
- Organizador Semanal
- Reuniões
- Recursos
- Base de Dados

#### 3.1.2 Visão "HOJE"
- Mostra todas as revisões agendadas para o dia atual
- Oferece acesso rápido aos materiais necessários
- Destaca prioridades do dia

#### 3.1.3 Status das Aulas e Revisões do Mês
- Visão condensada do progresso mensal
- Indicadores visuais de status (concluído, pendente, atrasado)
- Métricas de desempenho acumuladas

#### 3.1.4 Área de Atualizações e Sugestões
- Seção para anotações sobre atualizações no sistema
- Dicas personalizadas baseadas no padrão de estudo
- Recursos complementares recomendados

### 3.2 GERENCIADOR DETALHADO DE REVISÕES

#### 3.2.1 Calendário de Revisões
Organizado em dois níveis:
- **Visão Semanal**: Detalha as revisões dos próximos 7 dias
- **Visão Mensal**: Apresenta panorama completo do mês

#### 3.2.2 Painel de Controle da Semana
- Métricas de progresso da semana atual
- Comparativos com semanas anteriores
- Projeções de desempenho

#### 3.2.3 Controle Semanal Detalhado
Tabela completa com:
- Especialidades agendadas
- Estágio de revisão (R1, R2, etc)
- Tempo estimado para cada revisão
- Status atual (concluído/pendente)

---

## PARTE 4: ELEMENTOS AVANÇADOS DO SISTEMA

### 4.1 INTEGRAÇÃO COM PLATAFORMA EXTERNA

O sistema está integrado com uma plataforma web acessível via:
`https://secondbrain-notion-964192719475.southamerica-east1.run.app/`

Esta integração permite:
- Acesso móvel às revisões
- Sincronização em tempo real
- Visualizações alternativas dos dados
- Relatórios automatizados

### 4.2 COMANDOS ESPECÍFICOS DO SISTEMA

#### 4.2.1 Comandos de Cálculo
- **Cálculo de Próxima Revisão**: "Fiz uma revisão em 23/07/24 e acertei 64%, calcule a data da minha proxima revisao"

#### 4.2.2 Princípios para Comandos Efetivos
Regra fundamental: "Quanto mais contextualizar, enriquecer de detalhes, etc..., melhor a resposta"

Exemplo de comando contextualizado:
```
Supondo que voce é um nefrologista muito experiente e referencia mundial em hemodialise com muitos artigos publicados na área, crie uma prescrição de dialise completa e detalhada para um paciente de 70 kg que chega para sessão de dialise com 2kg acima do seu peso seco e com objetivo de atingir um indice de Kt/V > 1,2. Revise cada item da prescrição relacionado a maquina de hemodialiase como medicações, ions e fluidos administrados ao longo da dialise ou após. Para criar essa prescrição se baseie em referencias atualizadas...
```

### 4.3 CAMPOS ESSENCIAIS DE ANÁLISE DETALHADA

#### 4.3.1 Análise de Dificuldades
- **Caderno de Erros**: Registro detalhado dos erros recorrentes
- **Necessidade revisão teórica**: Marcador para tópicos que precisam de reforço conceitual

#### 4.3.2 Metadados de Performance
- **PC**: Data do Primeiro Contato
- **Dias**: Intervalo calculado para revisão
- **Percentual**: Taxa de acerto calculada

---

## PARTE 5: IMPLEMENTAÇÃO NA SUA MENTORIA

### 5.1 CONFIGURAÇÃO INICIAL DO SISTEMA

#### 5.1.1 Mapeamento Completo de Conteúdo
1. Liste todas as especialidades relevantes para sua área
2. Subdivida em tópicos e subtópicos hierarquicamente
3. Classifique por prioridade e complexidade
4. Determine a sequência lógica de aprendizado

#### 5.1.2 Parametrização do Sistema
1. Defina os parâmetros do algoritmo de espaçamento
2. Configure as propriedades e campos necessários
3. Estabeleça regras para classificação de dificuldade
4. Defina métricas de sucesso para cada tipo de conteúdo

### 5.2 OPERACIONALIZAÇÃO DO CICLO COMPLETO

#### 5.2.1 Fluxo Operacional Diário
1. Inicie o dia consultando o painel "HOJE"
2. Priorize revisões marcadas como "Urgente"
3. Execute as revisões programadas
4. Registre resultados imediatamente após conclusão
5. Consulte o novo agendamento gerado pelo sistema

#### 5.2.2 Revisão Semanal de Desempenho
1. Analise o "Painel de Controle da Semana"
2. Identifique padrões de dificuldade
3. Ajuste estratégias de estudo para tópicos problemáticos
4. Planeje recursos complementares necessários
5. Recalibre prioridades para a semana seguinte

#### 5.2.3 Gestão Mensal do Progresso
1. Avalie métricas mensais acumuladas
2. Compare com metas estabelecidas
3. Identifique tendências de desempenho
4. Ajuste parâmetros do sistema se necessário
5. Estabeleça novas metas para o próximo período

---

## PARTE 6: OTIMIZAÇÃO CONTÍNUA DO SISTEMA

### 6.1 MONITORAMENTO DE MÉTRICAS-CHAVE

#### 6.1.1 Indicadores de Eficiência
- Taxa média de retenção por especialidade
- Tempo médio entre revisões
- Progresso da curva de esquecimento
- Número de revisões necessárias para domínio

#### 6.1.2 Análise de Padrões
- Identificação de horários ótimos para estudo/revisão
- Correlação entre grau de dificuldade e retenção
- Impacto de recursos complementares na performance
- Eficácia dos diferentes tipos de revisão

### 6.2 AJUSTES ESTRATÉGICOS DO SISTEMA

#### 6.2.1 Personalização Algorítmica
1. Ajuste dos intervalos baseado em padrões pessoais
2. Calibração de dificuldade por especialidade
3. Adaptação da metodologia para diferentes tipos de conteúdo
4. Integração de técnicas complementares (mnemônicos, mapas mentais)

#### 6.2.2 Expansão do Sistema
- Incorporação de novas especialidades/tópicos
- Integração com outras ferramentas de estudo
- Desenvolvimento de relatórios avançados
- Compartilhamento de dados entre mentores/mentorados

---

## CONSIDERAÇÕES FINAIS

A implementação correta deste sistema de logística de agendamento e revisões oferece uma estrutura robusta e cientificamente fundamentada para maximizar a retenção de conhecimento. A chave para o sucesso está na consistência de uso, na análise regular dos dados gerados e na adaptação contínua às necessidades específicas de cada aprendiz.

Lembre-se de que o sistema foi projetado para ser flexível - utilize os princípios fundamentais como base, mas personalize os parâmetros de acordo com seu estilo de aprendizado, disponibilidade de tempo e objetivos específicos.

---

## APÊNDICE: COMANDOS E ATALHOS ESSENCIAIS

### A.1 COMANDOS DO SISTEMA
- **Cálculo de Próxima Revisão**: "Fiz uma revisão em [DATA] e acertei [%]%, calcule a data da minha proxima revisao"
- **Criação de Novo Estudo**: "Criar registro de Estudo Teórico para [ESPECIALIDADE] - [TÓPICO]"
- **Atualização de Status**: "Atualizar status do [TÓPICO] para [STATUS]"

### A.2 NAVEGAÇÃO RÁPIDA
- **Acesso ao Mural**: Link direto na sidebar
- **Dashboard de Hoje**: Botão "HOJE" no topo da página
- **Calendário Mensal**: Seção "CALENDÁRIO DE REVISÕES" > "Mensal"
- **Registro de Novos Estudos**: Base de Dados > Registro ET > + Novo

### A.3 RELATÓRIOS ESSENCIAIS
- **Desempenho Mensal**: Acesso via Status das Aulas e Revisões do Mês
- **Progresso por Especialidade**: Visualização filtrada no banco Revisões R+
- **Pendências Urgentes**: Filtrar por "Necessidade revisão teórica = Urgente"
- **Estudos Concluídos**: Filtrar Registro ET por "Concluído = Yes" 