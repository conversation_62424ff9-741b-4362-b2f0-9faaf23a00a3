# Revisões

<aside>
<img src="https://www.notion.so/icons/compass_gray.svg" alt="https://www.notion.so/icons/compass_gray.svg" width="40px" /> <PERSON><PERSON>

[Tarefas ](https://www.notion.so/Tarefas-83db7005526447398465ec7f100daa2f?pvs=21)

[Metas](https://www.notion.so/Metas-58f67c392dfb4960b49b751740eea58e?pvs=21)

---

[Resu<PERSON> e Anotações](https://www.notion.so/Resumo-e-Anota-es-cfc77b5530f74c258a721fa66848a385?pvs=21)

[Revisões](Reviso%CC%83es%2008754d1a2d474660b89331afc228d79c.md)

[Simulados e Provas](https://www.notion.so/Simulados-e-Provas-07a610af70ff4105b21bb0041538b87a?pvs=21)

[Organizador Semanal](https://www.notion.so/Organizador-Semanal-4331695c94134872a14d64072105c974?pvs=21)

[Reuniões](https://www.notion.so/Reuni-es-fddd347a38a148bdb89a14fe4fd02128?pvs=21)

---

[Recursos](https://www.notion.so/Recursos-1a591f073bde48a2a777f2e1a5ffb8a5?pvs=21)

---

[Base de Dados (NÃO MEXER)](https://www.notion.so/Base-de-Dados-N-O-MEXER-2126eb765f114422ad35871f42443183?pvs=21)

</aside>

<aside>
<img src="https://www.notion.so/icons/playback-fast-forward_gray.svg" alt="https://www.notion.so/icons/playback-fast-forward_gray.svg" width="40px" /> **NOVO:**

</aside>

<aside>
<img src="https://www.notion.so/icons/link_gray.svg" alt="https://www.notion.so/icons/link_gray.svg" width="40px" /> **ACESSO RÁPIDO:**

</aside>

### HOJE:

[Sem título](Reviso%CC%83es%2008754d1a2d474660b89331afc228d79c/Sem%20ti%CC%81tulo%208b3a10a51e1640c08279013b55521a20.csv)

### STATUS DAS AULAS E REVISÕES DO MÊS:

[Sem título](Reviso%CC%83es%2008754d1a2d474660b89331afc228d79c/Sem%20ti%CC%81tulo%20c0157eaa414b48058e0f0cb12e4aa605.csv)

### ÁREA DE ATUALIZAÇÕES E SUGESTÕES:

- Atualização:
    
    [ATT.mov](Reviso%CC%83es%2008754d1a2d474660b89331afc228d79c/ATT.mov)
    
- Sugestões:
    
    Dica Personalizada:
    
    [DICAS.mov](Reviso%CC%83es%2008754d1a2d474660b89331afc228d79c/DICAS.mov)
    

### GERENCIADOR DE REVISÕES:

[Sem título](Reviso%CC%83es%2008754d1a2d474660b89331afc228d79c/Sem%20ti%CC%81tulo%20bf7681749cf3471bb8efd6027643f214.csv)

[Sem título](Reviso%CC%83es%2008754d1a2d474660b89331afc228d79c/Sem%20ti%CC%81tulo%206361c63a35b44b9f8fc348c89b3e152a.csv)

### CALENDÁRIO DE REVISÕES:

### Semanal

[Sem título](Reviso%CC%83es%2008754d1a2d474660b89331afc228d79c/Sem%20ti%CC%81tulo%205418c730127d4a6aa86332addf0b0df8.csv)

### Mensal

[Sem título](Reviso%CC%83es%2008754d1a2d474660b89331afc228d79c/Sem%20ti%CC%81tulo%208840e8806d7d43548fd9dc287639ab17.csv)

### PAINEL DE CONTROLE DA SEMANA:

### Status das Revisões:

### Controle Semanal:

[Sem título](Reviso%CC%83es%2008754d1a2d474660b89331afc228d79c/Sem%20ti%CC%81tulo%201a1865af47854c14914966bf4f6d70e9.csv)

[https://secondbrain-notion-964192719475.southamerica-east1.run.app/](https://secondbrain-notion-964192719475.southamerica-east1.run.app/)

**COMANDOS DE TESTE:**

→ Prompt para criar a proxima revisão:

“Fiz uma revisão em 23/07/24 e acertei 64%, calcule a data da minha proxima revisao”

**→ DEVE CONSTAR DATA E PORCENTAGENS DE ACERTOS SMP**

→ Macete de criar um prompt:

Quanto mais conxtualizar, enriquecer de detalhes, etc…, melhor a resposta

Ex:

Supondo que voce é um nefrologista muito experiente e referencia mundial em hemodialise com muitos artigos publicados na área, crie uma prescrição de dialise completa e detalhada para um paciente de 70 kg que chega para sessão de dialise com 2kg acima do seu peso seco e com objetivo de atingir um indice de Kt/V > 1,2. Revise cada item da prescrição relacionado a maquina de hemodialiase como medicações, ions e fluidos administrados ao longo da dialise ou após. Para criar essa prescrição se baseie em referencias atualizadas e use dados confiaveis provinientes de bancos importantes como uptodate, pubmed, new england, guidelines, diretrizes de sociedades de nefrologia, protocolos de hemodialise de instituições oficiais, etc…