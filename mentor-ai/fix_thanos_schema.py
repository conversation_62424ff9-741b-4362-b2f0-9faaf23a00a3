#!/usr/bin/env python3
"""
Fix Thanos Schema Script
Corrige problemas de schema no banco de dados do Thanos
"""

import os
import sys
import sqlite3
import argparse
from pathlib import Path
import traceback

try:
    import psycopg2
    HAS_POSTGRES = True
except ImportError:
    HAS_POSTGRES = False

# Cores para console
class Colors:
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    ENDC = '\033[0m'

def print_colored(text, color):
    """Imprime texto colorido no console"""
    print(f"{color}{text}{Colors.ENDC}")

def get_db_connection(db_type="postgres"):
    """Obtém conexão com o banco de dados"""
    if db_type == "postgres":
        # Tentar obter conexão com PostgreSQL
        try:
            # Ler variáveis de ambiente ou .env
            db_url = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/mentor_ai_db")
            
            # Extrair parâmetros da URL
            parts = db_url.replace("postgresql://", "").split("/")
            credentials = parts[0].split("@")
            
            user_pass = credentials[0].split(":")
            host_port = credentials[1].split(":")
            
            username = user_pass[0]
            password = user_pass[1] if len(user_pass) > 1 else ""
            host = host_port[0]
            port = int(host_port[1]) if len(host_port) > 1 else 5432
            database = parts[1] if len(parts) > 1 else "postgres"
            
            # Conectar ao PostgreSQL
            conn = psycopg2.connect(
                host=host,
                port=port,
                database=database,
                user=username,
                password=password
            )
            
            print_colored(f"✅ Conectado ao PostgreSQL: {host}:{port}/{database}", Colors.GREEN)
            return conn, "postgres"
            
        except Exception as e:
            print_colored(f"⚠️ Não foi possível conectar ao PostgreSQL: {str(e)}", Colors.YELLOW)
            print_colored("⚠️ Tentando SQLite como fallback...", Colors.YELLOW)
    
    # Fallback para SQLite
    try:
        db_path = Path("./backend/thanos.db")
        
        # Se não existir, tentar outros locais comuns
        if not db_path.exists():
            possible_paths = [
                Path("./backend/fastapi_app/thanos.db"),
                Path("./thanos.db"),
                Path("./fastapi_app/thanos.db"),
                Path("./backend.db"),
                Path("./app.db")
            ]
            
            for path in possible_paths:
                if path.exists():
                    db_path = path
                    break
        
        print_colored(f"📂 Usando banco SQLite: {db_path}", Colors.BLUE)
        conn = sqlite3.connect(str(db_path))
        return conn, "sqlite"
    
    except Exception as e:
        print_colored(f"❌ Não foi possível conectar ao SQLite: {str(e)}", Colors.RED)
        return None, None

def check_and_fix_tables(conn, db_type):
    """Verifica e corrige tabelas do Thanos"""
    cursor = conn.cursor()
    fixed_tables = []
    failed_fixes = []
    
    # 0. Verificar se a tabela thanos_sessions existe
    print_colored("\n🔍 Verificando se a tabela thanos_sessions existe...", Colors.BLUE)
    try:
        # Verificar se a tabela existe
        if db_type == "postgres":
            cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_name = 'thanos_sessions'
                )
            """)
            table_exists = cursor.fetchone()[0]
        else:  # sqlite
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='thanos_sessions'
            """)
            table_exists = cursor.fetchone() is not None
        
        if not table_exists:
            print_colored("⚠️ Tabela thanos_sessions não encontrada, criando...", Colors.YELLOW)
            
            # Criar tabela thanos_sessions
            if db_type == "postgres":
                cursor.execute("""
                    CREATE TABLE thanos_sessions (
                        id VARCHAR(255) PRIMARY KEY,
                        document_id VARCHAR(255),
                        provider VARCHAR(50) NOT NULL,
                        model VARCHAR(50) NOT NULL,
                        api_key_hash VARCHAR(255),
                        language VARCHAR(10) DEFAULT 'pt',
                        use_rag BOOLEAN DEFAULT FALSE,
                        temperature FLOAT DEFAULT 0.7,
                        max_tokens INTEGER DEFAULT 2000,
                        streaming_enabled BOOLEAN DEFAULT TRUE,
                        context_window INTEGER DEFAULT 4000,
                        system_prompt TEXT,
                        total_messages INTEGER DEFAULT 0,
                        total_tokens_used INTEGER DEFAULT 0,
                        total_cost FLOAT DEFAULT 0.0,
                        average_response_time FLOAT,
                        user_id VARCHAR(255),
                        user_agent VARCHAR(255),
                        ip_address VARCHAR(50),
                        is_active BOOLEAN DEFAULT TRUE,
                        last_activity TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
            else:  # sqlite
                cursor.execute("""
                    CREATE TABLE thanos_sessions (
                        id TEXT PRIMARY KEY,
                        document_id TEXT,
                        provider TEXT NOT NULL,
                        model TEXT NOT NULL,
                        api_key_hash TEXT,
                        language TEXT DEFAULT 'pt',
                        use_rag INTEGER DEFAULT 0,
                        temperature REAL DEFAULT 0.7,
                        max_tokens INTEGER DEFAULT 2000,
                        streaming_enabled INTEGER DEFAULT 1,
                        context_window INTEGER DEFAULT 4000,
                        system_prompt TEXT,
                        total_messages INTEGER DEFAULT 0,
                        total_tokens_used INTEGER DEFAULT 0,
                        total_cost REAL DEFAULT 0.0,
                        average_response_time REAL,
                        user_id TEXT,
                        user_agent TEXT,
                        ip_address TEXT,
                        is_active INTEGER DEFAULT 1,
                        last_activity TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
            
            conn.commit()
            fixed_tables.append("thanos_sessions (criada)")
            print_colored("✅ Tabela thanos_sessions criada com sucesso!", Colors.GREEN)
        else:
            print_colored("✅ Tabela thanos_sessions já existe", Colors.GREEN)
    
    except Exception as e:
        print_colored(f"❌ Erro ao verificar/criar thanos_sessions: {str(e)}", Colors.RED)
        failed_fixes.append("thanos_sessions (criação)")
        # Continuar com as próximas verificações, mesmo se esta falhar
        conn.rollback()
    
    # 1. Verificar e corrigir tabela thanos_sessions
    print_colored("\n🔍 Verificando coluna api_key_hash em thanos_sessions...", Colors.BLUE)
    
    try:
        # Verificar se a coluna api_key_hash existe
        if db_type == "postgres":
            cursor.execute("""
                SELECT column_name FROM information_schema.columns 
                WHERE table_name = 'thanos_sessions' AND column_name = 'api_key_hash'
            """)
            has_column = cursor.fetchone() is not None
        else:  # sqlite
            cursor.execute("PRAGMA table_info(thanos_sessions)")
            columns = cursor.fetchall()
            has_column = any(col[1] == "api_key_hash" for col in columns)
        
        if not has_column:
            print_colored("⚠️ Coluna api_key_hash não encontrada, adicionando...", Colors.YELLOW)
            
            # Adicionar coluna api_key_hash
            if db_type == "postgres":
                cursor.execute("""
                    ALTER TABLE thanos_sessions 
                    ADD COLUMN api_key_hash VARCHAR(255)
                """)
            else:  # sqlite
                cursor.execute("""
                    ALTER TABLE thanos_sessions 
                    ADD COLUMN api_key_hash TEXT
                """)
            
            conn.commit()
            fixed_tables.append("thanos_sessions (adicionada coluna api_key_hash)")
            print_colored("✅ Coluna api_key_hash adicionada com sucesso!", Colors.GREEN)
        else:
            print_colored("✅ Coluna api_key_hash já existe", Colors.GREEN)
    
    except Exception as e:
        print_colored(f"❌ Erro ao verificar/corrigir coluna api_key_hash: {str(e)}", Colors.RED)
        failed_fixes.append("thanos_sessions (api_key_hash)")
        conn.rollback()
    
    # 2. Verificar tabela thanos_documents
    print_colored("\n🔍 Verificando tabela thanos_documents...", Colors.BLUE)
    
    try:
        # Verificar se a tabela existe
        if db_type == "postgres":
            cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_name = 'thanos_documents'
                )
            """)
            table_exists = cursor.fetchone()[0]
        else:  # sqlite
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='thanos_documents'
            """)
            table_exists = cursor.fetchone() is not None
        
        if not table_exists:
            print_colored("⚠️ Tabela thanos_documents não encontrada, criando...", Colors.YELLOW)
            
            # Criar tabela thanos_documents
            if db_type == "postgres":
                cursor.execute("""
                    CREATE TABLE thanos_documents (
                        id VARCHAR(255) PRIMARY KEY,
                        title VARCHAR(255) NOT NULL,
                        document_type VARCHAR(50) NOT NULL,
                        file_name VARCHAR(255),
                        file_path VARCHAR(255),
                        url VARCHAR(255),
                        content TEXT,
                        summary TEXT,
                        metadata JSONB,
                        embeddings_generated BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
            else:  # sqlite
                cursor.execute("""
                    CREATE TABLE thanos_documents (
                        id TEXT PRIMARY KEY,
                        title TEXT NOT NULL,
                        document_type TEXT NOT NULL,
                        file_name TEXT,
                        file_path TEXT,
                        url TEXT,
                        content TEXT,
                        summary TEXT,
                        metadata TEXT,
                        embeddings_generated INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
            
            conn.commit()
            fixed_tables.append("thanos_documents (criada)")
            print_colored("✅ Tabela thanos_documents criada com sucesso!", Colors.GREEN)
        else:
            print_colored("✅ Tabela thanos_documents já existe", Colors.GREEN)
    
    except Exception as e:
        print_colored(f"❌ Erro ao verificar/corrigir thanos_documents: {str(e)}", Colors.RED)
        failed_fixes.append("thanos_documents")
        conn.rollback()
    
    # 3. Verificar tabela thanos_chunks
    print_colored("\n🔍 Verificando tabela thanos_chunks...", Colors.BLUE)
    
    try:
        # Verificar se a tabela existe
        if db_type == "postgres":
            cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_name = 'thanos_chunks'
                )
            """)
            table_exists = cursor.fetchone()[0]
        else:  # sqlite
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='thanos_chunks'
            """)
            table_exists = cursor.fetchone() is not None
        
        if not table_exists:
            print_colored("⚠️ Tabela thanos_chunks não encontrada, criando...", Colors.YELLOW)
            
            # Criar tabela thanos_chunks
            if db_type == "postgres":
                cursor.execute("""
                    CREATE TABLE thanos_chunks (
                        id SERIAL PRIMARY KEY,
                        document_id VARCHAR(255) REFERENCES thanos_documents(id),
                        text TEXT,
                        embedding JSONB,
                        metadata JSONB,
                        chunk_index INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
            else:  # sqlite
                cursor.execute("""
                    CREATE TABLE thanos_chunks (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        document_id TEXT,
                        text TEXT,
                        embedding TEXT,
                        metadata TEXT,
                        chunk_index INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (document_id) REFERENCES thanos_documents(id)
                    )
                """)
            
            conn.commit()
            fixed_tables.append("thanos_chunks (criada)")
            print_colored("✅ Tabela thanos_chunks criada com sucesso!", Colors.GREEN)
        else:
            print_colored("✅ Tabela thanos_chunks já existe", Colors.GREEN)
    
    except Exception as e:
        print_colored(f"❌ Erro ao verificar/corrigir thanos_chunks: {str(e)}", Colors.RED)
        failed_fixes.append("thanos_chunks")
        conn.rollback()
    
    # 4. Verificar tabela thanos_messages
    print_colored("\n🔍 Verificando tabela thanos_messages...", Colors.BLUE)
    
    try:
        # Verificar se a tabela existe
        if db_type == "postgres":
            cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_name = 'thanos_messages'
                )
            """)
            table_exists = cursor.fetchone()[0]
        else:  # sqlite
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='thanos_messages'
            """)
            table_exists = cursor.fetchone() is not None
        
        if not table_exists:
            print_colored("⚠️ Tabela thanos_messages não encontrada, criando...", Colors.YELLOW)
            
            # Criar tabela thanos_messages
            if db_type == "postgres":
                cursor.execute("""
                    CREATE TABLE thanos_messages (
                        id SERIAL PRIMARY KEY,
                        session_id VARCHAR(255) REFERENCES thanos_sessions(id),
                        role VARCHAR(50) NOT NULL,
                        content TEXT NOT NULL,
                        tokens_used INTEGER DEFAULT 0,
                        processing_time FLOAT DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
            else:  # sqlite
                cursor.execute("""
                    CREATE TABLE thanos_messages (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT,
                        role TEXT NOT NULL,
                        content TEXT NOT NULL,
                        tokens_used INTEGER DEFAULT 0,
                        processing_time REAL DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES thanos_sessions(id)
                    )
                """)
            
            conn.commit()
            fixed_tables.append("thanos_messages (criada)")
            print_colored("✅ Tabela thanos_messages criada com sucesso!", Colors.GREEN)
        else:
            print_colored("✅ Tabela thanos_messages já existe", Colors.GREEN)
    
    except Exception as e:
        print_colored(f"❌ Erro ao verificar/corrigir thanos_messages: {str(e)}", Colors.RED)
        failed_fixes.append("thanos_messages")
        conn.rollback()
    
    return fixed_tables, failed_fixes

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description="Correção de schema do banco de dados do Thanos")
    parser.add_argument("--force", "-f", action="store_true", help="Forçar correção mesmo sem erros detectados")
    parser.add_argument("--db-type", choices=["postgres", "sqlite", "auto"], default="auto", 
                        help="Tipo de banco de dados (postgres, sqlite, auto)")
    args = parser.parse_args()
    
    print_colored("="*60, Colors.PURPLE)
    print_colored("      THANOS DATABASE SCHEMA FIX TOOL", Colors.PURPLE)
    print_colored("="*60, Colors.PURPLE)
    
    # Determinar tipo de banco
    db_type = args.db_type
    if db_type == "auto":
        if HAS_POSTGRES:
            db_type = "postgres"
        else:
            db_type = "sqlite"
    
    # Obter conexão
    conn, actual_db_type = get_db_connection(db_type)
    
    if not conn:
        print_colored("❌ Não foi possível conectar a nenhum banco de dados!", Colors.RED)
        print_colored("   Certifique-se de que o PostgreSQL está rodando ou um arquivo SQLite existe.", Colors.RED)
        return 1
    
    try:
        # Verificar e corrigir tabelas
        fixed_tables, failed_fixes = check_and_fix_tables(conn, actual_db_type)
        
        # Relatório final
        print_colored("\n" + "="*60, Colors.PURPLE)
        print_colored("            RELATÓRIO FINAL", Colors.PURPLE)
        print_colored("="*60, Colors.PURPLE)
        
        if fixed_tables:
            print_colored("\n✅ Tabelas corrigidas:", Colors.GREEN)
            for table in fixed_tables:
                print_colored(f"  - {table}", Colors.GREEN)
        
        if failed_fixes:
            print_colored("\n❌ Falhas na correção:", Colors.RED)
            for table in failed_fixes:
                print_colored(f"  - {table}", Colors.RED)
            
            print_colored("\n⚠️ Algumas correções falharam. Verifique os erros acima.", Colors.YELLOW)
            return 1
        
        if not fixed_tables and not failed_fixes:
            print_colored("\n✅ Nenhum problema de schema encontrado! O banco de dados está correto.", Colors.GREEN)
        else:
            print_colored("\n✅ Correções de schema concluídas com sucesso!", Colors.GREEN)
        
        print_colored("\n🚀 O banco de dados está pronto para uso com o Thanos!", Colors.BLUE)
        
    except Exception as e:
        print_colored(f"\n❌ Erro inesperado: {str(e)}", Colors.RED)
        traceback.print_exc()
        return 1
    finally:
        conn.close()
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 