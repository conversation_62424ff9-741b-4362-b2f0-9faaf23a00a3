#!/bin/bash

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                  MENTOR AI - FRONTEND DEPS FIX                               ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

# Cores para formatação
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para exibir mensagens com formatação
log_info() {
    echo -e "${BLUE}ℹ ${NC}$1"
}

log_success() {
    echo -e "${GREEN}✓ ${NC}$1"
}

log_warning() {
    echo -e "${YELLOW}⚠️ ${NC}$1"
}

log_error() {
    echo -e "${RED}✗ ${NC}$1"
}

# Diretório base
BASE_DIR="$(dirname "$0")"
cd "$BASE_DIR"

echo -e "${YELLOW}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${YELLOW}║                  MENTOR AI - FRONTEND DEPS FIX                               ║${NC}"
echo -e "${YELLOW}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Verificar se estamos no diretório correto
if [ ! -d "frontend" ]; then
    log_error "Diretório 'frontend' não encontrado. Execute este script no diretório raiz do projeto."
    exit 1
fi

# Parar o frontend se estiver rodando
log_info "Parando o frontend se estiver rodando..."
pkill -f "npm run serve" 2>/dev/null || true
lsof -ti:8082 | xargs kill -9 2>/dev/null || true
log_success "Frontend parado"

# Entrar no diretório frontend
cd frontend

# Limpar cache
log_info "Limpando cache do npm..."
npm cache clean --force
log_success "Cache limpo"

# Remover node_modules
log_info "Removendo node_modules..."
rm -rf node_modules
log_success "node_modules removido"

# Remover package-lock.json
log_info "Removendo package-lock.json..."
rm -f package-lock.json
log_success "package-lock.json removido"

# Corrigir dependências específicas
log_info "Atualizando dependências específicas..."

# Atualizar package.json para versões compatíveis
log_info "Atualizando package.json..."
cat > package.json << 'EOF'
{
  "name": "mentor-ai-frontend-ultra",
  "version": "4.0.0",
  "description": "MentorAI Frontend Ultra Robust - Titan Final Edition",
  "private": true,
  "scripts": {
    "serve": "vue-cli-service serve --port 8082 --host 0.0.0.0",
    "serve:fixed": "PORT=8082 HOST=0.0.0.0 vue-cli-service serve",
    "build": "vue-cli-service build",
    "build:prod": "vue-cli-service build --mode production",
    "lint": "vue-cli-service lint --fix",
    "test": "vue-cli-service test:unit",
    "analyze": "vue-cli-service build --analyze"
  },
  "dependencies": {
    "@fortawesome/fontawesome-svg-core": "^6.5.1",
    "@fortawesome/free-regular-svg-icons": "^6.5.1",
    "@fortawesome/free-solid-svg-icons": "^6.5.1",
    "@fortawesome/vue-fontawesome": "^3.0.5",
    "axios": "^1.6.7",
    "bootstrap": "^5.3.3",
    "bootstrap-vue-next": "^0.16.6",
    "chart.js": "^4.4.2",
    "core-js": "^3.36.0",
    "howler": "^2.2.4",
    "html2pdf.js": "^0.10.1",
    "lodash": "^4.17.21",
    "marked": "^12.0.0",
    "moment": "^2.30.1",
    "pinia": "^2.1.7",
    "vue": "^3.4.21",
    "vue-chartjs": "^5.3.0",
    "vue-router": "^4.3.0",
    "vuex": "^4.1.0",
    "vuex-persistedstate": "^4.1.0"
  },
  "devDependencies": {
    "@vue/cli-plugin-babel": "~5.0.8",
    "@vue/cli-plugin-eslint": "~5.0.8",
    "@vue/cli-plugin-router": "~5.0.8",
    "@vue/cli-plugin-vuex": "~5.0.8",
    "@vue/cli-service": "~5.0.8",
    "@vue/eslint-config-standard": "^8.0.1",
    "eslint": "^8.57.0",
    "eslint-plugin-vue": "^9.22.0",
    "sass": "^1.71.1",
    "sass-loader": "^14.1.1"
  },
  "browserslist": [
    "> 1%",
    "last 2 versions"
  ]
}
EOF
log_success "package.json atualizado"

# Instalar dependências
log_info "Instalando dependências..."
export NODE_OPTIONS="--openssl-legacy-provider --max-old-space-size=4096"
npm install --force
log_success "Dependências instaladas"

# Voltar ao diretório raiz
cd ..

# Reiniciar o frontend
log_info "Reiniciando o frontend..."
cd frontend
npm run serve:fixed > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..

# Salvar o PID
echo $FRONTEND_PID > .pids/frontend.pid

# Verificar se o processo está rodando
sleep 10
if ps -p $FRONTEND_PID > /dev/null; then
    log_success "Frontend reiniciado com sucesso! (PID: $FRONTEND_PID)"
    log_info "Acesse: http://localhost:8082"
    log_info "Logs disponíveis em: $BASE_DIR/logs/frontend.log"
else
    log_error "Falha ao reiniciar o frontend. Verificando logs..."
    tail -n 20 logs/frontend.log
    exit 1
fi

echo -e "\n${GREEN}═══════════════════════════════════════════════════════════════════════════════${NC}"
echo -e "${GREEN}                    FRONTEND CORRIGIDO COM SUCESSO!                              ${NC}"
echo -e "${GREEN}═══════════════════════════════════════════════════════════════════════════════${NC}"

echo -e "\nPara verificar o status do sistema a qualquer momento:"
echo -e "${BLUE}./CHECK_STATUS.sh${NC}"

echo -e "\nPara acessar o Second Brain:"
echo -e "${BLUE}http://localhost:8082/second-brain${NC}"

echo -e "\nSistema pronto e funcionando! 🚀" 