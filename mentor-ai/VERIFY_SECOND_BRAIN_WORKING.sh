#!/bin/bash

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                  MENTOR AI - SECOND BRAIN VERIFICATION                       ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

# Cores para formatação
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para exibir mensagens com formatação
log_info() {
    echo -e "${BLUE}ℹ ${NC}$1"
}

log_success() {
    echo -e "${GREEN}✓ ${NC}$1"
}

log_warning() {
    echo -e "${YELLOW}⚠️ ${NC}$1"
}

log_error() {
    echo -e "${RED}✗ ${NC}$1"
}

# Diretório base
BASE_DIR="$(dirname "$0")"
cd "$BASE_DIR"

echo -e "${YELLOW}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${YELLOW}║                  MENTOR AI - SECOND BRAIN VERIFICATION                       ║${NC}"
echo -e "${YELLOW}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Verificar se os serviços estão rodando
log_info "Verificando serviços..."

# Verificar FastAPI
log_info "Verificando FastAPI..."
FASTAPI_RESPONSE=$(curl -s http://localhost:8001/health || echo "FAILED")
if [[ "$FASTAPI_RESPONSE" == *"healthy"* ]]; then
    log_success "FastAPI está rodando"
else
    log_error "FastAPI não está rodando"
    log_info "Execute: ./MENTOR_AI_ULTRA_SYSTEM_FIXED.sh"
    exit 1
fi

# Verificar Frontend
log_info "Verificando Frontend..."
FRONTEND_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8082 || echo "FAILED")
if [[ "$FRONTEND_RESPONSE" == "200" ]]; then
    log_success "Frontend está rodando"
else
    log_error "Frontend não está rodando"
    log_info "Execute: ./MENTOR_AI_ULTRA_SYSTEM_FIXED.sh"
    exit 1
fi

# Verificar endpoint específico do Second Brain
log_info "Verificando API do Second Brain..."
SECOND_BRAIN_TEST=$(curl -s http://localhost:8001/api/chat/test || echo "FAILED")
if [[ "$SECOND_BRAIN_TEST" == *"Test stream working"* ]]; then
    log_success "API do Second Brain está funcionando"
else
    log_error "API do Second Brain não está funcionando"
    log_info "Resposta: $SECOND_BRAIN_TEST"
    exit 1
fi

# Verificar histórico do chat
log_info "Verificando histórico do chat..."
HISTORY_RESPONSE=$(curl -s http://localhost:8001/api/chat/history || echo "FAILED")
if [[ $? -eq 0 && "$HISTORY_RESPONSE" != "FAILED" ]]; then
    log_success "Endpoint de histórico do chat está funcionando"
else
    log_error "Endpoint de histórico do chat não está funcionando"
    exit 1
fi

# Limpar histórico do chat
log_info "Limpando histórico do chat..."
CLEAR_RESPONSE=$(curl -s -X POST http://localhost:8001/api/chat/clear || echo "FAILED")
if [[ $? -eq 0 && "$CLEAR_RESPONSE" != "FAILED" ]]; then
    log_success "Histórico do chat limpo com sucesso"
else
    log_error "Falha ao limpar histórico do chat"
    exit 1
fi

# Testar streaming
log_info "Testando streaming do Second Brain..."
TEST_MESSAGE="Teste de verificação do Second Brain"
STREAM_TEST=$(curl -s -N -H "Accept: text/event-stream" "http://localhost:8001/api/chat/stream?message=${TEST_MESSAGE// /%20}" -m 5 || echo "FAILED")

if [[ "$STREAM_TEST" != "FAILED" ]]; then
    log_success "Streaming do Second Brain está funcionando"
else
    log_error "Falha no streaming do Second Brain"
    exit 1
fi

# Verificar se o frontend tem acesso ao Second Brain
log_info "Verificando acesso do frontend ao Second Brain..."
log_info "Isso requer verificação manual no navegador."
log_info "Acesse: http://localhost:8082/second-brain"
log_info "E teste o chat com uma mensagem simples."

echo -e "\n${GREEN}═══════════════════════════════════════════════════════════════════════════════${NC}"
echo -e "${GREEN}                    SECOND BRAIN VERIFICADO COM SUCESSO!                        ${NC}"
echo -e "${GREEN}═══════════════════════════════════════════════════════════════════════════════${NC}"

echo -e "\nPara acessar o Second Brain:"
echo -e "${BLUE}http://localhost:8082/second-brain${NC}"

echo -e "\nPara verificar o status do sistema a qualquer momento:"
echo -e "${BLUE}./CHECK_STATUS.sh${NC}"

echo -e "\nSistema pronto e funcionando! 🚀" 