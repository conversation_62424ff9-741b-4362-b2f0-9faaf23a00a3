#!/bin/bash

# THANOS HEALTH CHECK SCRIPT
# Verifica se todos os componentes do Thanos estão funcionando

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Diretório base
cd /Users/<USER>/Projetos/meds-AI-Maketing_Site/mentor-ai

echo -e "${PURPLE}╔════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║       THANOS HEALTH CHECK TOOL         ║${NC}"
echo -e "${PURPLE}╚════════════════════════════════════════╝${NC}"

# Verificar backend
echo -e "\n${BLUE}Checking FastAPI backend:${NC}"
if curl -s http://localhost:8001/health > /dev/null; then
    echo -e "${GREEN}✅ FastAPI running on port 8001${NC}"
else
    echo -e "${RED}❌ FastAPI not running on port 8001${NC}"
fi

# Verificar Thanos API
echo -e "\n${BLUE}Checking Thanos API:${NC}"
if curl -s http://localhost:8001/api/thanos/health > /dev/null; then
    RESPONSE=$(curl -s http://localhost:8001/api/thanos/health)
    echo -e "${GREEN}✅ Thanos API responding:${NC} $RESPONSE"
else
    echo -e "${RED}❌ Thanos API not responding${NC}"
fi

# Verificar frontend
echo -e "\n${BLUE}Checking Frontend:${NC}"
if curl -s http://localhost:8082 > /dev/null; then
    echo -e "${GREEN}✅ Frontend running on port 8082${NC}"
else
    echo -e "${RED}❌ Frontend not running on port 8082${NC}"
fi

# Verificar processos
echo -e "\n${BLUE}Checking running processes:${NC}"
FASTAPI_RUNNING=$(ps aux | grep "[p]ython.*fastapi\|[p]ython.*uvicorn" | wc -l)
FRONTEND_RUNNING=$(ps aux | grep "[n]pm.*serve" | wc -l)

if [ $FASTAPI_RUNNING -gt 0 ]; then
    echo -e "${GREEN}✅ FastAPI process running ($FASTAPI_RUNNING instance(s))${NC}"
else
    echo -e "${RED}❌ No FastAPI process running${NC}"
fi

if [ $FRONTEND_RUNNING -gt 0 ]; then
    echo -e "${GREEN}✅ Frontend process running ($FRONTEND_RUNNING instance(s))${NC}"
else
    echo -e "${RED}❌ No Frontend process running${NC}"
fi

# Verificar arquivos PID
echo -e "\n${BLUE}Checking PID files:${NC}"
if [ -f ".fastapi.pid" ]; then
    FASTAPI_PID=$(cat .fastapi.pid)
    if ps -p $FASTAPI_PID > /dev/null; then
        echo -e "${GREEN}✅ FastAPI PID $FASTAPI_PID running${NC}"
    else
        echo -e "${YELLOW}⚠️ FastAPI PID $FASTAPI_PID not running${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ No .fastapi.pid file found${NC}"
fi

if [ -f ".frontend.pid" ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null; then
        echo -e "${GREEN}✅ Frontend PID $FRONTEND_PID running${NC}"
    else
        echo -e "${YELLOW}⚠️ Frontend PID $FRONTEND_PID not running${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ No .frontend.pid file found${NC}"
fi

# Verificar banco de dados
echo -e "\n${BLUE}Checking database:${NC}"
if pg_isready -h localhost -p 5432 &> /dev/null; then
    echo -e "${GREEN}✅ PostgreSQL running on port 5432${NC}"
else
    echo -e "${YELLOW}⚠️ PostgreSQL not running on port 5432${NC}"
fi

# Status geral
echo -e "\n${PURPLE}╔════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║            HEALTH SUMMARY              ║${NC}"
echo -e "${PURPLE}╚════════════════════════════════════════╝${NC}"

TOTAL_CHECKS=4
PASSED_CHECKS=0

if curl -s http://localhost:8001/health > /dev/null; then PASSED_CHECKS=$((PASSED_CHECKS+1)); fi
if curl -s http://localhost:8001/api/thanos/health > /dev/null; then PASSED_CHECKS=$((PASSED_CHECKS+1)); fi
if curl -s http://localhost:8082 > /dev/null; then PASSED_CHECKS=$((PASSED_CHECKS+1)); fi
if pg_isready -h localhost -p 5432 &> /dev/null; then PASSED_CHECKS=$((PASSED_CHECKS+1)); fi

HEALTH_PERCENTAGE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))

if [ $HEALTH_PERCENTAGE -eq 100 ]; then
    echo -e "${GREEN}✅ All systems operational (100%)${NC}"
elif [ $HEALTH_PERCENTAGE -ge 75 ]; then
    echo -e "${YELLOW}⚠️ System partially operational (${HEALTH_PERCENTAGE}%)${NC}"
else
    echo -e "${RED}❌ System degraded (${HEALTH_PERCENTAGE}%)${NC}"
fi

echo -e "\n${BLUE}Next steps if issues detected:${NC}"
echo -e "1. Check logs: ${YELLOW}tail -f logs/thanos_fastapi.log${NC}"
echo -e "2. Restart services: ${YELLOW}./start_thanos_fixed.sh${NC}"
echo -e "3. Stop all: ${YELLOW}./stop_ultra.sh${NC}"