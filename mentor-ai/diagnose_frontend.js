// Diagnóstico completo do frontend
const fs = require('fs');
const path = require('path');

console.log('🔍 Iniciando diagnóstico do frontend...\n');

// 1. Verificar se o arquivo RevisionScheduler.vue existe
const revisionSchedulerPath = path.join(__dirname, 'frontend/src/components/RevisionScheduler.vue');
if (fs.existsSync(revisionSchedulerPath)) {
    console.log('✅ RevisionScheduler.vue existe');
    const stats = fs.statSync(revisionSchedulerPath);
    console.log(`   Tamanho: ${stats.size} bytes`);
    console.log(`   Última modificação: ${stats.mtime}`);
} else {
    console.log('❌ RevisionScheduler.vue NÃO ENCONTRADO!');
}

// 2. Verificar imports do RevisionScheduler
console.log('\n📦 Verificando imports...');
const content = fs.readFileSync(revisionSchedulerPath, 'utf8');
const scriptMatch = content.match(/<script>([\s\S]*?)<\/script>/);
if (scriptMatch) {
    const imports = scriptMatch[1].match(/import .* from .*/g);
    if (imports) {
        imports.forEach(imp => {
            console.log(`   ${imp.trim()}`);
        });
    }
}

// 3. Verificar se SpacedRepetitionService existe
const spacedRepPath = path.join(__dirname, 'frontend/src/services/SpacedRepetitionService.js');
if (fs.existsSync(spacedRepPath)) {
    console.log('\n✅ SpacedRepetitionService.js existe');
} else {
    console.log('\n❌ SpacedRepetitionService.js NÃO ENCONTRADO!');
}

// 4. Verificar se SharedCalendar existe
const sharedCalendarPath = path.join(__dirname, 'frontend/src/components/SharedCalendar.vue');
if (fs.existsSync(sharedCalendarPath)) {
    console.log('✅ SharedCalendar.vue existe');
} else {
    console.log('❌ SharedCalendar.vue NÃO ENCONTRADO!');
}

// 5. Verificar rotas
console.log('\n🛣️  Verificando rotas...');
const routerPath = path.join(__dirname, 'frontend/src/router/index.js');
const routerContent = fs.readFileSync(routerPath, 'utf8');
const revisionRoute = routerContent.includes("path: '/revision-scheduler'");
console.log(revisionRoute ? '✅ Rota /revision-scheduler configurada' : '❌ Rota /revision-scheduler NÃO encontrada');

// 6. Verificar store
console.log('\n🗄️  Verificando Vuex store...');
const storeFlashcardsPath = path.join(__dirname, 'frontend/src/store/modules/flashcards.js');
if (fs.existsSync(storeFlashcardsPath)) {
    console.log('✅ Store flashcards.js existe');
    const storeContent = fs.readFileSync(storeFlashcardsPath, 'utf8');
    const hasAddFlashcard = storeContent.includes('addFlashcard');
    console.log(hasAddFlashcard ? '   ✅ Action addFlashcard encontrada' : '   ❌ Action addFlashcard NÃO encontrada');
} else {
    console.log('❌ Store flashcards.js NÃO ENCONTRADO!');
}

// 7. Verificar dependências
console.log('\n📚 Verificando dependências...');
const packageJsonPath = path.join(__dirname, 'frontend/package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
const requiredDeps = ['vue', 'vuex', 'vue-router', 'date-fns', '@fortawesome/vue-fontawesome'];
requiredDeps.forEach(dep => {
    const hasDep = packageJson.dependencies[dep] || packageJson.devDependencies[dep];
    console.log(hasDep ? `   ✅ ${dep}: ${hasDep}` : `   ❌ ${dep}: NÃO ENCONTRADO`);
});

// 8. Verificar erros de sintaxe no RevisionScheduler
console.log('\n🔧 Verificando sintaxe do RevisionScheduler...');
try {
    // Verificar se há erros óbvios
    const hasExportDefault = content.includes('export default');
    console.log(hasExportDefault ? '   ✅ export default encontrado' : '   ❌ export default NÃO encontrado');
    
    const hasSetup = content.includes('setup()');
    console.log(hasSetup ? '   ✅ setup() encontrado' : '   ❌ setup() NÃO encontrado');
    
    const hasReturn = content.includes('return {');
    console.log(hasReturn ? '   ✅ return statement encontrado' : '   ❌ return statement NÃO encontrado');
} catch (e) {
    console.log('   ❌ Erro ao verificar sintaxe:', e.message);
}

console.log('\n✅ Diagnóstico concluído!');