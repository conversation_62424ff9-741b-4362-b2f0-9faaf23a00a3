"""
Script para integrar o Thanos no FastAPI principal
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Este script será importado pelo main.py
from backend.thanos.router import router as thanos_router

def integrate_thanos(app):
    """Integra as rotas do Thanos no app FastAPI"""
    app.include_router(thanos_router)
    print("✅ Thanos router integrated successfully!")
    return app
