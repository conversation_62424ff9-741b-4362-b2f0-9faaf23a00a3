"""
Review Engine Services - Sistema Avançado de Análise e Avaliação
Serviços principais para processamento de reviews completos
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func

from .models import (
    ReviewSession, ContentReview, PerformanceReview, ReviewInsight,
    ContentAnalysisRequest, PerformanceAnalysisRequest,
    ContentReviewResponse, PerformanceReviewResponse, ComprehensiveReviewResponse,
    ReviewType, DifficultyLevel, MedicalSpecialty, ContentType,
    ContentMetrics, PerformanceMetrics, ReviewInsightModel
)
from .ai_reviewer import AIReviewerEngine
from ..database import get_db

logger = logging.getLogger(__name__)

class ReviewEngineService:
    """Serviço principal do sistema de reviews"""
    
    def __init__(self):
        self.ai_engine = AIReviewerEngine()
        self.analysis_cache = {}
        
    async def create_review_session(
        self,
        user_id: str,
        review_type: ReviewType,
        config: Optional[Dict[str, Any]] = None
    ) -> str:
        """Cria uma nova sessão de review"""
        try:
            db = next(get_db())
            
            session = ReviewSession(
                user_id=user_id,
                review_type=review_type.value,
                analysis_config=config or {}
            )
            
            db.add(session)
            db.commit()
            db.refresh(session)
            
            logger.info(f"Created review session {session.id} for user {user_id}")
            return session.id
            
        except Exception as e:
            logger.error(f"Error creating review session: {e}")
            raise
        finally:
            db.close()
    
    async def analyze_content(
        self,
        request: ContentAnalysisRequest,
        session_id: Optional[str] = None
    ) -> ContentReviewResponse:
        """Analisa conteúdo educacional médico"""
        try:
            # Criar sessão se não fornecida
            if not session_id:
                session_id = await self.create_review_session(
                    user_id="system",
                    review_type=ReviewType.CONTENT_ANALYSIS,
                    config=request.analysis_config.dict() if request.analysis_config else None
                )
            
            # Executar análise com IA
            analysis_result = await self.ai_engine.analyze_content(
                content_text=request.content_text,
                content_type=request.content_type,
                medical_specialty=request.medical_specialty,
                target_audience=request.target_audience,
                config=request.analysis_config
            )
            
            # Salvar resultado no banco
            db = next(get_db())
            
            content_review = ContentReview(
                session_id=session_id,
                content_title=request.content_title,
                content_type=request.content_type.value,
                medical_specialty=request.medical_specialty.value,
                difficulty_level=analysis_result["difficulty_level"],
                overall_score=analysis_result["metrics"]["overall_score"],
                technical_precision=analysis_result["metrics"]["technical_precision"],
                pedagogical_quality=analysis_result["metrics"]["pedagogical_quality"],
                clinical_relevance=analysis_result["metrics"]["clinical_relevance"],
                resource_quality=analysis_result["metrics"]["resource_quality"],
                strengths=analysis_result["strengths"],
                improvements=analysis_result["improvements"],
                recommendations=analysis_result["recommendations"],
                analysis_metadata=analysis_result["metadata"]
            )
            
            db.add(content_review)
            db.commit()
            db.refresh(content_review)
            
            # Atualizar status da sessão
            await self._update_session_status(session_id, "completed")
            
            # Construir resposta
            response = ContentReviewResponse(
                id=content_review.id,
                session_id=session_id,
                content_title=request.content_title,
                content_type=request.content_type.value,
                medical_specialty=request.medical_specialty.value,
                difficulty_level=analysis_result["difficulty_level"],
                metrics=ContentMetrics(**analysis_result["metrics"]),
                strengths=analysis_result["strengths"],
                improvements=analysis_result["improvements"],
                recommendations=analysis_result["recommendations"],
                analysis_metadata=analysis_result["metadata"],
                created_at=content_review.created_at,
                confidence_score=analysis_result["confidence_score"]
            )
            
            logger.info(f"Content analysis completed for session {session_id}")
            return response
            
        except Exception as e:
            logger.error(f"Error in content analysis: {e}")
            if session_id:
                await self._update_session_status(session_id, "failed")
            raise
        finally:
            db.close()
    
    async def analyze_performance(
        self,
        request: PerformanceAnalysisRequest,
        session_id: Optional[str] = None
    ) -> PerformanceReviewResponse:
        """Analisa performance de aprendizado do usuário"""
        try:
            # Criar sessão se não fornecida
            if not session_id:
                session_id = await self.create_review_session(
                    user_id=request.user_id,
                    review_type=ReviewType.PERFORMANCE_EVALUATION,
                    config=request.analysis_config.dict() if request.analysis_config else None
                )
            
            # Coletar dados de performance
            performance_data = await self._collect_performance_data(
                user_id=request.user_id,
                days=request.analysis_period_days,
                focus_areas=request.focus_areas
            )
            
            # Executar análise com IA
            analysis_result = await self.ai_engine.analyze_performance(
                performance_data=performance_data,
                user_id=request.user_id,
                include_predictions=request.include_predictions,
                config=request.analysis_config
            )
            
            # Salvar resultado no banco
            db = next(get_db())
            
            performance_review = PerformanceReview(
                session_id=session_id,
                user_id=request.user_id,
                overall_performance=analysis_result["metrics"]["overall_performance"],
                accuracy_rate=analysis_result["metrics"]["accuracy_rate"],
                response_time_avg=analysis_result["metrics"]["response_time_avg"],
                retention_rate=analysis_result["metrics"]["retention_rate"],
                consistency_score=analysis_result["metrics"]["consistency_score"],
                engagement_level=analysis_result["behavioral"]["engagement_level"],
                learning_style=analysis_result["behavioral"]["learning_style"],
                error_patterns=analysis_result["error_patterns"],
                success_probability=analysis_result["predictions"].get("success_probability"),
                risk_factors=analysis_result["risk_factors"],
                improvement_suggestions=analysis_result["improvement_suggestions"],
                analysis_period_start=datetime.now() - timedelta(days=request.analysis_period_days),
                analysis_period_end=datetime.now()
            )
            
            db.add(performance_review)
            db.commit()
            db.refresh(performance_review)
            
            # Gerar insights automáticos
            await self._generate_performance_insights(
                user_id=request.user_id,
                analysis_result=analysis_result
            )
            
            # Atualizar status da sessão
            await self._update_session_status(session_id, "completed")
            
            # Construir resposta
            response = PerformanceReviewResponse(
                id=performance_review.id,
                session_id=session_id,
                user_id=request.user_id,
                metrics=PerformanceMetrics(**analysis_result["metrics"]),
                behavioral_analysis=analysis_result["behavioral"],
                error_patterns=analysis_result["error_patterns"],
                predictions=analysis_result["predictions"],
                risk_factors=analysis_result["risk_factors"],
                improvement_suggestions=analysis_result["improvement_suggestions"],
                comparative_analysis=analysis_result.get("comparative_analysis"),
                analysis_period={
                    "start": performance_review.analysis_period_start,
                    "end": performance_review.analysis_period_end
                },
                created_at=performance_review.created_at,
                confidence_score=analysis_result["confidence_score"]
            )
            
            logger.info(f"Performance analysis completed for user {request.user_id}")
            return response
            
        except Exception as e:
            logger.error(f"Error in performance analysis: {e}")
            if session_id:
                await self._update_session_status(session_id, "failed")
            raise
        finally:
            db.close()
    
    async def comprehensive_review(
        self,
        user_id: str,
        content_requests: Optional[List[ContentAnalysisRequest]] = None,
        performance_request: Optional[PerformanceAnalysisRequest] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> ComprehensiveReviewResponse:
        """Executa review completo integrando análise de conteúdo e performance"""
        try:
            # Criar sessão principal
            session_id = await self.create_review_session(
                user_id=user_id,
                review_type=ReviewType.COMPREHENSIVE_REVIEW,
                config=config or {}
            )
            
            content_reviews = []
            performance_review = None
            
            # Executar análises de conteúdo se fornecidas
            if content_requests:
                for content_request in content_requests:
                    content_review = await self.analyze_content(
                        request=content_request,
                        session_id=session_id
                    )
                    content_reviews.append(content_review)
            
            # Executar análise de performance se fornecida
            if performance_request:
                performance_review = await self.analyze_performance(
                    request=performance_request,
                    session_id=session_id
                )
            
            # Gerar insights integrados
            integrated_insights = await self._generate_integrated_insights(
                user_id=user_id,
                content_reviews=content_reviews,
                performance_review=performance_review
            )
            
            # Gerar plano de ação consolidado
            action_plan = await self._generate_action_plan(
                user_id=user_id,
                content_reviews=content_reviews,
                performance_review=performance_review,
                insights=integrated_insights
            )
            
            # Calcular métricas consolidadas
            overall_assessment = await self._calculate_overall_assessment(
                content_reviews=content_reviews,
                performance_review=performance_review
            )
            
            # Construir resposta
            response = ComprehensiveReviewResponse(
                session_id=session_id,
                review_type=ReviewType.COMPREHENSIVE_REVIEW,
                user_id=user_id,
                content_review=content_reviews[0] if content_reviews else None,
                performance_review=performance_review,
                integrated_insights=integrated_insights,
                action_plan=action_plan,
                overall_assessment=overall_assessment,
                created_at=datetime.now(),
                completed_at=datetime.now(),
                confidence_score=self._calculate_confidence_score(
                    content_reviews, performance_review
                )
            )
            
            logger.info(f"Comprehensive review completed for user {user_id}")
            return response
            
        except Exception as e:
            logger.error(f"Error in comprehensive review: {e}")
            raise
    
    async def get_user_insights(
        self,
        user_id: str,
        category: Optional[str] = None,
        active_only: bool = True
    ) -> List[ReviewInsightModel]:
        """Recupera insights do usuário"""
        try:
            db = next(get_db())
            
            query = db.query(ReviewInsight).filter(ReviewInsight.user_id == user_id)
            
            if category:
                query = query.filter(ReviewInsight.category == category)
            
            if active_only:
                query = query.filter(ReviewInsight.is_active == True)
            
            insights = query.order_by(desc(ReviewInsight.created_at)).all()
            
            return [
                ReviewInsightModel(
                    id=insight.id,
                    insight_type=insight.insight_type,
                    category=insight.category,
                    title=insight.title,
                    description=insight.description,
                    confidence_score=insight.confidence_score,
                    priority=insight.priority,
                    supporting_data=insight.supporting_data,
                    action_items=insight.action_items,
                    is_active=insight.is_active,
                    created_at=insight.created_at
                )
                for insight in insights
            ]
            
        except Exception as e:
            logger.error(f"Error retrieving user insights: {e}")
            raise
        finally:
            db.close()
    
    # Métodos auxiliares privados
    async def _update_session_status(self, session_id: str, status: str):
        """Atualiza status da sessão"""
        try:
            db = next(get_db())
            session = db.query(ReviewSession).filter(ReviewSession.id == session_id).first()
            if session:
                session.status = status
                if status == "completed":
                    session.completed_at = datetime.now()
                db.commit()
        except Exception as e:
            logger.error(f"Error updating session status: {e}")
        finally:
            db.close()
    
    async def _collect_performance_data(
        self,
        user_id: str,
        days: int,
        focus_areas: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Coleta dados de performance do usuário"""
        # Implementar coleta de dados de diferentes fontes
        # (flashcards, second brain, calendar, etc.)
        
        # Por enquanto, retorna dados mock
        return {
            "user_id": user_id,
            "period_days": days,
            "total_sessions": 45,
            "total_questions": 1250,
            "correct_answers": 1000,
            "average_response_time": 12.5,
            "study_sessions": [],
            "performance_trends": {},
            "focus_areas": focus_areas or []
        }
    
    async def _generate_performance_insights(
        self,
        user_id: str,
        analysis_result: Dict[str, Any]
    ):
        """Gera insights automáticos baseados na análise de performance"""
        try:
            db = next(get_db())
            
            # Gerar insights baseados nos resultados
            insights_to_create = []
            
            # Insight de performance geral
            if analysis_result["metrics"]["overall_performance"] < 70:
                insights_to_create.append({
                    "insight_type": "recommendation",
                    "category": "performance",
                    "title": "Performance Abaixo do Esperado",
                    "description": "Sua performance geral está abaixo da média. Considere ajustar sua estratégia de estudo.",
                    "confidence_score": 0.85,
                    "priority": "high"
                })
            
            # Insight de padrões de erro
            if analysis_result["error_patterns"]:
                insights_to_create.append({
                    "insight_type": "pattern",
                    "category": "learning",
                    "title": "Padrões de Erro Identificados",
                    "description": "Foram identificados padrões específicos em seus erros que podem ser corrigidos.",
                    "confidence_score": 0.90,
                    "priority": "medium"
                })
            
            # Salvar insights
            for insight_data in insights_to_create:
                insight = ReviewInsight(
                    user_id=user_id,
                    **insight_data
                )
                db.add(insight)
            
            db.commit()
            
        except Exception as e:
            logger.error(f"Error generating performance insights: {e}")
        finally:
            db.close()
    
    async def _generate_integrated_insights(
        self,
        user_id: str,
        content_reviews: List[ContentReviewResponse],
        performance_review: Optional[PerformanceReviewResponse]
    ) -> List[ReviewInsightModel]:
        """Gera insights integrados combinando análises"""
        # Implementar lógica de integração de insights
        return []
    
    async def _generate_action_plan(
        self,
        user_id: str,
        content_reviews: List[ContentReviewResponse],
        performance_review: Optional[PerformanceReviewResponse],
        insights: List[ReviewInsightModel]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Gera plano de ação consolidado"""
        return {
            "immediate": [],
            "short_term": [],
            "long_term": []
        }
    
    async def _calculate_overall_assessment(
        self,
        content_reviews: List[ContentReviewResponse],
        performance_review: Optional[PerformanceReviewResponse]
    ) -> Dict[str, float]:
        """Calcula avaliação geral consolidada"""
        return {
            "overall_score": 85.0,
            "content_quality": 88.0,
            "performance_level": 82.0,
            "improvement_potential": 75.0
        }
    
    def _calculate_confidence_score(
        self,
        content_reviews: List[ContentReviewResponse],
        performance_review: Optional[PerformanceReviewResponse]
    ) -> float:
        """Calcula score de confiança da análise"""
        scores = []
        
        if content_reviews:
            scores.extend([review.confidence_score for review in content_reviews])
        
        if performance_review:
            scores.append(performance_review.confidence_score)
        
        return sum(scores) / len(scores) if scores else 0.0
