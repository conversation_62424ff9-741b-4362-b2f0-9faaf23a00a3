"""
AI Reviewer Engine - Motor de IA para Reviews Completos
Sistema de IA especializado em análise de conteúdo e performance educacional médica
"""

import asyncio
import json
import re
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from pathlib import Path

# Simulação de imports de IA (substituir por implementações reais)
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

try:
    import anthropic
    HAS_ANTHROPIC = True
except ImportError:
    HAS_ANTHROPIC = False

from .models import (
    ContentType, MedicalSpecialty, DifficultyLevel,
    ReviewConfigBase, ContentAnalysisRequest, PerformanceAnalysisRequest
)

logger = logging.getLogger(__name__)

class AIReviewerEngine:
    """Motor de IA para análise e avaliação completa"""
    
    def __init__(self):
        self.context_path = Path(__file__).parent.parent.parent / "CONTEXTS"
        self.load_contexts()
        self.initialize_ai_clients()
        
    def load_contexts(self):
        """Carrega contextos de prompts especializados"""
        try:
            # Carregar contextos principais
            self.review_master_context = self._load_context_file("REVIEW_MASTER.txt")
            self.content_analyzer_context = self._load_context_file("CONTENT_ANALYZER.txt")
            self.performance_evaluator_context = self._load_context_file("PERFORMANCE_EVALUATOR.txt")
            
            logger.info("AI contexts loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading AI contexts: {e}")
            # Usar contextos padrão se não conseguir carregar
            self._load_default_contexts()
    
    def _load_context_file(self, filename: str) -> str:
        """Carrega arquivo de contexto específico"""
        try:
            context_file = self.context_path / filename
            if context_file.exists():
                return context_file.read_text(encoding='utf-8')
            else:
                logger.warning(f"Context file not found: {filename}")
                return ""
        except Exception as e:
            logger.error(f"Error loading context file {filename}: {e}")
            return ""
    
    def _load_default_contexts(self):
        """Carrega contextos padrão caso os arquivos não estejam disponíveis"""
        self.review_master_context = """
        Você é um especialista em análise de conteúdo educacional médico.
        Analise o conteúdo fornecido e forneça uma avaliação completa.
        """
        
        self.content_analyzer_context = """
        Você é um analisador de conteúdo especializado em materiais médicos.
        Avalie a qualidade, precisão e efetividade pedagógica do conteúdo.
        """
        
        self.performance_evaluator_context = """
        Você é um avaliador de performance educacional especializado em medicina.
        Analise os dados de performance e forneça insights acionáveis.
        """
    
    def initialize_ai_clients(self):
        """Inicializa clientes de IA"""
        self.openai_client = None
        self.anthropic_client = None
        
        # Configurar OpenAI se disponível
        if HAS_OPENAI:
            try:
                # Configurar cliente OpenAI
                # openai.api_key = os.getenv("OPENAI_API_KEY")
                # self.openai_client = openai
                pass
            except Exception as e:
                logger.warning(f"OpenAI client initialization failed: {e}")
        
        # Configurar Anthropic se disponível
        if HAS_ANTHROPIC:
            try:
                # Configurar cliente Anthropic
                # self.anthropic_client = anthropic.Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
                pass
            except Exception as e:
                logger.warning(f"Anthropic client initialization failed: {e}")
    
    async def analyze_content(
        self,
        content_text: str,
        content_type: ContentType,
        medical_specialty: MedicalSpecialty,
        target_audience: str = "students",
        config: Optional[ReviewConfigBase] = None
    ) -> Dict[str, Any]:
        """Analisa conteúdo educacional médico usando IA"""
        try:
            # Preparar prompt para análise
            analysis_prompt = self._build_content_analysis_prompt(
                content_text=content_text,
                content_type=content_type,
                medical_specialty=medical_specialty,
                target_audience=target_audience,
                config=config
            )
            
            # Executar análise com IA
            if self.anthropic_client:
                analysis_result = await self._analyze_with_anthropic(analysis_prompt)
            elif self.openai_client:
                analysis_result = await self._analyze_with_openai(analysis_prompt)
            else:
                # Usar análise mock se IA não estiver disponível
                analysis_result = await self._mock_content_analysis(
                    content_text, content_type, medical_specialty
                )
            
            # Processar e estruturar resultado
            structured_result = self._structure_content_analysis(analysis_result)
            
            # Adicionar métricas calculadas
            structured_result["metrics"].update(
                self._calculate_content_metrics(content_text, content_type)
            )
            
            # Adicionar metadados
            structured_result["metadata"] = {
                "analysis_timestamp": datetime.now().isoformat(),
                "content_length": len(content_text),
                "content_type": content_type.value,
                "medical_specialty": medical_specialty.value,
                "target_audience": target_audience,
                "ai_model_used": self._get_active_ai_model(),
                "analysis_version": "2.0"
            }
            
            logger.info(f"Content analysis completed for {content_type.value} content")
            return structured_result
            
        except Exception as e:
            logger.error(f"Error in content analysis: {e}")
            # Retornar análise básica em caso de erro
            return await self._fallback_content_analysis(content_text, content_type)
    
    async def analyze_performance(
        self,
        performance_data: Dict[str, Any],
        user_id: str,
        include_predictions: bool = True,
        config: Optional[ReviewConfigBase] = None
    ) -> Dict[str, Any]:
        """Analisa performance de aprendizado usando IA"""
        try:
            # Preparar prompt para análise de performance
            analysis_prompt = self._build_performance_analysis_prompt(
                performance_data=performance_data,
                user_id=user_id,
                include_predictions=include_predictions,
                config=config
            )
            
            # Executar análise com IA
            if self.anthropic_client:
                analysis_result = await self._analyze_with_anthropic(analysis_prompt)
            elif self.openai_client:
                analysis_result = await self._analyze_with_openai(analysis_prompt)
            else:
                # Usar análise mock se IA não estiver disponível
                analysis_result = await self._mock_performance_analysis(performance_data)
            
            # Processar e estruturar resultado
            structured_result = self._structure_performance_analysis(analysis_result)
            
            # Adicionar métricas calculadas
            structured_result["metrics"].update(
                self._calculate_performance_metrics(performance_data)
            )
            
            # Adicionar análise preditiva se solicitada
            if include_predictions:
                structured_result["predictions"] = await self._generate_predictions(
                    performance_data, structured_result["metrics"]
                )
            
            # Adicionar metadados
            structured_result["metadata"] = {
                "analysis_timestamp": datetime.now().isoformat(),
                "user_id": user_id,
                "data_period_days": performance_data.get("period_days", 30),
                "total_data_points": len(performance_data.get("study_sessions", [])),
                "ai_model_used": self._get_active_ai_model(),
                "analysis_version": "2.0"
            }
            
            logger.info(f"Performance analysis completed for user {user_id}")
            return structured_result
            
        except Exception as e:
            logger.error(f"Error in performance analysis: {e}")
            # Retornar análise básica em caso de erro
            return await self._fallback_performance_analysis(performance_data)
    
    def _build_content_analysis_prompt(
        self,
        content_text: str,
        content_type: ContentType,
        medical_specialty: MedicalSpecialty,
        target_audience: str,
        config: Optional[ReviewConfigBase]
    ) -> str:
        """Constrói prompt para análise de conteúdo"""
        
        depth_level = config.depth_level if config else "standard"
        focus_areas = config.focus_areas if config else []
        
        prompt = f"""
{self.content_analyzer_context}

TAREFA: Analise o seguinte conteúdo educacional médico e forneça uma avaliação completa.

INFORMAÇÕES DO CONTEÚDO:
- Tipo: {content_type.value}
- Especialidade: {medical_specialty.value}
- Público-alvo: {target_audience}
- Nível de análise: {depth_level}
- Áreas de foco: {', '.join(focus_areas) if focus_areas else 'Análise geral'}

CONTEÚDO A ANALISAR:
{content_text[:5000]}  # Limitar para evitar tokens excessivos

INSTRUÇÕES:
1. Avalie a precisão científica e atualização do conteúdo
2. Analise a qualidade pedagógica e estrutura didática
3. Verifique a relevância clínica e aplicabilidade prática
4. Avalie a qualidade dos recursos e suporte oferecidos
5. Identifique pontos fortes e oportunidades de melhoria
6. Forneça recomendações específicas para diferentes níveis de usuários

FORMATO DE RESPOSTA:
Retorne um JSON estruturado com as seguintes seções:
- metrics: scores de 0-100 para cada dimensão
- difficulty_level: basic/intermediate/advanced
- strengths: lista de pontos fortes
- improvements: lista de melhorias sugeridas
- recommendations: recomendações por nível de usuário
- confidence_score: confiança da análise (0-100)
"""
        
        return prompt

    def _build_performance_analysis_prompt(
        self,
        performance_data: Dict[str, Any],
        user_id: str,
        include_predictions: bool,
        config: Optional[ReviewConfigBase]
    ) -> str:
        """Constrói prompt para análise de performance"""

        depth_level = config.depth_level if config else "standard"
        focus_areas = config.focus_areas if config else []

        # Preparar resumo dos dados de performance
        data_summary = {
            "total_sessions": performance_data.get("total_sessions", 0),
            "total_questions": performance_data.get("total_questions", 0),
            "correct_answers": performance_data.get("correct_answers", 0),
            "average_response_time": performance_data.get("average_response_time", 0),
            "period_days": performance_data.get("period_days", 30)
        }

        prompt = f"""
{self.performance_evaluator_context}

TAREFA: Analise os dados de performance de aprendizado e forneça uma avaliação completa.

INFORMAÇÕES DO USUÁRIO:
- ID do usuário: {user_id}
- Período de análise: {data_summary['period_days']} dias
- Nível de análise: {depth_level}
- Incluir predições: {include_predictions}
- Áreas de foco: {', '.join(focus_areas) if focus_areas else 'Análise geral'}

DADOS DE PERFORMANCE:
- Total de sessões de estudo: {data_summary['total_sessions']}
- Total de questões respondidas: {data_summary['total_questions']}
- Respostas corretas: {data_summary['correct_answers']}
- Tempo médio de resposta: {data_summary['average_response_time']} segundos
- Taxa de acerto: {(data_summary['correct_answers'] / max(data_summary['total_questions'], 1)) * 100:.1f}%

INSTRUÇÕES:
1. Calcule métricas de performance principais
2. Identifique padrões de aprendizado e comportamento
3. Analise tendências e consistência
4. Identifique pontos fortes e áreas de melhoria
5. Forneça recomendações específicas e acionáveis
6. {'Inclua análise preditiva e fatores de risco' if include_predictions else 'Foque na análise atual'}

FORMATO DE RESPOSTA:
Retorne um JSON estruturado com as seguintes seções:
- metrics: métricas quantitativas principais
- behavioral: análise comportamental e padrões
- error_patterns: padrões de erro identificados
- risk_factors: fatores de risco (se aplicável)
- improvement_suggestions: sugestões de melhoria
- confidence_score: confiança da análise (0-100)
"""

        return prompt

    async def _analyze_with_anthropic(self, prompt: str) -> str:
        """Executa análise usando Anthropic Claude"""
        try:
            # Implementar chamada real para Anthropic
            # response = await self.anthropic_client.messages.create(
            #     model="claude-3-sonnet-20240229",
            #     max_tokens=4000,
            #     messages=[{"role": "user", "content": prompt}]
            # )
            # return response.content[0].text

            # Por enquanto, retorna análise mock
            return await self._mock_ai_response(prompt)

        except Exception as e:
            logger.error(f"Error with Anthropic analysis: {e}")
            return await self._mock_ai_response(prompt)

    async def _analyze_with_openai(self, prompt: str) -> str:
        """Executa análise usando OpenAI GPT"""
        try:
            # Implementar chamada real para OpenAI
            # response = await self.openai_client.chat.completions.create(
            #     model="gpt-4",
            #     messages=[{"role": "user", "content": prompt}],
            #     max_tokens=4000,
            #     temperature=0.3
            # )
            # return response.choices[0].message.content

            # Por enquanto, retorna análise mock
            return await self._mock_ai_response(prompt)

        except Exception as e:
            logger.error(f"Error with OpenAI analysis: {e}")
            return await self._mock_ai_response(prompt)

    async def _mock_ai_response(self, prompt: str) -> str:
        """Gera resposta mock para desenvolvimento/teste"""
        await asyncio.sleep(0.1)  # Simular latência

        if "content analysis" in prompt.lower() or "conteúdo" in prompt.lower():
            return json.dumps({
                "metrics": {
                    "overall_score": 85.0,
                    "technical_precision": 88.0,
                    "pedagogical_quality": 82.0,
                    "clinical_relevance": 87.0,
                    "resource_quality": 80.0
                },
                "difficulty_level": "intermediate",
                "strengths": [
                    "Conteúdo cientificamente preciso e atualizado",
                    "Estrutura didática bem organizada",
                    "Exemplos clínicos relevantes e práticos"
                ],
                "improvements": [
                    "Adicionar mais recursos visuais",
                    "Incluir casos clínicos mais complexos",
                    "Melhorar a interatividade do conteúdo"
                ],
                "recommendations": {
                    "beginners": ["Revisar conceitos básicos antes", "Usar glossário"],
                    "intermediate": ["Focar nos casos práticos", "Fazer exercícios"],
                    "advanced": ["Explorar literatura adicional", "Aplicar em casos reais"]
                },
                "confidence_score": 92.0
            })

        elif "performance" in prompt.lower():
            return json.dumps({
                "metrics": {
                    "overall_performance": 78.0,
                    "accuracy_rate": 80.0,
                    "response_time_avg": 12.5,
                    "retention_rate": 75.0,
                    "consistency_score": 82.0
                },
                "behavioral": {
                    "engagement_level": "high",
                    "learning_style": "visual",
                    "persistence_score": 85.0
                },
                "error_patterns": [
                    {"type": "conceptual", "frequency": "medium", "areas": ["cardiologia"]},
                    {"type": "attention", "frequency": "low", "context": "sessões longas"}
                ],
                "risk_factors": [
                    "Tendência de fadiga em sessões > 45min",
                    "Performance reduzida em tópicos de neurologia"
                ],
                "improvement_suggestions": [
                    {"action": "Reduzir duração das sessões", "priority": "high"},
                    {"action": "Revisar conceitos de neurologia", "priority": "medium"},
                    {"action": "Usar mais recursos visuais", "priority": "medium"}
                ],
                "confidence_score": 88.0
            })

        else:
            return json.dumps({
                "analysis": "Análise mock gerada",
                "confidence_score": 75.0
            })

    async def _mock_content_analysis(
        self,
        content_text: str,
        content_type: ContentType,
        medical_specialty: MedicalSpecialty
    ) -> str:
        """Análise mock específica para conteúdo"""

        # Calcular métricas básicas baseadas no texto
        word_count = len(content_text.split())
        complexity_score = min(100, max(30, word_count / 10))

        # Ajustar scores baseado no tipo de conteúdo
        type_modifiers = {
            ContentType.ARTICLE: {"technical": 5, "pedagogical": 0},
            ContentType.CASE_STUDY: {"technical": 0, "pedagogical": 10},
            ContentType.LECTURE: {"technical": -5, "pedagogical": 5},
            ContentType.FLASHCARD: {"technical": -10, "pedagogical": 15}
        }

        modifier = type_modifiers.get(content_type, {"technical": 0, "pedagogical": 0})

        return json.dumps({
            "metrics": {
                "overall_score": min(100, 70 + complexity_score/5),
                "technical_precision": min(100, 75 + modifier["technical"]),
                "pedagogical_quality": min(100, 70 + modifier["pedagogical"]),
                "clinical_relevance": min(100, 80 + (complexity_score-50)/10),
                "resource_quality": min(100, 65 + word_count/100)
            },
            "difficulty_level": "intermediate" if complexity_score > 60 else "basic",
            "strengths": [
                f"Conteúdo de {content_type.value} bem estruturado",
                f"Adequado para {medical_specialty.value}",
                "Linguagem clara e objetiva"
            ],
            "improvements": [
                "Adicionar mais exemplos práticos",
                "Incluir referências atualizadas",
                "Melhorar organização visual"
            ],
            "recommendations": {
                "beginners": ["Revisar pré-requisitos", "Estudar em etapas"],
                "intermediate": ["Focar na aplicação prática", "Fazer resumos"],
                "advanced": ["Explorar tópicos relacionados", "Ensinar outros"]
            },
            "confidence_score": 85.0
        })

    async def _mock_performance_analysis(self, performance_data: Dict[str, Any]) -> str:
        """Análise mock específica para performance"""

        total_questions = performance_data.get("total_questions", 100)
        correct_answers = performance_data.get("correct_answers", 80)
        accuracy = (correct_answers / max(total_questions, 1)) * 100

        return json.dumps({
            "metrics": {
                "overall_performance": min(100, accuracy + 5),
                "accuracy_rate": accuracy,
                "response_time_avg": performance_data.get("average_response_time", 15.0),
                "retention_rate": min(100, accuracy - 5),
                "consistency_score": min(100, accuracy + np.random.uniform(-10, 10))
            },
            "behavioral": {
                "engagement_level": "high" if accuracy > 80 else "medium",
                "learning_style": "mixed",
                "persistence_score": min(100, accuracy + 10)
            },
            "error_patterns": [
                {"type": "conceptual", "frequency": "low" if accuracy > 85 else "medium"}
            ],
            "risk_factors": [] if accuracy > 75 else ["Performance abaixo da média"],
            "improvement_suggestions": [
                {"action": "Manter ritmo atual", "priority": "low"} if accuracy > 85
                else {"action": "Revisar conceitos básicos", "priority": "high"}
            ],
            "confidence_score": 90.0
        })

    def _structure_content_analysis(self, analysis_result: str) -> Dict[str, Any]:
        """Estrutura resultado da análise de conteúdo"""
        try:
            # Tentar parsear JSON da resposta da IA
            if isinstance(analysis_result, str):
                # Extrair JSON se estiver em markdown ou texto
                json_match = re.search(r'\{.*\}', analysis_result, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                else:
                    result = json.loads(analysis_result)
            else:
                result = analysis_result

            # Garantir estrutura padrão
            structured = {
                "metrics": result.get("metrics", {}),
                "difficulty_level": result.get("difficulty_level", "intermediate"),
                "strengths": result.get("strengths", []),
                "improvements": result.get("improvements", []),
                "recommendations": result.get("recommendations", {}),
                "confidence_score": result.get("confidence_score", 75.0)
            }

            return structured

        except Exception as e:
            logger.error(f"Error structuring content analysis: {e}")
            return self._get_default_content_structure()

    def _structure_performance_analysis(self, analysis_result: str) -> Dict[str, Any]:
        """Estrutura resultado da análise de performance"""
        try:
            # Tentar parsear JSON da resposta da IA
            if isinstance(analysis_result, str):
                json_match = re.search(r'\{.*\}', analysis_result, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                else:
                    result = json.loads(analysis_result)
            else:
                result = analysis_result

            # Garantir estrutura padrão
            structured = {
                "metrics": result.get("metrics", {}),
                "behavioral": result.get("behavioral", {}),
                "error_patterns": result.get("error_patterns", []),
                "risk_factors": result.get("risk_factors", []),
                "improvement_suggestions": result.get("improvement_suggestions", []),
                "predictions": result.get("predictions", {}),
                "confidence_score": result.get("confidence_score", 75.0)
            }

            return structured

        except Exception as e:
            logger.error(f"Error structuring performance analysis: {e}")
            return self._get_default_performance_structure()

    def _calculate_content_metrics(self, content_text: str, content_type: ContentType) -> Dict[str, float]:
        """Calcula métricas adicionais do conteúdo"""
        try:
            # Métricas básicas de texto
            word_count = len(content_text.split())
            sentence_count = len(re.findall(r'[.!?]+', content_text))
            avg_sentence_length = word_count / max(sentence_count, 1)

            # Densidade terminológica (aproximação)
            medical_terms = len(re.findall(r'\b[A-Z][a-z]*(?:ologia|patia|emia|ose|ite|oma)\b', content_text))
            terminology_density = (medical_terms / max(word_count, 1)) * 100

            # Score de legibilidade (aproximação simples)
            readability_score = max(0, min(100, 100 - (avg_sentence_length - 15) * 2))

            # Índice de complexidade
            complexity_index = min(100, (avg_sentence_length * 2) + (terminology_density * 3))

            return {
                "readability_score": readability_score,
                "complexity_index": complexity_index,
                "terminology_density": terminology_density,
                "word_count": word_count,
                "avg_sentence_length": avg_sentence_length
            }

        except Exception as e:
            logger.error(f"Error calculating content metrics: {e}")
            return {}

    def _calculate_performance_metrics(self, performance_data: Dict[str, Any]) -> Dict[str, float]:
        """Calcula métricas adicionais de performance"""
        try:
            total_questions = performance_data.get("total_questions", 0)
            correct_answers = performance_data.get("correct_answers", 0)
            total_sessions = performance_data.get("total_sessions", 0)

            # Métricas derivadas
            accuracy_rate = (correct_answers / max(total_questions, 1)) * 100
            questions_per_session = total_questions / max(total_sessions, 1)

            # Estimativa de engagement baseada em sessões
            engagement_score = min(100, (total_sessions / 30) * 100)  # 30 sessões = 100%

            # Score de consistência (simulado)
            consistency_score = max(0, min(100, accuracy_rate + np.random.uniform(-15, 15)))

            return {
                "questions_per_session": questions_per_session,
                "engagement_score": engagement_score,
                "study_frequency": total_sessions / 30,  # sessões por dia
                "performance_trend": "stable"  # seria calculado com dados históricos
            }

        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {}

    async def _generate_predictions(
        self,
        performance_data: Dict[str, Any],
        current_metrics: Dict[str, float]
    ) -> Dict[str, Any]:
        """Gera predições baseadas nos dados de performance"""
        try:
            accuracy = current_metrics.get("accuracy_rate", 75)
            consistency = current_metrics.get("consistency_score", 75)

            # Predições simples baseadas em regras
            success_probability = min(100, (accuracy + consistency) / 2)

            # Tempo estimado para melhoria
            if accuracy < 70:
                improvement_weeks = 4
            elif accuracy < 85:
                improvement_weeks = 2
            else:
                improvement_weeks = 1

            # Risco de abandono
            risk_level = "low" if accuracy > 80 else "medium" if accuracy > 60 else "high"

            return {
                "success_probability": success_probability,
                "estimated_improvement_weeks": improvement_weeks,
                "risk_level": risk_level,
                "recommended_study_hours_per_week": max(5, 15 - (accuracy / 10)),
                "optimal_session_duration": 30 if accuracy > 80 else 45
            }

        except Exception as e:
            logger.error(f"Error generating predictions: {e}")
            return {}

    async def _fallback_content_analysis(self, content_text: str, content_type: ContentType) -> Dict[str, Any]:
        """Análise de fallback para conteúdo em caso de erro"""
        return {
            "metrics": {
                "overall_score": 75.0,
                "technical_precision": 75.0,
                "pedagogical_quality": 75.0,
                "clinical_relevance": 75.0,
                "resource_quality": 75.0
            },
            "difficulty_level": "intermediate",
            "strengths": ["Conteúdo estruturado", "Linguagem adequada"],
            "improvements": ["Análise detalhada indisponível"],
            "recommendations": {
                "beginners": ["Revisar conceitos básicos"],
                "intermediate": ["Aplicar conhecimento"],
                "advanced": ["Explorar tópicos avançados"]
            },
            "confidence_score": 50.0,
            "metadata": {
                "analysis_timestamp": datetime.now().isoformat(),
                "fallback_mode": True,
                "content_type": content_type.value
            }
        }

    async def _fallback_performance_analysis(self, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """Análise de fallback para performance em caso de erro"""
        total_questions = performance_data.get("total_questions", 100)
        correct_answers = performance_data.get("correct_answers", 75)
        accuracy = (correct_answers / max(total_questions, 1)) * 100

        return {
            "metrics": {
                "overall_performance": accuracy,
                "accuracy_rate": accuracy,
                "response_time_avg": 15.0,
                "retention_rate": accuracy - 5,
                "consistency_score": accuracy
            },
            "behavioral": {
                "engagement_level": "medium",
                "learning_style": "mixed"
            },
            "error_patterns": [],
            "risk_factors": [],
            "improvement_suggestions": [
                {"action": "Continuar estudando regularmente", "priority": "medium"}
            ],
            "predictions": {
                "success_probability": accuracy
            },
            "confidence_score": 40.0,
            "metadata": {
                "analysis_timestamp": datetime.now().isoformat(),
                "fallback_mode": True
            }
        }

    def _get_default_content_structure(self) -> Dict[str, Any]:
        """Estrutura padrão para análise de conteúdo"""
        return {
            "metrics": {
                "overall_score": 70.0,
                "technical_precision": 70.0,
                "pedagogical_quality": 70.0,
                "clinical_relevance": 70.0,
                "resource_quality": 70.0
            },
            "difficulty_level": "intermediate",
            "strengths": [],
            "improvements": [],
            "recommendations": {},
            "confidence_score": 60.0
        }

    def _get_default_performance_structure(self) -> Dict[str, Any]:
        """Estrutura padrão para análise de performance"""
        return {
            "metrics": {
                "overall_performance": 70.0,
                "accuracy_rate": 70.0,
                "response_time_avg": 15.0,
                "retention_rate": 70.0,
                "consistency_score": 70.0
            },
            "behavioral": {},
            "error_patterns": [],
            "risk_factors": [],
            "improvement_suggestions": [],
            "predictions": {},
            "confidence_score": 60.0
        }

    def _get_active_ai_model(self) -> str:
        """Retorna o modelo de IA ativo"""
        if self.anthropic_client:
            return "claude-3-sonnet"
        elif self.openai_client:
            return "gpt-4"
        else:
            return "mock-analyzer"
