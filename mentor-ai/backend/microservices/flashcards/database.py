"""
Database configuration and connection management
"""

from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
import os
from contextlib import asynccontextmanager

class Database:
    """MongoDB database connection manager"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.client: Optional[AsyncIOMotorClient] = None
        self.db: Optional[AsyncIOMotorDatabase] = None
    
    async def connect(self):
        """Establish database connection"""
        self.client = AsyncIOMotorClient(self.connection_string)
        self.db = self.client.flashcards
        
        # Create indexes
        await self._create_indexes()
        
        print("Connected to MongoDB")
    
    async def disconnect(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            print("Disconnected from MongoDB")
    
    async def _create_indexes(self):
        """Create database indexes for performance"""
        # Decks indexes
        await self.db.decks.create_index("user_id")
        await self.db.decks.create_index("is_public")
        await self.db.decks.create_index([("created_at", -1)])
        await self.db.decks.create_index([("clone_count", -1)])
        
        # Cards indexes
        await self.db.cards.create_index("deck_id")
        await self.db.cards.create_index("next_review")
        await self.db.cards.create_index([("deck_id", 1), ("next_review", 1)])
        await self.db.cards.create_index([("$**", "text")])  # Text search
        
        # Reviews indexes
        await self.db.reviews.create_index("user_id")
        await self.db.reviews.create_index("card_id")
        await self.db.reviews.create_index([("timestamp", -1)])
        
        # Sessions indexes
        await self.db.sessions.create_index("user_id")
        await self.db.sessions.create_index([("timestamp", -1)])
    
    @property
    def decks(self):
        """Get decks collection"""
        return self.db.decks
    
    @property
    def cards(self):
        """Get cards collection"""
        return self.db.cards
    
    @property
    def reviews(self):
        """Get reviews collection"""
        return self.db.reviews
    
    @property
    def sessions(self):
        """Get sessions collection"""
        return self.db.sessions
    
    @property
    def users(self):
        """Get users collection"""
        return self.db.users

# Global database instance
_database: Optional[Database] = None

async def get_database() -> Database:
    """Get database instance"""
    global _database
    
    if _database is None:
        connection_string = os.getenv(
            "MONGODB_URL",
            "***************************************"
        )
        _database = Database(connection_string)
    
    return _database

@asynccontextmanager
async def database_session():
    """Database session context manager"""
    db = await get_database()
    try:
        yield db
    finally:
        pass  # Connection pooling handles cleanup