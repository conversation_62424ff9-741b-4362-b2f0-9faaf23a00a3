"""
Redis cache implementation
"""

import json
import pickle
from typing import Optional, Any, Union
import redis.asyncio as redis
from datetime import timedelta
import os

class RedisCache:
    """Redis cache manager"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_url = redis_url or os.getenv(
            "REDIS_URL",
            "redis://redis:6379"
        )
        self.client: Optional[redis.Redis] = None
    
    async def connect(self):
        """Connect to Redis"""
        self.client = redis.from_url(
            self.redis_url,
            encoding="utf-8",
            decode_responses=False
        )
        await self.client.ping()
        print("Connected to Red<PERSON>")
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.client:
            await self.client.close()
            print("Disconnected from Redis")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if not self.client:
            return None
        
        try:
            value = await self.client.get(key)
            if value is None:
                return None
            
            # Try to decode as JSON first
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                # Fall back to pickle
                return pickle.loads(value)
                
        except Exception as e:
            print(f"Cache get error: {e}")
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        expire: Union[int, timedelta, None] = None
    ) -> bool:
        """Set value in cache"""
        if not self.client:
            return False
        
        try:
            # Try to encode as JSON first
            try:
                encoded_value = json.dumps(value)
            except (TypeError, ValueError):
                # Fall back to pickle for complex objects
                encoded_value = pickle.dumps(value)
            
            # Convert timedelta to seconds
            if isinstance(expire, timedelta):
                expire = int(expire.total_seconds())
            
            await self.client.set(key, encoded_value, ex=expire)
            return True
            
        except Exception as e:
            print(f"Cache set error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        if not self.client:
            return False
        
        try:
            result = await self.client.delete(key)
            return result > 0
        except Exception as e:
            print(f"Cache delete error: {e}")
            return False
    
    async def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern"""
        if not self.client:
            return 0
        
        try:
            # Find all keys matching pattern
            keys = []
            async for key in self.client.scan_iter(match=pattern):
                keys.append(key)
            
            # Delete keys in batches
            if keys:
                return await self.client.delete(*keys)
            return 0
            
        except Exception as e:
            print(f"Cache delete pattern error: {e}")
            return 0
    
    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        if not self.client:
            return False
        
        try:
            return await self.client.exists(key) > 0
        except Exception as e:
            print(f"Cache exists error: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration on key"""
        if not self.client:
            return False
        
        try:
            return await self.client.expire(key, seconds)
        except Exception as e:
            print(f"Cache expire error: {e}")
            return False
    
    async def incr(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment counter"""
        if not self.client:
            return None
        
        try:
            return await self.client.incr(key, amount)
        except Exception as e:
            print(f"Cache incr error: {e}")
            return None
    
    async def decr(self, key: str, amount: int = 1) -> Optional[int]:
        """Decrement counter"""
        if not self.client:
            return None
        
        try:
            return await self.client.decr(key, amount)
        except Exception as e:
            print(f"Cache decr error: {e}")
            return None
    
    async def hget(self, name: str, key: str) -> Optional[Any]:
        """Get hash field value"""
        if not self.client:
            return None
        
        try:
            value = await self.client.hget(name, key)
            if value is None:
                return None
            
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return pickle.loads(value)
                
        except Exception as e:
            print(f"Cache hget error: {e}")
            return None
    
    async def hset(self, name: str, key: str, value: Any) -> bool:
        """Set hash field value"""
        if not self.client:
            return False
        
        try:
            try:
                encoded_value = json.dumps(value)
            except (TypeError, ValueError):
                encoded_value = pickle.dumps(value)
            
            await self.client.hset(name, key, encoded_value)
            return True
            
        except Exception as e:
            print(f"Cache hset error: {e}")
            return False
    
    async def hdel(self, name: str, *keys: str) -> int:
        """Delete hash fields"""
        if not self.client:
            return 0
        
        try:
            return await self.client.hdel(name, *keys)
        except Exception as e:
            print(f"Cache hdel error: {e}")
            return 0
    
    async def hgetall(self, name: str) -> dict:
        """Get all hash fields"""
        if not self.client:
            return {}
        
        try:
            raw_data = await self.client.hgetall(name)
            result = {}
            
            for key, value in raw_data.items():
                try:
                    result[key] = json.loads(value)
                except json.JSONDecodeError:
                    result[key] = pickle.loads(value)
            
            return result
            
        except Exception as e:
            print(f"Cache hgetall error: {e}")
            return {}

# Cache key generators
def deck_cache_key(user_id: str, limit: int, offset: int) -> str:
    """Generate cache key for deck list"""
    return f"decks:{user_id}:{limit}:{offset}"

def cards_cache_key(deck_id: str, limit: int, offset: int) -> str:
    """Generate cache key for card list"""
    return f"cards:{deck_id}:{limit}:{offset}"

def user_stats_key(user_id: str) -> str:
    """Generate cache key for user statistics"""
    return f"stats:{user_id}"

def learning_path_key(user_id: str) -> str:
    """Generate cache key for learning path"""
    return f"learning_path:{user_id}"