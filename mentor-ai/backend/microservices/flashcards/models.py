"""
Data models for Flashcards microservice
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class DifficultyLevel(str, Enum):
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"
    EXPERT = "expert"

class StudyIntensity(str, Enum):
    LIGHT = "light"
    MODERATE = "moderate"
    INTENSE = "intense"

class ContentType(str, Enum):
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    MIXED = "mixed"

# User model
class User(BaseModel):
    id: str
    email: str
    name: str
    expertise_level: float = Field(default=0.5, ge=0, le=1)
    preferences: Dict[str, Any] = Field(default_factory=dict)

# Deck models
class DeckBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    tags: List[str] = Field(default_factory=list)
    is_public: bool = Field(default=False)

class DeckCreate(DeckBase):
    pass

class DeckUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None

class Deck(DeckBase):
    id: str
    user_id: str
    card_count: int = 0
    progress: float = Field(default=0, ge=0, le=100)
    last_studied: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    average_rating: Optional[float] = Field(None, ge=0, le=5)
    clone_count: int = 0
    cloned_from: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Flashcard models
class FlashcardBase(BaseModel):
    front: str = Field(..., min_length=1)
    back: str = Field(..., min_length=1)
    tags: List[str] = Field(default_factory=list)
    difficulty: float = Field(default=0.5, ge=0, le=1)
    content_type: ContentType = ContentType.TEXT
    media_urls: Optional[Dict[str, str]] = None
    hints: Optional[List[str]] = None
    explanation: Optional[str] = None
    mnemonics: Optional[List[str]] = None

class FlashcardCreate(FlashcardBase):
    pass

class FlashcardUpdate(BaseModel):
    front: Optional[str] = Field(None, min_length=1)
    back: Optional[str] = Field(None, min_length=1)
    tags: Optional[List[str]] = None
    difficulty: Optional[float] = Field(None, ge=0, le=1)
    content_type: Optional[ContentType] = None
    media_urls: Optional[Dict[str, str]] = None
    hints: Optional[List[str]] = None
    explanation: Optional[str] = None
    mnemonics: Optional[List[str]] = None

class Flashcard(FlashcardBase):
    id: str
    deck_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    last_reviewed: Optional[datetime] = None
    next_review: Optional[datetime] = None
    optimal_time: Optional[datetime] = None
    review_count: int = 0
    success_rate: float = Field(default=0, ge=0, le=1)
    average_response_time: Optional[float] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Study models
class ReviewRecord(BaseModel):
    card_id: str
    correct: bool
    response_time: float = Field(..., gt=0)  # milliseconds
    difficulty: float = Field(..., ge=0, le=1)
    confidence: Optional[float] = Field(None, ge=0, le=1)

class StudySession(BaseModel):
    duration: float  # minutes
    cards_reviewed: int
    accuracy: float = Field(..., ge=0, le=1)
    new_cards: int = 0
    stats: Dict[str, Any] = Field(default_factory=dict)
    intensity: StudyIntensity = StudyIntensity.MODERATE

class ReviewSchedule(BaseModel):
    card_id: str
    next_review: datetime
    optimal_time: datetime
    difficulty: float
    priority: float = Field(..., ge=0, le=1)
    estimated_retention: float = Field(..., ge=0, le=1)

# Learning models
class LearningPath(BaseModel):
    user_id: str
    schedule: List[Dict[str, Any]]
    content: List[str]  # Card IDs
    adaptive_rules: List[Dict[str, Any]]
    estimated_outcomes: Dict[str, Any]
    last_updated: datetime

class CognitiveLoad(BaseModel):
    current: float = Field(..., ge=0, le=1)
    optimal: float = Field(..., ge=0, le=1)
    factors: Dict[str, float]

class RetentionCurve(BaseModel):
    immediate: float = Field(..., ge=0, le=1)
    one_day: float = Field(..., ge=0, le=1)
    one_week: float = Field(..., ge=0, le=1)
    one_month: float = Field(..., ge=0, le=1)
    six_months: float = Field(..., ge=0, le=1)

# Analytics models
class LearningMetrics(BaseModel):
    daily: Dict[str, Any]
    weekly: Dict[str, Any]
    monthly: Dict[str, Any]
    all_time: Dict[str, Any]

class PerformanceMetrics(BaseModel):
    average_accuracy: float = Field(..., ge=0, le=1)
    average_speed: float  # seconds
    retention_rate: float = Field(..., ge=0, le=1)
    learning_velocity: float
    mastery_level: float = Field(..., ge=0, le=1)

# AI models
class AIGenerateRequest(BaseModel):
    topic: str = Field(..., min_length=1, max_length=500)
    count: int = Field(default=10, ge=1, le=50)
    difficulty: str = Field(default="mixed")
    deck_id: Optional[str] = None
    language: str = Field(default="en")
    style: str = Field(default="concise")

class AICardImprovement(BaseModel):
    card_id: str
    improvement_type: str = Field(default="all")  # all, clarity, mnemonics, examples

# Import/Export models
class DeckExport(BaseModel):
    deck: Deck
    cards: List[Flashcard]
    version: str = "2.0.0"
    exported_at: datetime

class DeckImport(BaseModel):
    name: Optional[str] = None
    cards: List[FlashcardBase]
    format: str = Field(default="native")  # native, anki, quizlet

# Search models
class SearchRequest(BaseModel):
    query: str = Field(..., min_length=1)
    filters: Optional[Dict[str, Any]] = None
    limit: int = Field(default=50, ge=1, le=200)
    offset: int = Field(default=0, ge=0)

class SearchResult(BaseModel):
    card: Flashcard
    score: float
    semantic_score: Optional[float] = None
    highlights: Optional[List[str]] = None

# Community models
class DeckRating(BaseModel):
    deck_id: str
    rating: int = Field(..., ge=1, le=5)
    comment: Optional[str] = Field(None, max_length=500)

class ContentReport(BaseModel):
    content_id: str
    content_type: str = Field(..., regex="^(deck|card)$")
    reason: str = Field(..., min_length=10, max_length=500)
    details: Optional[str] = Field(None, max_length=1000)