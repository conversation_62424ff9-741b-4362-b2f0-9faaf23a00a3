"""
Event-driven architecture implementation using Kafka
"""

from typing import Dict, Any, Optional, AsyncIterator
from datetime import datetime
import json
import asyncio
from dataclasses import dataclass, asdict

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from aiokafka.errors import KafkaError
import logging

logger = logging.getLogger(__name__)

# Kafka configuration
KAFKA_BOOTSTRAP_SERVERS = "localhost:9092"
KAFKA_GROUP_ID = "flashcards-service"

@dataclass
class Event:
    """Base event structure"""
    type: str
    payload: Dict[str, Any]
    timestamp: Optional[datetime] = None
    correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
    
    def to_json(self) -> str:
        """Convert event to JSON string"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return json.dumps(data)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Event':
        """Create event from JSON string"""
        data = json.loads(json_str)
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)

class EventProducer:
    """Kafka event producer"""
    
    def __init__(self, bootstrap_servers: str = KAFKA_BOOTSTRAP_SERVERS):
        self.bootstrap_servers = bootstrap_servers
        self.producer: Optional[AIOKafkaProducer] = None
    
    async def start(self):
        """Start the producer"""
        self.producer = AIOKafkaProducer(
            bootstrap_servers=self.bootstrap_servers,
            value_serializer=lambda v: v.encode('utf-8'),
            key_serializer=lambda k: k.encode('utf-8') if k else None,
            compression_type="gzip",
            acks='all',
            enable_idempotence=True,
            max_in_flight_requests_per_connection=5
        )
        await self.producer.start()
        logger.info("Event producer started")
    
    async def stop(self):
        """Stop the producer"""
        if self.producer:
            await self.producer.stop()
            logger.info("Event producer stopped")
    
    async def send(self, event: Event, topic: Optional[str] = None):
        """Send event to Kafka"""
        if not self.producer:
            raise RuntimeError("Producer not started")
        
        # Determine topic from event type if not specified
        if topic is None:
            topic = self._get_topic_from_event_type(event.type)
        
        try:
            # Send event
            await self.producer.send(
                topic=topic,
                key=event.correlation_id,
                value=event.to_json()
            )
            
            logger.debug(f"Event sent: {event.type} to topic {topic}")
            
        except KafkaError as e:
            logger.error(f"Failed to send event: {e}")
            raise
    
    async def send_batch(self, events: list[Event]):
        """Send multiple events in a batch"""
        if not self.producer:
            raise RuntimeError("Producer not started")
        
        # Group events by topic
        events_by_topic: Dict[str, list[Event]] = {}
        for event in events:
            topic = self._get_topic_from_event_type(event.type)
            if topic not in events_by_topic:
                events_by_topic[topic] = []
            events_by_topic[topic].append(event)
        
        # Send batches
        tasks = []
        for topic, topic_events in events_by_topic.items():
            for event in topic_events:
                task = self.producer.send(
                    topic=topic,
                    key=event.correlation_id,
                    value=event.to_json()
                )
                tasks.append(task)
        
        # Wait for all sends to complete
        await asyncio.gather(*tasks)
        
        logger.debug(f"Batch of {len(events)} events sent")
    
    def _get_topic_from_event_type(self, event_type: str) -> str:
        """Extract topic from event type"""
        # Event types follow pattern: entity.action
        # Map to topics: flashcards.entity
        entity = event_type.split('.')[0]
        return f"flashcards.{entity}"

class EventConsumer:
    """Kafka event consumer"""
    
    def __init__(
        self,
        bootstrap_servers: str = KAFKA_BOOTSTRAP_SERVERS,
        group_id: str = KAFKA_GROUP_ID
    ):
        self.bootstrap_servers = bootstrap_servers
        self.group_id = group_id
        self.consumer: Optional[AIOKafkaConsumer] = None
        self.running = False
        self.handlers: Dict[str, list] = {}
    
    async def start(self):
        """Start the consumer"""
        self.consumer = AIOKafkaConsumer(
            bootstrap_servers=self.bootstrap_servers,
            group_id=self.group_id,
            value_deserializer=lambda v: v.decode('utf-8'),
            key_deserializer=lambda k: k.decode('utf-8') if k else None,
            auto_offset_reset='earliest',
            enable_auto_commit=False
        )
        
        # Subscribe to all relevant topics
        topics = [
            "flashcards.deck",
            "flashcards.card",
            "flashcards.user",
            "flashcards.session",
            "analytics.learning",
            "ai.processing"
        ]
        
        self.consumer.subscribe(topics)
        await self.consumer.start()
        self.running = True
        logger.info(f"Event consumer started for topics: {topics}")
        
        # Start consuming in background
        asyncio.create_task(self._consume_loop())
    
    async def stop(self):
        """Stop the consumer"""
        self.running = False
        if self.consumer:
            await self.consumer.stop()
            logger.info("Event consumer stopped")
    
    def register_handler(self, event_pattern: str, handler):
        """Register event handler"""
        if event_pattern not in self.handlers:
            self.handlers[event_pattern] = []
        self.handlers[event_pattern].append(handler)
        logger.info(f"Registered handler for pattern: {event_pattern}")
    
    async def _consume_loop(self):
        """Main consumer loop"""
        while self.running:
            try:
                # Fetch messages
                records = await self.consumer.getmany(timeout_ms=1000)
                
                for topic_partition, messages in records.items():
                    for message in messages:
                        try:
                            # Parse event
                            event = Event.from_json(message.value)
                            
                            # Process event
                            await self._process_event(event)
                            
                        except Exception as e:
                            logger.error(f"Error processing message: {e}")
                
                # Commit offsets
                await self.consumer.commit()
                
            except Exception as e:
                logger.error(f"Consumer error: {e}")
                await asyncio.sleep(5)  # Wait before retry
    
    async def _process_event(self, event: Event):
        """Process single event"""
        logger.debug(f"Processing event: {event.type}")
        
        # Find matching handlers
        for pattern, handlers in self.handlers.items():
            if self._match_pattern(pattern, event.type):
                for handler in handlers:
                    try:
                        await handler(event)
                    except Exception as e:
                        logger.error(f"Handler error for {event.type}: {e}")
    
    def _match_pattern(self, pattern: str, event_type: str) -> bool:
        """Check if event type matches pattern"""
        # Support wildcards: deck.* matches deck.created, deck.updated, etc.
        if pattern.endswith('*'):
            prefix = pattern[:-1]
            return event_type.startswith(prefix)
        return pattern == event_type
    
    async def consume(self, event_pattern: str) -> AsyncIterator[Event]:
        """Consume events matching pattern"""
        if not self.consumer:
            raise RuntimeError("Consumer not started")
        
        # Create a queue for this consumer
        queue = asyncio.Queue()
        
        # Register handler that puts events in queue
        async def queue_handler(event: Event):
            if self._match_pattern(event_pattern, event.type):
                await queue.put(event)
        
        self.register_handler(event_pattern, queue_handler)
        
        # Yield events from queue
        while self.running:
            try:
                event = await asyncio.wait_for(queue.get(), timeout=1.0)
                yield event
            except asyncio.TimeoutError:
                continue

# Event schemas for different domains
class DeckEvents:
    """Deck-related events"""
    
    @staticmethod
    def created(deck_id: str, user_id: str, name: str) -> Event:
        return Event(
            type="deck.created",
            payload={
                "deck_id": deck_id,
                "user_id": user_id,
                "name": name
            },
            user_id=user_id
        )
    
    @staticmethod
    def updated(deck_id: str, updates: Dict[str, Any]) -> Event:
        return Event(
            type="deck.updated",
            payload={
                "deck_id": deck_id,
                "updates": updates
            }
        )
    
    @staticmethod
    def deleted(deck_id: str, user_id: str) -> Event:
        return Event(
            type="deck.deleted",
            payload={
                "deck_id": deck_id,
                "user_id": user_id
            },
            user_id=user_id
        )
    
    @staticmethod
    def shared(deck_id: str, user_id: str, is_public: bool) -> Event:
        return Event(
            type="deck.shared",
            payload={
                "deck_id": deck_id,
                "user_id": user_id,
                "is_public": is_public
            },
            user_id=user_id
        )
    
    @staticmethod
    def cloned(source_deck_id: str, cloned_deck_id: str, user_id: str) -> Event:
        return Event(
            type="deck.cloned",
            payload={
                "source_deck_id": source_deck_id,
                "cloned_deck_id": cloned_deck_id,
                "user_id": user_id
            },
            user_id=user_id
        )

class CardEvents:
    """Card-related events"""
    
    @staticmethod
    def created(card_id: str, deck_id: str, user_id: str) -> Event:
        return Event(
            type="card.created",
            payload={
                "card_id": card_id,
                "deck_id": deck_id,
                "user_id": user_id
            },
            user_id=user_id
        )
    
    @staticmethod
    def updated(card_id: str, updates: Dict[str, Any]) -> Event:
        return Event(
            type="card.updated",
            payload={
                "card_id": card_id,
                "updates": updates
            }
        )
    
    @staticmethod
    def reviewed(
        card_id: str,
        user_id: str,
        correct: bool,
        response_time: float
    ) -> Event:
        return Event(
            type="card.reviewed",
            payload={
                "card_id": card_id,
                "user_id": user_id,
                "correct": correct,
                "response_time": response_time
            },
            user_id=user_id
        )
    
    @staticmethod
    def scheduled(
        card_id: str,
        next_review: datetime,
        optimal_time: datetime
    ) -> Event:
        return Event(
            type="card.scheduled",
            payload={
                "card_id": card_id,
                "next_review": next_review.isoformat(),
                "optimal_time": optimal_time.isoformat()
            }
        )

class SessionEvents:
    """Study session events"""
    
    @staticmethod
    def started(session_id: str, user_id: str, deck_id: Optional[str]) -> Event:
        return Event(
            type="session.started",
            payload={
                "session_id": session_id,
                "user_id": user_id,
                "deck_id": deck_id
            },
            user_id=user_id
        )
    
    @staticmethod
    def completed(
        session_id: str,
        user_id: str,
        duration: float,
        cards_reviewed: int,
        accuracy: float
    ) -> Event:
        return Event(
            type="session.completed",
            payload={
                "session_id": session_id,
                "user_id": user_id,
                "duration": duration,
                "cards_reviewed": cards_reviewed,
                "accuracy": accuracy
            },
            user_id=user_id
        )
    
    @staticmethod
    def paused(session_id: str, user_id: str) -> Event:
        return Event(
            type="session.paused",
            payload={
                "session_id": session_id,
                "user_id": user_id
            },
            user_id=user_id
        )

class LearningEvents:
    """Learning analytics events"""
    
    @staticmethod
    def milestone_reached(
        user_id: str,
        milestone_type: str,
        value: Any
    ) -> Event:
        return Event(
            type="learning.milestone_reached",
            payload={
                "user_id": user_id,
                "milestone_type": milestone_type,
                "value": value
            },
            user_id=user_id
        )
    
    @staticmethod
    def pattern_detected(
        user_id: str,
        pattern_type: str,
        details: Dict[str, Any]
    ) -> Event:
        return Event(
            type="learning.pattern_detected",
            payload={
                "user_id": user_id,
                "pattern_type": pattern_type,
                "details": details
            },
            user_id=user_id
        )
    
    @staticmethod
    def recommendation_generated(
        user_id: str,
        recommendations: list[Dict[str, Any]]
    ) -> Event:
        return Event(
            type="learning.recommendation_generated",
            payload={
                "user_id": user_id,
                "recommendations": recommendations
            },
            user_id=user_id
        )