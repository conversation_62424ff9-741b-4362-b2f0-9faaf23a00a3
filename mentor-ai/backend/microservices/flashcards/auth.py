"""
Authentication and authorization utilities
"""

from typing import Optional
from datetime import datetime, timedelta
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
import os

from .models import User

# Security configuration
SECRET_KEY = os.getenv("JWT_SECRET", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 24 hours

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Bearer token
security = HTTPBearer()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    
    return encoded_jwt

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """Get current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(
            credentials.credentials,
            SECRET_KEY,
            algorithms=[ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    # For now, return a mock user
    # In production, fetch from user service
    user = User(
        id=user_id,
        email=payload.get("email", "<EMAIL>"),
        name=payload.get("name", "User"),
        expertise_level=payload.get("expertise_level", 0.5),
        preferences=payload.get("preferences", {})
    )
    
    return user

def require_auth(user: User = Depends(get_current_user)):
    """Require authentication dependency"""
    return user

class RoleChecker:
    """Check user roles"""
    
    def __init__(self, allowed_roles: list[str]):
        self.allowed_roles = allowed_roles
    
    def __call__(self, user: User = Depends(get_current_user)):
        user_roles = user.preferences.get("roles", ["user"])
        
        for role in user_roles:
            if role in self.allowed_roles:
                return user
        
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )

# Role dependencies
require_admin = RoleChecker(["admin", "superadmin"])
require_moderator = RoleChecker(["admin", "moderator"])

# API Key authentication for service-to-service
class APIKeyAuth:
    """API Key authentication for internal services"""
    
    def __init__(self):
        self.api_keys = {
            "ai-service": os.getenv("AI_SERVICE_API_KEY", "ai-service-key"),
            "analytics-service": os.getenv("ANALYTICS_SERVICE_API_KEY", "analytics-service-key"),
            "user-service": os.getenv("USER_SERVICE_API_KEY", "user-service-key")
        }
    
    def __call__(self, api_key: str = Depends(security)) -> str:
        if api_key.credentials not in self.api_keys.values():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid API key"
            )
        
        # Return service name
        for service, key in self.api_keys.items():
            if key == api_key.credentials:
                return service
        
        return "unknown"

require_service_auth = APIKeyAuth()