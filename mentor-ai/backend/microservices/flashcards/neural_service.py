"""
Neural Service Client for AI operations
"""

import httpx
from typing import Optional, List, Dict, Any
from datetime import datetime
import os
from tenacity import retry, stop_after_attempt, wait_exponential

from .models import ReviewSchedule

class NeuralServiceClient:
    """Client for Neural AI Service"""
    
    def __init__(self, base_url: Optional[str] = None):
        self.base_url = base_url or os.getenv(
            "AI_SERVICE_URL",
            "http://ai-service:8002"
        )
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=30.0
        )
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10)
    )
    async def calculate_initial_schedule(
        self,
        card_id: str,
        user_id: str,
        difficulty: float
    ) -> ReviewSchedule:
        """Calculate initial review schedule for new card"""
        try:
            response = await self.client.post(
                "/api/ai/schedule/initial",
                json={
                    "card_id": card_id,
                    "user_id": user_id,
                    "difficulty": difficulty
                }
            )
            response.raise_for_status()
            
            data = response.json()
            return ReviewSchedule(**data)
            
        except Exception as e:
            print(f"Neural scheduling error: {e}")
            # Fallback to default schedule
            now = datetime.utcnow()
            return ReviewSchedule(
                card_id=card_id,
                next_review=now,
                optimal_time=now,
                difficulty=difficulty,
                priority=0.5,
                estimated_retention=0.9
            )
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10)
    )
    async def calculate_next_review(
        self,
        card_id: str,
        user_id: str,
        correct: bool,
        response_time: float,
        difficulty: float
    ) -> ReviewSchedule:
        """Calculate next review schedule based on performance"""
        try:
            response = await self.client.post(
                "/api/ai/schedule/next",
                json={
                    "card_id": card_id,
                    "user_id": user_id,
                    "correct": correct,
                    "response_time": response_time,
                    "difficulty": difficulty
                }
            )
            response.raise_for_status()
            
            data = response.json()
            return ReviewSchedule(**data)
            
        except Exception as e:
            print(f"Neural scheduling error: {e}")
            # Fallback to simple scheduling
            from datetime import timedelta
            
            if correct:
                days = 1 if difficulty > 0.7 else 3
            else:
                days = 0.5
            
            next_review = datetime.utcnow() + timedelta(days=days)
            
            return ReviewSchedule(
                card_id=card_id,
                next_review=next_review,
                optimal_time=next_review,
                difficulty=difficulty,
                priority=0.5,
                estimated_retention=0.7 if correct else 0.3
            )
    
    async def optimize_review_order(
        self,
        user_id: str,
        card_ids: List[str]
    ) -> List[str]:
        """Optimize the order of cards for review"""
        try:
            response = await self.client.post(
                "/api/ai/optimize/review-order",
                json={
                    "user_id": user_id,
                    "card_ids": card_ids
                }
            )
            response.raise_for_status()
            
            return response.json()["optimized_order"]
            
        except Exception as e:
            print(f"Review optimization error: {e}")
            # Return original order as fallback
            return card_ids
    
    async def generate_cards(
        self,
        topic: str,
        count: int,
        difficulty: str,
        user_id: str
    ) -> List[Dict[str, Any]]:
        """Generate AI flashcards"""
        try:
            response = await self.client.post(
                "/api/ai/generate/flashcards",
                json={
                    "topic": topic,
                    "count": count,
                    "difficulty": difficulty,
                    "user_id": user_id
                }
            )
            response.raise_for_status()
            
            return response.json()["cards"]
            
        except Exception as e:
            print(f"Card generation error: {e}")
            # Return empty list as fallback
            return []
    
    async def semantic_search(
        self,
        query: str,
        card_ids: List[str]
    ) -> Dict[str, float]:
        """Perform semantic search on cards"""
        try:
            response = await self.client.post(
                "/api/ai/search/semantic",
                json={
                    "query": query,
                    "card_ids": card_ids
                }
            )
            response.raise_for_status()
            
            return response.json()["scores"]
            
        except Exception as e:
            print(f"Semantic search error: {e}")
            # Return empty scores
            return {}
    
    async def update_learning_models(
        self,
        user_id: str,
        review_data: Dict[str, Any]
    ):
        """Update learning models with review data"""
        try:
            response = await self.client.post(
                "/api/ai/models/update",
                json={
                    "user_id": user_id,
                    "review_data": review_data
                }
            )
            response.raise_for_status()
            
        except Exception as e:
            print(f"Model update error: {e}")
            # Non-critical, continue