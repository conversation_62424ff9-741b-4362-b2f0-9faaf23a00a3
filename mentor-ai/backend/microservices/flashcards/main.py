"""
Flashcards Microservice
Event-driven architecture with FastAPI
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

from motor.motor_asyncio import AsyncIOMotorClient
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
import json

from .models import (
    Deck, DeckCreate, DeckUpdate,
    Flashcard, FlashcardCreate, FlashcardUpdate,
    StudySession, ReviewRecord,
    User
)
from .database import get_database, Database
from .events import EventProducer, EventConsumer, Event
from .auth import get_current_user, require_auth
from .neural_service import NeuralServiceClient
from .cache import RedisCache

# Global instances
db: Optional[Database] = None
event_producer: Optional[EventProducer] = None
event_consumer: Optional[EventConsumer] = None
neural_client: Optional[NeuralServiceClient] = None
cache: Optional[RedisCache] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage service lifecycle"""
    global db, event_producer, event_consumer, neural_client, cache
    
    # Startup
    print("Starting Flashcards Microservice...")
    
    # Initialize database
    db = await get_database()
    await db.connect()
    
    # Initialize Kafka
    event_producer = EventProducer()
    await event_producer.start()
    
    event_consumer = EventConsumer()
    asyncio.create_task(event_consumer.start())
    
    # Initialize neural service client
    neural_client = NeuralServiceClient()
    
    # Initialize cache
    cache = RedisCache()
    await cache.connect()
    
    print("Flashcards Microservice started successfully")
    
    yield
    
    # Shutdown
    print("Shutting down Flashcards Microservice...")
    
    await event_producer.stop()
    await event_consumer.stop()
    await db.disconnect()
    await cache.disconnect()
    
    print("Flashcards Microservice shut down")

# Create FastAPI app
app = FastAPI(
    title="Flashcards Microservice",
    version="2.0.0",
    description="Neural-powered flashcards service with spaced repetition",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check
@app.get("/health")
async def health_check():
    """Service health check"""
    return {
        "status": "healthy",
        "service": "flashcards",
        "version": "2.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }

# Deck endpoints
@app.get("/api/flashcards/decks", response_model=List[Deck])
async def get_decks(
    user: User = Depends(get_current_user),
    limit: int = 50,
    offset: int = 0
):
    """Get user's decks"""
    # Check cache
    cache_key = f"decks:{user.id}:{limit}:{offset}"
    cached = await cache.get(cache_key)
    if cached:
        return cached
    
    # Query database
    decks = await db.decks.find(
        {"user_id": user.id}
    ).skip(offset).limit(limit).to_list(limit)
    
    # Cache result
    await cache.set(cache_key, decks, expire=300)  # 5 minutes
    
    return decks

@app.get("/api/flashcards/decks/{deck_id}", response_model=Deck)
async def get_deck(
    deck_id: str,
    user: User = Depends(get_current_user)
):
    """Get specific deck"""
    deck = await db.decks.find_one({
        "_id": deck_id,
        "$or": [
            {"user_id": user.id},
            {"is_public": True}
        ]
    })
    
    if not deck:
        raise HTTPException(status_code=404, detail="Deck not found")
    
    return deck

@app.post("/api/flashcards/decks", response_model=Deck)
async def create_deck(
    deck_data: DeckCreate,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_current_user)
):
    """Create new deck"""
    deck = Deck(
        id=str(uuid.uuid4()),
        user_id=user.id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        card_count=0,
        progress=0,
        **deck_data.dict()
    )
    
    # Save to database
    await db.decks.insert_one(deck.dict())
    
    # Emit event
    event = Event(
        type="deck.created",
        payload={
            "deck_id": deck.id,
            "user_id": user.id,
            "name": deck.name
        }
    )
    await event_producer.send(event)
    
    # Invalidate cache
    background_tasks.add_task(
        cache.delete_pattern, f"decks:{user.id}:*"
    )
    
    return deck

@app.put("/api/flashcards/decks/{deck_id}", response_model=Deck)
async def update_deck(
    deck_id: str,
    updates: DeckUpdate,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_current_user)
):
    """Update deck"""
    # Verify ownership
    deck = await db.decks.find_one({
        "_id": deck_id,
        "user_id": user.id
    })
    
    if not deck:
        raise HTTPException(status_code=404, detail="Deck not found")
    
    # Update
    update_data = updates.dict(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow()
    
    await db.decks.update_one(
        {"_id": deck_id},
        {"$set": update_data}
    )
    
    # Get updated deck
    updated_deck = await db.decks.find_one({"_id": deck_id})
    
    # Emit event
    event = Event(
        type="deck.updated",
        payload={
            "deck_id": deck_id,
            "updates": update_data
        }
    )
    await event_producer.send(event)
    
    # Invalidate cache
    background_tasks.add_task(
        cache.delete_pattern, f"decks:{user.id}:*"
    )
    
    return updated_deck

@app.delete("/api/flashcards/decks/{deck_id}")
async def delete_deck(
    deck_id: str,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_current_user)
):
    """Delete deck and all its cards"""
    # Verify ownership
    deck = await db.decks.find_one({
        "_id": deck_id,
        "user_id": user.id
    })
    
    if not deck:
        raise HTTPException(status_code=404, detail="Deck not found")
    
    # Delete deck and cards
    await db.decks.delete_one({"_id": deck_id})
    await db.cards.delete_many({"deck_id": deck_id})
    
    # Emit event
    event = Event(
        type="deck.deleted",
        payload={
            "deck_id": deck_id,
            "user_id": user.id
        }
    )
    await event_producer.send(event)
    
    # Invalidate cache
    background_tasks.add_task(
        cache.delete_pattern, f"decks:{user.id}:*"
    )
    background_tasks.add_task(
        cache.delete_pattern, f"cards:{deck_id}:*"
    )
    
    return {"message": "Deck deleted successfully"}

# Card endpoints
@app.get("/api/flashcards/decks/{deck_id}/cards", response_model=List[Flashcard])
async def get_deck_cards(
    deck_id: str,
    user: User = Depends(get_current_user),
    limit: int = 100,
    offset: int = 0
):
    """Get cards in a deck"""
    # Verify access
    deck = await db.decks.find_one({
        "_id": deck_id,
        "$or": [
            {"user_id": user.id},
            {"is_public": True}
        ]
    })
    
    if not deck:
        raise HTTPException(status_code=404, detail="Deck not found")
    
    # Check cache
    cache_key = f"cards:{deck_id}:{limit}:{offset}"
    cached = await cache.get(cache_key)
    if cached:
        return cached
    
    # Query cards
    cards = await db.cards.find(
        {"deck_id": deck_id}
    ).skip(offset).limit(limit).to_list(limit)
    
    # Cache result
    await cache.set(cache_key, cards, expire=300)
    
    return cards

@app.post("/api/flashcards/decks/{deck_id}/cards", response_model=Flashcard)
async def create_card(
    deck_id: str,
    card_data: FlashcardCreate,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_current_user)
):
    """Create new card"""
    # Verify deck ownership
    deck = await db.decks.find_one({
        "_id": deck_id,
        "user_id": user.id
    })
    
    if not deck:
        raise HTTPException(status_code=404, detail="Deck not found")
    
    # Create card
    card = Flashcard(
        id=str(uuid.uuid4()),
        deck_id=deck_id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        review_count=0,
        success_rate=0,
        **card_data.dict()
    )
    
    # Save to database
    await db.cards.insert_one(card.dict())
    
    # Update deck card count
    await db.decks.update_one(
        {"_id": deck_id},
        {
            "$inc": {"card_count": 1},
            "$set": {"updated_at": datetime.utcnow()}
        }
    )
    
    # Get initial schedule from neural engine
    if neural_client:
        try:
            schedule = await neural_client.calculate_initial_schedule(
                card_id=card.id,
                user_id=user.id,
                difficulty=card.difficulty
            )
            
            # Update card with schedule
            await db.cards.update_one(
                {"_id": card.id},
                {"$set": {
                    "next_review": schedule.next_review,
                    "optimal_time": schedule.optimal_time
                }}
            )
        except Exception as e:
            print(f"Neural scheduling failed: {e}")
    
    # Emit event
    event = Event(
        type="card.created",
        payload={
            "card_id": card.id,
            "deck_id": deck_id,
            "user_id": user.id
        }
    )
    await event_producer.send(event)
    
    # Invalidate cache
    background_tasks.add_task(
        cache.delete_pattern, f"cards:{deck_id}:*"
    )
    
    return card

@app.put("/api/flashcards/cards/{card_id}", response_model=Flashcard)
async def update_card(
    card_id: str,
    updates: FlashcardUpdate,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_current_user)
):
    """Update card"""
    # Get card with deck info
    card = await db.cards.find_one({"_id": card_id})
    if not card:
        raise HTTPException(status_code=404, detail="Card not found")
    
    # Verify ownership through deck
    deck = await db.decks.find_one({
        "_id": card["deck_id"],
        "user_id": user.id
    })
    
    if not deck:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Update card
    update_data = updates.dict(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow()
    
    await db.cards.update_one(
        {"_id": card_id},
        {"$set": update_data}
    )
    
    # Get updated card
    updated_card = await db.cards.find_one({"_id": card_id})
    
    # Emit event
    event = Event(
        type="card.updated",
        payload={
            "card_id": card_id,
            "updates": update_data
        }
    )
    await event_producer.send(event)
    
    # Invalidate cache
    background_tasks.add_task(
        cache.delete_pattern, f"cards:{card['deck_id']}:*"
    )
    
    return updated_card

# Study endpoints
@app.get("/api/flashcards/study/due", response_model=List[Flashcard])
async def get_cards_for_review(
    user: User = Depends(get_current_user),
    limit: int = 20
):
    """Get cards due for review"""
    # Get all user's decks
    user_decks = await db.decks.find(
        {"user_id": user.id}
    ).to_list(None)
    
    deck_ids = [deck["_id"] for deck in user_decks]
    
    # Find due cards
    now = datetime.utcnow()
    due_cards = await db.cards.find({
        "deck_id": {"$in": deck_ids},
        "$or": [
            {"next_review": None},
            {"next_review": {"$lte": now}}
        ]
    }).sort("next_review", 1).limit(limit).to_list(limit)
    
    # Get optimal order from neural engine
    if neural_client and due_cards:
        try:
            card_ids = [card["_id"] for card in due_cards]
            optimal_order = await neural_client.optimize_review_order(
                user_id=user.id,
                card_ids=card_ids
            )
            
            # Reorder cards
            card_map = {card["_id"]: card for card in due_cards}
            due_cards = [card_map[card_id] for card_id in optimal_order if card_id in card_map]
        except Exception as e:
            print(f"Review optimization failed: {e}")
    
    return due_cards

@app.post("/api/flashcards/study/review")
async def record_review(
    review: ReviewRecord,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_current_user)
):
    """Record card review result"""
    # Get card
    card = await db.cards.find_one({"_id": review.card_id})
    if not card:
        raise HTTPException(status_code=404, detail="Card not found")
    
    # Verify access through deck
    deck = await db.decks.find_one({
        "_id": card["deck_id"],
        "user_id": user.id
    })
    
    if not deck:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Calculate new schedule with neural engine
    schedule = None
    if neural_client:
        try:
            schedule = await neural_client.calculate_next_review(
                card_id=review.card_id,
                user_id=user.id,
                correct=review.correct,
                response_time=review.response_time,
                difficulty=review.difficulty
            )
        except Exception as e:
            print(f"Neural scheduling failed: {e}")
    
    # Update card statistics
    new_success_rate = (
        (card["success_rate"] * card["review_count"] + (1 if review.correct else 0)) /
        (card["review_count"] + 1)
    )
    
    update_data = {
        "last_reviewed": datetime.utcnow(),
        "review_count": card["review_count"] + 1,
        "success_rate": new_success_rate,
        "difficulty": review.difficulty
    }
    
    if schedule:
        update_data.update({
            "next_review": schedule.next_review,
            "optimal_time": schedule.optimal_time
        })
    
    await db.cards.update_one(
        {"_id": review.card_id},
        {"$set": update_data}
    )
    
    # Save review record
    await db.reviews.insert_one({
        "_id": str(uuid.uuid4()),
        "user_id": user.id,
        "card_id": review.card_id,
        "deck_id": card["deck_id"],
        "correct": review.correct,
        "response_time": review.response_time,
        "difficulty": review.difficulty,
        "timestamp": datetime.utcnow()
    })
    
    # Update learning models
    if neural_client:
        background_tasks.add_task(
            neural_client.update_learning_models,
            user_id=user.id,
            review_data=review.dict()
        )
    
    # Emit event
    event = Event(
        type="card.reviewed",
        payload={
            "card_id": review.card_id,
            "user_id": user.id,
            "correct": review.correct,
            "response_time": review.response_time
        }
    )
    await event_producer.send(event)
    
    return {
        "success": True,
        "next_review": schedule.next_review if schedule else None,
        "new_success_rate": new_success_rate
    }

@app.post("/api/flashcards/study/session")
async def save_study_session(
    session: StudySession,
    user: User = Depends(get_current_user)
):
    """Save study session results"""
    # Save session
    session_data = session.dict()
    session_data["_id"] = str(uuid.uuid4())
    session_data["user_id"] = user.id
    session_data["timestamp"] = datetime.utcnow()
    
    await db.sessions.insert_one(session_data)
    
    # Update user statistics
    await db.users.update_one(
        {"_id": user.id},
        {
            "$inc": {
                "total_study_time": session.duration,
                "total_cards_reviewed": session.cards_reviewed
            },
            "$set": {
                "last_study_date": datetime.utcnow()
            }
        }
    )
    
    # Emit event
    event = Event(
        type="session.completed",
        payload={
            "user_id": user.id,
            "duration": session.duration,
            "cards_reviewed": session.cards_reviewed,
            "accuracy": session.accuracy
        }
    )
    await event_producer.send(event)
    
    return {"success": True, "session_id": session_data["_id"]}

# AI endpoints
@app.post("/api/flashcards/ai/generate", response_model=List[Flashcard])
async def generate_ai_cards(
    topic: str,
    count: int = 10,
    difficulty: str = "mixed",
    deck_id: Optional[str] = None,
    user: User = Depends(get_current_user)
):
    """Generate AI cards for a topic"""
    if not neural_client:
        raise HTTPException(status_code=503, detail="AI service unavailable")
    
    # Verify deck ownership if provided
    if deck_id:
        deck = await db.decks.find_one({
            "_id": deck_id,
            "user_id": user.id
        })
        if not deck:
            raise HTTPException(status_code=404, detail="Deck not found")
    
    # Generate cards
    try:
        generated_cards = await neural_client.generate_cards(
            topic=topic,
            count=count,
            difficulty=difficulty,
            user_id=user.id
        )
        
        # Create flashcard objects
        cards = []
        for card_data in generated_cards:
            card = Flashcard(
                id=str(uuid.uuid4()),
                deck_id=deck_id,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                review_count=0,
                success_rate=0,
                **card_data
            )
            cards.append(card)
        
        # Save to database if deck provided
        if deck_id:
            await db.cards.insert_many([card.dict() for card in cards])
            
            # Update deck card count
            await db.decks.update_one(
                {"_id": deck_id},
                {
                    "$inc": {"card_count": len(cards)},
                    "$set": {"updated_at": datetime.utcnow()}
                }
            )
        
        return cards
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI generation failed: {str(e)}")

# Search endpoints
@app.post("/api/flashcards/search")
async def search_cards(
    query: str,
    filters: Optional[Dict[str, Any]] = None,
    user: User = Depends(get_current_user),
    limit: int = 50
):
    """Search cards with semantic search"""
    # Get user's deck IDs
    user_decks = await db.decks.find(
        {"user_id": user.id}
    ).to_list(None)
    
    deck_ids = [deck["_id"] for deck in user_decks]
    
    # Build search query
    search_filter = {"deck_id": {"$in": deck_ids}}
    
    if filters:
        if "tags" in filters:
            search_filter["tags"] = {"$in": filters["tags"]}
        if "difficulty" in filters:
            search_filter["difficulty"] = {"$in": filters["difficulty"]}
    
    # Use text search
    search_filter["$text"] = {"$search": query}
    
    # Execute search
    results = await db.cards.find(
        search_filter,
        {"score": {"$meta": "textScore"}}
    ).sort([("score", {"$meta": "textScore"})]).limit(limit).to_list(limit)
    
    # Enhance with semantic search if available
    if neural_client and results:
        try:
            card_ids = [r["_id"] for r in results]
            semantic_scores = await neural_client.semantic_search(
                query=query,
                card_ids=card_ids
            )
            
            # Rerank results
            for result in results:
                if result["_id"] in semantic_scores:
                    result["semantic_score"] = semantic_scores[result["_id"]]
            
            results.sort(key=lambda x: x.get("semantic_score", 0), reverse=True)
        except Exception as e:
            print(f"Semantic search failed: {e}")
    
    return results

# Community endpoints
@app.get("/api/flashcards/community/decks", response_model=List[Deck])
async def get_public_decks(
    sort: str = "popular",
    limit: int = 20,
    offset: int = 0
):
    """Get public community decks"""
    # Define sort criteria
    sort_field = {
        "popular": "clone_count",
        "recent": "created_at",
        "rating": "average_rating"
    }.get(sort, "clone_count")
    
    # Query public decks
    public_decks = await db.decks.find(
        {"is_public": True}
    ).sort(sort_field, -1).skip(offset).limit(limit).to_list(limit)
    
    return public_decks

@app.post("/api/flashcards/decks/{deck_id}/clone", response_model=Deck)
async def clone_deck(
    deck_id: str,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_current_user)
):
    """Clone a public deck"""
    # Get source deck
    source_deck = await db.decks.find_one({
        "_id": deck_id,
        "is_public": True
    })
    
    if not source_deck:
        raise HTTPException(status_code=404, detail="Public deck not found")
    
    # Create cloned deck
    cloned_deck = Deck(
        id=str(uuid.uuid4()),
        user_id=user.id,
        name=f"{source_deck['name']} (Copy)",
        description=source_deck["description"],
        tags=source_deck["tags"],
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        card_count=source_deck["card_count"],
        is_public=False,
        cloned_from=deck_id,
        progress=0
    )
    
    # Save cloned deck
    await db.decks.insert_one(cloned_deck.dict())
    
    # Clone cards in background
    background_tasks.add_task(
        clone_deck_cards,
        source_deck_id=deck_id,
        target_deck_id=cloned_deck.id
    )
    
    # Update clone count
    await db.decks.update_one(
        {"_id": deck_id},
        {"$inc": {"clone_count": 1}}
    )
    
    # Emit event
    event = Event(
        type="deck.cloned",
        payload={
            "source_deck_id": deck_id,
            "cloned_deck_id": cloned_deck.id,
            "user_id": user.id
        }
    )
    await event_producer.send(event)
    
    return cloned_deck

async def clone_deck_cards(source_deck_id: str, target_deck_id: str):
    """Clone all cards from source to target deck"""
    # Get all cards from source deck
    source_cards = await db.cards.find(
        {"deck_id": source_deck_id}
    ).to_list(None)
    
    # Create cloned cards
    cloned_cards = []
    for card in source_cards:
        cloned_card = card.copy()
        cloned_card["_id"] = str(uuid.uuid4())
        cloned_card["deck_id"] = target_deck_id
        cloned_card["created_at"] = datetime.utcnow()
        cloned_card["updated_at"] = datetime.utcnow()
        cloned_card["review_count"] = 0
        cloned_card["success_rate"] = 0
        cloned_card.pop("last_reviewed", None)
        cloned_card.pop("next_review", None)
        cloned_cards.append(cloned_card)
    
    # Insert cloned cards
    if cloned_cards:
        await db.cards.insert_many(cloned_cards)

# Event handlers (background tasks)
async def handle_deck_events():
    """Handle deck-related events from other services"""
    async for event in event_consumer.consume("deck.*"):
        print(f"Received deck event: {event.type}")
        # Process events from other services

async def handle_user_events():
    """Handle user-related events"""
    async for event in event_consumer.consume("user.*"):
        if event.type == "user.deleted":
            # Clean up user data
            user_id = event.payload["user_id"]
            await db.decks.delete_many({"user_id": user_id})
            await db.reviews.delete_many({"user_id": user_id})
            await db.sessions.delete_many({"user_id": user_id})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)