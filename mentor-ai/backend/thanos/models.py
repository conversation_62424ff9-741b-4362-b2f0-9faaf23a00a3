"""
Thanos Models - Modelos específicos do Thanos
"""
from sqlalchemy import Column, String, Text, DateTime, Boolean, Integer, ForeignKey, Float, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
from backend.fastapi_app.db.database import Base

class ThanosDocument(Base):
    __tablename__ = "thanos_documents"
    
    id = Column(String, primary_key=True)
    title = Column(String, nullable=False)
    document_type = Column(String, nullable=False)
    file_name = Column(String)
    file_path = Column(String)
    url = Column(String)
    content = Column(Text)
    summary = Column(Text)
    meta_data = Column(JSON)
    embeddings_generated = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relacionamentos
    chunks = relationship("ThanosChunk", back_populates="document", cascade="all, delete-orphan")
    sessions = relationship("ThanosSession", back_populates="document")

class ThanosChunk(Base):
    __tablename__ = "thanos_chunks"
    
    id = Column(Integer, primary_key=True)
    document_id = Column(String, ForeignKey("thanos_documents.id"))
    text = Column(Text)
    embedding = Column(JSON)  # Armazenar vetor de embedding
    meta_data = Column(JSON)
    chunk_index = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    document = relationship("ThanosDocument", back_populates="chunks")

class ThanosSession(Base):
    __tablename__ = "thanos_sessions"
    
    id = Column(String, primary_key=True)
    document_id = Column(String, ForeignKey("thanos_documents.id"), nullable=True)
    provider = Column(String, nullable=False)
    model = Column(String, nullable=False)
    language = Column(String, default="pt")
    use_rag = Column(Boolean, default=False)
    temperature = Column(Float, default=0.7)
    max_tokens = Column(Integer, default=2000)
    context = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relacionamentos
    document = relationship("ThanosDocument", back_populates="sessions")
    messages = relationship("ThanosMessage", back_populates="session", cascade="all, delete-orphan")

class ThanosMessage(Base):
    __tablename__ = "thanos_messages"
    
    id = Column(Integer, primary_key=True)
    session_id = Column(String, ForeignKey("thanos_sessions.id"))
    role = Column(String, nullable=False)  # user, assistant, system
    content = Column(Text, nullable=False)
    tokens_used = Column(Integer, default=0)
    processing_time = Column(Float, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    session = relationship("ThanosSession", back_populates="messages")
