"""
Ultra Enhanced Thanos Models with Complete Integration
"""

from sqlalchemy import Column, String, Text, DateTime, Float, Integer, Boolean, JSON, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()

class Document(Base):
    """Enhanced document model with advanced features"""
    __tablename__ = "thanos_documents"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String(500), nullable=False)
    document_type = Column(String(50), nullable=False)
    file_name = Column(String(500))
    file_path = Column(String(1000))
    url = Column(String(1000))
    text = Column(Text)
    summary = Column(Text)
    
    # Enhanced metadata
    doc_metadata = Column(JSON, default=dict)
    tags = Column(JSON, default=list)
    language = Column(String(10), default="pt")
    
    # Embeddings and vector data
    embeddings_generated = Column(Boolean, default=False)
    embedding_model = Column(String(100))
    embedding_dimension = Column(Integer)
    
    # Processing info
    processing_status = Column(String(50), default="pending")
    processing_error = Column(Text)
    chunk_count = Column(Integer, default=0)
    word_count = Column(Integer, default=0)
    
    # Analytics
    view_count = Column(Integer, default=0)
    last_accessed = Column(DateTime)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")
    sessions = relationship("ThanosSession", back_populates="document")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_document_type', 'document_type'),
        Index('idx_created_at', 'created_at'),
        Index('idx_language', 'language'),
    )

class DocumentChunk(Base):
    """Enhanced chunk model with embeddings"""
    __tablename__ = "thanos_chunks"
    
    id = Column(Integer, primary_key=True)
    document_id = Column(String, ForeignKey("thanos_documents.id"))
    
    # Content
    text = Column(Text, nullable=False)
    index = Column(Integer, nullable=False)
    
    # Metadata
    page_number = Column(Integer)
    section_title = Column(String(500))
    chunk_metadata = Column(JSON, default=dict)
    
    # Embeddings (stored as JSON array)
    embedding = Column(JSON)
    embedding_model = Column(String(100))
    
    # Search optimization
    search_text = Column(Text)  # Preprocessed text for search
    keywords = Column(JSON, default=list)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    document = relationship("Document", back_populates="chunks")
    
    # Indexes
    __table_args__ = (
        Index('idx_document_id', 'document_id'),
        Index('idx_chunk_index', 'document_id', 'index'),
    )

class ThanosSession(Base):
    """Enhanced session model with analytics"""
    __tablename__ = "thanos_sessions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    document_id = Column(String, ForeignKey("thanos_documents.id"))
    
    # AI Configuration
    provider = Column(String(50), nullable=False)
    model = Column(String(100), nullable=False)
    api_key_hash = Column(String(256))  # Hashed API key for security
    
    # Session settings
    language = Column(String(10), default="pt")
    use_rag = Column(Boolean, default=True)
    temperature = Column(Float, default=0.7)
    max_tokens = Column(Integer, default=2000)
    
    # Enhanced settings
    streaming_enabled = Column(Boolean, default=True)
    context_window = Column(Integer, default=4000)
    system_prompt = Column(Text)
    
    # Analytics
    total_messages = Column(Integer, default=0)
    total_tokens_used = Column(Integer, default=0)
    total_cost = Column(Float, default=0.0)
    average_response_time = Column(Float)
    
    # User info
    user_id = Column(String(100))
    user_agent = Column(String(500))
    ip_address = Column(String(50))
    
    # Status
    is_active = Column(Boolean, default=True)
    last_activity = Column(DateTime, default=datetime.utcnow)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    document = relationship("Document", back_populates="sessions")
    messages = relationship("Message", back_populates="session", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_session_active', 'is_active'),
        Index('idx_session_user', 'user_id'),
        Index('idx_last_activity', 'last_activity'),
    )

class Message(Base):
    """Enhanced message model with metadata"""
    __tablename__ = "thanos_messages"
    
    id = Column(Integer, primary_key=True)
    session_id = Column(String, ForeignKey("thanos_sessions.id"))
    
    # Message content
    role = Column(String(20), nullable=False)  # user, assistant, system
    content = Column(Text, nullable=False)
    
    # Enhanced content
    enhanced_content = Column(Text)  # RAG-enhanced version
    chunks_used = Column(JSON, default=list)  # IDs of chunks used for RAG
    
    # Metrics
    tokens = Column(Integer, default=0)
    cost = Column(Float, default=0.0)
    response_time = Column(Float)  # in seconds
    
    # Quality metrics
    confidence_score = Column(Float)
    relevance_score = Column(Float)
    
    # Metadata
    msg_metadata = Column(JSON, default=dict)
    error = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    session = relationship("ThanosSession", back_populates="messages")
    
    # Indexes
    __table_args__ = (
        Index('idx_message_session', 'session_id'),
        Index('idx_message_role', 'role'),
        Index('idx_message_created', 'created_at'),
    )

class EmbeddingCache(Base):
    """Cache for document embeddings"""
    __tablename__ = "thanos_embedding_cache"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    document_id = Column(String, ForeignKey("thanos_documents.id"))
    
    # Cache data
    embeddings_data = Column(JSON)  # Compressed embeddings
    model_name = Column(String(100))
    dimension = Column(Integer)
    
    # Metadata
    chunk_count = Column(Integer)
    total_tokens = Column(Integer)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)
    
    # Indexes
    __table_args__ = (
        Index('idx_cache_document', 'document_id'),
        Index('idx_cache_expires', 'expires_at'),
    )

class Analytics(Base):
    """Analytics and metrics tracking"""
    __tablename__ = "thanos_analytics"
    
    id = Column(Integer, primary_key=True)
    session_id = Column(String, ForeignKey("thanos_sessions.id"))
    
    # Event data
    event_type = Column(String(50))  # query, upload, error, etc
    event_data = Column(JSON)
    
    # Performance metrics
    duration_ms = Column(Integer)
    tokens_used = Column(Integer)
    cost = Column(Float)
    
    # Quality metrics
    user_satisfaction = Column(Float)  # 0-1 score
    response_quality = Column(Float)   # 0-1 score
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Indexes
    __table_args__ = (
        Index('idx_analytics_session', 'session_id'),
        Index('idx_analytics_event', 'event_type'),
        Index('idx_analytics_created', 'created_at'),
    )