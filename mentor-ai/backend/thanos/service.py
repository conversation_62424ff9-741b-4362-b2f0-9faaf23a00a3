"""
Thanos Service - Serviço principal do Thanos
"""
import os
import json
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import anthropic
from sqlalchemy.orm import Session
from backend.thanos.models import ThanosDocument, ThanosChunk, ThanosSession, ThanosMessage
from backend.fastapi_app.services.pdf_extractor import pdf_extractor
from backend.fastapi_app.services.rag_service import rag_service

logger = logging.getLogger(__name__)

class ThanosService:
    def __init__(self):
        self.anthropic_client = None
        self.initialize_anthropic()
    
    def initialize_anthropic(self):
        """Inicializa cliente do Anthropic se a chave estiver disponível"""
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if api_key:
            self.anthropic_client = anthropic.Anthropic(api_key=api_key)
            logger.info("Anthropic client initialized")
    
    async def process_document(self, file_path: str, document_type: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Processa documento e retorna chunks com embeddings"""
        options = options or {}
        
        try:
            if document_type == "pdf":
                # Usar PDF extractor
                extraction_result = pdf_extractor.extract_text(
                    file_path,
                    extract_images=options.get("extractImages", False),
                    preserve_layout=options.get("preserveLayout", True),
                    extract_metadata=options.get("extractMetadata", True),
                    quality=options.get("quality", "medium")
                )
                
                text = extraction_result.get("text", "")
                metadata = extraction_result.get("metadata", {})
                
                # Criar chunks
                chunk_size = options.get("chunk_size", 1000)
                chunk_overlap = options.get("chunk_overlap", 200)
                chunks = pdf_extractor.chunk_text(text, chunk_size, chunk_overlap)
                
            elif document_type == "txt":
                with open(file_path, "r", encoding=options.get("encoding", "utf-8")) as f:
                    text = f.read()
                metadata = {"file_type": "txt"}
                
                # Chunking simples
                chunk_size = options.get("chunk_size", 1000)
                chunk_overlap = options.get("chunk_overlap", 200)
                chunks = []
                
                for i in range(0, len(text), chunk_size - chunk_overlap):
                    chunk_text = text[i:i + chunk_size]
                    if len(chunk_text) > 50:
                        chunks.append({
                            "text": chunk_text,
                            "chunk_index": len(chunks)
                        })
            else:
                raise ValueError(f"Tipo de documento não suportado: {document_type}")
            
            # Gerar embeddings para os chunks
            chunks_with_embeddings = rag_service.embed_document_chunks(chunks)
            
            return {
                "text": text,
                "metadata": metadata,
                "chunks": chunks_with_embeddings
            }
            
        except Exception as e:
            logger.error(f"Erro ao processar documento: {str(e)}")
            raise
    
    async def get_ai_response(self, message: str, session: ThanosSession, db: Session) -> str:
        """Obtém resposta da IA baseada no contexto da sessão"""
        try:
            # Se usar RAG e tiver documento, buscar contexto relevante
            context_chunks = []
            if session.use_rag and session.document_id:
                # Buscar chunks relevantes
                chunks = db.query(ThanosChunk).filter(
                    ThanosChunk.document_id == session.document_id
                ).all()
                
                if chunks:
                    # Aqui você implementaria a busca por similaridade
                    # Por enquanto, vamos pegar os primeiros chunks
                    context_chunks = [chunk.text for chunk in chunks[:3]]
            
            # Montar prompt com contexto
            system_prompt = self._build_system_prompt(session, context_chunks)
            
            # Obter histórico de mensagens
            history = db.query(ThanosMessage).filter(
                ThanosMessage.session_id == session.id
            ).order_by(ThanosMessage.created_at).all()
            
            # Usar provedor configurado
            if session.provider == "Anthropic" and self.anthropic_client:
                return await self._call_anthropic(message, system_prompt, history, session)
            else:
                # Fallback para resposta simulada
                return await self._generate_simulated_response(message, session, context_chunks)
                
        except Exception as e:
            logger.error(f"Erro ao obter resposta da IA: {str(e)}")
            raise
    
    def _build_system_prompt(self, session: ThanosSession, context_chunks: List[str]) -> str:
        """Constrói prompt do sistema com contexto"""
        base_prompt = "Você é o ThanosAI, um assistente especializado em análise de documentos e medicina."
        
        if session.language == "pt":
            base_prompt = "Você é o ThanosAI, um assistente especializado em análise de documentos e medicina."
        else:
            base_prompt = "You are ThanosAI, an assistant specialized in document analysis and medicine."
        
        if context_chunks:
            context_text = "\n\n".join(context_chunks)
            if session.language == "pt":
                base_prompt += f"\n\nContexto do documento:\n{context_text}"
            else:
                base_prompt += f"\n\nDocument context:\n{context_text}"
        
        return base_prompt
    
    async def _call_anthropic(self, message: str, system_prompt: str, history: List[ThanosMessage], session: ThanosSession) -> str:
        """Chama API do Anthropic"""
        try:
            messages = []
            
            # Adicionar histórico
            for msg in history[-10:]:  # Últimas 10 mensagens
                messages.append({
                    "role": msg.role if msg.role in ["user", "assistant"] else "assistant",
                    "content": msg.content
                })
            
            # Adicionar mensagem atual
            messages.append({"role": "user", "content": message})
            
            # Fazer chamada
            response = self.anthropic_client.messages.create(
                model=session.model,
                messages=messages,
                system=system_prompt,
                max_tokens=session.max_tokens,
                temperature=session.temperature
            )
            
            return response.content[0].text
            
        except Exception as e:
            logger.error(f"Erro ao chamar Anthropic: {str(e)}")
            # Fallback para resposta simulada
            return await self._generate_simulated_response(message, session, [])
    
    async def _generate_simulated_response(self, message: str, session: ThanosSession, context_chunks: List[str]) -> str:
        """Gera resposta simulada para desenvolvimento"""
        if context_chunks:
            context_preview = context_chunks[0][:100] + "..."
            return f"[THANOS SIMULADO - {session.provider}/{session.model}] Analisando sua pergunta '{message}' com base no documento fornecido. Contexto relevante: '{context_preview}'"
        else:
            return f"[THANOS SIMULADO - {session.provider}/{session.model}] Processando sua mensagem: '{message}'. Em produção, esta seria analisada pelo modelo de IA configurado."

# Instância global do serviço
thanos_service = ThanosService()
