"""
Ultra Enhanced Thanos Router with Advanced Features
"""

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, BackgroundTasks, Query, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union
import os
import json
import uuid
import asyncio
from datetime import datetime
import logging
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import func
import hashlib

# Import database and services
from fastapi_app.db.database import get_db
from .ultra_models import Document, DocumentChunk, ThanosSession, Message, EmbeddingCache, Analytics
# from .ultra_service import ultra_thanos_service
from .simple_service import simple_thanos_service as ultra_thanos_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("thanos-ultra-router")

# Create router
router = APIRouter(prefix="/api/thanos", tags=["Thanos Ultra"])

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, session_id: str):
        await websocket.accept()
        self.active_connections[session_id] = websocket
        logger.info(f"WebSocket connected for session {session_id}")
    
    def disconnect(self, session_id: str):
        if session_id in self.active_connections:
            del self.active_connections[session_id]
            logger.info(f"WebSocket disconnected for session {session_id}")
    
    async def send_message(self, message: str, session_id: str):
        if session_id in self.active_connections:
            await self.active_connections[session_id].send_text(message)
    
    async def broadcast(self, message: str):
        for connection in self.active_connections.values():
            await connection.send_text(message)

manager = ConnectionManager()

# Enhanced Request/Response Models
class UltraHealthResponse(BaseModel):
    status: str = "healthy"
    version: str = "2.0.0"
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    services: Dict[str, str] = {
        "database": "connected",
        "redis": "connected",
        "embeddings": "ready",
        "ai_providers": "configured"
    }
    metrics: Dict[str, Any] = {}

class DocumentUploadRequest(BaseModel):
    document_type: str
    options: Dict[str, Any] = {}
    tags: List[str] = []
    extract_images: bool = False
    generate_summary: bool = True
    auto_chunk: bool = True

class UltraDocumentResponse(BaseModel):
    document_id: str
    title: str
    document_type: str
    file_name: Optional[str] = None
    url: Optional[str] = None
    summary: Optional[str] = None
    language: str
    tags: List[str] = []
    metadata: Dict[str, Any]
    chunk_count: int
    word_count: int
    processing_time: float
    embeddings_ready: bool = False
    success: bool = True

class ConversationRequest(BaseModel):
    message: str
    use_rag: bool = True
    stream: bool = False
    language: str = "pt"
    max_tokens: int = 2000
    temperature: float = 0.7

class ConversationStreamResponse(BaseModel):
    session_id: str
    chunk: str
    chunk_index: int
    is_complete: bool = False
    metadata: Dict[str, Any] = {}

class AnalyticsRequest(BaseModel):
    session_ids: Optional[List[str]] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    metrics: List[str] = ["messages", "tokens", "cost", "quality"]

class AnalyticsResponse(BaseModel):
    period: Dict[str, str]
    total_sessions: int
    total_messages: int
    total_tokens: int
    total_cost: float
    average_response_time: float
    user_satisfaction: float
    top_topics: List[Dict[str, Any]]
    usage_by_provider: Dict[str, int]
    usage_by_model: Dict[str, int]
    error_rate: float

class SearchRequest(BaseModel):
    query: str
    document_ids: Optional[List[str]] = None
    top_k: int = 5
    similarity_threshold: float = 0.5
    include_metadata: bool = True

class SearchResult(BaseModel):
    document_id: str
    chunk_index: int
    text: str
    similarity: float
    metadata: Dict[str, Any] = {}

# API Endpoints
@router.get("/health", response_model=UltraHealthResponse)
async def ultra_health_check(db: Session = Depends(get_db)):
    """Enhanced health check with service status"""
    try:
        # Check database
        db.execute("SELECT 1")
        db_status = "connected"
    except:
        db_status = "error"
    
    # Check Redis
    try:
        await ultra_thanos_service.initialize()
        if ultra_thanos_service.redis_client:
            await ultra_thanos_service.redis_client.ping()
            redis_status = "connected"
        else:
            redis_status = "not configured"
    except:
        redis_status = "error"
    
    # Get metrics safely
    try:
        metrics = {
            "total_documents": db.query(func.count(Document.id)).scalar() or 0,
            "total_sessions": db.query(func.count(ThanosSession.id)).scalar() or 0,
            "active_sessions": db.query(func.count(ThanosSession.id)).filter(
                ThanosSession.is_active == True
            ).scalar() or 0,
            "total_messages": db.query(func.count(Message.id)).scalar() or 0
        }
    except Exception as e:
        logger.warning(f"Error getting metrics: {str(e)}")
        metrics = {
            "total_documents": 0,
            "total_sessions": 0,
            "active_sessions": 0,
            "total_messages": 0
        }
    
    return UltraHealthResponse(
        services={
            "database": db_status,
            "redis": redis_status,
            "embeddings": "ready" if ultra_thanos_service._initialized else "not initialized",
            "ai_providers": "configured"
        },
        metrics=metrics
    )

@router.post("/documents/upload", response_model=UltraDocumentResponse)
async def ultra_upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    document_type: str = Form(...),
    options: str = Form("{}"),
    tags: str = Form("[]"),
    extract_images: bool = Form(False),
    generate_summary: bool = Form(True),
    auto_chunk: bool = Form(True),
    db: Session = Depends(get_db)
):
    """Enhanced document upload with advanced processing"""
    document_id = str(uuid.uuid4())
    start_time = datetime.now()
    
    try:
        # Parse options and tags
        options_dict = json.loads(options)
        tags_list = json.loads(tags)
        
        # Create upload directory
        upload_dir = Path("./uploads")
        upload_dir.mkdir(exist_ok=True)
        
        # Save file
        file_path = upload_dir / f"{document_id}_{file.filename}"
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Process document
        logger.info(f"Processing document {document_id} of type {document_type}")
        
        result = await ultra_thanos_service.process_document(
            str(file_path),
            document_type,
            {
                **options_dict,
                "extract_images": extract_images,
                "auto_chunk": auto_chunk
            }
        )
        
        # Create document record
        document = Document(
            id=document_id,
            title=file.filename,
            document_type=document_type,
            file_name=file.filename,
            file_path=str(file_path),
            text=result["text"],
            summary=result.get("summary", "") if generate_summary else None,
            doc_metadata=result["metadata"],
            tags=tags_list,
            language=result["metadata"].get("language", "pt"),
            chunk_count=len(result["chunks"]),
            word_count=result["metadata"].get("word_count", 0),
            processing_status="completed",
            embeddings_generated=True,
            embedding_model=ultra_thanos_service.config["embedding_model"]
        )
        db.add(document)
        
        # Add chunks with embeddings
        for chunk_data in result["chunks"]:
            chunk = DocumentChunk(
                document_id=document_id,
                text=chunk_data["text"],
                index=chunk_data["index"],
                embedding=chunk_data.get("embedding"),
                embedding_model=ultra_thanos_service.config["embedding_model"],
                chunk_metadata=chunk_data
            )
            db.add(chunk)
        
        # Save embeddings to cache
        background_tasks.add_task(
            cache_document_embeddings,
            document_id,
            result["chunks"]
        )
        
        db.commit()
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Log analytics
        analytics = Analytics(
            event_type="document_upload",
            event_data={
                "document_id": document_id,
                "document_type": document_type,
                "file_size": os.path.getsize(file_path),
                "chunk_count": len(result["chunks"]),
                "processing_time": processing_time
            },
            duration_ms=int(processing_time * 1000)
        )
        db.add(analytics)
        db.commit()
        
        return UltraDocumentResponse(
            document_id=document_id,
            title=file.filename,
            document_type=document_type,
            file_name=file.filename,
            summary=result.get("summary"),
            language=result["metadata"].get("language", "pt"),
            tags=tags_list,
            doc_metadata=result["metadata"],
            chunk_count=len(result["chunks"]),
            word_count=result["metadata"].get("word_count", 0),
            processing_time=processing_time,
            embeddings_ready=True
        )
        
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        # Update document status if it was created
        if document_id:
            doc = db.query(Document).filter(Document.id == document_id).first()
            if doc:
                doc.processing_status = "error"
                doc.processing_error = str(e)
                db.commit()
        
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")

@router.post("/documents/url", response_model=UltraDocumentResponse)
async def ultra_process_url(
    background_tasks: BackgroundTasks,
    url: str = Query(...),
    document_type: str = Query(...),
    options: Dict[str, Any] = {},
    tags: List[str] = [],
    db: Session = Depends(get_db)
):
    """Process URL content with enhanced extraction"""
    document_id = str(uuid.uuid4())
    start_time = datetime.now()
    
    try:
        # Process URL
        result = await ultra_thanos_service.process_url(url, document_type, options)
        
        # Create document record
        document = Document(
            id=document_id,
            title=result["metadata"].get("title", url),
            document_type=document_type,
            url=url,
            text=result["text"],
            summary=result.get("summary"),
            doc_metadata=result["metadata"],
            tags=tags,
            language=result["metadata"].get("language", "pt"),
            chunk_count=len(result["chunks"]),
            word_count=result["metadata"].get("word_count", 0),
            processing_status="completed",
            embeddings_generated=True
        )
        db.add(document)
        
        # Add chunks
        for chunk_data in result["chunks"]:
            chunk = DocumentChunk(
                document_id=document_id,
                text=chunk_data["text"],
                index=chunk_data["index"],
                embedding=chunk_data.get("embedding"),
                chunk_metadata=chunk_data
            )
            db.add(chunk)
        
        db.commit()
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return UltraDocumentResponse(
            document_id=document_id,
            title=result["metadata"].get("title", url),
            document_type=document_type,
            url=url,
            summary=result.get("summary"),
            language=result["metadata"].get("language", "pt"),
            tags=tags,
            doc_metadata=result["metadata"],
            chunk_count=len(result["chunks"]),
            word_count=result["metadata"].get("word_count", 0),
            processing_time=processing_time,
            embeddings_ready=True
        )
        
    except Exception as e:
        logger.error(f"Error processing URL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing URL: {str(e)}")

@router.post("/search", response_model=List[SearchResult])
async def semantic_search(
    request: SearchRequest,
    db: Session = Depends(get_db)
):
    """Semantic search across documents"""
    try:
        results = []
        
        # Get documents to search
        query = db.query(Document)
        if request.document_ids:
            query = query.filter(Document.id.in_(request.document_ids))
        documents = query.all()
        
        for document in documents:
            # Get chunks
            chunks = db.query(DocumentChunk).filter(
                DocumentChunk.document_id == document.id
            ).all()
            
            if not chunks:
                continue
            
            # Convert to format expected by service
            chunk_data = [
                {
                    "text": chunk.text,
                    "index": chunk.index,
                    "embedding": chunk.embedding
                }
                for chunk in chunks
            ]
            
            # Retrieve similar chunks
            similar_chunks = await ultra_thanos_service.retrieve_similar_chunks(
                request.query,
                document.id,
                chunk_data,
                request.top_k,
                request.similarity_threshold
            )
            
            # Format results
            for chunk in similar_chunks:
                result = SearchResult(
                    document_id=document.id,
                    chunk_index=chunk["index"],
                    text=chunk["text"],
                    similarity=chunk["similarity"]
                )
                
                if request.include_metadata:
                    result.metadata = {
                        "document_title": document.title,
                        "document_type": document.document_type,
                        "language": document.language,
                        "tags": document.tags
                    }
                
                results.append(result)
        
        # Sort by similarity
        results.sort(key=lambda x: x.similarity, reverse=True)
        
        return results[:request.top_k]
        
    except Exception as e:
        logger.error(f"Error in semantic search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")

@router.post("/initialize")
async def ultra_initialize_session(
    provider: str = Form(...),
    model: str = Form(...),
    api_key: str = Form(...),
    document_id: Optional[str] = Form(None),
    language: str = Form("pt"),
    use_rag: bool = Form(True),
    temperature: float = Form(0.7),
    max_tokens: int = Form(2000),
    streaming_enabled: bool = Form(True),
    system_prompt: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """Initialize enhanced Thanos session"""
    session_id = str(uuid.uuid4())
    
    try:
        # Hash API key for security
        api_key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Create session
        session = ThanosSession(
            id=session_id,
            document_id=document_id,
            provider=provider,
            model=model,
            api_key_hash=api_key_hash,
            language=language,
            use_rag=use_rag,
            temperature=temperature,
            max_tokens=max_tokens,
            streaming_enabled=streaming_enabled,
            system_prompt=system_prompt
        )
        
        # Generate enhanced system prompt if document provided
        if document_id and use_rag:
            document = db.query(Document).filter(Document.id == document_id).first()
            if document:
                if language == "pt":
                    enhanced_prompt = f"""Você é o Thanos, um assistente inteligente especializado no conteúdo do documento '{document.title}'.
                    
Informações do documento:
- Tipo: {document.document_type}
- Idioma: {document.language}
- Resumo: {document.summary or 'Não disponível'}

Use essas informações para fornecer respostas precisas e contextualizadas."""
                else:
                    enhanced_prompt = f"""You are Thanos, an intelligent assistant specialized in the content of the document '{document.title}'.
                    
Document information:
- Type: {document.document_type}
- Language: {document.language}
- Summary: {document.summary or 'Not available'}

Use this information to provide accurate and contextualized answers."""
                
                session.system_prompt = system_prompt or enhanced_prompt
        
        db.add(session)
        db.commit()
        
        # Log session creation
        analytics = Analytics(
            session_id=session_id,
            event_type="session_created",
            event_data={
                "provider": provider,
                "model": model,
                "document_id": document_id,
                "use_rag": use_rag,
                "streaming": streaming_enabled
            }
        )
        db.add(analytics)
        db.commit()
        
        return {
            "session_id": session_id,
            "status": "initialized",
            "provider": provider,
            "model": model,
            "streaming_enabled": streaming_enabled,
            "websocket_url": f"/api/thanos/ws/{session_id}" if streaming_enabled else None
        }
        
    except Exception as e:
        logger.error(f"Error initializing session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error initializing session: {str(e)}")

@router.post("/conversations/{session_id}/message")
async def ultra_send_message(
    session_id: str,
    request: ConversationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Send message with streaming support"""
    try:
        # Get session
        session = db.query(ThanosSession).filter(ThanosSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Update session activity
        session.last_activity = datetime.utcnow()
        session.total_messages += 1
        
        # Store user message
        user_message = Message(
            session_id=session_id,
            role="user",
            content=request.message
        )
        db.add(user_message)
        db.commit()
        
        # Get conversation history
        messages = db.query(Message).filter(
            Message.session_id == session_id
        ).order_by(Message.id).all()
        
        history = [
            {"role": msg.role, "content": msg.content}
            for msg in messages[:-1]  # Exclude current message
        ]
        
        # Prepare context
        context = {
            "history": history,
            "temperature": request.temperature or session.temperature,
            "max_tokens": request.max_tokens or session.max_tokens,
            "system_prompt": session.system_prompt
        }
        
        # Handle RAG if enabled
        enhanced_message = request.message
        chunks_used = []
        
        if session.use_rag and session.document_id and request.use_rag:
            # Get document chunks
            chunks = db.query(DocumentChunk).filter(
                DocumentChunk.document_id == session.document_id
            ).all()
            
            chunk_data = [
                {
                    "text": chunk.text,
                    "index": chunk.index,
                    "embedding": chunk.embedding
                }
                for chunk in chunks
            ]
            
            # Retrieve similar chunks
            similar_chunks = await ultra_thanos_service.retrieve_similar_chunks(
                request.message,
                session.document_id,
                chunk_data,
                top_k=3,
                similarity_threshold=0.5
            )
            
            if similar_chunks:
                enhanced_message = ultra_thanos_service.enhance_prompt_with_context(
                    request.message,
                    similar_chunks,
                    session.language
                )
                chunks_used = [chunk["index"] for chunk in similar_chunks]
        
        # Stream or regular response
        if request.stream and session.streaming_enabled:
            # Return streaming response
            return StreamingResponse(
                stream_response(
                    session,
                    enhanced_message,
                    context,
                    chunks_used,
                    db
                ),
                media_type="text/event-stream"
            )
        else:
            # Regular response
            start_time = datetime.now()
            
            # Call AI provider (using dummy key for development)
            response_text = ""
            async for chunk in ultra_thanos_service.stream_ai_response(
                session.provider,
                session.model,
                enhanced_message,
                context,
                "dummy_api_key"  # In production, decrypt session.api_key_hash
            ):
                response_text += chunk
            
            response_time = (datetime.now() - start_time).total_seconds()
            
            # Store assistant message
            assistant_message = Message(
                session_id=session_id,
                role="assistant",
                content=response_text,
                enhanced_content=enhanced_message if chunks_used else None,
                chunks_used=chunks_used,
                tokens=len(response_text.split()),
                response_time=response_time
            )
            db.add(assistant_message)
            
            # Update session stats
            session.total_tokens_used += len(response_text.split())
            
            db.commit()
            
            # Log analytics in background
            background_tasks.add_task(
                log_message_analytics,
                session_id,
                len(response_text.split()),
                response_time,
                len(chunks_used)
            )
            
            return {
                "session_id": session_id,
                "response": response_text,
                "tokens_used": len(response_text.split()),
                "response_time": response_time,
                "chunks_used": len(chunks_used),
                "enhanced": bool(chunks_used)
            }
            
    except Exception as e:
        logger.error(f"Error sending message: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@router.websocket("/ws/{session_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    session_id: str,
    db: Session = Depends(get_db)
):
    """WebSocket endpoint for real-time streaming"""
    await manager.connect(websocket, session_id)
    
    try:
        # Get session
        session = db.query(ThanosSession).filter(ThanosSession.id == session_id).first()
        if not session:
            await websocket.send_json({"error": "Session not found"})
            return
        
        while True:
            # Receive message
            data = await websocket.receive_json()
            
            if data.get("type") == "message":
                # Process message similar to HTTP endpoint
                message = data.get("message", "")
                
                # Store user message
                user_message = Message(
                    session_id=session_id,
                    role="user",
                    content=message
                )
                db.add(user_message)
                db.commit()
                
                # Get history
                messages = db.query(Message).filter(
                    Message.session_id == session_id
                ).order_by(Message.id).limit(20).all()
                
                history = [
                    {"role": msg.role, "content": msg.content}
                    for msg in messages[:-1]
                ]
                
                # Prepare context
                context = {
                    "history": history,
                    "temperature": session.temperature,
                    "max_tokens": session.max_tokens,
                    "system_prompt": session.system_prompt
                }
                
                # Stream response
                response_text = ""
                chunk_index = 0
                
                async for chunk in ultra_thanos_service.stream_ai_response(
                    session.provider,
                    session.model,
                    message,
                    context,
                    "dummy_api_key"
                ):
                    response_text += chunk
                    
                    # Send chunk to client
                    await websocket.send_json({
                        "type": "chunk",
                        "chunk": chunk,
                        "chunk_index": chunk_index
                    })
                    chunk_index += 1
                
                # Send completion signal
                await websocket.send_json({
                    "type": "complete",
                    "full_response": response_text,
                    "tokens": len(response_text.split())
                })
                
                # Store assistant response
                assistant_message = Message(
                    session_id=session_id,
                    role="assistant",
                    content=response_text,
                    tokens=len(response_text.split())
                )
                db.add(assistant_message)
                db.commit()
            
            elif data.get("type") == "ping":
                await websocket.send_json({"type": "pong"})
            
    except WebSocketDisconnect:
        manager.disconnect(session_id)
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
        await websocket.send_json({"error": str(e)})
        manager.disconnect(session_id)

@router.get("/analytics", response_model=AnalyticsResponse)
async def get_analytics(
    request: AnalyticsRequest = None,
    db: Session = Depends(get_db)
):
    """Get comprehensive analytics"""
    try:
        # Default date range (last 30 days)
        if not request:
            request = AnalyticsRequest()
        
        if not request.end_date:
            request.end_date = datetime.utcnow()
        
        if not request.start_date:
            request.start_date = request.end_date - timedelta(days=30)
        
        # Build query
        query = db.query(ThanosSession)
        
        if request.session_ids:
            query = query.filter(ThanosSession.id.in_(request.session_ids))
        
        query = query.filter(
            ThanosSession.created_at >= request.start_date,
            ThanosSession.created_at <= request.end_date
        )
        
        sessions = query.all()
        
        # Calculate metrics
        total_sessions = len(sessions)
        total_messages = sum(s.total_messages for s in sessions)
        total_tokens = sum(s.total_tokens_used for s in sessions)
        total_cost = sum(s.total_cost for s in sessions)
        
        # Average response time
        response_times = []
        for session in sessions:
            messages = db.query(Message).filter(
                Message.session_id == session.id,
                Message.role == "assistant",
                Message.response_time.isnot(None)
            ).all()
            response_times.extend([m.response_time for m in messages])
        
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # Usage by provider/model
        usage_by_provider = {}
        usage_by_model = {}
        
        for session in sessions:
            usage_by_provider[session.provider] = usage_by_provider.get(session.provider, 0) + 1
            usage_by_model[session.model] = usage_by_model.get(session.model, 0) + 1
        
        # Get conversation analytics for topics
        all_topics = []
        for session in sessions[:100]:  # Limit for performance
            messages = db.query(Message).filter(
                Message.session_id == session.id
            ).all()
            
            if messages:
                message_data = [
                    {"role": m.role, "content": m.content, "created_at": m.created_at}
                    for m in messages
                ]
                
                analysis = await ultra_thanos_service.analyze_conversation(
                    session.id,
                    message_data
                )
                
                all_topics.extend(analysis.get("topics", []))
        
        # Count topic frequency
        from collections import Counter
        topic_counts = Counter(all_topics)
        top_topics = [
            {"topic": topic, "count": count}
            for topic, count in topic_counts.most_common(10)
        ]
        
        # Calculate error rate
        error_events = db.query(Analytics).filter(
            Analytics.event_type == "error",
            Analytics.created_at >= request.start_date,
            Analytics.created_at <= request.end_date
        ).count()
        
        total_events = db.query(Analytics).filter(
            Analytics.created_at >= request.start_date,
            Analytics.created_at <= request.end_date
        ).count()
        
        error_rate = error_events / total_events if total_events > 0 else 0
        
        return AnalyticsResponse(
            period={
                "start": request.start_date.isoformat(),
                "end": request.end_date.isoformat()
            },
            total_sessions=total_sessions,
            total_messages=total_messages,
            total_tokens=total_tokens,
            total_cost=total_cost,
            average_response_time=avg_response_time,
            user_satisfaction=0.85,  # Placeholder - implement satisfaction tracking
            top_topics=top_topics,
            usage_by_provider=usage_by_provider,
            usage_by_model=usage_by_model,
            error_rate=error_rate
        )
        
    except Exception as e:
        logger.error(f"Error getting analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analytics error: {str(e)}")

@router.delete("/sessions/{session_id}")
async def end_session(
    session_id: str,
    db: Session = Depends(get_db)
):
    """End a session and clean up resources"""
    try:
        session = db.query(ThanosSession).filter(ThanosSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Mark session as inactive
        session.is_active = False
        db.commit()
        
        # Disconnect WebSocket if connected
        manager.disconnect(session_id)
        
        return {"status": "session_ended", "session_id": session_id}
        
    except Exception as e:
        logger.error(f"Error ending session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@router.post("/cleanup")
async def cleanup_old_data(
    days: int = Query(30, description="Days to keep data"),
    db: Session = Depends(get_db)
):
    """Clean up old data and cache"""
    try:
        # Clean up service cache
        cleanup_stats = await ultra_thanos_service.cleanup_old_data(days)
        
        # Clean up old sessions
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        old_sessions = db.query(ThanosSession).filter(
            ThanosSession.last_activity < cutoff_date,
            ThanosSession.is_active == False
        ).count()
        
        # Delete old inactive sessions
        db.query(ThanosSession).filter(
            ThanosSession.last_activity < cutoff_date,
            ThanosSession.is_active == False
        ).delete()
        
        db.commit()
        
        return {
            "status": "cleanup_complete",
            "sessions_removed": old_sessions,
            **cleanup_stats
        }
        
    except Exception as e:
        logger.error(f"Error during cleanup: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Cleanup error: {str(e)}")

# Helper functions
async def stream_response(session, message, context, chunks_used, db):
    """Stream response generator for SSE"""
    try:
        response_text = ""
        chunk_index = 0
        start_time = datetime.now()
        
        async for chunk in ultra_thanos_service.stream_ai_response(
            session.provider,
            session.model,
            message,
            context,
            "dummy_api_key"
        ):
            response_text += chunk
            
            # Format as SSE
            yield f"data: {json.dumps({'chunk': chunk, 'index': chunk_index})}\n\n"
            chunk_index += 1
        
        # Send completion event
        response_time = (datetime.now() - start_time).total_seconds()
        
        yield f"data: {json.dumps({'complete': True, 'total_chunks': chunk_index, 'response_time': response_time})}\n\n"
        
        # Store complete message
        assistant_message = Message(
            session_id=session.id,
            role="assistant",
            content=response_text,
            chunks_used=chunks_used,
            tokens=len(response_text.split()),
            response_time=response_time
        )
        db.add(assistant_message)
        db.commit()
        
    except Exception as e:
        yield f"data: {json.dumps({'error': str(e)})}\n\n"

async def cache_document_embeddings(document_id: str, chunks: List[Dict[str, Any]]):
    """Cache document embeddings for faster retrieval"""
    try:
        cache_data = {
            "chunks": chunks,
            "model": ultra_thanos_service.config["embedding_model"],
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Save to Redis cache
        if ultra_thanos_service.redis_client:
            key = f"thanos:doc_embeddings:{document_id}"
            await ultra_thanos_service.redis_client.setex(
                key,
                3600 * 24,  # 24 hours
                json.dumps(cache_data)
            )
            
    except Exception as e:
        logger.error(f"Error caching embeddings: {str(e)}")

async def log_message_analytics(session_id: str, tokens: int, response_time: float, chunks_used: int):
    """Log message analytics in background"""
    try:
        # This would be implemented with proper analytics service
        logger.info(f"Analytics - Session: {session_id}, Tokens: {tokens}, Time: {response_time}s, Chunks: {chunks_used}")
    except Exception as e:
        logger.error(f"Error logging analytics: {str(e)}")