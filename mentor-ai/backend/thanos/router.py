"""
Thanos Router - Rotas integradas do Thanos
"""
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, Query
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.orm import Session
import json
import uuid
import os
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

from backend.fastapi_app.db.database import get_db
from backend.thanos.models import ThanosDocument, ThanosChunk, ThanosSession, ThanosMessage
from backend.thanos.service import thanos_service
from backend.fastapi_app.routers.thanos import (
    InitializeRequest, MessageRequest, DocumentUploadResponse, 
    DocumentURLResponse, DocumentSummaryResponse, ConversationResponse
)

router = APIRouter(prefix="/api/thanos", tags=["Thanos"])

# Diretórios
UPLOAD_DIR = Path("./uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

@router.get("/health")
async def health_check():
    """Health check do Thanos"""
    return {
        "status": "ok",
        "service": "ThanosAI",
        "version": "2.0",
        "timestamp": datetime.now().isoformat()
    }

@router.post("/documents/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    document_type: str = Form(...),
    config: str = Form("{}"),
    db: Session = Depends(get_db)
):
    """Upload e processamento de documento"""
    document_id = str(uuid.uuid4())
    
    try:
        # Parse configuração
        options = json.loads(config)
        
        # Salvar arquivo
        file_path = UPLOAD_DIR / f"{document_id}_{file.filename}"
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Processar documento
        result = await thanos_service.process_document(str(file_path), document_type, options)
        
        # Criar documento no banco
        document = ThanosDocument(
            id=document_id,
            title=file.filename,
            document_type=document_type,
            file_name=file.filename,
            file_path=str(file_path),
            content=result["text"],
            summary=result["text"][:500] + "...",
            metadata=result["metadata"],
            embeddings_generated=True
        )
        db.add(document)
        
        # Adicionar chunks
        for chunk in result["chunks"]:
            chunk_obj = ThanosChunk(
                document_id=document_id,
                text=chunk["text"],
                embedding=chunk.get("embedding"),
                chunk_index=chunk.get("chunk_index", 0)
            )
            db.add(chunk_obj)
        
        db.commit()
        
        return DocumentUploadResponse(
            document_id=document_id,
            document_type=document_type,
            file_name=file.filename,
            file_size=os.path.getsize(file_path),
            chunk_count=len(result["chunks"])
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/initialize")
async def initialize_thanos(request: InitializeRequest, db: Session = Depends(get_db)):
    """Inicializa sessão do Thanos"""
    session_id = str(uuid.uuid4())
    
    try:
        # Criar sessão
        session = ThanosSession(
            id=session_id,
            document_id=request.document_id,
            provider=request.provider,
            model=request.model,
            language=request.language,
            use_rag=request.use_rag,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            context={
                "api_key": request.api_key,
                "initialized_at": datetime.now().isoformat()
            }
        )
        db.add(session)
        db.commit()
        
        return {
            "session_id": session_id,
            "document_id": request.document_id,
            "status": "initialized",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/conversations/{session_id}/message", response_model=ConversationResponse)
async def send_message(session_id: str, request: MessageRequest, db: Session = Depends(get_db)):
    """Envia mensagem para o Thanos"""
    try:
        # Buscar sessão
        session = db.query(ThanosSession).filter(ThanosSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Sessão não encontrada")
        
        # Obter resposta
        start_time = datetime.now()
        response = await thanos_service.get_ai_response(request.message, session, db)
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Salvar mensagens
        user_msg = ThanosMessage(
            session_id=session_id,
            role="user",
            content=request.message
        )
        db.add(user_msg)
        
        assistant_msg = ThanosMessage(
            session_id=session_id,
            role="assistant",
            content=response,
            processing_time=processing_time
        )
        db.add(assistant_msg)
        
        db.commit()
        
        return ConversationResponse(
            session_id=session_id,
            response=response,
            tokens_used=len(response.split()),
            processing_time=processing_time
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/{document_id}/summary", response_model=DocumentSummaryResponse)
async def get_document_summary(document_id: str, db: Session = Depends(get_db)):
    """Obtém resumo do documento"""
    document = db.query(ThanosDocument).filter(ThanosDocument.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Documento não encontrado")
    
    return DocumentSummaryResponse(
        document_id=document_id,
        summary=document.summary or "Resumo não disponível",
        title=document.title
    )
