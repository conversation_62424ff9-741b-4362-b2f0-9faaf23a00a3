"""
Ultra Enhanced Thanos Service with Advanced Features
"""

import os
import json
import uuid
import httpx
import asyncio
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
from pathlib import Path
import logging
import aiofiles
import redis.asyncio as redis
from functools import lru_cache

# Import AI providers
import openai
import anthropic
from groq import Groq

# Import document processing
import PyPDF2
try:
    import pytesseract
except ImportError:
    pytesseract = None
from PIL import Image
import pandas as pd
from bs4 import BeautifulSoup
from youtube_transcript_api import YouTubeTranscriptApi
import trafilatura

# Import NLP and ML
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import tiktoken
import spacy
from langdetect import detect

# Import database models
from .ultra_models import Document, DocumentChunk, ThanosSession, Message, EmbeddingCache, Analytics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("thanos-ultra-service")

class UltraThanosService:
    """Ultra enhanced Thanos service with advanced capabilities"""
    
    def __init__(self):
        # Initialize components
        self.embedding_model = None
        self.redis_client = None
        self.nlp = None
        self.tokenizer = None
        
        # Configuration
        self.config = {
            "embedding_model": "all-MiniLM-L6-v2",
            "max_chunk_size": 1000,
            "chunk_overlap": 200,
            "redis_url": os.getenv("REDIS_URL", "redis://localhost:6379"),
            "cache_ttl": 3600,  # 1 hour
            "max_context_length": 16000,
            "streaming_chunk_size": 50,
        }
        
        # AI Provider clients
        self.providers = {
            "openai": None,
            "anthropic": None,
            "groq": None
        }
        
        # Initialize on first use
        self._initialized = False
    
    async def initialize(self):
        """Initialize all components"""
        if self._initialized:
            return
        
        try:
            # Initialize embedding model
            logger.info("Initializing embedding model...")
            self.embedding_model = SentenceTransformer(self.config["embedding_model"])
            
            # Initialize Redis for caching
            logger.info("Connecting to Redis...")
            self.redis_client = await redis.from_url(self.config["redis_url"])
            
            # Initialize NLP models
            logger.info("Loading NLP models...")
            self.nlp = spacy.load("pt_core_news_sm")  # Portuguese model
            self.tokenizer = tiktoken.encoding_for_model("gpt-4")
            
            # Initialize AI providers if keys are available
            if os.getenv("OPENAI_API_KEY"):
                openai.api_key = os.getenv("OPENAI_API_KEY")
                self.providers["openai"] = openai
            
            if os.getenv("ANTHROPIC_API_KEY"):
                self.providers["anthropic"] = anthropic.Anthropic(
                    api_key=os.getenv("ANTHROPIC_API_KEY")
                )
            
            if os.getenv("GROQ_API_KEY"):
                self.providers["groq"] = Groq(api_key=os.getenv("GROQ_API_KEY"))
            
            self._initialized = True
            logger.info("Thanos Ultra Service initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Thanos service: {str(e)}")
            raise
    
    async def process_document(self, file_path: str, document_type: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process document with advanced extraction"""
        await self.initialize()
        
        options = options or {}
        
        try:
            # Extract text based on document type
            if document_type == "pdf":
                text, metadata = await self._extract_pdf(file_path, options)
            elif document_type == "txt":
                text, metadata = await self._extract_text(file_path, options)
            elif document_type == "csv":
                text, metadata = await self._extract_csv(file_path, options)
            elif document_type == "image":
                text, metadata = await self._extract_image(file_path, options)
            else:
                raise ValueError(f"Unsupported document type: {document_type}")
            
            # Detect language
            language = detect(text[:500]) if len(text) > 50 else "pt"
            
            # Extract entities and keywords
            doc = self.nlp(text[:1000000])  # Limit for performance
            entities = [(ent.text, ent.label_) for ent in doc.ents]
            keywords = self._extract_keywords(doc)
            
            # Create smart chunks with context
            chunks = await self._create_smart_chunks(text, options)
            
            # Generate embeddings for chunks
            embeddings = await self._generate_embeddings([chunk["text"] for chunk in chunks])
            
            # Add embeddings to chunks
            for i, chunk in enumerate(chunks):
                chunk["embedding"] = embeddings[i].tolist()
            
            # Generate document summary
            summary = await self._generate_summary(text, language)
            
            return {
                "text": text,
                "chunks": chunks,
                "metadata": {
                    **metadata,
                    "language": language,
                    "entities": entities[:50],  # Top 50 entities
                    "keywords": keywords[:20],   # Top 20 keywords
                    "word_count": len(text.split()),
                    "chunk_count": len(chunks)
                },
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            raise
    
    async def _extract_pdf(self, file_path: str, options: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """Extract text from PDF with OCR fallback"""
        text = ""
        metadata = {"page_count": 0}
        
        try:
            # Try PyPDF2 first
            with open(file_path, "rb") as file:
                pdf_reader = PyPDF2.PdfReader(file)
                metadata["page_count"] = len(pdf_reader.pages)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    
                    # If no text extracted and OCR is enabled, try OCR
                    if not page_text.strip() and options.get("use_ocr", True):
                        # Convert PDF page to image and OCR
                        # This is a placeholder - actual implementation would use pdf2image
                        logger.info(f"Page {page_num + 1} appears to be scanned, OCR would be applied")
                    
                    text += page_text + "\n\n"
                
                # Extract metadata
                if pdf_reader.metadata:
                    metadata.update({
                        "title": pdf_reader.metadata.get("/Title", ""),
                        "author": pdf_reader.metadata.get("/Author", ""),
                        "subject": pdf_reader.metadata.get("/Subject", ""),
                        "creator": pdf_reader.metadata.get("/Creator", ""),
                    })
        
        except Exception as e:
            logger.error(f"Error extracting PDF: {str(e)}")
            raise
        
        return text.strip(), metadata
    
    async def _extract_text(self, file_path: str, options: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """Extract text from plain text file"""
        encoding = options.get("encoding", "utf-8")
        
        async with aiofiles.open(file_path, "r", encoding=encoding) as file:
            text = await file.read()
        
        metadata = {
            "encoding": encoding,
            "file_size": os.path.getsize(file_path)
        }
        
        return text, metadata
    
    async def _extract_csv(self, file_path: str, options: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """Extract and format CSV data"""
        try:
            df = pd.read_csv(file_path, nrows=options.get("max_rows", 10000))
            
            # Convert to readable text format
            text = f"CSV Data Summary:\n"
            text += f"Columns: {', '.join(df.columns)}\n"
            text += f"Rows: {len(df)}\n\n"
            
            # Add sample data
            text += "Sample Data:\n"
            text += df.head(10).to_string()
            
            # Add statistics for numeric columns
            text += "\n\nStatistics:\n"
            text += df.describe().to_string()
            
            metadata = {
                "columns": list(df.columns),
                "row_count": len(df),
                "column_count": len(df.columns),
                "numeric_columns": list(df.select_dtypes(include=[np.number]).columns),
                "dtypes": df.dtypes.to_dict()
            }
            
            return text, metadata
            
        except Exception as e:
            logger.error(f"Error extracting CSV: {str(e)}")
            raise
    
    async def _extract_image(self, file_path: str, options: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """Extract text from image using OCR"""
        try:
            image = Image.open(file_path)
            
            # Extract text using Tesseract if available
            if pytesseract:
                text = pytesseract.image_to_string(
                    image, 
                    lang=options.get("ocr_lang", "por")  # Portuguese by default
                )
            else:
                text = "[OCR not available - Tesseract not installed]"
                logger.warning("Tesseract OCR is not installed")
            
            metadata = {
                "image_size": image.size,
                "image_mode": image.mode,
                "image_format": image.format
            }
            
            return text, metadata
            
        except Exception as e:
            logger.error(f"Error extracting image: {str(e)}")
            raise
    
    async def process_url(self, url: str, document_type: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process URL content (website or YouTube)"""
        await self.initialize()
        
        options = options or {}
        
        try:
            if document_type == "youtube":
                return await self._process_youtube(url, options)
            elif document_type == "site":
                return await self._process_website(url, options)
            else:
                raise ValueError(f"Unsupported URL type: {document_type}")
            
        except Exception as e:
            logger.error(f"Error processing URL: {str(e)}")
            raise
    
    async def _process_youtube(self, url: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Process YouTube video with transcript"""
        try:
            # Extract video ID from URL
            video_id = self._extract_youtube_id(url)
            
            # Get transcript
            transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
            
            # Try to get Portuguese transcript first, then English, then any
            transcript = None
            for t in transcript_list:
                if t.language_code == "pt":
                    transcript = t
                    break
            
            if not transcript:
                transcript = transcript_list.find_transcript(['pt', 'en', 'es'])
            
            # Fetch the transcript
            transcript_data = transcript.fetch()
            
            # Format transcript text
            text = "\n".join([entry['text'] for entry in transcript_data])
            
            # Create chunks with timestamps
            chunks = []
            current_chunk = {"text": "", "start": 0, "duration": 0}
            
            for entry in transcript_data:
                if len(current_chunk["text"]) + len(entry['text']) > self.config["max_chunk_size"]:
                    chunks.append(current_chunk)
                    current_chunk = {
                        "text": entry['text'],
                        "start": entry['start'],
                        "duration": entry['duration']
                    }
                else:
                    current_chunk["text"] += " " + entry['text']
                    current_chunk["duration"] = entry['start'] + entry['duration'] - current_chunk["start"]
            
            if current_chunk["text"]:
                chunks.append(current_chunk)
            
            # Generate embeddings
            embeddings = await self._generate_embeddings([chunk["text"] for chunk in chunks])
            for i, chunk in enumerate(chunks):
                chunk["embedding"] = embeddings[i].tolist()
                chunk["index"] = i
            
            metadata = {
                "video_id": video_id,
                "url": url,
                "language": transcript.language_code,
                "duration": sum(entry['duration'] for entry in transcript_data),
                "word_count": len(text.split())
            }
            
            # Generate summary
            summary = await self._generate_summary(text, transcript.language_code)
            
            return {
                "text": text,
                "chunks": chunks,
                "metadata": metadata,
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"Error processing YouTube video: {str(e)}")
            raise
    
    async def _process_website(self, url: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Process website content"""
        try:
            # Use trafilatura for content extraction
            downloaded = trafilatura.fetch_url(url)
            
            if not downloaded:
                raise ValueError("Could not fetch URL content")
            
            # Extract main content
            text = trafilatura.extract(
                downloaded,
                include_comments=False,
                include_tables=True,
                deduplicate=True
            )
            
            if not text:
                # Fallback to BeautifulSoup
                soup = BeautifulSoup(downloaded, 'html.parser')
                text = soup.get_text(separator='\n', strip=True)
            
            # Extract metadata
            metadata = trafilatura.extract_metadata(downloaded)
            
            # Detect language
            language = detect(text[:500]) if len(text) > 50 else "pt"
            
            # Create chunks
            chunks = await self._create_smart_chunks(text, options)
            
            # Generate embeddings
            embeddings = await self._generate_embeddings([chunk["text"] for chunk in chunks])
            for i, chunk in enumerate(chunks):
                chunk["embedding"] = embeddings[i].tolist()
            
            # Generate summary
            summary = await self._generate_summary(text, language)
            
            return {
                "text": text,
                "chunks": chunks,
                "metadata": {
                    "url": url,
                    "title": metadata.title if metadata else "Unknown",
                    "author": metadata.author if metadata else "Unknown",
                    "date": metadata.date if metadata else None,
                    "language": language,
                    "word_count": len(text.split())
                },
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"Error processing website: {str(e)}")
            raise
    
    def _extract_youtube_id(self, url: str) -> str:
        """Extract YouTube video ID from URL"""
        import re
        
        patterns = [
            r'(?:v=|\/)([0-9A-Za-z_-]{11}).*',
            r'(?:embed\/)([0-9A-Za-z_-]{11})',
            r'(?:watch\?v=)([0-9A-Za-z_-]{11})',
            r'(?:youtu.be\/)([0-9A-Za-z_-]{11})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        raise ValueError("Could not extract YouTube video ID from URL")
    
    async def _create_smart_chunks(self, text: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create intelligent chunks that preserve context"""
        chunks = []
        
        # Use spaCy for sentence segmentation
        doc = self.nlp(text[:1000000])  # Limit for performance
        sentences = [sent.text for sent in doc.sents]
        
        current_chunk = ""
        current_sentences = []
        chunk_index = 0
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) > self.config["max_chunk_size"]:
                # Save current chunk
                if current_chunk:
                    chunks.append({
                        "text": current_chunk.strip(),
                        "index": chunk_index,
                        "sentence_count": len(current_sentences),
                        "start_sentence": current_sentences[0][:50] if current_sentences else "",
                        "end_sentence": current_sentences[-1][:50] if current_sentences else ""
                    })
                    chunk_index += 1
                
                # Start new chunk with overlap
                overlap_sentences = current_sentences[-2:] if len(current_sentences) > 2 else current_sentences
                current_chunk = " ".join(overlap_sentences) + " " + sentence
                current_sentences = overlap_sentences + [sentence]
            else:
                current_chunk += " " + sentence
                current_sentences.append(sentence)
        
        # Add last chunk
        if current_chunk:
            chunks.append({
                "text": current_chunk.strip(),
                "index": chunk_index,
                "sentence_count": len(current_sentences),
                "start_sentence": current_sentences[0][:50] if current_sentences else "",
                "end_sentence": current_sentences[-1][:50] if current_sentences else ""
            })
        
        return chunks
    
    def _extract_keywords(self, doc) -> List[str]:
        """Extract keywords from spaCy doc"""
        # Extract noun phrases and named entities
        keywords = []
        
        # Add noun phrases
        for chunk in doc.noun_chunks:
            if len(chunk.text.split()) <= 3:  # Max 3 words
                keywords.append(chunk.text.lower())
        
        # Add named entities
        for ent in doc.ents:
            keywords.append(ent.text.lower())
        
        # Count frequency and return top keywords
        from collections import Counter
        keyword_counts = Counter(keywords)
        
        return [kw for kw, _ in keyword_counts.most_common(20)]
    
    async def _generate_embeddings(self, texts: List[str]) -> np.ndarray:
        """Generate embeddings for texts"""
        try:
            # Check cache first
            cache_keys = [self._get_embedding_cache_key(text) for text in texts]
            cached_embeddings = await self._get_cached_embeddings(cache_keys)
            
            # Generate embeddings for non-cached texts
            texts_to_embed = []
            indices_to_embed = []
            
            for i, (text, cached) in enumerate(zip(texts, cached_embeddings)):
                if cached is None:
                    texts_to_embed.append(text)
                    indices_to_embed.append(i)
            
            if texts_to_embed:
                # Generate new embeddings
                new_embeddings = self.embedding_model.encode(
                    texts_to_embed,
                    show_progress_bar=False,
                    convert_to_numpy=True
                )
                
                # Cache new embeddings
                for text, embedding, idx in zip(texts_to_embed, new_embeddings, indices_to_embed):
                    await self._cache_embedding(cache_keys[idx], embedding)
            
            # Combine cached and new embeddings
            all_embeddings = []
            new_idx = 0
            
            for i, cached in enumerate(cached_embeddings):
                if cached is not None:
                    all_embeddings.append(cached)
                else:
                    all_embeddings.append(new_embeddings[new_idx])
                    new_idx += 1
            
            return np.array(all_embeddings)
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise
    
    def _get_embedding_cache_key(self, text: str) -> str:
        """Generate cache key for text embedding"""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        return f"thanos:embedding:{self.config['embedding_model']}:{text_hash}"
    
    async def _get_cached_embeddings(self, keys: List[str]) -> List[Optional[np.ndarray]]:
        """Get embeddings from cache"""
        if not self.redis_client:
            return [None] * len(keys)
        
        try:
            values = await self.redis_client.mget(keys)
            embeddings = []
            
            for value in values:
                if value:
                    embedding = np.frombuffer(value, dtype=np.float32)
                    embeddings.append(embedding)
                else:
                    embeddings.append(None)
            
            return embeddings
            
        except Exception as e:
            logger.warning(f"Error getting cached embeddings: {str(e)}")
            return [None] * len(keys)
    
    async def _cache_embedding(self, key: str, embedding: np.ndarray):
        """Cache embedding"""
        if not self.redis_client:
            return
        
        try:
            await self.redis_client.setex(
                key,
                self.config["cache_ttl"],
                embedding.tobytes()
            )
        except Exception as e:
            logger.warning(f"Error caching embedding: {str(e)}")
    
    async def _generate_summary(self, text: str, language: str) -> str:
        """Generate intelligent summary using LLM"""
        try:
            # Limit text length for summary
            max_length = 4000
            if len(text) > max_length:
                text = text[:max_length] + "..."
            
            # Use appropriate prompt based on language
            if language == "pt":
                prompt = f"Resuma o seguinte texto em no máximo 3 parágrafos:\n\n{text}"
            else:
                prompt = f"Summarize the following text in at most 3 paragraphs:\n\n{text}"
            
            # Try to use available LLM
            if self.providers.get("openai"):
                response = await self._call_openai(prompt, "gpt-3.5-turbo")
                return response
            elif self.providers.get("anthropic"):
                response = await self._call_anthropic(prompt, "claude-3-haiku-20240307")
                return response
            else:
                # Fallback to simple extraction
                sentences = text.split('.')[:5]
                return '. '.join(sentences) + '.'
                
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            # Fallback to simple extraction
            sentences = text.split('.')[:3]
            return '. '.join(sentences) + '.'
    
    async def retrieve_similar_chunks(
        self, 
        query: str, 
        document_id: str,
        chunks: List[Dict[str, Any]],
        top_k: int = 5,
        similarity_threshold: float = 0.5
    ) -> List[Dict[str, Any]]:
        """Retrieve similar chunks using semantic search"""
        try:
            # Generate query embedding
            query_embedding = await self._generate_embeddings([query])
            query_embedding = query_embedding[0]
            
            # Calculate similarities
            chunk_embeddings = np.array([chunk["embedding"] for chunk in chunks])
            similarities = cosine_similarity([query_embedding], chunk_embeddings)[0]
            
            # Get top-k similar chunks
            top_indices = np.argsort(similarities)[-top_k:][::-1]
            
            # Filter by threshold and prepare results
            similar_chunks = []
            for idx in top_indices:
                if similarities[idx] >= similarity_threshold:
                    chunk = chunks[idx].copy()
                    chunk["similarity"] = float(similarities[idx])
                    similar_chunks.append(chunk)
            
            # Log retrieval metrics
            await self._log_retrieval_metrics(
                document_id, 
                len(similar_chunks),
                similarities[top_indices].tolist()
            )
            
            return similar_chunks
            
        except Exception as e:
            logger.error(f"Error retrieving similar chunks: {str(e)}")
            return []
    
    async def _log_retrieval_metrics(self, document_id: str, chunks_found: int, similarities: List[float]):
        """Log retrieval metrics for analytics"""
        # This would log to analytics table in production
        logger.info(f"Retrieval metrics - Document: {document_id}, Chunks: {chunks_found}, Similarities: {similarities}")
    
    def enhance_prompt_with_context(
        self, 
        query: str, 
        similar_chunks: List[Dict[str, Any]], 
        language: str = "pt"
    ) -> str:
        """Enhance prompt with RAG context"""
        if not similar_chunks:
            return query
        
        # Build context from similar chunks
        context_parts = []
        for i, chunk in enumerate(similar_chunks[:3]):  # Use top 3 chunks
            context_parts.append(f"[Trecho {i+1}]: {chunk['text']}")
        
        context = "\n\n".join(context_parts)
        
        # Create enhanced prompt based on language
        if language == "pt":
            enhanced_prompt = f"""Baseando-se nas informações fornecidas abaixo, responda à pergunta de forma precisa e completa.

Informações relevantes:
{context}

Pergunta: {query}

Resposta:"""
        else:
            enhanced_prompt = f"""Based on the information provided below, answer the question accurately and completely.

Relevant information:
{context}

Question: {query}

Answer:"""
        
        return enhanced_prompt
    
    async def stream_ai_response(
        self,
        provider: str,
        model: str,
        message: str,
        context: Dict[str, Any],
        api_key: str
    ):
        """Stream AI response for real-time updates"""
        try:
            if provider == "openai":
                async for chunk in self._stream_openai(message, model, context, api_key):
                    yield chunk
            elif provider == "anthropic":
                async for chunk in self._stream_anthropic(message, model, context, api_key):
                    yield chunk
            elif provider == "groq":
                # Groq doesn't support streaming yet, return full response
                response = await self._call_groq(message, model, context, api_key)
                yield response
            else:
                yield f"[Streaming not supported for {provider}]"
                
        except Exception as e:
            logger.error(f"Error streaming response: {str(e)}")
            yield f"Error: {str(e)}"
    
    async def _stream_openai(self, message: str, model: str, context: Dict[str, Any], api_key: str):
        """Stream OpenAI response"""
        try:
            import openai
            openai.api_key = api_key
            
            messages = [
                {"role": "system", "content": context.get("system_prompt", "You are a helpful assistant.")},
                *context.get("history", []),
                {"role": "user", "content": message}
            ]
            
            stream = await openai.ChatCompletion.acreate(
                model=model,
                messages=messages,
                temperature=context.get("temperature", 0.7),
                max_tokens=context.get("max_tokens", 2000),
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Error streaming OpenAI: {str(e)}")
            yield f"Error: {str(e)}"
    
    async def _stream_anthropic(self, message: str, model: str, context: Dict[str, Any], api_key: str):
        """Stream Anthropic response"""
        try:
            client = anthropic.AsyncAnthropic(api_key=api_key)
            
            messages = [
                *[{"role": msg["role"] if msg["role"] != "system" else "assistant", 
                   "content": msg["content"]} for msg in context.get("history", [])],
                {"role": "user", "content": message}
            ]
            
            stream = await client.messages.create(
                model=model,
                messages=messages,
                system=context.get("system_prompt", "You are a helpful assistant."),
                max_tokens=context.get("max_tokens", 2000),
                temperature=context.get("temperature", 0.7),
                stream=True
            )
            
            async for chunk in stream:
                if chunk.type == "content_block_delta":
                    yield chunk.delta.text
                    
        except Exception as e:
            logger.error(f"Error streaming Anthropic: {str(e)}")
            yield f"Error: {str(e)}"
    
    async def _call_openai(self, prompt: str, model: str) -> str:
        """Call OpenAI API"""
        try:
            response = await openai.ChatCompletion.acreate(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error calling OpenAI: {str(e)}")
            raise
    
    async def _call_anthropic(self, prompt: str, model: str) -> str:
        """Call Anthropic API"""
        try:
            response = await self.providers["anthropic"].messages.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500
            )
            return response.content[0].text
        except Exception as e:
            logger.error(f"Error calling Anthropic: {str(e)}")
            raise
    
    async def _call_groq(self, message: str, model: str, context: Dict[str, Any], api_key: str) -> str:
        """Call Groq API"""
        try:
            client = Groq(api_key=api_key)
            
            messages = [
                {"role": "system", "content": context.get("system_prompt", "You are a helpful assistant.")},
                *context.get("history", []),
                {"role": "user", "content": message}
            ]
            
            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=context.get("temperature", 0.7),
                max_tokens=context.get("max_tokens", 2000)
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error calling Groq: {str(e)}")
            raise
    
    async def analyze_conversation(self, session_id: str, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze conversation for insights"""
        try:
            # Calculate message statistics
            user_messages = [m for m in messages if m["role"] == "user"]
            assistant_messages = [m for m in messages if m["role"] == "assistant"]
            
            # Token analysis
            total_tokens = sum(len(m["content"].split()) for m in messages)
            avg_message_length = total_tokens / len(messages) if messages else 0
            
            # Topic extraction (using simple keyword extraction)
            all_text = " ".join(m["content"] for m in user_messages)
            doc = self.nlp(all_text[:100000])  # Limit for performance
            topics = [ent.text for ent in doc.ents if ent.label_ in ["TOPIC", "ORG", "PRODUCT"]]
            
            # Sentiment analysis (simplified)
            sentiment_scores = []
            for msg in user_messages:
                # In production, use a proper sentiment analysis model
                if any(word in msg["content"].lower() for word in ["obrigado", "ótimo", "excelente", "bom"]):
                    sentiment_scores.append(1.0)
                elif any(word in msg["content"].lower() for word in ["ruim", "péssimo", "horrível", "mal"]):
                    sentiment_scores.append(-1.0)
                else:
                    sentiment_scores.append(0.0)
            
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.0
            
            return {
                "session_id": session_id,
                "message_count": len(messages),
                "user_messages": len(user_messages),
                "assistant_messages": len(assistant_messages),
                "total_tokens": total_tokens,
                "avg_message_length": avg_message_length,
                "topics": list(set(topics))[:10],
                "sentiment": {
                    "average": avg_sentiment,
                    "positive": sum(1 for s in sentiment_scores if s > 0),
                    "neutral": sum(1 for s in sentiment_scores if s == 0),
                    "negative": sum(1 for s in sentiment_scores if s < 0)
                },
                "conversation_duration": (messages[-1]["created_at"] - messages[0]["created_at"]).total_seconds() if len(messages) > 1 else 0
            }
            
        except Exception as e:
            logger.error(f"Error analyzing conversation: {str(e)}")
            return {}
    
    async def cleanup_old_data(self, days: int = 30):
        """Clean up old data and cache"""
        try:
            # Clear old Redis cache entries
            if self.redis_client:
                # In production, implement proper cache cleanup
                logger.info(f"Cleaning up cache entries older than {days} days")
            
            # Return cleanup stats
            return {
                "cache_entries_removed": 0,
                "old_sessions_archived": 0,
                "space_freed_mb": 0
            }
            
        except Exception as e:
            logger.error(f"Error cleaning up data: {str(e)}")
            return {}

# Global instance
ultra_thanos_service = UltraThanosService()