"""
Direct views for streaming that bypass the Django REST Framework
"""
import os
import logging
import traceback
from django.http import StreamingHttpResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_GET
from dotenv import load_dotenv
from anthropic import Anthropic

from .models import ChatMessage

# Configurar logging
logger = logging.getLogger(__name__)

# Carregar variáveis de ambiente
load_dotenv()

@csrf_exempt
@require_GET
def direct_stream_message(request):
    """
    Stream messages using Server-Sent Events (SSE) - Django direct view
    This view completely bypasses the Django REST Framework
    """
    logger.info("Endpoint de streaming direto chamado!")
    
    message = request.GET.get('message', '')
    if not message:
        # Retornar uma resposta HTTP normal, não JSON
        logger.error("Parâmetro 'message' não fornecido")
        response = StreamingHttpResponse("Error: Message is required", status=400)
        add_cors_headers(response)
        return response
    
    logger.info(f"Processando mensagem: {message[:30]}...")
    
    # Salvar mensagem do usuário
    ChatMessage.objects.create(
        user=None,  # Usuário anônimo para desenvolvimento
        role='user',
        content=message
    )
    
    # Definir o prompt do sistema
    SECOND_BRAIN_PROMPT = """Você é um assistente pessoal especializado em agendamento de revisões espaçadas e organização de estudos. 
    Sua tarefa é ajudar a programar as próximas revisões baseadas no resultado das revisões anteriores feitas.
    
    Fórmula para calcular o intervalo de dias até a próxima revisão com base na porcentagem de acertos:
    - Até 50%: 2 dias
    - 51-55%: 7 dias
    - 56-60%: 14 dias
    - 61-65%: 18 dias
    - 66-75%: 24 dias
    - 76-80%: 30 dias"""

    def event_stream():
        response_text = ""
        try:
            logger.info("Iniciando event_stream")
            api_key = os.getenv('ANTHROPIC_API_KEY')
            
            # Verificar se temos uma chave API
            if not api_key:
                logger.warning("API key não encontrada, usando modo fallback...")
                # Modo fallback - retornar uma resposta simples
                fallback_response = "Estou usando um modo de fallback pois a chave API não está configurada. Como posso ajudar com seu agendamento de revisões?"
                yield f"data: {fallback_response}\n\n"
                yield "event: complete\ndata: \n\n"
                
                # Salvar a resposta de fallback
                ChatMessage.objects.create(
                    user=None,
                    role='assistant',
                    content=fallback_response
                )
                return
            
            try:
                logger.info(f"Streaming usando API key (primeiros 5 chars): {api_key[:5]}...")
                client = Anthropic(api_key=api_key)
                
                # Tentar criar o stream da API Anthropic
                try:
                    logger.info("Iniciando stream da API Anthropic...")
                    with client.messages.stream(
                        model="claude-3-haiku-20240307",  # Modelo mais leve e mais barato
                        max_tokens=1024,
                        system=SECOND_BRAIN_PROMPT,
                        messages=[{"role": "user", "content": message}]
                    ) as stream:
                        for text in stream.text_stream:
                            if text:  # Only yield non-empty chunks
                                response_text += text
                                # Mensagem de debug para cada chunk recebido
                                logger.info(f"Chunk recebido: {text[:20]}...")
                                # Format as SSE data
                                yield f"data: {text}\n\n"
                        
                        # Salvar a resposta completa no banco de dados
                        if response_text:
                            ChatMessage.objects.create(
                                user=None,  # Usuário anônimo para desenvolvimento
                                role='assistant',
                                content=response_text
                            )
                        
                        # Send a final event to signal completion
                        logger.info("Stream completo, enviando evento de conclusão")
                        yield "event: complete\ndata: \n\n"
                except Exception as stream_error:
                    logger.error(f"Erro específico no streaming da API: {str(stream_error)}")
                    logger.error(traceback.format_exc())
                    
                    # Modo fallback quando a API falha
                    fallback_response = f"Desculpe, estou tendo problemas para me conectar aos servidores. Aqui está uma resposta simples para sua pergunta: '{message}'\n\nSua pergunta foi registrada e será processada assim que a conexão for restabelecida."
                    yield f"data: {fallback_response}\n\n"
                    yield "event: complete\ndata: \n\n"
                    
                    # Salvar a resposta de fallback
                    ChatMessage.objects.create(
                        user=None,
                        role='assistant',
                        content=fallback_response
                    )
            except Exception as client_error:
                logger.error(f"Erro ao criar cliente Anthropic: {str(client_error)}")
                # Modo fallback quando não podemos criar o cliente
                fallback_response = "Estou tendo problemas técnicos para processar sua solicitação. Por favor, tente novamente mais tarde."
                yield f"data: {fallback_response}\n\n"
                yield "event: complete\ndata: \n\n"
                
                # Salvar a resposta de fallback
                ChatMessage.objects.create(
                    user=None,
                    role='assistant',
                    content=fallback_response
                )

        except Exception as e:
            error_msg = f"Erro: {str(e)}"
            logger.error(f"Erro no streaming: {error_msg}")
            logger.error(traceback.format_exc())
            
            # Modo fallback para qualquer outro erro
            fallback_response = "Ocorreu um erro inesperado. Por favor, tente novamente."
            yield f"data: {fallback_response}\n\n"
            yield "event: complete\ndata: \n\n"
            
            # Salvar a resposta de fallback
            ChatMessage.objects.create(
                user=None,
                role='assistant',
                content=fallback_response
            )

    # Criar resposta de streaming
    logger.info("Criando resposta de streaming...")
    response = StreamingHttpResponse(
        event_stream(),
        content_type='text/event-stream'
    )
    
    # Adicionar cabeçalhos CORS e de cache explicitamente
    logger.info("Adicionando cabeçalhos CORS...")
    add_cors_headers(response)
    
    logger.info("Retornando resposta de streaming")
    return response

@csrf_exempt
@require_GET
def test_stream(request):
    """
    Simple test endpoint for SSE
    """
    logger.info("Test stream endpoint called")
    
    def event_stream():
        yield "data: Test message 1\n\n"
        yield "data: Test message 2\n\n"
        yield "event: complete\ndata: \n\n"
    
    response = StreamingHttpResponse(
        event_stream(),
        content_type='text/event-stream'
    )
    add_cors_headers(response)
    return response

def add_cors_headers(response):
    """
    Add CORS headers to response
    """
    response['Cache-Control'] = 'no-cache, no-transform'
    response['X-Accel-Buffering'] = 'no'  # Desativar buffering para Nginx
    response['Access-Control-Allow-Origin'] = '*'  # Permitir solicitações de qualquer origem para desenvolvimento
    response['Access-Control-Allow-Headers'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    return response 