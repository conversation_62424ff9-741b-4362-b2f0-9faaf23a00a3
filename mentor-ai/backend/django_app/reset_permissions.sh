#!/bin/bash

# Reset permissions script for PostgreSQL database used by Django
echo "Resetting PostgreSQL database permissions..."

# Load environment variables from .env if it exists
ENV_FILE=".env"
if [ -f "$ENV_FILE" ]; then
    echo "Loading environment variables from $ENV_FILE"
    export $(grep -v '^#' "$ENV_FILE" | xargs)
else
    echo "Warning: .env file not found, using default values"
fi

# Default database connection variables
DB_NAME=${POSTGRES_DB:-mentor_ai_db}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASS=${POSTGRES_PASSWORD:-postgres}
DB_HOST=${POSTGRES_HOST:-localhost}
DB_PORT=${POSTGRES_PORT:-5432}

echo "Using database: $DB_NAME on $DB_HOST:$DB_PORT"

# First, get the list of all tables in the database
echo "Getting list of tables in $DB_NAME..."
TABLES=$(PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT tablename FROM pg_tables WHERE schemaname='public';")

# Grant permissions to each table individually
echo "Granting permissions on all tables..."
for TABLE in $TABLES; do
    echo "  - Granting permissions on $TABLE"
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "ALTER TABLE \"$TABLE\" OWNER TO \"$DB_USER\";"
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "GRANT ALL PRIVILEGES ON TABLE \"$TABLE\" TO \"$DB_USER\";"
done

# Get the list of all sequences in the database
echo "Getting list of sequences in $DB_NAME..."
SEQUENCES=$(PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema='public';")

# Grant permissions to each sequence individually
echo "Granting permissions on all sequences..."
for SEQ in $SEQUENCES; do
    echo "  - Granting permissions on $SEQ"
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "ALTER SEQUENCE \"$SEQ\" OWNER TO \"$DB_USER\";"
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "GRANT ALL PRIVILEGES ON SEQUENCE \"$SEQ\" TO \"$DB_USER\";"
done

# Try to recreate the second_brain_chatmessage table if it's causing problems
echo "Attempting to fix second_brain_chatmessage table specifically..."
PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
BEGIN;
-- Save current data if the table exists
CREATE TABLE IF NOT EXISTS second_brain_chatmessage_backup AS 
SELECT * FROM second_brain_chatmessage WHERE 1=1;

-- Drop and recreate with explicit owner
DROP TABLE IF EXISTS second_brain_chatmessage CASCADE;
CREATE TABLE second_brain_chatmessage (
    id BIGSERIAL PRIMARY KEY,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id INTEGER REFERENCES auth_user(id) ON DELETE CASCADE NULL
);

-- Set the proper owner
ALTER TABLE second_brain_chatmessage OWNER TO \"$DB_USER\";

-- Restore data if backup exists
INSERT INTO second_brain_chatmessage (id, role, content, timestamp, user_id)
SELECT id, role, content, timestamp, user_id 
FROM second_brain_chatmessage_backup
WHERE EXISTS (SELECT 1 FROM second_brain_chatmessage_backup LIMIT 1);

-- Clean up
DROP TABLE IF EXISTS second_brain_chatmessage_backup;
COMMIT;
"

# Grant default permissions to the current user for future tables
echo "Setting default privileges for future objects..."
PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO \"$DB_USER\";"
PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO \"$DB_USER\";"

echo "Permissions reset complete!"
echo "You can now run the server with: python manage.py runserver 0.0.0.0:8003"