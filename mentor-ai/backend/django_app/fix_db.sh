#!/bin/bash

# Comprehensive fix script for PostgreSQL database used by Django
echo "Fixing PostgreSQL database issues..."

# Load environment variables from .env if it exists
ENV_FILE=".env"
if [ -f "$ENV_FILE" ]; then
    echo "Loading environment variables from $ENV_FILE"
    export $(grep -v '^#' "$ENV_FILE" | xargs)
else
    echo "Warning: .env file not found, using default values"
fi

# Default database connection variables
DB_NAME=${POSTGRES_DB:-mentor_ai_db}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASS=${POSTGRES_PASSWORD:-postgres}
DB_HOST=${POSTGRES_HOST:-localhost}
DB_PORT=${POSTGRES_PORT:-5432}

echo "Using database: $DB_NAME on $DB_HOST:$DB_PORT"

# Step 1: Check if database exists and create if needed
echo "Step 1: Checking database..."
DB_EXISTS=$(PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -t -c "SELECT 1 FROM pg_database WHERE datname='$DB_NAME'")
if [ -z "$DB_EXISTS" ]; then
    echo "Database $DB_NAME does not exist. Creating..."
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $DB_NAME;"
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
else
    echo "Database $DB_NAME already exists."
fi

# Step 2: Apply migrations if needed
echo "Step 2: Applying Django migrations..."
python manage.py makemigrations
python manage.py migrate

# Step 3: Try to recreate problematic tables as a last resort
echo "Step 3: Attempting to fix specific table issues..."

# First check if the table exists
TABLE_EXISTS=$(PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT 1 FROM information_schema.tables WHERE table_name='second_brain_chatmessage' AND table_schema='public'")

if [ -n "$TABLE_EXISTS" ]; then
    echo "Found second_brain_chatmessage table, attempting to fix permissions..."
    
    # Try direct ownership change first
    echo "Changing ownership of table..."
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "ALTER TABLE second_brain_chatmessage OWNER TO $DB_USER;"
    
    # Try direct grants
    echo "Granting privileges..."
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "GRANT ALL PRIVILEGES ON TABLE second_brain_chatmessage TO $DB_USER;"
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "GRANT ALL PRIVILEGES ON SEQUENCE second_brain_chatmessage_id_seq TO $DB_USER;"
else
    echo "Table second_brain_chatmessage does not exist. Will be created by migrations."
fi

# Step 4: Set default privileges for future objects
echo "Step 4: Setting default privileges for future objects..."
PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $DB_USER;"
PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $DB_USER;"

# Step 5: Verify permissions
echo "Step 5: Verifying table permissions..."
echo "Tables in database:"
PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "\dt"

echo "Fix process complete!"
echo "You can now run the server with: python manage.py runserver 0.0.0.0:8003"