# Generated by Django 5.1.6 on 2025-03-12 02:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mentorship_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('color', models.Char<PERSON>ield(default='#42b983', max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='StudyEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=255)),
                ('start', models.DateTimeField()),
                ('end', models.DateTimeField()),
                ('description', models.TextField(blank=True, null=True)),
                ('revisionType', models.CharField(choices=[('Teórica', 'Teórica'), ('Prática', 'Prática'), ('Mista', 'Mista'), ('Flashcards', 'Flashcards'), ('Resumo', 'Resumo')], default='Teórica', max_length=20)),
                ('priority', models.CharField(choices=[('Baixa', 'Baixa'), ('Média', 'Média'), ('Alta', 'Alta'), ('Urgente', 'Urgente')], default='Média', max_length=20)),
                ('color', models.CharField(blank=True, max_length=20, null=True)),
                ('isRevision', models.BooleanField(default=True)),
                ('progress', models.IntegerField(default=0)),
                ('completed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='study_events', to=settings.AUTH_USER_MODEL)),
                ('subject', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='events', to='mentorship_app.subject')),
            ],
        ),
        migrations.CreateModel(
            name='RevisionStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('duration', models.IntegerField(default=0)),
                ('questions_total', models.IntegerField(default=0)),
                ('questions_correct', models.IntegerField(default=0)),
                ('difficulty', models.CharField(choices=[('Fácil', 'Fácil'), ('Médio', 'Médio'), ('Difícil', 'Difícil')], default='Médio', max_length=20)),
                ('revision_type', models.CharField(choices=[('Teórica', 'Teórica'), ('Prática', 'Prática'), ('Mista', 'Mista'), ('Flashcards', 'Flashcards'), ('Resumo', 'Resumo')], default='Teórica', max_length=20)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revision_stats', to=settings.AUTH_USER_MODEL)),
                ('subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revision_stats', to='mentorship_app.subject')),
            ],
            options={
                'verbose_name': 'Revision Statistics',
                'verbose_name_plural': 'Revision Statistics',
            },
        ),
        migrations.DeleteModel(
            name='StudyPlan',
        ),
        migrations.DeleteModel(
            name='User',
        ),
    ]
