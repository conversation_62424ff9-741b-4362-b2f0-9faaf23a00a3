#!/bin/bash

echo "Starting all services for Mentor AI application..."

# Function to check if PostgreSQL is running
check_postgres() {
  pg_isready -h localhost -p 5432 > /dev/null 2>&1
}

# Start PostgreSQL if not running
if ! check_postgres; then
  echo "Starting PostgreSQL..."
  pg_ctl -D /opt/homebrew/var/postgresql@14 start
  
  # Wait for PostgreSQL to start
  echo "Waiting for PostgreSQL to start..."
  for i in {1..30}; do
    if check_postgres; then
      echo "PostgreSQL is running."
      break
    fi
    sleep 1
    if [ "$i" = 30 ]; then
      echo "Failed to start PostgreSQL. Check your PostgreSQL installation."
      exit 1
    fi
  done
else
  echo "PostgreSQL is already running."
fi

# Create database if it doesn't exist
echo "Checking if database exists..."
if ! psql -U postgres -lqt | cut -d \| -f 1 | grep -qw mentor_ai_db; then
  echo "Creating database mentor_ai_db..."
  createdb mentor_ai_db -U postgres
  echo "Database created."
else
  echo "Database mentor_ai_db already exists."
fi

# Start Django server
echo "Starting Django server..."
cd "$(dirname "$0")"
python manage.py migrate
echo "Starting Django server on port 8003..."
echo "Open a new terminal and run: cd $(pwd) && python manage.py runserver 0.0.0.0:8003"

# Start FastAPI
echo "To start FastAPI server, open a new terminal and run:"
echo "cd $(dirname "$(pwd)")/fastapi_app && uvicorn main:app --host 0.0.0.0 --port 8001"

# Start Vue.js frontend
echo "To start Vue.js frontend, open a new terminal and run:"
echo "cd $(dirname "$(dirname "$(pwd)")")/frontend && npm run serve -- --port 8082"

echo "Instructions for verifying services:"
echo "1. Django Backend: curl -s http://127.0.0.1:8003/ | head -10"
echo "2. Streaming endpoint: curl -s http://127.0.0.1:8003/test-stream/ | head -3"
echo "3. FastAPI: Open http://127.0.0.1:8001/docs in your browser"
echo "4. Frontend: Open http://localhost:8082 in your browser"
echo ""
echo "Access URLs:"
echo "- Frontend: http://localhost:8082"
echo "- Backend Django API: http://localhost:8003"
echo "- API FastAPI (AI): http://localhost:8001/docs"