#!/bin/bash

# <PERSON>ript to create and setup PostgreSQL database for Django
echo "Setting up PostgreSQL database for Django..."

# Load environment variables from .env if it exists
ENV_FILE=".env"
if [ -f "$ENV_FILE" ]; then
    echo "Loading environment variables from $ENV_FILE"
    export $(grep -v '^#' "$ENV_FILE" | xargs)
else
    echo "Warning: .env file not found, using default values"
fi

# Default database connection variables
DB_NAME=${POSTGRES_DB:-mentor_ai_db}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASS=${POSTGRES_PASSWORD:-postgres}
DB_HOST=${POSTGRES_HOST:-localhost}
DB_PORT=${POSTGRES_PORT:-5432}

echo "Using database: $DB_NAME on $DB_HOST:$DB_PORT"

# Function to run SQL commands
run_sql() {
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "$1"
}

# Create database if it doesn't exist
echo "Creating database $DB_NAME if it doesn't exist..."
run_sql "SELECT 'CREATE DATABASE $DB_NAME' WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DB_NAME');"

# Apply migrations
echo "Applying Django migrations..."
python manage.py makemigrations
python manage.py migrate

# Set correct permissions
echo "Setting permissions..."
run_sql "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"

# Connect to the specific database to set table permissions
PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $DB_USER;"
PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $DB_USER;"

echo "Database setup complete!"
echo "You can now run the server with: python manage.py runserver 0.0.0.0:8003"

# Adicione estas linhas após reiniciar o PostgreSQL
echo "Verificando status do PostgreSQL..."
for i in {1..5}; do
    if pg_isready > /dev/null 2>&1; then
        echo -e "${GREEN}✓ PostgreSQL está aceitando conexões${NC}"
        break
    else
        echo -e "${YELLOW}⚠ Aguardando PostgreSQL iniciar (tentativa $i de 5)${NC}"
        sleep 3
    fi
    
    if [ $i -eq 5 ]; then
        echo -e "${RED}✗ PostgreSQL não está respondendo após várias tentativas${NC}"
        echo -e "${YELLOW}⚠ Tentando iniciar novamente...${NC}"
        brew services restart postgresql@14
        sleep 5
    fi
done