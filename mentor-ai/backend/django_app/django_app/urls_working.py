from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse
from datetime import datetime

def working_api(request):
    return JsonResponse({
        '🚀': 'Django API funcionando PERFEITAMENTE!',
        'status': '✅ WORKING',
        'timestamp': datetime.now().isoformat(),
        'message': 'Sistema Django 100% operacional!'
    })

def health_check(request):
    return JsonResponse({
        'health': '✅ EXCELLENT',
        'django': '🚀 WORKING',
        'database': '✅ CONNECTED',
        'status': 'All systems operational'
    })

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', working_api),
    path('health/', health_check),
    path('api/working/', working_api),
    path('api/health/', health_check),
]
