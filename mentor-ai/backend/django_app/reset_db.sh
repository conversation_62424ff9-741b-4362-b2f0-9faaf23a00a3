#!/bin/bash

# Reset database script for Django
echo "Resetting Django SQLite database..."

# Remove SQLite database if it exists
if [ -f db.sqlite3 ]; then
    echo "Removing existing SQLite database..."
    rm db.sqlite3
fi

# Find and remove any .pyc files that might cause import issues
find . -name "*.pyc" -delete
find . -path "*/migrations/*.py" -not -name "__init__.py" -delete

# Create initial migrations
echo "Creating new migrations..."
python manage.py makemigrations mentorship_app
python manage.py makemigrations second_brain

# Apply migrations
echo "Applying migrations..."
python manage.py migrate

echo "Database reset complete!"
echo "You can now run the server with: python manage.py runserver 0.0.0.0:8888"