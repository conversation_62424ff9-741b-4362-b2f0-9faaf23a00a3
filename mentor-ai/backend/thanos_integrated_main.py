"""
Main integrado com Thanos
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Importar database e criar tabelas
from backend.fastapi_app.db.database import engine, Base
from backend.thanos.models import ThanosDocument, ThanosChunk, ThanosSession, ThanosMessage

# Importar routers
from backend.fastapi_app.routers import flashcards, second_brain
from backend.thanos.router import router as thanos_router

# Criar app
app = FastAPI(title="Mentor AI API with Thanos", version="2.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Criar tabelas
Base.metadata.create_all(bind=engine)

# Registrar routers
app.include_router(flashcards.router, prefix="/api", tags=["flashcards"])
app.include_router(second_brain.router, prefix="/api", tags=["second-brain"])
app.include_router(thanos_router)

@app.get("/")
async def root():
    return {
        "message": "Mentor AI API with Thanos Integration",
        "version": "2.0.0",
        "endpoints": {
            "flashcards": "/api/flashcards",
            "second_brain": "/api/second-brain", 
            "thanos": "/api/thanos"
        }
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "mentor-ai-thanos"}

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8001))
    uvicorn.run("thanos_integrated_main:app", host="0.0.0.0", port=port, reload=True)
