"""
Sistema de gamificação para o gerador de questões médicas
"""

import os
import logging
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
from uuid import uuid4

logger = logging.getLogger(__name__)

class GamificationManager:
    """
    Gerenciador de gamificação para aumentar engajamento no gerador de questões
    
    Esta classe implementa:
    1. Sistema de XP e níveis
    2. Conquistas e medalhas
    3. Estatísticas de progresso do usuário
    4. Recomendações personalizadas
    """
    
    def __init__(self):
        """Inicializa o gerenciador de gamificação"""
        # Configurações de XP e progressão
        self.level_thresholds = [0, 100, 250, 450, 700, 1000, 1400, 1900, 2500, 3200]
        self.xp_rewards = {
            "question_answered": 10,
            "question_correct": 15,
            "daily_login": 20,
            "questions_generated": 5,  # por questão
            "feedback_provided": 15,
            "achievement_unlocked": 50
        }
        
        # Conquistas disponíveis
        self.achievements = self._init_achievements()
        
        logger.info("Gerenciador de gamificação inicializado")
    
    def _init_achievements(self) -> Dict[str, Dict[str, Any]]:
        """Inicializa as conquistas disponíveis no sistema"""
        return {
            "first_questions": {
                "id": "first_questions",
                "name": "Primeiras Questões",
                "description": "Gere suas primeiras 10 questões",
                "icon": "fas fa-star",
                "requirement": {"questions_generated": 10},
                "xp_reward": 50
            },
            "question_master": {
                "id": "question_master",
                "name": "Mestre das Questões",
                "description": "Gere 100 questões ao total",
                "icon": "fas fa-crown",
                "requirement": {"questions_generated": 100},
                "xp_reward": 100
            },
            "feedback_contributor": {
                "id": "feedback_contributor",
                "name": "Contribuidor",
                "description": "Forneça feedback 5 vezes",
                "icon": "fas fa-comment",
                "requirement": {"feedback_count": 5},
                "xp_reward": 75
            },
            "cardiology_explorer": {
                "id": "cardiology_explorer",
                "name": "Explorador de Cardiologia",
                "description": "Gere 25 questões de cardiologia",
                "icon": "fas fa-heartbeat",
                "requirement": {"specialty_questions": {"cardiology": 25}},
                "xp_reward": 75
            },
            "neurology_explorer": {
                "id": "neurology_explorer",
                "name": "Explorador de Neurologia",
                "description": "Gere 25 questões de neurologia",
                "icon": "fas fa-brain",
                "requirement": {"specialty_questions": {"neurology": 25}},
                "xp_reward": 75
            },
            "knowledge_seeker": {
                "id": "knowledge_seeker",
                "name": "Buscador de Conhecimento",
                "description": "Explore 3 especialidades diferentes",
                "icon": "fas fa-book",
                "requirement": {"specialties_explored": 3},
                "xp_reward": 100
            },
            "expert_level": {
                "id": "expert_level",
                "name": "Nível Expert",
                "description": "Atinja o nível 5",
                "icon": "fas fa-award",
                "requirement": {"user_level": 5},
                "xp_reward": 150
            }
        }
    
    def calculate_level(self, xp: int) -> Dict[str, Any]:
        """
        Calcula o nível atual do usuário com base no XP
        
        Args:
            xp: Pontos de experiência do usuário
            
        Returns:
            Dicionário com nível, XP atual, XP para próximo nível e porcentagem
        """
        level = 1
        for i, threshold in enumerate(self.level_thresholds[1:], 1):
            if xp >= threshold:
                level = i + 1
            else:
                break
        
        current_threshold = self.level_thresholds[level - 1]
        next_threshold = self.level_thresholds[level] if level < len(self.level_thresholds) else current_threshold * 1.5
        
        xp_for_current_level = xp - current_threshold
        xp_needed_for_next = next_threshold - current_threshold
        percentage = min(100, round((xp_for_current_level / xp_needed_for_next) * 100))
        
        return {
            "level": level,
            "current_xp": xp_for_current_level,
            "next_level_xp": xp_needed_for_next,
            "xp_percentage": percentage
        }
        
    def award_xp(self, user_stats: Dict[str, Any], action: str, quantity: int = 1) -> Dict[str, Any]:
        """
        Concede XP ao usuário por uma ação realizada
        
        Args:
            user_stats: Estatísticas atuais do usuário
            action: Tipo de ação realizada
            quantity: Quantidade de ações (padrão: 1)
            
        Returns:
            Estatísticas atualizadas do usuário
        """
        # Iniciar estatísticas se necessário
        if "xp" not in user_stats:
            user_stats["xp"] = 0
            
        if "xp_history" not in user_stats:
            user_stats["xp_history"] = []
            
        # Calcular XP a conceder
        xp_gained = 0
        if action in self.xp_rewards:
            xp_gained = self.xp_rewards[action] * quantity
            
        if xp_gained > 0:
            # Registrar histórico de XP
            user_stats["xp_history"].append({
                "action": action,
                "xp": xp_gained,
                "timestamp": datetime.now().isoformat()
            })
            
            # Atualizar XP total
            user_stats["xp"] += xp_gained
            
            # Atualizar informações de nível
            level_info = self.calculate_level(user_stats["xp"])
            user_stats.update(level_info)
            
        return user_stats
        
    def check_achievements(self, user_stats: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Verifica e desbloqueia conquistas
        
        Args:
            user_stats: Estatísticas do usuário
            
        Returns:
            Lista de novas conquistas desbloqueadas
        """
        # Inicializar conquistas do usuário se necessário
        if "achievements" not in user_stats:
            user_stats["achievements"] = []
            
        # Converter lista de conquistas para um set para facilitar verificação
        unlocked_achievements = set(ach["id"] for ach in user_stats["achievements"])
        
        # Armazenar novas conquistas desbloqueadas
        newly_unlocked = []
        
        # Verificar cada conquista
        for achievement_id, achievement in self.achievements.items():
            # Se já desbloqueada, pular
            if achievement_id in unlocked_achievements:
                continue
                
            # Verificar requisitos
            meets_requirements = True
            for req_key, req_value in achievement["requirement"].items():
                if req_key == "questions_generated":
                    if user_stats.get("questions_generated", 0) < req_value:
                        meets_requirements = False
                        break
                elif req_key == "feedback_count":
                    if user_stats.get("feedback_count", 0) < req_value:
                        meets_requirements = False
                        break
                elif req_key == "specialty_questions":
                    for specialty, count in req_value.items():
                        if user_stats.get("specialty_counts", {}).get(specialty, 0) < count:
                            meets_requirements = False
                            break
                elif req_key == "specialties_explored":
                    explored = sum(1 for count in user_stats.get("specialty_counts", {}).values() if count > 0)
                    if explored < req_value:
                        meets_requirements = False
                        break
                elif req_key == "user_level":
                    if user_stats.get("level", 1) < req_value:
                        meets_requirements = False
                        break
            
            # Se atende aos requisitos, desbloquear
            if meets_requirements:
                # Criar cópia da conquista para o usuário
                unlocked_achievement = {
                    "id": achievement["id"],
                    "name": achievement["name"],
                    "description": achievement["description"],
                    "icon": achievement["icon"],
                    "unlocked_at": datetime.now().isoformat()
                }
                
                # Adicionar à lista de conquistas do usuário
                user_stats["achievements"].append(unlocked_achievement)
                
                # Conceder XP pela conquista
                user_stats = self.award_xp(user_stats, "achievement_unlocked")
                
                # Adicionar à lista de novas conquistas
                newly_unlocked.append({
                    **unlocked_achievement,
                    "xp_reward": achievement["xp_reward"]
                })
                
        return newly_unlocked
        
    def update_stats_for_generation(self, user_stats: Dict[str, Any], generation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Atualiza estatísticas do usuário após geração de questões
        
        Args:
            user_stats: Estatísticas atuais do usuário
            generation_data: Dados da geração realizada
            
        Returns:
            Estatísticas atualizadas
        """
        # Inicializar estatísticas se necessário
        if "questions_generated" not in user_stats:
            user_stats["questions_generated"] = 0
            
        if "specialty_counts" not in user_stats:
            user_stats["specialty_counts"] = {}
            
        if "generation_history" not in user_stats:
            user_stats["generation_history"] = []
            
        # Extrair dados da geração
        question_count = generation_data.get("question_count", 0)
        specialty = generation_data.get("specialty", "general_medicine")
        
        # Atualizar contadores
        user_stats["questions_generated"] += question_count
        user_stats["specialty_counts"][specialty] = user_stats["specialty_counts"].get(specialty, 0) + question_count
        
        # Adicionar ao histórico
        user_stats["generation_history"].append({
            "id": str(uuid4()),
            "question_count": question_count,
            "specialty": specialty,
            "timestamp": datetime.now().isoformat()
        })
        
        # Limitar tamanho do histórico
        max_history = 50
        if len(user_stats["generation_history"]) > max_history:
            user_stats["generation_history"] = user_stats["generation_history"][-max_history:]
            
        # Conceder XP
        user_stats = self.award_xp(user_stats, "questions_generated", question_count)
        
        # Verificar novas conquistas
        new_achievements = self.check_achievements(user_stats)
        
        # Adicionar conquistas à resposta se houver
        if new_achievements:
            user_stats["new_achievements"] = new_achievements
            
        return user_stats
        
    def generate_recommendations(self, user_stats: Dict[str, Any]) -> Dict[str, Any]:
        """
        Gera recomendações personalizadas baseadas no histórico do usuário
        
        Args:
            user_stats: Estatísticas do usuário
            
        Returns:
            Recomendações personalizadas
        """
        recommendations = {
            "difficulty": 2,  # Padrão
            "specialty": "general_medicine",  # Padrão
            "questionTypes": ["multiple_choice"]  # Padrão
        }
        
        # Se não há histórico suficiente, retornar padrões
        if not user_stats.get("generation_history", []):
            return recommendations
            
        # Analisar especialidades mais usadas
        specialty_counts = user_stats.get("specialty_counts", {})
        if specialty_counts:
            # Encontrar especialidade mais frequente
            top_specialty = max(specialty_counts.items(), key=lambda x: x[1])[0]
            recommendations["specialty"] = top_specialty
            
        # Ajustar dificuldade com base no nível do usuário
        level = user_stats.get("level", 1)
        if level >= 5:
            recommendations["difficulty"] = 3
        elif level >= 3:
            recommendations["difficulty"] = 2
        
        # Tipos de questão recomendados
        if level >= 4:
            recommendations["questionTypes"] = ["multiple_choice", "case_study"]
        elif level >= 2:
            recommendations["questionTypes"] = ["multiple_choice", "true_false"]
            
        return recommendations

# Instância global para uso em endpoints
gamification_manager = GamificationManager() 