"""
Modelagem de dificuldade de questões usando Item Response Theory (IRT)
"""

import math
import logging
import numpy as np
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class IRTCalibrator:
    """
    Calibrador de dificuldade de questões usando Item Response Theory (IRT)
    
    IRT é um paradigma para design, análise e pontuação de testes, questionários e
    instrumentos similares que medem habilidades, atitudes ou outras variáveis.
    
    Este módulo implementa um modelo de 3 parâmetros:
    - Discriminação (a): Quão bem a questão diferencia entre usuários de diferentes níveis
    - Dificuldade (b): O nível de habilidade necessário para ter 50% de chance de acertar
    - Adivinhação (c): A probabilidade de um usuário de baixa habilidade acertar por acaso
    """
    
    def __init__(self, target_difficulty: float = 0.7):
        """
        Inicializa o calibrador de dificuldade
        
        Args:
            target_difficulty: Dificuldade alvo (0-1), onde maior valor requer mais habilidade
        """
        self.target_difficulty = target_difficulty
        logger.info(f"Calibrador IRT inicializado com dificuldade alvo: {target_difficulty}")
    
    def predict_success_probability(
        self, 
        ability: float, 
        difficulty: float, 
        discrimination: float = 1.0, 
        guessing: float = 0.25
    ) -> float:
        """
        Calcula a probabilidade de acerto usando o modelo de 3 parâmetros IRT
        
        Args:
            ability: Nível de habilidade do usuário (theta)
            difficulty: Nível de dificuldade da questão (b)
            discrimination: Parâmetro de discriminação da questão (a)
            guessing: Parâmetro de adivinhação (c)
            
        Returns:
            Probabilidade de acerto (0-1)
        """
        # Modelo logístico de 3 parâmetros
        # P(θ) = c + (1-c) / (1 + exp(-a(θ-b)))
        try:
            # Evitar overflow em exp
            x = discrimination * (ability - difficulty)
            if x > 20:  # Limite para evitar overflow
                return 1.0
            if x < -20:  # Limite para evitar underflow
                return guessing
                
            exp_term = math.exp(-x)
            probability = guessing + (1 - guessing) / (1 + exp_term)
            
            # Garantir que está no intervalo [0, 1]
            return max(0.0, min(1.0, probability))
        except Exception as e:
            logger.error(f"Erro ao calcular probabilidade IRT: {e}")
            return 0.5  # Fallback
    
    def estimate_optimal_difficulty(
        self, 
        ability: float, 
        target_success_probability: float = 0.7, 
        discrimination: float = 1.0, 
        guessing: float = 0.25
    ) -> float:
        """
        Estima a dificuldade ideal para um usuário com determinado nível de habilidade
        
        Args:
            ability: Nível de habilidade do usuário
            target_success_probability: Probabilidade de sucesso desejada
            discrimination: Parâmetro de discriminação
            guessing: Parâmetro de adivinhação
            
        Returns:
            Dificuldade ideal para a questão
        """
        try:
            # Inverter a fórmula do modelo logístico para calcular b (dificuldade)
            # b = θ - (1/a) * ln((P-c)/(1-P))
            
            # Ajustar target para limites do modelo
            if target_success_probability <= guessing:
                # Impossível ter probabilidade menor que adivinhação
                target_success_probability = guessing + 0.01
            
            if target_success_probability >= 1.0:
                # Impossível ter certeza absoluta
                target_success_probability = 0.99
            
            # Calcular dificuldade
            numerator = target_success_probability - guessing
            denominator = 1 - target_success_probability
            log_term = math.log(numerator / (1 - guessing) / denominator)
            
            difficulty = ability - (1 / discrimination) * log_term
            
            # Limite nos valores de dificuldade
            return max(-3.0, min(3.0, difficulty))
            
        except Exception as e:
            logger.error(f"Erro ao estimar dificuldade: {e}")
            return ability  # Fallback: usar habilidade como dificuldade
    
    def map_difficulty_to_scale(self, difficulty_theta: float) -> int:
        """
        Mapeia dificuldade da escala theta (-3 a +3) para escala da aplicação (1-3)
        
        Args:
            difficulty_theta: Dificuldade na escala theta
            
        Returns:
            Dificuldade na escala da aplicação (1-3)
        """
        # Mapear de [-3, 3] para [1, 3]
        scaled = ((difficulty_theta + 3) / 6) * 2 + 1
        # Arredondar para nível inteiro
        return max(1, min(3, round(scaled)))
    
    def map_scale_to_difficulty(self, difficulty_level: int) -> float:
        """
        Mapeia dificuldade da escala da aplicação (1-3) para escala theta (-3 a +3)
        
        Args:
            difficulty_level: Dificuldade na escala da aplicação
            
        Returns:
            Dificuldade na escala theta
        """
        # Mapear de [1, 3] para [-3, 3]
        return ((difficulty_level - 1) / 2) * 6 - 3
    
    def calibrate_question(self, question: Dict[str, Any], user_stats: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Calibra a dificuldade de uma questão baseada no perfil do usuário
        
        Args:
            question: Questão a ser calibrada
            user_stats: Estatísticas do usuário (contendo proficiency e specialty_skills)
            
        Returns:
            Questão com dificuldade calibrada
        """
        # Se não há estatísticas do usuário, retornar questão original
        if not user_stats:
            return question
        
        try:
            # Extrair estatísticas relevantes
            specialty = question.get("specialty", "general_medicine")
            current_difficulty = question.get("difficulty", 2)
            
            # Converter dificuldade atual para escala theta
            current_difficulty_theta = self.map_scale_to_difficulty(current_difficulty)
            
            # Obter habilidade geral e específica do usuário
            general_proficiency = user_stats.get("proficiency", 0.5)
            specialty_skills = user_stats.get("specialty_skills", {})
            specialty_proficiency = specialty_skills.get(specialty, general_proficiency)
            
            # Combinar habilidades para esta especialidade (70% específica, 30% geral)
            effective_proficiency = 0.7 * specialty_proficiency + 0.3 * general_proficiency
            
            # Converter proficiência para escala theta (0-1 para -3 a +3)
            ability_theta = (effective_proficiency * 6) - 3
            
            # Calcular dificuldade ideal para este usuário
            optimal_difficulty_theta = self.estimate_optimal_difficulty(
                ability=ability_theta,
                target_success_probability=self.target_difficulty
            )
            
            # Determinar nova dificuldade (média ponderada entre atual e ideal)
            # Damos mais peso à dificuldade original para preservar intenção original
            weighted_difficulty_theta = 0.7 * current_difficulty_theta + 0.3 * optimal_difficulty_theta
            
            # Converter para escala da aplicação
            new_difficulty = self.map_difficulty_to_scale(weighted_difficulty_theta)
            
            # Atualizar questão
            question["difficulty"] = new_difficulty
            question["calibrated"] = True
            
            logger.debug(f"Questão calibrada: id={question.get('id')}, dificuldade={current_difficulty}->{new_difficulty}")
            
            return question
            
        except Exception as e:
            logger.error(f"Erro ao calibrar questão: {e}")
            return question  # Retornar questão original em caso de erro
    
    def calibrate_questions_batch(
        self, 
        questions: List[Dict[str, Any]], 
        user_stats: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Calibra um lote de questões para um usuário específico
        
        Args:
            questions: Lista de questões a serem calibradas
            user_stats: Estatísticas do usuário
            
        Returns:
            Lista de questões calibradas
        """
        return [self.calibrate_question(q, user_stats) for q in questions]
    
    def update_user_model(
        self, 
        user_stats: Dict[str, Any], 
        question_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Atualiza o modelo do usuário com base nos resultados de questões
        
        Args:
            user_stats: Estatísticas atuais do usuário
            question_results: Resultados de questões respondidas
            
        Returns:
            Estatísticas de usuário atualizadas
        """
        if not question_results:
            return user_stats
            
        # Inicializar valores padrão se não existirem
        if "proficiency" not in user_stats:
            user_stats["proficiency"] = 0.5
            
        if "specialty_skills" not in user_stats:
            user_stats["specialty_skills"] = {}
            
        if "question_history" not in user_stats:
            user_stats["question_history"] = []
            
        # Calcular novo modelo
        correct_count = sum(1 for q in question_results if q.get("correct", False))
        total_count = len(question_results)
        success_rate = correct_count / total_count if total_count > 0 else 0
        
        # Atualizar proficiência geral (com taxa de aprendizado pequena para suavizar mudanças)
        learning_rate = 0.1
        user_stats["proficiency"] = (1 - learning_rate) * user_stats["proficiency"] + learning_rate * success_rate
        
        # Atualizar habilidades específicas por especialidade
        specialty_counts = {}
        specialty_success = {}
        
        for result in question_results:
            specialty = result.get("specialty", "general_medicine")
            correct = result.get("correct", False)
            
            specialty_counts[specialty] = specialty_counts.get(specialty, 0) + 1
            specialty_success[specialty] = specialty_success.get(specialty, 0) + (1 if correct else 0)
        
        # Atualizar cada especialidade
        for specialty, count in specialty_counts.items():
            success_rate = specialty_success[specialty] / count
            
            if specialty not in user_stats["specialty_skills"]:
                user_stats["specialty_skills"][specialty] = success_rate
            else:
                user_stats["specialty_skills"][specialty] = (
                    (1 - learning_rate) * user_stats["specialty_skills"][specialty] + 
                    learning_rate * success_rate
                )
        
        # Adicionar ao histórico
        user_stats["question_history"].extend([{
            "id": result.get("id"),
            "specialty": result.get("specialty", "general_medicine"),
            "difficulty": result.get("difficulty", 2),
            "correct": result.get("correct", False),
            "timestamp": result.get("timestamp", "")
        } for result in question_results])
        
        # Limitar tamanho do histórico para evitar crescimento excessivo
        max_history = 100
        if len(user_stats["question_history"]) > max_history:
            user_stats["question_history"] = user_stats["question_history"][-max_history:]
            
        return user_stats 