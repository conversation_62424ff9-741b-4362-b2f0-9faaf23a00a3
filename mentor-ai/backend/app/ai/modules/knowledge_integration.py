"""
Integrações com fontes de conhecimento médico externas para enriquecer questões
"""

import os
import asyncio
import logging
import json
from typing import List, Dict, Any, Optional
import aiohttp

logger = logging.getLogger(__name__)

class KnowledgeGraphClient:
    """
    Cliente para consultar grafos de conhecimento médico e outras fontes externas
    
    Este cliente pode integrar com:
    - UMLS (Unified Medical Language System)
    - PubMed para artigos científicos
    - Outras fontes médicas estruturadas
    """
    
    def __init__(self, endpoint: Optional[str] = None, api_key: Optional[str] = None):
        """
        Inicializa o cliente de grafo de conhecimento
        
        Args:
            endpoint: URL do endpoint do grafo de conhecimento
            api_key: Chave de API para acessar o serviço
        """
        self.endpoint = endpoint or os.environ.get("KNOWLEDGE_GRAPH_ENDPOINT", "")
        self.api_key = api_key or os.environ.get("KNOWLEDGE_GRAPH_API_KEY", "")
        self.pubmed_endpoint = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils"
        self.umls_endpoint = os.environ.get("UMLS_ENDPOINT", "")
        self.umls_api_key = os.environ.get("UMLS_API_KEY", "")
        
        # Flag para indicar se inicialização foi bem-sucedida
        self.initialized = bool(self.endpoint) or bool(self.umls_endpoint)
        
        # Cache de conceitos para evitar requisições repetidas
        self.concept_cache = {}
        
        logger.info(f"Cliente de grafo de conhecimento inicializado: {self.initialized}")
    
    async def query_concepts(self, text: str) -> List[Dict[str, Any]]:
        """
        Consulta conceitos médicos relacionados ao texto
        
        Args:
            text: Texto para extrair conceitos
            
        Returns:
            Lista de conceitos médicos relevantes
        """
        # Verificar cache
        cache_key = text[:100]  # Usar primeiros 100 caracteres como chave
        if cache_key in self.concept_cache:
            logger.info(f"Usando conceitos em cache para: {cache_key}")
            return self.concept_cache[cache_key]
        
        # Se não está inicializado, retornar dados simulados
        if not self.initialized:
            logger.warning("Usando dados simulados para conceitos médicos")
            await asyncio.sleep(0.5)  # Simular latência de rede
            
            # Identificar algumas possíveis palavras-chave médicas no texto
            medical_terms = [
                ("hipertensão", "C0020538", 0.92),
                ("diabetes", "C0011849", 0.89),
                ("arritmia", "C0003811", 0.85),
                ("infecção", "C0021311", 0.83),
                ("pressão arterial", "C0005823", 0.82),
                ("cardiovascular", "C0007226", 0.80),
                ("pulmonar", "C0024109", 0.78),
                ("antibiótico", "C0003232", 0.75),
                ("analgésico", "C0002771", 0.74),
                ("neurológico", "C0027763", 0.72),
            ]
            
            concepts = []
            for term, cui, score in medical_terms:
                if term.lower() in text.lower():
                    concepts.append({
                        "concept": term.title(),
                        "cui": cui,
                        "relevance": score,
                        "semantic_types": ["Disease", "Procedure", "Medication"]
                    })
            
            # Se não encontrou nenhum termo específico, retornar alguns genéricos
            if not concepts:
                concepts = [
                    {"concept": "Hipertensão", "cui": "C0020538", "relevance": 0.92, "semantic_types": ["Disease"]},
                    {"concept": "Pressão arterial", "cui": "C0005823", "relevance": 0.87, "semantic_types": ["Measurement"]},
                    {"concept": "Anti-hipertensivos", "cui": "C0003289", "relevance": 0.76, "semantic_types": ["Medication"]},
                ]
            
            # Armazenar no cache
            self.concept_cache[cache_key] = concepts
            return concepts
        
        # Implementação real faria requisição para endpoint
        try:
            if self.endpoint:
                # Fazer requisição para grafo de conhecimento
                logger.info(f"Consultando grafo de conhecimento para: {text[:50]}...")
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.endpoint}/concepts",
                        json={"text": text},
                        headers={"Authorization": f"Bearer {self.api_key}"}
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            concepts = data.get("concepts", [])
                        else:
                            logger.error(f"Erro na requisição: {response.status}")
                            concepts = []
            
            elif self.umls_endpoint:
                # Alternativa: Usar API do UMLS
                logger.info(f"Consultando UMLS para: {text[:50]}...")
                
                # Implementação real consultaria o UMLS
                # Retornando dados simulados para exemplo
                concepts = [
                    {"concept": "Hipertensão", "cui": "C0020538", "relevance": 0.92, "semantic_types": ["Disease"]},
                    {"concept": "Pressão arterial", "cui": "C0005823", "relevance": 0.87, "semantic_types": ["Measurement"]},
                ]
            
            else:
                # Fallback para dados simulados
                concepts = [
                    {"concept": "Hipertensão", "cui": "C0020538", "relevance": 0.92, "semantic_types": ["Disease"]},
                    {"concept": "Pressão arterial", "cui": "C0005823", "relevance": 0.87, "semantic_types": ["Measurement"]},
                ]
                
            # Armazenar no cache
            self.concept_cache[cache_key] = concepts
            return concepts
            
        except Exception as e:
            logger.error(f"Erro ao consultar conceitos: {e}")
            # Fallback para dados simulados em caso de erro
            concepts = [
                {"concept": "Erro ao consultar", "cui": "E0000", "relevance": 0.5, "semantic_types": ["Error"]}
            ]
            return concepts
    
    async def fetch_pubmed_data(self, concept: str, limit: int = 3) -> List[Dict[str, Any]]:
        """
        Busca artigos relacionados a um conceito no PubMed
        
        Args:
            concept: Conceito médico a pesquisar
            limit: Número máximo de resultados
            
        Returns:
            Lista de artigos relevantes
        """
        logger.info(f"Buscando artigos sobre: {concept}")
        
        try:
            # Dados simulados para demonstração
            # Em produção, faria chamada real à API do PubMed
            await asyncio.sleep(0.5)  # Simular latência de rede
            
            articles = []
            journals = ["New England Journal of Medicine", "The Lancet", "JAMA", "BMJ", "Nature Medicine"]
            authors = ["Smith J, et al.", "Johnson MK, et al.", "Lee S, et al.", "Williams R, et al.", "Brown K, et al."]
            years = [2020, 2021, 2022, 2023]
            
            for i in range(min(limit, 3)):
                articles.append({
                    "pmid": f"356{i}9{i}01",
                    "title": f"Recent advances in {concept}: a comprehensive review",
                    "authors": authors[i % len(authors)],
                    "journal": journals[i % len(journals)],
                    "year": years[i % len(years)],
                    "abstract": f"This study reviews the current understanding of {concept}, highlighting recent developments in diagnosis and treatment. Several novel approaches are discussed.",
                    "url": f"https://pubmed.ncbi.nlm.nih.gov/356{i}9{i}01/"
                })
            
            return articles
            
        except Exception as e:
            logger.error(f"Erro ao buscar dados PubMed: {e}")
            return []
    
    async def generate_citations(self, concepts: List[Dict[str, Any]], limit: int = 3) -> List[Dict[str, Any]]:
        """
        Gera citações para conceitos médicos
        
        Args:
            concepts: Lista de conceitos médicos
            limit: Número máximo de citações
            
        Returns:
            Lista de citações relevantes
        """
        logger.info(f"Gerando citações para {len(concepts)} conceitos")
        
        citations = []
        for concept in concepts[:limit]:
            concept_name = concept.get("concept", "")
            if not concept_name:
                continue
                
            # Buscar artigos no PubMed
            articles = await self.fetch_pubmed_data(concept_name, 1)
            
            for article in articles:
                citations.append({
                    "text": f"{article['authors']} ({article['year']}). {article['title']}. {article['journal']}.",
                    "url": article['url'],
                    "concept": concept_name
                })
        
        return citations 