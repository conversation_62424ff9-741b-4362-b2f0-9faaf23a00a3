from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any
import logging
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# In-memory storage
documents = {}
sessions = {}

@router.get("/health")
async def health_check():
    """Check if Thanos service is healthy"""
    from datetime import datetime
    return {
        "status": "healthy",
        "version": "4.0.0",
        "services": {
            "database": "online",
            "embeddings": "ready",
            "llm": "ready"
        },
        "timestamp": datetime.now().isoformat()
    }

@router.post("/analyze")
async def analyze_document(
    file: UploadFile = File(...),
    prompt: Optional[str] = Form(None)
):
    """Analyze a document with AI"""
    try:
        # Log the request
        logger.info(f"Received file: {file.filename}")
        
        # Read file content
        content = await file.read()
        doc_id = str(uuid.uuid4())
        
        # Store document (in-memory for now)
        documents[doc_id] = {
            "filename": file.filename,
            "content": content,
            "size": len(content)
        }
        
        # Mock response
        return {
            "status": "success",
            "document_id": doc_id,
            "filename": file.filename,
            "analysis": {
                "summary": f"Análise do documento {file.filename}",
                "key_points": [
                    "Ponto chave 1: Documento processado com sucesso",
                    "Ponto chave 2: Conteúdo analisado usando IA",
                    "Ponto chave 3: Insights extraídos automaticamente"
                ],
                "content_type": file.content_type,
                "size": len(content),
                "word_count": len(content.decode('utf-8', errors='ignore').split()) if file.content_type == 'text/plain' else 0
            }
        }
    except Exception as e:
        logger.error(f"Error analyzing document: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents")
async def list_documents():
    """List all analyzed documents"""
    return {
        "documents": [
            {
                "id": doc_id,
                "filename": doc_data["filename"],
                "size": doc_data["size"]
            }
            for doc_id, doc_data in documents.items()
        ],
        "total": len(documents)
    }

@router.delete("/documents/{document_id}")
async def delete_document(document_id: str):
    """Delete a document"""
    if document_id in documents:
        del documents[document_id]
        return {
            "status": "success",
            "message": f"Document {document_id} deleted"
        }
    else:
        raise HTTPException(status_code=404, detail="Document not found")

@router.post("/chat")
async def chat_with_document(
    session_id: str = Form(...),
    message: str = Form(...),
    document_id: Optional[str] = Form(None)
):
    """Chat with AI about a document"""
    try:
        # Create or get session
        if session_id not in sessions:
            sessions[session_id] = {
                "messages": [],
                "document_id": document_id
            }
        
        # Add user message
        sessions[session_id]["messages"].append({
            "role": "user",
            "content": message
        })
        
        # Generate AI response (mock for now)
        if document_id and document_id in documents:
            response = f"Baseado no documento '{documents[document_id]['filename']}', {message}"
        else:
            response = f"Resposta para: {message}"
        
        # Add AI response
        sessions[session_id]["messages"].append({
            "role": "assistant",
            "content": response
        })
        
        return {
            "session_id": session_id,
            "response": response,
            "tokens_used": len(response.split())
        }
    except Exception as e:
        logger.error(f"Error in chat: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))