from fastapi import APIRouter, HTTPException, UploadFile, File, Form, BackgroundTasks, Depends
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import asyncio
import json
import time
from datetime import datetime

# Create router
router = APIRouter(
    prefix="/api/second_brain",
    tags=["second_brain"],
    responses={404: {"description": "Not found"}},
)

# Models
class Message(BaseModel):
    role: str
    content: str
    timestamp: Optional[str] = None

class ChatRequest(BaseModel):
    message: str

# In-memory store for messages (replace with database in production)
chat_history: List[Message] = []

@router.get("/health")
async def health():
    return {"status": "healthy", "service": "second-brain"}

@router.get("/status")
async def status():
    """Get Second Brain API status"""
    return {
        "status": "online",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@router.get("/messages")
async def get_messages():
    """Get all messages"""
    return {"data": chat_history}

@router.post("/chat")
async def chat(request: ChatRequest):
    """Process a chat message and return a response"""
    user_message = Message(
        role="user",
        content=request.message,
        timestamp=datetime.now().isoformat()
    )
    
    # Add user message to history
    chat_history.append(user_message)
    
    # Simulate AI processing time
    await asyncio.sleep(1)
    
    # Generate AI response
    ai_response = Message(
        role="assistant",
        content=f"Second Brain received: {request.message}. This is a simulated response.",
        timestamp=datetime.now().isoformat()
    )
    
    # Add AI response to history
    chat_history.append(ai_response)
    
    return ai_response

@router.post("/chat/stream")
async def chat_stream(message: str = Form(...)):
    """Stream a chat response"""
    # Add user message to history
    user_message = Message(
        role="user",
        content=message,
        timestamp=datetime.now().isoformat()
    )
    chat_history.append(user_message)
    
    # Define the stream generator function
    async def response_generator():
        # Simulate a streaming response with multiple chunks
        responses = [
            "Processando sua pergunta...",
            "Analisando conceitos médicos...",
            "Revisando literatura científica...",
            f"Resposta para sua pergunta: '{message}'\n\n",
            "Em medicina, é importante considerar múltiplos fatores ao analisar um caso. ",
            "Os principais pontos a destacar são:\n",
            "1. Evidência científica atualizada\n",
            "2. Contexto clínico específico\n",
            "3. Individualização do tratamento\n",
            "Espero ter respondido sua dúvida! Posso ajudar com mais alguma coisa?"
        ]
        
        for chunk in responses:
            await asyncio.sleep(0.5)  # Simulate processing time
            yield chunk
        
        # After streaming is complete, save the full response to history
        ai_response = Message(
            role="assistant",
            content="".join(responses),
            timestamp=datetime.now().isoformat()
        )
        chat_history.append(ai_response)
    
    return StreamingResponse(
        response_generator(),
        media_type="text/event-stream"
    )

@router.delete("/messages")
async def clear_history():
    """Clear all chat messages"""
    chat_history.clear()
    return {"status": "success", "message": "Chat history cleared"} 