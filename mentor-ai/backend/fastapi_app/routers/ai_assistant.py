from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
import os
import anthropic

router = APIRouter()

@router.get("/api/ai-assistant/test")
async def test_endpoint():
    """Test endpoint to verify connectivity"""
    return {"status": "ok", "message": "AI Assistant endpoint is working"}

class AIAssistantRequest(BaseModel):
    message: str
    context: Optional[str] = ""

class AIAssistantResponse(BaseModel):
    response: str

# Anthropic configuration
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "")
client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY) if ANTHROPIC_API_KEY else None

# System prompt for the AI assistant
SYSTEM_PROMPT = """Você é um assistente de estudos especializado e amigável, focado em ajudar estudantes brasileiros. 

Suas características:
- Responda sempre em português brasileiro
- <PERSON><PERSON> claro, didático e motivador
- Forneça exemplos práticos e aplicáveis
- Use formatação para melhor legibilidade
- Adapte suas respostas ao nível do estudante
- Sugira técnicas baseadas em evidências científicas

Áreas de especialização:
- Técnicas de estudo e memorização
- Organização e planejamento de estudos
- Gestão de tempo e produtividade
- Métodos como Pomodoro, Cornell, Feynman
- Estratégias para provas e concursos
- Controle de ansiedade e estresse
- Motivação e disciplina

Sempre que possível, estruture suas respostas com:
1. Resposta direta à pergunta
2. Explicação detalhada
3. Exemplos práticos
4. Dicas adicionais
5. Pergunta de follow-up para engajamento"""

@router.post("/api/ai-assistant", response_model=AIAssistantResponse)
async def get_ai_response(request: AIAssistantRequest):
    """
    Get AI assistant response for study-related questions
    """
    try:
        print(f"Received request: {request.message}")
        print(f"API Key configured: {bool(ANTHROPIC_API_KEY)}")
        print(f"Client configured: {bool(client)}")
        
        # Check if Anthropic client is configured
        if not client:
            print("No client configured, returning demo message")
            # Return a helpful message if API key is not configured
            return AIAssistantResponse(
                response="O assistente de IA está em modo de demonstração. Para respostas mais inteligentes, configure a chave da API no servidor."
            )
        
        # Prepare the message with context
        full_prompt = SYSTEM_PROMPT
        if request.context:
            full_prompt += f"\n\nContexto da conversa:\n{request.context}"
        
        # Make request to Anthropic
        print("Making request to Anthropic API...")
        message = client.messages.create(
            model="claude-3-sonnet-20240229",
            max_tokens=600,
            temperature=0.7,
            system=full_prompt,
            messages=[
                {
                    "role": "user",
                    "content": request.message
                }
            ]
        )
        
        print(f"Received response from Anthropic: {message}")
        
        # Extract the response text
        response_text = message.content[0].text
        print(f"Extracted text: {response_text[:100]}...")
        
        return AIAssistantResponse(
            response=response_text
        )
            
    except anthropic.RateLimitError as e:
        raise HTTPException(
            status_code=429,
            detail="Limite de requisições excedido. Por favor, aguarde um momento."
        )
    except anthropic.AuthenticationError as e:
        raise HTTPException(
            status_code=500,
            detail="Erro de autenticação com o serviço de IA."
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro inesperado: {str(e)}"
        )

@router.get("/api/ai-assistant/status")
async def get_ai_status():
    """
    Check if AI assistant is properly configured
    """
    return {
        "configured": bool(ANTHROPIC_API_KEY),
        "mode": "production" if ANTHROPIC_API_KEY else "demo",
        "model": "claude-3-sonnet-20240229" if ANTHROPIC_API_KEY else None
    }