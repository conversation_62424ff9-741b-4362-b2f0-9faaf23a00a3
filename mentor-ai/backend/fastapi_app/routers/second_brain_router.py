from fastapi import APIRouter, HTTPException, Body
from fastapi.responses import StreamingResponse
from ..services.second_brain_service import second_brain_service
from ..models.message import Message, ChatRequest, ChatHistory
from typing import Dict, Any, List
import json
from datetime import datetime

router = APIRouter(prefix="/api/second-brain", tags=["second-brain"])

# In-memory storage for messages (in production, use a database)
chat_messages: List[Message] = []

async def stream_generator(generator):
    try:
        async for chunk in generator:
            if chunk:
                yield f"data: {json.dumps({'content': chunk})}\n\n"
    except Exception as e:
        yield f"data: {json.dumps({'error': str(e)})}\n\n"
    finally:
        yield "data: [DONE]\n\n"

@router.post("/chat")
async def chat_endpoint(request: ChatRequest = Body(...)):
    try:
        user_message = request.message
        if not user_message:
            raise HTTPException(status_code=400, detail="Message is required")

        # Store user message
        user_msg = Message(
            role="user",
            content=user_message,
            timestamp=datetime.now().isoformat()
        )
        chat_messages.append(user_msg)

        # Create a placeholder for the assistant's response that will be filled by the stream
        assistant_msg = Message(
            role="assistant",
            content="",  # Will be filled during streaming
            timestamp=datetime.now().isoformat()
        )
        chat_messages.append(assistant_msg)

        async def update_assistant_message(generator):
            full_response = ""
            try:
                async for chunk in generator:
                    if chunk:
                        full_response += chunk
                        yield f"data: {json.dumps({'content': chunk})}\n\n"
            except Exception as e:
                yield f"data: {json.dumps({'error': str(e)})}\n\n"
            finally:
                # Update the stored assistant message with the complete response
                assistant_msg.content = full_response
                yield "data: [DONE]\n\n"

        return StreamingResponse(
            update_assistant_message(second_brain_service.get_chat_response(user_message)),
            media_type="text/event-stream"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/test")
async def test_endpoint():
    try:
        return StreamingResponse(
            stream_generator(second_brain_service.test_connection()),
            media_type="text/event-stream"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/messages")
async def get_messages():
    """Get all chat messages"""
    return ChatHistory(data=chat_messages)

@router.post("/clear")
async def clear_chat_history():
    """Clear all chat messages"""
    chat_messages.clear()
    return {"status": "success", "message": "Chat history cleared"}

@router.get("/health")
async def health_check():
    return {
        "status": "online",
        "service": "Second Brain API",
        "model": second_brain_service.model,
        "features": [
            "Real-time chat",
            "Streaming responses",
            "Revisões espaçadas inteligentes",
            "Organização de estudos personalizada",
            "Técnicas de memorização",
            "Cronogramas otimizados"
        ]
    } 