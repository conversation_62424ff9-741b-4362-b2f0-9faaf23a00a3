"""
Router para endpoints de Analytics e Performance
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from pydantic import BaseModel
import logging
import random
from uuid import uuid4

# Configurar logger
logger = logging.getLogger(__name__)

# Criar router
router = APIRouter(
    prefix="/api/analytics",
    tags=["analytics", "performance"],
    responses={404: {"description": "Not found"}},
)

# Modelos Pydantic
class PerformanceResponse(BaseModel):
    totalStudyTime: int
    totalCards: int
    masteredCards: int
    averageRetention: float
    studyConsistency: float
    currentStreak: int
    reviewHistory: List[Dict[str, Any]]
    recentSessions: List[Dict[str, Any]]
    speedHistory: List[int]

class RetentionRatesResponse(BaseModel):
    daily: List[Dict[str, float]]
    weekly: List[Dict[str, float]]
    monthly: List[Dict[str, float]]
    overall: float

class CategoryBreakdownResponse(BaseModel):
    categories: List[Dict[str, Any]]

class RankingResponse(BaseModel):
    position: int
    total: int
    percentile: float
    badges: List[str]

class NeuralWeightsRequest(BaseModel):
    weights: Dict[str, float]
    bias: float

# Mock data generator functions
def generate_mock_review_history(days: int = 90) -> List[Dict[str, Any]]:
    """Gera histórico de revisões mock"""
    history = []
    categories = ['Anatomia', 'Fisiologia', 'Farmacologia', 'Patologia', 'Bioquímica']
    
    for i in range(days * 20):  # ~20 cards por dia
        date = datetime.now() - timedelta(days=random.randint(0, days))
        history.append({
            'id': str(uuid4()),
            'cardId': f'card-{random.randint(1, 500)}',
            'date': date.isoformat(),
            'correct': random.random() > 0.25,  # 75% de acerto
            'responseTime': random.randint(5000, 30000),
            'difficulty': random.choice(['easy', 'medium', 'hard']),
            'category': random.choice(categories)
        })
    
    return sorted(history, key=lambda x: x['date'])

def generate_mock_sessions(days: int = 30) -> List[Dict[str, Any]]:
    """Gera sessões de estudo mock"""
    sessions = []
    
    for i in range(days):
        date = datetime.now() - timedelta(days=i)
        sessions.append({
            'id': str(uuid4()),
            'date': date.isoformat(),
            'duration': random.randint(30, 180),  # 30-180 minutos
            'cardsReviewed': random.randint(20, 100),
            'accuracy': random.uniform(0.6, 0.95)
        })
    
    return sessions

# Endpoints
@router.get("/performance/{user_id}", response_model=PerformanceResponse)
async def get_performance_analytics(user_id: str):
    """
    Retorna análise completa de performance do usuário
    """
    try:
        # Gerar dados mock
        review_history = generate_mock_review_history()
        recent_sessions = generate_mock_sessions()
        
        # Calcular métricas
        total_cards = len(set(r['cardId'] for r in review_history))
        correct_reviews = len([r for r in review_history if r['correct']])
        total_reviews = len(review_history)
        
        # Calcular tempo total de estudo
        total_study_time = sum(s['duration'] for s in recent_sessions)
        
        # Calcular consistência (% de dias estudados nos últimos 30 dias)
        study_days = len(set(s['date'].split('T')[0] for s in recent_sessions))
        study_consistency = study_days / 30
        
        # Calcular streak atual
        current_streak = calculate_streak(recent_sessions)
        
        # Gerar histórico de velocidade
        speed_history = [random.randint(30, 80) for _ in range(30)]
        
        return PerformanceResponse(
            totalStudyTime=total_study_time,
            totalCards=total_cards,
            masteredCards=int(total_cards * 0.65),  # 65% dominados
            averageRetention=correct_reviews / total_reviews if total_reviews > 0 else 0,
            studyConsistency=study_consistency,
            currentStreak=current_streak,
            reviewHistory=review_history[-100:],  # Últimas 100 revisões
            recentSessions=recent_sessions,
            speedHistory=speed_history
        )
    
    except Exception as e:
        logger.error(f"Erro ao buscar analytics de performance: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/retention/{user_id}", response_model=RetentionRatesResponse)
async def get_retention_rates(user_id: str):
    """
    Retorna taxas de retenção detalhadas
    """
    try:
        # Gerar dados mock de retenção
        daily_retention = []
        weekly_retention = []
        monthly_retention = []
        
        # Dados diários (últimos 30 dias)
        for i in range(30):
            date = datetime.now() - timedelta(days=i)
            daily_retention.append({
                'date': date.isoformat(),
                'retention': random.uniform(0.7, 0.95),
                'cardsReviewed': random.randint(20, 100)
            })
        
        # Dados semanais (últimas 12 semanas)
        for i in range(12):
            date = datetime.now() - timedelta(weeks=i)
            weekly_retention.append({
                'week': date.strftime('%Y-W%U'),
                'retention': random.uniform(0.75, 0.9),
                'cardsReviewed': random.randint(140, 700)
            })
        
        # Dados mensais (últimos 6 meses)
        for i in range(6):
            date = datetime.now() - timedelta(days=i*30)
            monthly_retention.append({
                'month': date.strftime('%Y-%m'),
                'retention': random.uniform(0.8, 0.9),
                'cardsReviewed': random.randint(600, 3000)
            })
        
        return RetentionRatesResponse(
            daily=daily_retention,
            weekly=weekly_retention,
            monthly=monthly_retention,
            overall=0.85  # 85% de retenção geral
        )
    
    except Exception as e:
        logger.error(f"Erro ao buscar taxas de retenção: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/categories/{user_id}", response_model=CategoryBreakdownResponse)
async def get_category_breakdown(user_id: str):
    """
    Retorna análise por categoria
    """
    try:
        categories = [
            {
                'id': 'anatomy',
                'name': 'Anatomia',
                'totalCards': 450,
                'masteredCards': 380,
                'accuracy': 0.84,
                'avgResponseTime': 12500,
                'lastReviewed': datetime.now().isoformat(),
                'trend': 'improving'
            },
            {
                'id': 'physiology',
                'name': 'Fisiologia',
                'totalCards': 380,
                'masteredCards': 290,
                'accuracy': 0.76,
                'avgResponseTime': 15000,
                'lastReviewed': (datetime.now() - timedelta(days=1)).isoformat(),
                'trend': 'stable'
            },
            {
                'id': 'pharmacology',
                'name': 'Farmacologia',
                'totalCards': 520,
                'masteredCards': 350,
                'accuracy': 0.67,
                'avgResponseTime': 18000,
                'lastReviewed': (datetime.now() - timedelta(days=2)).isoformat(),
                'trend': 'declining'
            },
            {
                'id': 'pathology',
                'name': 'Patologia',
                'totalCards': 300,
                'masteredCards': 180,
                'accuracy': 0.60,
                'avgResponseTime': 20000,
                'lastReviewed': datetime.now().isoformat(),
                'trend': 'improving'
            },
            {
                'id': 'biochemistry',
                'name': 'Bioquímica',
                'totalCards': 280,
                'masteredCards': 210,
                'accuracy': 0.75,
                'avgResponseTime': 14000,
                'lastReviewed': (datetime.now() - timedelta(hours=6)).isoformat(),
                'trend': 'stable'
            }
        ]
        
        return CategoryBreakdownResponse(categories=categories)
    
    except Exception as e:
        logger.error(f"Erro ao buscar breakdown de categorias: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/learning-curve/{user_id}")
async def get_learning_curve(user_id: str, days: int = Query(default=90, ge=7, le=365)):
    """
    Retorna curva de aprendizado
    """
    try:
        curve_data = []
        
        for i in range(days):
            date = datetime.now() - timedelta(days=days-i)
            # Simular curva de aprendizado crescente com variações
            base_value = (i / days) * 80  # Crescimento até 80%
            variation = random.uniform(-5, 5)
            
            curve_data.append({
                'date': date.isoformat(),
                'mastery': max(0, min(100, base_value + variation)),
                'cardsLearned': int(i * 5 + random.randint(0, 10)),
                'studyTime': random.randint(30, 180)
            })
        
        return {
            'data': curve_data,
            'trend': 'ascending',
            'projectedMastery': 92,
            'daysToGoal': 45
        }
    
    except Exception as e:
        logger.error(f"Erro ao buscar curva de aprendizado: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/ranking/{user_id}", response_model=RankingResponse)
async def get_user_ranking(user_id: str):
    """
    Retorna ranking do usuário
    """
    try:
        # Simular posição no ranking
        position = random.randint(50, 150)
        total = 1000
        percentile = ((total - position) / total) * 100
        
        badges = []
        if percentile > 90:
            badges.append('top-10-percent')
        if percentile > 80:
            badges.append('high-achiever')
        if position <= 100:
            badges.append('top-100')
        
        return RankingResponse(
            position=position,
            total=total,
            percentile=round(percentile, 1),
            badges=badges
        )
    
    except Exception as e:
        logger.error(f"Erro ao buscar ranking: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/difficulty/{user_id}")
async def get_difficulty_distribution(user_id: str):
    """
    Retorna distribuição de dificuldade dos cards
    """
    try:
        return {
            'distribution': {
                'easy': 35,
                'medium': 45,
                'hard': 20
            },
            'trends': {
                'easy': 'decreasing',
                'medium': 'stable',
                'hard': 'increasing'
            },
            'recommendations': [
                'Considere adicionar mais cards de nível difícil',
                'Revise cards médios com maior frequência'
            ]
        }
    
    except Exception as e:
        logger.error(f"Erro ao buscar distribuição de dificuldade: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/neural-weights/{user_id}")
async def update_neural_weights(user_id: str, weights: NeuralWeightsRequest):
    """
    Atualiza pesos da rede neural do usuário
    """
    try:
        # Aqui você salvaria os pesos no banco de dados
        logger.info(f"Atualizando pesos neurais para usuário {user_id}: {weights.dict()}")
        
        return {
            'status': 'success',
            'message': 'Pesos neurais atualizados com sucesso',
            'timestamp': datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"Erro ao atualizar pesos neurais: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Funções auxiliares
def calculate_streak(sessions: List[Dict[str, Any]]) -> int:
    """Calcula streak atual de dias estudados"""
    if not sessions:
        return 0
    
    # Ordenar por data
    sorted_sessions = sorted(sessions, key=lambda x: x['date'], reverse=True)
    
    streak = 0
    current_date = datetime.now().date()
    
    for session in sorted_sessions:
        session_date = datetime.fromisoformat(session['date'].replace('Z', '+00:00')).date()
        
        if session_date == current_date:
            streak += 1
            current_date -= timedelta(days=1)
        elif session_date == current_date - timedelta(days=1):
            current_date = session_date
            streak += 1
            current_date -= timedelta(days=1)
        else:
            break
    
    return streak