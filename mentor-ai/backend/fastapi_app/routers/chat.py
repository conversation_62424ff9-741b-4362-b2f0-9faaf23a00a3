from fastapi import APIRouter, Query, HTTPException
from fastapi.responses import StreamingResponse
from typing import List, Optional
import asyncio
import json
from datetime import datetime
import anthropic
import os
from dotenv import load_dotenv

load_dotenv()

# Create router for chat endpoints
router = APIRouter(
    prefix="/api/chat",
    tags=["chat"],
)

# In-memory chat history
chat_history = []

# Get Anthropic API key
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY) if ANTHROPIC_API_KEY and not ANTHROPIC_API_KEY.startswith("your_") else None

@router.get("/history")
async def get_history():
    """Get chat history"""
    return chat_history

@router.post("/clear")
async def clear_history():
    """Clear chat history"""
    chat_history.clear()
    return {"status": "success", "message": "Chat history cleared"}

@router.get("/stream")
async def stream_chat(message: str = Query(...)):
    """Stream a chat response using SSE"""
    
    # Add user message to history
    chat_history.append({
        "role": "user",
        "content": message,
        "timestamp": datetime.now().isoformat()
    })
    
    async def generate():
        try:
            if client and ANTHROPIC_API_KEY and not ANTHROPIC_API_KEY.startswith("sk-test"):
                # Use real Anthropic API
                yield f"data: Conectando ao Claude...\n\n"
                
                response = client.messages.create(
                    model="claude-3-haiku-20240307",
                    max_tokens=1000,
                    temperature=0.7,
                    system="Você é um assistente médico especializado chamado Second Brain. Responda em português de forma clara e educativa.",
                    messages=[
                        {"role": "user", "content": message}
                    ],
                    stream=True
                )
                
                full_response = ""
                for chunk in response:
                    if chunk.type == "content_block_delta":
                        text = chunk.delta.text
                        full_response += text
                        yield f"data: {text}\n\n"
                
                # Save to history
                chat_history.append({
                    "role": "assistant",
                    "content": full_response,
                    "timestamp": datetime.now().isoformat()
                })
                
            else:
                # Simulated response
                yield f"data: Processando sua pergunta...\n\n"
                await asyncio.sleep(0.5)
                
                responses = [
                    "Analisando informações médicas... ",
                    f"Sobre '{message}':\n\n",
                    "Com base no conhecimento médico atual, ",
                    "posso fornecer as seguintes informações:\n\n",
                    "• Evidências científicas relevantes\n",
                    "• Diretrizes clínicas atualizadas\n",
                    "• Considerações práticas importantes\n\n",
                    "Lembre-se sempre de consultar um profissional de saúde ",
                    "para decisões clínicas específicas."
                ]
                
                full_response = ""
                for chunk in responses:
                    full_response += chunk
                    yield f"data: {chunk}\n\n"
                    await asyncio.sleep(0.3)
                
                # Save to history
                chat_history.append({
                    "role": "assistant",
                    "content": full_response,
                    "timestamp": datetime.now().isoformat()
                })
            
            # Send completion signal
            yield f"event: end\ndata: \n\n"
            
        except Exception as e:
            yield f"data: Erro: {str(e)}\n\n"
            yield f"event: end\ndata: \n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )

@router.get("/test")
async def test_stream():
    """Test SSE stream endpoint"""
    async def generate():
        yield f"data: Test stream working at {datetime.now().isoformat()}\n\n"
        yield f"event: end\ndata: \n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/event-stream"
    )