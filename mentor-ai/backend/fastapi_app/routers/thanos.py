"""
ThanosAI Router - Document processing and AI conversation API
"""

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union
import os
import json
import uuid
import httpx
from datetime import datetime
import logging
import shutil
from pathlib import Path
import asyncio
from sqlalchemy.orm import Session

# Import database session
from fastapi_app.db.database import get_db
from fastapi_app.db.models import Document, DocumentChunk, ThanosSession, Message

# Import PDF extractor service
from fastapi_app.services.pdf_extractor import pdf_extractor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("thanos-api")

# Create router
router = APIRouter(tags=["ThanosAI"])

# Create uploads directory if it doesn't exist
UPLOAD_DIR = Path("./uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

# Document storage
DOCUMENTS_DIR = Path("./documents")
DOCUMENTS_DIR.mkdir(exist_ok=True)

# In-memory storage for development (will be replaced with database)
sessions = {}
documents = {}
conversations = {}

# AI Provider configurations
AI_PROVIDERS = {
    "OpenAI": {
        "api_url": "https://api.openai.com/v1/chat/completions",
        "headers": lambda api_key: {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        },
        "models": ["gpt-4o-mini", "gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"],
        "format_request": lambda message, context: {
            "model": context["model"],
            "messages": [
                {"role": "system", "content": context.get("system_prompt", "You are a helpful assistant.")},
                *context.get("history", []),
                {"role": "user", "content": message}
            ],
            "temperature": context.get("temperature", 0.7),
            "max_tokens": context.get("max_tokens", 2000)
        },
        "parse_response": lambda response: response["choices"][0]["message"]["content"]
    },
    "Groq": {
        "api_url": "https://api.groq.com/openai/v1/chat/completions",
        "headers": lambda api_key: {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        },
        "models": ["llama-3.1-70b-versatile", "gemma2-9b-it", "mixtral-8x7b-32768"],
        "format_request": lambda message, context: {
            "model": context["model"],
            "messages": [
                {"role": "system", "content": context.get("system_prompt", "You are a helpful assistant.")},
                *context.get("history", []),
                {"role": "user", "content": message}
            ],
            "temperature": context.get("temperature", 0.7),
            "max_tokens": context.get("max_tokens", 2000)
        },
        "parse_response": lambda response: response["choices"][0]["message"]["content"]
    },
    "Anthropic": {
        "api_url": "https://api.anthropic.com/v1/messages",
        "headers": lambda api_key: {
            "x-api-key": api_key,
            "anthropic-version": "2023-06-01",
            "Content-Type": "application/json"
        },
        "models": ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
        "format_request": lambda message, context: {
            "model": context["model"],
            "system": context.get("system_prompt", "You are a helpful assistant."),
            "messages": [
                *[{"role": msg["role"] if msg["role"] != "system" else "assistant", 
                   "content": msg["content"]} for msg in context.get("history", [])],
                {"role": "user", "content": message}
            ],
            "max_tokens": context.get("max_tokens", 2000)
        },
        "parse_response": lambda response: response["content"][0]["text"]
    }
}

# Document processing configurations
DOC_PROCESSORS = {
    "pdf": {
        "chunk_size": 1000,
        "chunk_overlap": 200,
        "extract_meta": True,
        "extract_images": False
    },
    "site": {
        "remove_ads": True,
        "include_images": False,
        "follow_links": False,
        "max_pages": 1
    },
    "youtube": {
        "include_metadata": True,
        "extract_chapters": True,
        "transcription_quality": "high"
    },
    "csv": {
        "delimiter": ",",
        "header_row": True,
        "max_rows": 1000
    },
    "txt": {
        "encoding": "utf-8",
        "preserve_line_breaks": True
    }
}

# Model schemas
class HealthResponse(BaseModel):
    status: str = "healthy"
    version: str = "4.0.0"
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    services: Dict[str, str] = Field(default_factory=lambda: {
        "database": "online",
        "embeddings": "ready",
        "llm": "ready"
    })

class AIConfig(BaseModel):
    provider: str
    model: str
    api_key: str
    temperature: float = 0.7
    max_tokens: int = 2000
    language: str = "pt"
    use_rag: bool = False

class DocumentConfig(BaseModel):
    document_type: str
    chunk_size: int = 1000
    chunk_overlap: int = 200
    options: Dict[str, Any] = {}

class InitializeRequest(BaseModel):
    provider: str
    model: str
    api_key: str
    document_id: Optional[str] = None
    document_url: Optional[str] = None
    document: Optional[str] = None
    document_type: str = "text"
    language: str = "pt"
    use_rag: bool = False
    temperature: float = 0.7
    max_tokens: int = 2000

class MessageRequest(BaseModel):
    message: str
    use_rag: bool = False
    language: str = "pt"

class DocumentUploadResponse(BaseModel):
    document_id: str
    document_type: str
    file_name: str
    file_size: int
    chunk_count: int
    success: bool = True

class DocumentURLResponse(BaseModel):
    document_id: str
    document_type: str
    url: str
    title: Optional[str] = None
    success: bool = True

class DocumentSummaryResponse(BaseModel):
    document_id: str
    summary: str
    title: Optional[str] = None

class ConversationResponse(BaseModel):
    session_id: str
    response: str
    tokens_used: int = 0
    processing_time: float = 0

# Helper functions
async def process_document(file_path: str, document_type: str, options: Dict[str, Any] = None):
    """Process a document based on its type and return chunks and metadata."""
    options = options or {}
    
    # In a real implementation, you would use specialized libraries for each document type
    # Here we'll just read the file and perform basic chunking
    
    try:
        text = ""
        metadata = {}
        chunks = []

        # Process document based on type
        if document_type == "pdf":
            # Use PDF extractor for PDF files
            logger.info(f"Processing PDF document: {file_path}")
            extraction_result = pdf_extractor.extract_text(
                file_path,
                extract_images=options.get("extractImages", False),
                preserve_layout=options.get("preserveLayout", True),
                extract_metadata=options.get("extractMetadata", True),
                quality=options.get("quality", "medium")
            )

            # Get text and metadata from extraction result
            text = extraction_result.get("text", "")
            metadata = {
                **extraction_result.get("metadata", {}),
                "page_count": extraction_result.get("page_count", 0)
            }

            # Use the PDF extractor's chunking function
            chunk_size = options.get("chunk_size", 1000)
            chunk_overlap = options.get("chunk_overlap", 200)

            pdf_chunks = pdf_extractor.chunk_text(text, chunk_size, chunk_overlap)
            chunks = [{"text": chunk["text"], "index": chunk["chunk_index"]} for chunk in pdf_chunks]

            logger.info(f"Processed PDF with {len(chunks)} chunks")

        elif document_type == "txt":
            with open(file_path, "r", encoding=options.get("encoding", "utf-8")) as f:
                text = f.read()

            # Simple chunking for text files
            chunk_size = options.get("chunk_size", 1000)
            chunk_overlap = options.get("chunk_overlap", 200)

            chunks = []
            for i in range(0, len(text), chunk_size - chunk_overlap):
                chunk = text[i:i + chunk_size]
                if len(chunk) > 50:  # Skip very small chunks
                    chunks.append({
                        "text": chunk,
                        "index": len(chunks)
                    })

        elif document_type == "csv":
            # In production, use pandas or csv module
            with open(file_path, "r", encoding="utf-8") as f:
                text = f.read()

            # Simple chunking for CSV data
            chunk_size = options.get("chunk_size", 1000)
            chunk_overlap = options.get("chunk_overlap", 200)

            chunks = []
            for i in range(0, len(text), chunk_size - chunk_overlap):
                chunk = text[i:i + chunk_size]
                if len(chunk) > 50:  # Skip very small chunks
                    chunks.append({
                        "text": chunk,
                        "index": len(chunks)
                    })
        else:
            text = f"[UNKNOWN DOCUMENT TYPE] Content could not be extracted from {file_path}"
            chunks = [{"text": text, "index": 0}]
        
        # Add general metadata (merge with any existing metadata)
        metadata = {
            **metadata,
            "file_path": file_path,
            "document_type": document_type,
            "chunk_count": len(chunks),
            "total_size": len(text),
            "processing_timestamp": datetime.now().isoformat()
        }

        return {"chunks": chunks, "metadata": metadata, "text": text}
    
    except Exception as e:
        logger.error(f"Error processing document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")

async def process_url(url: str, document_type: str, options: Dict[str, Any] = None):
    """Process content from a URL (website or YouTube)."""
    options = options or {}
    
    try:
        # In production, use requests, BeautifulSoup, youtube_dl, etc.
        if document_type == "site":
            # Simulate website content
            text = f"[WEBSITE CONTENT SIMULATION] This is simulated content from the website at {url}"
            title = f"Website: {url}"
        elif document_type == "youtube":
            # Simulate YouTube transcript
            text = f"[YOUTUBE TRANSCRIPT SIMULATION] This is simulated transcript from the YouTube video at {url}"
            title = f"YouTube Video: {url}"
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported URL type: {document_type}")
        
        # Simple chunking
        chunk_size = options.get("chunk_size", 1000)
        chunk_overlap = options.get("chunk_overlap", 200)
        
        chunks = []
        for i in range(0, len(text), chunk_size - chunk_overlap):
            chunk = text[i:i + chunk_size]
            if len(chunk) > 50:  # Skip very small chunks
                chunks.append({
                    "text": chunk,
                    "index": len(chunks)
                })
        
        # Generate simple metadata
        metadata = {
            "url": url,
            "document_type": document_type,
            "title": title,
            "chunk_count": len(chunks),
            "total_size": len(text),
            "processing_timestamp": datetime.now().isoformat()
        }
        
        return {"chunks": chunks, "metadata": metadata, "text": text, "title": title}
    
    except Exception as e:
        logger.error(f"Error processing URL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing URL: {str(e)}")

async def generate_summary(text: str, max_length: int = 200):
    """Generate a summary of the document text."""
    # In production, use an LLM API call to generate a summary
    # Here we'll just return the first few characters as a simple summary
    if not text:
        return "No content to summarize"
    
    # Simple summarization: first few characters
    simple_summary = text[:max_length] + ("..." if len(text) > max_length else "")
    return simple_summary

async def call_ai_provider(provider: str, api_key: str, model: str, message: str, context: Dict[str, Any]):
    """Call an AI provider API and return the response."""
    if provider not in AI_PROVIDERS:
        raise HTTPException(status_code=400, detail=f"Unsupported AI provider: {provider}")

    provider_config = AI_PROVIDERS[provider]

    if model not in provider_config["models"]:
        raise HTTPException(status_code=400, detail=f"Unsupported model for {provider}: {model}")

    # Check for development mode or test API key
    if api_key in ["dummy_api_key", "sk-test", "sk-test-key"] or api_key.startswith("sk-test"):
        logger.info(f"Using development mode for AI response with provider {provider} and model {model}")

        # Generate a mock response based on message content
        if "cardiologia" in message.lower():
            return "Cardiologia é a especialidade médica que estuda e trata das doenças relacionadas ao coração e sistema cardiovascular. Esta especialidade é fundamental para o diagnóstico, tratamento e prevenção de doenças como infarto do miocárdio, insuficiência cardíaca, arritmias e hipertensão arterial."
        elif "neurologia" in message.lower():
            return "Neurologia é a especialidade médica que estuda e trata as doenças que afetam o sistema nervoso, incluindo o cérebro, a medula espinhal e os nervos periféricos. Neurologistas diagnosticam e tratam condições como AVC, epilepsia, enxaqueca, Alzheimer, Parkinson e esclerose múltipla."
        elif "medicina" in message.lower():
            return "Medicina é a ciência e prática dedicada à prevenção, diagnóstico, tratamento e cura das doenças. É um campo amplo que inclui diversas especialidades como cardiologia, neurologia, pediatria, entre outras. A medicina combina conhecimentos científicos com habilidades clínicas para promover a saúde e o bem-estar das pessoas."
        elif "?" in message:
            return f"Esta é uma resposta simulada para sua pergunta sobre '{message}'. No modo de produção, esta consulta seria processada pelo modelo {model} do provedor {provider}."
        else:
            return f"Obrigado por seu comentário. Esta é uma resposta simulada no modo de desenvolvimento. Seu texto: '{message}' seria analisado pelo modelo {model} do provedor {provider} em produção."

    try:
        # Format the request
        request_data = provider_config["format_request"](message, {**context, "model": model})
        headers = provider_config["headers"](api_key)

        # Make the API call
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                provider_config["api_url"],
                json=request_data,
                headers=headers
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Error from {provider} API: {response.text}"
                )

            # Parse the response
            response_data = response.json()
            return provider_config["parse_response"](response_data)

    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Error calling {provider} API: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

# Import advanced RAG service
# from fastapi_app.services.rag_service import rag_service

# API Endpoints
@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Check if the API is running."""
    return HealthResponse()

@router.post("/documents/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    document_type: str = Form(...),
    config: str = Form("{}"),
    db: Session = Depends(get_db)
):
    """Upload and process a document file."""
    document_id = str(uuid.uuid4())
    
    try:
        # Parse configuration
        options = json.loads(config)
        
        # Save file
        file_path = UPLOAD_DIR / f"{document_id}_{file.filename}"
        
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Process the document
        document_data = await process_document(
            str(file_path),
            document_type,
            {**DOC_PROCESSORS.get(document_type, {}), **options}
        )

        # Generate embeddings for document chunks
        # chunks_with_embeddings = rag_service.embed_document_chunks(document_data["chunks"])
        chunks_with_embeddings = document_data["chunks"]  # Temporary: use chunks as-is

        # Store embeddings in cache for faster retrieval
        # rag_service.save_embeddings_to_cache(document_id, chunks_with_embeddings)

        # Create document record in database
        new_document = Document(
            id=document_id,
            title=file.filename,
            document_type=document_type,
            file_name=file.filename,
            file_path=str(file_path),
            text=document_data["text"],
            created_at=datetime.utcnow()
        )
        db.add(new_document)

        # Add chunks to database
        for i, chunk in enumerate(chunks_with_embeddings):
            new_chunk = DocumentChunk(
                document_id=document_id,
                text=chunk["text"],
                index=chunk["index"]
            )
            db.add(new_chunk)
        
        db.commit()
        
        return DocumentUploadResponse(
            document_id=document_id,
            document_type=document_type,
            file_name=file.filename,
            file_size=os.path.getsize(file_path),
            chunk_count=len(document_data["chunks"])
        )
    
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error uploading document: {str(e)}")

@router.post("/documents/url", response_model=DocumentURLResponse)
async def process_url_document(
    url: str = Query(...),
    document_type: str = Query(...),
    options: Dict[str, Any] = {},
    db: Session = Depends(get_db)
):
    """Process a document from a URL (website or YouTube)."""
    document_id = str(uuid.uuid4())
    
    try:
        # Process URL
        document_data = await process_url(
            url,
            document_type,
            {**DOC_PROCESSORS.get(document_type, {}), **options}
        )
        
        # Create document record in database
        new_document = Document(
            id=document_id,
            title=document_data.get("title", url),
            document_type=document_type,
            url=url,
            text=document_data["text"],
            created_at=datetime.utcnow()
        )
        db.add(new_document)
        
        # Add chunks to database
        for chunk in document_data["chunks"]:
            new_chunk = DocumentChunk(
                document_id=document_id,
                text=chunk["text"],
                index=chunk["index"]
            )
            db.add(new_chunk)
        
        db.commit()
        
        return DocumentURLResponse(
            document_id=document_id,
            document_type=document_type,
            url=url,
            title=document_data.get("title")
        )
    
    except Exception as e:
        logger.error(f"Error processing URL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing URL: {str(e)}")

@router.get("/documents/{document_id}/summary", response_model=DocumentSummaryResponse)
async def get_document_summary(document_id: str, db: Session = Depends(get_db)):
    """Get a summary of a document."""
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Generate summary if it doesn't exist
    if not document.summary:
        document.summary = await generate_summary(document.text)
        db.commit()
    
    return DocumentSummaryResponse(
        document_id=document_id,
        summary=document.summary,
        title=document.title or document.file_name or "Untitled Document"
    )

@router.post("/initialize")
async def initialize_thanos(request: InitializeRequest, db: Session = Depends(get_db)):
    """Initialize a ThanosAI session with configuration and document."""
    session_id = str(uuid.uuid4())
    
    try:
        # Create session in database
        session = ThanosSession(
            id=session_id,
            document_id=request.document_id,
            provider=request.provider,
            model=request.model,
            language=request.language,
            use_rag=request.use_rag,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            created_at=datetime.utcnow()
        )
        db.add(session)
        
        # Set system prompt based on document content if available
        system_prompt = "You are a friendly assistant named Thanos."
        if request.document_id:
            document = db.query(Document).filter(Document.id == request.document_id).first()
            if document:
                prefix = "Você é um assistente amigável chamado Thanos." if request.language == "pt" else "You are a friendly assistant named Thanos."
                instructions = (
                    "Utilize as informações fornecidas para basear suas respostas. Seja conciso e direto."
                    if request.language == "pt" else
                    "Use the information provided to base your answers. Be concise and direct."
                )
                system_prompt = f"{prefix}\n\nInformação do documento:\n\n{document.text[:4000]}...\n\n{instructions}"
        
        # Add system message
        system_message = Message(
            session_id=session_id,
            role="system",
            content=system_prompt
        )
        db.add(system_message)
        db.commit()
        
        return {
            "session_id": session_id,
            "status": "initialized",
            "system_prompt_length": len(system_prompt)
        }
    
    except Exception as e:
        logger.error(f"Error initializing ThanosAI: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error initializing ThanosAI: {str(e)}")

@router.post("/conversations/{session_id}/message", response_model=ConversationResponse)
async def send_message(session_id: str, request: MessageRequest, db: Session = Depends(get_db)):
    """Send a message to an AI provider and get a response with RAG enhancement."""
    try:
        # Get session from database
        session = db.query(ThanosSession).filter(ThanosSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Get message history
        messages = db.query(Message).filter(Message.session_id == session_id).order_by(Message.id).all()
        history = [{"role": msg.role, "content": msg.content} for msg in messages]

        # Process RAG if enabled
        context = {
            "history": history,
            "temperature": session.temperature,
            "max_tokens": session.max_tokens
        }

        # Store original user message
        user_message = Message(
            session_id=session_id,
            role="user",
            content=request.message
        )
        db.add(user_message)

        if session.use_rag and session.document_id and request.use_rag:
            logger.info(f"Using RAG for session {session_id} with document {session.document_id}")

            # Simplified RAG implementation
            chunks = db.query(DocumentChunk).filter(DocumentChunk.document_id == session.document_id).all()
            chunk_data = [{"text": chunk.text, "index": chunk.index} for chunk in chunks[:3]]  # Use first 3 chunks
            
            # Simple context enhancement
            context_text = "\n\n".join([f"Contexto {i+1}: {chunk['text'][:200]}..." for i, chunk in enumerate(chunk_data)])
            enhanced_message = f"Baseando-se no contexto abaixo, responda: {request.message}\n\nContexto:\n{context_text}"
            
            relevant_chunks = chunk_data  # For compatibility

            # Call AI provider with enhanced message
            start_time = datetime.now()
            response_text = await call_ai_provider(
                session.provider,
                "dummy_api_key",  # In production, use secure API key storage
                session.model,
                enhanced_message,
                context
            )
            processing_time = (datetime.now() - start_time).total_seconds()

            # Store metadata about the RAG process
            rag_metadata = {
                "chunks_used": len(relevant_chunks),
                "similarity_scores": [round(chunk["similarity"], 2) for chunk in relevant_chunks],
                "enhanced_prompt_length": len(enhanced_message)
            }
            logger.info(f"RAG metadata: {rag_metadata}")

        else:
            # No RAG - call AI provider with original message
            start_time = datetime.now()
            response_text = await call_ai_provider(
                session.provider,
                "dummy_api_key",  # In production, use secure API key storage
                session.model,
                request.message,
                context
            )
            processing_time = (datetime.now() - start_time).total_seconds()

        # Store assistant response
        assistant_message = Message(
            session_id=session_id,
            role="assistant",
            content=response_text,
            tokens=len(response_text.split())  # Rough estimation
        )
        db.add(assistant_message)
        db.commit()

        return ConversationResponse(
            session_id=session_id,
            response=response_text,
            tokens_used=len(response_text.split()),  # Rough estimation
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"Error sending message: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error sending message: {str(e)}")

@router.get("/analytics/{session_id}")
async def get_analytics(session_id: str, db: Session = Depends(get_db)):
    """Get analytics for a conversation session."""
    try:
        # Get session from database
        session = db.query(ThanosSession).filter(ThanosSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get message history
        messages = db.query(Message).filter(Message.session_id == session_id).order_by(Message.id).all()
        
        # Calculate basic analytics
        user_messages = [msg for msg in messages if msg.role == "user"]
        assistant_messages = [msg for msg in messages if msg.role == "assistant"]
        total_tokens = sum(msg.tokens or 0 for msg in messages)
        
        return {
            "session_id": session_id,
            "provider": session.provider,
            "model": session.model,
            "message_count": {
                "user": len(user_messages),
                "assistant": len(assistant_messages),
                "total": len(messages)
            },
            "tokens_used": total_tokens,
            "rag_enabled": session.use_rag,
            "language": session.language,
            "created_at": session.created_at.isoformat(),
            "document_id": session.document_id
        }
    
    except Exception as e:
        logger.error(f"Error getting analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting analytics: {str(e)}")