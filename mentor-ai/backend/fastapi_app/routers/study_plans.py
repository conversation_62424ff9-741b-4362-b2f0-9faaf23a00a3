from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date
from pydantic import BaseModel, Field
import uuid

# Import database dependencies
from fastapi_app.db.database import get_db

router = APIRouter(prefix="/api/study-plans", tags=["study-plans"])

# Pydantic models for request/response
class StudyPlanObjective(BaseModel):
    text: str

class StudyPlanNotifications(BaseModel):
    enabled: bool = True
    daily: bool = True
    deadline: bool = True
    progress: bool = False

class StudyPlanCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    category: Optional[str] = None
    difficulty: str = Field(default="medio")
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    daily_hours: float = Field(default=2.0, ge=0.5, le=12.0)
    total_hours: int = Field(default=40, ge=1)
    icon: str = Field(default="fas fa-book")
    color: str = Field(default="linear-gradient(135deg, #667eea, #764ba2)")
    objectives: List[StudyPlanObjective] = Field(default_factory=list)
    notifications: StudyPlanNotifications = Field(default_factory=StudyPlanNotifications)

class StudyPlanUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    category: Optional[str] = None
    difficulty: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    daily_hours: Optional[float] = Field(None, ge=0.5, le=12.0)
    total_hours: Optional[int] = Field(None, ge=1)
    icon: Optional[str] = None
    color: Optional[str] = None
    objectives: Optional[List[StudyPlanObjective]] = None
    notifications: Optional[StudyPlanNotifications] = None
    is_active: Optional[bool] = None
    progress: Optional[float] = Field(None, ge=0, le=100)

class StudyPlanResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    category: Optional[str]
    difficulty: str
    start_date: Optional[date]
    end_date: Optional[date]
    daily_hours: float
    total_hours: int
    icon: str
    color: str
    objectives: List[StudyPlanObjective]
    notifications: StudyPlanNotifications
    is_active: bool
    progress: float
    completed_tasks: int
    total_tasks: int
    hours_completed: float
    created_at: datetime
    updated_at: datetime

class StudySessionCreate(BaseModel):
    plan_id: str
    duration_minutes: int = Field(..., ge=1)
    notes: Optional[str] = None

class StudySessionResponse(BaseModel):
    id: str
    plan_id: str
    duration_minutes: int
    notes: Optional[str]
    created_at: datetime

# In-memory storage for demo purposes
# In production, this would be replaced with proper database models
study_plans_storage = {}
study_sessions_storage = {}

@router.get("/", response_model=List[StudyPlanResponse])
async def get_study_plans(
    active_only: bool = True,
    category: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get all study plans for the current user"""
    try:
        plans = []
        for plan_id, plan_data in study_plans_storage.items():
            if active_only and not plan_data.get("is_active", True):
                continue
            if category and plan_data.get("category") != category:
                continue
            
            # Calculate derived fields
            objectives = plan_data.get("objectives", [])
            total_tasks = len([obj for obj in objectives if obj.get("text", "").strip()])
            completed_tasks = plan_data.get("completed_tasks", 0)
            progress = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            
            plan_response = StudyPlanResponse(
                id=plan_id,
                name=plan_data["name"],
                description=plan_data.get("description"),
                category=plan_data.get("category"),
                difficulty=plan_data.get("difficulty", "medio"),
                start_date=plan_data.get("start_date"),
                end_date=plan_data.get("end_date"),
                daily_hours=plan_data.get("daily_hours", 2.0),
                total_hours=plan_data.get("total_hours", 40),
                icon=plan_data.get("icon", "fas fa-book"),
                color=plan_data.get("color", "linear-gradient(135deg, #667eea, #764ba2)"),
                objectives=objectives,
                notifications=plan_data.get("notifications", StudyPlanNotifications()),
                is_active=plan_data.get("is_active", True),
                progress=progress,
                completed_tasks=completed_tasks,
                total_tasks=total_tasks,
                hours_completed=plan_data.get("hours_completed", 0.0),
                created_at=plan_data.get("created_at", datetime.now()),
                updated_at=plan_data.get("updated_at", datetime.now())
            )
            plans.append(plan_response)
        
        return plans
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching study plans: {str(e)}")

@router.post("/", response_model=StudyPlanResponse)
async def create_study_plan(
    plan: StudyPlanCreate,
    db: Session = Depends(get_db)
):
    """Create a new study plan"""
    try:
        plan_id = str(uuid.uuid4())
        now = datetime.now()
        
        # Calculate total tasks from objectives
        objectives = [obj for obj in plan.objectives if obj.text.strip()]
        total_tasks = len(objectives)
        
        plan_data = {
            "name": plan.name,
            "description": plan.description,
            "category": plan.category,
            "difficulty": plan.difficulty,
            "start_date": plan.start_date,
            "end_date": plan.end_date,
            "daily_hours": plan.daily_hours,
            "total_hours": plan.total_hours,
            "icon": plan.icon,
            "color": plan.color,
            "objectives": [{"text": obj.text} for obj in objectives],
            "notifications": plan.notifications.dict(),
            "is_active": True,
            "progress": 0.0,
            "completed_tasks": 0,
            "hours_completed": 0.0,
            "created_at": now,
            "updated_at": now
        }
        
        study_plans_storage[plan_id] = plan_data
        
        return StudyPlanResponse(
            id=plan_id,
            total_tasks=total_tasks,
            **plan_data
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating study plan: {str(e)}")

@router.get("/{plan_id}", response_model=StudyPlanResponse)
async def get_study_plan(
    plan_id: str,
    db: Session = Depends(get_db)
):
    """Get a specific study plan by ID"""
    if plan_id not in study_plans_storage:
        raise HTTPException(status_code=404, detail="Study plan not found")
    
    plan_data = study_plans_storage[plan_id]
    objectives = plan_data.get("objectives", [])
    total_tasks = len([obj for obj in objectives if obj.get("text", "").strip()])
    
    return StudyPlanResponse(
        id=plan_id,
        total_tasks=total_tasks,
        **plan_data
    )

@router.put("/{plan_id}", response_model=StudyPlanResponse)
async def update_study_plan(
    plan_id: str,
    plan_update: StudyPlanUpdate,
    db: Session = Depends(get_db)
):
    """Update an existing study plan"""
    if plan_id not in study_plans_storage:
        raise HTTPException(status_code=404, detail="Study plan not found")
    
    plan_data = study_plans_storage[plan_id]
    
    # Update only provided fields
    update_data = plan_update.dict(exclude_unset=True)
    if "objectives" in update_data:
        update_data["objectives"] = [{"text": obj.text} for obj in plan_update.objectives if obj.text.strip()]
    
    plan_data.update(update_data)
    plan_data["updated_at"] = datetime.now()
    
    objectives = plan_data.get("objectives", [])
    total_tasks = len([obj for obj in objectives if obj.get("text", "").strip()])
    
    return StudyPlanResponse(
        id=plan_id,
        total_tasks=total_tasks,
        **plan_data
    )

@router.delete("/{plan_id}")
async def delete_study_plan(
    plan_id: str,
    db: Session = Depends(get_db)
):
    """Delete a study plan"""
    if plan_id not in study_plans_storage:
        raise HTTPException(status_code=404, detail="Study plan not found")
    
    del study_plans_storage[plan_id]
    return {"message": "Study plan deleted successfully"}

@router.post("/{plan_id}/sessions", response_model=StudySessionResponse)
async def create_study_session(
    plan_id: str,
    session: StudySessionCreate,
    db: Session = Depends(get_db)
):
    """Create a new study session for a plan"""
    if plan_id not in study_plans_storage:
        raise HTTPException(status_code=404, detail="Study plan not found")
    
    session_id = str(uuid.uuid4())
    session_data = {
        "plan_id": plan_id,
        "duration_minutes": session.duration_minutes,
        "notes": session.notes,
        "created_at": datetime.now()
    }
    
    study_sessions_storage[session_id] = session_data
    
    # Update plan hours completed
    plan_data = study_plans_storage[plan_id]
    hours_to_add = session.duration_minutes / 60
    plan_data["hours_completed"] = plan_data.get("hours_completed", 0) + hours_to_add
    plan_data["updated_at"] = datetime.now()
    
    return StudySessionResponse(
        id=session_id,
        **session_data
    )

@router.get("/{plan_id}/sessions", response_model=List[StudySessionResponse])
async def get_study_sessions(
    plan_id: str,
    db: Session = Depends(get_db)
):
    """Get all study sessions for a plan"""
    if plan_id not in study_plans_storage:
        raise HTTPException(status_code=404, detail="Study plan not found")
    
    sessions = []
    for session_id, session_data in study_sessions_storage.items():
        if session_data["plan_id"] == plan_id:
            sessions.append(StudySessionResponse(
                id=session_id,
                **session_data
            ))
    
    return sessions

@router.get("/stats/overview")
async def get_study_stats_overview(db: Session = Depends(get_db)):
    """Get overview statistics for all study plans"""
    total_plans = len(study_plans_storage)
    active_plans = len([p for p in study_plans_storage.values() if p.get("is_active", True)])
    total_hours = sum(p.get("hours_completed", 0) for p in study_plans_storage.values())
    total_sessions = len(study_sessions_storage)
    
    return {
        "total_plans": total_plans,
        "active_plans": active_plans,
        "total_hours_studied": round(total_hours, 1),
        "total_sessions": total_sessions,
        "average_hours_per_plan": round(total_hours / max(total_plans, 1), 1)
    }
