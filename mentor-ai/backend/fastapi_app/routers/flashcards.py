from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from sqlalchemy.orm import Session
import httpx
import os
from datetime import date

# Fix imports to use absolute paths
from fastapi_app.db.database import get_db
from fastapi_app.models.flashcards import (
    FlashcardCreate, FlashcardUpdate, DeckCreate, DeckUpdate, 
    FlashcardReview, FlashcardGeneration
)
from fastapi_app.controllers.flashcards import (
    get_decks, get_deck, create_deck, update_deck, delete_deck,
    get_flashcards, get_flashcard, create_flashcard, update_flashcard, delete_flashcard,
    get_cards_for_review, record_review
)

# Placeholder for AI integration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
AI_SERVICE_URL = os.getenv("AI_SERVICE_URL", "http://localhost:8000/ai")

router = APIRouter(
    prefix="/flashcards",
    tags=["flashcards"],
    responses={404: {"description": "Not found"}},
)

# Temporary user_id for development
# In production, this would come from auth middleware
def get_current_user_id():
    return "test_user_id"


# === DECK ROUTES ===

@router.get("/decks/", response_model=List[dict])
async def read_decks(
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Get all decks for the current user."""
    decks = get_decks(db, user_id, skip, limit)
    # Convert SQLAlchemy objects to dicts for serialization
    return [
        {
            "id": deck.id,
            "name": deck.name,
            "description": deck.description,
            "color": deck.color,
            "tags": deck.tags,
            "created_at": deck.created_at,
            "updated_at": deck.updated_at,
            "card_count": len(deck.flashcards)
        } 
        for deck in decks
    ]


@router.post("/decks/", response_model=dict, status_code=status.HTTP_201_CREATED)
async def create_new_deck(
    deck: DeckCreate, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Create a new flashcard deck."""
    new_deck = create_deck(db, deck, user_id)
    return {
        "id": new_deck.id,
        "name": new_deck.name,
        "description": new_deck.description,
        "color": new_deck.color,
        "tags": new_deck.tags,
        "created_at": new_deck.created_at,
        "updated_at": new_deck.updated_at,
        "card_count": len(new_deck.flashcards)
    }


@router.get("/decks/{deck_id}", response_model=dict)
async def read_deck(
    deck_id: str, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Get a specific deck by ID."""
    deck = get_deck(db, deck_id, user_id)
    return {
        "id": deck.id,
        "name": deck.name,
        "description": deck.description,
        "color": deck.color,
        "tags": deck.tags,
        "created_at": deck.created_at,
        "updated_at": deck.updated_at,
        "card_count": len(deck.flashcards)
    }


@router.put("/decks/{deck_id}", response_model=dict)
async def update_deck_info(
    deck_id: str, 
    deck_update: DeckUpdate, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Update a deck's information."""
    updated_deck = update_deck(db, deck_id, deck_update, user_id)
    return {
        "id": updated_deck.id,
        "name": updated_deck.name,
        "description": updated_deck.description,
        "color": updated_deck.color,
        "tags": updated_deck.tags,
        "created_at": updated_deck.created_at,
        "updated_at": updated_deck.updated_at,
        "card_count": len(updated_deck.flashcards)
    }


@router.delete("/decks/{deck_id}")
async def delete_deck_endpoint(
    deck_id: str, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Delete a deck and all its flashcards."""
    return delete_deck(db, deck_id, user_id)


# === FLASHCARD ROUTES ===

@router.get("/decks/{deck_id}/flashcards", response_model=List[dict])
async def read_flashcards(
    deck_id: str, 
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Get all flashcards in a deck."""
    cards = get_flashcards(db, deck_id, user_id, skip, limit)
    return [
        {
            "id": card.id,
            "question": card.question,
            "answer": card.answer,
            "type": card.type,
            "difficulty": card.difficulty,
            "tags": card.tags,
            "options": card.options,
            "correct_option": card.correct_option,
            "created_at": card.created_at,
            "updated_at": card.updated_at,
            "last_reviewed": card.last_reviewed,
            "next_review": card.next_review,
            "consecutive_correct": card.consecutive_correct,
            "memory_strength": card.memory_strength,
            "deck_id": card.deck_id
        }
        for card in cards
    ]


@router.post("/decks/{deck_id}/flashcards", response_model=dict, status_code=status.HTTP_201_CREATED)
async def create_new_flashcard(
    deck_id: str,
    flashcard: FlashcardCreate, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Create a new flashcard in a deck."""
    # Ensure deck_id from path and from request body match
    if flashcard.deck_id != deck_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Deck ID in path and request body must match"
        )
    
    new_card = create_flashcard(db, flashcard, user_id)
    return {
        "id": new_card.id,
        "question": new_card.question,
        "answer": new_card.answer,
        "type": new_card.type,
        "difficulty": new_card.difficulty,
        "tags": new_card.tags,
        "options": new_card.options,
        "correct_option": new_card.correct_option,
        "created_at": new_card.created_at,
        "updated_at": new_card.updated_at,
        "last_reviewed": new_card.last_reviewed,
        "next_review": new_card.next_review,
        "consecutive_correct": new_card.consecutive_correct,
        "memory_strength": new_card.memory_strength,
        "deck_id": new_card.deck_id
    }


@router.get("/flashcards/{card_id}", response_model=dict)
async def read_flashcard(
    card_id: str, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Get a specific flashcard by ID."""
    card = get_flashcard(db, card_id, user_id)
    return {
        "id": card.id,
        "question": card.question,
        "answer": card.answer,
        "type": card.type,
        "difficulty": card.difficulty,
        "tags": card.tags,
        "options": card.options,
        "correct_option": card.correct_option,
        "created_at": card.created_at,
        "updated_at": card.updated_at,
        "last_reviewed": card.last_reviewed,
        "next_review": card.next_review,
        "consecutive_correct": card.consecutive_correct,
        "memory_strength": card.memory_strength,
        "deck_id": card.deck_id
    }


@router.put("/flashcards/{card_id}", response_model=dict)
async def update_flashcard_info(
    card_id: str, 
    flashcard_update: FlashcardUpdate, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Update a flashcard's information."""
    updated_card = update_flashcard(db, card_id, flashcard_update, user_id)
    return {
        "id": updated_card.id,
        "question": updated_card.question,
        "answer": updated_card.answer,
        "type": updated_card.type,
        "difficulty": updated_card.difficulty,
        "tags": updated_card.tags,
        "options": updated_card.options,
        "correct_option": updated_card.correct_option,
        "created_at": updated_card.created_at,
        "updated_at": updated_card.updated_at,
        "last_reviewed": updated_card.last_reviewed,
        "next_review": updated_card.next_review,
        "consecutive_correct": updated_card.consecutive_correct,
        "memory_strength": updated_card.memory_strength,
        "deck_id": updated_card.deck_id
    }


@router.delete("/flashcards/{card_id}")
async def delete_flashcard_endpoint(
    card_id: str, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Delete a flashcard."""
    return delete_flashcard(db, card_id, user_id)


# === REVIEW ROUTES ===

@router.get("/review", response_model=List[dict])
async def get_flashcards_for_review(
    deck_id: Optional[str] = None,
    limit: int = 20,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Get flashcards that are due for review today."""
    cards = get_cards_for_review(db, user_id, deck_id, limit)
    return [
        {
            "id": card.id,
            "question": card.question,
            "answer": card.answer,
            "type": card.type,
            "difficulty": card.difficulty,
            "tags": card.tags,
            "options": card.options,
            "correct_option": card.correct_option,
            "created_at": card.created_at,
            "updated_at": card.updated_at,
            "last_reviewed": card.last_reviewed,
            "next_review": card.next_review,
            "consecutive_correct": card.consecutive_correct,
            "memory_strength": card.memory_strength,
            "deck_id": card.deck_id
        }
        for card in cards
    ]


@router.post("/flashcards/{card_id}/review", response_model=dict)
async def update_flashcard_review(
    card_id: str, 
    review: FlashcardReview, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Record a flashcard review and update spaced repetition parameters."""
    # Ensure card_id from path and from request body match
    if review.card_id != card_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Card ID in path and request body must match"
        )
    
    result = record_review(db, review, user_id)
    return {
        "card": {
            "id": result["card"].id,
            "question": result["card"].question,
            "answer": result["card"].answer,
            "difficulty": result["card"].difficulty,
            "next_review": result["card"].next_review,
            "consecutive_correct": result["card"].consecutive_correct,
            "memory_strength": result["card"].memory_strength,
        },
        "next_review_date": result["next_review_date"],
        "interval": result["interval"],
        "ease_factor": result["ease_factor"],
        "memory_strength": result["memory_strength"]
    }


# === AI GENERATION ROUTES ===

@router.post("/generate", response_model=List[dict])
async def generate_flashcards(
    params: FlashcardGeneration, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Generate flashcards using AI based on text input."""
    try:
        # If no OpenAI API key, return mock data
        if not OPENAI_API_KEY:
            # For development/testing, return mock cards
            import random
            import time
            
            # Simulate AI processing time
            time.sleep(2)
            
            # Create mock cards from text
            words = params.text.split()
            num_cards = min(params.num_cards, len(words) // 5)
            
            generated_cards = []
            for i in range(num_cards):
                start_idx = i * 5
                question_words = words[start_idx:start_idx + 3]
                answer_words = words[start_idx + 3:start_idx + 5]
                
                card = {
                    "question": " ".join(question_words) + "?",
                    "answer": " ".join(answer_words),
                    "type": "basic",
                    "difficulty": random.randint(2, 4),
                    "tags": [params.topic] if params.topic else [],
                }
                generated_cards.append(card)
            
            return generated_cards
        
        # In production, call the AI service
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{AI_SERVICE_URL}/generate_flashcards",
                json={
                    "text": params.text,
                    "num_cards": params.num_cards,
                    "topic": params.topic,
                    "difficulty_level": params.difficulty_level
                },
                headers={"Authorization": f"Bearer {OPENAI_API_KEY}"}
            )
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=response.status_code,
                    detail="Error generating flashcards with AI"
                )
                
            return response.json()
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating flashcards: {str(e)}"
        )