from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse
import logging
from typing import Dict, List, Optional

router = APIRouter(prefix="/api/basic", tags=["basic"])
logger = logging.getLogger("fastapi_app.basic_router")

@router.get("/")
async def get_basic_info():
    """Endpoint básico para teste"""
    logger.info("Endpoint básico acessado")
    return {"message": "Basic router is working", "status": "online"}

@router.get("/status")
async def get_status():
    """Retorna informações de status do sistema"""
    return {
        "system": "online",
        "api_version": "3.0.0",
        "endpoints_available": 2,
        "documentation": "/docs"
    }
