from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, Float, Text, JSON, Date
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import datetime
import uuid

Base = declarative_base()


class User(Base):
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    first_name = Column(String)
    last_name = Column(String)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # Relationships
    decks = relationship("Deck", back_populates="user")
    study_sessions = relationship("StudySession", back_populates="user")


class Deck(Base):
    __tablename__ = "decks"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    description = Column(Text)
    color = Column(String, default="#4F46E5")
    tags = Column(JSON, default=list)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    user_id = Column(String, ForeignKey("users.id"))
    
    # Relationships
    user = relationship("User", back_populates="decks")
    flashcards = relationship("Flashcard", back_populates="deck", cascade="all, delete-orphan")


class Flashcard(Base):
    __tablename__ = "flashcards"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=False)
    type = Column(String, default="basic")  # basic, cloze, multiChoice
    difficulty = Column(Integer, default=3)  # 1-5 scale
    tags = Column(JSON, default=list)
    options = Column(JSON, nullable=True)  # For multiple choice
    correct_option = Column(Integer, nullable=True)  # For multiple choice
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    deck_id = Column(String, ForeignKey("decks.id"))
    
    # Spaced repetition fields
    last_reviewed = Column(DateTime, nullable=True)
    next_review = Column(Date, nullable=True)
    consecutive_correct = Column(Integer, default=0)
    ease_factor = Column(Float, default=2.5)
    interval = Column(Integer, default=1)  # Days
    memory_strength = Column(Float, default=0.5)  # 0-1 scale
    
    # Relationships
    deck = relationship("Deck", back_populates="flashcards")
    reviews = relationship("Review", back_populates="flashcard", cascade="all, delete-orphan")


class Review(Base):
    __tablename__ = "reviews"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    is_correct = Column(Boolean, nullable=False)
    difficulty = Column(Integer, nullable=False)  # 1-5 scale
    response_time_ms = Column(Integer, nullable=True)
    reviewed_at = Column(DateTime, default=datetime.datetime.utcnow)
    flashcard_id = Column(String, ForeignKey("flashcards.id"))
    
    # Spaced repetition data at review time
    interval = Column(Integer)
    ease_factor = Column(Float)
    memory_strength = Column(Float)
    
    # Relationships
    flashcard = relationship("Flashcard", back_populates="reviews")


class StudySession(Base):
    __tablename__ = "study_sessions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    started_at = Column(DateTime, default=datetime.datetime.utcnow)
    ended_at = Column(DateTime, nullable=True)
    cards_studied = Column(Integer, default=0)
    correct_count = Column(Integer, default=0)
    user_id = Column(String, ForeignKey("users.id"))
    
    # Relationships
    user = relationship("User", back_populates="study_sessions")
    
    # Optional deck relationship if session is for a specific deck
    deck_id = Column(String, ForeignKey("decks.id"), nullable=True)
    deck = relationship("Deck")

# ThanosAI models
class Document(Base):
    __tablename__ = "thanos_documents"
    
    id = Column(String, primary_key=True)
    title = Column(String, nullable=True)
    document_type = Column(String, nullable=False)
    file_name = Column(String, nullable=True)
    file_path = Column(String, nullable=True)
    url = Column(String, nullable=True)
    text = Column(Text, nullable=True)
    summary = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    
    chunks = relationship("DocumentChunk", back_populates="document")
    sessions = relationship("ThanosSession", back_populates="document")

class DocumentChunk(Base):
    __tablename__ = "thanos_document_chunks"
    
    id = Column(Integer, primary_key=True)
    document_id = Column(String, ForeignKey("thanos_documents.id"))
    text = Column(Text, nullable=False)
    index = Column(Integer, nullable=False)
    
    document = relationship("Document", back_populates="chunks")

class ThanosSession(Base):
    __tablename__ = "thanos_sessions"
    
    id = Column(String, primary_key=True)
    document_id = Column(String, ForeignKey("thanos_documents.id"), nullable=True)
    provider = Column(String, nullable=False)
    model = Column(String, nullable=False)
    language = Column(String, nullable=False, default="pt")
    use_rag = Column(Boolean, default=False)
    temperature = Column(Float, default=0.7)
    max_tokens = Column(Integer, default=2000)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    
    document = relationship("Document", back_populates="sessions")
    messages = relationship("Message", back_populates="session")

class Message(Base):
    __tablename__ = "thanos_messages"
    
    id = Column(Integer, primary_key=True)
    session_id = Column(String, ForeignKey("thanos_sessions.id"))
    role = Column(String, nullable=False)  # user, assistant or system
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=datetime.datetime.utcnow)
    tokens = Column(Integer, nullable=True)
    
    session = relationship("ThanosSession", back_populates="messages")