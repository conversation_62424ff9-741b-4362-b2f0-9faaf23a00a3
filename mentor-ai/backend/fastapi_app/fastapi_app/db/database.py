from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from databases import Database
import os

# Database URL configuration
DATABASE_URL = f"postgresql://postgres:postgres@localhost:5432/mentor_ai_db"
ASYNC_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@localhost:5432/mentor_ai_db"

# Synchronous database setup
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Asynchronous database setup
async_engine = create_async_engine(ASYNC_DATABASE_URL)
async_session = sessionmaker(async_engine, class_=AsyncSession, expire_on_commit=False)

# Database instance for async operations
database = Database(ASYNC_DATABASE_URL)

Base = declarative_base()

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Async dependency
async def get_async_db():
    async with async_session() as session:
        yield session
