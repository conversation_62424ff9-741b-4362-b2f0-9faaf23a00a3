from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Form, File, UploadFile, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import text
from datetime import datetime
import uuid
import asyncio
import json
from typing import List, Dict, Any, Optional

# Import our ultra modules
from .db.database import get_db, database, engine, Base
from .models.base import Chat<PERSON>essage, Document, FlashcardDeck
from .schemas.chat import ChatMessageCreate, ChatMessageResponse, ChatResponse

# Create all tables
Base.metadata.create_all(bind=engine)

# Ultra FastAPI application
app = FastAPI(
    title="🧠 Mentor AI - ULTRA GIGANTIC Backend",
    description="The ultimate FastAPI backend with PostgreSQL integration for Mentor AI platform",
    version="3.0.0-ULTRA",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Ultra CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Startup and shutdown events
@app.on_event("startup")
async def startup():
    await database.connect()
    print("🚀 ULTRA FastAPI connected to PostgreSQL database!")

@app.on_event("shutdown")
async def shutdown():
    await database.disconnect()
    print("👋 ULTRA FastAPI disconnected from database")

# Ultra root endpoint
@app.get("/")
async def ultra_root():
    return {
        "message": "🧠 Mentor AI - ULTRA GIGANTIC FastAPI Backend",
        "status": "🚀 ULTRA OPERATIONAL",
        "version": "3.0.0-ULTRA",
        "database": "✅ PostgreSQL Connected",
        "docs": "/docs",
        "api": {
            "health": "/health",
            "database": "/database/status",
            "second_brain": "/api/second_brain/",
            "thanos": "/api/thanos/",
            "flashcards": "/api/flashcards/"
        }
    }

# Ultra health check with database verification
@app.get("/health")
async def ultra_health_check(db: Session = Depends(get_db)):
    try:
        # Test database connection
        db.execute(text("SELECT 1"))
        db_status = "✅ Connected"
    except Exception as e:
        db_status = f"❌ Error: {str(e)}"
    
    return {
        "status": "🚀 ULTRA HEALTHY",
        "timestamp": datetime.now().isoformat(),
        "service": "mentor-ai-ultra-fastapi",
        "version": "3.0.0-ULTRA",
        "database": db_status,
        "endpoints": {
            "total": len(app.routes),
            "documented": len([r for r in app.routes if hasattr(r, 'summary')])
        }
    }

# Ultra database status endpoint
@app.get("/database/status")
async def database_status(db: Session = Depends(get_db)):
    try:
        # Test various database operations
        result = db.execute(text("SELECT version()")).fetchone()
        
        # Count records in our tables
        messages_count = db.query(ChatMessage).count()
        documents_count = db.query(Document).count()
        decks_count = db.query(FlashcardDeck).count()
        
        return {
            "status": "✅ ULTRA DATABASE OPERATIONAL",
            "postgresql_version": result[0] if result else "Unknown",
            "database_name": "mentor_ai_db",
            "tables": {
                "chat_messages": messages_count,
                "documents": documents_count,
                "flashcard_decks": decks_count
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

# Ultra Second Brain API
@app.get("/api/second_brain/messages")
async def get_ultra_messages(db: Session = Depends(get_db)):
    messages = db.query(ChatMessage).order_by(ChatMessage.timestamp.desc()).limit(50).all()
    return {"messages": messages, "count": len(messages)}

@app.post("/api/second_brain/chat", response_model=ChatResponse)
async def ultra_chat(message: str = Form(...), db: Session = Depends(get_db)):
    try:
        session_id = uuid.uuid4()
        
        # Save user message
        user_msg = ChatMessage(
            session_id=session_id,
            role="user",
            content=message
        )
        db.add(user_msg)
        db.commit()
        db.refresh(user_msg)
        
        # Generate AI response (enhanced)
        ai_content = f"🧠 ULTRA Response to: '{message}'\n\n✅ FastAPI is working perfectly with PostgreSQL!\n🗄️ Message saved to database with ID: {user_msg.id}\n🚀 All systems operational!"
        
        # Save AI response
        ai_msg = ChatMessage(
            session_id=session_id,
            role="assistant",
            content=ai_content
        )
        db.add(ai_msg)
        db.commit()
        db.refresh(ai_msg)
        
        return ChatResponse(
            message=message,
            response=ai_content,
            session_id=session_id,
            timestamp=datetime.now()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat error: {str(e)}")

@app.delete("/api/second_brain/clear")
async def clear_ultra_messages(db: Session = Depends(get_db)):
    try:
        deleted_count = db.query(ChatMessage).delete()
        db.commit()
        return {"status": "success", "deleted_messages": deleted_count}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Clear error: {str(e)}")

# Ultra Thanos Document Management API
@app.get("/api/thanos/documents")
async def get_ultra_documents(db: Session = Depends(get_db)):
    documents = db.query(Document).order_by(Document.uploaded_at.desc()).all()
    return {"documents": documents, "count": len(documents)}

@app.post("/api/thanos/upload")
async def upload_ultra_document(file: UploadFile = File(...), db: Session = Depends(get_db)):
    try:
        content = await file.read()
        
        doc = Document(
            filename=file.filename,
            content=content.decode('utf-8', errors='ignore')[:10000],  # First 10k chars
            file_path=f"/uploads/{file.filename}",
            processed=True
        )
        db.add(doc)
        db.commit()
        db.refresh(doc)
        
        return {
            "id": doc.id,
            "filename": doc.filename,
            "message": f"📄 Document '{file.filename}' uploaded successfully to ULTRA system!",
            "size": len(content),
            "processed": doc.processed
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload error: {str(e)}")

# Ultra Flashcards API
@app.get("/api/flashcards/decks")
async def get_ultra_flashcard_decks(db: Session = Depends(get_db)):
    decks = db.query(FlashcardDeck).all()
    
    # Create default decks if none exist
    if not decks:
        default_decks = [
            FlashcardDeck(name="🏥 Medical Terminology", description="Essential medical terms", card_count=150),
            FlashcardDeck(name="🫀 Anatomy & Physiology", description="Human body systems", card_count=200),
            FlashcardDeck(name="💊 Pharmacology", description="Drug interactions and effects", card_count=100),
            FlashcardDeck(name="🧬 Pathology", description="Disease mechanisms", card_count=120),
        ]
        
        for deck in default_decks:
            db.add(deck)
        db.commit()
        
        decks = db.query(FlashcardDeck).all()
    
    return {"decks": decks, "count": len(decks)}

@app.post("/api/flashcards/decks")
async def create_ultra_deck(name: str = Form(...), description: str = Form(""), db: Session = Depends(get_db)):
    try:
        deck = FlashcardDeck(name=name, description=description)
        db.add(deck)
        db.commit()
        db.refresh(deck)
        
        return {
            "id": deck.id,
            "name": deck.name,
            "message": f"🃏 Flashcard deck '{name}' created in ULTRA system!"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Deck creation error: {str(e)}")

# Ultra streaming endpoint
@app.post("/api/stream")
async def ultra_stream(message: str = Form(...)):
    async def generate_ultra_stream():
        responses = [
            "🚀 ULTRA processing your request...",
            f"🧠 Analyzing: '{message}'",
            "🗄️ Connecting to PostgreSQL database...",
            "✅ Database connection successful!",
            "🔄 Processing with ULTRA algorithms...",
            "💡 Generating intelligent response...",
            "🎯 ULTRA response ready!",
            f"📊 Final answer: Your message '{message}' has been processed by the ULTRA Mentor AI system with full database integration!"
        ]
        
        for response in responses:
            yield f"data: {json.dumps({'message': response})}\n\n"
            await asyncio.sleep(0.5)
    
    return StreamingResponse(generate_ultra_stream(), media_type="text/plain")

# Ultra system information
@app.get("/api/system/info")
async def ultra_system_info(db: Session = Depends(get_db)):
    try:
        # Get database statistics
        messages_count = db.query(ChatMessage).count()
        documents_count = db.query(Document).count()
        decks_count = db.query(FlashcardDeck).count()
        
        return {
            "system": "🧠 Mentor AI ULTRA GIGANTIC System",
            "version": "3.0.0-ULTRA",
            "status": "🚀 FULLY OPERATIONAL",
            "components": {
                "fastapi": "✅ Running",
                "postgresql": "✅ Connected",
                "django": "✅ Integrated",
                "vue": "✅ Connected"
            },
            "database_stats": {
                "total_messages": messages_count,
                "total_documents": documents_count,
                "total_decks": decks_count
            },
            "features": [
                "🧠 AI Chat with database persistence",
                "📄 Document management system",
                "🃏 Flashcard management",
                "🔄 Real-time streaming",
                "🗄️ Full PostgreSQL integration",
                "🔌 RESTful API design"
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"System info error: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001, reload=True)
