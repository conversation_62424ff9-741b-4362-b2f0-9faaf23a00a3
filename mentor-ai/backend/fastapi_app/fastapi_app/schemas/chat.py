from pydantic import BaseModel
from datetime import datetime
from typing import Optional
import uuid

class ChatMessageCreate(BaseModel):
    content: str
    role: str = "user"
    session_id: Optional[uuid.UUID] = None

class ChatMessageResponse(BaseModel):
    id: int
    session_id: uuid.UUID
    role: str
    content: str
    timestamp: datetime
    
    class Config:
        from_attributes = True

class ChatResponse(BaseModel):
    message: str
    response: str
    session_id: uuid.UUID
    timestamp: datetime
