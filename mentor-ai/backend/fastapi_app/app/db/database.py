"""
Database Ultra Robust Configuration
MentorAI FastAPI - Titan Final Edition
"""

import logging
from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
import os

logger = logging.getLogger("mentor_ai_fastapi.database")

# URLs de banco ultra robustas
POSTGRESQL_URL = "postgresql://postgres:postgres@localhost:5432/mentor_ai_db"
SQLITE_FALLBACK_URL = "sqlite:///./mentor_ai_ultra.db"

def create_ultra_robust_engine():
    """Criar engine ultra robusta com fallback"""
    try:
        # Tentar PostgreSQL primeiro
        engine = create_engine(
            POSTGRESQL_URL,
            poolclass=QueuePool,
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=False
        )
        
        # Testar conexão
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        
        logger.info("✓ PostgreSQL engine ultra robusta criada")
        return engine
        
    except Exception as e:
        logger.warning(f"PostgreSQL falhou: {e}. Usando SQLite como fallback.")
        
        # Fallback para SQLite
        engine = create_engine(
            SQLITE_FALLBACK_URL,
            poolclass=QueuePool,
            pool_size=10,
            pool_pre_ping=True,
            connect_args={"check_same_thread": False}
        )
        
        logger.info("✓ SQLite fallback engine criada")
        return engine

# Criar engine ultra robusta
engine = create_ultra_robust_engine()

# Configuração de sessão ultra robusta
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False
)

# Base ultra robusta
Base = declarative_base()

def get_db():
    """Obter sessão ultra robusta do banco"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Erro na sessão do banco: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def init_ultra_robust_database():
    """Inicializar banco ultra robusto"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("✓ Banco de dados ultra robusto inicializado")
        return True
    except Exception as e:
        logger.error(f"Erro ao inicializar banco ultra robusto: {e}")
        return False

# Inicializar automaticamente
init_ultra_robust_database()
