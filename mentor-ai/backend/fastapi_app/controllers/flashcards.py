from fastapi import H<PERSON><PERSON><PERSON>xception
from sqlalchemy.orm import Session
from datetime import datetime, date, timedelta
import math
import uuid

from fastapi_app.db.models import Deck, Flashcard, Review
from fastapi_app.models.flashcards import FlashcardCreate, FlashcardUpdate, DeckCreate, DeckUpdate, FlashcardReview


def get_decks(db: Session, user_id: str, skip: int = 0, limit: int = 100):
    """
    Get all decks for a user.
    """
    return db.query(Deck).filter(Deck.user_id == user_id).offset(skip).limit(limit).all()


def get_deck(db: Session, deck_id: str, user_id: str):
    """
    Get a specific deck by ID, ensuring it belongs to the specified user.
    """
    deck = db.query(Deck).filter(Deck.id == deck_id, Deck.user_id == user_id).first()
    if not deck:
        raise HTTPException(status_code=404, detail="Deck not found")
    return deck


def create_deck(db: Session, deck: DeckCreate, user_id: str):
    """
    Create a new deck for a user.
    """
    new_deck = Deck(
        id=str(uuid.uuid4()),
        name=deck.name,
        description=deck.description,
        color=deck.color,
        tags=deck.tags,
        user_id=user_id,
    )
    db.add(new_deck)
    db.commit()
    db.refresh(new_deck)
    return new_deck


def update_deck(db: Session, deck_id: str, deck_update: DeckUpdate, user_id: str):
    """
    Update a deck's information.
    """
    db_deck = get_deck(db, deck_id, user_id)
    
    update_data = deck_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_deck, key, value)
    
    db_deck.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_deck)
    return db_deck


def delete_deck(db: Session, deck_id: str, user_id: str):
    """
    Delete a deck and all its flashcards.
    """
    db_deck = get_deck(db, deck_id, user_id)
    db.delete(db_deck)
    db.commit()
    return {"detail": "Deck deleted successfully"}


def get_flashcards(db: Session, deck_id: str, user_id: str, skip: int = 0, limit: int = 100):
    """
    Get all flashcards in a deck.
    """
    # Verify deck belongs to user
    db_deck = get_deck(db, deck_id, user_id)
    
    return db.query(Flashcard).filter(Flashcard.deck_id == deck_id).offset(skip).limit(limit).all()


def get_flashcard(db: Session, card_id: str, user_id: str):
    """
    Get a specific flashcard, ensuring it belongs to a deck owned by the user.
    """
    flashcard = db.query(Flashcard).join(Deck).filter(
        Flashcard.id == card_id,
        Deck.user_id == user_id
    ).first()
    
    if not flashcard:
        raise HTTPException(status_code=404, detail="Flashcard not found")
    return flashcard


def create_flashcard(db: Session, flashcard: FlashcardCreate, user_id: str):
    """
    Create a new flashcard in a deck.
    """
    # Verify deck belongs to user
    db_deck = get_deck(db, flashcard.deck_id, user_id)
    
    new_card = Flashcard(
        id=str(uuid.uuid4()),
        question=flashcard.question,
        answer=flashcard.answer,
        type=flashcard.type,
        difficulty=flashcard.difficulty,
        tags=flashcard.tags,
        options=flashcard.options,
        correct_option=flashcard.correct_option,
        deck_id=flashcard.deck_id,
        next_review=date.today()  # Set initial review date to today
    )
    db.add(new_card)
    db.commit()
    db.refresh(new_card)
    return new_card


def update_flashcard(db: Session, card_id: str, flashcard_update: FlashcardUpdate, user_id: str):
    """
    Update a flashcard's information.
    """
    db_card = get_flashcard(db, card_id, user_id)
    
    update_data = flashcard_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_card, key, value)
    
    db_card.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_card)
    return db_card


def delete_flashcard(db: Session, card_id: str, user_id: str):
    """
    Delete a flashcard.
    """
    db_card = get_flashcard(db, card_id, user_id)
    db.delete(db_card)
    db.commit()
    return {"detail": "Flashcard deleted successfully"}


def get_cards_for_review(db: Session, user_id: str, deck_id: str = None, limit: int = 20):
    """
    Get cards that are due for review today.
    Optionally filter by deck ID.
    """
    today = date.today()
    query = db.query(Flashcard).join(Deck).filter(
        Deck.user_id == user_id,
        Flashcard.next_review <= today
    )
    
    if deck_id:
        query = query.filter(Flashcard.deck_id == deck_id)
    
    return query.limit(limit).all()


def record_review(db: Session, review: FlashcardReview, user_id: str):
    """
    Record a flashcard review and update the spaced repetition parameters.
    Uses the SuperMemo-2 algorithm with some enhancements.
    """
    db_card = get_flashcard(db, review.card_id, user_id)
    
    # Create review record
    new_review = Review(
        id=str(uuid.uuid4()),
        is_correct=review.is_correct,
        difficulty=review.difficulty,
        response_time_ms=review.response_time_ms,
        flashcard_id=db_card.id,
        interval=db_card.interval,
        ease_factor=db_card.ease_factor,
        memory_strength=db_card.memory_strength
    )
    db.add(new_review)
    
    # Update card's spaced repetition parameters
    if review.is_correct:
        db_card.consecutive_correct += 1
    else:
        db_card.consecutive_correct = 0
    
    # Update ease factor based on performance (SM-2 algorithm)
    # Adjust ease factor based on difficulty rating (1-5)
    db_card.ease_factor = max(1.3, db_card.ease_factor + (0.1 - (5 - review.difficulty) * 0.08))
    
    # Calculate next interval
    if db_card.consecutive_correct == 0:
        # Failed card, review again tomorrow
        db_card.interval = 1
    elif db_card.consecutive_correct == 1:
        # First successful review
        db_card.interval = 1
    elif db_card.consecutive_correct == 2:
        # Second successful review
        db_card.interval = 3
    else:
        # Subsequent successful reviews
        db_card.interval = round(db_card.interval * db_card.ease_factor)
    
    # Cap maximum interval at 365 days
    db_card.interval = min(db_card.interval, 365)
    
    # Update memory strength based on review performance and time since last review
    if db_card.last_reviewed:
        days_since_review = (datetime.utcnow().date() - db_card.last_reviewed.date()).days
        
        # Memory decay formula (Ebbinghaus-inspired)
        decay_factor = math.exp(-0.05 * days_since_review)
        current_strength = db_card.memory_strength * decay_factor
        
        # Memory strengthening based on performance
        if review.is_correct:
            # Successful review boosts memory
            strengthening = (1 - current_strength) * 0.2 * (review.difficulty / 5.0)
            db_card.memory_strength = current_strength + strengthening
        else:
            # Failed review weakens memory
            db_card.memory_strength = max(0.1, current_strength * 0.8)
    else:
        # First review
        db_card.memory_strength = 0.3 if review.is_correct else 0.1
    
    # Update review dates
    db_card.last_reviewed = datetime.utcnow()
    db_card.next_review = date.today() + timedelta(days=db_card.interval)
    
    db.commit()
    db.refresh(db_card)
    
    # Return updated card with next review information
    return {
        "card": db_card,
        "next_review_date": db_card.next_review,
        "interval": db_card.interval,
        "ease_factor": db_card.ease_factor,
        "memory_strength": db_card.memory_strength
    }