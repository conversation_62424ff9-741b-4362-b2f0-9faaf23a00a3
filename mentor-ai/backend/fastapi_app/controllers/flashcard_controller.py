from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import List, Optional, Dict
from datetime import datetime, timedelta
import json

from ..models.flashcard_models import (
    Deck, Card, StudySession, Review, UserStats, DeckShare
)


class FlashcardController:
    def __init__(self, db: Session):
        self.db = db
    
    # Deck Management
    def get_user_decks(self, user_id: int, skip: int = 0, limit: int = 100) -> List[Deck]:
        """Get all decks for a user"""
        return self.db.query(Deck).filter(
            Deck.user_id == user_id
        ).offset(skip).limit(limit).all()
    
    def get_deck(self, deck_id: int) -> Optional[Deck]:
        """Get a specific deck"""
        return self.db.query(Deck).filter(Deck.id == deck_id).first()
    
    def create_deck(self, user_id: int, deck_data: Dict) -> Deck:
        """Create a new deck"""
        deck = Deck(
            user_id=user_id,
            **deck_data
        )
        self.db.add(deck)
        self.db.commit()
        self.db.refresh(deck)
        return deck
    
    def update_deck(self, deck_id: int, updates: Dict) -> Optional[Deck]:
        """Update a deck"""
        deck = self.get_deck(deck_id)
        if deck:
            for key, value in updates.items():
                if hasattr(deck, key):
                    setattr(deck, key, value)
            self.db.commit()
            self.db.refresh(deck)
        return deck
    
    def delete_deck(self, deck_id: int) -> bool:
        """Delete a deck"""
        deck = self.get_deck(deck_id)
        if deck:
            self.db.delete(deck)
            self.db.commit()
            return True
        return False
    
    # Card Management
    def get_deck_cards(self, deck_id: int, skip: int = 0, limit: int = 100) -> List[Card]:
        """Get all cards in a deck"""
        return self.db.query(Card).filter(
            Card.deck_id == deck_id
        ).offset(skip).limit(limit).all()
    
    def get_card(self, card_id: int) -> Optional[Card]:
        """Get a specific card"""
        return self.db.query(Card).filter(Card.id == card_id).first()
    
    def create_card(self, deck_id: int, card_data: Dict) -> Card:
        """Create a new card"""
        card = Card(
            deck_id=deck_id,
            **card_data
        )
        self.db.add(card)
        self.db.commit()
        self.db.refresh(card)
        return card
    
    def create_cards_bulk(self, deck_id: int, cards_data: List[Dict]) -> List[Card]:
        """Create multiple cards at once"""
        cards = []
        for card_data in cards_data:
            card = Card(
                deck_id=deck_id,
                **card_data
            )
            self.db.add(card)
            cards.append(card)
        
        self.db.commit()
        for card in cards:
            self.db.refresh(card)
        
        return cards
    
    def update_card(self, card_id: int, updates: Dict) -> Optional[Card]:
        """Update a card"""
        card = self.get_card(card_id)
        if card:
            for key, value in updates.items():
                if hasattr(card, key):
                    setattr(card, key, value)
            self.db.commit()
            self.db.refresh(card)
        return card
    
    def delete_card(self, card_id: int) -> bool:
        """Delete a card"""
        card = self.get_card(card_id)
        if card:
            self.db.delete(card)
            self.db.commit()
            return True
        return False
    
    def get_deck_card_count(self, deck_id: int) -> int:
        """Get the total number of cards in a deck"""
        return self.db.query(Card).filter(Card.deck_id == deck_id).count()
    
    # Study Session Management
    def start_study_session(self, user_id: int, deck_id: int) -> StudySession:
        """Start a new study session"""
        session = StudySession(
            user_id=user_id,
            deck_id=deck_id
        )
        self.db.add(session)
        self.db.commit()
        self.db.refresh(session)
        
        # Update user streak
        self._update_user_streak(user_id)
        
        return session
    
    def get_next_card(self, session_id: int) -> Optional[Card]:
        """Get the next card to review in the session"""
        session = self.db.query(StudySession).filter(
            StudySession.id == session_id
        ).first()
        
        if not session:
            return None
        
        # Get cards due for review
        now = datetime.utcnow()
        cards = self.db.query(Card).filter(
            and_(
                Card.deck_id == session.deck_id,
                Card.next_review_date <= now
            )
        ).order_by(Card.next_review_date).first()
        
        # If no due cards, get any card
        if not cards:
            cards = self.db.query(Card).filter(
                Card.deck_id == session.deck_id
            ).order_by(Card.last_review_date.asc().nullsfirst()).first()
        
        return cards
    
    def record_review(self, card_id: int, session_id: int, quality: int, 
                     response_time: float) -> Review:
        """Record a card review"""
        # Create review record
        review = Review(
            card_id=card_id,
            session_id=session_id,
            user_id=self.db.query(StudySession).filter(
                StudySession.id == session_id
            ).first().user_id,
            quality=quality,
            response_time=response_time
        )
        self.db.add(review)
        
        # Update card with spaced repetition algorithm
        card = self.get_card(card_id)
        if card:
            card.calculate_next_review(quality)
            card.total_reviews += 1
            if quality >= 3:
                card.correct_reviews += 1
            
            # Update average response time
            total_time = card.average_response_time * (card.total_reviews - 1) + response_time
            card.average_response_time = total_time / card.total_reviews
        
        # Update session stats
        session = self.db.query(StudySession).filter(
            StudySession.id == session_id
        ).first()
        if session:
            session.cards_studied += 1
            if quality >= 3:
                session.correct_answers += 1
            else:
                session.wrong_answers += 1
            
            # Update average response time
            total_time = session.average_response_time * (session.cards_studied - 1) + response_time
            session.average_response_time = total_time / session.cards_studied
        
        self.db.commit()
        self.db.refresh(review)
        return review
    
    def end_study_session(self, session_id: int) -> StudySession:
        """End a study session"""
        session = self.db.query(StudySession).filter(
            StudySession.id == session_id
        ).first()
        
        if session:
            session.ended_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(session)
        
        return session
    
    # Statistics and Analytics
    def get_user_stats(self, user_id: int) -> Dict:
        """Get user statistics"""
        stats = self.db.query(UserStats).filter(
            UserStats.user_id == user_id
        ).first()
        
        if not stats:
            # Create stats if not exists
            stats = UserStats(user_id=user_id)
            self.db.add(stats)
            self.db.commit()
            self.db.refresh(stats)
        
        # Get today's stats
        today = datetime.utcnow().date()
        today_sessions = self.db.query(StudySession).filter(
            and_(
                StudySession.user_id == user_id,
                StudySession.started_at >= today
            )
        ).all()
        
        cards_studied_today = sum(s.cards_studied for s in today_sessions)
        correct_today = sum(s.correct_answers for s in today_sessions)
        wrong_today = sum(s.wrong_answers for s in today_sessions)
        
        return {
            "cards_studied_today": cards_studied_today,
            "correct_today": correct_today,
            "wrong_today": wrong_today,
            "avg_response_time": sum(s.average_response_time for s in today_sessions) / len(today_sessions) if today_sessions else 0,
            "current_streak": stats.current_streak,
            "longest_streak": stats.longest_streak,
            "retention_rate": stats.retention_rate,
            "total_cards_studied": stats.total_cards_studied
        }
    
    def get_deck_stats(self, deck_id: int) -> Dict:
        """Get statistics for a specific deck"""
        deck = self.get_deck(deck_id)
        if not deck:
            return {}
        
        total_cards = self.db.query(Card).filter(Card.deck_id == deck_id).count()
        due_cards = self.db.query(Card).filter(
            and_(
                Card.deck_id == deck_id,
                Card.next_review_date <= datetime.utcnow()
            )
        ).count()
        
        # Calculate mastery level
        mastered_cards = self.db.query(Card).filter(
            and_(
                Card.deck_id == deck_id,
                Card.easiness_factor >= 2.5,
                Card.interval >= 21  # 3 weeks
            )
        ).count()
        
        return {
            "total_cards": total_cards,
            "due_cards": due_cards,
            "mastered_cards": mastered_cards,
            "mastery_percentage": (mastered_cards / total_cards * 100) if total_cards > 0 else 0,
            "average_ease": self.db.query(Card).filter(
                Card.deck_id == deck_id
            ).with_entities(func.avg(Card.easiness_factor)).scalar() or 2.5
        }
    
    def get_study_history(self, user_id: int, days: int = 30) -> List[Dict]:
        """Get study history for the last N days"""
        start_date = datetime.utcnow() - timedelta(days=days)
        
        sessions = self.db.query(StudySession).filter(
            and_(
                StudySession.user_id == user_id,
                StudySession.started_at >= start_date
            )
        ).all()
        
        # Group by day
        history = {}
        for session in sessions:
            date = session.started_at.date().isoformat()
            if date not in history:
                history[date] = {
                    "date": date,
                    "cards_studied": 0,
                    "correct_answers": 0,
                    "sessions": 0,
                    "average_accuracy": 0
                }
            
            history[date]["cards_studied"] += session.cards_studied
            history[date]["correct_answers"] += session.correct_answers
            history[date]["sessions"] += 1
        
        # Calculate accuracy
        for date_data in history.values():
            if date_data["cards_studied"] > 0:
                date_data["average_accuracy"] = (
                    date_data["correct_answers"] / date_data["cards_studied"] * 100
                )
        
        return list(history.values())
    
    # Search and Filter
    def search_cards(self, query: str, deck_id: Optional[int] = None, 
                    tags: Optional[List[str]] = None, user_id: int = None) -> List[Card]:
        """Search cards by text and filters"""
        filters = []
        
        if deck_id:
            filters.append(Card.deck_id == deck_id)
        
        if query:
            filters.append(
                or_(
                    Card.front.ilike(f"%{query}%"),
                    Card.back.ilike(f"%{query}%"),
                    Card.explanation.ilike(f"%{query}%")
                )
            )
        
        if tags:
            # JSON array contains check
            for tag in tags:
                filters.append(Card.tags.contains([tag]))
        
        if user_id:
            # Join with Deck to filter by user
            return self.db.query(Card).join(Deck).filter(
                and_(Deck.user_id == user_id, *filters)
            ).all()
        
        return self.db.query(Card).filter(and_(*filters)).all()
    
    # Due Cards and Scheduling
    def get_due_cards(self, deck_id: int) -> List[Card]:
        """Get cards due for review"""
        now = datetime.utcnow()
        return self.db.query(Card).filter(
            and_(
                Card.deck_id == deck_id,
                Card.next_review_date <= now
            )
        ).order_by(Card.next_review_date).all()
    
    def update_card_schedule(self, card_id: int, quality: int) -> Optional[Card]:
        """Update card review schedule"""
        card = self.get_card(card_id)
        if card:
            card.calculate_next_review(quality)
            self.db.commit()
            self.db.refresh(card)
        return card
    
    # Import/Export
    def export_deck(self, deck_id: int) -> Optional[Dict]:
        """Export deck to JSON format"""
        deck = self.get_deck(deck_id)
        if not deck:
            return None
        
        cards = self.get_deck_cards(deck_id, limit=10000)
        
        return {
            "deck": {
                "name": deck.name,
                "description": deck.description,
                "category": deck.category,
                "tags": deck.tags
            },
            "cards": [
                {
                    "front": card.front,
                    "back": card.back,
                    "type": card.type,
                    "explanation": card.explanation,
                    "tags": card.tags
                }
                for card in cards
            ]
        }
    
    def import_deck(self, user_id: int, deck_data: Dict) -> Deck:
        """Import deck from JSON format"""
        # Create deck
        deck = self.create_deck(user_id, deck_data["deck"])
        
        # Create cards
        if "cards" in deck_data:
            self.create_cards_bulk(deck.id, deck_data["cards"])
        
        return deck
    
    # Private helper methods
    def _update_user_streak(self, user_id: int):
        """Update user study streak"""
        stats = self.db.query(UserStats).filter(
            UserStats.user_id == user_id
        ).first()
        
        if not stats:
            stats = UserStats(user_id=user_id)
            self.db.add(stats)
        
        stats.update_streak()
        self.db.commit()