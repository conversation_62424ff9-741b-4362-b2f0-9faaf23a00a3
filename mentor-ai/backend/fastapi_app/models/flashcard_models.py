from sqlalchemy import Column, Integer, String, Text, DateTime, Float, <PERSON>olean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, timedelta
from ..db.database import Base

class Deck(Base):
    __tablename__ = "flashcard_decks"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    category = Column(String(100))
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    is_public = Column(Boolean, default=False)
    tags = Column(JSON, default=list)
    settings = Column(JSON, default=dict)
    
    # Relationships
    cards = relationship("Card", back_populates="deck", cascade="all, delete-orphan")
    study_sessions = relationship("StudySession", back_populates="deck")
    
    @property
    def card_count(self):
        return len(self.cards)
    
    @property
    def due_count(self):
        now = datetime.utcnow()
        return sum(1 for card in self.cards if card.next_review_date <= now)
    
    @property
    def progress(self):
        if not self.cards:
            return 0
        mastered = sum(1 for card in self.cards if card.easiness_factor >= 2.5)
        return int((mastered / len(self.cards)) * 100)


class Card(Base):
    __tablename__ = "flashcard_cards"
    
    id = Column(Integer, primary_key=True, index=True)
    deck_id = Column(Integer, ForeignKey("flashcard_decks.id"))
    front = Column(Text, nullable=False)
    back = Column(Text, nullable=False)
    type = Column(String(50), default="basic")  # basic, cloze, image, audio
    media_url = Column(String(500))
    explanation = Column(Text)
    tags = Column(JSON, default=list)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Spaced Repetition Fields
    repetitions = Column(Integer, default=0)
    easiness_factor = Column(Float, default=2.5)
    interval = Column(Integer, default=1)  # days
    next_review_date = Column(DateTime, default=func.now())
    last_review_date = Column(DateTime)
    
    # Statistics
    total_reviews = Column(Integer, default=0)
    correct_reviews = Column(Integer, default=0)
    average_response_time = Column(Float, default=0)
    
    # Relationships
    deck = relationship("Deck", back_populates="cards")
    reviews = relationship("Review", back_populates="card", cascade="all, delete-orphan")
    
    def calculate_next_review(self, quality: int):
        """
        Calculate next review date using SM-2 algorithm
        quality: 0-5 (0=complete blackout, 5=perfect response)
        """
        if quality < 3:
            self.repetitions = 0
            self.interval = 1
        else:
            if self.repetitions == 0:
                self.interval = 1
            elif self.repetitions == 1:
                self.interval = 6
            else:
                self.interval = int(self.interval * self.easiness_factor)
            
            self.repetitions += 1
        
        # Update easiness factor
        self.easiness_factor = max(1.3, self.easiness_factor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02)))
        
        # Set next review date
        self.next_review_date = datetime.utcnow() + timedelta(days=self.interval)
        self.last_review_date = datetime.utcnow()
        
        return self.next_review_date


class StudySession(Base):
    __tablename__ = "study_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    deck_id = Column(Integer, ForeignKey("flashcard_decks.id"))
    started_at = Column(DateTime, server_default=func.now())
    ended_at = Column(DateTime)
    cards_studied = Column(Integer, default=0)
    correct_answers = Column(Integer, default=0)
    wrong_answers = Column(Integer, default=0)
    average_response_time = Column(Float, default=0)
    
    # Relationships
    deck = relationship("Deck", back_populates="study_sessions")
    reviews = relationship("Review", back_populates="session")


class Review(Base):
    __tablename__ = "card_reviews"
    
    id = Column(Integer, primary_key=True, index=True)
    card_id = Column(Integer, ForeignKey("flashcard_cards.id"))
    session_id = Column(Integer, ForeignKey("study_sessions.id"))
    user_id = Column(Integer, index=True)
    quality = Column(Integer)  # 0-5 rating
    response_time = Column(Float)  # seconds
    reviewed_at = Column(DateTime, server_default=func.now())
    
    # Relationships
    card = relationship("Card", back_populates="reviews")
    session = relationship("StudySession", back_populates="reviews")


class DeckShare(Base):
    __tablename__ = "deck_shares"
    
    id = Column(Integer, primary_key=True, index=True)
    deck_id = Column(Integer, ForeignKey("flashcard_decks.id"))
    shared_by = Column(Integer)  # user_id
    shared_with = Column(Integer)  # user_id
    permission = Column(String(50), default="view")  # view, edit, clone
    shared_at = Column(DateTime, server_default=func.now())


class UserStats(Base):
    __tablename__ = "user_flashcard_stats"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, unique=True, index=True)
    total_cards_created = Column(Integer, default=0)
    total_cards_studied = Column(Integer, default=0)
    total_study_time = Column(Integer, default=0)  # minutes
    current_streak = Column(Integer, default=0)
    longest_streak = Column(Integer, default=0)
    last_study_date = Column(DateTime)
    retention_rate = Column(Float, default=0)
    study_calendar = Column(JSON, default=dict)  # {date: count}
    
    def update_streak(self):
        """Update study streak based on last study date"""
        if not self.last_study_date:
            self.current_streak = 1
        else:
            days_diff = (datetime.utcnow().date() - self.last_study_date.date()).days
            if days_diff == 0:
                pass  # Same day, no change
            elif days_diff == 1:
                self.current_streak += 1
            else:
                self.current_streak = 1
        
        self.longest_streak = max(self.longest_streak, self.current_streak)
        self.last_study_date = datetime.utcnow()