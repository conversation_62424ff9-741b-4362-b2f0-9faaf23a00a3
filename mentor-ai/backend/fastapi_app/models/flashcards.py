from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, date
import uuid


class FlashcardBase(BaseModel):
    """Base model for flashcard data with common fields."""
    question: str
    answer: str
    type: str = "basic"  # basic, cloze, multiChoice
    difficulty: int = Field(3, ge=1, le=5)  # 1-5 scale
    tags: List[str] = []
    options: Optional[List[str]] = None  # For multiple choice cards
    correct_option: Optional[int] = None  # For multiple choice cards


class FlashcardCreate(FlashcardBase):
    """Model for creating new flashcards."""
    deck_id: str


class FlashcardUpdate(FlashcardBase):
    """Model for updating existing flashcards."""
    question: Optional[str] = None
    answer: Optional[str] = None
    tags: Optional[List[str]] = None


class FlashcardReview(BaseModel):
    """Model for recording a flashcard review."""
    card_id: str
    difficulty: int = Field(3, ge=1, le=5)  # 1-5 scale (1=hard, 5=easy)
    is_correct: bool
    response_time_ms: Optional[int] = None  # Response time in milliseconds


class DeckBase(BaseModel):
    """Base model for deck data."""
    name: str
    description: Optional[str] = None
    tags: List[str] = []
    color: str = "#4F46E5"  # Default color


class DeckCreate(DeckBase):
    """Model for creating new decks."""
    pass


class DeckUpdate(BaseModel):
    """Model for updating decks."""
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    color: Optional[str] = None


class ReviewResponse(BaseModel):
    """Model for spaced repetition review response."""
    next_review_date: date
    interval: int  # Days until next review
    ease_factor: float = 2.5  # SM-2 algorithm ease factor
    memory_strength: float = 0.5  # Estimated strength of memory (0-1)
    

class FlashcardGeneration(BaseModel):
    """Model for AI flashcard generation request."""
    text: str  # Text to generate cards from
    num_cards: int = Field(10, ge=1, le=50)  # Number of cards to generate
    deck_id: Optional[str] = None  # Target deck
    topic: Optional[str] = None  # Topic for custom generation
    difficulty_level: str = "medium"  # easy, medium, hard