from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class Message(BaseModel):
    """Message model for chat conversations."""
    role: str = Field(..., description="Role of the message sender: 'user', 'assistant', or 'system'")
    content: str = Field(..., description="Content of the message")
    timestamp: Optional[str] = Field(default_factory=lambda: datetime.now().isoformat(), 
                                   description="ISO timestamp of when the message was created")

class ChatRequest(BaseModel):
    """Request model for chat endpoint."""
    message: str = Field(..., description="User message")

class ChatHistory(BaseModel):
    """Model for returning chat history."""
    data: List[Message] = Field(default_factory=list, description="List of messages in the conversation") 