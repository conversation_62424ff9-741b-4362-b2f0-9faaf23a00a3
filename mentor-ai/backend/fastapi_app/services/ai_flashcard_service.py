import os
import asyncio
from typing import List, Dict
import anthropic
from dotenv import load_dotenv

load_dotenv()


class AIFlashcardService:
    def __init__(self):
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if api_key:
            self.client = anthropic.Anthropic(api_key=api_key)
        else:
            self.client = None
    
    async def generate_cards(self, text: str, count: int = 10, 
                           difficulty: str = "medium") -> List[Dict]:
        """Generate flashcards from text using AI"""
        if not self.client:
            # Return mock data if no API key
            return self._generate_mock_cards(count)
        
        prompt = self._build_generation_prompt(text, count, difficulty)
        
        try:
            response = await asyncio.to_thread(
                self.client.messages.create,
                model="claude-3-haiku-20240307",
                max_tokens=2000,
                temperature=0.7,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            )
            
            # Parse the response
            cards = self._parse_cards_response(response.content[0].text)
            return cards
            
        except Exception as e:
            print(f"Error generating cards: {e}")
            return self._generate_mock_cards(count)
    
    async def improve_card(self, front: str, back: str) -> Dict:
        """Improve an existing card using AI"""
        if not self.client:
            return {"front": front, "back": back}
        
        prompt = f"""Melhore este flashcard médico tornando-o mais claro e efetivo:

Frente: {front}
Verso: {back}

Forneça uma versão melhorada mantendo o formato:
FRENTE: [pergunta melhorada]
VERSO: [resposta melhorada]
EXPLICAÇÃO: [explicação adicional se necessário]"""
        
        try:
            response = await asyncio.to_thread(
                self.client.messages.create,
                model="claude-3-haiku-20240307",
                max_tokens=500,
                temperature=0.3,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            )
            
            return self._parse_improvement_response(response.content[0].text)
            
        except Exception as e:
            print(f"Error improving card: {e}")
            return {"front": front, "back": back}
    
    async def generate_explanation(self, front: str, back: str) -> str:
        """Generate a detailed explanation for a card"""
        if not self.client:
            return "Explicação não disponível."
        
        prompt = f"""Para este flashcard médico, forneça uma explicação detalhada e didática:

Pergunta: {front}
Resposta: {back}

Crie uma explicação que:
1. Contextualize o conceito
2. Explique por que a resposta está correta
3. Forneça exemplos ou casos clínicos relevantes
4. Destaque pontos importantes para memorização"""
        
        try:
            response = await asyncio.to_thread(
                self.client.messages.create,
                model="claude-3-haiku-20240307",
                max_tokens=800,
                temperature=0.5,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            )
            
            return response.content[0].text.strip()
            
        except Exception as e:
            print(f"Error generating explanation: {e}")
            return "Erro ao gerar explicação."
    
    def _build_generation_prompt(self, text: str, count: int, difficulty: str) -> str:
        """Build the prompt for card generation"""
        difficulty_map = {
            "easy": "conceitos básicos e fundamentais",
            "medium": "conceitos intermediários com aplicação prática",
            "hard": "conceitos avançados e casos complexos"
        }
        
        return f"""Analise o seguinte texto médico e crie {count} flashcards de nível {difficulty_map.get(difficulty, 'médio')}.

TEXTO:
{text}

INSTRUÇÕES:
1. Crie flashcards no formato pergunta-resposta
2. Foque em {difficulty_map.get(difficulty)}
3. As perguntas devem ser diretas e específicas
4. As respostas devem ser concisas mas completas
5. Inclua termos médicos importantes
6. Para conceitos complexos, divida em múltiplos cards

FORMATO DE SAÍDA:
Para cada flashcard, use exatamente este formato:
CARD_START
FRENTE: [pergunta]
VERSO: [resposta]
TAGS: [tag1, tag2, tag3]
CARD_END

Gere exatamente {count} flashcards."""
    
    def _parse_cards_response(self, response: str) -> List[Dict]:
        """Parse AI response into card objects"""
        cards = []
        
        # Split by CARD_START/CARD_END markers
        card_blocks = response.split("CARD_START")
        
        for block in card_blocks[1:]:  # Skip first empty split
            if "CARD_END" in block:
                card_text = block.split("CARD_END")[0].strip()
                card = self._extract_card_data(card_text)
                if card:
                    cards.append(card)
        
        return cards
    
    def _extract_card_data(self, card_text: str) -> Dict:
        """Extract card data from text block"""
        lines = card_text.strip().split('\n')
        card = {
            "front": "",
            "back": "",
            "tags": []
        }
        
        for line in lines:
            if line.startswith("FRENTE:"):
                card["front"] = line.replace("FRENTE:", "").strip()
            elif line.startswith("VERSO:"):
                card["back"] = line.replace("VERSO:", "").strip()
            elif line.startswith("TAGS:"):
                tags_str = line.replace("TAGS:", "").strip()
                card["tags"] = [tag.strip() for tag in tags_str.split(",")]
        
        return card if card["front"] and card["back"] else None
    
    def _parse_improvement_response(self, response: str) -> Dict:
        """Parse improvement response"""
        improved = {
            "front": "",
            "back": "",
            "explanation": ""
        }
        
        lines = response.strip().split('\n')
        for line in lines:
            if line.startswith("FRENTE:"):
                improved["front"] = line.replace("FRENTE:", "").strip()
            elif line.startswith("VERSO:"):
                improved["back"] = line.replace("VERSO:", "").strip()
            elif line.startswith("EXPLICAÇÃO:"):
                improved["explanation"] = line.replace("EXPLICAÇÃO:", "").strip()
        
        return improved
    
    def _generate_mock_cards(self, count: int) -> List[Dict]:
        """Generate mock cards for testing"""
        mock_cards = [
            {
                "front": "Qual é a principal função do sistema cardiovascular?",
                "back": "Transportar oxigênio, nutrientes e hormônios para as células do corpo e remover produtos metabólicos.",
                "tags": ["cardiovascular", "fisiologia", "básico"]
            },
            {
                "front": "O que é hipertensão arterial?",
                "back": "Condição crônica em que a pressão arterial nas artérias está persistentemente elevada (≥140/90 mmHg).",
                "tags": ["cardiovascular", "patologia", "hipertensão"]
            },
            {
                "front": "Quais são os principais fatores de risco para infarto do miocárdio?",
                "back": "Hipertensão, diabetes, dislipidemia, tabagismo, obesidade, sedentarismo, história familiar e idade avançada.",
                "tags": ["cardiovascular", "infarto", "fatores de risco"]
            },
            {
                "front": "O que é o reflexo barorreceptor?",
                "back": "Mecanismo de controle da pressão arterial através de receptores no seio carotídeo e arco aórtico que detectam mudanças na pressão.",
                "tags": ["cardiovascular", "fisiologia", "reflexos"]
            },
            {
                "front": "Qual é a diferença entre sístole e diástole?",
                "back": "Sístole é a contração ventricular (ejeção de sangue), enquanto diástole é o relaxamento ventricular (enchimento).",
                "tags": ["cardiovascular", "ciclo cardíaco", "básico"]
            }
        ]
        
        return mock_cards[:count]