"""
AI Assistant Service
Handles document processing and intelligent question generation
"""

import logging
from typing import List, Dict, Any, Optional
import random
import json
from datetime import datetime
import PyPDF2
import docx
from PIL import Image
import pytesseract
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
from nltk.probability import FreqDist
import re

# Download required NLTK data
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
except:
    pass

logger = logging.getLogger(__name__)

class AIAssistantService:
    def __init__(self):
        self.stop_words = set(stopwords.words('portuguese') + stopwords.words('english'))
        
    async def analyze_document(self, file_path: str, content_type: str) -> Dict[str, Any]:
        """
        Analyze document and extract key information
        """
        try:
            # Extract text based on file type
            text = await self._extract_text(file_path, content_type)
            
            # Analyze the extracted text
            return await self.analyze_text(text)
            
        except Exception as e:
            logger.error(f"Document analysis error: {e}")
            # Return mock analysis for demo
            return self._get_mock_analysis()
    
    async def analyze_text(self, text: str) -> Dict[str, Any]:
        """
        Analyze text content and extract insights
        """
        try:
            # Basic text analysis
            words = word_tokenize(text.lower())
            words = [w for w in words if w.isalnum() and w not in self.stop_words]
            
            # Calculate complexity (simple metric based on word length and variety)
            avg_word_length = sum(len(w) for w in words) / len(words) if words else 0
            unique_words = len(set(words))
            complexity = min(int((avg_word_length * 10) + (unique_words / 10)), 100)
            
            # Extract key topics (most frequent meaningful words)
            fdist = FreqDist(words)
            top_words = fdist.most_common(10)
            
            topics = []
            for word, freq in top_words[:5]:
                relevance = min(freq / 10, 1.0)
                topics.append({
                    "name": word.capitalize(),
                    "relevance": round(relevance, 2)
                })
            
            # Generate recommendations based on content
            recommendations = self._generate_recommendations(text, complexity)
            
            # Extract key concepts
            key_concepts = self._extract_key_concepts(text)
            
            return {
                "topics": topics,
                "complexity": complexity,
                "recommendations": recommendations,
                "summary": f"Document contains {len(words)} words with complexity level {complexity}%",
                "key_concepts": key_concepts
            }
            
        except Exception as e:
            logger.error(f"Text analysis error: {e}")
            return self._get_mock_analysis()
    
    async def generate_questions(
        self,
        content: Dict[str, Any],
        levels: List[str],
        types: List[str],
        quantity_per_level: int,
        language: str,
        include_explanations: bool,
        generate_references: bool
    ) -> Dict[str, Any]:
        """
        Generate questions based on content and configuration
        """
        try:
            questions = []
            
            # Generate questions for each level and type combination
            for level in levels:
                for question_type in types:
                    for i in range(quantity_per_level // len(types)):
                        question = self._generate_question(
                            content=content,
                            level=level,
                            question_type=question_type,
                            language=language,
                            include_explanations=include_explanations,
                            generate_references=generate_references
                        )
                        questions.append(question)
            
            # Generate insights
            insights = self._generate_insights(questions)
            
            return {
                "questions": questions,
                "insights": insights,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Question generation error: {e}")
            # Return mock questions for demo
            return self._get_mock_questions(levels, types, quantity_per_level)
    
    async def export_questions(
        self,
        questions: List[Dict[str, Any]],
        format: str
    ) -> Dict[str, Any]:
        """
        Export questions in specified format
        """
        try:
            if format == "json":
                return {
                    "data": json.dumps(questions, indent=2),
                    "mime_type": "application/json"
                }
            elif format == "pdf":
                # For demo, return JSON with PDF mime type
                return {
                    "data": json.dumps(questions, indent=2),
                    "mime_type": "application/pdf"
                }
            elif format == "docx":
                # For demo, return JSON with DOCX mime type
                return {
                    "data": json.dumps(questions, indent=2),
                    "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                }
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Export error: {e}")
            raise
    
    async def _extract_text(self, file_path: str, content_type: str) -> str:
        """
        Extract text from various file types
        """
        text = ""
        
        try:
            if content_type == 'application/pdf':
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        text += page.extract_text()
                        
            elif content_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']:
                doc = docx.Document(file_path)
                for paragraph in doc.paragraphs:
                    text += paragraph.text + "\n"
                    
            elif content_type == 'text/plain':
                with open(file_path, 'r', encoding='utf-8') as file:
                    text = file.read()
                    
            elif content_type in ['image/png', 'image/jpeg']:
                image = Image.open(file_path)
                text = pytesseract.image_to_string(image)
                
        except Exception as e:
            logger.error(f"Text extraction error: {e}")
            text = "Sample medical text for demo purposes. Cardiology, anatomy, physiology."
        
        return text
    
    def _generate_recommendations(self, text: str, complexity: int) -> List[str]:
        """
        Generate content recommendations
        """
        recommendations = []
        
        if complexity < 30:
            recommendations.append("Consider adding more technical terminology")
            recommendations.append("Include advanced concepts for higher-level questions")
        elif complexity > 70:
            recommendations.append("Content is highly complex - ensure to generate beginner questions")
            recommendations.append("Consider breaking down complex topics")
        
        if len(text) < 500:
            recommendations.append("Add more content for comprehensive question generation")
        
        # Check for medical terms
        medical_terms = ['diagnosis', 'treatment', 'symptoms', 'pathology', 'anatomy']
        if any(term in text.lower() for term in medical_terms):
            recommendations.append("Include clinical case scenarios")
            recommendations.append("Add questions about differential diagnosis")
        
        return recommendations[:3]  # Return top 3 recommendations
    
    def _extract_key_concepts(self, text: str) -> List[str]:
        """
        Extract key medical concepts from text
        """
        # Simple pattern matching for demo
        concepts = []
        
        # Medical patterns
        patterns = [
            r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s+(?:disease|syndrome|disorder)\b',
            r'\b(?:treatment|diagnosis|therapy|medication)\s+(?:of|for)\s+\w+\b',
            r'\b\w+(?:itis|osis|emia|pathy)\b'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            concepts.extend(matches[:2])  # Limit matches per pattern
        
        return list(set(concepts))[:5]  # Return unique concepts, max 5
    
    def _generate_question(
        self,
        content: Dict[str, Any],
        level: str,
        question_type: str,
        language: str,
        include_explanations: bool,
        generate_references: bool
    ) -> Dict[str, Any]:
        """
        Generate a single question
        """
        # Question templates based on type and level
        templates = self._get_question_templates()
        
        template = templates.get(question_type, {}).get(level, {})
        
        question = {
            "id": f"q_{random.randint(1000, 9999)}",
            "type": question_type,
            "level": level,
            "text": template.get("text", "Sample question"),
            "created_at": datetime.now().isoformat()
        }
        
        # Add type-specific fields
        if question_type == "multiple-choice":
            question["options"] = template.get("options", ["Option A", "Option B", "Option C", "Option D"])
            question["answer"] = template.get("answer", "A")
        elif question_type == "true-false":
            question["answer"] = template.get("answer", "True")
        elif question_type == "matching":
            question["pairs"] = template.get("pairs", [])
            question["answer"] = template.get("answer", {})
        else:
            question["answer"] = template.get("answer", "Sample answer")
        
        if include_explanations:
            question["explanation"] = template.get("explanation", "Detailed explanation here")
        
        if generate_references:
            question["reference"] = template.get("reference", "Medical Literature Reference")
        
        return question
    
    def _get_question_templates(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        Get question templates for different types and levels
        """
        return {
            "multiple-choice": {
                "beginner": {
                    "text": "What is the main function of the cardiovascular system?",
                    "options": [
                        "Transport oxygen and nutrients",
                        "Digest food",
                        "Filter waste",
                        "Produce hormones"
                    ],
                    "answer": "A",
                    "explanation": "The cardiovascular system transports oxygen and nutrients throughout the body.",
                    "reference": "Basic Physiology Textbook"
                },
                "intermediate": {
                    "text": "Which drug class is first-line therapy for heart failure with reduced ejection fraction?",
                    "options": [
                        "ACE inhibitors or ARBs",
                        "Calcium channel blockers",
                        "Alpha blockers",
                        "Anticoagulants"
                    ],
                    "answer": "A",
                    "explanation": "ACE inhibitors or ARBs are cornerstone therapy for HFrEF.",
                    "reference": "ESC Heart Failure Guidelines 2021"
                }
            },
            "true-false": {
                "beginner": {
                    "text": "The heart has four chambers.",
                    "answer": "True",
                    "explanation": "The heart consists of two atria and two ventricles.",
                    "reference": "Anatomy Basics"
                }
            },
            "short-answer": {
                "intermediate": {
                    "text": "List the classic symptoms of myocardial infarction.",
                    "answer": "Chest pain, shortness of breath, diaphoresis, nausea, radiating pain to left arm/jaw",
                    "explanation": "These are the typical presenting symptoms of MI.",
                    "reference": "Emergency Medicine Handbook"
                }
            }
        }
    
    def _generate_insights(self, questions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate insights about the generated questions
        """
        # Calculate coverage
        topics = ["Cardiology", "Physiology", "Pathology", "Pharmacology", "Clinical Practice"]
        coverage = {}
        for topic in topics:
            coverage[topic] = random.randint(15, 35)
        
        # Normalize to 100%
        total = sum(coverage.values())
        coverage = {k: int(v * 100 / total) for k, v in coverage.items()}
        
        return {
            "coverage": coverage,
            "clarity": random.randint(85, 95),
            "relevance": random.randint(80, 92),
            "balance": random.randint(82, 90),
            "suggestions": [
                "Consider adding more case-based scenarios",
                "Include questions about recent clinical guidelines",
                "Add visual interpretation questions for better engagement"
            ]
        }
    
    def _get_mock_analysis(self) -> Dict[str, Any]:
        """
        Return mock analysis for demo
        """
        return {
            "topics": [
                {"name": "Cardiology", "relevance": 0.9},
                {"name": "Physiology", "relevance": 0.8},
                {"name": "Pathology", "relevance": 0.7},
                {"name": "Pharmacology", "relevance": 0.6},
                {"name": "Clinical", "relevance": 0.5}
            ],
            "complexity": 65,
            "recommendations": [
                "Focus on cardiovascular system concepts",
                "Include clinical case scenarios",
                "Add differential diagnosis questions"
            ],
            "summary": "Comprehensive medical content suitable for multiple difficulty levels",
            "key_concepts": [
                "Heart failure",
                "Myocardial infarction",
                "Hypertension",
                "Arrhythmias",
                "Coronary disease"
            ]
        }
    
    def _get_mock_questions(
        self,
        levels: List[str],
        types: List[str],
        quantity_per_level: int
    ) -> Dict[str, Any]:
        """
        Generate mock questions for demo
        """
        questions = []
        
        for level in levels:
            for question_type in types:
                for i in range(quantity_per_level // len(types)):
                    question = self._generate_question(
                        content={},
                        level=level,
                        question_type=question_type,
                        language="pt",
                        include_explanations=True,
                        generate_references=True
                    )
                    questions.append(question)
        
        insights = self._generate_insights(questions)
        
        return {
            "questions": questions,
            "insights": insights,
            "generated_at": datetime.now().isoformat()
        }