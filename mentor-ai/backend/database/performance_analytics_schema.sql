-- Script de criação de tabelas para o sistema de análise de desempenho
-- Banco de dados: PostgreSQL
-- Versão: 1.0.0

-- <PERSON><PERSON>r schema se não existir
CREATE SCHEMA IF NOT EXISTS analytics;

-- Definir schema padrão
SET search_path TO analytics, public;

-- Tabela de métricas de performance do usuário
CREATE TABLE IF NOT EXISTS user_performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    total_study_time INTEGER DEFAULT 0, -- em minutos
    total_cards INTEGER DEFAULT 0,
    mastered_cards INTEGER DEFAULT 0,
    average_accuracy DECIMAL(5,2) DEFAULT 0.00,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_study_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_user_metrics UNIQUE (user_id)
);

-- Índices para performance
CREATE INDEX idx_user_performance_user_id ON user_performance_metrics(user_id);
CREATE INDEX idx_user_performance_last_study ON user_performance_metrics(last_study_date);

-- Tabela de sessões de estudo
CREATE TABLE IF NOT EXISTS learning_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    session_date DATE NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- em minutos
    cards_reviewed INTEGER DEFAULT 0,
    cards_correct INTEGER DEFAULT 0,
    cards_incorrect INTEGER DEFAULT 0,
    average_response_time INTEGER, -- em milissegundos
    session_type VARCHAR(50), -- 'new', 'review', 'mixed'
    device_type VARCHAR(50),
    location VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_session_user FOREIGN KEY (user_id) 
        REFERENCES user_performance_metrics(user_id) ON DELETE CASCADE
);

-- Índices
CREATE INDEX idx_learning_sessions_user_id ON learning_sessions(user_id);
CREATE INDEX idx_learning_sessions_date ON learning_sessions(session_date);
CREATE INDEX idx_learning_sessions_user_date ON learning_sessions(user_id, session_date);

-- Tabela de análise por categoria
CREATE TABLE IF NOT EXISTS category_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    category_id VARCHAR(100) NOT NULL,
    category_name VARCHAR(255) NOT NULL,
    total_cards INTEGER DEFAULT 0,
    mastered_cards INTEGER DEFAULT 0,
    in_progress_cards INTEGER DEFAULT 0,
    new_cards INTEGER DEFAULT 0,
    accuracy_rate DECIMAL(5,2) DEFAULT 0.00,
    average_response_time INTEGER, -- em milissegundos
    last_reviewed TIMESTAMP WITH TIME ZONE,
    review_count INTEGER DEFAULT 0,
    difficulty_distribution JSONB, -- {"easy": 30, "medium": 50, "hard": 20}
    performance_trend VARCHAR(20), -- 'improving', 'stable', 'declining'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_user_category UNIQUE (user_id, category_id),
    CONSTRAINT fk_category_user FOREIGN KEY (user_id) 
        REFERENCES user_performance_metrics(user_id) ON DELETE CASCADE
);

-- Índices
CREATE INDEX idx_category_analytics_user_id ON category_analytics(user_id);
CREATE INDEX idx_category_analytics_category ON category_analytics(category_id);
CREATE INDEX idx_category_analytics_accuracy ON category_analytics(accuracy_rate);

-- Tabela de histórico de revisões detalhado
CREATE TABLE IF NOT EXISTS review_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    session_id UUID NOT NULL,
    card_id VARCHAR(255) NOT NULL,
    category_id VARCHAR(100),
    review_date TIMESTAMP WITH TIME ZONE NOT NULL,
    response_time INTEGER NOT NULL, -- em milissegundos
    correct BOOLEAN NOT NULL,
    difficulty VARCHAR(20), -- 'easy', 'medium', 'hard'
    confidence_level INTEGER CHECK (confidence_level >= 1 AND confidence_level <= 5),
    previous_interval INTEGER, -- dias desde última revisão
    next_interval INTEGER, -- dias até próxima revisão
    retention_score DECIMAL(3,2), -- 0.00 a 1.00
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_review_user FOREIGN KEY (user_id) 
        REFERENCES user_performance_metrics(user_id) ON DELETE CASCADE,
    CONSTRAINT fk_review_session FOREIGN KEY (session_id) 
        REFERENCES learning_sessions(id) ON DELETE CASCADE
);

-- Índices
CREATE INDEX idx_review_history_user_id ON review_history(user_id);
CREATE INDEX idx_review_history_card_id ON review_history(card_id);
CREATE INDEX idx_review_history_date ON review_history(review_date);
CREATE INDEX idx_review_history_session ON review_history(session_id);

-- Tabela de conquistas (achievements)
CREATE TABLE IF NOT EXISTS achievement_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    achievement_id VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    rarity VARCHAR(50), -- 'common', 'rare', 'epic', 'legendary'
    points INTEGER DEFAULT 0,
    requirement_type VARCHAR(50), -- 'streak', 'cards', 'accuracy', 'time', 'special'
    requirement_value JSONB, -- Critérios específicos
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de conquistas desbloqueadas pelo usuário
CREATE TABLE IF NOT EXISTS user_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    achievement_id UUID NOT NULL,
    unlocked_date TIMESTAMP WITH TIME ZONE NOT NULL,
    progress DECIMAL(5,2) DEFAULT 0.00, -- 0 a 100
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_user_achievement UNIQUE (user_id, achievement_id),
    CONSTRAINT fk_user_achievement_user FOREIGN KEY (user_id) 
        REFERENCES user_performance_metrics(user_id) ON DELETE CASCADE,
    CONSTRAINT fk_user_achievement_achievement FOREIGN KEY (achievement_id) 
        REFERENCES achievement_tracking(id) ON DELETE CASCADE
);

-- Índices
CREATE INDEX idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_unlocked ON user_achievements(unlocked_date);

-- Tabela de metas de estudo
CREATE TABLE IF NOT EXISTS goal_management (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    goal_type VARCHAR(50), -- 'cards', 'time', 'accuracy', 'streak', 'category'
    target_value INTEGER NOT NULL,
    current_value INTEGER DEFAULT 0,
    deadline DATE,
    priority VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high'
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'completed', 'failed', 'paused'
    category_id VARCHAR(100), -- Se a meta for específica de categoria
    icon VARCHAR(100),
    color VARCHAR(7), -- Hex color
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT fk_goal_user FOREIGN KEY (user_id) 
        REFERENCES user_performance_metrics(user_id) ON DELETE CASCADE
);

-- Índices
CREATE INDEX idx_goal_management_user_id ON goal_management(user_id);
CREATE INDEX idx_goal_management_status ON goal_management(status);
CREATE INDEX idx_goal_management_deadline ON goal_management(deadline);

-- Tabela de pesos da rede neural por usuário
CREATE TABLE IF NOT EXISTS neural_network_weights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    model_version VARCHAR(50) DEFAULT '1.0.0',
    weights JSONB NOT NULL, -- {"retention": 0.35, "difficulty": 0.25, ...}
    bias DECIMAL(5,4) DEFAULT 0.5000,
    learning_rate DECIMAL(5,4) DEFAULT 0.0100,
    last_training_date TIMESTAMP WITH TIME ZONE,
    performance_score DECIMAL(5,2), -- Pontuação de eficácia do modelo
    training_iterations INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_user_neural_weights UNIQUE (user_id, model_version),
    CONSTRAINT fk_neural_user FOREIGN KEY (user_id) 
        REFERENCES user_performance_metrics(user_id) ON DELETE CASCADE
);

-- Índices
CREATE INDEX idx_neural_weights_user_id ON neural_network_weights(user_id);

-- Tabela de rankings e comparações
CREATE TABLE IF NOT EXISTS user_rankings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    ranking_date DATE NOT NULL,
    global_rank INTEGER,
    total_users INTEGER,
    percentile DECIMAL(5,2),
    category_rankings JSONB, -- {"anatomy": 45, "physiology": 23, ...}
    badges JSONB, -- ["top-10-percent", "streak-master", ...]
    points INTEGER DEFAULT 0,
    level INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_user_ranking_date UNIQUE (user_id, ranking_date),
    CONSTRAINT fk_ranking_user FOREIGN KEY (user_id) 
        REFERENCES user_performance_metrics(user_id) ON DELETE CASCADE
);

-- Índices
CREATE INDEX idx_user_rankings_user_id ON user_rankings(user_id);
CREATE INDEX idx_user_rankings_date ON user_rankings(ranking_date);
CREATE INDEX idx_user_rankings_percentile ON user_rankings(percentile);

-- View materializada para estatísticas agregadas
CREATE MATERIALIZED VIEW IF NOT EXISTS user_statistics_summary AS
SELECT 
    u.user_id,
    u.total_study_time,
    u.total_cards,
    u.mastered_cards,
    u.average_accuracy,
    u.current_streak,
    COUNT(DISTINCT ls.id) as total_sessions,
    COUNT(DISTINCT ls.session_date) as study_days,
    AVG(ls.duration) as avg_session_duration,
    COUNT(DISTINCT ua.achievement_id) as achievements_unlocked,
    COUNT(DISTINCT gm.id) FILTER (WHERE gm.status = 'completed') as goals_completed,
    MAX(ur.percentile) as best_percentile,
    MAX(ur.global_rank) as best_rank
FROM user_performance_metrics u
LEFT JOIN learning_sessions ls ON u.user_id = ls.user_id
LEFT JOIN user_achievements ua ON u.user_id = ua.user_id
LEFT JOIN goal_management gm ON u.user_id = gm.user_id
LEFT JOIN user_rankings ur ON u.user_id = ur.user_id
GROUP BY u.user_id, u.total_study_time, u.total_cards, u.mastered_cards, 
         u.average_accuracy, u.current_streak;

-- Índice para a view materializada
CREATE INDEX idx_user_stats_summary_user_id ON user_statistics_summary(user_id);

-- Função para atualizar timestamp de updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para atualizar updated_at
CREATE TRIGGER update_user_performance_metrics_updated_at 
    BEFORE UPDATE ON user_performance_metrics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_category_analytics_updated_at 
    BEFORE UPDATE ON category_analytics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_neural_network_weights_updated_at 
    BEFORE UPDATE ON neural_network_weights 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para calcular e atualizar streak
CREATE OR REPLACE FUNCTION update_user_streak(p_user_id VARCHAR(255))
RETURNS VOID AS $$
DECLARE
    v_current_streak INTEGER := 0;
    v_last_date DATE;
    v_current_date DATE := CURRENT_DATE;
BEGIN
    -- Buscar última data de estudo
    SELECT MAX(session_date) INTO v_last_date
    FROM learning_sessions
    WHERE user_id = p_user_id;
    
    -- Calcular streak atual
    IF v_last_date IS NOT NULL THEN
        IF v_last_date = v_current_date OR v_last_date = v_current_date - 1 THEN
            -- Contar dias consecutivos
            WITH consecutive_days AS (
                SELECT session_date,
                       session_date - ROW_NUMBER() OVER (ORDER BY session_date) AS grp
                FROM (
                    SELECT DISTINCT session_date
                    FROM learning_sessions
                    WHERE user_id = p_user_id
                    ORDER BY session_date DESC
                ) dates
            )
            SELECT COUNT(*) INTO v_current_streak
            FROM consecutive_days
            WHERE grp = (
                SELECT grp FROM consecutive_days 
                ORDER BY session_date DESC 
                LIMIT 1
            );
        END IF;
    END IF;
    
    -- Atualizar métricas do usuário
    UPDATE user_performance_metrics
    SET current_streak = v_current_streak,
        longest_streak = GREATEST(longest_streak, v_current_streak),
        last_study_date = v_last_date
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Inserir conquistas padrão
INSERT INTO achievement_tracking (achievement_id, name, description, icon, rarity, points, requirement_type, requirement_value) VALUES
('first-week', 'Primeira Semana', 'Complete 7 dias consecutivos de estudo', 'calendar-week', 'common', 10, 'streak', '{"days": 7}'),
('card-master-100', 'Iniciante', 'Revise 100 flashcards', 'layer-group', 'common', 10, 'cards', '{"count": 100}'),
('card-master-1000', 'Mestre dos Cards', 'Revise 1000 flashcards', 'layer-group', 'rare', 25, 'cards', '{"count": 1000}'),
('card-master-5000', 'Grão-Mestre', 'Revise 5000 flashcards', 'layer-group', 'epic', 50, 'cards', '{"count": 5000}'),
('perfectionist', 'Perfeccionista', 'Alcance 95% de taxa de acerto em uma sessão', 'bullseye', 'epic', 50, 'accuracy', '{"rate": 95}'),
('streak-30', 'Determinado', 'Mantenha uma sequência de 30 dias', 'fire', 'rare', 30, 'streak', '{"days": 30}'),
('streak-100', 'Estudante Lendário', 'Mantenha uma sequência de 100 dias', 'fire', 'legendary', 100, 'streak', '{"days": 100}'),
('speed-demon', 'Velocista', 'Responda 50 cards em menos de 10 segundos cada', 'bolt', 'rare', 25, 'special', '{"cards": 50, "time": 10}'),
('night-owl', 'Coruja Noturna', 'Estude após as 22h por 10 dias', 'moon', 'common', 15, 'special', '{"hour": 22, "days": 10}'),
('early-bird', 'Madrugador', 'Estude antes das 6h por 10 dias', 'sun', 'common', 15, 'special', '{"hour": 6, "days": 10}')
ON CONFLICT (achievement_id) DO NOTHING;

-- Permissões
GRANT ALL ON SCHEMA analytics TO postgres;
GRANT USAGE ON SCHEMA analytics TO PUBLIC;
GRANT SELECT ON ALL TABLES IN SCHEMA analytics TO PUBLIC;
GRANT ALL ON ALL TABLES IN SCHEMA analytics TO postgres;

-- Comentários nas tabelas
COMMENT ON TABLE user_performance_metrics IS 'Métricas gerais de desempenho do usuário';
COMMENT ON TABLE learning_sessions IS 'Histórico detalhado de sessões de estudo';
COMMENT ON TABLE category_analytics IS 'Análise de desempenho por categoria/disciplina';
COMMENT ON TABLE review_history IS 'Histórico completo de revisões de cards';
COMMENT ON TABLE achievement_tracking IS 'Definições de conquistas disponíveis';
COMMENT ON TABLE user_achievements IS 'Conquistas desbloqueadas pelos usuários';
COMMENT ON TABLE goal_management IS 'Metas de estudo definidas pelos usuários';
COMMENT ON TABLE neural_network_weights IS 'Pesos personalizados da rede neural por usuário';
COMMENT ON TABLE user_rankings IS 'Rankings e comparações entre usuários';