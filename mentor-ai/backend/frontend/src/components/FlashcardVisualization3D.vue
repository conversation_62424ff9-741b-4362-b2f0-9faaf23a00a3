<template>
  <div class="flashcard-3d-container">
    <div class="visualization-header">
      <h2 class="visualization-title">
        <icon-cube class="title-icon" />
        Visualização 3D Imersiva
      </h2>
      <div class="visualization-controls">
        <button 
          class="control-btn"
          :class="{ active: viewMode === 'sphere' }"
          @click="viewMode = 'sphere'"
        >
          <icon-sphere />
          Esfera
        </button>
        <button 
          class="control-btn"
          :class="{ active: viewMode === 'helix' }"
          @click="viewMode = 'helix'"
        >
          <icon-dna />
          Hélice
        </button>
        <button 
          class="control-btn"
          :class="{ active: viewMode === 'galaxy' }"
          @click="viewMode = 'galaxy'"
        >
          <icon-stars />
          Galáxia
        </button>
        <button 
          class="control-btn"
          :class="{ active: viewMode === 'grid' }"
          @click="viewMode = 'grid'"
        >
          <icon-grid />
          Grade
        </button>
      </div>
    </div>

    <div class="canvas-container" ref="canvasContainer">
      <div class="loading-overlay" v-if="isLoading">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <p>Carregando visualização 3D...</p>
      </div>
    </div>

    <div class="interaction-panel">
      <div class="panel-section">
        <h3>Controles</h3>
        <div class="control-grid">
          <div class="control-item">
            <icon-mouse class="control-icon" />
            <div>
              <p class="control-label">Rotacionar</p>
              <p class="control-desc">Clique e arraste</p>
            </div>
          </div>
          <div class="control-item">
            <icon-zoom-in class="control-icon" />
            <div>
              <p class="control-label">Zoom</p>
              <p class="control-desc">Scroll do mouse</p>
            </div>
          </div>
          <div class="control-item">
            <icon-hand class="control-icon" />
            <div>
              <p class="control-label">Mover</p>
              <p class="control-desc">Clique direito + arraste</p>
            </div>
          </div>
          <div class="control-item">
            <icon-pointer class="control-icon" />
            <div>
              <p class="control-label">Selecionar</p>
              <p class="control-desc">Clique no card</p>
            </div>
          </div>
        </div>
      </div>

      <div class="panel-section">
        <h3>Filtros</h3>
        <div class="filter-options">
          <label class="filter-item">
            <input type="checkbox" v-model="filters.showMastered">
            <span class="filter-label">
              <span class="filter-dot mastered"></span>
              Dominados
            </span>
          </label>
          <label class="filter-item">
            <input type="checkbox" v-model="filters.showLearning">
            <span class="filter-label">
              <span class="filter-dot learning"></span>
              Aprendendo
            </span>
          </label>
          <label class="filter-item">
            <input type="checkbox" v-model="filters.showDifficult">
            <span class="filter-label">
              <span class="filter-dot difficult"></span>
              Difíceis
            </span>
          </label>
          <label class="filter-item">
            <input type="checkbox" v-model="filters.showDueToday">
            <span class="filter-label">
              <span class="filter-dot due"></span>
              Para hoje
            </span>
          </label>
        </div>
      </div>

      <div class="panel-section">
        <h3>Opções de Visualização</h3>
        <div class="visualization-options">
          <div class="option-slider">
            <label>Velocidade de Rotação</label>
            <input 
              type="range" 
              v-model="rotationSpeed" 
              min="0" 
              max="2" 
              step="0.1"
              class="slider"
            >
            <span class="slider-value">{{ rotationSpeed }}x</span>
          </div>
          <div class="option-slider">
            <label>Tamanho dos Cards</label>
            <input 
              type="range" 
              v-model="cardScale" 
              min="0.5" 
              max="2" 
              step="0.1"
              class="slider"
            >
            <span class="slider-value">{{ cardScale }}x</span>
          </div>
          <div class="option-toggle">
            <label class="toggle-switch">
              <input type="checkbox" v-model="showConnections">
              <span class="toggle-slider"></span>
              <span class="toggle-label">Mostrar Conexões</span>
            </label>
          </div>
          <div class="option-toggle">
            <label class="toggle-switch">
              <input type="checkbox" v-model="enableParticles">
              <span class="toggle-slider"></span>
              <span class="toggle-label">Partículas</span>
            </label>
          </div>
        </div>
      </div>

      <div class="panel-section stats-section">
        <h3>Estatísticas</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-value">{{ totalCards }}</span>
            <span class="stat-label">Total de Cards</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ selectedDeck || 'Todos' }}</span>
            <span class="stat-label">Baralho</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ avgDifficulty.toFixed(1) }}</span>
            <span class="stat-label">Dificuldade Média</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Selected Card Modal -->
    <transition name="modal-slide">
      <div v-if="selectedCard" class="card-modal" @click.self="selectedCard = null">
        <div class="modal-content">
          <button class="modal-close" @click="selectedCard = null">
            <icon-x />
          </button>
          
          <div class="card-3d-preview">
            <div class="card-3d" :class="{ flipped: isCardFlipped }" @click="isCardFlipped = !isCardFlipped">
              <div class="card-face card-front">
                <p>{{ selectedCard.front }}</p>
              </div>
              <div class="card-face card-back">
                <p>{{ selectedCard.back }}</p>
              </div>
            </div>
          </div>
          
          <div class="card-details">
            <h3>Detalhes do Card</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">Dificuldade</span>
                <div class="difficulty-bar">
                  <div 
                    class="difficulty-fill" 
                    :style="{ width: (selectedCard.difficulty * 20) + '%' }"
                    :class="`difficulty-${selectedCard.difficulty}`"
                  ></div>
                </div>
              </div>
              <div class="detail-item">
                <span class="detail-label">Última Revisão</span>
                <span class="detail-value">{{ formatDate(selectedCard.lastReview) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Próxima Revisão</span>
                <span class="detail-value">{{ formatDate(selectedCard.nextReview) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Taxa de Acerto</span>
                <span class="detail-value">{{ selectedCard.successRate }}%</span>
              </div>
            </div>
            
            <div class="card-actions">
              <button class="action-btn primary" @click="reviewCard(selectedCard)">
                <icon-play />
                Revisar Agora
              </button>
              <button class="action-btn secondary" @click="editCard(selectedCard)">
                <icon-edit />
                Editar
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass';
import { mapState, mapActions } from 'vuex';
import gsap from 'gsap';

export default {
  name: 'FlashcardVisualization3D',
  props: {
    deckId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      viewMode: 'sphere',
      isLoading: true,
      selectedCard: null,
      isCardFlipped: false,
      
      // Filters
      filters: {
        showMastered: true,
        showLearning: true,
        showDifficult: true,
        showDueToday: true
      },
      
      // Visualization options
      rotationSpeed: 0.5,
      cardScale: 1,
      showConnections: true,
      enableParticles: true,
      
      // Three.js objects
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      composer: null,
      cardMeshes: [],
      connectionLines: [],
      particles: null,
      raycaster: null,
      mouse: null,
      
      // Animation
      animationId: null,
      clock: null
    };
  },
  computed: {
    ...mapState('flashcards', ['decks']),
    
    cards() {
      if (this.deckId) {
        const deck = this.decks.find(d => d.id === this.deckId);
        return deck ? deck.cards : [];
      }
      return this.decks.flatMap(deck => deck.cards || []);
    },
    
    filteredCards() {
      return this.cards.filter(card => {
        if (!this.filters.showMastered && card.easeFactor > 2.5) return false;
        if (!this.filters.showLearning && card.easeFactor >= 1.5 && card.easeFactor <= 2.5) return false;
        if (!this.filters.showDifficult && card.easeFactor < 1.5) return false;
        if (!this.filters.showDueToday && !this.isDueToday(card.nextReview)) return false;
        return true;
      });
    },
    
    totalCards() {
      return this.filteredCards.length;
    },
    
    selectedDeck() {
      if (!this.deckId) return null;
      const deck = this.decks.find(d => d.id === this.deckId);
      return deck ? deck.name : null;
    },
    
    avgDifficulty() {
      if (this.filteredCards.length === 0) return 0;
      const sum = this.filteredCards.reduce((acc, card) => acc + (card.difficulty || 3), 0);
      return sum / this.filteredCards.length;
    }
  },
  watch: {
    viewMode() {
      this.updateVisualization();
    },
    filters: {
      deep: true,
      handler() {
        this.updateVisualization();
      }
    },
    cardScale() {
      this.updateCardScale();
    },
    showConnections() {
      this.updateConnections();
    },
    enableParticles() {
      this.updateParticles();
    }
  },
  mounted() {
    this.init3D();
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    this.cleanup();
  },
  methods: {
    ...mapActions('flashcards', ['updateCard']),
    
    init3D() {
      const container = this.$refs.canvasContainer;
      const width = container.clientWidth;
      const height = container.clientHeight;
      
      // Scene
      this.scene = new THREE.Scene();
      this.scene.background = new THREE.Color(0x000000);
      this.scene.fog = new THREE.Fog(0x000000, 50, 200);
      
      // Camera
      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
      this.camera.position.set(0, 20, 50);
      
      // Renderer
      this.renderer = new THREE.WebGLRenderer({ 
        antialias: true,
        alpha: true
      });
      this.renderer.setSize(width, height);
      this.renderer.setPixelRatio(window.devicePixelRatio);
      this.renderer.shadowMap.enabled = true;
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      container.appendChild(this.renderer.domElement);
      
      // Post-processing
      this.composer = new EffectComposer(this.renderer);
      const renderPass = new RenderPass(this.scene, this.camera);
      this.composer.addPass(renderPass);
      
      const bloomPass = new UnrealBloomPass(
        new THREE.Vector2(width, height),
        0.5, // Strength
        0.4, // Radius
        0.85 // Threshold
      );
      this.composer.addPass(bloomPass);
      
      // Controls
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      this.controls.enableDamping = true;
      this.controls.dampingFactor = 0.05;
      this.controls.minDistance = 20;
      this.controls.maxDistance = 200;
      this.controls.autoRotate = true;
      this.controls.autoRotateSpeed = this.rotationSpeed;
      
      // Lighting
      this.setupLighting();
      
      // Raycaster for interaction
      this.raycaster = new THREE.Raycaster();
      this.mouse = new THREE.Vector2();
      
      // Clock for animations
      this.clock = new THREE.Clock();
      
      // Event listeners
      this.renderer.domElement.addEventListener('click', this.onCanvasClick);
      this.renderer.domElement.addEventListener('mousemove', this.onCanvasMouseMove);
      
      // Create visualization
      this.createVisualization();
      
      // Start animation
      this.animate();
      
      // Loading complete
      setTimeout(() => {
        this.isLoading = false;
      }, 1000);
    },
    
    setupLighting() {
      // Ambient light
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.2);
      this.scene.add(ambientLight);
      
      // Directional lights
      const directionalLight1 = new THREE.DirectionalLight(0x8b5cf6, 0.8);
      directionalLight1.position.set(50, 50, 50);
      directionalLight1.castShadow = true;
      directionalLight1.shadow.camera.near = 0.1;
      directionalLight1.shadow.camera.far = 200;
      directionalLight1.shadow.camera.left = -50;
      directionalLight1.shadow.camera.right = 50;
      directionalLight1.shadow.camera.top = 50;
      directionalLight1.shadow.camera.bottom = -50;
      this.scene.add(directionalLight1);
      
      const directionalLight2 = new THREE.DirectionalLight(0x3b82f6, 0.6);
      directionalLight2.position.set(-50, 30, -50);
      this.scene.add(directionalLight2);
      
      // Point lights for glow effects
      const pointLight1 = new THREE.PointLight(0x8b5cf6, 1, 100);
      pointLight1.position.set(0, 0, 0);
      this.scene.add(pointLight1);
      
      const pointLight2 = new THREE.PointLight(0x3b82f6, 0.5, 80);
      pointLight2.position.set(30, 20, 30);
      this.scene.add(pointLight2);
    },
    
    createVisualization() {
      // Clear existing meshes
      this.cardMeshes.forEach(mesh => {
        this.scene.remove(mesh);
      });
      this.cardMeshes = [];
      
      // Create card meshes based on view mode
      switch (this.viewMode) {
        case 'sphere':
          this.createSphereLayout();
          break;
        case 'helix':
          this.createHelixLayout();
          break;
        case 'galaxy':
          this.createGalaxyLayout();
          break;
        case 'grid':
          this.createGridLayout();
          break;
      }
      
      // Create connections
      if (this.showConnections) {
        this.createConnections();
      }
      
      // Create particles
      if (this.enableParticles) {
        this.createParticles();
      }
    },
    
    createCardMesh(card, position) {
      // Card geometry
      const geometry = new THREE.BoxGeometry(3, 4, 0.2);
      
      // Card material based on difficulty
      let color, emissiveColor;
      if (card.easeFactor > 2.5) {
        color = 0x4ade80; // Green for mastered
        emissiveColor = 0x2d8659;
      } else if (card.easeFactor >= 1.5) {
        color = 0x3b82f6; // Blue for learning
        emissiveColor = 0x1d4ed8;
      } else {
        color = 0xef4444; // Red for difficult
        emissiveColor = 0x991b1b;
      }
      
      const material = new THREE.MeshPhongMaterial({
        color: color,
        emissive: emissiveColor,
        emissiveIntensity: 0.3,
        shininess: 100,
        specular: 0xffffff,
        transparent: true,
        opacity: 0.9
      });
      
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.copy(position);
      mesh.castShadow = true;
      mesh.receiveShadow = true;
      mesh.userData = { card };
      
      // Add floating animation
      const randomOffset = Math.random() * Math.PI * 2;
      mesh.userData.floatOffset = randomOffset;
      
      // Add to scene and array
      this.scene.add(mesh);
      this.cardMeshes.push(mesh);
      
      // Animate card appearance
      mesh.scale.set(0, 0, 0);
      gsap.to(mesh.scale, {
        x: this.cardScale,
        y: this.cardScale,
        z: this.cardScale,
        duration: 0.5,
        delay: Math.random() * 0.5,
        ease: "back.out(1.7)"
      });
      
      return mesh;
    },
    
    createSphereLayout() {
      const radius = 30;
      const cards = this.filteredCards;
      
      cards.forEach((card, index) => {
        const phi = Math.acos(-1 + (2 * index) / cards.length);
        const theta = Math.sqrt(cards.length * Math.PI) * phi;
        
        const x = radius * Math.cos(theta) * Math.sin(phi);
        const y = radius * Math.sin(theta) * Math.sin(phi);
        const z = radius * Math.cos(phi);
        
        const position = new THREE.Vector3(x, y, z);
        const mesh = this.createCardMesh(card, position);
        mesh.lookAt(0, 0, 0);
      });
    },
    
    createHelixLayout() {
      const cards = this.filteredCards;
      const radius = 20;
      const height = 80;
      const turns = 3;
      
      cards.forEach((card, index) => {
        const t = index / cards.length;
        const angle = t * turns * Math.PI * 2;
        
        const x = radius * Math.cos(angle);
        const y = -height / 2 + height * t;
        const z = radius * Math.sin(angle);
        
        const position = new THREE.Vector3(x, y, z);
        const mesh = this.createCardMesh(card, position);
        mesh.rotation.y = angle + Math.PI / 2;
      });
    },
    
    createGalaxyLayout() {
      const cards = this.filteredCards;
      const arms = 5;
      const armAngle = (Math.PI * 2) / arms;
      
      cards.forEach((card, index) => {
        const armIndex = index % arms;
        const distanceOnArm = (index / arms) * 3;
        const angle = armIndex * armAngle + distanceOnArm * 0.3;
        const radius = 10 + distanceOnArm * 5;
        
        // Add spiral effect
        const spiralOffset = distanceOnArm * 0.2;
        
        const x = radius * Math.cos(angle + spiralOffset);
        const y = (Math.random() - 0.5) * 5;
        const z = radius * Math.sin(angle + spiralOffset);
        
        const position = new THREE.Vector3(x, y, z);
        const mesh = this.createCardMesh(card, position);
        mesh.rotation.y = angle;
      });
    },
    
    createGridLayout() {
      const cards = this.filteredCards;
      const gridSize = Math.ceil(Math.sqrt(cards.length));
      const spacing = 6;
      const offsetX = (gridSize - 1) * spacing / 2;
      const offsetZ = (gridSize - 1) * spacing / 2;
      
      cards.forEach((card, index) => {
        const row = Math.floor(index / gridSize);
        const col = index % gridSize;
        
        const x = col * spacing - offsetX;
        const y = 0;
        const z = row * spacing - offsetZ;
        
        const position = new THREE.Vector3(x, y, z);
        const mesh = this.createCardMesh(card, position);
        mesh.rotation.x = -Math.PI / 8;
      });
    },
    
    createConnections() {
      // Clear existing connections
      this.connectionLines.forEach(line => {
        this.scene.remove(line);
      });
      this.connectionLines = [];
      
      // Create connections between related cards
      const material = new THREE.LineBasicMaterial({
        color: 0x8b5cf6,
        opacity: 0.2,
        transparent: true
      });
      
      // Connect cards with similar difficulty
      for (let i = 0; i < this.cardMeshes.length; i++) {
        for (let j = i + 1; j < this.cardMeshes.length; j++) {
          const card1 = this.cardMeshes[i].userData.card;
          const card2 = this.cardMeshes[j].userData.card;
          
          if (Math.abs(card1.easeFactor - card2.easeFactor) < 0.5) {
            const points = [
              this.cardMeshes[i].position,
              this.cardMeshes[j].position
            ];
            
            const geometry = new THREE.BufferGeometry().setFromPoints(points);
            const line = new THREE.Line(geometry, material);
            
            this.scene.add(line);
            this.connectionLines.push(line);
            
            // Limit connections for performance
            if (this.connectionLines.length > 100) break;
          }
        }
        if (this.connectionLines.length > 100) break;
      }
    },
    
    createParticles() {
      // Remove existing particles
      if (this.particles) {
        this.scene.remove(this.particles);
      }
      
      const particleCount = 1000;
      const geometry = new THREE.BufferGeometry();
      const positions = new Float32Array(particleCount * 3);
      const colors = new Float32Array(particleCount * 3);
      
      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;
        
        positions[i3] = (Math.random() - 0.5) * 200;
        positions[i3 + 1] = (Math.random() - 0.5) * 200;
        positions[i3 + 2] = (Math.random() - 0.5) * 200;
        
        const color = new THREE.Color();
        color.setHSL(Math.random() * 0.2 + 0.7, 0.8, 0.5);
        colors[i3] = color.r;
        colors[i3 + 1] = color.g;
        colors[i3 + 2] = color.b;
      }
      
      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
      
      const material = new THREE.PointsMaterial({
        size: 0.5,
        vertexColors: true,
        transparent: true,
        opacity: 0.6,
        blending: THREE.AdditiveBlending
      });
      
      this.particles = new THREE.Points(geometry, material);
      this.scene.add(this.particles);
    },
    
    animate() {
      this.animationId = requestAnimationFrame(this.animate);
      
      const delta = this.clock.getDelta();
      const time = this.clock.getElapsedTime();
      
      // Update controls
      this.controls.autoRotateSpeed = this.rotationSpeed;
      this.controls.update();
      
      // Animate cards floating
      this.cardMeshes.forEach((mesh, index) => {
        const floatOffset = mesh.userData.floatOffset || 0;
        mesh.position.y += Math.sin(time + floatOffset) * 0.005;
        mesh.rotation.z = Math.sin(time * 0.5 + floatOffset) * 0.02;
      });
      
      // Animate particles
      if (this.particles) {
        this.particles.rotation.y += 0.0005;
        this.particles.rotation.x += 0.0002;
      }
      
      // Animate connections
      this.connectionLines.forEach((line, index) => {
        line.material.opacity = 0.1 + Math.sin(time * 2 + index) * 0.05;
      });
      
      // Render
      this.composer.render();
    },
    
    onCanvasClick(event) {
      const rect = this.renderer.domElement.getBoundingClientRect();
      this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
      
      this.raycaster.setFromCamera(this.mouse, this.camera);
      const intersects = this.raycaster.intersectObjects(this.cardMeshes);
      
      if (intersects.length > 0) {
        const selectedMesh = intersects[0].object;
        this.selectedCard = selectedMesh.userData.card;
        this.isCardFlipped = false;
        
        // Highlight selected card
        gsap.to(selectedMesh.scale, {
          x: this.cardScale * 1.2,
          y: this.cardScale * 1.2,
          z: this.cardScale * 1.2,
          duration: 0.3,
          yoyo: true,
          repeat: 1
        });
      }
    },
    
    onCanvasMouseMove(event) {
      const rect = this.renderer.domElement.getBoundingClientRect();
      this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
      
      this.raycaster.setFromCamera(this.mouse, this.camera);
      const intersects = this.raycaster.intersectObjects(this.cardMeshes);
      
      // Reset all cards
      this.cardMeshes.forEach(mesh => {
        mesh.material.emissiveIntensity = 0.3;
      });
      
      // Highlight hovered card
      if (intersects.length > 0) {
        const hoveredMesh = intersects[0].object;
        hoveredMesh.material.emissiveIntensity = 0.6;
        this.renderer.domElement.style.cursor = 'pointer';
      } else {
        this.renderer.domElement.style.cursor = 'grab';
      }
    },
    
    updateVisualization() {
      // Clear and recreate visualization
      this.createVisualization();
    },
    
    updateCardScale() {
      this.cardMeshes.forEach(mesh => {
        gsap.to(mesh.scale, {
          x: this.cardScale,
          y: this.cardScale,
          z: this.cardScale,
          duration: 0.5
        });
      });
    },
    
    updateConnections() {
      if (this.showConnections) {
        this.createConnections();
      } else {
        this.connectionLines.forEach(line => {
          this.scene.remove(line);
        });
        this.connectionLines = [];
      }
    },
    
    updateParticles() {
      if (this.enableParticles) {
        this.createParticles();
      } else if (this.particles) {
        this.scene.remove(this.particles);
        this.particles = null;
      }
    },
    
    handleResize() {
      const container = this.$refs.canvasContainer;
      const width = container.clientWidth;
      const height = container.clientHeight;
      
      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
      
      this.renderer.setSize(width, height);
      this.composer.setSize(width, height);
    },
    
    isDueToday(date) {
      if (!date) return false;
      const today = new Date();
      const dueDate = new Date(date);
      return dueDate.toDateString() === today.toDateString();
    },
    
    formatDate(date) {
      if (!date) return 'Nunca';
      return new Date(date).toLocaleDateString('pt-BR');
    },
    
    reviewCard(card) {
      // Emit event to parent component
      this.$emit('review-card', card);
      this.selectedCard = null;
    },
    
    editCard(card) {
      // Emit event to parent component
      this.$emit('edit-card', card);
      this.selectedCard = null;
    },
    
    cleanup() {
      // Cancel animation
      if (this.animationId) {
        cancelAnimationFrame(this.animationId);
      }
      
      // Remove event listeners
      window.removeEventListener('resize', this.handleResize);
      if (this.renderer && this.renderer.domElement) {
        this.renderer.domElement.removeEventListener('click', this.onCanvasClick);
        this.renderer.domElement.removeEventListener('mousemove', this.onCanvasMouseMove);
      }
      
      // Dispose Three.js objects
      if (this.renderer) {
        this.renderer.dispose();
      }
      if (this.controls) {
        this.controls.dispose();
      }
      
      // Clear scene
      if (this.scene) {
        this.scene.traverse((child) => {
          if (child.geometry) {
            child.geometry.dispose();
          }
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        });
      }
    }
  }
};
</script>

<style scoped>
.flashcard-3d-container {
  display: flex;
  height: 100vh;
  background: #000;
  color: #fff;
  font-family: 'Inter', sans-serif;
}

/* Canvas Container */
.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  position: relative;
  width: 100px;
  height: 100px;
  margin-bottom: 2rem;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top-color: #8b5cf6;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border-top-color: #3b82f6;
  animation-duration: 1.2s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
  border-top-color: #10b981;
  animation-duration: 0.9s;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-overlay p {
  font-size: 1.125rem;
  color: #a1a1aa;
}

/* Visualization Header */
.visualization-header {
  position: absolute;
  top: 2rem;
  left: 2rem;
  z-index: 5;
}

.visualization-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.title-icon {
  width: 2rem;
  height: 2rem;
  color: #8b5cf6;
}

.visualization-controls {
  display: flex;
  gap: 0.5rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  padding: 0.5rem;
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  color: #a1a1aa;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.control-btn.active {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  color: #8b5cf6;
}

/* Interaction Panel */
.interaction-panel {
  width: 320px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem;
  overflow-y: auto;
}

.panel-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-section:last-child {
  border-bottom: none;
}

.panel-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #fff;
}

/* Controls Grid */
.control-grid {
  display: grid;
  gap: 1rem;
}

.control-item {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.control-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #8b5cf6;
  flex-shrink: 0;
}

.control-label {
  font-weight: 500;
  color: #fff;
  font-size: 0.875rem;
}

.control-desc {
  font-size: 0.75rem;
  color: #71717a;
}

/* Filter Options */
.filter-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.filter-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.filter-item input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
  accent-color: #8b5cf6;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #a1a1aa;
}

.filter-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.filter-dot.mastered {
  background: #4ade80;
}

.filter-dot.learning {
  background: #3b82f6;
}

.filter-dot.difficult {
  background: #ef4444;
}

.filter-dot.due {
  background: #fbbf24;
}

/* Visualization Options */
.visualization-options {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.option-slider {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.option-slider label {
  font-size: 0.875rem;
  color: #a1a1aa;
}

.slider {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: #8b5cf6;
  border-radius: 50%;
  cursor: pointer;
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #8b5cf6;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.slider-value {
  font-size: 0.875rem;
  color: #8b5cf6;
  font-weight: 600;
}

/* Toggle Switch */
.toggle-switch {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-switch input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 44px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  margin-right: 0.75rem;
  transition: background 0.3s ease;
}

.toggle-slider::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: #fff;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.toggle-switch input:checked + .toggle-slider {
  background: #8b5cf6;
}

.toggle-switch input:checked + .toggle-slider::after {
  transform: translateX(20px);
}

.toggle-label {
  font-size: 0.875rem;
  color: #a1a1aa;
}

/* Stats Section */
.stats-grid {
  display: grid;
  gap: 1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
}

.stat-value {
  font-weight: 600;
  color: #fff;
}

.stat-label {
  font-size: 0.875rem;
  color: #71717a;
}

/* Card Modal */
.card-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 2rem;
}

.modal-content {
  background: #0f0f0f;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1.5rem;
  padding: 2rem;
  max-width: 600px;
  width: 100%;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #a1a1aa;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

/* 3D Card Preview */
.card-3d-preview {
  perspective: 1000px;
  margin-bottom: 2rem;
}

.card-3d {
  position: relative;
  width: 100%;
  height: 250px;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  cursor: pointer;
}

.card-3d.flipped {
  transform: rotateY(180deg);
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 1.125rem;
  line-height: 1.6;
}

.card-back {
  transform: rotateY(180deg);
}

/* Card Details */
.card-details h3 {
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
}

.detail-grid {
  display: grid;
  gap: 1.25rem;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-label {
  font-size: 0.875rem;
  color: #71717a;
}

.detail-value {
  font-weight: 600;
  color: #fff;
}

.difficulty-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.difficulty-fill {
  height: 100%;
  transition: width 0.5s ease;
}

.difficulty-fill.difficulty-1 {
  background: #4ade80;
}

.difficulty-fill.difficulty-2 {
  background: #34d399;
}

.difficulty-fill.difficulty-3 {
  background: #fbbf24;
}

.difficulty-fill.difficulty-4 {
  background: #fb923c;
}

.difficulty-fill.difficulty-5 {
  background: #ef4444;
}

/* Card Actions */
.card-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  color: #fff;
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #a1a1aa;
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

/* Modal Transitions */
.modal-slide-enter-active,
.modal-slide-leave-active {
  transition: all 0.3s ease;
}

.modal-slide-enter-from {
  opacity: 0;
}

.modal-slide-leave-to {
  opacity: 0;
}

.modal-slide-enter-active .modal-content,
.modal-slide-leave-active .modal-content {
  transition: transform 0.3s ease;
}

.modal-slide-enter-from .modal-content {
  transform: scale(0.9) translateY(20px);
}

.modal-slide-leave-to .modal-content {
  transform: scale(0.9) translateY(20px);
}

/* Responsive */
@media (max-width: 1024px) {
  .interaction-panel {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }
  
  .interaction-panel.open {
    transform: translateX(0);
  }
}

@media (max-width: 640px) {
  .visualization-header {
    top: 1rem;
    left: 1rem;
  }
  
  .visualization-title {
    font-size: 1.5rem;
  }
  
  .visualization-controls {
    flex-wrap: wrap;
  }
  
  .control-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .modal-content {
    padding: 1.5rem;
  }
  
  .card-3d {
    height: 200px;
  }
}
</style>