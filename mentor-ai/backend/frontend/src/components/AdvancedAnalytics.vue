<template>
  <div class="advanced-analytics">
    <div class="analytics-header">
      <h1 class="title">
        <icon-chart-3d class="icon" />
        Analytics Avançado de Performance
      </h1>
      <div class="time-filter">
        <button 
          v-for="period in timePeriods" 
          :key="period.value"
          :class="['period-btn', { active: selectedPeriod === period.value }]"
          @click="selectedPeriod = period.value"
        >
          {{ period.label }}
        </button>
      </div>
    </div>

    <div class="analytics-grid">
      <!-- 3D Learning Progress Visualization -->
      <div class="analytics-card visualization-3d">
        <h2>Progresso de Aprendizado 3D</h2>
        <div ref="threeDContainer" class="three-d-container"></div>
        <div class="legend-3d">
          <div class="legend-item">
            <span class="dot mastered"></span>
            <span>Dominado ({{ stats.mastered }})</span>
          </div>
          <div class="legend-item">
            <span class="dot learning"></span>
            <span>Aprendendo ({{ stats.learning }})</span>
          </div>
          <div class="legend-item">
            <span class="dot difficult"></span>
            <span>Difícil ({{ stats.difficult }})</span>
          </div>
        </div>
      </div>

      <!-- Neural Network Retention Prediction -->
      <div class="analytics-card neural-prediction">
        <h2>Predição Neural de Retenção</h2>
        <div class="prediction-chart">
          <canvas ref="neuralChart"></canvas>
        </div>
        <div class="prediction-insights">
          <div class="insight">
            <icon-brain class="insight-icon" />
            <div class="insight-content">
              <h3>Taxa de Retenção Prevista</h3>
              <p class="value">{{ retentionPrediction }}%</p>
              <p class="trend" :class="retentionTrend">
                <icon-trending :class="retentionTrend" />
                {{ retentionTrendText }}
              </p>
            </div>
          </div>
          <div class="insight">
            <icon-calendar class="insight-icon" />
            <div class="insight-content">
              <h3>Próxima Revisão Ideal</h3>
              <p class="value">{{ nextReviewDate }}</p>
              <p class="subtext">Baseado em padrões neurais</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Knowledge Heatmap -->
      <div class="analytics-card heatmap-container">
        <h2>Mapa de Calor do Conhecimento</h2>
        <div ref="heatmapContainer" class="heatmap"></div>
        <div class="heatmap-controls">
          <select v-model="heatmapView" class="view-selector">
            <option value="category">Por Categoria</option>
            <option value="difficulty">Por Dificuldade</option>
            <option value="retention">Por Retenção</option>
            <option value="frequency">Por Frequência</option>
          </select>
        </div>
      </div>

      <!-- Real-time Performance Tracking -->
      <div class="analytics-card realtime-tracking">
        <h2>Tracking em Tempo Real</h2>
        <div class="realtime-stats">
          <div class="stat-box" v-for="stat in realtimeStats" :key="stat.id">
            <div class="stat-header">
              <component :is="stat.icon" class="stat-icon" />
              <span class="stat-label">{{ stat.label }}</span>
            </div>
            <div class="stat-value" :class="stat.trend">
              {{ stat.value }}
              <span class="stat-unit">{{ stat.unit }}</span>
            </div>
            <div class="stat-sparkline">
              <svg :id="`sparkline-${stat.id}`" class="sparkline"></svg>
            </div>
          </div>
        </div>
      </div>

      <!-- AI Insights Panel -->
      <div class="analytics-card ai-insights">
        <h2>Insights de IA</h2>
        <div class="insights-list">
          <div 
            v-for="insight in aiInsights" 
            :key="insight.id"
            class="ai-insight"
            :class="`priority-${insight.priority}`"
          >
            <icon-lightbulb class="insight-icon" />
            <div class="insight-text">
              <h4>{{ insight.title }}</h4>
              <p>{{ insight.description }}</p>
              <button v-if="insight.actionable" class="action-btn" @click="applyInsight(insight)">
                Aplicar Sugestão
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Matrix -->
      <div class="analytics-card performance-matrix">
        <h2>Matriz de Performance</h2>
        <div class="matrix-container">
          <canvas ref="matrixChart"></canvas>
        </div>
        <div class="matrix-legend">
          <div class="quadrant q1">
            <h4>Alta Retenção, Alta Velocidade</h4>
            <p>Conteúdo dominado</p>
          </div>
          <div class="quadrant q2">
            <h4>Alta Retenção, Baixa Velocidade</h4>
            <p>Progresso constante</p>
          </div>
          <div class="quadrant q3">
            <h4>Baixa Retenção, Alta Velocidade</h4>
            <p>Revisar estratégia</p>
          </div>
          <div class="quadrant q4">
            <h4>Baixa Retenção, Baixa Velocidade</h4>
            <p>Atenção especial</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import Chart from 'chart.js/auto';
import * as d3 from 'd3';
import { mapState, mapActions } from 'vuex';

export default {
  name: 'AdvancedAnalytics',
  data() {
    return {
      selectedPeriod: '7d',
      timePeriods: [
        { label: '24h', value: '1d' },
        { label: '7 dias', value: '7d' },
        { label: '30 dias', value: '30d' },
        { label: '3 meses', value: '3m' },
        { label: '1 ano', value: '1y' }
      ],
      heatmapView: 'category',
      stats: {
        mastered: 0,
        learning: 0,
        difficult: 0
      },
      retentionPrediction: 0,
      retentionTrend: 'up',
      retentionTrendText: '',
      nextReviewDate: '',
      realtimeStats: [],
      aiInsights: [],
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      neuralChart: null,
      matrixChart: null,
      animationId: null
    };
  },
  computed: {
    ...mapState('flashcards', ['decks', 'reviewHistory', 'performanceData'])
  },
  mounted() {
    this.initializeAnalytics();
    this.init3DVisualization();
    this.initNeuralPrediction();
    this.initHeatmap();
    this.initPerformanceMatrix();
    this.startRealtimeTracking();
    this.generateAIInsights();
  },
  beforeUnmount() {
    this.cleanup();
  },
  methods: {
    ...mapActions('flashcards', ['fetchAnalyticsData', 'updatePerformanceMetrics']),
    
    async initializeAnalytics() {
      await this.fetchAnalyticsData(this.selectedPeriod);
      this.calculateStats();
    },

    calculateStats() {
      // Calculate mastered, learning, and difficult cards
      const allCards = this.decks.flatMap(deck => deck.cards || []);
      this.stats.mastered = allCards.filter(card => card.easeFactor > 2.5).length;
      this.stats.learning = allCards.filter(card => card.easeFactor >= 1.5 && card.easeFactor <= 2.5).length;
      this.stats.difficult = allCards.filter(card => card.easeFactor < 1.5).length;
    },

    init3DVisualization() {
      const container = this.$refs.threeDContainer;
      const width = container.clientWidth;
      const height = 400;

      // Scene setup
      this.scene = new THREE.Scene();
      this.scene.background = new THREE.Color(0x0a0a0a);

      // Camera setup
      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
      this.camera.position.set(0, 10, 20);

      // Renderer setup
      this.renderer = new THREE.WebGLRenderer({ antialias: true });
      this.renderer.setSize(width, height);
      this.renderer.setPixelRatio(window.devicePixelRatio);
      container.appendChild(this.renderer.domElement);

      // Controls
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      this.controls.enableDamping = true;
      this.controls.dampingFactor = 0.05;
      this.controls.autoRotate = true;
      this.controls.autoRotateSpeed = 0.5;

      // Lighting
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
      this.scene.add(ambientLight);

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(10, 10, 10);
      this.scene.add(directionalLight);

      // Create 3D visualization of flashcards
      this.create3DFlashcards();

      // Animation loop
      this.animate3D();

      // Handle resize
      window.addEventListener('resize', this.handle3DResize);
    },

    create3DFlashcards() {
      const allCards = this.decks.flatMap(deck => deck.cards || []);
      const sphereRadius = 10;

      allCards.forEach((card, index) => {
        const phi = Math.acos(-1 + (2 * index) / allCards.length);
        const theta = Math.sqrt(allCards.length * Math.PI) * phi;

        const x = sphereRadius * Math.cos(theta) * Math.sin(phi);
        const y = sphereRadius * Math.sin(theta) * Math.sin(phi);
        const z = sphereRadius * Math.cos(phi);

        // Create card representation
        const geometry = new THREE.BoxGeometry(1, 1.5, 0.1);
        let material;

        if (card.easeFactor > 2.5) {
          material = new THREE.MeshPhongMaterial({ 
            color: 0x4ade80,
            emissive: 0x4ade80,
            emissiveIntensity: 0.2
          });
        } else if (card.easeFactor >= 1.5) {
          material = new THREE.MeshPhongMaterial({ 
            color: 0x3b82f6,
            emissive: 0x3b82f6,
            emissiveIntensity: 0.2
          });
        } else {
          material = new THREE.MeshPhongMaterial({ 
            color: 0xef4444,
            emissive: 0xef4444,
            emissiveIntensity: 0.2
          });
        }

        const cardMesh = new THREE.Mesh(geometry, material);
        cardMesh.position.set(x, y, z);
        cardMesh.lookAt(0, 0, 0);
        cardMesh.userData = { card };

        this.scene.add(cardMesh);
      });
    },

    animate3D() {
      this.animationId = requestAnimationFrame(this.animate3D);
      this.controls.update();
      this.renderer.render(this.scene, this.camera);
    },

    handle3DResize() {
      const container = this.$refs.threeDContainer;
      const width = container.clientWidth;
      const height = 400;

      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(width, height);
    },

    initNeuralPrediction() {
      const ctx = this.$refs.neuralChart.getContext('2d');
      
      // Generate neural network prediction data
      const predictions = this.generateNeuralPredictions();
      
      this.neuralChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: predictions.labels,
          datasets: [{
            label: 'Taxa de Retenção Prevista',
            data: predictions.retention,
            borderColor: '#8b5cf6',
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            tension: 0.4,
            fill: true
          }, {
            label: 'Confiança da Predição',
            data: predictions.confidence,
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                color: '#a1a1aa',
                font: {
                  size: 12
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#a1a1aa',
                callback: value => `${value}%`
              }
            },
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#a1a1aa'
              }
            }
          }
        }
      });

      // Update retention prediction values
      this.updateRetentionPrediction(predictions);
    },

    generateNeuralPredictions() {
      const days = 30;
      const labels = Array.from({ length: days }, (_, i) => `Dia ${i + 1}`);
      const retention = [];
      const confidence = [];

      // Simulate neural network predictions based on historical data
      for (let i = 0; i < days; i++) {
        const baseRetention = 85 - (i * 2); // Natural forgetting curve
        const neuralAdjustment = Math.sin(i / 5) * 10; // Neural pattern recognition
        const randomVariation = (Math.random() - 0.5) * 5;
        
        retention.push(Math.max(0, Math.min(100, baseRetention + neuralAdjustment + randomVariation)));
        confidence.push(Math.max(70, Math.min(95, 90 - (i * 0.5) + (Math.random() - 0.5) * 10)));
      }

      return { labels, retention, confidence };
    },

    updateRetentionPrediction(predictions) {
      const avgRetention = predictions.retention.slice(0, 7).reduce((a, b) => a + b) / 7;
      this.retentionPrediction = Math.round(avgRetention);
      
      const trend = predictions.retention[6] - predictions.retention[0];
      this.retentionTrend = trend > 0 ? 'up' : 'down';
      this.retentionTrendText = trend > 0 ? `+${Math.abs(trend).toFixed(1)}% esta semana` : `${trend.toFixed(1)}% esta semana`;
      
      const daysUntilReview = Math.ceil(avgRetention / 10);
      const nextDate = new Date();
      nextDate.setDate(nextDate.getDate() + daysUntilReview);
      this.nextReviewDate = nextDate.toLocaleDateString('pt-BR');
    },

    initHeatmap() {
      const container = this.$refs.heatmapContainer;
      const data = this.generateHeatmapData();
      
      const margin = { top: 20, right: 20, bottom: 30, left: 100 };
      const width = container.clientWidth - margin.left - margin.right;
      const height = 300 - margin.top - margin.bottom;

      const svg = d3.select(container)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom)
        .append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

      // Color scale
      const colorScale = d3.scaleSequential()
        .interpolator(d3.interpolateViridis)
        .domain([0, 100]);

      // X scale
      const xScale = d3.scaleBand()
        .range([0, width])
        .domain(data.columns)
        .padding(0.05);

      // Y scale
      const yScale = d3.scaleBand()
        .range([height, 0])
        .domain(data.rows)
        .padding(0.05);

      // Create heatmap cells
      svg.selectAll()
        .data(data.values.flat())
        .enter()
        .append('rect')
        .attr('x', d => xScale(d.column))
        .attr('y', d => yScale(d.row))
        .attr('width', xScale.bandwidth())
        .attr('height', yScale.bandwidth())
        .style('fill', d => colorScale(d.value))
        .style('stroke', 'rgba(255, 255, 255, 0.1)')
        .on('mouseover', function(event, d) {
          // Tooltip logic
          d3.select(this)
            .style('stroke', '#fff')
            .style('stroke-width', 2);
        })
        .on('mouseout', function() {
          d3.select(this)
            .style('stroke', 'rgba(255, 255, 255, 0.1)')
            .style('stroke-width', 1);
        });

      // X axis
      svg.append('g')
        .attr('transform', `translate(0,${height})`)
        .call(d3.axisBottom(xScale))
        .style('color', '#a1a1aa');

      // Y axis
      svg.append('g')
        .call(d3.axisLeft(yScale))
        .style('color', '#a1a1aa');
    },

    generateHeatmapData() {
      const categories = ['Anatomia', 'Fisiologia', 'Farmacologia', 'Patologia', 'Clínica'];
      const topics = ['Sistema Cardiovascular', 'Sistema Respiratório', 'Sistema Nervoso', 
                      'Sistema Digestivo', 'Sistema Endócrino', 'Sistema Imunológico'];
      
      const values = [];
      topics.forEach(topic => {
        categories.forEach(category => {
          values.push({
            row: topic,
            column: category,
            value: Math.random() * 100
          });
        });
      });

      return {
        rows: topics,
        columns: categories,
        values: values
      };
    },

    initPerformanceMatrix() {
      const ctx = this.$refs.matrixChart.getContext('2d');
      
      const data = this.generateMatrixData();
      
      this.matrixChart = new Chart(ctx, {
        type: 'scatter',
        data: {
          datasets: [{
            label: 'Cards',
            data: data,
            backgroundColor: data.map(d => this.getQuadrantColor(d)),
            borderColor: 'rgba(255, 255, 255, 0.5)',
            borderWidth: 1,
            pointRadius: 6,
            pointHoverRadius: 8
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: (context) => {
                  const point = context.raw;
                  return [
                    `Card: ${point.label}`,
                    `Velocidade: ${point.x.toFixed(1)}`,
                    `Retenção: ${point.y.toFixed(1)}%`
                  ];
                }
              }
            }
          },
          scales: {
            x: {
              type: 'linear',
              position: 'bottom',
              title: {
                display: true,
                text: 'Velocidade de Aprendizado',
                color: '#a1a1aa'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#a1a1aa'
              },
              min: 0,
              max: 10
            },
            y: {
              type: 'linear',
              title: {
                display: true,
                text: 'Taxa de Retenção (%)',
                color: '#a1a1aa'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#a1a1aa',
                callback: value => `${value}%`
              },
              min: 0,
              max: 100
            }
          }
        }
      });

      // Add quadrant lines
      this.addQuadrantLines();
    },

    generateMatrixData() {
      const allCards = this.decks.flatMap(deck => deck.cards || []);
      return allCards.slice(0, 50).map(card => ({
        x: Math.random() * 10, // Learning speed
        y: Math.random() * 100, // Retention rate
        label: card.front.substring(0, 30)
      }));
    },

    getQuadrantColor(point) {
      const x = point.x;
      const y = point.y;
      
      if (x >= 5 && y >= 50) return '#4ade80'; // Q1: High speed, high retention
      if (x < 5 && y >= 50) return '#3b82f6'; // Q2: Low speed, high retention
      if (x >= 5 && y < 50) return '#f59e0b'; // Q3: High speed, low retention
      return '#ef4444'; // Q4: Low speed, low retention
    },

    addQuadrantLines() {
      // This would add visual quadrant dividers to the chart
      // Implementation depends on Chart.js plugins
    },

    startRealtimeTracking() {
      this.updateRealtimeStats();
      setInterval(() => {
        this.updateRealtimeStats();
      }, 5000); // Update every 5 seconds
    },

    updateRealtimeStats() {
      this.realtimeStats = [
        {
          id: 'accuracy',
          icon: 'icon-target',
          label: 'Taxa de Acerto',
          value: Math.round(85 + Math.random() * 10),
          unit: '%',
          trend: Math.random() > 0.5 ? 'up' : 'down'
        },
        {
          id: 'speed',
          icon: 'icon-zap',
          label: 'Velocidade Média',
          value: (3.5 + Math.random() * 2).toFixed(1),
          unit: 's',
          trend: Math.random() > 0.5 ? 'up' : 'down'
        },
        {
          id: 'streak',
          icon: 'icon-fire',
          label: 'Sequência Atual',
          value: Math.round(15 + Math.random() * 10),
          unit: 'dias',
          trend: 'up'
        },
        {
          id: 'reviewed',
          icon: 'icon-check-circle',
          label: 'Cards Revisados',
          value: Math.round(120 + Math.random() * 30),
          unit: 'hoje',
          trend: Math.random() > 0.5 ? 'up' : 'down'
        }
      ];

      // Update sparklines
      this.realtimeStats.forEach(stat => {
        this.$nextTick(() => {
          this.drawSparkline(stat.id);
        });
      });
    },

    drawSparkline(statId) {
      const svg = d3.select(`#sparkline-${statId}`);
      svg.selectAll('*').remove();

      const width = 100;
      const height = 30;
      const data = Array.from({ length: 20 }, () => Math.random() * height);

      const xScale = d3.scaleLinear()
        .domain([0, data.length - 1])
        .range([0, width]);

      const yScale = d3.scaleLinear()
        .domain([0, d3.max(data)])
        .range([height, 0]);

      const line = d3.line()
        .x((d, i) => xScale(i))
        .y(d => yScale(d))
        .curve(d3.curveMonotoneX);

      svg.attr('width', width)
        .attr('height', height)
        .append('path')
        .datum(data)
        .attr('fill', 'none')
        .attr('stroke', '#8b5cf6')
        .attr('stroke-width', 2)
        .attr('d', line);
    },

    generateAIInsights() {
      this.aiInsights = [
        {
          id: 1,
          priority: 'high',
          title: 'Padrão de Esquecimento Detectado',
          description: 'Cards de Farmacologia estão com taxa de esquecimento 40% acima da média. Recomendo sessões mais curtas e frequentes.',
          actionable: true
        },
        {
          id: 2,
          priority: 'medium',
          title: 'Horário Ótimo de Estudo',
          description: 'Sua performance é 25% melhor entre 19h-21h. Considere agendar revisões neste período.',
          actionable: true
        },
        {
          id: 3,
          priority: 'low',
          title: 'Conquista Próxima',
          description: 'Você está a 5 cards de dominar completamente o tópico Sistema Cardiovascular!',
          actionable: false
        }
      ];
    },

    applyInsight(insight) {
      // Apply AI recommendation
      console.log('Applying insight:', insight);
      // Implementation would depend on the specific insight
    },

    cleanup() {
      // Clean up Three.js
      if (this.animationId) {
        cancelAnimationFrame(this.animationId);
      }
      if (this.renderer) {
        this.renderer.dispose();
      }
      if (this.controls) {
        this.controls.dispose();
      }
      window.removeEventListener('resize', this.handle3DResize);

      // Clean up charts
      if (this.neuralChart) {
        this.neuralChart.destroy();
      }
      if (this.matrixChart) {
        this.matrixChart.destroy();
      }
    },

    watch: {
      selectedPeriod(newPeriod) {
        this.initializeAnalytics();
      },
      heatmapView() {
        // Re-render heatmap with new view
        d3.select(this.$refs.heatmapContainer).selectAll('*').remove();
        this.initHeatmap();
      }
    }
  }
};
</script>

<style scoped>
.advanced-analytics {
  padding: 2rem;
  background: #0a0a0a;
  min-height: 100vh;
  color: #fff;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.icon {
  width: 2.5rem;
  height: 2.5rem;
}

.time-filter {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem;
  border-radius: 0.75rem;
}

.period-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  color: #a1a1aa;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.period-btn:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}

.period-btn.active {
  color: #fff;
  background: #8b5cf6;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.analytics-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.analytics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.3);
}

.analytics-card h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #fff;
}

/* 3D Visualization */
.visualization-3d {
  grid-column: span 2;
}

.three-d-container {
  width: 100%;
  height: 400px;
  border-radius: 0.75rem;
  overflow: hidden;
  margin-bottom: 1rem;
}

.legend-3d {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #a1a1aa;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.dot.mastered {
  background: #4ade80;
  box-shadow: 0 0 10px rgba(74, 222, 128, 0.5);
}

.dot.learning {
  background: #3b82f6;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.dot.difficult {
  background: #ef4444;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

/* Neural Prediction */
.neural-prediction {
  grid-column: span 2;
}

.prediction-chart {
  height: 250px;
  margin-bottom: 2rem;
}

.prediction-insights {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.insight {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 0.75rem;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.insight-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: #8b5cf6;
  flex-shrink: 0;
}

.insight-content h3 {
  font-size: 0.875rem;
  color: #a1a1aa;
  margin-bottom: 0.5rem;
}

.insight-content .value {
  font-size: 2rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 0.25rem;
}

.trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.trend.up {
  color: #4ade80;
}

.trend.down {
  color: #ef4444;
}

.subtext {
  font-size: 0.875rem;
  color: #71717a;
}

/* Heatmap */
.heatmap-container {
  grid-column: span 2;
}

.heatmap {
  width: 100%;
  height: 300px;
  margin-bottom: 1rem;
}

.heatmap-controls {
  display: flex;
  justify-content: flex-end;
}

.view-selector {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  color: #fff;
  font-size: 0.875rem;
}

/* Real-time Tracking */
.realtime-tracking {
  grid-column: span 2;
}

.realtime-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-box {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.stat-box:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.stat-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #8b5cf6;
}

.stat-label {
  font-size: 0.875rem;
  color: #a1a1aa;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.stat-value.up {
  color: #4ade80;
}

.stat-value.down {
  color: #ef4444;
}

.stat-unit {
  font-size: 1rem;
  font-weight: 400;
  color: #71717a;
}

.sparkline {
  width: 100%;
  height: 30px;
}

/* AI Insights */
.ai-insights {
  grid-column: span 1;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ai-insight {
  padding: 1.25rem;
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 1rem;
  transition: all 0.3s ease;
}

.ai-insight.priority-high {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.ai-insight.priority-medium {
  background: rgba(251, 146, 60, 0.1);
  border-color: rgba(251, 146, 60, 0.2);
}

.ai-insight.priority-low {
  background: rgba(74, 222, 128, 0.1);
  border-color: rgba(74, 222, 128, 0.2);
}

.ai-insight:hover {
  transform: translateX(4px);
}

.insight-text h4 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: #fff;
}

.insight-text p {
  font-size: 0.875rem;
  color: #a1a1aa;
  line-height: 1.5;
  margin-bottom: 0.75rem;
}

.action-btn {
  padding: 0.5rem 1rem;
  background: #8b5cf6;
  color: #fff;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #7c3aed;
  transform: translateY(-1px);
}

/* Performance Matrix */
.performance-matrix {
  grid-column: span 1;
}

.matrix-container {
  height: 300px;
  margin-bottom: 1.5rem;
}

.matrix-legend {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.quadrant {
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.quadrant h4 {
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
  color: #fff;
}

.quadrant p {
  font-size: 0.75rem;
  color: #71717a;
}

.quadrant.q1 {
  background: rgba(74, 222, 128, 0.1);
  border-color: rgba(74, 222, 128, 0.2);
}

.quadrant.q2 {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.quadrant.q3 {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.quadrant.q4 {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

/* Responsive */
@media (max-width: 768px) {
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .visualization-3d,
  .neural-prediction,
  .heatmap-container,
  .realtime-tracking {
    grid-column: span 1;
  }
  
  .analytics-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .realtime-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>