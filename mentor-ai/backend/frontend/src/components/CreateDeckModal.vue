<template>
  <transition name="modal-fade">
    <div v-if="isOpen" class="modal-overlay" @click.self="closeModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">
            <icon-sparkles class="icon" />
            Criar Novo Baralho com IA
          </h2>
          <button class="close-btn" @click="closeModal">
            <icon-x />
          </button>
        </div>

        <div class="modal-body">
          <!-- Input Type Selection -->
          <div class="input-type-selector">
            <div 
              v-for="type in inputTypes" 
              :key="type.id"
              :class="['input-type', { active: selectedInputType === type.id }]"
              @click="selectedInputType = type.id"
            >
              <component :is="type.icon" class="type-icon" />
              <span class="type-label">{{ type.label }}</span>
              <span class="type-badge" v-if="type.badge">{{ type.badge }}</span>
            </div>
          </div>

          <!-- Dynamic Input Area -->
          <div class="input-area">
            <!-- Text Input -->
            <div v-if="selectedInputType === 'text'" class="text-input-container">
              <textarea
                v-model="textInput"
                placeholder="Cole seu texto aqui ou digite o conteúdo que deseja transformar em flashcards..."
                class="text-input"
                @input="analyzeContent"
              ></textarea>
              <div class="input-actions">
                <button class="action-btn" @click="pasteFromClipboard">
                  <icon-clipboard /> Colar
                </button>
                <button class="action-btn" @click="clearText">
                  <icon-trash /> Limpar
                </button>
              </div>
            </div>

            <!-- PDF Upload -->
            <div v-if="selectedInputType === 'pdf'" class="upload-container">
              <div 
                class="upload-area"
                :class="{ dragging: isDragging }"
                @dragover.prevent="isDragging = true"
                @dragleave.prevent="isDragging = false"
                @drop.prevent="handlePDFDrop"
              >
                <input 
                  type="file" 
                  ref="pdfInput"
                  accept=".pdf"
                  @change="handlePDFUpload"
                  style="display: none"
                >
                <icon-file-text class="upload-icon" />
                <h3>Arraste um PDF aqui</h3>
                <p>ou <button class="link-btn" @click="$refs.pdfInput.click()">clique para selecionar</button></p>
                <span class="file-limit">Máximo 50MB</span>
              </div>
              <div v-if="uploadedFile" class="uploaded-file">
                <icon-file class="file-icon" />
                <div class="file-info">
                  <span class="file-name">{{ uploadedFile.name }}</span>
                  <span class="file-size">{{ formatFileSize(uploadedFile.size) }}</span>
                </div>
                <button class="remove-file" @click="removeFile">
                  <icon-x />
                </button>
              </div>
            </div>

            <!-- Image Upload -->
            <div v-if="selectedInputType === 'image'" class="upload-container">
              <div 
                class="upload-area image-upload"
                :class="{ dragging: isDragging }"
                @dragover.prevent="isDragging = true"
                @dragleave.prevent="isDragging = false"
                @drop.prevent="handleImageDrop"
              >
                <input 
                  type="file" 
                  ref="imageInput"
                  accept="image/*"
                  multiple
                  @change="handleImageUpload"
                  style="display: none"
                >
                <icon-image class="upload-icon" />
                <h3>Arraste imagens aqui</h3>
                <p>ou <button class="link-btn" @click="$refs.imageInput.click()">clique para selecionar</button></p>
                <span class="file-limit">PNG, JPG, JPEG (máx. 10MB cada)</span>
              </div>
              <div v-if="uploadedImages.length" class="image-preview-grid">
                <div v-for="(image, index) in uploadedImages" :key="index" class="image-preview">
                  <img :src="image.preview" :alt="image.name">
                  <button class="remove-image" @click="removeImage(index)">
                    <icon-x />
                  </button>
                </div>
              </div>
            </div>

            <!-- URL Input -->
            <div v-if="selectedInputType === 'url'" class="url-input-container">
              <div class="url-input-wrapper">
                <icon-globe class="url-icon" />
                <input
                  v-model="urlInput"
                  type="url"
                  placeholder="https://exemplo.com/artigo-medico"
                  class="url-input"
                  @input="validateURL"
                >
              </div>
              <button 
                class="fetch-btn" 
                :disabled="!isValidURL || isFetching"
                @click="fetchURLContent"
              >
                <icon-download v-if="!isFetching" />
                <icon-loader v-else class="spinning" />
                {{ isFetching ? 'Buscando...' : 'Buscar Conteúdo' }}
              </button>
              <div v-if="urlError" class="error-message">
                <icon-alert-circle />
                {{ urlError }}
              </div>
            </div>
          </div>

          <!-- AI Processing Options -->
          <div class="ai-options">
            <h3 class="section-title">Opções de Processamento IA</h3>
            
            <div class="option-grid">
              <div class="option-card">
                <label class="option-label">
                  <icon-language class="option-icon" />
                  Idioma
                </label>
                <select v-model="processingOptions.language" class="option-select">
                  <option value="pt-BR">Português (BR)</option>
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="auto">Auto-detectar</option>
                </select>
              </div>

              <div class="option-card">
                <label class="option-label">
                  <icon-gauge class="option-icon" />
                  Dificuldade
                </label>
                <select v-model="processingOptions.difficulty" class="option-select">
                  <option value="auto">Auto-detectar</option>
                  <option value="beginner">Iniciante</option>
                  <option value="intermediate">Intermediário</option>
                  <option value="advanced">Avançado</option>
                  <option value="expert">Especialista</option>
                </select>
              </div>

              <div class="option-card">
                <label class="option-label">
                  <icon-brain class="option-icon" />
                  Tipo de Card
                </label>
                <select v-model="processingOptions.cardType" class="option-select">
                  <option value="mixed">Misto</option>
                  <option value="basic">Básico (Frente/Verso)</option>
                  <option value="cloze">Cloze (Preencher lacunas)</option>
                  <option value="image">Com Imagens</option>
                  <option value="clinical">Caso Clínico</option>
                </select>
              </div>

              <div class="option-card">
                <label class="option-label">
                  <icon-target class="option-icon" />
                  Foco
                </label>
                <select v-model="processingOptions.focus" class="option-select">
                  <option value="comprehensive">Compreensivo</option>
                  <option value="key-concepts">Conceitos-chave</option>
                  <option value="memorization">Memorização</option>
                  <option value="application">Aplicação Prática</option>
                  <option value="differential">Diagnóstico Diferencial</option>
                </select>
              </div>
            </div>

            <div class="advanced-options">
              <button class="toggle-advanced" @click="showAdvanced = !showAdvanced">
                <icon-settings class="toggle-icon" />
                Opções Avançadas
                <icon-chevron-down class="chevron" :class="{ rotated: showAdvanced }" />
              </button>
              
              <transition name="slide-down">
                <div v-if="showAdvanced" class="advanced-panel">
                  <div class="toggle-option">
                    <label class="toggle-label">
                      <input type="checkbox" v-model="processingOptions.includeExplanations">
                      <span class="toggle-text">Incluir explicações detalhadas</span>
                    </label>
                  </div>
                  <div class="toggle-option">
                    <label class="toggle-label">
                      <input type="checkbox" v-model="processingOptions.generateImages">
                      <span class="toggle-text">Gerar imagens com IA</span>
                    </label>
                  </div>
                  <div class="toggle-option">
                    <label class="toggle-label">
                      <input type="checkbox" v-model="processingOptions.addPronunciation">
                      <span class="toggle-text">Adicionar pronúncia (termos médicos)</span>
                    </label>
                  </div>
                  <div class="toggle-option">
                    <label class="toggle-label">
                      <input type="checkbox" v-model="processingOptions.crossReference">
                      <span class="toggle-text">Criar referências cruzadas</span>
                    </label>
                  </div>
                </div>
              </transition>
            </div>
          </div>

          <!-- Deck Configuration -->
          <div class="deck-config">
            <h3 class="section-title">Configuração do Baralho</h3>
            
            <div class="config-grid">
              <div class="config-field">
                <label class="field-label">Nome do Baralho</label>
                <input
                  v-model="deckConfig.name"
                  type="text"
                  placeholder="Ex: Anatomia - Sistema Cardiovascular"
                  class="field-input"
                >
              </div>
              
              <div class="config-field">
                <label class="field-label">Categoria</label>
                <div class="category-input">
                  <select v-model="deckConfig.category" class="field-select">
                    <option value="">Selecionar categoria...</option>
                    <option v-for="cat in categories" :key="cat" :value="cat">{{ cat }}</option>
                    <option value="custom">+ Nova categoria</option>
                  </select>
                  <input
                    v-if="deckConfig.category === 'custom'"
                    v-model="customCategory"
                    type="text"
                    placeholder="Nome da categoria"
                    class="field-input custom-category"
                  >
                </div>
              </div>
              
              <div class="config-field full-width">
                <label class="field-label">Tags (separadas por vírgula)</label>
                <div class="tags-input-wrapper">
                  <input
                    v-model="tagsInput"
                    type="text"
                    placeholder="Ex: cardiologia, fisiologia, prova1"
                    class="field-input"
                    @keydown.enter="addTag"
                  >
                  <div class="tag-suggestions" v-if="tagSuggestions.length">
                    <button
                      v-for="tag in tagSuggestions"
                      :key="tag"
                      class="tag-suggestion"
                      @click="addSuggestedTag(tag)"
                    >
                      {{ tag }}
                    </button>
                  </div>
                </div>
                <div class="tags-display">
                  <span v-for="(tag, index) in deckConfig.tags" :key="index" class="tag">
                    {{ tag }}
                    <button class="remove-tag" @click="removeTag(index)">
                      <icon-x />
                    </button>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Preview Section -->
          <div v-if="generatedCards.length > 0" class="preview-section">
            <div class="preview-header">
              <h3 class="section-title">
                Prévia dos Cards
                <span class="card-count">({{ generatedCards.length }} cards)</span>
              </h3>
              <div class="preview-controls">
                <button class="preview-nav" @click="previousCard" :disabled="currentPreviewIndex === 0">
                  <icon-chevron-left />
                </button>
                <span class="preview-counter">
                  {{ currentPreviewIndex + 1 }} / {{ generatedCards.length }}
                </span>
                <button class="preview-nav" @click="nextCard" :disabled="currentPreviewIndex === generatedCards.length - 1">
                  <icon-chevron-right />
                </button>
              </div>
            </div>
            
            <div class="card-preview" v-if="currentPreviewCard">
              <div class="preview-card" :class="{ flipped: isFlipped }" @click="isFlipped = !isFlipped">
                <div class="card-face card-front">
                  <div class="card-content">
                    <p v-html="currentPreviewCard.front"></p>
                    <img v-if="currentPreviewCard.frontImage" :src="currentPreviewCard.frontImage" alt="Card image">
                  </div>
                  <div class="card-metadata">
                    <span class="difficulty-badge" :class="`difficulty-${currentPreviewCard.difficulty}`">
                      {{ getDifficultyLabel(currentPreviewCard.difficulty) }}
                    </span>
                    <span class="quality-score" :class="`quality-${getQualityClass(currentPreviewCard.qualityScore)}`">
                      <icon-star-filled />
                      {{ currentPreviewCard.qualityScore }}/10
                    </span>
                  </div>
                </div>
                <div class="card-face card-back">
                  <div class="card-content">
                    <p v-html="currentPreviewCard.back"></p>
                    <img v-if="currentPreviewCard.backImage" :src="currentPreviewCard.backImage" alt="Card image">
                    <div v-if="currentPreviewCard.explanation" class="card-explanation">
                      <h4>Explicação:</h4>
                      <p>{{ currentPreviewCard.explanation }}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="card-actions">
                <button class="card-action edit" @click="editCard(currentPreviewIndex)">
                  <icon-edit /> Editar
                </button>
                <button class="card-action delete" @click="deleteCard(currentPreviewIndex)">
                  <icon-trash /> Remover
                </button>
              </div>
            </div>
          </div>

          <!-- Processing Status -->
          <div v-if="isProcessing" class="processing-status">
            <div class="processing-animation">
              <div class="pulse-ring"></div>
              <div class="pulse-ring"></div>
              <div class="pulse-ring"></div>
              <icon-brain class="processing-icon" />
            </div>
            <h3>{{ processingStatus }}</h3>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: processingProgress + '%' }"></div>
            </div>
            <p class="processing-detail">{{ processingDetail }}</p>
          </div>
        </div>

        <div class="modal-footer">
          <div class="footer-info">
            <span v-if="estimatedTime" class="time-estimate">
              <icon-clock />
              Tempo estimado: {{ estimatedTime }}
            </span>
            <span v-if="generatedCards.length" class="cards-ready">
              <icon-check-circle />
              {{ generatedCards.length }} cards prontos
            </span>
          </div>
          
          <div class="footer-actions">
            <button class="btn-secondary" @click="closeModal">
              Cancelar
            </button>
            <button 
              class="btn-primary" 
              :disabled="!canGenerate"
              @click="generateFlashcards"
            >
              <icon-sparkles v-if="!isProcessing" />
              <icon-loader v-else class="spinning" />
              {{ isProcessing ? 'Gerando...' : 'Gerar Flashcards' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { mapActions } from 'vuex';
import AIFlashcardServiceUltra from '@/services/AIFlashcardServiceUltra';

export default {
  name: 'CreateDeckModal',
  props: {
    isOpen: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedInputType: 'text',
      inputTypes: [
        { id: 'text', label: 'Texto', icon: 'icon-text', badge: null },
        { id: 'pdf', label: 'PDF', icon: 'icon-file-text', badge: 'Popular' },
        { id: 'image', label: 'Imagem', icon: 'icon-image', badge: 'Novo' },
        { id: 'url', label: 'URL', icon: 'icon-globe', badge: null }
      ],
      
      // Input data
      textInput: '',
      urlInput: '',
      uploadedFile: null,
      uploadedImages: [],
      isDragging: false,
      isValidURL: false,
      isFetching: false,
      urlError: '',
      
      // Processing options
      processingOptions: {
        language: 'pt-BR',
        difficulty: 'auto',
        cardType: 'mixed',
        focus: 'comprehensive',
        includeExplanations: true,
        generateImages: false,
        addPronunciation: false,
        crossReference: true
      },
      showAdvanced: false,
      
      // Deck configuration
      deckConfig: {
        name: '',
        category: '',
        tags: []
      },
      customCategory: '',
      tagsInput: '',
      categories: [
        'Anatomia',
        'Fisiologia',
        'Farmacologia',
        'Patologia',
        'Microbiologia',
        'Bioquímica',
        'Histologia',
        'Embriologia',
        'Genética',
        'Imunologia',
        'Clínica Médica',
        'Cirurgia',
        'Pediatria',
        'Ginecologia',
        'Psiquiatria',
        'Radiologia'
      ],
      tagSuggestions: [],
      
      // Generated cards
      generatedCards: [],
      currentPreviewIndex: 0,
      isFlipped: false,
      
      // Processing state
      isProcessing: false,
      processingStatus: '',
      processingProgress: 0,
      processingDetail: '',
      estimatedTime: '',
      
      // AI Service
      aiService: null
    };
  },
  computed: {
    canGenerate() {
      const hasInput = this.textInput || this.urlInput || this.uploadedFile || this.uploadedImages.length > 0;
      const hasName = this.deckConfig.name.trim() !== '';
      return hasInput && hasName && !this.isProcessing;
    },
    currentPreviewCard() {
      return this.generatedCards[this.currentPreviewIndex] || null;
    }
  },
  created() {
    this.aiService = new AIFlashcardServiceUltra();
  },
  methods: {
    ...mapActions('flashcards', ['createDeck', 'addCardsToDeck']),
    
    closeModal() {
      if (this.isProcessing) {
        if (!confirm('A geração está em andamento. Deseja cancelar?')) {
          return;
        }
      }
      this.$emit('close');
      this.resetModal();
    },
    
    resetModal() {
      this.selectedInputType = 'text';
      this.textInput = '';
      this.urlInput = '';
      this.uploadedFile = null;
      this.uploadedImages = [];
      this.generatedCards = [];
      this.currentPreviewIndex = 0;
      this.isFlipped = false;
      this.isProcessing = false;
      this.processingProgress = 0;
      this.deckConfig = {
        name: '',
        category: '',
        tags: []
      };
    },
    
    async analyzeContent() {
      if (this.textInput.length > 100) {
        // Analyze content for smart suggestions
        const analysis = await this.aiService.analyzeContent(this.textInput);
        if (analysis.suggestedTags) {
          this.tagSuggestions = analysis.suggestedTags;
        }
        if (analysis.estimatedCards) {
          this.estimatedTime = this.calculateEstimatedTime(analysis.estimatedCards);
        }
      }
    },
    
    async pasteFromClipboard() {
      try {
        const text = await navigator.clipboard.readText();
        this.textInput = text;
        this.analyzeContent();
      } catch (error) {
        console.error('Failed to read clipboard:', error);
      }
    },
    
    clearText() {
      this.textInput = '';
      this.tagSuggestions = [];
      this.estimatedTime = '';
    },
    
    handlePDFDrop(event) {
      this.isDragging = false;
      const file = event.dataTransfer.files[0];
      if (file && file.type === 'application/pdf') {
        this.processP>DFFile(file);
      }
    },
    
    handlePDFUpload(event) {
      const file = event.target.files[0];
      if (file) {
        this.processPDFFile(file);
      }
    },
    
    async processPDFFile(file) {
      if (file.size > 50 * 1024 * 1024) {
        alert('O arquivo PDF deve ter no máximo 50MB');
        return;
      }
      
      this.uploadedFile = file;
      this.isProcessing = true;
      this.processingStatus = 'Extraindo texto do PDF...';
      
      try {
        const extractedText = await this.aiService.extractTextFromPDF(file);
        this.textInput = extractedText;
        await this.analyzeContent();
        this.isProcessing = false;
      } catch (error) {
        console.error('Error processing PDF:', error);
        this.isProcessing = false;
        alert('Erro ao processar PDF. Tente novamente.');
      }
    },
    
    removeFile() {
      this.uploadedFile = null;
      this.textInput = '';
    },
    
    handleImageDrop(event) {
      this.isDragging = false;
      const files = Array.from(event.dataTransfer.files);
      this.processImageFiles(files);
    },
    
    handleImageUpload(event) {
      const files = Array.from(event.target.files);
      this.processImageFiles(files);
    },
    
    async processImageFiles(files) {
      const validFiles = files.filter(file => {
        const isImage = file.type.startsWith('image/');
        const isValidSize = file.size <= 10 * 1024 * 1024;
        return isImage && isValidSize;
      });
      
      for (const file of validFiles) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.uploadedImages.push({
            file,
            name: file.name,
            preview: e.target.result
          });
        };
        reader.readAsDataURL(file);
      }
    },
    
    removeImage(index) {
      this.uploadedImages.splice(index, 1);
    },
    
    validateURL() {
      try {
        new URL(this.urlInput);
        this.isValidURL = true;
        this.urlError = '';
      } catch {
        this.isValidURL = false;
      }
    },
    
    async fetchURLContent() {
      if (!this.isValidURL) return;
      
      this.isFetching = true;
      this.urlError = '';
      
      try {
        const content = await this.aiService.extractContentFromURL(this.urlInput);
        this.textInput = content;
        await this.analyzeContent();
      } catch (error) {
        this.urlError = 'Erro ao buscar conteúdo. Verifique a URL e tente novamente.';
        console.error('Error fetching URL:', error);
      } finally {
        this.isFetching = false;
      }
    },
    
    addTag() {
      const tag = this.tagsInput.trim();
      if (tag && !this.deckConfig.tags.includes(tag)) {
        this.deckConfig.tags.push(tag);
        this.tagsInput = '';
      }
    },
    
    addSuggestedTag(tag) {
      if (!this.deckConfig.tags.includes(tag)) {
        this.deckConfig.tags.push(tag);
      }
    },
    
    removeTag(index) {
      this.deckConfig.tags.splice(index, 1);
    },
    
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    calculateEstimatedTime(cardCount) {
      const baseTime = 2; // seconds per card
      const totalSeconds = cardCount * baseTime;
      if (totalSeconds < 60) return `${totalSeconds} segundos`;
      if (totalSeconds < 3600) return `${Math.ceil(totalSeconds / 60)} minutos`;
      return `${Math.ceil(totalSeconds / 3600)} horas`;
    },
    
    previousCard() {
      if (this.currentPreviewIndex > 0) {
        this.currentPreviewIndex--;
        this.isFlipped = false;
      }
    },
    
    nextCard() {
      if (this.currentPreviewIndex < this.generatedCards.length - 1) {
        this.currentPreviewIndex++;
        this.isFlipped = false;
      }
    },
    
    editCard(index) {
      // Open card editor
      console.log('Edit card:', index);
    },
    
    deleteCard(index) {
      this.generatedCards.splice(index, 1);
      if (this.currentPreviewIndex >= this.generatedCards.length) {
        this.currentPreviewIndex = Math.max(0, this.generatedCards.length - 1);
      }
    },
    
    getDifficultyLabel(difficulty) {
      const labels = {
        1: 'Muito Fácil',
        2: 'Fácil',
        3: 'Médio',
        4: 'Difícil',
        5: 'Muito Difícil'
      };
      return labels[difficulty] || 'Médio';
    },
    
    getQualityClass(score) {
      if (score >= 8) return 'high';
      if (score >= 6) return 'medium';
      return 'low';
    },
    
    async generateFlashcards() {
      this.isProcessing = true;
      this.processingProgress = 0;
      
      try {
        // Prepare input data
        const inputData = {
          type: this.selectedInputType,
          content: this.textInput,
          url: this.urlInput,
          images: this.uploadedImages.map(img => img.file),
          pdf: this.uploadedFile
        };
        
        // Update processing status
        this.processingStatus = 'Analisando conteúdo com IA...';
        this.processingDetail = 'Identificando conceitos principais';
        this.processingProgress = 10;
        
        // Generate cards with AI
        const generationOptions = {
          ...this.processingOptions,
          deckName: this.deckConfig.name,
          category: this.deckConfig.category === 'custom' ? this.customCategory : this.deckConfig.category,
          tags: this.deckConfig.tags,
          onProgress: (status, progress, detail) => {
            this.processingStatus = status;
            this.processingProgress = progress;
            this.processingDetail = detail;
          }
        };
        
        const cards = await this.aiService.generateFlashcards(inputData, generationOptions);
        
        this.generatedCards = cards;
        this.processingStatus = 'Finalizando...';
        this.processingProgress = 95;
        
        // Create deck and add cards
        const deckData = {
          name: this.deckConfig.name,
          category: this.deckConfig.category === 'custom' ? this.customCategory : this.deckConfig.category,
          tags: this.deckConfig.tags,
          description: `Criado automaticamente com IA a partir de ${this.selectedInputType}`,
          cards: this.generatedCards
        };
        
        await this.createDeck(deckData);
        
        this.processingProgress = 100;
        this.processingStatus = 'Concluído!';
        
        setTimeout(() => {
          this.$emit('success', {
            deckName: this.deckConfig.name,
            cardCount: this.generatedCards.length
          });
          this.closeModal();
        }, 1500);
        
      } catch (error) {
        console.error('Error generating flashcards:', error);
        this.isProcessing = false;
        alert('Erro ao gerar flashcards. Tente novamente.');
      }
    }
  }
};
</script>

<style scoped>
/* Modal Base */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-container {
  background: #0f0f0f;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1.5rem;
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.icon {
  width: 2rem;
  height: 2rem;
}

.close-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #a1a1aa;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  transform: scale(1.05);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

/* Input Type Selector */
.input-type-selector {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.input-type {
  position: relative;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.input-type:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.input-type.active {
  background: rgba(139, 92, 246, 0.1);
  border-color: #8b5cf6;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.2);
}

.type-icon {
  width: 2rem;
  height: 2rem;
  color: #8b5cf6;
}

.type-label {
  font-weight: 600;
  color: #fff;
}

.type-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem 0.5rem;
  background: #8b5cf6;
  color: #fff;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 0.375rem;
}

/* Input Areas */
.input-area {
  margin-bottom: 2rem;
}

/* Text Input */
.text-input-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.text-input {
  width: 100%;
  min-height: 200px;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  color: #fff;
  font-size: 1rem;
  line-height: 1.6;
  resize: vertical;
  transition: all 0.3s ease;
}

.text-input:focus {
  outline: none;
  border-color: #8b5cf6;
  background: rgba(255, 255, 255, 0.05);
}

.input-actions {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  color: #a1a1aa;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  transform: translateY(-1px);
}

/* Upload Areas */
.upload-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.upload-area {
  padding: 3rem;
  background: rgba(255, 255, 255, 0.03);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover,
.upload-area.dragging {
  background: rgba(139, 92, 246, 0.05);
  border-color: #8b5cf6;
}

.upload-icon {
  width: 3rem;
  height: 3rem;
  color: #8b5cf6;
  margin-bottom: 1rem;
}

.upload-area h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #fff;
}

.upload-area p {
  color: #a1a1aa;
  margin-bottom: 0.5rem;
}

.link-btn {
  color: #8b5cf6;
  background: none;
  border: none;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
}

.file-limit {
  font-size: 0.875rem;
  color: #71717a;
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
}

.file-icon {
  width: 2rem;
  height: 2rem;
  color: #8b5cf6;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.file-name {
  font-weight: 600;
  color: #fff;
}

.file-size {
  font-size: 0.875rem;
  color: #a1a1aa;
}

.remove-file,
.remove-image {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.5rem;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #ef4444;
}

.remove-file:hover,
.remove-image:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.05);
}

/* Image Preview */
.image-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.image-preview {
  position: relative;
  aspect-ratio: 1;
  border-radius: 0.75rem;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem;
}

/* URL Input */
.url-input-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.url-input-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 0.25rem 0.25rem 0.25rem 1rem;
  transition: all 0.3s ease;
}

.url-input-wrapper:focus-within {
  border-color: #8b5cf6;
  background: rgba(255, 255, 255, 0.05);
}

.url-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #8b5cf6;
  margin-right: 0.75rem;
}

.url-input {
  flex: 1;
  background: none;
  border: none;
  color: #fff;
  font-size: 1rem;
  outline: none;
}

.fetch-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #8b5cf6;
  color: #fff;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fetch-btn:hover:not(:disabled) {
  background: #7c3aed;
  transform: translateY(-1px);
}

.fetch-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.5rem;
  color: #ef4444;
  font-size: 0.875rem;
}

/* AI Options */
.ai-options {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 1.5rem;
}

.option-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.option-card {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #a1a1aa;
}

.option-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #8b5cf6;
}

.option-select {
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-select:focus {
  outline: none;
  border-color: #8b5cf6;
  background: rgba(255, 255, 255, 0.05);
}

/* Advanced Options */
.advanced-options {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  overflow: hidden;
}

.toggle-advanced {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-advanced:hover {
  background: rgba(255, 255, 255, 0.05);
}

.toggle-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #8b5cf6;
}

.chevron {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.3s ease;
}

.chevron.rotated {
  transform: rotate(180deg);
}

.advanced-panel {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.toggle-option {
  margin-bottom: 1rem;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
}

.toggle-label input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  accent-color: #8b5cf6;
}

.toggle-text {
  color: #a1a1aa;
}

/* Deck Configuration */
.deck-config {
  margin-bottom: 2rem;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.config-field {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.config-field.full-width {
  grid-column: span 2;
}

.field-label {
  font-weight: 500;
  color: #a1a1aa;
}

.field-input,
.field-select {
  padding: 0.875rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  color: #fff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.field-input:focus,
.field-select:focus {
  outline: none;
  border-color: #8b5cf6;
  background: rgba(255, 255, 255, 0.05);
}

.category-input {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.field-select {
  cursor: pointer;
}

.custom-category {
  flex: 1;
}

.tags-input-wrapper {
  position: relative;
}

.tag-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #1a1a1a;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 0.5rem;
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  z-index: 10;
}

.tag-suggestion {
  padding: 0.5rem 0.75rem;
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 0.5rem;
  color: #8b5cf6;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tag-suggestion:hover {
  background: rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
}

.tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 0.5rem;
  color: #8b5cf6;
  font-size: 0.875rem;
  font-weight: 500;
}

.remove-tag {
  background: none;
  border: none;
  color: #8b5cf6;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.remove-tag:hover {
  color: #ef4444;
}

/* Preview Section */
.preview-section {
  margin-bottom: 2rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.card-count {
  font-size: 1rem;
  font-weight: 400;
  color: #a1a1aa;
}

.preview-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.preview-nav {
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  color: #a1a1aa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preview-nav:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.preview-nav:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.preview-counter {
  font-weight: 600;
  color: #fff;
}

/* Card Preview */
.card-preview {
  perspective: 1000px;
  margin-bottom: 1.5rem;
}

.preview-card {
  position: relative;
  width: 100%;
  max-width: 600px;
  height: 350px;
  margin: 0 auto;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  cursor: pointer;
}

.preview-card.flipped {
  transform: rotateY(180deg);
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-back {
  transform: rotateY(180deg);
}

.card-content {
  flex: 1;
  overflow-y: auto;
  font-size: 1.125rem;
  line-height: 1.6;
  color: #fff;
}

.card-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.card-metadata {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.difficulty-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.difficulty-badge.difficulty-1 {
  background: rgba(74, 222, 128, 0.1);
  color: #4ade80;
  border: 1px solid rgba(74, 222, 128, 0.2);
}

.difficulty-badge.difficulty-2 {
  background: rgba(52, 211, 153, 0.1);
  color: #34d399;
  border: 1px solid rgba(52, 211, 153, 0.2);
}

.difficulty-badge.difficulty-3 {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.difficulty-badge.difficulty-4 {
  background: rgba(251, 146, 60, 0.1);
  color: #fb923c;
  border: 1px solid rgba(251, 146, 60, 0.2);
}

.difficulty-badge.difficulty-5 {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.quality-score {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.quality-score.quality-high {
  background: rgba(74, 222, 128, 0.1);
  color: #4ade80;
  border: 1px solid rgba(74, 222, 128, 0.2);
}

.quality-score.quality-medium {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.quality-score.quality-low {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.card-explanation {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(139, 92, 246, 0.05);
  border: 1px solid rgba(139, 92, 246, 0.1);
  border-radius: 0.5rem;
}

.card-explanation h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #8b5cf6;
  margin-bottom: 0.5rem;
}

.card-explanation p {
  font-size: 0.875rem;
  color: #a1a1aa;
  line-height: 1.5;
}

.card-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.card-action {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  color: #a1a1aa;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card-action:hover {
  transform: translateY(-1px);
}

.card-action.edit:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.card-action.delete:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* Processing Status */
.processing-status {
  text-align: center;
  padding: 3rem;
}

.processing-animation {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border: 3px solid #8b5cf6;
  border-radius: 50%;
  animation: pulse 2s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

.pulse-ring:nth-child(2) {
  animation-delay: 0.5s;
}

.pulse-ring:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

.processing-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 3rem;
  height: 3rem;
  color: #8b5cf6;
}

.processing-status h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 1rem;
}

.progress-bar {
  width: 100%;
  max-width: 400px;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  margin: 0 auto 1rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #8b5cf6 0%, #3b82f6 100%);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.processing-detail {
  font-size: 0.875rem;
  color: #a1a1aa;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.time-estimate,
.cards-ready {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #a1a1aa;
}

.cards-ready {
  color: #4ade80;
}

.footer-actions {
  display: flex;
  gap: 1rem;
}

.btn-secondary {
  padding: 0.875rem 1.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  color: #a1a1aa;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  border: none;
  border-radius: 0.75rem;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 24px rgba(139, 92, 246, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.4);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active .modal-container,
.modal-fade-leave-active .modal-container {
  transition: transform 0.3s ease;
}

.modal-fade-enter-from .modal-container {
  transform: scale(0.9);
}

.modal-fade-leave-to .modal-container {
  transform: scale(0.9);
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
  opacity: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .modal-container {
    max-width: 90vw;
  }
  
  .input-type-selector {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .option-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .config-field.full-width {
    grid-column: span 1;
  }
}

@media (max-width: 640px) {
  .modal-overlay {
    padding: 0;
  }
  
  .modal-container {
    max-width: 100%;
    max-height: 100vh;
    border-radius: 0;
  }
  
  .modal-title {
    font-size: 1.5rem;
  }
  
  .input-type-selector {
    grid-template-columns: 1fr;
  }
  
  .option-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-card {
    height: 300px;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 1rem;
  }
  
  .footer-info {
    width: 100%;
    justify-content: center;
  }
  
  .footer-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>