import axios from 'axios';
import * as pdfjs from 'pdfjs-dist';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

class AIFlashcardServiceUltra {
  constructor() {
    this.claudeApiKey = process.env.VUE_APP_CLAUDE_API_KEY || '';
    this.claudeApiUrl = 'https://api.anthropic.com/v1/messages';
    this.backendUrl = process.env.VUE_APP_API_URL || 'http://localhost:8000/api';
    
    // Initialize services
    this.nlpProcessor = new MedicalNLPProcessor();
    this.imageAnalyzer = new ImageAnalyzer();
    this.voiceSynthesizer = new VoiceSynthesizer();
    this.qualityScorer = new QualityScorer();
  }

  /**
   * Analyze content to provide insights before generation
   */
  async analyzeContent(content) {
    const analysis = {
      estimatedCards: 0,
      suggestedTags: [],
      detectedLanguage: 'pt-BR',
      complexity: 'medium',
      topics: []
    };

    // Estimate card count based on content length and structure
    const sentences = content.match(/[.!?]+/g) || [];
    const paragraphs = content.split(/\n\n+/);
    const words = content.split(/\s+/).length;
    
    analysis.estimatedCards = Math.min(
      Math.max(5, Math.floor(sentences.length / 3)),
      Math.floor(words / 50)
    );

    // Extract key topics using NLP
    const topics = await this.nlpProcessor.extractKeyTopics(content);
    analysis.topics = topics;
    analysis.suggestedTags = topics.slice(0, 5);

    // Detect complexity
    const medicalTerms = await this.nlpProcessor.extractMedicalTerms(content);
    if (medicalTerms.length > 20) {
      analysis.complexity = 'high';
    } else if (medicalTerms.length < 5) {
      analysis.complexity = 'low';
    }

    return analysis;
  }

  /**
   * Generate flashcards from various input types
   */
  async generateFlashcards(inputData, options) {
    const { type, content, url, images, pdf } = inputData;
    const { onProgress } = options;

    let processedContent = '';
    let extractedImages = [];

    // Process input based on type
    switch (type) {
      case 'text':
        processedContent = content;
        break;
        
      case 'url':
        onProgress?.('Extraindo conteúdo da URL...', 10, 'Conectando ao site');
        processedContent = await this.extractContentFromURL(url);
        break;
        
      case 'pdf':
        onProgress?.('Processando PDF...', 10, 'Extraindo texto e imagens');
        const pdfData = await this.extractFromPDF(pdf);
        processedContent = pdfData.text;
        extractedImages = pdfData.images;
        break;
        
      case 'image':
        onProgress?.('Analisando imagens...', 10, 'Aplicando OCR e reconhecimento');
        const imageData = await this.processImages(images);
        processedContent = imageData.text;
        extractedImages = imageData.processedImages;
        break;
    }

    // Enhance content with medical NLP
    onProgress?.('Processando com NLP médico...', 30, 'Identificando termos técnicos');
    const enhancedContent = await this.nlpProcessor.enhanceContent(processedContent);

    // Generate cards using Claude API
    onProgress?.('Gerando flashcards com IA...', 50, 'Claude está criando cards otimizados');
    const cards = await this.generateCardsWithClaude(enhancedContent, options);

    // Add medical pronunciations if requested
    if (options.addPronunciation) {
      onProgress?.('Adicionando pronúncias...', 70, 'Gerando áudio para termos médicos');
      cards = await this.addPronunciations(cards);
    }

    // Generate AI images if requested
    if (options.generateImages) {
      onProgress?.('Gerando imagens com IA...', 80, 'Criando diagramas e ilustrações');
      cards = await this.generateAIImages(cards);
    }

    // Score card quality
    onProgress?.('Avaliando qualidade...', 90, 'Calculando scores de qualidade');
    cards = await this.scoreCardQuality(cards);

    // Create cross-references if requested
    if (options.crossReference) {
      onProgress?.('Criando referências cruzadas...', 95, 'Conectando conceitos relacionados');
      cards = this.createCrossReferences(cards);
    }

    return cards;
  }

  /**
   * Generate cards using Claude API
   */
  async generateCardsWithClaude(content, options) {
    const { language, difficulty, cardType, focus, includeExplanations } = options;
    
    const systemPrompt = `Você é um especialista em educação médica criando flashcards de alta qualidade.
    
Diretrizes:
- Crie flashcards ${cardType} focados em ${focus}
- Nível de dificuldade: ${difficulty}
- Idioma: ${language}
- ${includeExplanations ? 'Inclua explicações detalhadas' : 'Seja conciso'}
- Use terminologia médica precisa
- Garanta que cada card teste um conceito específico
- Para cards de caso clínico, inclua contexto realista

Formato de resposta JSON:
{
  "cards": [
    {
      "front": "Pergunta ou prompt",
      "back": "Resposta",
      "type": "basic|cloze|image|clinical",
      "difficulty": 1-5,
      "tags": ["tag1", "tag2"],
      "explanation": "Explicação opcional",
      "relatedConcepts": ["conceito1", "conceito2"],
      "clinicalPearl": "Dica clínica opcional"
    }
  ]
}`;

    try {
      const response = await axios.post(
        this.claudeApiUrl,
        {
          model: 'claude-3-opus-20240229',
          max_tokens: 4000,
          temperature: 0.7,
          system: systemPrompt,
          messages: [
            {
              role: 'user',
              content: `Crie flashcards de alta qualidade a partir do seguinte conteúdo:\n\n${content}`
            }
          ]
        },
        {
          headers: {
            'x-api-key': this.claudeApiKey,
            'anthropic-version': '2023-06-01',
            'content-type': 'application/json'
          }
        }
      );

      const generatedContent = response.data.content[0].text;
      const parsedResponse = JSON.parse(generatedContent);
      
      return parsedResponse.cards.map(card => ({
        ...card,
        id: this.generateId(),
        createdAt: new Date().toISOString(),
        easeFactor: 2.5,
        interval: 1,
        repetitions: 0,
        nextReview: new Date().toISOString(),
        lastReview: null,
        successRate: 0,
        qualityScore: 0
      }));
    } catch (error) {
      console.error('Error generating cards with Claude:', error);
      throw new Error('Falha ao gerar flashcards com IA');
    }
  }

  /**
   * Extract text from PDF
   */
  async extractTextFromPDF(file) {
    const arrayBuffer = await file.arrayBuffer();
    const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;
    let fullText = '';
    const images = [];

    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      const pageText = textContent.items.map(item => item.str).join(' ');
      fullText += pageText + '\n\n';

      // Extract images
      const ops = await page.getOperatorList();
      for (let j = 0; j < ops.fnArray.length; j++) {
        if (ops.fnArray[j] === pdfjs.OPS.paintJpegXObject || 
            ops.fnArray[j] === pdfjs.OPS.paintImageXObject) {
          // Image extraction logic
          images.push({
            pageNumber: i,
            imageIndex: j
          });
        }
      }
    }

    return { text: fullText, images };
  }

  /**
   * Extract content from URL
   */
  async extractContentFromURL(url) {
    try {
      const response = await axios.post(`${this.backendUrl}/extract-url`, { url });
      return response.data.content;
    } catch (error) {
      // Fallback to client-side extraction
      const response = await axios.get(`https://api.allorigins.win/get?url=${encodeURIComponent(url)}`);
      const html = response.data.contents;
      
      // Simple HTML to text conversion
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      
      // Remove scripts and styles
      const scripts = tempDiv.getElementsByTagName('script');
      const styles = tempDiv.getElementsByTagName('style');
      
      for (let i = scripts.length - 1; i >= 0; i--) {
        scripts[i].remove();
      }
      for (let i = styles.length - 1; i >= 0; i--) {
        styles[i].remove();
      }
      
      return tempDiv.textContent || tempDiv.innerText || '';
    }
  }

  /**
   * Process images with OCR and analysis
   */
  async processImages(images) {
    const processedImages = [];
    let extractedText = '';

    for (const image of images) {
      // Convert to base64
      const base64 = await this.fileToBase64(image.file);
      
      // Analyze with Claude Vision
      const analysis = await this.analyzeImageWithClaude(base64);
      
      processedImages.push({
        original: image,
        analysis: analysis,
        base64: base64
      });
      
      extractedText += analysis.text + '\n\n';
    }

    return { text: extractedText, processedImages };
  }

  /**
   * Analyze image with Claude Vision
   */
  async analyzeImageWithClaude(base64Image) {
    try {
      const response = await axios.post(
        this.claudeApiUrl,
        {
          model: 'claude-3-opus-20240229',
          max_tokens: 1000,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'image',
                  source: {
                    type: 'base64',
                    media_type: 'image/jpeg',
                    data: base64Image.split(',')[1]
                  }
                },
                {
                  type: 'text',
                  text: 'Analise esta imagem médica/educacional. Extraia todo o texto, identifique diagramas, estruturas anatômicas, ou conceitos médicos importantes. Formate a resposta em JSON com campos: text, concepts, structures, clinical_relevance.'
                }
              ]
            }
          ]
        },
        {
          headers: {
            'x-api-key': this.claudeApiKey,
            'anthropic-version': '2023-06-01',
            'content-type': 'application/json'
          }
        }
      );

      const analysis = JSON.parse(response.data.content[0].text);
      return analysis;
    } catch (error) {
      console.error('Error analyzing image:', error);
      return { text: '', concepts: [], structures: [], clinical_relevance: '' };
    }
  }

  /**
   * Add pronunciation audio to cards
   */
  async addPronunciations(cards) {
    const enhancedCards = [];

    for (const card of cards) {
      const medicalTerms = await this.nlpProcessor.extractMedicalTerms(card.front + ' ' + card.back);
      
      if (medicalTerms.length > 0) {
        const pronunciations = await this.voiceSynthesizer.generatePronunciations(medicalTerms);
        card.pronunciations = pronunciations;
      }
      
      enhancedCards.push(card);
    }

    return enhancedCards;
  }

  /**
   * Generate AI images for cards
   */
  async generateAIImages(cards) {
    const enhancedCards = [];

    for (const card of cards) {
      if (card.type === 'image' || this.shouldHaveImage(card)) {
        try {
          // Generate image prompt
          const imagePrompt = await this.createImagePrompt(card);
          
          // Generate image using DALL-E or similar service
          const imageUrl = await this.imageAnalyzer.generateImage(imagePrompt);
          
          if (card.type === 'image') {
            card.frontImage = imageUrl;
          } else {
            card.backImage = imageUrl;
          }
        } catch (error) {
          console.error('Error generating image:', error);
        }
      }
      
      enhancedCards.push(card);
    }

    return enhancedCards;
  }

  /**
   * Score card quality
   */
  async scoreCardQuality(cards) {
    return cards.map(card => {
      const score = this.qualityScorer.calculateScore(card);
      return { ...card, qualityScore: score };
    });
  }

  /**
   * Create cross-references between related cards
   */
  createCrossReferences(cards) {
    const enhancedCards = [...cards];

    for (let i = 0; i < enhancedCards.length; i++) {
      const relatedIndices = [];
      
      for (let j = 0; j < enhancedCards.length; j++) {
        if (i !== j) {
          const similarity = this.calculateSimilarity(enhancedCards[i], enhancedCards[j]);
          if (similarity > 0.7) {
            relatedIndices.push(j);
          }
        }
      }
      
      enhancedCards[i].relatedCards = relatedIndices.slice(0, 3);
    }

    return enhancedCards;
  }

  /**
   * Utility functions
   */
  generateId() {
    return `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  }

  shouldHaveImage(card) {
    const imageKeywords = ['anatomia', 'estrutura', 'diagrama', 'sistema', 'órgão', 'músculo', 'osso'];
    const content = (card.front + ' ' + card.back).toLowerCase();
    return imageKeywords.some(keyword => content.includes(keyword));
  }

  createImagePrompt(card) {
    return `Medical educational diagram: ${card.front}. Style: Clean, professional medical illustration with labels. High contrast, suitable for study.`;
  }

  calculateSimilarity(card1, card2) {
    // Simple similarity calculation based on shared tags and concepts
    const tags1 = new Set(card1.tags || []);
    const tags2 = new Set(card2.tags || []);
    const concepts1 = new Set(card1.relatedConcepts || []);
    const concepts2 = new Set(card2.relatedConcepts || []);
    
    const sharedTags = [...tags1].filter(tag => tags2.has(tag)).length;
    const sharedConcepts = [...concepts1].filter(concept => concepts2.has(concept)).length;
    
    const totalTags = tags1.size + tags2.size;
    const totalConcepts = concepts1.size + concepts2.size;
    
    if (totalTags === 0 && totalConcepts === 0) return 0;
    
    return (sharedTags * 2 + sharedConcepts * 2) / (totalTags + totalConcepts);
  }
}

/**
 * Medical NLP Processor
 */
class MedicalNLPProcessor {
  constructor() {
    this.medicalTermsDatabase = new Set([
      'anatomia', 'fisiologia', 'patologia', 'farmacologia',
      'diagnóstico', 'tratamento', 'sintoma', 'síndrome',
      'cardiovascular', 'respiratório', 'neurológico', 'endócrino',
      'inflamação', 'infecção', 'neoplasia', 'trauma',
      'antibiótico', 'analgésico', 'anti-inflamatório',
      'hemograma', 'radiografia', 'tomografia', 'ressonância'
    ]);
  }

  async extractKeyTopics(text) {
    const words = text.toLowerCase().split(/\s+/);
    const wordFreq = {};
    
    // Count word frequency
    words.forEach(word => {
      if (word.length > 4 && !this.isStopWord(word)) {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });
    
    // Sort by frequency and filter medical terms
    const topics = Object.entries(wordFreq)
      .sort((a, b) => b[1] - a[1])
      .filter(([word]) => this.isMedicalTerm(word))
      .slice(0, 10)
      .map(([word]) => word);
    
    return topics;
  }

  async extractMedicalTerms(text) {
    const words = text.toLowerCase().split(/\s+/);
    const medicalTerms = [];
    
    words.forEach(word => {
      if (this.medicalTermsDatabase.has(word) || this.isMedicalTerm(word)) {
        medicalTerms.push(word);
      }
    });
    
    return [...new Set(medicalTerms)];
  }

  async enhanceContent(content) {
    // Add medical context and structure
    const enhanced = content
      .replace(/(\b\w+ite\b)/gi, match => `${match} (inflamação)`)
      .replace(/(\b\w+oma\b)/gi, match => `${match} (tumor)`)
      .replace(/(\b\w+ose\b)/gi, match => `${match} (condição)`)
      .replace(/(\b\w+emia\b)/gi, match => `${match} (no sangue)`);
    
    return enhanced;
  }

  isStopWord(word) {
    const stopWords = ['o', 'a', 'de', 'da', 'do', 'em', 'um', 'uma', 'para', 'com', 'não', 'que', 'e', 'os', 'as'];
    return stopWords.includes(word);
  }

  isMedicalTerm(word) {
    // Check for medical suffixes and prefixes
    const medicalPatterns = [
      /.*ite$/,    // inflammation
      /.*oma$/,    // tumor
      /.*ose$/,    // condition
      /.*emia$/,   // blood condition
      /^hemo.*/,   // blood-related
      /^cardio.*/, // heart-related
      /^neuro.*/,  // nerve-related
      /^gastro.*/, // stomach-related
      /.*logia$/,  // study of
      /.*grafia$/, // imaging
    ];
    
    return medicalPatterns.some(pattern => pattern.test(word)) || 
           this.medicalTermsDatabase.has(word);
  }
}

/**
 * Image Analyzer
 */
class ImageAnalyzer {
  async generateImage(prompt) {
    // Placeholder for DALL-E or similar API integration
    // For now, return a placeholder
    return `https://via.placeholder.com/400x300/8b5cf6/ffffff?text=${encodeURIComponent(prompt.slice(0, 30))}`;
  }
}

/**
 * Voice Synthesizer
 */
class VoiceSynthesizer {
  async generatePronunciations(terms) {
    const pronunciations = {};
    
    for (const term of terms) {
      // Use Web Speech API or external service
      pronunciations[term] = {
        phonetic: this.getPhonetic(term),
        audioUrl: await this.generateAudio(term)
      };
    }
    
    return pronunciations;
  }

  getPhonetic(term) {
    // Simple phonetic representation
    const phoneticMap = {
      'anatomia': 'a-na-to-MI-a',
      'fisiologia': 'fi-zi-o-lo-GI-a',
      'patologia': 'pa-to-lo-GI-a',
      'farmacologia': 'far-ma-co-lo-GI-a'
    };
    
    return phoneticMap[term] || term;
  }

  async generateAudio(term) {
    // Placeholder for TTS API
    return `data:audio/mp3;base64,${btoa(term)}`;
  }
}

/**
 * Quality Scorer
 */
class QualityScorer {
  calculateScore(card) {
    let score = 5; // Base score
    
    // Length criteria
    if (card.front.length > 20 && card.front.length < 200) score += 1;
    if (card.back.length > 20 && card.back.length < 300) score += 1;
    
    // Has explanation
    if (card.explanation) score += 1;
    
    // Has clinical relevance
    if (card.clinicalPearl) score += 1;
    
    // Has proper difficulty
    if (card.difficulty >= 2 && card.difficulty <= 4) score += 1;
    
    return Math.min(10, score);
  }
}

export default AIFlashcardServiceUltra;