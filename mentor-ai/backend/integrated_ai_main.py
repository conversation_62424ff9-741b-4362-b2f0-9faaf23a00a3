"""
Mentor AI - Integrated FastAPI with all AI Services
"""
import os
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
from dotenv import load_dotenv

load_dotenv()

# Import database
from fastapi_app.db.database import engine, Base

# Import routers
from fastapi_app.routers import flashcards, second_brain, mentorship, chat
try:
    from fastapi_app.routers.thanos import router as thanos_router
except ImportError:
    try:
        from fastapi_app.routers.thanos_simple import router as thanos_router
    except ImportError:
        thanos_router = None
# from fastapi_app.routers.quest_ai import router as quest_ai_router  # Removido

# Create tables
Base.metadata.create_all(bind=engine)

# Create app
app = FastAPI(
    title="Mentor AI - Complete AI System",
    description="Integrated AI Learning Platform with Second Brain, Thanos, and more",
    version="3.0.0"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
uploads_dir = Path(__file__).parent / "uploads"
uploads_dir.mkdir(exist_ok=True)
app.mount("/uploads", StaticFiles(directory=str(uploads_dir)), name="uploads")

# Include all routers
app.include_router(flashcards.router, prefix="/api", tags=["flashcards"])
app.include_router(second_brain.router, prefix="/api", tags=["second-brain"])
app.include_router(mentorship.router, prefix="/api", tags=["mentorship"])
if thanos_router:
    app.include_router(thanos_router, prefix="/api/thanos", tags=["thanos"])
app.include_router(chat.router, tags=["chat"])  # Chat router for Second Brain UI
# app.include_router(quest_ai_router, tags=["quest-ai"])  # QuestAI router - Removido

@app.get("/")
async def root():
    return {
        "name": "Mentor AI API",
        "version": "3.0.0",
        "status": "running",
        "ai_services": {
            "second_brain": "active",
            "thanos": "active",
            "flashcards": "active",
            "mentorship": "active"
        }
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "services": {
            "database": "connected",
            "ai": "ready",
            "cache": "active"
        }
    }

@app.get("/test-stream")
async def test_stream():
    """Test SSE stream endpoint"""
    from fastapi.responses import StreamingResponse
    import asyncio
    
    async def generate():
        messages = [
            "data: Conexão estabelecida com sucesso!\n\n",
            "data: Sistema Second Brain está funcionando corretamente.\n\n",
            "data: Pronto para receber suas perguntas médicas.\n\n",
            "event: end\ndata: \n\n"
        ]
        for msg in messages:
            yield msg
            await asyncio.sleep(0.5)
    
    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8001))
    uvicorn.run("integrated_ai_main:app", host="0.0.0.0", port=port, reload=True)