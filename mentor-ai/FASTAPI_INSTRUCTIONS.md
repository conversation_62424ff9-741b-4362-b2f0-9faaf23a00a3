# FastAPI - Instruções de Execução

## Estrutura Esperada

```
mentor-ai/
├── backend/
│   ├── fastapi_app/
│   │   ├── main.py          # Arquivo principal do FastAPI
│   │   ├── requirements.txt # Dependências do FastAPI
│   │   ├── routers/         # Rotas da <PERSON>
│   │   │   ├── analytics.py
│   │   │   ├── flashcards.py
│   │   │   └── performance.py
│   │   └── models/          # Modelos de dados
│   └── logs/                # Diretório de logs
```

## Executar com o Script MENTOR_AI_ULTRA_SYSTEM.sh

### 1. Dar permissão de execução ao script
```bash
chmod +x MENTOR_AI_ULTRA_SYSTEM.sh
```

### 2. Executar o script
```bash
./MENTOR_AI_ULTRA_SYSTEM.sh
```

### 3. Escolher uma das opções:
- **Opção 3**: Corrigir FastAPI (rápido) - Inicia o FastAPI rapidamente
- **Opção 4**: Corrigir <PERSON> (limpeza completa) - Reinstala dependências e inicia
- **Opção 6**: Iniciar apenas o FastAPI

## Executar Manualmente

### 1. Navegar para o diretório FastAPI
```bash
cd backend/fastapi_app
```

### 2. Instalar dependências (primeira vez)
```bash
pip install -r requirements.txt
```

### 3. Configurar variáveis de ambiente
```bash
export PYTHONPATH="../:$PYTHONPATH"
export DATABASE_URL="postgresql://localhost/mentor_ai"
```

### 4. Executar o FastAPI
```bash
uvicorn main:app --host 0.0.0.0 --port 8001 --reload
```

## Endpoints Disponíveis

Após iniciar o FastAPI, você pode acessar:

- **Documentação Interativa**: http://localhost:8001/docs
- **Health Check**: http://localhost:8001/health
- **Flashcards API**: http://localhost:8001/api/flashcards
- **Analytics API**: http://localhost:8001/api/analytics
- **Performance API**: http://localhost:8001/api/performance

## Verificar se está funcionando

```bash
curl http://localhost:8001/health
```

Resposta esperada:
```json
{"status": "healthy", "service": "mentor-ai-fastapi"}
```

## Logs

### Ver logs em tempo real:
```bash
tail -f logs/fastapi.log
```

### Ver últimas 50 linhas:
```bash
tail -n 50 logs/fastapi.log
```

## Troubleshooting

### Porta 8001 em uso
```bash
# Verificar o que está usando a porta
lsof -i:8001

# Matar o processo
kill -9 <PID>
```

### Erro de importação de módulos
```bash
# Garantir que o PYTHONPATH está correto
export PYTHONPATH="/caminho/para/mentor-ai/backend:$PYTHONPATH"
```

### Erro de conexão com banco de dados
```bash
# Verificar se PostgreSQL está rodando
pg_isready -h localhost

# Iniciar PostgreSQL (macOS)
brew services start postgresql@14
```

## Dependências Principais

O FastAPI precisa das seguintes bibliotecas principais:

- **fastapi**: Framework web
- **uvicorn**: Servidor ASGI
- **pydantic**: Validação de dados
- **sqlalchemy**: ORM para banco de dados
- **psycopg2-binary**: Driver PostgreSQL
- **python-jose**: JWT tokens
- **passlib**: Hashing de senhas
- **python-multipart**: Upload de arquivos

## Desenvolvimento

### Adicionar nova rota

1. Criar arquivo em `routers/`:
```python
# routers/exemplo.py
from fastapi import APIRouter

router = APIRouter()

@router.get("/exemplo")
async def exemplo():
    return {"message": "Exemplo funcionando"}
```

2. Registrar no `main.py`:
```python
from routers import exemplo

app.include_router(exemplo.router, prefix="/api", tags=["exemplo"])
```

### Hot Reload

O FastAPI reinicia automaticamente quando você salva alterações se executado com `--reload`:
```bash
uvicorn main:app --reload
```

## Integração com Frontend

O Frontend Vue.js em http://localhost:8082 se conecta automaticamente ao FastAPI em http://localhost:8001.

Certifique-se de que o CORS está configurado no `main.py`:
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8082"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```