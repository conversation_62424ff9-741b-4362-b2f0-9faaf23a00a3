#!/bin/bash

# MENTOR AI - Complete AI Integration Startup Script
# Starts all services with full AI capabilities

set -e

echo "🚀 STARTING MENTOR AI WITH FULL AI INTEGRATION 🚀"
echo "================================================"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Base directory
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$BASE_DIR"

# Create necessary directories
mkdir -p logs .pids uploads documents cache embeddings_cache

# Function to wait for service
wait_for_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=0
    
    echo -e "${YELLOW}Waiting for $name to start...${NC}"
    while [ $attempt -lt $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $name is ready!${NC}"
            return 0
        fi
        sleep 2
        attempt=$((attempt + 1))
    done
    echo -e "${RED}❌ $name failed to start${NC}"
    return 1
}

echo -e "${BLUE}1. Checking PostgreSQL...${NC}"
if pg_ctl status > /dev/null 2>&1 || brew services list | grep -q "postgresql.*started"; then
    echo -e "${GREEN}✅ PostgreSQL is running${NC}"
else
    echo -e "${YELLOW}Starting PostgreSQL...${NC}"
    brew services start postgresql@14
    sleep 3
fi

echo -e "${BLUE}2. Setting up environment variables...${NC}"
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/mentor_ai_db"
export DJANGO_SETTINGS_MODULE="django_app.settings"
export PYTHONPATH="$BASE_DIR/backend:$PYTHONPATH"

# Update backend .env
cat > backend/.env << EOF
DATABASE_URL=$DATABASE_URL
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=${OPENAI_API_KEY:-your_openai_key_here}
ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-your_anthropic_key_here}
GROQ_API_KEY=${GROQ_API_KEY:-your_groq_key_here}
PORT=8001
HOST=0.0.0.0
FRONTEND_URL=http://localhost:8082
EOF

echo -e "${BLUE}3. Starting Django Backend...${NC}"
cd "$BASE_DIR/backend/django_app"
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi
source venv/bin/activate
pip install -q django djangorestframework django-cors-headers psycopg2-binary python-dotenv

# Run migrations
python manage.py migrate --noinput > "$BASE_DIR/logs/django_migrate.log" 2>&1

# Start Django
nohup python manage.py runserver 8000 > "$BASE_DIR/logs/django.log" 2>&1 &
echo $! > "$BASE_DIR/.pids/django.pid"
echo -e "${GREEN}✅ Django started on port 8000${NC}"

echo -e "${BLUE}4. Starting FastAPI with AI Services...${NC}"
cd "$BASE_DIR/backend"

# Create integrated main.py with all AI services
cat > integrated_ai_main.py << 'EOF'
"""
Mentor AI - Integrated FastAPI with all AI Services
"""
import os
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
from dotenv import load_dotenv

load_dotenv()

# Import routers
from fastapi_app.routers import flashcards, second_brain, mentorship
from fastapi_app.routers.thanos import router as thanos_router

# Create app
app = FastAPI(
    title="Mentor AI - Complete AI System",
    description="Integrated AI Learning Platform with Second Brain, Thanos, and more",
    version="3.0.0"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Include all routers
app.include_router(flashcards.router, prefix="/api", tags=["flashcards"])
app.include_router(second_brain.router, prefix="/api", tags=["second-brain"])
app.include_router(mentorship.router, prefix="/api", tags=["mentorship"])
app.include_router(thanos_router, prefix="/api/thanos", tags=["thanos"])

@app.get("/")
async def root():
    return {
        "name": "Mentor AI API",
        "version": "3.0.0",
        "status": "running",
        "ai_services": {
            "second_brain": "active",
            "thanos": "active",
            "flashcards": "active",
            "mentorship": "active"
        }
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "services": {
            "database": "connected",
            "ai": "ready",
            "cache": "active"
        }
    }

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8001))
    uvicorn.run("integrated_ai_main:app", host="0.0.0.0", port=port, reload=True)
EOF

# Install dependencies
source venv/bin/activate
pip install -q fastapi uvicorn python-multipart sqlalchemy psycopg2-binary \
    langchain langchain-community anthropic openai redis aiofiles \
    python-jose passlib bcrypt email-validator httpx pydantic pydantic-settings

# Start FastAPI
nohup python integrated_ai_main.py > "$BASE_DIR/logs/fastapi.log" 2>&1 &
echo $! > "$BASE_DIR/.pids/fastapi.pid"
echo -e "${GREEN}✅ FastAPI with AI services started on port 8001${NC}"

echo -e "${BLUE}5. Starting Frontend...${NC}"
cd "$BASE_DIR/frontend"

# Start frontend
nohup npm run serve > "$BASE_DIR/logs/frontend.log" 2>&1 &
echo $! > "$BASE_DIR/.pids/frontend.pid"
echo -e "${GREEN}✅ Frontend started on port 8082${NC}"

# Wait for services
echo -e "${BLUE}6. Verifying services...${NC}"
sleep 5

wait_for_service "http://localhost:8000/admin/" "Django"
wait_for_service "http://localhost:8001/health" "FastAPI"
wait_for_service "http://localhost:8082" "Frontend"

echo -e "${GREEN}================================================${NC}"
echo -e "${GREEN}✅ ALL SERVICES STARTED SUCCESSFULLY!${NC}"
echo -e "${GREEN}================================================${NC}"
echo
echo -e "${BLUE}Access Points:${NC}"
echo -e "  • Frontend: ${GREEN}http://localhost:8082${NC}"
echo -e "  • Django Admin: ${GREEN}http://localhost:8000/admin${NC}"
echo -e "  • API Docs: ${GREEN}http://localhost:8001/docs${NC}"
echo
echo -e "${BLUE}AI Services Available:${NC}"
echo -e "  • Second Brain Chat: ${GREEN}http://localhost:8082/second-brain${NC}"
echo -e "  • Thanos AI: ${GREEN}http://localhost:8082/thanos${NC}"
echo -e "  • Flashcards System: ${GREEN}http://localhost:8082/flashcards${NC}"
echo
echo -e "${YELLOW}To stop all services: ./stop_ultra.sh${NC}"
echo

# Test AI endpoints
echo -e "${BLUE}7. Testing AI endpoints...${NC}"
sleep 2

# Test Second Brain
echo -n "Testing Second Brain: "
if curl -s http://localhost:8001/api/second-brain/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Working${NC}"
else
    echo -e "${RED}❌ Not responding${NC}"
fi

# Test Thanos
echo -n "Testing Thanos AI: "
if curl -s http://localhost:8001/api/thanos/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Working${NC}"
else
    echo -e "${RED}❌ Not responding${NC}"
fi

echo
echo -e "${GREEN}🎉 MENTOR AI IS READY TO USE! 🎉${NC}"