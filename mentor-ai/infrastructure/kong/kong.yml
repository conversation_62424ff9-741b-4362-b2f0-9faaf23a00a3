_format_version: "3.0"

services:
  # Flashcards Service
  - name: flashcards-service
    url: http://flashcards-service:8001
    protocol: http
    port: 8001
    path: /
    retries: 5
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    tags:
      - flashcards
      - microservice

  # AI Service
  - name: ai-service
    url: http://ai-service:8002
    protocol: http
    port: 8002
    path: /
    retries: 3
    connect_timeout: 120000
    write_timeout: 120000
    read_timeout: 120000
    tags:
      - ai
      - microservice

  # User Service
  - name: user-service
    url: http://user-service:8003
    protocol: http
    port: 8003
    path: /
    retries: 5
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    tags:
      - user
      - microservice

  # Analytics Service
  - name: analytics-service
    url: http://analytics-service:8004
    protocol: http
    port: 8004
    path: /
    retries: 5
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    tags:
      - analytics
      - microservice

  # Content Service
  - name: content-service
    url: http://content-service:8005
    protocol: http
    port: 8005
    path: /
    retries: 5
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    tags:
      - content
      - microservice

routes:
  # Flashcards routes
  - name: flashcards-routes
    service: flashcards-service
    paths:
      - /api/flashcards
    strip_path: false
    preserve_host: true
    regex_priority: 0
    tags:
      - flashcards

  # AI routes
  - name: ai-routes
    service: ai-service
    paths:
      - /api/ai
    strip_path: false
    preserve_host: true
    regex_priority: 0
    tags:
      - ai

  # User routes
  - name: user-routes
    service: user-service
    paths:
      - /api/users
      - /api/auth
    strip_path: false
    preserve_host: true
    regex_priority: 0
    tags:
      - user

  # Analytics routes
  - name: analytics-routes
    service: analytics-service
    paths:
      - /api/analytics
    strip_path: false
    preserve_host: true
    regex_priority: 0
    tags:
      - analytics

  # Content routes
  - name: content-routes
    service: content-service
    paths:
      - /api/content
    strip_path: false
    preserve_host: true
    regex_priority: 0
    tags:
      - content

plugins:
  # Global plugins
  - name: cors
    config:
      origins:
        - "*"
      methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
        - PATCH
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
      exposed_headers:
        - X-Auth-Token
      credentials: true
      max_age: 3600
      preflight_continue: false

  # Rate limiting
  - name: rate-limiting
    config:
      minute: 60
      hour: 1000
      day: 10000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
      redis_ssl: false
      redis_ssl_verify: false

  # Request size limiting
  - name: request-size-limiting
    config:
      allowed_payload_size: 10
      size_unit: megabytes
      require_content_length: false

  # Authentication
  - name: jwt
    config:
      uri_param_names:
        - jwt
      cookie_names:
        - token
      header_names:
        - authorization
      claims_to_verify:
        - exp
        - nbf
      key_claim_name: iss
      secret_is_base64: false
      anonymous: null
      run_on_preflight: true
      maximum_expiration: 31536000

  # Request ID
  - name: correlation-id
    config:
      header_name: X-Request-ID
      generator: uuid
      echo_downstream: true

  # Logging
  - name: http-log
    config:
      http_endpoint: http://logstash:5000
      method: POST
      timeout: 10000
      keepalive: 60000
      flush_timeout: 2
      retry_count: 10
      queue_size: 1000
      content_type: application/json
      
  # Monitoring
  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true

  # Circuit breaker
  - name: circuit-breaker
    config:
      error_threshold_percentage: 50
      min_calls_in_error_state: 20
      window_time: 60
      api_call_timeout_ms: 5000
      failure_rate_threshold: 50
      recovery_timeout: 60
      excluded_status_codes:
        - 499

  # Response transformation
  - name: response-transformer
    config:
      add:
        headers:
          X-Service-Name: mentor-ai
          X-Service-Version: "2.0.0"
      remove:
        headers:
          - Server
          - X-Powered-By

# Service-specific plugins
consumers:
  - username: web-app
    custom_id: web-app-001
    tags:
      - web
      - frontend

  - username: mobile-app
    custom_id: mobile-app-001
    tags:
      - mobile
      - frontend

  - username: admin
    custom_id: admin-001
    tags:
      - admin
      - internal

# ACLs
acls:
  - consumer: web-app
    group: users
    tags:
      - user-access

  - consumer: mobile-app
    group: users
    tags:
      - user-access

  - consumer: admin
    group: admins
    tags:
      - admin-access

# Upstreams for load balancing
upstreams:
  - name: flashcards-upstream
    algorithm: round-robin
    slots: 10000
    healthchecks:
      active:
        healthy:
          interval: 5
          successes: 2
        unhealthy:
          interval: 5
          tcp_failures: 2
          http_failures: 2
        http_path: /health
        timeout: 3
        type: http
      passive:
        healthy:
          successes: 2
        unhealthy:
          tcp_failures: 2
          http_failures: 2
    tags:
      - flashcards

  - name: ai-upstream
    algorithm: least-connections
    slots: 10000
    healthchecks:
      active:
        healthy:
          interval: 10
          successes: 2
        unhealthy:
          interval: 10
          tcp_failures: 2
          http_failures: 2
        http_path: /health
        timeout: 5
        type: http
      passive:
        healthy:
          successes: 2
        unhealthy:
          tcp_failures: 2
          http_failures: 2
    tags:
      - ai

# Targets for upstreams
targets:
  # Flashcards service instances
  - upstream: flashcards-upstream
    target: flashcards-service-1:8001
    weight: 100
    tags:
      - production
      - primary

  - upstream: flashcards-upstream
    target: flashcards-service-2:8001
    weight: 100
    tags:
      - production
      - secondary

  # AI service instances
  - upstream: ai-upstream
    target: ai-service-1:8002
    weight: 100
    tags:
      - production
      - gpu-enabled

  - upstream: ai-upstream
    target: ai-service-2:8002
    weight: 50
    tags:
      - production
      - cpu-only