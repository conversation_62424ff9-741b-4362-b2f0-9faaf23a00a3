global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'mentor-ai'
    env: 'production'

scrape_configs:
  # Kong metrics
  - job_name: 'kong'
    static_configs:
      - targets: ['kong:8001']
    metrics_path: /metrics

  # Microservices
  - job_name: 'flashcards-service'
    static_configs:
      - targets: ['flashcards-service:8001']
    metrics_path: /metrics

  - job_name: 'ai-service'
    static_configs:
      - targets: ['ai-service:8002']
    metrics_path: /metrics

  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:8003']
    metrics_path: /metrics

  - job_name: 'analytics-service'
    static_configs:
      - targets: ['analytics-service:8004']
    metrics_path: /metrics

  # Databases
  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb-exporter:9216']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Kafka
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka-exporter:9308']

  # Node exporters
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

# Alerting configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']

# Rules files
rule_files:
  - '/etc/prometheus/alerts/*.yml'