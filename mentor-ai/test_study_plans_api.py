#!/usr/bin/env python3
"""
Test script for Study Plans API
Tests the consolidated study plans functionality
"""

import requests
import json
from datetime import date, datetime

# API Configuration
BASE_URL = "http://localhost:8001"
API_URL = f"{BASE_URL}/api/study-plans"

def test_api_health():
    """Test if the API is running"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ API is running")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure the backend is running.")
        return False

def test_create_study_plan():
    """Test creating a new study plan"""
    plan_data = {
        "name": "Anatomia Humana - Teste",
        "description": "Plano de estudos para anatomia humana com foco no sistema musculoesquelético",
        "category": "medicina",
        "difficulty": "medio",
        "start_date": str(date.today()),
        "end_date": str(date(2024, 12, 31)),
        "daily_hours": 2.5,
        "total_hours": 60,
        "icon": "fas fa-bone",
        "color": "linear-gradient(135deg, #667eea, #764ba2)",
        "objectives": [
            {"text": "Estudar sistema ósseo"},
            {"text": "Compreender articulações"},
            {"text": "Memorizar músculos principais"}
        ],
        "notifications": {
            "enabled": True,
            "daily": True,
            "deadline": True,
            "progress": False
        }
    }
    
    try:
        response = requests.post(API_URL, json=plan_data)
        if response.status_code == 200:
            plan = response.json()
            print(f"✅ Study plan created successfully: {plan['name']} (ID: {plan['id']})")
            return plan['id']
        else:
            print(f"❌ Failed to create study plan: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error creating study plan: {e}")
        return None

def test_get_study_plans():
    """Test getting all study plans"""
    try:
        response = requests.get(API_URL)
        if response.status_code == 200:
            plans = response.json()
            print(f"✅ Retrieved {len(plans)} study plans")
            for plan in plans:
                print(f"   - {plan['name']} ({plan['progress']}% complete)")
            return plans
        else:
            print(f"❌ Failed to get study plans: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error getting study plans: {e}")
        return []

def test_create_study_session(plan_id):
    """Test creating a study session"""
    if not plan_id:
        print("❌ No plan ID provided for session test")
        return None
        
    session_data = {
        "duration_minutes": 45,
        "notes": "Estudei o sistema ósseo - muito produtivo!"
    }
    
    try:
        response = requests.post(f"{API_URL}/{plan_id}/sessions", json=session_data)
        if response.status_code == 200:
            session = response.json()
            print(f"✅ Study session created: {session['duration_minutes']} minutes")
            return session['id']
        else:
            print(f"❌ Failed to create study session: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error creating study session: {e}")
        return None

def test_get_stats():
    """Test getting study statistics"""
    try:
        response = requests.get(f"{API_URL}/stats/overview")
        if response.status_code == 200:
            stats = response.json()
            print("✅ Study statistics retrieved:")
            print(f"   - Total plans: {stats['total_plans']}")
            print(f"   - Active plans: {stats['active_plans']}")
            print(f"   - Total hours studied: {stats['total_hours_studied']}")
            print(f"   - Total sessions: {stats['total_sessions']}")
            return stats
        else:
            print(f"❌ Failed to get study stats: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error getting study stats: {e}")
        return None

def main():
    """Run all tests"""
    print("🧪 Testing Study Plans API")
    print("=" * 50)
    
    # Test API health
    if not test_api_health():
        print("\n❌ API is not available. Please start the backend server:")
        print("   cd mentor-ai/backend/fastapi_app")
        print("   python main.py")
        return
    
    print("\n📝 Testing Study Plan Creation...")
    plan_id = test_create_study_plan()
    
    print("\n📋 Testing Study Plans Retrieval...")
    plans = test_get_study_plans()
    
    print("\n⏱️ Testing Study Session Creation...")
    session_id = test_create_study_session(plan_id)
    
    print("\n📊 Testing Statistics...")
    stats = test_get_stats()
    
    print("\n" + "=" * 50)
    if plan_id and session_id and stats:
        print("✅ All tests passed! Study Plans API is working correctly.")
    else:
        print("❌ Some tests failed. Check the output above for details.")
    
    print("\n🌐 API Documentation available at:")
    print(f"   {BASE_URL}/docs")

if __name__ == "__main__":
    main()
