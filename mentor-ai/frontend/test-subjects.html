<!DOCTYPE html>
<html>
<head>
    <title>Teste Subjects Store</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vuex@4/dist/vuex.global.js"></script>
</head>
<body>
    <div id="app">
        <h1>Teste do Store de Subjects</h1>
        
        <div>
            <h2>Disciplinas Atuais:</h2>
            <ul>
                <li v-for="subject in subjects" :key="subject.id">
                    {{ subject.name }} - <span :style="{ color: subject.color }">{{ subject.color }}</span>
                </li>
            </ul>
        </div>
        
        <div>
            <h2>Adicionar Nova Disciplina:</h2>
            <input v-model="newSubjectName" placeholder="Nome da disciplina">
            <input v-model="newSubjectColor" type="color">
            <button @click="addSubject">Adicionar</button>
        </div>
        
        <div>
            <h2>Console Log:</h2>
            <pre>{{ logs }}</pre>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { createStore } = Vuex;
        
        // Módulo subjects simplificado
        const subjectsModule = {
            namespaced: true,
            state: {
                subjects: []
            },
            getters: {
                allSubjects: state => state.subjects
            },
            mutations: {
                SET_SUBJECTS(state, subjects) {
                    console.log('SET_SUBJECTS:', subjects);
                    state.subjects = subjects;
                },
                ADD_SUBJECT(state, subject) {
                    console.log('ADD_SUBJECT:', subject);
                    state.subjects.push(subject);
                }
            },
            actions: {
                fetchSubjects({ commit, state }) {
                    if (state.subjects.length === 0) {
                        const mockSubjects = [
                            { id: '1', name: 'Anatomia', color: '#e63946' },
                            { id: '2', name: 'Fisiologia', color: '#457b9d' }
                        ];
                        commit('SET_SUBJECTS', mockSubjects);
                    }
                    return Promise.resolve(state.subjects);
                },
                addSubject({ commit }, subject) {
                    commit('ADD_SUBJECT', subject);
                    return Promise.resolve(subject);
                }
            }
        };
        
        // Criar store
        const store = createStore({
            modules: {
                subjects: subjectsModule
            }
        });
        
        // Criar app
        createApp({
            data() {
                return {
                    newSubjectName: '',
                    newSubjectColor: '#6366f1',
                    logs: ''
                };
            },
            computed: {
                subjects() {
                    return this.$store.getters['subjects/allSubjects'];
                }
            },
            methods: {
                addSubject() {
                    if (!this.newSubjectName.trim()) return;
                    
                    const subject = {
                        id: Date.now().toString(),
                        name: this.newSubjectName,
                        color: this.newSubjectColor
                    };
                    
                    this.logs += `\nAdicionando: ${JSON.stringify(subject)}`;
                    
                    this.$store.dispatch('subjects/addSubject', subject).then(() => {
                        this.logs += `\nSucesso! Total: ${this.subjects.length}`;
                        this.newSubjectName = '';
                        this.newSubjectColor = '#6366f1';
                    });
                }
            },
            mounted() {
                this.$store.dispatch('subjects/fetchSubjects');
                this.logs = 'App iniciado';
            }
        }).use(store).mount('#app');
    </script>
</body>
</html>