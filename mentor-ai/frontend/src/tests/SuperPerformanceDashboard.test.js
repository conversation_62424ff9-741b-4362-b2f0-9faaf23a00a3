import { mount, shallowMount } from '@vue/test-utils'
import { createStore } from 'vuex'
import SuperPerformanceDashboard from '@/components/SuperPerformanceDashboard.vue'
import { nextTick } from 'vue'

// Mock Chart.js
jest.mock('chart.js/auto', () => ({
  default: jest.fn().mockImplementation(() => ({
    destroy: jest.fn(),
    update: jest.fn()
  }))
}))

// Mock date-fns
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => '01/01/2024'),
  differenceInDays: jest.fn(() => 5),
  addDays: jest.fn((date, days) => new Date()),
  startOfMonth: jest.fn(() => new Date()),
  endOfMonth: jest.fn(() => new Date()),
  ptBR: {}
}))

describe('SuperPerformanceDashboard.vue', () => {
  let wrapper
  let store
  let mockState
  let mockGetters
  let mockActions

  beforeEach(() => {
    // Mock do estado Vuex
    mockState = {
      performanceMetrics: {
        totalStudyTime: 1200,
        totalCards: 500,
        masteredCards: 350,
        averageAccuracy: 85,
        currentStreak: 15,
        longestStreak: 30,
        lastStudyDate: '2024-01-01'
      },
      retentionRates: {
        shortTerm: 80,
        mediumTerm: 75,
        longTerm: 70,
        overall: 75
      },
      categoryAnalytics: [
        {
          id: '1',
          name: 'Anatomia',
          accuracy: 85,
          totalCards: 150,
          avgTime: 12,
          weeklyProgress: [70, 75, 80, 85]
        },
        {
          id: '2',
          name: 'Fisiologia',
          accuracy: 55,
          totalCards: 100,
          avgTime: 15,
          weeklyProgress: [50, 52, 54, 55]
        }
      ],
      studySessions: [],
      reviewHistory: [],
      userRanking: {
        position: 5,
        total: 100,
        percentile: 95,
        badges: []
      },
      studyGoals: [
        {
          id: '1',
          title: 'Dominar Anatomia',
          description: 'Completar todos os cards de Anatomia',
          type: 'cards',
          current: 120,
          target: 150,
          deadline: '2024-02-01',
          icon: 'bullseye',
          color: '#6366f1',
          unit: 'cards'
        }
      ],
      achievements: [
        {
          id: '1',
          name: 'Primeira Semana',
          description: 'Complete 7 dias consecutivos',
          icon: 'calendar-week',
          unlocked: true,
          unlockedDate: '2024-01-15',
          rarity: 'common',
          points: 10
        }
      ],
      aiInsights: [
        {
          id: '1',
          type: 'success',
          icon: 'lightbulb',
          title: 'Excelente progresso!',
          description: 'Sua taxa de retenção melhorou 15% esta semana',
          impact: 4,
          actions: [
            { id: '1', label: 'Ver detalhes' }
          ]
        }
      ],
      recommendations: [],
      chartData: {
        progress: [70, 75, 80, 85, 82, 87, 90],
        retention: [],
        speed: [],
        categories: []
      },
      loading: {
        metrics: false,
        insights: false,
        charts: false
      }
    }

    mockGetters = {
      masteryLevel: () => 70,
      studyTimeFormatted: () => '20h 0m',
      performanceTrend: () => 'improving',
      weakCategories: () => [mockState.categoryAnalytics[1]],
      activeGoals: () => mockState.studyGoals,
      recentAchievements: () => mockState.achievements
    }

    mockActions = {
      fetchAnalytics: jest.fn(),
      fetchCategoryAnalytics: jest.fn(),
      fetchReviewHistory: jest.fn(),
      fetchGoals: jest.fn(),
      fetchAchievements: jest.fn(),
      generateInsights: jest.fn(),
      exportReport: jest.fn(),
      createGoal: jest.fn()
    }

    store = createStore({
      modules: {
        performance: {
          namespaced: true,
          state: mockState,
          getters: mockGetters,
          actions: mockActions
        },
        auth: {
          namespaced: true,
          state: {
            user: { id: 'test-user' }
          }
        }
      }
    })

    wrapper = shallowMount(SuperPerformanceDashboard, {
      global: {
        plugins: [store],
        stubs: {
          'font-awesome-icon': true,
          teleport: true
        }
      }
    })
  })

  afterEach(() => {
    wrapper.unmount()
    jest.clearAllMocks()
  })

  describe('Renderização Inicial', () => {
    it('deve renderizar o componente corretamente', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.super-performance-dashboard').exists()).toBe(true)
    })

    it('deve exibir o header com título e subtítulo', () => {
      expect(wrapper.find('.header-title').exists()).toBe(true)
      expect(wrapper.find('.header-title').text()).toContain('Central de Performance')
      expect(wrapper.find('.header-subtitle').exists()).toBe(true)
    })

    it('deve renderizar os botões de período', () => {
      const periodButtons = wrapper.findAll('.period-btn')
      expect(periodButtons).toHaveLength(4)
      expect(periodButtons[0].text()).toContain('Hoje')
      expect(periodButtons[1].text()).toContain('Semana')
      expect(periodButtons[2].text()).toContain('Mês')
      expect(periodButtons[3].text()).toContain('Ano')
    })

    it('deve renderizar os botões de ação rápida', () => {
      expect(wrapper.find('.action-btn.refresh').exists()).toBe(true)
      expect(wrapper.find('.action-btn.export').exists()).toBe(true)
      expect(wrapper.find('.action-btn.settings').exists()).toBe(true)
    })
  })

  describe('Métricas Principais', () => {
    it('deve exibir todas as métricas principais', () => {
      const metrics = wrapper.vm.keyMetrics
      expect(metrics).toHaveLength(6)
      
      const metricCards = wrapper.findAll('.metric-card')
      expect(metricCards).toHaveLength(6)
    })

    it('deve formatar corretamente os valores das métricas', () => {
      const masteryMetric = wrapper.vm.keyMetrics.find(m => m.id === 'mastery')
      expect(masteryMetric.value).toBe(70)
      expect(masteryMetric.unit).toBe('%')
      
      const streakMetric = wrapper.vm.keyMetrics.find(m => m.id === 'streak')
      expect(streakMetric.value).toBe(15)
      expect(streakMetric.unit).toBe(' dias')
    })

    it('deve calcular tendências corretamente', () => {
      const trend = wrapper.vm.calculateTrend('mastery')
      expect(typeof trend).toBe('number')
    })

    it('deve gerar dados de sparkline', () => {
      const sparklineData = wrapper.vm.getSparklineData('mastery')
      expect(Array.isArray(sparklineData)).toBe(true)
      expect(sparklineData).toHaveLength(12)
    })
  })

  describe('Seleção de Período', () => {
    it('deve alterar o período quando um botão é clicado', async () => {
      const weekButton = wrapper.findAll('.period-btn')[1]
      await weekButton.trigger('click')
      
      expect(wrapper.vm.selectedPeriod).toBe('week')
      expect(mockActions.fetchAnalytics).toHaveBeenCalled()
    })

    it('deve marcar o período selecionado como ativo', async () => {
      wrapper.vm.selectedPeriod = 'month'
      await nextTick()
      
      const monthButton = wrapper.findAll('.period-btn')[2]
      expect(monthButton.classes()).toContain('active')
    })
  })

  describe('Insights de IA', () => {
    it('deve exibir insights corretamente', () => {
      const insightCards = wrapper.findAll('.insight-card')
      expect(insightCards).toHaveLength(1)
      
      const firstInsight = insightCards[0]
      expect(firstInsight.text()).toContain('Excelente progresso!')
    })

    it('deve chamar generateInsights ao clicar no botão', async () => {
      const generateBtn = wrapper.find('.generate-btn')
      await generateBtn.trigger('click')
      
      expect(mockActions.generateInsights).toHaveBeenCalled()
    })

    it('deve exibir ações dos insights quando disponíveis', () => {
      const actionButtons = wrapper.findAll('.insight-action-btn')
      expect(actionButtons).toHaveLength(1)
      expect(actionButtons[0].text()).toBe('Ver detalhes')
    })

    it('deve exibir o impacto dos insights com estrelas', () => {
      const impactStars = wrapper.findAll('.impact-stars .fa-star')
      expect(impactStars).toHaveLength(5)
    })
  })

  describe('Metas e Conquistas', () => {
    it('deve exibir metas ativas', () => {
      const goalCards = wrapper.findAll('.goal-card')
      expect(goalCards).toHaveLength(1)
      
      const firstGoal = goalCards[0]
      expect(firstGoal.text()).toContain('Dominar Anatomia')
    })

    it('deve calcular progresso das metas corretamente', () => {
      const goal = wrapper.vm.activeGoals[0]
      const progressPercentage = (goal.current / goal.target) * 100
      expect(progressPercentage).toBe(80)
    })

    it('deve identificar metas próximas do prazo', () => {
      const goal = {
        deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000) // 2 dias
      }
      expect(wrapper.vm.isNearDeadline(goal)).toBe(true)
    })

    it('deve exibir conquistas desbloqueadas', () => {
      const achievementCards = wrapper.findAll('.achievement-card')
      expect(achievementCards).toHaveLength(1)
      expect(achievementCards[0].text()).toContain('Primeira Semana')
    })

    it('deve calcular pontos totais de conquistas', () => {
      expect(wrapper.vm.totalAchievementPoints).toBe(10)
    })
  })

  describe('Performance por Categoria', () => {
    it('deve exibir todas as categorias', () => {
      const categoryCards = wrapper.findAll('.category-card')
      expect(categoryCards).toHaveLength(2)
    })

    it('deve adicionar ícones apropriados às categorias', () => {
      const categories = wrapper.vm.categoryPerformance
      expect(categories[0].icon).toBe('bone') // Anatomia
      expect(categories[1].icon).toBe('heartbeat') // Fisiologia
    })

    it('deve gerar recomendações baseadas na performance', () => {
      const categories = wrapper.vm.categoryPerformance
      expect(categories[0].recommendation).toContain('Excelente desempenho')
      expect(categories[1].recommendation).toContain('Reforce os conceitos básicos')
    })
  })

  describe('Modais', () => {
    describe('Modal de Exportação', () => {
      it('deve abrir o modal ao clicar no botão exportar', async () => {
        const exportBtn = wrapper.find('.action-btn.export')
        await exportBtn.trigger('click')
        
        expect(wrapper.vm.showExportModal).toBe(true)
      })

      it('deve definir datas padrão ao abrir', async () => {
        await wrapper.vm.openExportModal()
        
        expect(wrapper.vm.exportStartDate).toBeTruthy()
        expect(wrapper.vm.exportEndDate).toBeTruthy()
      })

      it('deve chamar exportReport com os parâmetros corretos', async () => {
        wrapper.vm.exportFormat = 'pdf'
        wrapper.vm.exportSections = {
          metrics: true,
          charts: true,
          insights: false,
          goals: false
        }
        
        await wrapper.vm.exportReport()
        
        expect(mockActions.exportReport).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            format: 'pdf',
            sections: wrapper.vm.exportSections
          })
        )
      })
    })

    describe('Modal de Metas', () => {
      it('deve abrir o modal ao clicar no botão nova meta', async () => {
        const addGoalBtn = wrapper.find('.add-goal-btn')
        await addGoalBtn.trigger('click')
        
        expect(wrapper.vm.showGoalsModal).toBe(true)
      })

      it('deve resetar o formulário ao fechar', async () => {
        wrapper.vm.newGoal.title = 'Test Goal'
        wrapper.vm.closeGoalsModal()
        
        await nextTick()
        expect(wrapper.vm.newGoal.title).toBe('')
      })

      it('deve criar meta com dados válidos', async () => {
        wrapper.vm.newGoal = {
          title: 'Nova Meta',
          description: 'Descrição da meta',
          type: 'cards',
          target: 100,
          deadline: '2024-12-31',
          icon: 'trophy',
          color: '#f59e0b'
        }
        
        await wrapper.vm.createGoal()
        
        expect(mockActions.createGoal).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            title: 'Nova Meta',
            target: 100,
            current: 0,
            unit: 'cards'
          })
        )
      })
    })

    describe('Modal de Configurações', () => {
      it('deve abrir o modal ao clicar no botão configurações', async () => {
        const settingsBtn = wrapper.find('.action-btn.settings')
        await settingsBtn.trigger('click')
        
        expect(wrapper.vm.showSettingsModal).toBe(true)
      })

      it('deve salvar configurações no localStorage', () => {
        const mockSetItem = jest.spyOn(Storage.prototype, 'setItem')
        
        wrapper.vm.settings.animations = false
        wrapper.vm.saveSettings()
        
        expect(mockSetItem).toHaveBeenCalledWith(
          'dashboardSettings',
          expect.stringContaining('"animations":false')
        )
      })

      it('deve aplicar configurações corretamente', () => {
        wrapper.vm.settings.animations = false
        wrapper.vm.applySettings()
        
        expect(document.documentElement.classList.contains('no-animations')).toBe(true)
        
        wrapper.vm.settings.compactMode = true
        wrapper.vm.applySettings()
        
        expect(document.documentElement.classList.contains('compact-mode')).toBe(true)
      })
    })
  })

  describe('Funcionalidades de Atualização', () => {
    it('deve mostrar spinner ao atualizar dados', async () => {
      const refreshBtn = wrapper.find('.action-btn.refresh')
      await refreshBtn.trigger('click')
      
      expect(wrapper.vm.isRefreshing).toBe(true)
      expect(refreshBtn.classes()).toContain('spinning')
    })

    it('deve chamar todas as ações de fetch ao atualizar', async () => {
      await wrapper.vm.refreshData()
      
      expect(mockActions.fetchAnalytics).toHaveBeenCalled()
      expect(mockActions.fetchCategoryAnalytics).toHaveBeenCalled()
      expect(mockActions.fetchReviewHistory).toHaveBeenCalled()
      expect(mockActions.fetchGoals).toHaveBeenCalled()
      expect(mockActions.fetchAchievements).toHaveBeenCalled()
    })

    it('deve exibir overlay de loading quando está carregando', async () => {
      mockState.loading.metrics = true
      await nextTick()
      
      expect(wrapper.vm.isLoading).toBe(true)
    })
  })

  describe('Utilitários e Formatação', () => {
    it('deve formatar valores de métricas corretamente', () => {
      const metric = { value: 85, unit: '%' }
      expect(wrapper.vm.formatMetricValue(metric)).toBe('85%')
      
      const timeMetric = { type: 'time', value: '2h 30m' }
      expect(wrapper.vm.formatMetricValue(timeMetric)).toBe('2h 30m')
    })

    it('deve formatar datas corretamente', () => {
      const date = '2024-01-15'
      const formatted = wrapper.vm.formatDate(date)
      expect(formatted).toBe('01/01/2024') // Mockado
    })

    it('deve formatar prazos relativos', () => {
      const deadline = new Date(Date.now() + 24 * 60 * 60 * 1000) // Amanhã
      const formatted = wrapper.vm.formatDeadline(deadline)
      expect(formatted).toMatch(/dias|Amanhã/)
    })

    it('deve gerar cores de heatmap baseadas em intensidade', () => {
      const color = wrapper.vm.getHeatmapColor(80)
      expect(color).toMatch(/hsl\(\d+, 70%, \d+%\)/)
    })
  })

  describe('Padrões de Estudo', () => {
    it('deve calcular horários de pico', () => {
      const peakHours = wrapper.vm.peakHours
      expect(peakHours).toHaveLength(24)
      expect(peakHours[0]).toHaveProperty('hour', 0)
      expect(peakHours[0]).toHaveProperty('intensity')
    })

    it('deve gerar calendário de sequências', () => {
      const calendar = wrapper.vm.streakCalendar
      expect(Array.isArray(calendar)).toBe(true)
      expect(calendar.length).toBeGreaterThan(0)
      
      const day = calendar[0]
      expect(day).toHaveProperty('date')
      expect(day).toHaveProperty('day')
      expect(day).toHaveProperty('studied')
    })
  })

  describe('Lifecycle Hooks', () => {
    it('deve carregar configurações ao montar', () => {
      const mockGetItem = jest.spyOn(Storage.prototype, 'getItem')
      const newWrapper = shallowMount(SuperPerformanceDashboard, {
        global: {
          plugins: [store],
          stubs: {
            'font-awesome-icon': true,
            teleport: true
          }
        }
      })
      
      expect(mockGetItem).toHaveBeenCalledWith('dashboardSettings')
      newWrapper.unmount()
    })

    it('deve limpar recursos ao desmontar', () => {
      const mockRemoveEventListener = jest.spyOn(window, 'removeEventListener')
      wrapper.unmount()
      
      expect(mockRemoveEventListener).toHaveBeenCalledWith('resize', expect.any(Function))
    })
  })

  describe('Tratamento de Erros', () => {
    it('deve tratar erros ao atualizar dados', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation()
      mockActions.fetchAnalytics.mockRejectedValue(new Error('Erro de rede'))
      
      await wrapper.vm.refreshData()
      
      expect(consoleError).toHaveBeenCalledWith('Erro ao atualizar dados:', expect.any(Error))
      expect(wrapper.vm.isRefreshing).toBe(false)
      
      consoleError.mockRestore()
    })

    it('deve validar dados do formulário de meta', async () => {
      wrapper.vm.newGoal.title = ''
      await wrapper.vm.createGoal()
      
      expect(mockActions.createGoal).not.toHaveBeenCalled()
    })
  })

  describe('Responsividade', () => {
    it('deve lidar com redimensionamento da janela', () => {
      const handleResize = jest.spyOn(wrapper.vm, 'handleResize')
      
      window.dispatchEvent(new Event('resize'))
      
      expect(handleResize).toHaveBeenCalled()
    })
  })
})

// Testes de Integração
describe('SuperPerformanceDashboard - Integração', () => {
  let wrapper
  let store

  beforeEach(() => {
    // Store com dados mais realistas para testes de integração
    store = createStore({
      modules: {
        performance: {
          namespaced: true,
          state: {
            performanceMetrics: {
              totalStudyTime: 2400,
              totalCards: 1000,
              masteredCards: 750,
              averageAccuracy: 92,
              currentStreak: 45,
              longestStreak: 60
            },
            categoryAnalytics: [
              {
                id: '1',
                name: 'Anatomia',
                accuracy: 95,
                totalCards: 300,
                avgTime: 10
              },
              {
                id: '2',
                name: 'Fisiologia',
                accuracy: 88,
                totalCards: 250,
                avgTime: 12
              },
              {
                id: '3',
                name: 'Farmacologia',
                accuracy: 45,
                totalCards: 200,
                avgTime: 18
              }
            ],
            studyGoals: [
              {
                id: '1',
                title: 'Meta 1',
                current: 80,
                target: 100,
                deadline: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000)
              },
              {
                id: '2',
                title: 'Meta 2',
                current: 95,
                target: 100,
                deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000)
              }
            ],
            achievements: Array(5).fill(null).map((_, i) => ({
              id: String(i),
              name: `Conquista ${i + 1}`,
              unlocked: true,
              points: (i + 1) * 10
            })),
            loading: {}
          },
          getters: {
            masteryLevel: () => 75,
            studyTimeFormatted: () => '40h 0m',
            performanceTrend: () => 'improving',
            weakCategories: (state) => state.categoryAnalytics.filter(c => c.accuracy < 60),
            activeGoals: (state) => state.studyGoals,
            recentAchievements: (state) => state.achievements
          },
          actions: {
            fetchAnalytics: jest.fn(),
            fetchCategoryAnalytics: jest.fn(),
            fetchReviewHistory: jest.fn(),
            fetchGoals: jest.fn(),
            fetchAchievements: jest.fn(),
            generateInsights: jest.fn(),
            exportReport: jest.fn(),
            createGoal: jest.fn()
          }
        },
        auth: {
          namespaced: true,
          state: {
            user: { id: 'test-user' }
          }
        }
      }
    })

    wrapper = mount(SuperPerformanceDashboard, {
      global: {
        plugins: [store],
        stubs: {
          'font-awesome-icon': true,
          teleport: true
        }
      }
    })
  })

  afterEach(() => {
    wrapper.unmount()
  })

  it('deve identificar e destacar categorias fracas', () => {
    const weakCategories = wrapper.vm.weakCategories
    expect(weakCategories).toHaveLength(1)
    expect(weakCategories[0].name).toBe('Farmacologia')
  })

  it('deve calcular corretamente o total de pontos de conquistas', () => {
    expect(wrapper.vm.totalAchievementPoints).toBe(150) // 10+20+30+40+50
  })

  it('deve identificar metas próximas do prazo', () => {
    const goals = wrapper.vm.activeGoals
    const nearDeadlineGoals = goals.filter(g => wrapper.vm.isNearDeadline(g))
    expect(nearDeadlineGoals).toHaveLength(1)
    expect(nearDeadlineGoals[0].id).toBe('2')
  })

  it('deve gerar labels de gráfico baseados no período', () => {
    wrapper.vm.selectedPeriod = 'week'
    const labels = wrapper.vm.getChartLabels()
    expect(labels).toEqual(['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'])
    
    wrapper.vm.selectedPeriod = 'today'
    const hourLabels = wrapper.vm.getChartLabels()
    expect(hourLabels).toHaveLength(24)
    expect(hourLabels[0]).toBe('0h')
    expect(hourLabels[23]).toBe('23h')
  })
})

// Testes de Performance
describe('SuperPerformanceDashboard - Performance', () => {
  it('deve renderizar grande quantidade de dados sem problemas', () => {
    const largeData = {
      categoryAnalytics: Array(50).fill(null).map((_, i) => ({
        id: String(i),
        name: `Categoria ${i}`,
        accuracy: Math.random() * 100,
        totalCards: Math.floor(Math.random() * 500),
        avgTime: Math.floor(Math.random() * 30)
      })),
      achievements: Array(100).fill(null).map((_, i) => ({
        id: String(i),
        name: `Conquista ${i}`,
        unlocked: Math.random() > 0.5,
        points: Math.floor(Math.random() * 100)
      }))
    }
    
    const store = createStore({
      modules: {
        performance: {
          namespaced: true,
          state: { ...largeData, loading: {} },
          getters: {
            recentAchievements: (state) => state.achievements.slice(0, 5)
          }
        }
      }
    })
    
    const start = performance.now()
    const wrapper = mount(SuperPerformanceDashboard, {
      global: { plugins: [store], stubs: { 'font-awesome-icon': true, teleport: true } }
    })
    const end = performance.now()
    
    expect(end - start).toBeLessThan(1000) // Deve renderizar em menos de 1 segundo
    wrapper.unmount()
  })
})