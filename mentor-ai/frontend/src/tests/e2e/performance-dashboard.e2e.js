// Teste E2E para o Dashboard de Performance
// Este arquivo contém testes de ponta a ponta para verificar o funcionamento completo
// do SuperPerformanceDashboard em um ambiente real

describe('Dashboard de Performance E2E', () => {
  beforeEach(() => {
    // Fazer login antes de cada teste
    cy.visit('/login')
    cy.get('input[type="email"]').type('<EMAIL>')
    cy.get('input[type="password"]').type('password123')
    cy.get('button[type="submit"]').click()
    
    // Navegar para o dashboard
    cy.visit('/progress-dashboard')
    cy.wait(2000) // Aguardar carregamento inicial
  })

  describe('Carregamento Inicial', () => {
    it('deve carregar a página corretamente', () => {
      cy.get('.super-performance-dashboard').should('be.visible')
      cy.get('.header-title').should('contain', 'Central de Performance')
      cy.get('.header-subtitle').should('contain', 'Análise completa e inteligente')
    })

    it('deve exibir todas as seções principais', () => {
      cy.get('.metrics-overview').should('be.visible')
      cy.get('.charts-section').should('be.visible')
      cy.get('.insights-section').should('be.visible')
      cy.get('.goals-achievements').should('be.visible')
      cy.get('.category-performance').should('be.visible')
      cy.get('.study-patterns').should('be.visible')
    })

    it('deve carregar métricas principais', () => {
      cy.get('.metric-card').should('have.length.at.least', 6)
      cy.get('.metric-card').first().within(() => {
        cy.get('.metric-value').should('not.be.empty')
        cy.get('.metric-label').should('not.be.empty')
      })
    })
  })

  describe('Interações com Período', () => {
    it('deve permitir mudar o período de análise', () => {
      // Verificar período padrão (semana)
      cy.get('.period-btn.active').should('contain', 'Semana')
      
      // Mudar para mês
      cy.get('.period-btn').contains('Mês').click()
      cy.get('.period-btn.active').should('contain', 'Mês')
      
      // Verificar se os dados foram atualizados
      cy.get('.loading-overlay').should('not.exist')
    })

    it('deve atualizar gráficos ao mudar período', () => {
      cy.get('.period-btn').contains('Hoje').click()
      cy.wait(1000)
      
      // Verificar se o gráfico principal foi atualizado
      cy.get('.main-chart').should('be.visible')
    })
  })

  describe('Métricas e Visualizações', () => {
    it('deve exibir sparklines nas métricas', () => {
      cy.get('.metric-sparkline').should('have.length.at.least', 3)
      cy.get('.sparkline-svg').first().should('be.visible')
    })

    it('deve mostrar progresso em métricas com barra', () => {
      cy.get('.metric-progress').should('exist')
      cy.get('.progress-fill').should('have.css', 'width')
    })

    it('deve exibir tendências corretamente', () => {
      cy.get('.metric-trend').should('exist')
      cy.get('.trend-up, .trend-down').should('exist')
    })
  })

  describe('Gráficos e Análises', () => {
    it('deve permitir trocar tipo de gráfico', () => {
      cy.get('.chart-selector').select('retention')
      cy.wait(500)
      cy.get('.main-chart').should('be.visible')
      
      cy.get('.chart-selector').select('categories')
      cy.wait(500)
      cy.get('.main-chart').should('be.visible')
    })

    it('deve exibir mini gráficos laterais', () => {
      cy.get('.mini-chart-card').should('have.length', 2)
      cy.get('.mini-chart-card').first().should('contain', 'Distribuição por Dificuldade')
      cy.get('.mini-chart-card').last().should('contain', 'Tempo de Resposta')
    })
  })

  describe('Insights de IA', () => {
    it('deve exibir insights gerados', () => {
      cy.get('.insight-card').should('have.length.at.least', 1)
      cy.get('.insight-title').should('not.be.empty')
      cy.get('.insight-description').should('not.be.empty')
    })

    it('deve permitir gerar novos insights', () => {
      cy.get('.generate-btn').click()
      cy.get('.loading-text').should('contain', 'Gerando insights')
      cy.wait(2000)
      cy.get('.insight-card').should('exist')
    })

    it('deve exibir ações nos insights quando disponíveis', () => {
      cy.get('.insight-card').first().within(() => {
        cy.get('.insight-action-btn').should('exist')
      })
    })

    it('deve mostrar impacto dos insights', () => {
      cy.get('.impact-stars').should('exist')
      cy.get('.impact-stars .fa-star.filled').should('have.length.at.least', 1)
    })
  })

  describe('Metas', () => {
    it('deve exibir metas ativas', () => {
      cy.get('.goal-card').should('have.length.at.least', 1)
      cy.get('.goal-title').should('not.be.empty')
      cy.get('.goal-progress').should('be.visible')
    })

    it('deve destacar metas próximas do prazo', () => {
      cy.get('.goal-card.near-deadline').should('have.css', 'border-color')
    })

    it('deve abrir modal para criar nova meta', () => {
      cy.get('.add-goal-btn').click()
      cy.get('.modal-overlay').should('be.visible')
      cy.get('.modal-header').should('contain', 'Criar Nova Meta')
    })

    it('deve criar nova meta com sucesso', () => {
      cy.get('.add-goal-btn').click()
      
      // Preencher formulário
      cy.get('input[placeholder*="Dominar"]').type('Nova Meta de Teste')
      cy.get('textarea').type('Descrição da meta de teste')
      cy.get('select').first().select('accuracy')
      cy.get('input[type="number"]').type('95')
      cy.get('input[type="date"]').type('2024-12-31')
      
      // Selecionar ícone e cor
      cy.get('.option-item').eq(2).click()
      
      // Submeter
      cy.get('.modal-footer .btn-primary').click()
      
      // Verificar se foi adicionada
      cy.get('.goal-card').should('contain', 'Nova Meta de Teste')
    })
  })

  describe('Conquistas', () => {
    it('deve exibir conquistas desbloqueadas', () => {
      cy.get('.achievement-card').should('have.length.at.least', 1)
      cy.get('.achievement-name').should('not.be.empty')
      cy.get('.achievement-points').should('exist')
    })

    it('deve mostrar total de pontos', () => {
      cy.get('.total-points').should('contain', 'pts')
      cy.get('.total-points').invoke('text').then((text) => {
        const points = parseInt(text)
        expect(points).to.be.greaterThan(0)
      })
    })

    it('deve aplicar classes de raridade', () => {
      cy.get('.achievement-card').should('have.class', 'rarity-common')
        .or('have.class', 'rarity-rare')
        .or('have.class', 'rarity-epic')
        .or('have.class', 'rarity-legendary')
    })
  })

  describe('Performance por Categoria', () => {
    it('deve exibir cards de categoria', () => {
      cy.get('.category-card').should('have.length.at.least', 2)
      cy.get('.category-name').should('not.be.empty')
    })

    it('deve mostrar estatísticas por categoria', () => {
      cy.get('.category-card').first().within(() => {
        cy.get('.stat-item').should('have.length', 3)
        cy.get('.stat-value').should('not.be.empty')
        cy.get('.stat-label').should('exist')
      })
    })

    it('deve exibir recomendações para categorias fracas', () => {
      cy.get('.category-recommendation').should('exist')
    })

    it('deve permitir clicar em categoria para detalhes', () => {
      cy.get('.category-card').first().click()
      // Verificar se alguma ação ocorreu (log no console, etc)
    })
  })

  describe('Padrões de Estudo', () => {
    it('deve exibir distribuição de tempo', () => {
      cy.get('.time-distribution').should('be.visible')
    })

    it('deve mostrar horários de pico', () => {
      cy.get('.peak-hours').should('be.visible')
      cy.get('.hour-block').should('have.length', 24)
    })

    it('deve exibir calendário de sequências', () => {
      cy.get('.streak-calendar').should('be.visible')
      cy.get('.calendar-day').should('have.length.at.least', 28)
      cy.get('.calendar-day.studied').should('exist')
    })

    it('deve destacar dia atual no calendário', () => {
      cy.get('.calendar-day.today').should('have.css', 'border')
    })
  })

  describe('Funcionalidades de Exportação', () => {
    it('deve abrir modal de exportação', () => {
      cy.get('.action-btn.export').click()
      cy.get('.modal-overlay').should('be.visible')
      cy.get('.modal-header').should('contain', 'Exportar Relatório')
    })

    it('deve permitir selecionar formato de exportação', () => {
      cy.get('.action-btn.export').click()
      
      cy.get('input[value="pdf"]').should('be.checked')
      cy.get('input[value="excel"]').click()
      cy.get('input[value="excel"]').should('be.checked')
    })

    it('deve permitir selecionar período de exportação', () => {
      cy.get('.action-btn.export').click()
      
      cy.get('input[type="date"]').first().should('have.value')
      cy.get('input[type="date"]').last().should('have.value')
    })

    it('deve permitir selecionar seções para exportar', () => {
      cy.get('.action-btn.export').click()
      
      cy.get('input[type="checkbox"]').should('have.length', 4)
      cy.get('input[type="checkbox"]').first().should('be.checked')
      
      // Desmarcar uma seção
      cy.get('input[type="checkbox"]').eq(2).click()
      cy.get('input[type="checkbox"]').eq(2).should('not.be.checked')
    })

    it('deve exportar relatório com sucesso', () => {
      cy.get('.action-btn.export').click()
      cy.get('.modal-footer .btn-primary').click()
      
      // Verificar se o download foi iniciado (difícil de testar completamente)
      cy.get('.modal-overlay').should('not.exist')
    })
  })

  describe('Configurações', () => {
    it('deve abrir modal de configurações', () => {
      cy.get('.action-btn.settings').click()
      cy.get('.modal-overlay').should('be.visible')
      cy.get('.modal-header').should('contain', 'Configurações')
    })

    it('deve permitir alternar animações', () => {
      cy.get('.action-btn.settings').click()
      
      cy.get('.toggle-option').contains('Mostrar Animações').click()
      cy.get('.modal-footer .btn-primary').click()
      
      // Verificar se a classe foi aplicada
      cy.get('html').should('have.class', 'no-animations')
    })

    it('deve permitir ativar modo compacto', () => {
      cy.get('.action-btn.settings').click()
      
      cy.get('.toggle-option').contains('Modo Compacto').click()
      cy.get('.modal-footer .btn-primary').click()
      
      cy.get('html').should('have.class', 'compact-mode')
    })

    it('deve permitir limpar cache', () => {
      cy.get('.action-btn.settings').click()
      cy.get('.btn-secondary').contains('Limpar Cache').click()
      
      // Verificar se os dados foram recarregados
      cy.get('.loading-overlay').should('be.visible')
    })
  })

  describe('Atualização de Dados', () => {
    it('deve permitir atualizar dados manualmente', () => {
      cy.get('.action-btn.refresh').click()
      cy.get('.action-btn.refresh').should('have.class', 'spinning')
      
      cy.wait(2000)
      cy.get('.action-btn.refresh').should('not.have.class', 'spinning')
    })

    it('deve mostrar overlay de loading durante carregamento', () => {
      cy.get('.action-btn.refresh').click()
      cy.get('.loading-overlay').should('be.visible')
      cy.get('.loading-text').should('not.be.empty')
      
      cy.wait(2000)
      cy.get('.loading-overlay').should('not.exist')
    })
  })

  describe('Responsividade', () => {
    it('deve adaptar layout para mobile', () => {
      cy.viewport(375, 667) // iPhone SE
      
      cy.get('.header-container').should('be.visible')
      cy.get('.metrics-grid').should('have.css', 'grid-template-columns')
      cy.get('.period-selector').should('have.css', 'flex-wrap', 'wrap')
    })

    it('deve adaptar layout para tablet', () => {
      cy.viewport(768, 1024) // iPad
      
      cy.get('.charts-container').should('be.visible')
      cy.get('.goals-achievements').should('be.visible')
    })

    it('deve ocultar texto em botões no mobile', () => {
      cy.viewport(375, 667)
      
      cy.get('.action-btn.export span').should('not.be.visible')
    })
  })

  describe('Interações Avançadas', () => {
    it('deve permitir interação com múltiplas funcionalidades', () => {
      // Mudar período
      cy.get('.period-btn').contains('Mês').click()
      cy.wait(1000)
      
      // Gerar insights
      cy.get('.generate-btn').click()
      cy.wait(2000)
      
      // Abrir modal de meta
      cy.get('.add-goal-btn').click()
      cy.get('.modal-close').click()
      
      // Verificar que tudo continua funcionando
      cy.get('.metric-card').should('be.visible')
      cy.get('.insight-card').should('exist')
    })

    it('deve manter estado após navegação', () => {
      // Mudar algumas configurações
      cy.get('.period-btn').contains('Ano').click()
      cy.get('.chart-selector').select('heatmap')
      
      // Navegar para outra página e voltar
      cy.visit('/')
      cy.visit('/progress-dashboard')
      
      // Verificar se manteve as seleções (se implementado)
      cy.get('.period-btn.active').should('contain', 'Ano')
    })
  })

  describe('Acessibilidade', () => {
    it('deve ter navegação por teclado', () => {
      cy.get('body').tab()
      cy.focused().should('be.visible')
      
      // Navegar pelos elementos principais
      cy.focused().tab().tab().tab()
      cy.focused().should('have.class', 'period-btn')
    })

    it('deve ter labels apropriados', () => {
      cy.get('button').each(($btn) => {
        cy.wrap($btn).should('have.attr', 'type')
          .or('have.attr', 'aria-label')
          .or('contain.text')
      })
    })
  })
})

// Testes de Performance
describe('Performance do Dashboard', () => {
  it('deve carregar rapidamente', () => {
    cy.visit('/progress-dashboard', {
      onBeforeLoad: (win) => {
        win.performance.mark('start')
      },
      onLoad: (win) => {
        win.performance.mark('end')
        win.performance.measure('pageLoad', 'start', 'end')
        const measure = win.performance.getEntriesByName('pageLoad')[0]
        expect(measure.duration).to.be.lessThan(3000) // 3 segundos
      }
    })
  })

  it('deve renderizar muitos elementos sem travamento', () => {
    cy.visit('/progress-dashboard')
    
    // Verificar que todos os elementos principais são renderizados
    cy.get('.metric-card').should('have.length.at.least', 6)
    cy.get('.category-card').should('have.length.at.least', 2)
    cy.get('.insight-card').should('exist')
    cy.get('.goal-card').should('exist')
    cy.get('.achievement-card').should('exist')
    
    // Interagir para verificar responsividade
    cy.get('.period-btn').each(($btn) => {
      cy.wrap($btn).click()
      cy.wait(100)
    })
  })
})