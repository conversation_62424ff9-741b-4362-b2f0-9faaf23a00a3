/* Comprehensive Color Theme System */

:root {
  /* Brand Colors */
  --brand-primary: #667eea;
  --brand-secondary: #764ba2;
  --brand-tertiary: #f093fb;
  --brand-accent: #f5576c;
  
  /* Semantic Colors */
  --color-success: #10b981;
  --color-success-light: #34d399;
  --color-success-dark: #059669;
  --color-success-bg: rgba(16, 185, 129, 0.1);
  
  --color-warning: #f59e0b;
  --color-warning-light: #fbbf24;
  --color-warning-dark: #d97706;
  --color-warning-bg: rgba(245, 158, 11, 0.1);
  
  --color-danger: #ef4444;
  --color-danger-light: #f87171;
  --color-danger-dark: #dc2626;
  --color-danger-bg: rgba(239, 68, 68, 0.1);
  
  --color-info: #3b82f6;
  --color-info-light: #60a5fa;
  --color-info-dark: #2563eb;
  --color-info-bg: rgba(59, 130, 246, 0.1);
  
  /* Neutral Colors */
  --color-white: #ffffff;
  --color-black: #000000;
  
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Light Theme */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-elevated: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;
  --text-muted: #d1d5db;
  
  --border-color: #e5e7eb;
  --border-color-strong: #d1d5db;
  --border-color-light: #f3f4f6;
  
  --card-bg: #ffffff;
  --card-bg-hover: #f9fafb;
  --card-border: #e5e7eb;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --card-shadow-hover: 0 10px 25px rgba(0, 0, 0, 0.1);
  
  /* Component Specific */
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-border-focus: var(--brand-primary);
  --input-placeholder: #9ca3af;
  
  --button-primary-bg: var(--brand-primary);
  --button-primary-hover: var(--brand-secondary);
  --button-primary-text: #ffffff;
  
  --button-secondary-bg: #f3f4f6;
  --button-secondary-hover: #e5e7eb;
  --button-secondary-text: #374151;
  
  --navbar-bg: #ffffff;
  --navbar-border: #e5e7eb;
  --navbar-text: #111827;
  
  --footer-bg: #f9fafb;
  --footer-text: #6b7280;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--brand-tertiary) 0%, var(--brand-accent) 100%);
  --gradient-success: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-dark) 100%);
  --gradient-warning: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
  --gradient-danger: linear-gradient(135deg, var(--color-danger) 0%, var(--color-danger-dark) 100%);
  --gradient-dark: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  
  /* Skeleton Loading */
  --skeleton-base: #e5e7eb;
  --skeleton-shine: rgba(255, 255, 255, 0.4);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
  
  /* Focus States */
  --focus-ring-color: var(--brand-primary);
  --focus-ring-offset: 2px;
  --focus-ring-width: 3px;
  
  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
  
  /* Medical Theme Accents */
  --medical-blue: #0ea5e9;
  --medical-teal: #14b8a6;
  --medical-green: #22c55e;
  --medical-purple: #a855f7;
  --medical-pink: #ec4899;
}

/* Dark Theme */
:root[data-theme="dark"] {
  /* Backgrounds */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-elevated: #1e293b;
  --bg-overlay: rgba(0, 0, 0, 0.7);
  
  /* Text */
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --text-inverse: #0f172a;
  --text-muted: #64748b;
  
  /* Borders */
  --border-color: #334155;
  --border-color-strong: #475569;
  --border-color-light: #1e293b;
  
  /* Cards */
  --card-bg: #1e293b;
  --card-bg-hover: #334155;
  --card-border: #334155;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  --card-shadow-hover: 0 10px 25px rgba(0, 0, 0, 0.4);
  
  /* Components */
  --input-bg: #1e293b;
  --input-border: #475569;
  --input-border-focus: var(--brand-primary);
  --input-placeholder: #64748b;
  
  --button-secondary-bg: #334155;
  --button-secondary-hover: #475569;
  --button-secondary-text: #e2e8f0;
  
  --navbar-bg: #1e293b;
  --navbar-border: #334155;
  --navbar-text: #f1f5f9;
  
  --footer-bg: #0f172a;
  --footer-text: #94a3b8;
  
  /* Skeleton */
  --skeleton-base: #334155;
  --skeleton-shine: rgba(255, 255, 255, 0.1);
  
  /* Shadows (more subtle in dark mode) */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.5);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.6);
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #1f2937;
    --border-color: #000000;
    --focus-ring-width: 4px;
  }
  
  :root[data-theme="dark"] {
    --text-primary: #ffffff;
    --text-secondary: #f3f4f6;
    --border-color: #ffffff;
  }
}

/* Color Utilities */
.text-brand { color: var(--brand-primary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-danger { color: var(--color-danger); }
.text-info { color: var(--color-info); }

.bg-brand { background-color: var(--brand-primary); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-danger { background-color: var(--color-danger); }
.bg-info { background-color: var(--color-info); }

.bg-gradient-primary { background: var(--gradient-primary); }
.bg-gradient-secondary { background: var(--gradient-secondary); }
.bg-gradient-success { background: var(--gradient-success); }
.bg-gradient-warning { background: var(--gradient-warning); }
.bg-gradient-danger { background: var(--gradient-danger); }

/* Medical Theme Classes */
.medical-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  position: relative;
  overflow: hidden;
}

.medical-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.medical-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 600;
  background: var(--color-info-bg);
  color: var(--color-info);
}

.medical-badge.success {
  background: var(--color-success-bg);
  color: var(--color-success);
}

.medical-badge.warning {
  background: var(--color-warning-bg);
  color: var(--color-warning);
}

.medical-badge.danger {
  background: var(--color-danger-bg);
  color: var(--color-danger);
}

/* Focus Styles */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 var(--focus-ring-offset) transparent,
              0 0 0 calc(var(--focus-ring-offset) + var(--focus-ring-width)) var(--focus-ring-color);
  transition: box-shadow var(--transition-fast) ease;
}

/* Gradient Text */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Theme Toggle Animation */
.theme-transition,
.theme-transition *,
.theme-transition *::before,
.theme-transition *::after {
  transition: all var(--transition-normal) ease !important;
}

/* Print Styles */
@media print {
  :root {
    --bg-primary: #ffffff;
    --text-primary: #000000;
    --border-color: #d1d5db;
  }
}