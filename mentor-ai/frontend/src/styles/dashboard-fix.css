/* Dashboard Purple Screen Fix
   This CSS file removes all purple overlays and gradients
   Import this AFTER the component styles to override them */

/* Remove ALL purple gradients - replace with solid colors */
.performance-dashboard .title-icon,
.performance-dashboard .period-info-bar,
.performance-dashboard .calendar-day.has-activity,
.performance-dashboard .calendar-day.high-activity,
.performance-dashboard .milestone-item.achieved .milestone-marker,
.performance-dashboard .metric-bar .bar-fill,
.performance-dashboard .btn-compare,
.performance-dashboard .ranking-card,
.performance-dashboard .leaderboard-item.is-you,
.performance-dashboard .record-item::after {
  background: none !important;
  background-color: transparent !important;
}

/* Fix specific elements with safe colors */
.performance-dashboard .title-icon {
  background-color: #667eea !important;
}

.performance-dashboard .period-info-bar {
  background-color: #ffffff !important;
  border-bottom: 1px solid #e9ecef !important;
}

.performance-dashboard .calendar-day.has-activity {
  background-color: rgba(102, 126, 234, 0.05) !important;
  border-color: rgba(102, 126, 234, 0.4) !important;
}

.performance-dashboard .calendar-day.high-activity {
  background-color: rgba(102, 126, 234, 0.2) !important;
  border-color: #667eea !important;
}

.performance-dashboard .milestone-item.achieved .milestone-marker {
  background-color: #667eea !important;
  color: white !important;
}

.performance-dashboard .metric-bar .bar-fill {
  background-color: #667eea !important;
}

.performance-dashboard .btn-compare {
  background-color: #667eea !important;
  color: white !important;
}

.performance-dashboard .ranking-card {
  background-color: rgba(102, 126, 234, 0.02) !important;
}

.performance-dashboard .leaderboard-item.is-you {
  background-color: rgba(102, 126, 234, 0.06) !important;
  border: 1px solid rgba(102, 126, 234, 0.4) !important;
}

/* Ensure no element can create full-screen overlays */
.performance-dashboard * {
  max-width: 100% !important;
  max-height: 100vh !important;
  position: relative !important;
}

/* Fix any absolute/fixed positioned elements */
.performance-dashboard *[style*="position: fixed"],
.performance-dashboard *[style*="position: absolute"] {
  position: relative !important;
}

/* Prevent any element from covering the entire screen */
.performance-dashboard > * {
  z-index: auto !important;
}

/* Ensure SVGs don't create overlays */
.performance-dashboard svg {
  max-width: 100% !important;
  max-height: 100% !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Fix any canvas elements */
.performance-dashboard canvas {
  max-width: 100% !important;
  max-height: 400px !important;
  position: relative !important;
  z-index: 1 !important;
  background-color: transparent !important;
}

/* Remove any :before/:after pseudo-elements that might create overlays */
.performance-dashboard *::before,
.performance-dashboard *::after {
  z-index: -1 !important;
  pointer-events: none !important;
}

/* Ensure charts container doesn't overflow */
.performance-dashboard .chart-container {
  overflow: hidden !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Fix any modal/overlay classes */
.performance-dashboard .modal,
.performance-dashboard .overlay,
.performance-dashboard [class*="overlay"] {
  display: none !important;
}

/* Ensure main content is always visible */
.performance-dashboard .dashboard-header,
.performance-dashboard .overview-section,
.performance-dashboard .content-grid,
.performance-dashboard .card {
  position: relative !important;
  z-index: 10 !important;
  background-color: #ffffff !important;
}

/* Remove any purple-ish colors */
*[style*="#764ba2"],
*[style*="rgb(118, 75, 162)"],
*[style*="rgba(118, 75, 162"] {
  background-color: #667eea !important;
}

/* Global fix for any hidden overlays */
body .performance-dashboard {
  overflow: visible !important;
}

/* Ensure no blurred backgrounds */
.performance-dashboard *[style*="backdrop-filter"],
.performance-dashboard *[style*="filter"] {
  backdrop-filter: none !important;
  filter: none !important;
}

/* Debug helper - remove in production */
.performance-dashboard [style*="background-color: purple"],
.performance-dashboard [style*="background: purple"] {
  background: red !important;
  border: 5px solid yellow !important;
}