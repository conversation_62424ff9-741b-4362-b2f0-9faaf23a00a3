/* Study Plan Page Styles */
.study-plan-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
  padding-bottom: 60px;
}

/* Hero Section */
.hero-section {
  position: relative;
  padding: 80px 20px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.floating-shapes {
  position: relative;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

.shape-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  top: -100px;
  left: -100px;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
  top: 50%;
  right: -100px;
  animation-delay: 5s;
}

.shape-3 {
  width: 250px;
  height: 250px;
  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
  bottom: -100px;
  left: 30%;
  animation-delay: 10s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  25% { transform: translate(50px, -50px) scale(1.1); }
  50% { transform: translate(-50px, 50px) scale(0.9); }
  75% { transform: translate(30px, 30px) scale(1.05); }
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.hero-title {
  margin-bottom: 20px;
}

.gradient-text {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: block;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 1.5rem;
  color: #cbd5e0;
  font-weight: 300;
  display: block;
}

.hero-description {
  font-size: 1.125rem;
  color: #a0aec0;
  margin-bottom: 40px;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button, .secondary-button {
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
}

.cta-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
}

.secondary-button {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.secondary-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Quick Stats */
.quick-stats {
  padding: 40px 20px;
}

.stats-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: white;
}

.stat-content p {
  font-size: 0.875rem;
  color: #a0aec0;
  margin: 4px 0 0;
}

.stat-trend {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  font-weight: 600;
}

.stat-trend.up {
  color: #48bb78;
}

.stat-trend.down {
  color: #f56565;
}

/* Active Plans */
.active-plans {
  padding: 40px 20px;
}

.section-header {
  max-width: 1200px;
  margin: 0 auto 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 0;
}

.view-toggle {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 4px;
}

.toggle-btn {
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #a0aec0;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.plans-container {
  max-width: 1200px;
  margin: 0 auto;
}

.plans-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.plans-container.list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.plan-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.plan-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
}

.plan-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.plan-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.plan-meta {
  flex: 1;
}

.plan-meta h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.plan-meta p {
  margin: 4px 0 0;
  font-size: 0.875rem;
  color: #a0aec0;
}

.plan-menu {
  position: relative;
}

.plan-menu svg {
  color: #a0aec0;
  cursor: pointer;
  transition: color 0.3s ease;
}

.plan-menu svg:hover {
  color: white;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: #1a1f3a;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 150px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.dropdown-menu a {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 16px;
  color: #cbd5e0;
  text-decoration: none;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.dropdown-menu a:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.dropdown-menu a.danger {
  color: #fc8181;
}

.plan-progress {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.875rem;
  color: #cbd5e0;
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.plan-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.plan-stats .stat {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  color: #cbd5e0;
}

.plan-stats .stat svg {
  font-size: 0.75rem;
}

.plan-actions {
  display: flex;
  gap: 12px;
}

.continue-btn, .details-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 0.875rem;
}

.continue-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.continue-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.details-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.details-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Study Calendar */
.study-calendar {
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;
}

.calendar-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.current-month {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  min-width: 150px;
  text-align: center;
}

.calendar-grid {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  margin-top: 24px;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.weekday {
  text-align: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: #a0aec0;
  padding: 8px;
}

.days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
}

.day {
  aspect-ratio: 1;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 8px;
}

.day:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.day.other-month {
  opacity: 0.3;
}

.day.today {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.day.has-plan {
  border-color: rgba(255, 255, 255, 0.1);
}

.day-number {
  font-size: 0.875rem;
  color: white;
  font-weight: 500;
}

.day-indicators {
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 2px;
}

.study-indicator {
  width: 4px;
  height: 4px;
  border-radius: 50%;
}

/* AI Recommendations */
.ai-recommendations {
  padding: 40px 20px;
}

.refresh-btn {
  padding: 8px 16px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.recommendations-grid {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.recommendation-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: all 0.3s ease;
}

.recommendation-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
}

.recommendation-card.optimization {
  border-left: 4px solid #4CAF50;
}

.recommendation-card.warning {
  border-left: 4px solid #FF9800;
}

.recommendation-card.suggestion {
  border-left: 4px solid #2196F3;
}

.rec-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.rec-content h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
}

.rec-content p {
  margin: 8px 0 0;
  font-size: 0.875rem;
  color: #cbd5e0;
  line-height: 1.5;
}

.rec-meta {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.impact, .time {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.75rem;
  color: #a0aec0;
}

.apply-btn {
  padding: 10px 20px;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.apply-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Plan Wizard Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.plan-wizard {
  background: #1a1f3a;
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.wizard-header {
  padding: 24px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.wizard-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.wizard-progress {
  padding: 32px;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #a0aec0;
  transition: all 0.3s ease;
}

.progress-step.active .step-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
  color: white;
}

.progress-step.completed .step-number {
  background: #48bb78;
  border-color: transparent;
  color: white;
}

.progress-step span {
  font-size: 0.875rem;
  color: #a0aec0;
  font-weight: 500;
}

.progress-step.active span,
.progress-step.completed span {
  color: white;
}

.progress-line {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.progress-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.5s ease;
}

.wizard-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

.wizard-step h3 {
  margin: 0 0 24px;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.goal-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.goal-option {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.goal-option:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.goal-option.selected {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.goal-option svg {
  font-size: 2rem;
  color: white;
  margin-bottom: 12px;
}

.goal-option h4 {
  margin: 0 0 8px;
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
}

.goal-option p {
  margin: 0;
  font-size: 0.875rem;
  color: #a0aec0;
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.subject-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.subject-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.subject-card.selected {
  border-color: #667eea;
}

.subject-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  margin: 0 auto 8px;
}

.subject-card span {
  font-size: 0.875rem;
  color: white;
  font-weight: 500;
}

.schedule-config {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.config-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #cbd5e0;
}

.hours-selector {
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px 20px;
  width: fit-content;
}

.hours-selector button {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hours-selector button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.hours-selector span {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  min-width: 40px;
  text-align: center;
}

.weekdays-selector {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.weekday-btn {
  padding: 8px 16px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: #cbd5e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.weekday-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.weekday-btn.selected {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: white;
}

.duration-select {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  width: 200px;
}

.duration-select option {
  background: #1a1f3a;
}

.personalization {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #cbd5e0;
}

.form-input, .form-textarea {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.color-picker {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 3px solid transparent;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.selected {
  border-color: white;
  box-shadow: 0 0 0 2px #1a1f3a, 0 0 0 4px white;
}

.wizard-actions {
  padding: 24px 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
}

.back-btn, .next-btn, .create-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.next-btn, .create-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-left: auto;
}

.next-btn:hover:not(:disabled), .create-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.next-btn:disabled, .create-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Plan Details Modal */
.plan-details-modal {
  background: #1a1f3a;
  border-radius: 20px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 24px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.modal-tabs {
  display: flex;
  padding: 0 32px;
  background: rgba(255, 255, 255, 0.03);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab {
  padding: 16px 24px;
  border: none;
  background: transparent;
  color: #a0aec0;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.tab:hover {
  color: white;
}

.tab.active {
  color: white;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.modal-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.metric-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
}

.metric-card h4 {
  margin: 0 0 16px;
  font-size: 1rem;
  font-weight: 600;
  color: #cbd5e0;
}

.circular-progress {
  position: relative;
  width: 150px;
  height: 150px;
  margin: 0 auto;
}

.circular-progress svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-text .value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.progress-text .label {
  display: block;
  font-size: 0.875rem;
  color: #a0aec0;
  margin-top: 4px;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.stat-label {
  color: #a0aec0;
  font-size: 0.875rem;
}

.stat-value {
  color: white;
  font-weight: 600;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.activity-info h5 {
  margin: 0 0 4px;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

.activity-info p {
  margin: 0;
  font-size: 0.75rem;
  color: #a0aec0;
}

.schedule-view {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.schedule-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
}

.edit-schedule-btn {
  padding: 8px 16px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
}

.edit-schedule-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.weekly-schedule {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.day-schedule h4 {
  margin: 0 0 12px;
  font-size: 1rem;
  font-weight: 600;
  color: white;
}

.time-blocks {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.time-block {
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.block-time {
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.block-subject {
  font-size: 0.875rem;
  color: #cbd5e0;
}

.analytics-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.chart-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
}

.chart-container h4 {
  margin: 0 0 16px;
  font-size: 1rem;
  font-weight: 600;
  color: white;
}

.chart-container canvas {
  max-height: 250px;
}

/* Success Animation */
.success-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.success-animation {
  text-align: center;
}

.checkmark-circle {
  width: 80px;
  height: 80px;
  background: #48bb78;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  animation: scaleIn 0.3s ease;
}

.checkmark-circle svg {
  font-size: 2rem;
  color: white;
}

.success-animation h3 {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

@keyframes scaleIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Transitions */
.modal-enter-active, .modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from, .modal-leave-to {
  opacity: 0;
}

.modal-enter-from .plan-wizard,
.modal-leave-to .plan-wizard,
.modal-enter-from .plan-details-modal,
.modal-leave-to .plan-details-modal {
  transform: scale(0.9);
}

.success-enter-active, .success-leave-active {
  transition: all 0.3s ease;
}

.success-enter-from, .success-leave-to {
  opacity: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .gradient-text {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.25rem;
  }
  
  .hero-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .cta-button, .secondary-button {
    width: 100%;
  }
  
  .stats-container {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .plans-container.grid {
    grid-template-columns: 1fr;
  }
  
  .wizard-progress {
    padding: 24px 16px;
  }
  
  .progress-step span {
    display: none;
  }
  
  .goal-options {
    grid-template-columns: 1fr;
  }
  
  .subjects-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .wizard-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .back-btn, .next-btn, .create-btn {
    width: 100%;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .analytics-charts {
    grid-template-columns: 1fr;
  }
}