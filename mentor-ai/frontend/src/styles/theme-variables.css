/* Global CSS Variables for Theme System */
:root {
  /* Default to dark theme */
  --theme-background: #0a0a0a;
  --theme-surface: #1a1a1a;
  --theme-surfaceLight: #2a2a2a;
  
  --theme-text: #ffffff;
  --theme-textSecondary: #a0a0a0;
  --theme-textMuted: #666666;
  
  --theme-primary: #00ffff;
  --theme-secondary: #ff00ff;
  --theme-accent: #ffff00;
  --theme-success: #00ff88;
  --theme-warning: #ffaa00;
  --theme-error: #ff4444;
  
  --theme-border: rgba(255, 255, 255, 0.1);
  --theme-borderHover: rgba(255, 255, 255, 0.2);
  
  --theme-glass: rgba(255, 255, 255, 0.05);
  --theme-glassHover: rgba(255, 255, 255, 0.1);
  
  --theme-shadow: rgba(0, 0, 0, 0.5);
  --theme-glow: rgba(0, 255, 255, 0.5);
  
  /* Transition for smooth theme changes */
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark theme explicitly */
[data-theme="dark"] {
  --theme-background: #0a0a0a;
  --theme-surface: #1a1a1a;
  --theme-surfaceLight: #2a2a2a;
  
  --theme-text: #ffffff;
  --theme-textSecondary: #a0a0a0;
  --theme-textMuted: #666666;
  
  --theme-primary: #00ffff;
  --theme-secondary: #ff00ff;
  --theme-accent: #ffff00;
  --theme-success: #00ff88;
  --theme-warning: #ffaa00;
  --theme-error: #ff4444;
  
  --theme-border: rgba(255, 255, 255, 0.1);
  --theme-borderHover: rgba(255, 255, 255, 0.2);
  
  --theme-glass: rgba(255, 255, 255, 0.05);
  --theme-glassHover: rgba(255, 255, 255, 0.1);
  
  --theme-shadow: rgba(0, 0, 0, 0.5);
  --theme-glow: rgba(0, 255, 255, 0.5);
}

/* Light theme */
[data-theme="light"] {
  --theme-background: #ffffff;
  --theme-surface: #f8f9fa;
  --theme-surfaceLight: #e9ecef;
  
  --theme-text: #212529;
  --theme-textSecondary: #6c757d;
  --theme-textMuted: #adb5bd;
  
  --theme-primary: #0066cc;
  --theme-secondary: #6610f2;
  --theme-accent: #fd7e14;
  --theme-success: #28a745;
  --theme-warning: #ffc107;
  --theme-error: #dc3545;
  
  --theme-border: rgba(0, 0, 0, 0.1);
  --theme-borderHover: rgba(0, 0, 0, 0.2);
  
  --theme-glass: rgba(255, 255, 255, 0.7);
  --theme-glassHover: rgba(255, 255, 255, 0.9);
  
  --theme-shadow: rgba(0, 0, 0, 0.1);
  --theme-glow: rgba(0, 102, 204, 0.3);
}

/* Base styles that use theme variables */
* {
  box-sizing: border-box;
}

body {
  background-color: var(--theme-background);
  color: var(--theme-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: var(--theme-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--theme-border);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-borderHover);
}

/* Selection styling */
::selection {
  background: var(--theme-primary);
  color: var(--theme-background);
}

::-moz-selection {
  background: var(--theme-primary);
  color: var(--theme-background);
}

/* Focus styling */
:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* Links */
a {
  color: var(--theme-primary);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--theme-secondary);
}

/* Buttons base */
button {
  background: var(--theme-glass);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
  transition: all 0.3s ease;
}

button:hover {
  background: var(--theme-glassHover);
  border-color: var(--theme-borderHover);
}

/* Inputs base */
input, textarea, select {
  background: var(--theme-surface);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
  transition: all 0.3s ease;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px var(--theme-glow);
}

/* Cards and surfaces */
.card, .surface {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  box-shadow: 0 4px 20px var(--theme-shadow);
}

/* Glass effect utility */
.glass {
  background: var(--theme-glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Text utilities */
.text-primary { color: var(--theme-primary) !important; }
.text-secondary { color: var(--theme-secondary) !important; }
.text-accent { color: var(--theme-accent) !important; }
.text-success { color: var(--theme-success) !important; }
.text-warning { color: var(--theme-warning) !important; }
.text-error { color: var(--theme-error) !important; }
.text-muted { color: var(--theme-textMuted) !important; }

/* Background utilities */
.bg-primary { background-color: var(--theme-primary) !important; }
.bg-secondary { background-color: var(--theme-secondary) !important; }
.bg-surface { background-color: var(--theme-surface) !important; }
.bg-surface-light { background-color: var(--theme-surfaceLight) !important; }

/* Border utilities */
.border-primary { border-color: var(--theme-primary) !important; }
.border-secondary { border-color: var(--theme-secondary) !important; }

/* Shadow utilities */
.shadow-sm { box-shadow: 0 2px 10px var(--theme-shadow) !important; }
.shadow { box-shadow: 0 4px 20px var(--theme-shadow) !important; }
.shadow-lg { box-shadow: 0 8px 40px var(--theme-shadow) !important; }

/* Glow utilities */
.glow-primary { box-shadow: 0 0 20px var(--theme-primary) !important; }
.glow-secondary { box-shadow: 0 0 20px var(--theme-secondary) !important; }

/* Transitions */
.theme-transition {
  transition: background-color 0.3s ease, 
              color 0.3s ease, 
              border-color 0.3s ease,
              box-shadow 0.3s ease !important;
}