/* Theme-specific overrides for existing components */

/* App.vue */
#app {
  background-color: var(--theme-background);
  color: var(--theme-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Headers and Navigation */
.app-header,
.header,
.navbar,
.nav {
  background: var(--theme-surface);
  border-bottom: 1px solid var(--theme-border);
  color: var(--theme-text);
}

/* Cards and Panels */
.card,
.panel,
.modal-content,
.dropdown-menu,
.dropdown,
.user-dropdown,
.notification-dropdown {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  color: var(--theme-text);
  box-shadow: 0 4px 20px var(--theme-shadow);
}

/* Forms and Inputs */
input,
textarea,
select,
.form-control,
.search-input,
.input-field {
  background: var(--theme-surfaceLight);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
}

input::placeholder,
textarea::placeholder {
  color: var(--theme-textMuted);
}

input:focus,
textarea:focus,
select:focus,
.form-control:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px var(--theme-glow);
  background: var(--theme-surface);
}

/* Buttons */
button,
.btn,
.button {
  background: var(--theme-glass);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
  transition: all 0.3s ease;
}

button:hover,
.btn:hover,
.button:hover {
  background: var(--theme-glassHover);
  border-color: var(--theme-borderHover);
}

.btn-primary {
  background: var(--theme-primary);
  color: var(--theme-background);
  border-color: var(--theme-primary);
}

.btn-primary:hover {
  background: var(--theme-secondary);
  border-color: var(--theme-secondary);
}

/* Links */
a {
  color: var(--theme-primary);
}

a:hover {
  color: var(--theme-secondary);
}

/* Tables */
table {
  background: var(--theme-surface);
}

th {
  background: var(--theme-surfaceLight);
  color: var(--theme-text);
  border-color: var(--theme-border);
}

td {
  border-color: var(--theme-border);
  color: var(--theme-text);
}

tr:hover td {
  background: var(--theme-glass);
}

/* Sidebar */
.sidebar,
aside {
  background: var(--theme-surface);
  border-right: 1px solid var(--theme-border);
}

/* Toast notifications */
.Vue-Toastification__toast {
  background: var(--theme-surface) !important;
  color: var(--theme-text) !important;
  border: 1px solid var(--theme-border) !important;
}

/* Icons */
.icon,
.fa,
.fas,
.far,
svg {
  color: inherit;
}

/* Specific component overrides */

/* Login/Register pages */
.auth-container,
.login-container,
.register-container {
  background: var(--theme-background);
}

.auth-card,
.login-card,
.register-card {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
  box-shadow: 0 8px 40px var(--theme-shadow);
}

/* Dashboard */
.dashboard {
  background: var(--theme-background);
}

.dashboard-card,
.stat-card,
.metric-card {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
}

/* Calendar */
.calendar {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
}

.calendar-day {
  background: var(--theme-surfaceLight);
  border: 1px solid var(--theme-border);
}

.calendar-day:hover {
  background: var(--theme-glass);
}

.calendar-event {
  background: var(--theme-primary);
  color: var(--theme-background);
}

/* Study cards */
.study-card,
.resource-card,
.note-card {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
}

/* Code blocks */
pre,
code {
  background: var(--theme-surfaceLight);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
}

/* Modals */
.modal-backdrop {
  background: rgba(0, 0, 0, 0.7);
}

[data-theme="light"] .modal-backdrop {
  background: rgba(0, 0, 0, 0.5);
}

/* Progress bars */
.progress {
  background: var(--theme-surfaceLight);
}

.progress-bar {
  background: var(--theme-primary);
}

/* Tooltips */
.tooltip {
  background: var(--theme-surface);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
}

/* Notifications */
.notification-item {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
}

.notification-item:hover {
  background: var(--theme-glass);
}

/* AI components specific */
.ai-card,
.engine-card,
.workflow-item {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
}

.quantum-particle,
.neural-particle {
  background: var(--theme-primary);
}

/* Terminal/Console */
.terminal,
.console,
.command-terminal {
  background: var(--theme-surfaceLight);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
}

/* Charts and graphs */
.chart-container {
  background: var(--theme-surface);
  border: 1px solid var(--theme-border);
}

/* Loading states */
.spinner,
.loader {
  border-color: var(--theme-border);
  border-top-color: var(--theme-primary);
}

/* Badges */
.badge {
  background: var(--theme-primary);
  color: var(--theme-background);
}

.badge-secondary {
  background: var(--theme-secondary);
}

/* Tabs */
.nav-tabs .nav-link {
  background: var(--theme-glass);
  color: var(--theme-text);
  border-color: var(--theme-border);
}

.nav-tabs .nav-link.active {
  background: var(--theme-surface);
  border-bottom-color: var(--theme-surface);
}

/* Pagination */
.pagination .page-link {
  background: var(--theme-surface);
  color: var(--theme-text);
  border-color: var(--theme-border);
}

.pagination .page-link:hover {
  background: var(--theme-glass);
}

.pagination .active .page-link {
  background: var(--theme-primary);
  color: var(--theme-background);
  border-color: var(--theme-primary);
}

/* Light theme specific adjustments */
[data-theme="light"] {
  /* Reduce contrast for light mode */
  .text-white {
    color: var(--theme-text) !important;
  }
  
  .bg-dark {
    background: var(--theme-surface) !important;
  }
  
  /* Adjust shadows */
  .shadow,
  .card,
  .modal-content {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }
  
  /* Adjust glass effects */
  .glass,
  .glass-effect {
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(10px);
  }
  
  /* Code syntax highlighting */
  .hljs {
    background: var(--theme-surfaceLight) !important;
  }
}

/* Dark theme specific adjustments */
[data-theme="dark"] {
  /* Enhance glow effects */
  .glow {
    filter: drop-shadow(0 0 10px var(--theme-primary));
  }
  
  /* Neon effects */
  .neon-text {
    text-shadow: 0 0 10px var(--theme-primary);
  }
  
  /* Matrix rain opacity */
  .matrix-canvas {
    opacity: 0.1;
  }
}

/* Transitions for theme switching */
* {
  transition: background-color 0.3s ease, 
              color 0.3s ease, 
              border-color 0.3s ease,
              box-shadow 0.3s ease;
}

/* Disable transitions on theme switch for performance */
.theme-switching * {
  transition: none !important;
}