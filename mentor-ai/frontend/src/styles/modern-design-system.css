/* Modern Design System 
   A comprehensive design system for the flashcard application with:
   - Glassmorphism effects
   - Dark mode support
   - 3D animations
   - Modern UI components
*/

:root {
  /* Base colors - will be overridden by JS based on theme */
  --primary-color: #2563EB;
  --secondary-color: #3B82F6;
  --accent-color: #F43F5E;
  --background-color: #F8FAFC;
  --text-color: #1E293B;
  
  /* Neutral colors */
  --neutral-50: #F8FAFC;
  --neutral-100: #F1F5F9;
  --neutral-200: #E2E8F0;
  --neutral-300: #CBD5E1;
  --neutral-400: #94A3B8;
  --neutral-500: #64748B;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1E293B;
  --neutral-900: #0F172A;
  
  /* System variables */
  --card-padding: 1.5rem;
  --border-radius: 8px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition-speed: 0.3s;
  
  /* Glassmorphism variables */
  --glass-opacity: 0.7;
  --glass-blur: 12px;
  --glass-border: 1px solid rgba(255, 255, 255, 0.2);
  
  /* Font sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  
  /* Z-index layers */
  --z-dropdown: 1000;
  --z-sticky: 1100;
  --z-modal: 1200;
  --z-tooltip: 1300;
  --z-toast: 1400;
}

/* Dark theme overrides */
.dark-theme {
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --glass-border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Global Styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
  overflow-x: hidden;
  line-height: 1.6;
}

/* Glassmorphism card style */
.glass-card {
  background: rgba(255, 255, 255, var(--glass-opacity));
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border-radius: var(--border-radius);
  border: var(--glass-border);
  box-shadow: var(--shadow-md);
  padding: var(--card-padding);
  transition: all var(--transition-speed) ease;
}

.dark-theme .glass-card {
  background: rgba(20, 20, 40, var(--glass-opacity));
}

/* 3D Flashcard styles */
.card-3d {
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.card-3d.flipped {
  transform: rotateY(180deg);
}

.card-3d .front, .card-3d .back {
  backface-visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
}

.card-3d .back {
  transform: rotateY(180deg);
}

/* Modern button style */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  font-size: var(--font-size-md);
  border: none;
  outline: none;
  background-color: var(--primary-color);
  color: white;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
}

.btn-secondary {
  background-color: var(--secondary-color);
}

.btn-accent {
  background-color: var(--accent-color);
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-ghost {
  background-color: transparent;
  color: var(--primary-color);
}

/* Focus mode styles */
.focus-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
}

.focus-mode-content {
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow: auto;
  padding: 2rem;
  border-radius: var(--border-radius);
  background-color: var(--background-color);
  box-shadow: var(--shadow-lg);
}

/* Modern form controls */
.input {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--neutral-50);
  background-clip: padding-box;
  border: 1px solid var(--neutral-300);
  border-radius: var(--border-radius);
  transition: border-color var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.dark-theme .input {
  background-color: var(--neutral-800);
  border-color: var(--neutral-700);
  color: var(--neutral-100);
}

.input:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

.select {
  display: block;
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--neutral-50);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 16px 12px;
  border: 1px solid var(--neutral-300);
  border-radius: var(--border-radius);
  appearance: none;
  transition: border-color var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.dark-theme .select {
  background-color: var(--neutral-800);
  border-color: var(--neutral-700);
  color: var(--neutral-100);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23f1f5f9' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}

.select:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

/* Fade Transitions */
.fade-enter-active, .fade-leave-active {
  transition: opacity var(--transition-speed) ease;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
  transition: transform var(--transition-speed) ease, opacity var(--transition-speed) ease;
}
.slide-up-enter, .slide-up-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

/* Reduced motion overrides */
.reduced-motion * {
  transition-duration: 0s !important;
  animation-duration: 0s !important;
}

/* Modern Badge */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-xs);
  font-weight: 500;
  line-height: 1;
  border-radius: 9999px;
  background-color: var(--neutral-200);
  color: var(--neutral-800);
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}

.badge-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.badge-accent {
  background-color: var(--accent-color);
  color: white;
}

/* Neon glow effects for focus mode */
.neon-text {
  text-shadow: 0 0 5px rgba(59, 130, 246, 0.3), 
               0 0 10px rgba(59, 130, 246, 0.2), 
               0 0 15px rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

.neon-border {
  box-shadow: 0 0 5px rgba(59, 130, 246, 0.5), 
              0 0 10px rgba(59, 130, 246, 0.3), 
              0 0 15px rgba(59, 130, 246, 0.1);
  border: 1px solid var(--primary-color);
}

/* Modern Alert Component */
.alert {
  position: relative;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: var(--primary-color);
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
  color: #10B981;
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
  color: #F59E0B;
}

.alert-danger {
  background-color: rgba(244, 63, 94, 0.1);
  border-color: rgba(244, 63, 94, 0.3);
  color: var(--accent-color);
}

/* Grid System */
.container {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -0.5rem;
  margin-left: -0.5rem;
}

.col {
  flex: 1 0 0%;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

/* Flex utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* Spacing utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-4 { margin: var(--spacing-4); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-1); }
.ml-2 { margin-left: var(--spacing-2); }
.ml-4 { margin-left: var(--spacing-4); }
.ml-6 { margin-left: var(--spacing-6); }
.ml-8 { margin-left: var(--spacing-8); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-1); }
.mr-2 { margin-right: var(--spacing-2); }
.mr-4 { margin-right: var(--spacing-4); }
.mr-6 { margin-right: var(--spacing-6); }
.mr-8 { margin-right: var(--spacing-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--spacing-1); }
.pt-2 { padding-top: var(--spacing-2); }
.pt-4 { padding-top: var(--spacing-4); }
.pt-6 { padding-top: var(--spacing-6); }
.pt-8 { padding-top: var(--spacing-8); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--spacing-1); }
.pb-2 { padding-bottom: var(--spacing-2); }
.pb-4 { padding-bottom: var(--spacing-4); }
.pb-6 { padding-bottom: var(--spacing-6); }
.pb-8 { padding-bottom: var(--spacing-8); }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--spacing-1); }
.pl-2 { padding-left: var(--spacing-2); }
.pl-4 { padding-left: var(--spacing-4); }
.pl-6 { padding-left: var(--spacing-6); }
.pl-8 { padding-left: var(--spacing-8); }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--spacing-1); }
.pr-2 { padding-right: var(--spacing-2); }
.pr-4 { padding-right: var(--spacing-4); }
.pr-6 { padding-right: var(--spacing-6); }
.pr-8 { padding-right: var(--spacing-8); }

/* Gap utilities */
.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-4 { gap: var(--spacing-4); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

/* Display utilities */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

/* Text utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }
.font-light { font-weight: 300; }

/* Border utilities */
.rounded { border-radius: var(--border-radius); }
.rounded-sm { border-radius: calc(var(--border-radius) * 0.5); }
.rounded-lg { border-radius: calc(var(--border-radius) * 1.5); }
.rounded-full { border-radius: 9999px; }

.border { border: 1px solid var(--neutral-300); }
.border-t { border-top: 1px solid var(--neutral-300); }
.border-r { border-right: 1px solid var(--neutral-300); }
.border-b { border-bottom: 1px solid var(--neutral-300); }
.border-l { border-left: 1px solid var(--neutral-300); }

.dark-theme .border { border-color: var(--neutral-700); }
.dark-theme .border-t { border-color: var(--neutral-700); }
.dark-theme .border-r { border-color: var(--neutral-700); }
.dark-theme .border-b { border-color: var(--neutral-700); }
.dark-theme .border-l { border-color: var(--neutral-700); }

/* Shadow utilities */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Animation utilities */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Tooltip styles */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: var(--neutral-800);
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: var(--z-tooltip);
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--neutral-800) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Add support for specific color themes */
.theme-blue {
  --primary-hue: 220;
}

.theme-green {
  --primary-hue: 150;
}

.theme-purple {
  --primary-hue: 270;
}

.theme-red {
  --primary-hue: 0;
}

.theme-orange {
  --primary-hue: 30;
}

/* Additional utilities for common actions */
.cursor-pointer { cursor: pointer; }
.pointer-events-none { pointer-events: none; }
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.whitespace-nowrap { white-space: nowrap; }
.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:flex { display: flex; }
  .sm\:hidden { display: none; }
}

@media (min-width: 768px) {
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
}

@media (min-width: 1024px) {
  .lg\:flex { display: flex; }
  .lg\:hidden { display: none; }
} 