/* Enhanced Mobile Responsive Styles */

/* Mobile-First Approach Base Styles */
:root {
  --mobile-padding: 1rem;
  --mobile-margin: 0.75rem;
  --mobile-font-scale: 0.9;
}

/* Typography Scaling */
@media (max-width: 768px) {
  body {
    font-size: calc(var(--base-font-size) * var(--mobile-font-scale));
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }
  h5 { font-size: 1.1rem; }
  h6 { font-size: 1rem; }
  
  .hero-title {
    font-size: 2.5rem !important;
    line-height: 1.2;
  }
  
  .section-title {
    font-size: 2rem !important;
  }
}

/* Container & Layout Adjustments */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--mobile-padding);
    max-width: 100%;
  }
  
  .section {
    padding: 3rem var(--mobile-padding);
  }
  
  /* Stack grids on mobile */
  .grid,
  .tools-grid,
  .features-grid,
  .resources-grid,
  .quick-tools-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem;
  }
  
  /* Flex layouts to column */
  .flex-row {
    flex-direction: column;
  }
  
  .flex-between {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

/* Navigation Mobile */
@media (max-width: 768px) {
  .app-header {
    padding: 0.75rem var(--mobile-padding);
    position: sticky;
    top: 0;
    z-index: 1000;
  }
  
  .nav-menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    transform: translateY(0);
    transition: transform 0.3s ease;
    max-height: calc(100vh - 60px);
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
  
  .nav-menu.mobile-active {
    transform: translateY(-100%);
  }
  
  .nav-items {
    flex-direction: column;
    width: 100%;
    padding: 1rem 0;
  }
  
  .nav-item {
    width: 100%;
    padding: 0;
  }
  
  .nav-link {
    display: block;
    padding: 1rem var(--mobile-padding);
    width: 100%;
    text-align: left;
  }
  
  .dropdown-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    background: var(--bg-secondary);
    margin: 0.5rem var(--mobile-padding);
    border-radius: 0.5rem;
  }
  
  .mobile-menu-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    cursor: pointer;
    color: var(--text-primary);
    font-size: 1.5rem;
  }
  
  .quick-stats-bar {
    display: none;
  }
}

/* Cards & Components Mobile */
@media (max-width: 768px) {
  .card,
  .feature-card,
  .stat-card,
  .tool-card {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .card-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .icon-wrapper {
    width: 45px;
    height: 45px;
    font-size: 1.25rem;
  }
  
  /* Button Adjustments */
  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    width: 100%;
    text-align: center;
  }
  
  .btn-group {
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
  }
  
  .btn-group .btn {
    margin: 0;
  }
}

/* Home Page Mobile */
@media (max-width: 768px) {
  .hero-section {
    padding: 3rem var(--mobile-padding);
    text-align: center;
  }
  
  .hero-content {
    max-width: 100%;
  }
  
  .hero-visual {
    margin-top: 2rem;
    height: 250px;
  }
  
  .quick-tool-card {
    padding: 1.5rem;
    text-align: center;
  }
  
  .tool-icon-wrapper {
    margin: 0 auto 1rem;
  }
  
  .ai-engine-card {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
  }
  
  .engine-icon {
    margin: 0 auto 1rem;
  }
}

/* Footer Mobile */
@media (max-width: 768px) {
  .app-footer {
    padding: 2rem var(--mobile-padding);
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .footer-column h4 {
    margin-bottom: 1rem;
  }
  
  .newsletter-section {
    padding: 2rem var(--mobile-padding);
  }
  
  .newsletter-content {
    flex-direction: column;
    text-align: center;
  }
  
  .newsletter-form {
    width: 100%;
    max-width: 100%;
  }
  
  .newsletter-input-group {
    flex-direction: column;
  }
  
  .newsletter-input {
    width: 100%;
    margin-bottom: 0.75rem;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .social-links {
    justify-content: center;
  }
}

/* Modals & Overlays Mobile */
@media (max-width: 768px) {
  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    width: calc(100% - 2rem);
  }
  
  .modal-header {
    padding: 1rem;
  }
  
  .modal-body {
    padding: 1rem;
    max-height: calc(100vh - 10rem);
    overflow-y: auto;
  }
  
  .modal-footer {
    padding: 1rem;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .modal-footer .btn {
    width: 100%;
  }
}

/* Forms Mobile */
@media (max-width: 768px) {
  .form-group {
    margin-bottom: 1.5rem;
  }
  
  .form-label {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
  
  .form-control {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .form-col {
    width: 100%;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .input-group-prepend,
  .input-group-append {
    width: 100%;
  }
}

/* Tables Mobile */
@media (max-width: 768px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  table {
    min-width: 600px;
  }
  
  /* Alternative: Convert to cards on mobile */
  .table-mobile-cards table,
  .table-mobile-cards thead,
  .table-mobile-cards tbody,
  .table-mobile-cards th,
  .table-mobile-cards td,
  .table-mobile-cards tr {
    display: block;
  }
  
  .table-mobile-cards thead {
    display: none;
  }
  
  .table-mobile-cards tr {
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1rem;
    background: var(--card-bg);
  }
  
  .table-mobile-cards td {
    text-align: right;
    padding-left: 50%;
    position: relative;
  }
  
  .table-mobile-cards td::before {
    content: attr(data-label);
    position: absolute;
    left: 1rem;
    font-weight: 600;
    text-align: left;
  }
}

/* Touch-Friendly Interactions */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets */
  button,
  a,
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Remove hover effects on touch devices */
  .hover-lift:hover,
  .hover-glow:hover,
  .hover-scale:hover {
    transform: none;
    box-shadow: none;
  }
  
  /* Add active states for better feedback */
  button:active,
  a:active,
  .clickable:active {
    opacity: 0.8;
    transform: scale(0.98);
  }
}

/* Landscape Mobile */
@media (max-width: 812px) and (orientation: landscape) {
  .hero-section {
    min-height: auto;
    padding: 2rem var(--mobile-padding);
  }
  
  .modal-content {
    max-height: calc(100vh - 1rem);
  }
  
  .app-header {
    padding: 0.5rem var(--mobile-padding);
  }
}

/* Small Devices (phones < 400px) */
@media (max-width: 400px) {
  :root {
    --mobile-padding: 0.75rem;
    --mobile-font-scale: 0.85;
  }
  
  .hero-title {
    font-size: 2rem !important;
  }
  
  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
  
  .card,
  .feature-card,
  .stat-card {
    padding: 1.25rem;
  }
}

/* Tablet Adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    max-width: 100%;
    padding: 0 2rem;
  }
  
  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .hero-section {
    padding: 4rem 2rem;
  }
}

/* Performance Optimizations */
@media (max-width: 768px) {
  /* Reduce animations on mobile for better performance */
  * {
    animation-duration: 0.3s !important;
  }
  
  /* Disable parallax effects */
  .parallax {
    background-attachment: scroll !important;
  }
  
  /* Simplify shadows */
  .card,
  .feature-card,
  .stat-card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

/* Utility Classes for Mobile */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }
  
  .desktop-only {
    display: none;
  }
  
  .text-mobile-center {
    text-align: center !important;
  }
  
  .p-mobile-0 {
    padding: 0 !important;
  }
  
  .m-mobile-0 {
    margin: 0 !important;
  }
}