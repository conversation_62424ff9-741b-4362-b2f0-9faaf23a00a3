/**
 * Type definitions for the Neural Learning Engine
 */

export interface User {
  id: string;
  totalStudyTime: number;
  averageAccuracy: number;
  expertiseLevel: number;
  aggressiveGoals: boolean;
  preferences: UserPreferences;
}

export interface UserPreferences {
  wakeTime: number;
  sleepTime: number;
  studyIntensity: 'light' | 'moderate' | 'intense';
  preferredTopics: string[];
  learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'mixed';
}

export interface LearningPath {
  userId: string;
  schedule: StudySession[];
  content: LearningContent[];
  adaptiveRules: AdaptiveRule[];
  estimatedOutcomes: PredictedOutcomes;
  lastUpdated: Date;
}

export interface StudySession {
  id?: string;
  startTime: number;
  duration: number;
  intensity: 'light' | 'moderate' | 'intense';
  topics: string[];
  date: Date;
  correctAnswers: number;
  totalAttempts: number;
  totalTime: number;
  cardsReviewed: number;
  isRetentionTest: boolean;
  completed: boolean;
  isExtraStudy: boolean;
}

export interface LearningContent {
  id: string;
  type: 'flashcard' | 'quiz' | 'case_study' | 'image_analysis';
  difficulty: number;
  topic: string;
  contentType: string;
  estimatedTime: number;
  prerequisites: string[];
  metadata: any;
}

export interface CognitiveLoad {
  current: number;
  optimal: number;
  factors: CognitiveLoadFactors;
}

export interface CognitiveLoadFactors {
  timeOfDay: TimeOfDayEffect;
  contentDifficulty: number;
  sessionDuration: number;
  breakFrequency: number;
  lowPerformanceTimes?: number[];
}

export interface TimeOfDayEffect {
  morning: number;
  afternoon: number;
  evening: number;
  night: number;
}

export interface RetentionCurve {
  immediate: number;
  oneDay: number;
  oneWeek: number;
  oneMonth: number;
  sixMonths: number;
}

export interface FlashcardPerformance {
  cardId: string;
  userId: string;
  timestamp: Date;
  correct: boolean;
  responseTime: number;
  difficulty: number;
  retention: number;
  card: Flashcard;
}

export interface Flashcard {
  id: string;
  front: string;
  back: string;
  contentType?: string;
  tags: string[];
  difficulty: number;
  lastReviewed?: Date;
  nextReview?: Date;
  reviewCount: number;
  successRate: number;
}

export interface AdaptiveRule {
  condition: string;
  action: string;
  parameters: any;
}

export interface PredictedOutcomes {
  expectedRetention: {
    oneWeek: number;
    oneMonth: number;
    threeMonths: number;
  };
  expectedMastery: {
    basic: number;
    intermediate: number;
    advanced: number;
  };
  timeToGoal: number;
  confidenceInterval: number;
}