<template>
  <section class="modern-card sessions-section">
    <div class="section-header">
      <h2 class="section-heading">
        <span class="title-gradient">Sessões</span>
        <span class="title-accent">Hoje</span>
        <span class="today-date">{{ formatTodayDate() }}</span>
      </h2>
      <div class="header-actions">
        <div class="view-toggle">
          <button 
            v-for="view in views" 
            :key="view.id"
            @click="currentView = view.id"
            :class="['toggle-btn', { active: currentView === view.id }]"
            :title="view.label"
          >
            <i :class="view.icon"></i>
          </button>
        </div>
        <button @click="showFilters = !showFilters" class="filter-btn" :class="{ active: showFilters }">
          <i class="fas fa-filter"></i>
        </button>
      </div>
    </div>

    <!-- Progress Overview -->
    <div class="daily-progress">
      <div class="progress-header">
        <h3>Progresso do Dia</h3>
        <span class="progress-percentage">{{ dailyProgress }}%</span>
      </div>
      <div class="progress-bar-large">
        <div class="progress-fill-large" :style="{ width: dailyProgress + '%' }">
          <div class="progress-glow"></div>
        </div>
      </div>
      <div class="progress-stats">
        <div class="progress-stat">
          <i class="fas fa-check-circle"></i>
          <span>{{ completedSessions }} de {{ totalSessions }} concluídas</span>
        </div>
        <div class="progress-stat">
          <i class="fas fa-clock"></i>
          <span>{{ totalHoursStudied }}h estudadas</span>
        </div>
        <div class="progress-stat">
          <i class="fas fa-fire"></i>
          <span>{{ currentStreak }} dias seguidos</span>
        </div>
      </div>
    </div>

    <!-- Quick Actions Bar -->
    <div class="session-quick-actions">
      <button @click="createNewSession" class="quick-action-btn primary">
        <i class="fas fa-plus-circle"></i>
        Nova Sessão
      </button>
      <button @click="startQuickSession" class="quick-action-btn">
        <i class="fas fa-bolt"></i>
        Sessão Rápida (25min)
      </button>
      <button @click="startAISession" class="quick-action-btn ai">
        <i class="fas fa-robot"></i>
        Sessão com IA
      </button>
      <button @click="showStats" class="quick-action-btn">
        <i class="fas fa-chart-pie"></i>
        Estatísticas
      </button>
    </div>

    <!-- Filters (Collapsible) -->
    <transition name="slide-down">
      <div v-if="showFilters" class="filters-section">
        <div class="filter-group">
          <label>Status:</label>
          <div class="filter-chips">
            <button 
              v-for="status in statusFilters" 
              :key="status.value"
              @click="toggleFilter('status', status.value)"
              :class="['filter-chip', { active: activeFilters.status.includes(status.value) }]"
            >
              <i :class="status.icon"></i>
              {{ status.label }}
            </button>
          </div>
        </div>
        <div class="filter-group">
          <label>Matérias:</label>
          <div class="filter-chips">
            <button 
              v-for="subject in subjects" 
              :key="subject"
              @click="toggleFilter('subject', subject)"
              :class="['filter-chip', { active: activeFilters.subject.includes(subject) }]"
            >
              {{ subject }}
            </button>
          </div>
        </div>
        <button @click="clearFilters" class="clear-filters-btn">
          <i class="fas fa-times"></i>
          Limpar Filtros
        </button>
      </div>
    </transition>

    <!-- Session Stats Bar -->
    <div class="session-stats-bar">
      <div class="stat-item">
        <i class="fas fa-brain"></i>
        <div class="stat-content">
          <span class="stat-value">{{ focusScore }}%</span>
          <span class="stat-label">Foco</span>
        </div>
        <div class="stat-trend up">
          <i class="fas fa-arrow-up"></i>
          +5%
        </div>
      </div>
      <div class="stat-item">
        <i class="fas fa-tachometer-alt"></i>
        <div class="stat-content">
          <span class="stat-value">{{ productivityScore }}</span>
          <span class="stat-label">Produtividade</span>
        </div>
        <div class="stat-trend up">
          <i class="fas fa-arrow-up"></i>
          +12%
        </div>
      </div>
      <div class="stat-item">
        <i class="fas fa-bullseye"></i>
        <div class="stat-content">
          <span class="stat-value">{{ goalsCompleted }}/{{ totalGoals }}</span>
          <span class="stat-label">Metas</span>
        </div>
      </div>
      <div class="stat-item">
        <i class="fas fa-medal"></i>
        <div class="stat-content">
          <span class="stat-value">{{ points }}</span>
          <span class="stat-label">Pontos</span>
        </div>
      </div>
    </div>

    <!-- Sessions List View -->
    <div v-if="currentView === 'list'" class="sessions-list">
      <transition-group name="session-list" tag="div">
        <div 
          v-for="session in filteredSessions" 
          :key="session.id"
          class="session-card"
          :class="{ 
            completed: session.status === 'completed',
            current: session.status === 'in-progress',
            upcoming: session.status === 'upcoming',
            paused: session.status === 'paused'
          }"
          @click="selectSession(session)"
        >
          <!-- Session Time -->
          <div class="session-time-block">
            <div class="time-range">
              <i class="fas fa-clock"></i>
              <span>{{ session.startTime }} - {{ session.endTime }}</span>
            </div>
            <div class="duration">{{ session.duration }}min</div>
          </div>

          <!-- Session Content -->
          <div class="session-content">
            <div class="session-header-info">
              <h4>{{ session.subject }}</h4>
              <div class="session-meta">
                <span class="session-topic">{{ session.topic }}</span>
                <div class="session-tags">
                  <span v-for="tag in session.tags" :key="tag" class="tag" :class="`tag-${tag.toLowerCase()}`">
                    {{ tag }}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- Progress for in-progress sessions -->
            <div v-if="session.status === 'in-progress'" class="session-progress">
              <div class="progress-info">
                <span>Em andamento</span>
                <span>{{ session.timeElapsed }}/{{ session.duration }}min</span>
              </div>
              <div class="progress-bar-mini">
                <div class="progress-fill-mini" :style="{ width: session.progressPercentage + '%' }"></div>
              </div>
            </div>

            <!-- Notes preview -->
            <div v-if="session.notes" class="session-notes-preview">
              <i class="fas fa-sticky-note"></i>
              <span>{{ session.notes }}</span>
            </div>
          </div>

          <!-- Session Actions -->
          <div class="session-actions">
            <button 
              v-if="session.status === 'upcoming'" 
              @click.stop="startSession(session)"
              class="action-btn primary"
            >
              <i class="fas fa-play"></i>
              Iniciar
            </button>
            
            <button 
              v-else-if="session.status === 'in-progress'" 
              @click.stop="pauseSession(session)"
              class="action-btn secondary"
            >
              <i class="fas fa-pause"></i>
              Pausar
            </button>
            
            <button 
              v-else-if="session.status === 'paused'" 
              @click.stop="resumeSession(session)"
              class="action-btn primary"
            >
              <i class="fas fa-play"></i>
              Retomar
            </button>
            
            <div v-else-if="session.status === 'completed'" class="completion-info">
              <div class="completion-badge">
                <i class="fas fa-check-circle"></i>
                Concluído
              </div>
              <div class="session-score">
                <i class="fas fa-star"></i>
                {{ session.score || 95 }}%
              </div>
            </div>

            <!-- Action Menu -->
            <div class="action-menu">
              <button @click.stop="toggleMenu(session.id)" class="menu-btn">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <transition name="menu-fade">
                <div v-if="openMenuId === session.id" class="menu-dropdown">
                  <button @click.stop="editSession(session)">
                    <i class="fas fa-edit"></i>
                    Editar
                  </button>
                  <button @click.stop="duplicateSession(session)">
                    <i class="fas fa-copy"></i>
                    Duplicar
                  </button>
                  <button @click.stop="rescheduleSession(session)">
                    <i class="fas fa-calendar-alt"></i>
                    Reagendar
                  </button>
                  <button @click.stop="addNotes(session)">
                    <i class="fas fa-sticky-note"></i>
                    Adicionar Notas
                  </button>
                  <button @click.stop="deleteSession(session)" class="delete">
                    <i class="fas fa-trash"></i>
                    Excluir
                  </button>
                </div>
              </transition>
            </div>
          </div>
        </div>
      </transition-group>

      <!-- Empty State -->
      <div v-if="!filteredSessions.length" class="empty-sessions">
        <div class="empty-icon">
          <i class="fas fa-calendar-day"></i>
        </div>
        <h3>{{ activeFilters.status.length || activeFilters.subject.length ? 'Nenhuma sessão encontrada' : 'Nenhuma sessão agendada' }}</h3>
        <p>{{ activeFilters.status.length || activeFilters.subject.length ? 'Tente ajustar os filtros' : 'Comece criando sua primeira sessão de estudos do dia!' }}</p>
        <button @click="createNewSession" class="btn-primary">
          <i class="fas fa-plus"></i>
          Criar Sessão
        </button>
      </div>
    </div>

    <!-- Timeline View -->
    <div v-else-if="currentView === 'timeline'" class="timeline-view">
      <div class="timeline-container">
        <div class="timeline-header">
          <div v-for="hour in timelineHours" :key="hour" class="timeline-hour">
            {{ hour }}:00
          </div>
        </div>
        <div class="timeline-content">
          <div class="timeline-track">
            <div class="current-time-indicator" :style="{ left: currentTimePosition + '%' }">
              <div class="time-marker"></div>
              <span class="time-label">{{ currentTime }}</span>
            </div>
            <div 
              v-for="session in filteredSessions" 
              :key="session.id"
              class="timeline-session"
              :class="`status-${session.status}`"
              :style="getTimelineStyle(session)"
              @click="selectSession(session)"
            >
              <div class="timeline-session-content">
                <span class="session-subject">{{ session.subject }}</span>
                <span class="session-time">{{ session.startTime }} - {{ session.endTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar View -->
    <div v-else-if="currentView === 'calendar'" class="calendar-view">
      <div class="mini-calendar">
        <div class="calendar-nav">
          <button @click="previousWeek" class="nav-btn">
            <i class="fas fa-chevron-left"></i>
          </button>
          <span class="calendar-week">{{ currentWeekRange }}</span>
          <button @click="nextWeek" class="nav-btn">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
        <div class="week-grid">
          <div v-for="day in weekDays" :key="day.date" class="day-column" :class="{ today: isToday(day.date) }">
            <div class="day-header">
              <span class="day-name">{{ day.name }}</span>
              <span class="day-number">{{ day.number }}</span>
            </div>
            <div class="day-sessions">
              <div 
                v-for="session in getSessionsForDay(day.date)" 
                :key="session.id"
                class="mini-session"
                :class="`status-${session.status}`"
                @click="selectSession(session)"
              >
                <span class="mini-time">{{ session.startTime }}</span>
                <span class="mini-subject">{{ session.subject }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <SessionModal 
      v-if="showSessionModal"
      :session="selectedSession"
      :mode="modalMode"
      @save="saveSession"
      @close="closeModal"
    />

    <StatsModal
      v-if="showStatsModal"
      :stats="sessionStats"
      @close="showStatsModal = false"
    />
  </section>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import SessionModal from './SessionModal.vue'
import StatsModal from './StatsModal.vue'

export default {
  name: 'StudySessionsAdvanced',
  components: {
    SessionModal,
    StatsModal
  },
  setup() {
    // State
    const currentView = ref('list')
    const showFilters = ref(false)
    const showSessionModal = ref(false)
    const showStatsModal = ref(false)
    const selectedSession = ref(null)
    const modalMode = ref('create')
    const openMenuId = ref(null)
    const currentTime = ref('')
    const currentTimePosition = ref(0)
    
    // Views
    const views = [
      { id: 'list', icon: 'fas fa-list', label: 'Lista' },
      { id: 'timeline', icon: 'fas fa-stream', label: 'Linha do Tempo' },
      { id: 'calendar', icon: 'fas fa-calendar-week', label: 'Calendário' }
    ]
    
    // Filters
    const activeFilters = ref({
      status: [],
      subject: []
    })
    
    const statusFilters = [
      { value: 'upcoming', label: 'Próximas', icon: 'fas fa-clock' },
      { value: 'in-progress', label: 'Em Andamento', icon: 'fas fa-play' },
      { value: 'paused', label: 'Pausadas', icon: 'fas fa-pause' },
      { value: 'completed', label: 'Concluídas', icon: 'fas fa-check' }
    ]
    
    // Sessions data
    const sessions = ref([
      {
        id: 1,
        startTime: '08:00',
        endTime: '09:30',
        duration: 90,
        subject: 'Anatomia',
        topic: 'Sistema Cardiovascular',
        tags: ['Revisão', 'Importante'],
        status: 'completed',
        score: 98,
        notes: 'Revisar artérias coronárias e circulação colateral',
        progressPercentage: 100,
        date: new Date().toISOString()
      },
      {
        id: 2,
        startTime: '10:00',
        endTime: '11:00',
        duration: 60,
        subject: 'Farmacologia',
        topic: 'Antibióticos Beta-lactâmicos',
        tags: ['Nova matéria'],
        status: 'completed',
        score: 92,
        progressPercentage: 100,
        date: new Date().toISOString()
      },
      {
        id: 3,
        startTime: '14:00',
        endTime: '15:30',
        duration: 90,
        subject: 'Neurociências',
        topic: 'Vias Sensoriais',
        tags: ['Prática'],
        status: 'in-progress',
        timeElapsed: 45,
        progressPercentage: 50,
        date: new Date().toISOString()
      },
      {
        id: 4,
        startTime: '16:00',
        endTime: '17:00',
        duration: 60,
        subject: 'Fisiologia',
        topic: 'Sistema Endócrino',
        tags: ['Revisão'],
        status: 'upcoming',
        date: new Date().toISOString()
      }
    ])
    
    // Stats
    const focusScore = ref(85)
    const productivityScore = ref(92)
    const goalsCompleted = ref(3)
    const totalGoals = ref(5)
    const points = ref(2450)
    const currentStreak = ref(7)
    
    // Timeline
    const timelineHours = ref([6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22])
    let timeUpdateInterval = null
    
    // Calendar
    const currentWeekOffset = ref(0)
    
    // Computed
    const subjects = computed(() => {
      const uniqueSubjects = new Set(sessions.value.map(s => s.subject))
      return Array.from(uniqueSubjects)
    })
    
    const filteredSessions = computed(() => {
      let filtered = sessions.value
      
      if (activeFilters.value.status.length > 0) {
        filtered = filtered.filter(s => activeFilters.value.status.includes(s.status))
      }
      
      if (activeFilters.value.subject.length > 0) {
        filtered = filtered.filter(s => activeFilters.value.subject.includes(s.subject))
      }
      
      return filtered
    })
    
    const completedSessions = computed(() => 
      sessions.value.filter(s => s.status === 'completed').length
    )
    
    const totalSessions = computed(() => sessions.value.length)
    
    const dailyProgress = computed(() => 
      totalSessions.value > 0 ? Math.round((completedSessions.value / totalSessions.value) * 100) : 0
    )
    
    const totalHoursStudied = computed(() => {
      const completedMinutes = sessions.value
        .filter(s => s.status === 'completed')
        .reduce((acc, s) => acc + s.duration, 0)
      return (completedMinutes / 60).toFixed(1)
    })
    
    const sessionStats = computed(() => ({
      totalSessions: totalSessions.value,
      completedSessions: completedSessions.value,
      totalHours: totalHoursStudied.value,
      averageScore: 95,
      focusScore: focusScore.value,
      productivityScore: productivityScore.value,
      streak: currentStreak.value
    }))
    
    const weekDays = computed(() => {
      const days = []
      const today = new Date()
      const startOfWeek = new Date(today)
      startOfWeek.setDate(today.getDate() - today.getDay() + (currentWeekOffset.value * 7))
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek)
        date.setDate(startOfWeek.getDate() + i)
        days.push({
          date: date.toISOString().split('T')[0],
          name: date.toLocaleDateString('pt-BR', { weekday: 'short' }),
          number: date.getDate()
        })
      }
      
      return days
    })
    
    const currentWeekRange = computed(() => {
      const start = weekDays.value[0]
      const end = weekDays.value[6]
      const startDate = new Date(start.date)
      const endDate = new Date(end.date)
      
      return `${startDate.getDate()} - ${endDate.getDate()} ${endDate.toLocaleDateString('pt-BR', { month: 'short' })}`
    })
    
    // Methods
    const formatTodayDate = () => {
      return new Date().toLocaleDateString('pt-BR', { 
        weekday: 'long', 
        day: 'numeric', 
        month: 'long' 
      })
    }
    
    const toggleFilter = (type, value) => {
      const index = activeFilters.value[type].indexOf(value)
      if (index > -1) {
        activeFilters.value[type].splice(index, 1)
      } else {
        activeFilters.value[type].push(value)
      }
    }
    
    const clearFilters = () => {
      activeFilters.value = {
        status: [],
        subject: []
      }
    }
    
    const createNewSession = () => {
      selectedSession.value = null
      modalMode.value = 'create'
      showSessionModal.value = true
    }
    
    const startQuickSession = () => {
      const quickSession = {
        id: Date.now(),
        startTime: new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
        endTime: '',
        duration: 25,
        subject: 'Estudo Livre',
        topic: 'Sessão Pomodoro',
        tags: ['Rápida'],
        status: 'in-progress',
        timeElapsed: 0,
        progressPercentage: 0,
        date: new Date().toISOString()
      }
      
      sessions.value.push(quickSession)
      startSession(quickSession)
    }
    
    const startAISession = () => {
      console.log('Starting AI-powered study session')
      // Implementar sessão com IA
    }
    
    const showStats = () => {
      showStatsModal.value = true
    }
    
    const selectSession = (session) => {
      selectedSession.value = session
      if (session.status === 'completed') {
        // Mostrar detalhes da sessão
        modalMode.value = 'view'
        showSessionModal.value = true
      }
    }
    
    const startSession = (session) => {
      session.status = 'in-progress'
      session.timeElapsed = 0
      session.progressPercentage = 0
      
      // Start timer
      session.timerInterval = setInterval(() => {
        session.timeElapsed++
        session.progressPercentage = Math.round((session.timeElapsed / session.duration) * 100)
        
        if (session.timeElapsed >= session.duration) {
          completeSession(session)
        }
      }, 60000) // Update every minute
    }
    
    const pauseSession = (session) => {
      session.status = 'paused'
      if (session.timerInterval) {
        clearInterval(session.timerInterval)
      }
    }
    
    const resumeSession = (session) => {
      session.status = 'in-progress'
      startSession(session)
    }
    
    const completeSession = (session) => {
      session.status = 'completed'
      session.progressPercentage = 100
      if (session.timerInterval) {
        clearInterval(session.timerInterval)
      }
      
      // Add points
      points.value += 100
      
      // Show completion notification
      console.log('Session completed:', session.subject)
    }
    
    const toggleMenu = (sessionId) => {
      openMenuId.value = openMenuId.value === sessionId ? null : sessionId
    }
    
    const editSession = (session) => {
      selectedSession.value = session
      modalMode.value = 'edit'
      showSessionModal.value = true
      openMenuId.value = null
    }
    
    const duplicateSession = (session) => {
      const duplicate = {
        ...session,
        id: Date.now(),
        status: 'upcoming',
        score: null,
        notes: '',
        progressPercentage: 0
      }
      sessions.value.push(duplicate)
      openMenuId.value = null
    }
    
    const rescheduleSession = (session) => {
      selectedSession.value = session
      modalMode.value = 'reschedule'
      showSessionModal.value = true
      openMenuId.value = null
    }
    
    const addNotes = (session) => {
      selectedSession.value = session
      modalMode.value = 'notes'
      showSessionModal.value = true
      openMenuId.value = null
    }
    
    const deleteSession = (session) => {
      if (confirm(`Deseja excluir a sessão de ${session.subject}?`)) {
        const index = sessions.value.findIndex(s => s.id === session.id)
        if (index > -1) {
          sessions.value.splice(index, 1)
        }
      }
      openMenuId.value = null
    }
    
    const saveSession = (sessionData) => {
      if (modalMode.value === 'create') {
        sessions.value.push({
          ...sessionData,
          id: Date.now(),
          status: 'upcoming',
          progressPercentage: 0
        })
      } else if (modalMode.value === 'edit') {
        const index = sessions.value.findIndex(s => s.id === sessionData.id)
        if (index > -1) {
          sessions.value[index] = { ...sessions.value[index], ...sessionData }
        }
      }
      closeModal()
    }
    
    const closeModal = () => {
      showSessionModal.value = false
      selectedSession.value = null
    }
    
    const updateCurrentTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
      
      const hours = now.getHours()
      const minutes = now.getMinutes()
      const totalMinutes = (hours - 6) * 60 + minutes
      const totalTimelineMinutes = 16 * 60 // 6am to 10pm
      currentTimePosition.value = (totalMinutes / totalTimelineMinutes) * 100
    }
    
    const getTimelineStyle = (session) => {
      const [startHour, startMin] = session.startTime.split(':').map(Number)
      const [endHour, endMin] = session.endTime.split(':').map(Number)
      
      const startMinutes = (startHour - 6) * 60 + startMin
      const endMinutes = (endHour - 6) * 60 + endMin
      const totalMinutes = 16 * 60
      
      const left = (startMinutes / totalMinutes) * 100
      const width = ((endMinutes - startMinutes) / totalMinutes) * 100
      
      return {
        left: `${left}%`,
        width: `${width}%`
      }
    }
    
    const isToday = (date) => {
      return date === new Date().toISOString().split('T')[0]
    }
    
    const getSessionsForDay = (date) => {
      return sessions.value.filter(s => s.date.split('T')[0] === date)
    }
    
    const previousWeek = () => {
      currentWeekOffset.value--
    }
    
    const nextWeek = () => {
      currentWeekOffset.value++
    }
    
    // Lifecycle
    onMounted(() => {
      updateCurrentTime()
      timeUpdateInterval = setInterval(updateCurrentTime, 60000)
      
      // Close menu when clicking outside
      document.addEventListener('click', () => {
        openMenuId.value = null
      })
    })
    
    onBeforeUnmount(() => {
      if (timeUpdateInterval) {
        clearInterval(timeUpdateInterval)
      }
      
      // Clear all session timers
      sessions.value.forEach(session => {
        if (session.timerInterval) {
          clearInterval(session.timerInterval)
        }
      })
    })
    
    return {
      // State
      currentView,
      showFilters,
      showSessionModal,
      showStatsModal,
      selectedSession,
      modalMode,
      openMenuId,
      views,
      activeFilters,
      statusFilters,
      sessions,
      filteredSessions,
      subjects,
      
      // Stats
      focusScore,
      productivityScore,
      goalsCompleted,
      totalGoals,
      points,
      currentStreak,
      completedSessions,
      totalSessions,
      dailyProgress,
      totalHoursStudied,
      sessionStats,
      
      // Timeline
      timelineHours,
      currentTime,
      currentTimePosition,
      
      // Calendar
      weekDays,
      currentWeekRange,
      
      // Methods
      formatTodayDate,
      toggleFilter,
      clearFilters,
      createNewSession,
      startQuickSession,
      startAISession,
      showStats,
      selectSession,
      startSession,
      pauseSession,
      resumeSession,
      toggleMenu,
      editSession,
      duplicateSession,
      rescheduleSession,
      addNotes,
      deleteSession,
      saveSession,
      closeModal,
      getTimelineStyle,
      isToday,
      getSessionsForDay,
      previousWeek,
      nextWeek
    }
  }
}
</script>

<style scoped>
/* Section Header */
.sessions-section {
  position: relative;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.today-date {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
  margin-left: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* View Toggle */
.view-toggle {
  display: flex;
  gap: 0.25rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem;
  border-radius: 10px;
}

.toggle-btn {
  width: 36px;
  height: 36px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.toggle-btn.active {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.filter-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-btn:hover,
.filter-btn.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  color: #667eea;
}

/* Daily Progress */
.daily-progress {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.progress-header h3 {
  margin: 0;
  font-size: 1.125rem;
  color: white;
}

.progress-percentage {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.progress-bar-large {
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill-large {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.5s ease;
  position: relative;
}

.progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 50px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
  animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressGlow {
  0% { opacity: 0; transform: translateX(-50px); }
  50% { opacity: 1; }
  100% { opacity: 0; transform: translateX(0); }
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.progress-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

.progress-stat i {
  color: #667eea;
}

/* Quick Actions */
.session-quick-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.quick-action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
}

.quick-action-btn.primary:hover {
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.quick-action-btn.ai {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  border: none;
}

/* Filters Section */
.filters-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.filter-group {
  margin-bottom: 1.5rem;
}

.filter-group label {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.75rem;
}

.filter-chips {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-chip:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.filter-chip.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  color: #667eea;
}

.clear-filters-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.813rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-filters-btn:hover {
  border-color: rgba(255, 255, 255, 0.4);
  color: white;
}

/* Session Stats Bar */
.session-stats-bar {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  margin-bottom: 2rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-item i {
  font-size: 1.5rem;
  color: #667eea;
}

.stat-content {
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.stat-trend.up {
  color: #4ade80;
}

.stat-trend.down {
  color: #f87171;
}

/* Sessions List */
.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.session-card {
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.session-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.session-card.completed {
  opacity: 0.7;
}

.session-card.current {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.5);
}

.session-card.upcoming {
  border-style: dashed;
}

.session-card.paused {
  background: rgba(251, 191, 36, 0.1);
  border-color: rgba(251, 191, 36, 0.5);
}

/* Session Time Block */
.session-time-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.duration {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Session Content */
.session-content {
  flex: 1;
}

.session-header-info h4 {
  margin: 0 0 0.5rem;
  font-size: 1.125rem;
  color: white;
}

.session-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.session-topic {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

.session-tags {
  display: flex;
  gap: 0.5rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 16px;
  font-size: 0.75rem;
  color: #667eea;
  white-space: nowrap;
}

.tag-importante {
  background: rgba(245, 87, 108, 0.2);
  color: #f5576c;
}

.tag-revisão {
  background: rgba(250, 204, 21, 0.2);
  color: #facc15;
}

.tag-prática {
  background: rgba(74, 222, 128, 0.2);
  color: #4ade80;
}

.tag-nova {
  background: rgba(240, 147, 251, 0.2);
  color: #f093fb;
}

.tag-rápida {
  background: rgba(79, 172, 254, 0.2);
  color: #4facfe;
}

/* Session Progress */
.session-progress {
  margin-top: 1rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.5rem;
}

.progress-bar-mini {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill-mini {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

/* Session Notes Preview */
.session-notes-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.6);
}

.session-notes-preview i {
  color: rgba(255, 255, 255, 0.4);
}

/* Session Actions */
.session-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  border: none;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
}

.completion-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.completion-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4ade80;
  font-size: 0.875rem;
  font-weight: 500;
}

.session-score {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #facc15;
}

/* Action Menu */
.action-menu {
  position: relative;
}

.menu-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: rgba(30, 30, 30, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.5rem;
  min-width: 180px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.menu-dropdown button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
}

.menu-dropdown button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.menu-dropdown button.delete {
  color: #f87171;
}

.menu-dropdown button.delete:hover {
  background: rgba(248, 113, 113, 0.2);
}

/* Timeline View */
.timeline-view {
  padding: 2rem 0;
}

.timeline-container {
  position: relative;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  padding: 1.5rem;
  overflow-x: auto;
}

.timeline-header {
  display: grid;
  grid-template-columns: repeat(17, 1fr);
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.timeline-hour {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.timeline-content {
  position: relative;
  min-height: 200px;
}

.timeline-track {
  position: relative;
  height: 100%;
}

.current-time-indicator {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #f5576c;
  z-index: 10;
}

.time-marker {
  position: absolute;
  top: -8px;
  left: -4px;
  width: 10px;
  height: 10px;
  background: #f5576c;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(245, 87, 108, 0.5);
}

.time-label {
  position: absolute;
  top: -25px;
  left: -20px;
  font-size: 0.75rem;
  color: #f5576c;
  font-weight: 600;
  white-space: nowrap;
}

.timeline-session {
  position: absolute;
  top: 20px;
  height: 60px;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.5);
  border-radius: 12px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-session:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
}

.timeline-session.status-completed {
  background: rgba(74, 222, 128, 0.2);
  border-color: rgba(74, 222, 128, 0.5);
}

.timeline-session.status-in-progress {
  background: rgba(102, 126, 234, 0.3);
  border-color: rgba(102, 126, 234, 0.7);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.timeline-session-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.session-subject {
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

.session-time {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Calendar View */
.calendar-view {
  padding: 1rem 0;
}

.mini-calendar {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  padding: 1.5rem;
}

.calendar-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.nav-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.calendar-week {
  font-size: 1rem;
  font-weight: 600;
  color: white;
}

.week-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1rem;
}

.day-column {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  min-height: 200px;
}

.day-column.today {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.5);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.day-name {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
}

.day-number {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
}

.day-sessions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mini-session {
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mini-session:hover {
  background: rgba(255, 255, 255, 0.1);
}

.mini-session.status-completed {
  opacity: 0.6;
}

.mini-time {
  display: block;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.25rem;
}

.mini-subject {
  display: block;
  font-size: 0.813rem;
  color: white;
  font-weight: 500;
}

/* Empty State */
.empty-sessions {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-icon {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.2);
  margin-bottom: 1rem;
}

.empty-sessions h3 {
  font-size: 1.5rem;
  margin: 0 0 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.empty-sessions p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 1.5rem;
}

.btn-primary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* Transitions */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.session-list-enter-active,
.session-list-leave-active {
  transition: all 0.3s ease;
}

.session-list-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.session-list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.menu-fade-enter-active,
.menu-fade-leave-active {
  transition: all 0.2s ease;
}

.menu-fade-enter-from,
.menu-fade-leave-to {
  opacity: 0;
  transform: translateY(-5px);
}
</style>