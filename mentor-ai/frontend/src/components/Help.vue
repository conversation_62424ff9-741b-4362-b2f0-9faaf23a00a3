<template>
  <div class="help-container">
    <!-- Hero Section -->
    <div class="help-hero">
      <div class="hero-background">
        <div class="floating-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
        </div>
      </div>
      
      <div class="hero-content">
        <h1 class="hero-title">
          Central de <span class="highlight">Ajuda</span>
        </h1>
        <p class="hero-subtitle">
          Encontre respostas rápidas e aprenda a usar todas as funcionalidades da plataforma
        </p>
        
        <!-- Search Bar -->
        <div class="search-container">
          <div class="search-wrapper">
            <font-awesome-icon icon="fa-solid fa-search" class="search-icon" />
            <input 
              v-model="searchQuery"
              type="text" 
              placeholder="Buscar por tópicos, ferramentas ou palavras-chave..."
              class="search-input"
              @input="filterContent"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Links -->
    <section class="quick-links">
      <div class="container">
        <div class="links-grid">
          <a href="#getting-started" class="quick-link-card">
            <div class="link-icon">
              <font-awesome-icon icon="fa-solid fa-rocket" />
            </div>
            <h3>Primeiros Passos</h3>
            <p>Comece sua jornada aqui</p>
          </a>
          
          <a href="#features" class="quick-link-card">
            <div class="link-icon">
              <font-awesome-icon icon="fa-solid fa-tools" />
            </div>
            <h3>Funcionalidades</h3>
            <p>Explore todas as ferramentas</p>
          </a>
          
          <a href="#faq" class="quick-link-card">
            <div class="link-icon">
              <font-awesome-icon icon="fa-solid fa-question-circle" />
            </div>
            <h3>Perguntas Frequentes</h3>
            <p>Respostas para dúvidas comuns</p>
          </a>
          
          <a href="#shortcuts" class="quick-link-card">
            <div class="link-icon">
              <font-awesome-icon icon="fa-solid fa-keyboard" />
            </div>
            <h3>Atalhos</h3>
            <p>Teclas de atalho úteis</p>
          </a>
        </div>
      </div>
    </section>

    <!-- Main Content -->
    <div class="help-content">
      <div class="container">
        <!-- Getting Started Section -->
        <section id="getting-started" class="content-section">
          <h2 class="section-title">
            <font-awesome-icon icon="fa-solid fa-rocket" />
            Primeiros Passos
          </h2>
          
          <div class="content-grid">
            <div class="content-card">
              <h3>1. Bem-vindo ao Sophos Academy</h3>
              <p>
                O Sophos Academy é uma plataforma completa de estudos médicos que combina 
                inteligência artificial com métodos comprovados de aprendizagem.
              </p>
              <ul>
                <li>Sistema de flashcards com IA</li>
                <li>Second Brain para gestão de conhecimento</li>
                <li>Análise inteligente de exames</li>
                <li>Ferramentas de IA especializadas</li>
              </ul>
            </div>
            
            <div class="content-card">
              <h3>2. Configurando seu Perfil</h3>
              <p>Personalize sua experiência:</p>
              <ul>
                <li>Acesse <strong>Configurações</strong> no menu do usuário</li>
                <li>Defina suas preferências de estudo</li>
                <li>Configure notificações e lembretes</li>
                <li>Escolha seu tema (claro/escuro)</li>
              </ul>
            </div>
            
            <div class="content-card">
              <h3>3. Navegando pela Plataforma</h3>
              <p>Use o menu principal para acessar:</p>
              <ul>
                <li><strong>ESTUDO:</strong> Flashcards, Second Brain, Simulados</li>
                <li><strong>IA TOOLS:</strong> Todas as ferramentas de IA</li>
                <li><strong>CONFIGURAÇÕES:</strong> Perfil e preferências</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="content-section">
          <h2 class="section-title">
            <font-awesome-icon icon="fa-solid fa-tools" />
            Funcionalidades Principais
          </h2>
          
          <div class="features-accordion">
            <div 
              v-for="(feature, index) in features" 
              :key="index"
              class="accordion-item"
              :class="{ active: activeAccordion === index }"
            >
              <div class="accordion-header" @click="toggleAccordion(index)">
                <div class="header-content">
                  <font-awesome-icon :icon="feature.icon" class="feature-icon" />
                  <h3>{{ feature.title }}</h3>
                </div>
                <font-awesome-icon 
                  :icon="activeAccordion === index ? 'fa-solid fa-chevron-up' : 'fa-solid fa-chevron-down'" 
                  class="chevron"
                />
              </div>
              
              <transition name="accordion">
                <div v-if="activeAccordion === index" class="accordion-content">
                  <p>{{ feature.description }}</p>
                  <div class="feature-details">
                    <h4>Como usar:</h4>
                    <ol>
                      <li v-for="(step, stepIndex) in feature.steps" :key="stepIndex">
                        {{ step }}
                      </li>
                    </ol>
                    <div v-if="feature.tips" class="tips-box">
                      <h4><font-awesome-icon icon="fa-solid fa-lightbulb" /> Dicas:</h4>
                      <ul>
                        <li v-for="(tip, tipIndex) in feature.tips" :key="tipIndex">
                          {{ tip }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </transition>
            </div>
          </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="content-section">
          <h2 class="section-title">
            <font-awesome-icon icon="fa-solid fa-question-circle" />
            Perguntas Frequentes
          </h2>
          
          <div class="faq-grid">
            <div v-for="(faq, index) in filteredFaqs" :key="index" class="faq-item">
              <div class="faq-question">
                <font-awesome-icon icon="fa-solid fa-question" class="q-icon" />
                <h4>{{ faq.question }}</h4>
              </div>
              <div class="faq-answer">
                <p>{{ faq.answer }}</p>
              </div>
            </div>
          </div>
        </section>

        <!-- Shortcuts Section -->
        <section id="shortcuts" class="content-section">
          <h2 class="section-title">
            <font-awesome-icon icon="fa-solid fa-keyboard" />
            Atalhos de Teclado
          </h2>
          
          <div class="shortcuts-grid">
            <div class="shortcut-category">
              <h3>Navegação Geral</h3>
              <div class="shortcuts-list">
                <div class="shortcut-item">
                  <span class="keys">
                    <kbd>Ctrl</kbd> + <kbd>K</kbd>
                  </span>
                  <span class="description">Busca rápida</span>
                </div>
                <div class="shortcut-item">
                  <span class="keys">
                    <kbd>Esc</kbd>
                  </span>
                  <span class="description">Fechar modal/menu</span>
                </div>
                <div class="shortcut-item">
                  <span class="keys">
                    <kbd>?</kbd>
                  </span>
                  <span class="description">Abrir ajuda</span>
                </div>
              </div>
            </div>
            
            <div class="shortcut-category">
              <h3>Flashcards</h3>
              <div class="shortcuts-list">
                <div class="shortcut-item">
                  <span class="keys">
                    <kbd>Espaço</kbd>
                  </span>
                  <span class="description">Virar card</span>
                </div>
                <div class="shortcut-item">
                  <span class="keys">
                    <kbd>1</kbd> - <kbd>4</kbd>
                  </span>
                  <span class="description">Classificar dificuldade</span>
                </div>
                <div class="shortcut-item">
                  <span class="keys">
                    <kbd>→</kbd>
                  </span>
                  <span class="description">Próximo card</span>
                </div>
              </div>
            </div>
            
            <div class="shortcut-category">
              <h3>Editor de Texto</h3>
              <div class="shortcuts-list">
                <div class="shortcut-item">
                  <span class="keys">
                    <kbd>Ctrl</kbd> + <kbd>B</kbd>
                  </span>
                  <span class="description">Negrito</span>
                </div>
                <div class="shortcut-item">
                  <span class="keys">
                    <kbd>Ctrl</kbd> + <kbd>I</kbd>
                  </span>
                  <span class="description">Itálico</span>
                </div>
                <div class="shortcut-item">
                  <span class="keys">
                    <kbd>Ctrl</kbd> + <kbd>S</kbd>
                  </span>
                  <span class="description">Salvar</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Support Section -->
        <section id="support" class="content-section">
          <h2 class="section-title">
            <font-awesome-icon icon="fa-solid fa-headset" />
            Suporte
          </h2>
          
          <div class="support-options">
            <div class="support-card">
              <div class="support-icon">
                <font-awesome-icon icon="fa-solid fa-envelope" />
              </div>
              <h3>Email</h3>
              <p>Envie suas dúvidas para:</p>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
            
            <div class="support-card">
              <div class="support-icon">
                <font-awesome-icon icon="fa-solid fa-comments" />
              </div>
              <h3>Chat ao Vivo</h3>
              <p>Segunda a Sexta: 9h às 18h</p>
              <button class="chat-button">Iniciar Chat</button>
            </div>
            
            <div class="support-card">
              <div class="support-icon">
                <font-awesome-icon icon="fa-solid fa-book" />
              </div>
              <h3>Documentação</h3>
              <p>Guias detalhados e tutoriais</p>
              <a href="#" class="docs-link">Acessar Docs</a>
            </div>
          </div>
        </section>

        <!-- Video Tutorials -->
        <section id="tutorials" class="content-section">
          <h2 class="section-title">
            <font-awesome-icon icon="fa-solid fa-video" />
            Tutoriais em Vídeo
          </h2>
          
          <div class="tutorials-grid">
            <div class="tutorial-card">
              <div class="video-placeholder">
                <font-awesome-icon icon="fa-solid fa-play-circle" />
              </div>
              <h4>Tour pela Plataforma</h4>
              <p>5 min • Introdução completa</p>
            </div>
            
            <div class="tutorial-card">
              <div class="video-placeholder">
                <font-awesome-icon icon="fa-solid fa-play-circle" />
              </div>
              <h4>Dominando os Flashcards</h4>
              <p>8 min • Técnicas avançadas</p>
            </div>
            
            <div class="tutorial-card">
              <div class="video-placeholder">
                <font-awesome-icon icon="fa-solid fa-play-circle" />
              </div>
              <h4>Second Brain Explicado</h4>
              <p>10 min • Gestão de conhecimento</p>
            </div>
          </div>
        </section>
      </div>
    </div>

    <!-- Floating Help Button -->
    <button class="floating-help" @click="scrollToTop">
      <font-awesome-icon icon="fa-solid fa-arrow-up" />
    </button>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';

export default {
  name: 'Help',
  setup() {
    const searchQuery = ref('');
    const activeAccordion = ref(null);
    
    // Features data
    const features = ref([
      {
        icon: 'fa-solid fa-clone',
        title: 'FlashMastery Pro',
        description: 'Sistema avançado de flashcards com algoritmos de aprendizado neural e repetição espaçada.',
        steps: [
          'Acesse "Flashcards" no menu ESTUDO',
          'Crie um novo deck ou selecione um existente',
          'Adicione cards manualmente ou use a IA para gerar',
          'Inicie uma sessão de estudo',
          'Classifique cada card de acordo com a dificuldade'
        ],
        tips: [
          'Use a IA para gerar flashcards de PDFs ou textos',
          'Revise cards diariamente para melhor retenção',
          'Analise suas estatísticas de desempenho regularmente'
        ]
      },
      {
        icon: 'fa-solid fa-brain',
        title: 'Second Brain',
        description: 'Seu segundo cérebro digital para armazenar, conectar e recuperar conhecimento médico.',
        steps: [
          'Acesse "Second Brain" no menu',
          'Crie notas ou importe documentos',
          'Use tags para organizar conteúdo',
          'Faça perguntas à IA sobre seu conhecimento',
          'Conecte ideias relacionadas'
        ],
        tips: [
          'Mantenha notas concisas e bem organizadas',
          'Use a busca semântica para encontrar informações',
          'Revise e atualize suas notas regularmente'
        ]
      },
      {
        icon: 'fa-solid fa-microscope',
        title: 'Análise de Exames',
        description: 'Ferramenta de IA para interpretar e analisar exames laboratoriais.',
        steps: [
          'Vá para "Análise de Exames"',
          'Faça upload da imagem ou PDF do exame',
          'Aguarde o processamento da IA',
          'Revise a interpretação gerada',
          'Salve ou exporte o relatório'
        ],
        tips: [
          'Garanta que as imagens estejam nítidas',
          'Sempre confirme com um profissional',
          'Use como ferramenta de estudo e apoio'
        ]
      },
      {
        icon: 'fa-solid fa-robot',
        title: 'Ferramentas de IA',
        description: 'Conjunto completo de ferramentas de inteligência artificial para estudos médicos.',
        steps: [
          'Acesse "IA TOOLS" no menu principal',
          'Escolha a ferramenta desejada',
          'Siga as instruções específicas de cada tool',
          'Salve ou exporte os resultados'
        ],
        tips: [
          'Explore todas as ferramentas disponíveis',
          'Combine diferentes tools para melhores resultados',
          'Aproveite os créditos de IA com sabedoria'
        ]
      }
    ]);

    // FAQ data
    const faqs = ref([
      {
        question: 'Como faço para resetar minha senha?',
        answer: 'Clique em "Esqueci minha senha" na tela de login. Digite seu email cadastrado e siga as instruções enviadas para redefinir sua senha.'
      },
      {
        question: 'Posso usar a plataforma offline?',
        answer: 'Algumas funcionalidades básicas funcionam offline, mas recursos de IA e sincronização requerem conexão com a internet.'
      },
      {
        question: 'Como exporto meus flashcards?',
        answer: 'Vá para a página de Flashcards, selecione o deck desejado, clique no menu (três pontos) e escolha "Exportar". Você pode exportar em formatos PDF, CSV ou JSON.'
      },
      {
        question: 'Há limite de uso para as ferramentas de IA?',
        answer: 'Sim, cada plano tem um limite mensal de créditos de IA. Você pode ver seu saldo atual no canto superior direito da tela.'
      },
      {
        question: 'Como funciona o algoritmo de repetição espaçada?',
        answer: 'Nosso algoritmo usa redes neurais para prever o momento ideal de revisão baseado em seu desempenho, garantindo máxima retenção com mínimo esforço.'
      },
      {
        question: 'Posso compartilhar meus decks com outros usuários?',
        answer: 'Sim! Cada deck tem opções de compartilhamento. Você pode gerar um link de compartilhamento ou convidar usuários específicos por email.'
      },
      {
        question: 'Como faço backup dos meus dados?',
        answer: 'Acesse Configurações > Backup e Sincronização. Você pode configurar backups automáticos ou fazer backup manual a qualquer momento.'
      },
      {
        question: 'A plataforma funciona em dispositivos móveis?',
        answer: 'Sim, o Sophos Academy é totalmente responsivo e funciona em smartphones e tablets. Também temos apps nativos em desenvolvimento.'
      }
    ]);

    // Computed
    const filteredFaqs = computed(() => {
      if (!searchQuery.value) return faqs.value;
      
      const query = searchQuery.value.toLowerCase();
      return faqs.value.filter(faq => 
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
      );
    });

    // Methods
    const toggleAccordion = (index) => {
      activeAccordion.value = activeAccordion.value === index ? null : index;
    };

    const filterContent = () => {
      // Implement content filtering based on search
    };

    const scrollToTop = () => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    // Smooth scroll for anchor links
    onMounted(() => {
      const links = document.querySelectorAll('a[href^="#"]');
      links.forEach(link => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const targetId = link.getAttribute('href').substring(1);
          const targetElement = document.getElementById(targetId);
          if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth' });
          }
        });
      });
    });

    return {
      searchQuery,
      activeAccordion,
      features,
      faqs,
      filteredFaqs,
      toggleAccordion,
      filterContent,
      scrollToTop
    };
  }
};
</script>

<style scoped>
/* Container */
.help-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
}

/* Hero Section */
.help-hero {
  position: relative;
  padding: 6rem 2rem 4rem;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(78, 205, 196, 0.1), rgba(102, 126, 234, 0.1));
  filter: blur(40px);
  animation: float 20s infinite ease-in-out;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  left: -100px;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 50%;
  right: -100px;
  animation-delay: -5s;
}

.shape-3 {
  width: 250px;
  height: 250px;
  bottom: -125px;
  left: 50%;
  animation-delay: -10s;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
  line-height: 1.2;
}

.highlight {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 3rem;
  line-height: 1.6;
}

/* Search */
.search-container {
  max-width: 600px;
  margin: 0 auto;
}

.search-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 100px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 0;
  transition: all 0.3s ease;
}

.search-wrapper:focus-within {
  border-color: rgba(78, 205, 196, 0.5);
  box-shadow: 0 0 0 4px rgba(78, 205, 196, 0.1);
}

.search-icon {
  position: absolute;
  left: 2rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.2rem;
}

.search-input {
  width: 100%;
  padding: 1.2rem 2rem 1.2rem 4rem;
  background: transparent;
  border: none;
  color: white;
  font-size: 1.1rem;
  outline: none;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Quick Links */
.quick-links {
  padding: 4rem 2rem;
  background: rgba(0, 0, 0, 0.3);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.quick-link-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  text-decoration: none;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.quick-link-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(102, 126, 234, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quick-link-card:hover::before {
  opacity: 1;
}

.quick-link-card:hover {
  transform: translateY(-5px);
  border-color: rgba(78, 205, 196, 0.3);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.link-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.2), rgba(102, 126, 234, 0.2));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: #4ecdc4;
  transition: all 0.3s ease;
}

.quick-link-card:hover .link-icon {
  transform: scale(1.1);
  box-shadow: 0 5px 20px rgba(78, 205, 196, 0.3);
}

.quick-link-card h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.quick-link-card p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
}

/* Content Sections */
.help-content {
  padding: 4rem 2rem;
}

.content-section {
  margin-bottom: 6rem;
}

.section-title {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 3rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
}

.section-title svg {
  color: #4ecdc4;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.content-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.content-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(78, 205, 196, 0.2);
  transform: translateY(-2px);
}

.content-card h3 {
  color: white;
  font-size: 1.4rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.content-card p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.content-card ul {
  list-style: none;
  padding: 0;
}

.content-card li {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem 0;
  padding-left: 1.5rem;
  position: relative;
}

.content-card li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #4ecdc4;
  font-weight: bold;
}

/* Accordion */
.features-accordion {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.accordion-item {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.accordion-item.active {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(78, 205, 196, 0.3);
}

.accordion-header {
  padding: 1.5rem 2rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.accordion-header:hover {
  background: rgba(255, 255, 255, 0.03);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.feature-icon {
  font-size: 1.5rem;
  color: #4ecdc4;
}

.accordion-header h3 {
  margin: 0;
  font-size: 1.3rem;
  color: white;
  font-weight: 600;
}

.chevron {
  color: rgba(255, 255, 255, 0.6);
  transition: transform 0.3s ease;
}

.accordion-content {
  padding: 0 2rem 2rem;
}

.feature-details {
  color: rgba(255, 255, 255, 0.8);
}

.feature-details h4 {
  color: white;
  margin: 1.5rem 0 1rem;
  font-size: 1.1rem;
}

.feature-details ol {
  padding-left: 1.5rem;
}

.feature-details li {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.tips-box {
  background: rgba(78, 205, 196, 0.1);
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 10px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.tips-box h4 {
  color: #4ecdc4;
  margin-top: 0;
}

.tips-box ul {
  list-style: none;
  padding: 0;
}

.tips-box li {
  padding: 0.5rem 0;
  padding-left: 1.5rem;
  position: relative;
}

.tips-box li::before {
  content: '💡';
  position: absolute;
  left: 0;
}

/* FAQ */
.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.faq-item {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.faq-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(78, 205, 196, 0.2);
  transform: translateY(-2px);
}

.faq-question {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.q-icon {
  color: #4ecdc4;
  font-size: 1.2rem;
  margin-top: 0.2rem;
}

.faq-question h4 {
  margin: 0;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.4;
}

.faq-answer p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

/* Shortcuts */
.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.shortcut-category {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
}

.shortcut-category h3 {
  color: white;
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  transition: all 0.2s ease;
}

.shortcut-item:hover {
  background: rgba(78, 205, 196, 0.1);
}

.keys {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

kbd {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  padding: 0.3rem 0.6rem;
  font-family: monospace;
  font-size: 0.9rem;
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
}

/* Support */
.support-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.support-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.support-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(78, 205, 196, 0.2);
  transform: translateY(-5px);
}

.support-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.2), rgba(102, 126, 234, 0.2));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #4ecdc4;
}

.support-card h3 {
  color: white;
  font-size: 1.4rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.support-card p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
}

.support-card a {
  color: #4ecdc4;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
}

.support-card a:hover {
  color: #667eea;
}

.chat-button {
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chat-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(78, 205, 196, 0.3);
}

.docs-link {
  display: inline-block;
  padding: 0.8rem 2rem;
  background: rgba(78, 205, 196, 0.1);
  border: 2px solid rgba(78, 205, 196, 0.3);
  border-radius: 50px;
  transition: all 0.3s ease;
}

.docs-link:hover {
  background: rgba(78, 205, 196, 0.2);
  border-color: rgba(78, 205, 196, 0.5);
}

/* Tutorials */
.tutorials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.tutorial-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tutorial-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(78, 205, 196, 0.2);
  transform: translateY(-5px);
}

.video-placeholder {
  aspect-ratio: 16/9;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.tutorial-card:hover .video-placeholder {
  color: #4ecdc4;
}

.tutorial-card h4 {
  color: white;
  font-size: 1.2rem;
  margin: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.tutorial-card p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 1.5rem 1.5rem;
  font-size: 0.95rem;
}

/* Floating Help Button */
.floating-help {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #4ecdc4, #667eea);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 5px 20px rgba(78, 205, 196, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
}

.floating-help:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(78, 205, 196, 0.4);
}

/* Animations */
.accordion-enter-active,
.accordion-leave-active {
  transition: all 0.3s ease;
  max-height: 500px;
  overflow: hidden;
}

.accordion-enter-from,
.accordion-leave-to {
  max-height: 0;
  opacity: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .links-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
  
  .content-grid,
  .faq-grid {
    grid-template-columns: 1fr;
  }
  
  .shortcuts-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
  
  .quick-link-card {
    padding: 1.5rem;
  }
  
  .content-card,
  .faq-item,
  .shortcut-category {
    padding: 1.5rem;
  }
}
</style>