<template>
  <div class="pomodoro-page">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Modern Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <i class="fas fa-clock"></i>
            <div class="icon-glow"></div>
          </div>
          <div class="page-info">
            <h1 class="page-title">Ultra Pomodoro Timer</h1>
            <p class="page-subtitle">Maximize sua produtividade com técnica comprovada</p>
          </div>
        </div>
        <div class="header-right">
          <div class="header-stats">
            <div class="stat-item" v-for="stat in headerStats" :key="stat.id">
              <div class="stat-icon" :style="{ color: stat.color }">
                <i :class="stat.icon"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value">{{ stat.value }}</span>
                <span class="stat-label">{{ stat.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Timer Section -->
      <div class="timer-section">
        <div class="timer-card modern-card">
          <!-- Session Info -->
          <div class="session-header">
            <div class="session-badge" :class="sessionTypeClass">
              <i :class="sessionIcon"></i>
              <span>{{ currentSessionType }}</span>
            </div>
            <div class="session-counter">
              <span>Sessão</span>
              <span class="session-number">#{{ completedSessions + 1 }}</span>
            </div>
          </div>

          <!-- Timer Display -->
          <div class="timer-display-wrapper">
            <svg class="progress-ring" :width="circleSize" :height="circleSize">
              <defs>
                <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" :style="`stop-color:${progressColor1}`" />
                  <stop offset="100%" :style="`stop-color:${progressColor2}`" />
                </linearGradient>
              </defs>
              <circle
                class="progress-ring-bg"
                :r="radius"
                :cx="circleSize/2"
                :cy="circleSize/2"
                :stroke-width="strokeWidth"
              />
              <circle
                class="progress-ring-bar"
                :r="radius"
                :cx="circleSize/2"
                :cy="circleSize/2"
                :stroke-width="strokeWidth"
                :stroke-dasharray="circumference"
                :stroke-dashoffset="strokeDashoffset"
                stroke="url(#progressGradient)"
              />
            </svg>
            
            <div class="timer-content">
              <div class="time-display">
                <span class="time-digit">{{ displayMinutes }}</span>
                <span class="time-separator" :class="{ blink: isRunning }">:</span>
                <span class="time-digit">{{ displaySeconds }}</span>
              </div>
              
              <div class="timer-controls">
                <button 
                  @click="toggleTimer" 
                  class="control-btn primary"
                  :class="{ 'pulse-animation': !isRunning && timeLeft === duration }"
                >
                  <div class="btn-content">
                    <i v-if="!isRunning && timeLeft === duration" class="fas fa-play"></i>
                    <i v-else-if="!isRunning && timeLeft < duration" class="fas fa-play"></i>
                    <i v-else class="fas fa-pause"></i>
                    <span>{{ timerButtonText }}</span>
                  </div>
                  <div class="btn-glow"></div>
                </button>
                
                <button @click="resetTimer" class="control-btn secondary">
                  <i class="fas fa-redo"></i>
                  <span>Resetar</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Quick Time Adjustments -->
          <div class="quick-adjustments">
            <div class="adjustment-group" v-for="adj in timeAdjustments" :key="adj.type">
              <div class="adjustment-header">
                <i :class="adj.icon"></i>
                <span>{{ adj.label }}</span>
              </div>
              <div class="time-selector">
                <button 
                  @click="adjustTime(adj.type, -adj.step)" 
                  class="adjust-btn"
                  :disabled="isRunning"
                >
                  <i class="fas fa-minus"></i>
                </button>
                <div class="time-value">
                  <span class="value">{{ settings[adj.setting] }}</span>
                  <span class="unit">min</span>
                </div>
                <button 
                  @click="adjustTime(adj.type, adj.step)" 
                  class="adjust-btn"
                  :disabled="isRunning"
                >
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
          <div class="stat-card modern-card" v-for="stat in statistics" :key="stat.id">
            <div class="card-background" :style="{ background: `linear-gradient(135deg, ${stat.color}20, transparent)` }"></div>
            <div class="card-content">
              <div class="card-icon" :style="{ backgroundColor: stat.color + '15' }">
                <i :class="stat.icon" :style="{ color: stat.color }"></i>
              </div>
              <div class="card-info">
                <h3>{{ stat.value }}</h3>
                <p>{{ stat.label }}</p>
              </div>
              <div class="card-decoration">
                <div class="decoration-line"></div>
                <div class="decoration-dot"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Section -->
      <div class="right-section">
        <!-- Tasks Card -->
        <div class="tasks-card modern-card">
          <div class="card-header">
            <h2>
              <i class="fas fa-tasks"></i>
              Tarefas da Sessão
            </h2>
            <span class="task-count">{{ tasks.length }} tarefas</span>
          </div>
          
          <div class="task-input-wrapper">
            <div class="input-icon">
              <i class="fas fa-plus"></i>
            </div>
            <input 
              v-model="newTask"
              @keyup.enter="addTask"
              type="text" 
              placeholder="Adicionar nova tarefa..."
              class="modern-input"
            >
            <button @click="addTask" class="add-btn" :disabled="!newTask.trim()">
              <i class="fas fa-arrow-right"></i>
            </button>
          </div>
          
          <div class="tasks-list">
            <transition-group name="task-list">
              <div 
                v-for="(task, index) in tasks" 
                :key="task.id"
                class="task-item"
                :class="{ completed: task.completed }"
              >
                <label class="task-checkbox-wrapper">
                  <input 
                    type="checkbox" 
                    v-model="task.completed"
                    class="task-checkbox"
                  >
                  <div class="checkbox-custom">
                    <i class="fas fa-check"></i>
                  </div>
                </label>
                <span class="task-text">{{ task.text }}</span>
                <button @click="removeTask(index)" class="remove-btn">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </transition-group>
            
            <div v-if="tasks.length === 0" class="empty-tasks">
              <i class="fas fa-clipboard-list"></i>
              <p>Nenhuma tarefa adicionada</p>
            </div>
          </div>
        </div>

        <!-- Settings Card -->
        <div class="settings-card modern-card">
          <div class="card-header">
            <h2>
              <i class="fas fa-cog"></i>
              Configurações
            </h2>
            <button @click="showAdvanced = !showAdvanced" class="toggle-btn">
              <i :class="showAdvanced ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
            </button>
          </div>
          
          <div class="settings-content" :class="{ expanded: showAdvanced }">
            <div class="setting-item" v-for="setting in advancedSettings" :key="setting.id">
              <label class="setting-label">
                <div class="setting-info">
                  <i :class="setting.icon"></i>
                  <span>{{ setting.label }}</span>
                </div>
                <div class="setting-control">
                  <input 
                    v-if="setting.type === 'checkbox'"
                    type="checkbox" 
                    v-model="settings[setting.model]"
                    class="modern-checkbox"
                  >
                  <input 
                    v-else-if="setting.type === 'number'"
                    type="number" 
                    v-model.number="settings[setting.model]"
                    :min="setting.min" 
                    :max="setting.max"
                    class="modern-number-input"
                  >
                  <div v-if="setting.type === 'checkbox'" class="checkbox-slider"></div>
                </div>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Motivational Toast -->
    <transition name="toast">
      <div v-if="showMotivation" class="motivation-toast">
        <div class="toast-icon">
          <i class="fas fa-fire"></i>
        </div>
        <div class="toast-content">
          <p>{{ currentMotivation }}</p>
        </div>
        <div class="toast-decoration"></div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'UltraPomodoroTimer',
  data() {
    return {
      // Timer state
      isRunning: false,
      timeLeft: 25 * 60,
      duration: 25 * 60,
      sessionType: 'work',
      circleSize: 280,
      radius: 120,
      strokeWidth: 12,
      
      // Settings
      settings: {
        workDuration: 25,
        shortBreakDuration: 5,
        longBreakDuration: 15,
        autoStartBreaks: true,
        autoStartPomodoros: false,
        soundEnabled: true,
        notificationsEnabled: true,
        sessionsUntilLongBreak: 4
      },
      
      // Statistics
      completedSessions: 0,
      totalFocusTime: 0,
      currentStreak: 0,
      lastActiveDate: null,
      
      // UI state
      showAdvanced: false,
      showMotivation: false,
      currentMotivation: '',
      
      // Tasks
      newTask: '',
      tasks: [],
      
      // Timer interval
      timerInterval: null,
      
      // Motivational quotes
      motivationalQuotes: [
        "Excelente foco! Continue assim! 🔥",
        "Você está arrasando! Mantenha o ritmo! 💪",
        "Produtividade máxima ativada! 🚀",
        "Cada minuto conta! Você consegue! ⭐",
        "Pausa merecida! Descanse e volte forte! 🌟",
        "Incrível progresso! Continue brilhando! ✨",
        "Foco de campeão! Nada pode te parar! 🏆",
        "Energia renovada! Vamos com tudo! ⚡"
      ]
    }
  },
  
  computed: {
    displayMinutes() {
      return Math.floor(this.timeLeft / 60).toString().padStart(2, '0');
    },
    
    displaySeconds() {
      return (this.timeLeft % 60).toString().padStart(2, '0');
    },
    
    currentSessionType() {
      switch(this.sessionType) {
        case 'work': return 'Foco Intenso';
        case 'shortBreak': return 'Pausa Curta';
        case 'longBreak': return 'Pausa Longa';
        default: return '';
      }
    },
    
    sessionIcon() {
      switch(this.sessionType) {
        case 'work': return 'fas fa-brain';
        case 'shortBreak': return 'fas fa-coffee';
        case 'longBreak': return 'fas fa-couch';
        default: return '';
      }
    },
    
    sessionTypeClass() {
      return `session-${this.sessionType}`;
    },
    
    progressColor1() {
      return this.sessionType === 'work' ? '#6366f1' : '#10b981';
    },
    
    progressColor2() {
      return this.sessionType === 'work' ? '#8b5cf6' : '#14b8a6';
    },
    
    timerButtonText() {
      if (!this.isRunning && this.timeLeft === this.duration) return 'Iniciar';
      if (!this.isRunning && this.timeLeft < this.duration) return 'Continuar';
      return 'Pausar';
    },
    
    circumference() {
      return 2 * Math.PI * this.radius;
    },
    
    strokeDashoffset() {
      const progress = this.timeLeft / this.duration;
      return this.circumference * (1 - progress);
    },
    
    headerStats() {
      return [
        {
          id: 1,
          icon: 'fas fa-fire',
          value: this.currentStreak,
          label: 'Dias de sequência',
          color: '#f59e0b'
        },
        {
          id: 2,
          icon: 'fas fa-clock',
          value: `${this.totalFocusTime}min`,
          label: 'Tempo total',
          color: '#6366f1'
        }
      ];
    },
    
    statistics() {
      return [
        {
          id: 1,
          icon: 'fas fa-check-circle',
          value: this.completedSessions,
          label: 'Sessões Completas',
          color: '#10b981'
        },
        {
          id: 2,
          icon: 'fas fa-hourglass-half',
          value: `${this.totalFocusTime}min`,
          label: 'Tempo de Foco',
          color: '#6366f1'
        },
        {
          id: 3,
          icon: 'fas fa-fire-alt',
          value: this.currentStreak,
          label: 'Sequência Atual',
          color: '#f59e0b'
        }
      ];
    },
    
    timeAdjustments() {
      return [
        {
          type: 'work',
          label: 'Tempo de Foco',
          icon: 'fas fa-brain',
          setting: 'workDuration',
          step: 5
        },
        {
          type: 'shortBreak',
          label: 'Pausa Curta',
          icon: 'fas fa-coffee',
          setting: 'shortBreakDuration',
          step: 1
        },
        {
          type: 'longBreak',
          label: 'Pausa Longa',
          icon: 'fas fa-couch',
          setting: 'longBreakDuration',
          step: 5
        }
      ];
    },
    
    advancedSettings() {
      return [
        {
          id: 1,
          type: 'checkbox',
          model: 'autoStartBreaks',
          label: 'Auto-iniciar pausas',
          icon: 'fas fa-play-circle'
        },
        {
          id: 2,
          type: 'checkbox',
          model: 'autoStartPomodoros',
          label: 'Auto-iniciar próxima sessão',
          icon: 'fas fa-forward'
        },
        {
          id: 3,
          type: 'checkbox',
          model: 'soundEnabled',
          label: 'Sons habilitados',
          icon: 'fas fa-volume-up'
        },
        {
          id: 4,
          type: 'checkbox',
          model: 'notificationsEnabled',
          label: 'Notificações do navegador',
          icon: 'fas fa-bell'
        },
        {
          id: 5,
          type: 'number',
          model: 'sessionsUntilLongBreak',
          label: 'Sessões até pausa longa',
          icon: 'fas fa-list-ol',
          min: 2,
          max: 8
        }
      ];
    }
  },
  
  methods: {
    toggleTimer() {
      if (this.isRunning) {
        this.pauseTimer();
      } else {
        this.startTimer();
      }
    },
    
    startTimer() {
      this.isRunning = true;
      this.timerInterval = setInterval(() => {
        if (this.timeLeft > 0) {
          this.timeLeft--;
        } else {
          this.completeSession();
        }
      }, 1000);
      
      this.showMotivationalQuote();
    },
    
    pauseTimer() {
      this.isRunning = false;
      clearInterval(this.timerInterval);
    },
    
    resetTimer() {
      this.pauseTimer();
      this.timeLeft = this.duration;
    },
    
    completeSession() {
      this.pauseTimer();
      
      if (this.sessionType === 'work') {
        this.completedSessions++;
        this.totalFocusTime += this.settings.workDuration;
        this.updateStreak();
        
        if (this.settings.soundEnabled) {
          this.playSound('complete');
        }
        
        if (this.settings.notificationsEnabled) {
          this.showNotification('Sessão completa!', 'Hora de fazer uma pausa!');
        }
        
        if (this.completedSessions % this.settings.sessionsUntilLongBreak === 0) {
          this.switchToSession('longBreak');
        } else {
          this.switchToSession('shortBreak');
        }
      } else {
        if (this.settings.soundEnabled) {
          this.playSound('break-end');
        }
        
        if (this.settings.notificationsEnabled) {
          this.showNotification('Pausa terminada!', 'Pronto para mais uma sessão?');
        }
        
        this.switchToSession('work');
      }
    },
    
    switchToSession(type) {
      this.sessionType = type;
      
      switch(type) {
        case 'work':
          this.duration = this.settings.workDuration * 60;
          if (this.settings.autoStartPomodoros) {
            setTimeout(() => this.startTimer(), 1000);
          }
          break;
        case 'shortBreak':
          this.duration = this.settings.shortBreakDuration * 60;
          if (this.settings.autoStartBreaks) {
            setTimeout(() => this.startTimer(), 1000);
          }
          break;
        case 'longBreak':
          this.duration = this.settings.longBreakDuration * 60;
          if (this.settings.autoStartBreaks) {
            setTimeout(() => this.startTimer(), 1000);
          }
          break;
      }
      
      this.timeLeft = this.duration;
    },
    
    adjustTime(type, amount) {
      if (!this.isRunning) {
        switch(type) {
          case 'work':
            this.settings.workDuration = Math.max(1, this.settings.workDuration + amount);
            if (this.sessionType === 'work') {
              this.duration = this.settings.workDuration * 60;
              this.timeLeft = this.duration;
            }
            break;
          case 'shortBreak':
            this.settings.shortBreakDuration = Math.max(1, this.settings.shortBreakDuration + amount);
            if (this.sessionType === 'shortBreak') {
              this.duration = this.settings.shortBreakDuration * 60;
              this.timeLeft = this.duration;
            }
            break;
          case 'longBreak':
            this.settings.longBreakDuration = Math.max(1, this.settings.longBreakDuration + amount);
            if (this.sessionType === 'longBreak') {
              this.duration = this.settings.longBreakDuration * 60;
              this.timeLeft = this.duration;
            }
            break;
        }
        this.saveSettings();
      }
    },
    
    addTask() {
      if (this.newTask.trim()) {
        this.tasks.push({
          id: Date.now(),
          text: this.newTask.trim(),
          completed: false
        });
        this.newTask = '';
        this.saveTasks();
      }
    },
    
    removeTask(index) {
      this.tasks.splice(index, 1);
      this.saveTasks();
    },
    
    updateStreak() {
      const today = new Date().toDateString();
      if (this.lastActiveDate === today) {
        return;
      }
      
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (this.lastActiveDate === yesterday.toDateString()) {
        this.currentStreak++;
      } else {
        this.currentStreak = 1;
      }
      
      this.lastActiveDate = today;
      this.saveStats();
    },
    
    showMotivationalQuote() {
      const randomQuote = this.motivationalQuotes[
        Math.floor(Math.random() * this.motivationalQuotes.length)
      ];
      this.currentMotivation = randomQuote;
      this.showMotivation = true;
      
      setTimeout(() => {
        this.showMotivation = false;
      }, 5000);
    },
    
    playSound(type) {
      const audio = new Audio();
      if (type === 'complete') {
        audio.src = '/sounds/complete.mp3';
      } else if (type === 'break-end') {
        audio.src = '/sounds/break-time.mp3';
      }
      audio.play().catch(e => console.log('Audio play failed:', e));
    },
    
    showNotification(title, body) {
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
          body: body,
          icon: '/favicon.ico'
        });
      }
    },
    
    requestNotificationPermission() {
      if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
      }
    },
    
    saveSettings() {
      localStorage.setItem('pomodoroSettings', JSON.stringify(this.settings));
    },
    
    loadSettings() {
      const saved = localStorage.getItem('pomodoroSettings');
      if (saved) {
        this.settings = { ...this.settings, ...JSON.parse(saved) };
      }
    },
    
    saveStats() {
      const stats = {
        completedSessions: this.completedSessions,
        totalFocusTime: this.totalFocusTime,
        currentStreak: this.currentStreak,
        lastActiveDate: this.lastActiveDate
      };
      localStorage.setItem('pomodoroStats', JSON.stringify(stats));
    },
    
    loadStats() {
      const saved = localStorage.getItem('pomodoroStats');
      if (saved) {
        const stats = JSON.parse(saved);
        this.completedSessions = stats.completedSessions || 0;
        this.totalFocusTime = stats.totalFocusTime || 0;
        this.currentStreak = stats.currentStreak || 0;
        this.lastActiveDate = stats.lastActiveDate;
      }
    },
    
    saveTasks() {
      localStorage.setItem('pomodoroTasks', JSON.stringify(this.tasks));
    },
    
    loadTasks() {
      const saved = localStorage.getItem('pomodoroTasks');
      if (saved) {
        this.tasks = JSON.parse(saved);
      }
    }
  },
  
  mounted() {
    this.loadSettings();
    this.loadStats();
    this.loadTasks();
    this.requestNotificationPermission();
    
    this.duration = this.settings.workDuration * 60;
    this.timeLeft = this.duration;
  },
  
  beforeUnmount() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  }
}
</script>

<style scoped>
/* Variables */
:root {
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --dark-bg: #0a0f1b;
  --card-bg: #1a2332;
  --text-primary: #e4e6eb;
  --text-secondary: #94a3b8;
  --border-color: rgba(148, 163, 184, 0.1);
}

/* Base */
.pomodoro-page {
  min-height: 100vh;
  background: var(--dark-bg);
  color: var(--text-primary);
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

.gradient-orb.orb-1 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, #6366f1, transparent);
  top: -200px;
  left: -200px;
}

.gradient-orb.orb-2 {
  width: 800px;
  height: 800px;
  background: radial-gradient(circle, #8b5cf6, transparent);
  bottom: -300px;
  right: -300px;
  animation-delay: -7s;
}

.gradient-orb.orb-3 {
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, #10b981, transparent);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -14s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-30px, 30px) scale(0.9); }
}

.grid-overlay {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 30s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Header */
.page-header {
  position: relative;
  z-index: 10;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: 2rem;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.page-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  position: relative;
}

.icon-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 16px;
  opacity: 0.5;
  filter: blur(10px);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.05); }
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-subtitle {
  margin: 0.25rem 0 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.header-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stat-icon {
  font-size: 1.5rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 5;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  align-items: start;
}

/* Modern Card */
.modern-card {
  background: rgba(26, 35, 50, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  transition: all 0.3s;
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(99, 102, 241, 0.3);
}

/* Timer Section */
.timer-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Timer Card */
.timer-card {
  padding: 2.5rem;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.session-badge {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s;
}

.session-badge.session-work {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
  border: 1px solid rgba(99, 102, 241, 0.3);
}

.session-badge.session-shortBreak {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.session-badge.session-longBreak {
  background: rgba(20, 184, 166, 0.1);
  color: #14b8a6;
  border: 1px solid rgba(20, 184, 166, 0.3);
}

.session-counter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.session-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

/* Timer Display */
.timer-display-wrapper {
  position: relative;
  width: 280px;
  height: 280px;
  margin: 0 auto 2.5rem;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-bg {
  fill: none;
  stroke: rgba(148, 163, 184, 0.1);
}

.progress-ring-bar {
  fill: none;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.5s ease;
}

.timer-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.time-display {
  font-size: 3.5rem;
  font-weight: 300;
  font-family: 'SF Mono', 'Monaco', 'Courier New', monospace;
  letter-spacing: -2px;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.time-separator {
  margin: 0 0.25rem;
  opacity: 1;
  transition: opacity 0.5s;
}

.time-separator.blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* Timer Controls */
.timer-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.control-btn {
  padding: 0.875rem 1.75rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  z-index: 1;
}

.control-btn.primary {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
}

.control-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 40px rgba(99, 102, 241, 0.4);
}

.pulse-animation {
  animation: pulse-btn 2s infinite;
}

@keyframes pulse-btn {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
  }
  50% { 
    transform: scale(1.02);
    box-shadow: 0 6px 30px rgba(99, 102, 241, 0.5);
  }
}

.btn-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  filter: blur(20px);
  opacity: 0;
  transition: opacity 0.3s;
}

.control-btn.primary:hover .btn-glow {
  opacity: 0.5;
}

.control-btn.secondary {
  background: rgba(148, 163, 184, 0.1);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.control-btn.secondary:hover {
  background: rgba(148, 163, 184, 0.2);
  border-color: rgba(148, 163, 184, 0.3);
}

/* Quick Adjustments */
.quick-adjustments {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.adjustment-group {
  text-align: center;
}

.adjustment-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.time-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
}

.adjust-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: rgba(148, 163, 184, 0.1);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.adjust-btn:hover:not(:disabled) {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
  transform: scale(1.1);
}

.adjust-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.time-value {
  min-width: 80px;
  text-align: center;
}

.time-value .value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.time-value .unit {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-left: 0.25rem;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.stat-card {
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.card-background {
  position: absolute;
  inset: 0;
  opacity: 0.3;
  transition: opacity 0.3s;
}

.stat-card:hover .card-background {
  opacity: 0.5;
}

.card-content {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.card-info h3 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
}

.card-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.card-decoration {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.decoration-line {
  width: 40px;
  height: 2px;
  background: currentColor;
  opacity: 0.2;
  margin-bottom: 0.5rem;
}

.decoration-dot {
  width: 6px;
  height: 6px;
  background: currentColor;
  border-radius: 50%;
  opacity: 0.4;
  margin-left: auto;
}

/* Right Section */
.right-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Tasks Card */
.tasks-card {
  padding: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-header h2 {
  margin: 0;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.task-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
  background: rgba(148, 163, 184, 0.1);
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
}

.task-input-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-secondary);
  pointer-events: none;
  z-index: 1;
}

.modern-input {
  width: 100%;
  padding: 0.875rem 3rem 0.875rem 2.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid transparent;
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s;
}

.modern-input:focus {
  outline: none;
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(30, 41, 59, 0.8);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.add-btn {
  position: absolute;
  right: 0.5rem;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border: none;
  border-radius: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-btn:hover:not(:disabled) {
  transform: scale(1.1);
}

.add-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Tasks List */
.tasks-list {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 12px;
  margin-bottom: 0.75rem;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.task-item:hover {
  background: rgba(30, 41, 59, 0.5);
  border-color: var(--border-color);
}

.task-item.completed {
  opacity: 0.6;
}

.task-item.completed .task-text {
  text-decoration: line-through;
  color: var(--text-secondary);
}

.task-checkbox-wrapper {
  position: relative;
  cursor: pointer;
}

.task-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkbox-custom {
  width: 24px;
  height: 24px;
  background: rgba(148, 163, 184, 0.1);
  border: 2px solid var(--border-color);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.checkbox-custom i {
  color: white;
  font-size: 0.75rem;
  opacity: 0;
  transform: scale(0);
  transition: all 0.2s;
}

.task-checkbox:checked ~ .checkbox-custom {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-color: transparent;
}

.task-checkbox:checked ~ .checkbox-custom i {
  opacity: 1;
  transform: scale(1);
}

.task-text {
  flex: 1;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.remove-btn {
  width: 32px;
  height: 32px;
  background: rgba(239, 68, 68, 0.1);
  border: none;
  border-radius: 8px;
  color: var(--danger);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
}

.task-item:hover .remove-btn {
  opacity: 1;
}

.remove-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.empty-tasks {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.empty-tasks i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.3;
}

.empty-tasks p {
  margin: 0;
  font-size: 0.875rem;
}

/* Settings Card */
.settings-card {
  padding: 1.5rem;
}

.toggle-btn {
  width: 32px;
  height: 32px;
  background: rgba(148, 163, 184, 0.1);
  border: none;
  border-radius: 8px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover {
  background: rgba(148, 163, 184, 0.2);
  color: var(--text-primary);
  transform: rotate(180deg);
}

.settings-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.settings-content.expanded {
  max-height: 500px;
}

.setting-item {
  margin-bottom: 1rem;
}

.setting-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.setting-label:hover {
  background: rgba(30, 41, 59, 0.5);
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.setting-control {
  position: relative;
}

.modern-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkbox-slider {
  width: 44px;
  height: 24px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  position: relative;
  transition: all 0.3s;
}

.checkbox-slider::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.modern-checkbox:checked ~ .checkbox-slider {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}

.modern-checkbox:checked ~ .checkbox-slider::after {
  transform: translateX(20px);
}

.modern-number-input {
  width: 60px;
  padding: 0.5rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  text-align: center;
  font-size: 0.875rem;
}

/* Motivational Toast */
.motivation-toast {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  padding: 1.25rem 1.5rem;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.4);
  display: flex;
  align-items: center;
  gap: 1rem;
  max-width: 400px;
  z-index: 100;
}

.toast-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.toast-content {
  flex: 1;
}

.toast-content p {
  margin: 0;
  font-weight: 500;
  line-height: 1.4;
}

.toast-decoration {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50%;
}

/* Animations */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.5s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.9);
}

.toast-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.9);
}

.task-list-enter-active,
.task-list-leave-active {
  transition: all 0.3s ease;
}

.task-list-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.task-list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

/* Scrollbar */
.tasks-list::-webkit-scrollbar {
  width: 6px;
}

.tasks-list::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 3px;
}

.tasks-list::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.tasks-list::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Responsive */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .right-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-stats {
    display: none;
  }
  
  .timer-card {
    padding: 1.5rem;
  }
  
  .time-display {
    font-size: 2.5rem;
  }
  
  .timer-display-wrapper {
    width: 240px;
    height: 240px;
  }
  
  .quick-adjustments {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .right-section {
    grid-template-columns: 1fr;
  }
  
  .motivation-toast {
    left: 1rem;
    right: 1rem;
    bottom: 1rem;
  }
}
</style>