<template>
  <div class="notes-editor">
    <!-- Background Effects -->
    <div class="editor-background">
      <div class="gradient-mesh"></div>
    </div>

    <!-- Header Toolbar -->
    <div class="editor-header">
      <div class="header-left">
        <router-link to="/resumos-notas" class="back-btn">
          <i class="fas fa-arrow-left"></i>
          <span>Voltar</span>
        </router-link>
        
        <div class="note-info">
          <input v-model="noteTitle" 
                 placeholder="Título da Nota..." 
                 class="note-title-input" />
          <div class="note-meta">
            <span class="meta-item">
              <i class="fas fa-clock"></i>
              {{ lastSaved }}
            </span>
            <span class="meta-item">
              <i class="fas fa-file-word"></i>
              {{ wordCount }} palavras
            </span>
          </div>
        </div>
      </div>

      <div class="header-center">
        <div class="format-toolbar">
          <button v-for="tool in formatTools" 
                  :key="tool.command"
                  @click="executeCommand(tool.command)"
                  :class="{ active: tool.active }"
                  class="format-btn"
                  :title="tool.title">
            <i :class="`fas fa-${tool.icon}`"></i>
          </button>
          
          <div class="toolbar-divider"></div>
          
          <button @click="executeAction('export')" class="action-btn" title="Exportar">
            <i class="fas fa-download"></i>
            <span>Exportar</span>
          </button>
        </div>
      </div>

      <div class="header-right">
        <button @click="toggleAIAssistant" class="ai-toggle-btn" :class="{ active: showAI }">
          <i class="fas fa-robot"></i>
          <span>IA Assistant</span>
        </button>
        
        <button @click="saveNote" class="save-btn">
          <i class="fas fa-save"></i>
          <span>Salvar</span>
        </button>
      </div>
    </div>

    <!-- Main Editor Area -->
    <div class="editor-container">
      <!-- Editor Panel -->
      <div class="editor-panel" :class="{ 'with-ai': showAI }">
        <div class="editor-wrapper">
          <!-- Content Editor -->
          <div class="editor-content-area">
            <textarea 
              v-model="noteContent"
              @input="handleInput"
              class="editor-textarea"
              placeholder="Comece a digitar suas notas aqui..."
            ></textarea>
          </div>
        </div>

        <!-- Preview Toggle -->
        <div class="preview-toggle">
          <button @click="togglePreview" class="preview-btn">
            <i :class="`fas fa-${showPreview ? 'eye-slash' : 'eye'}`"></i>
            {{ showPreview ? 'Editor' : 'Preview' }}
          </button>
        </div>

        <!-- Markdown Preview -->
        <transition name="slide-fade">
          <div v-if="showPreview" class="markdown-preview">
            <div v-html="markdownHTML" class="preview-content"></div>
          </div>
        </transition>
      </div>

      <!-- AI Assistant Panel -->
      <transition name="slide-right">
        <div v-if="showAI" class="ai-panel">
          <div class="ai-header">
            <h3>
              <i class="fas fa-brain"></i>
              IA Assistant
            </h3>
            <button @click="showAI = false" class="close-ai">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="ai-features">
            <!-- AI Suggestions -->
            <div class="ai-section">
              <h4>Sugestões Inteligentes</h4>
              <div class="suggestion-list">
                <div v-for="suggestion in aiSuggestions" 
                     :key="suggestion.id"
                     class="suggestion-item"
                     @click="applySuggestion(suggestion)">
                  <i :class="`fas fa-${suggestion.icon}`"></i>
                  <div class="suggestion-content">
                    <h5>{{ suggestion.title }}</h5>
                    <p>{{ suggestion.preview }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- AI Commands -->
            <div class="ai-section">
              <h4>Comandos Rápidos</h4>
              <div class="ai-commands">
                <button v-for="cmd in aiCommands" 
                        :key="cmd.id"
                        @click="executeAICommand(cmd)"
                        class="ai-command-btn">
                  <i :class="`fas fa-${cmd.icon}`"></i>
                  <span>{{ cmd.label }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- Bottom Status Bar -->
    <div class="status-bar">
      <div class="status-left">
        <span class="status-item">
          <i class="fas fa-file-lines"></i>
          {{ lineCount }} linhas
        </span>
        <span class="status-item">
          <i class="fas fa-text-width"></i>
          {{ charCount }} caracteres
        </span>
      </div>
      
      <div class="status-center">
        <span class="status-mode">{{ editorMode }}</span>
      </div>
      
      <div class="status-right">
        <span class="auto-save-status" :class="{ saving: autoSaving }">
          <i :class="`fas fa-${autoSaving ? 'sync fa-spin' : 'check-circle'}`"></i>
          {{ autoSaving ? 'Salvando...' : 'Salvo automaticamente' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'NotesEditor',
  
  setup() {
    const router = useRouter()
    
    // Editor State
    const noteTitle = ref('Nova Nota')
    const noteContent = ref('')
    const showPreview = ref(false)
    const showAI = ref(false)
    const autoSaving = ref(false)
    const lastSaved = ref('Agora')
    const editorMode = ref('Markdown')
    
    // Stats
    const wordCount = computed(() => {
      const text = noteContent.value
      return text.split(/\s+/).filter(word => word.length > 0).length
    })
    
    const charCount = computed(() => {
      return noteContent.value.length
    })
    
    const lineCount = computed(() => {
      return noteContent.value.split('\n').length
    })
    
    // Markdown Preview
    const markdownHTML = computed(() => {
      // Simple markdown to HTML conversion
      let html = noteContent.value
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/\*\*\*(.+?)\*\*\*/g, '<strong><em>$1</em></strong>')
        .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.+?)\*/g, '<em>$1</em>')
        .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
        .replace(/`([^`]+)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>')
      return html
    })
    
    // Format Tools
    const formatTools = ref([
      { command: 'bold', icon: 'bold', title: 'Negrito', active: false },
      { command: 'italic', icon: 'italic', title: 'Itálico', active: false },
      { command: 'heading', icon: 'heading', title: 'Título', active: false },
      { command: 'quote', icon: 'quote-left', title: 'Citação', active: false },
      { command: 'code', icon: 'code', title: 'Código', active: false },
      { command: 'link', icon: 'link', title: 'Link', active: false },
      { command: 'image', icon: 'image', title: 'Imagem', active: false },
      { command: 'list-ul', icon: 'list-ul', title: 'Lista', active: false },
      { command: 'list-ol', icon: 'list-ol', title: 'Lista Numerada', active: false },
    ])
    
    // AI Features
    const aiSuggestions = ref([
      {
        id: 1,
        icon: 'lightbulb',
        title: 'Melhorar Parágrafo',
        preview: 'Tornar este parágrafo mais claro e conciso...'
      },
      {
        id: 2,
        icon: 'check-circle',
        title: 'Correção Gramatical',
        preview: 'Corrigir erros de gramática e ortografia...'
      },
      {
        id: 3,
        icon: 'expand',
        title: 'Expandir Ideia',
        preview: 'Adicionar mais detalhes e exemplos...'
      }
    ])
    
    const aiCommands = ref([
      { id: 'summarize', icon: 'compress', label: 'Resumir' },
      { id: 'translate', icon: 'language', label: 'Traduzir' },
      { id: 'explain', icon: 'info-circle', label: 'Explicar' },
      { id: 'format', icon: 'align-left', label: 'Formatar' }
    ])
    
    // Methods
    const executeCommand = (command) => {
      const selection = window.getSelection()
      const selectedText = selection.toString()
      const cursorPosition = noteContent.value.selectionStart || noteContent.value.length
      
      let before = ''
      let after = ''
      
      switch(command) {
        case 'bold':
          before = '**'
          after = '**'
          break
        case 'italic':
          before = '*'
          after = '*'
          break
        case 'heading':
          before = '# '
          after = ''
          break
        case 'quote':
          before = '> '
          after = ''
          break
        case 'code':
          before = '`'
          after = '`'
          break
        case 'link':
          const url = prompt('Digite a URL:')
          if (url) {
            before = '['
            after = `](${url})`
          }
          break
        case 'list-ul':
          before = '- '
          after = ''
          break
        case 'list-ol':
          before = '1. '
          after = ''
          break
      }
      
      if (before || after) {
        const newText = noteContent.value.slice(0, cursorPosition) + 
                       before + selectedText + after + 
                       noteContent.value.slice(cursorPosition + selectedText.length)
        noteContent.value = newText
      }
    }
    
    const executeAction = (action) => {
      if (action === 'export') {
        exportNote()
      }
    }
    
    const handleInput = () => {
      startAutoSave()
    }
    
    const togglePreview = () => {
      showPreview.value = !showPreview.value
    }
    
    const toggleAIAssistant = () => {
      showAI.value = !showAI.value
    }
    
    const applySuggestion = (suggestion) => {
      console.log('Applying suggestion:', suggestion)
      // Implementação da sugestão de IA
    }
    
    const executeAICommand = (cmd) => {
      console.log('Executing AI command:', cmd)
      // Implementação do comando de IA
    }
    
    let autoSaveTimeout = null
    const startAutoSave = () => {
      clearTimeout(autoSaveTimeout)
      autoSaveTimeout = setTimeout(() => {
        saveNote(true)
      }, 2000)
    }
    
    const saveNote = async (auto = false) => {
      if (auto) {
        autoSaving.value = true
      }
      
      // Simular salvamento
      setTimeout(() => {
        autoSaving.value = false
        lastSaved.value = new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
      }, 500)
    }
    
    const exportNote = () => {
      const filename = `${noteTitle.value.replace(/\s+/g, '_')}.md`
      const blob = new Blob([noteContent.value], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      a.click()
      URL.revokeObjectURL(url)
    }
    
    // Lifecycle
    onMounted(() => {
      console.log('NotesEditor mounted successfully')
      // Conteúdo inicial
      noteContent.value = `# Bem-vindo ao Editor de Notas

Este é um editor de notas avançado com suporte a Markdown e assistente de IA.

## Recursos disponíveis:

- **Formatação rica** com Markdown
- *Assistente de IA* integrado
- Preview em tempo real
- Salvamento automático
- Exportação em múltiplos formatos

Comece a digitar suas notas abaixo...`
    })
    
    return {
      // State
      noteTitle,
      noteContent,
      showPreview,
      showAI,
      autoSaving,
      lastSaved,
      editorMode,
      
      // Computed
      wordCount,
      charCount,
      lineCount,
      markdownHTML,
      
      // Data
      formatTools,
      aiSuggestions,
      aiCommands,
      
      // Methods
      executeCommand,
      executeAction,
      handleInput,
      togglePreview,
      toggleAIAssistant,
      applySuggestion,
      executeAICommand,
      saveNote,
      exportNote
    }
  }
}
</script>

<style scoped>
.notes-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #0a0a14;
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

/* Background Effects */
.editor-background {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-mesh {
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 20%, rgba(255, 255, 0, 0.1) 0%, transparent 50%);
  filter: blur(100px);
}

/* Header Toolbar */
.editor-header {
  position: relative;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex: 1;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-3px);
}

.note-info {
  flex: 1;
}

.note-title-input {
  width: 100%;
  font-size: 1.75rem;
  font-weight: 900;
  background: rgba(0, 255, 255, 0.1);
  border: 2px solid rgba(0, 255, 255, 0.3);
  color: #00ffff;
  outline: none;
  margin-bottom: 0.25rem;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.note-title-input:focus {
  background: rgba(0, 255, 255, 0.15);
  border-color: #00ffff;
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
}

.note-title-input::placeholder {
  color: rgba(0, 255, 255, 0.5);
  font-weight: 600;
}

.note-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.header-center {
  flex: 2;
  display: flex;
  justify-content: center;
}

.format-toolbar {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.format-btn,
.action-btn {
  padding: 0.5rem 0.75rem;
  background: transparent;
  border: none;
  color: #ffffff;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.format-btn:hover,
.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #00ffff;
}

.format-btn.active {
  background: #00ffff;
  color: #0a0a14;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 0.25rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.ai-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ai-toggle-btn:hover,
.ai-toggle-btn.active {
  background: #00ffff;
  color: #0a0a14;
  border-color: #00ffff;
}

.save-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1.5rem;
  background: #00ff88;
  border: none;
  border-radius: 8px;
  color: #0a0a14;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
}

/* Editor Container */
.editor-container {
  flex: 1;
  display: flex;
  position: relative;
  z-index: 10;
  overflow: hidden;
}

.editor-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.editor-panel.with-ai {
  margin-right: 400px;
}

.editor-wrapper {
  flex: 1;
  display: flex;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin: 1rem;
  border-radius: 12px;
  overflow: hidden;
}

.editor-content-area {
  flex: 1;
  display: flex;
}

.editor-textarea {
  flex: 1;
  padding: 2rem;
  background: transparent;
  border: none;
  color: #ffffff;
  font-family: 'Courier New', monospace;
  font-size: 1rem;
  line-height: 1.6;
  resize: none;
  outline: none;
}

.editor-textarea::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

/* Preview */
.preview-toggle {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  z-index: 20;
}

.preview-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.preview-btn:hover {
  background: #00ffff;
  color: #0a0a14;
}

.markdown-preview {
  position: absolute;
  inset: 1rem;
  background: rgba(26, 26, 36, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  overflow-y: auto;
  z-index: 15;
}

.preview-content {
  max-width: 800px;
  margin: 0 auto;
  color: #ffffff;
  line-height: 1.8;
}

.preview-content h1 {
  font-size: 2rem;
  margin: 1.5rem 0;
  color: #00ffff;
}

.preview-content h2 {
  font-size: 1.5rem;
  margin: 1.25rem 0;
  color: #ff00ff;
}

.preview-content h3 {
  font-size: 1.25rem;
  margin: 1rem 0;
  color: #ffff00;
}

.preview-content strong {
  color: #00ff88;
}

.preview-content em {
  color: #ff8800;
}

.preview-content code {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.preview-content a {
  color: #00ffff;
  text-decoration: underline;
}

/* AI Panel */
.ai-panel {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 400px;
  background: rgba(26, 26, 36, 0.95);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 20;
}

.ai-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ai-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff;
  margin: 0;
}

.close-ai {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-ai:hover {
  background: #ff0088;
  color: white;
}

.ai-features {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.ai-section {
  margin-bottom: 2rem;
}

.ai-section h4 {
  color: #ffffff;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.suggestion-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-5px);
}

.suggestion-item i {
  font-size: 1.25rem;
  color: #00ffff;
}

.suggestion-content h5 {
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.suggestion-content p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.ai-commands {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.ai-command-btn {
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.ai-command-btn:hover {
  background: #00ffff;
  color: #0a0a14;
}

/* Status Bar */
.status-bar {
  position: relative;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.875rem;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: rgba(255, 255, 255, 0.6);
}

.status-mode {
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: #00ffff;
  font-weight: 500;
}

.auto-save-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #00ff88;
}

.auto-save-status.saving {
  color: #ff8800;
}

/* Transitions */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from,
.slide-right-leave-to {
  transform: translateX(100%);
}

/* Responsive */
@media (max-width: 1024px) {
  .header-center {
    display: none;
  }
  
  .editor-panel.with-ai {
    margin-right: 0;
  }
  
  .ai-panel {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .editor-header {
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .header-left,
  .header-right {
    width: 100%;
  }
  
  .format-toolbar {
    overflow-x: auto;
    max-width: 100%;
  }
}
</style>