<template>
  <div class="study-environment-page">
    <!-- Ambient Background -->
    <div class="ambient-background">
      <div class="particle-field" ref="particleField"></div>
      <div class="gradient-overlay"></div>
      <canvas ref="waveCanvas" class="wave-canvas"></canvas>
    </div>

    <!-- Main Layout -->
    <div class="main-layout">
      <!-- Top Bar -->
      <div class="top-bar">
        <div class="environment-title">
          <div class="title-icon">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <h1>Ambiente de Estudo Inteligente</h1>
        </div>
        
        <div class="top-controls">
          <button @click="toggleAmbientMode" class="control-btn" :class="{ active: ambientMode }">
            <i class="fas fa-moon"></i>
            <span>Modo Ambiente</span>
          </button>
          
          <button @click="toggleFullscreen" class="control-btn">
            <i class="fas fa-expand"></i>
            <span>Tela Cheia</span>
          </button>
          
          <div class="user-stats">
            <div class="stat-badge">
              <i class="fas fa-fire"></i>
              <span>{{ userStats.streak }} dias</span>
            </div>
            <div class="stat-badge">
              <i class="fas fa-trophy"></i>
              <span>Nível {{ userStats.level }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Left Panel - Study Timer & Focus -->
        <div class="panel left-panel">
          <!-- Focus Timer Card -->
          <div class="focus-timer-card card-3d">
            <div class="card-header">
              <h2>Sessão de Foco</h2>
              <div class="session-type-selector">
                <button 
                  v-for="type in sessionTypes" 
                  :key="type.id"
                  @click="selectSessionType(type)"
                  class="type-btn"
                  :class="{ active: currentSessionType.id === type.id }"
                >
                  <i :class="type.icon"></i>
                  <span>{{ type.name }}</span>
                </button>
              </div>
            </div>
            
            <div class="timer-display">
              <div class="timer-ring">
                <svg viewBox="0 0 240 240" class="timer-svg">
                  <defs>
                    <linearGradient id="timerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#6366f1" />
                      <stop offset="100%" style="stop-color:#8b5cf6" />
                    </linearGradient>
                  </defs>
                  <circle cx="120" cy="120" r="110" class="timer-track"/>
                  <circle 
                    cx="120" 
                    cy="120" 
                    r="110" 
                    class="timer-progress"
                    :style="timerProgressStyle"
                    stroke="url(#timerGradient)"
                  />
                </svg>
                <div class="timer-center">
                  <div class="time-text">{{ formattedTime }}</div>
                  <div class="session-info">{{ currentSessionType.description }}</div>
                </div>
              </div>
              
              <div class="timer-controls">
                <button @click="toggleTimer" class="timer-btn primary">
                  <i :class="isTimerRunning ? 'fas fa-pause' : 'fas fa-play'"></i>
                  {{ isTimerRunning ? 'Pausar' : 'Iniciar' }}
                </button>
                <button @click="resetTimer" class="timer-btn secondary">
                  <i class="fas fa-redo"></i>
                  Resetar
                </button>
              </div>
            </div>
          </div>

          <!-- Focus Metrics -->
          <div class="focus-metrics card-3d">
            <h3>Métricas de Foco</h3>
            <div class="metrics-grid">
              <div class="metric-item" v-for="metric in focusMetrics" :key="metric.id">
                <div class="metric-icon" :style="{ background: metric.color }">
                  <i :class="metric.icon"></i>
                </div>
                <div class="metric-data">
                  <span class="metric-value">{{ metric.value }}</span>
                  <span class="metric-label">{{ metric.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Center Panel - Main Study Area -->
        <div class="panel center-panel">
          <!-- Study Tools Tabs -->
          <div class="study-tools-tabs">
            <button 
              v-for="tool in studyTools" 
              :key="tool.id"
              @click="activeToolId = tool.id"
              class="tool-tab"
              :class="{ active: activeToolId === tool.id }"
            >
              <i :class="tool.icon"></i>
              <span>{{ tool.name }}</span>
            </button>
          </div>

          <!-- Notes Tool -->
          <div v-if="activeToolId === 'notes'" class="tool-content card-3d">
            <div class="notes-editor">
              <div class="editor-toolbar">
                <button @click="formatText('bold')" class="toolbar-btn">
                  <i class="fas fa-bold"></i>
                </button>
                <button @click="formatText('italic')" class="toolbar-btn">
                  <i class="fas fa-italic"></i>
                </button>
                <button @click="formatText('underline')" class="toolbar-btn">
                  <i class="fas fa-underline"></i>
                </button>
                <div class="toolbar-separator"></div>
                <button @click="formatText('h1')" class="toolbar-btn">
                  <i class="fas fa-heading"></i>
                </button>
                <button @click="formatText('bullet')" class="toolbar-btn">
                  <i class="fas fa-list"></i>
                </button>
                <button @click="formatText('code')" class="toolbar-btn">
                  <i class="fas fa-code"></i>
                </button>
                <div class="toolbar-separator"></div>
                <button @click="saveNote" class="toolbar-btn save">
                  <i class="fas fa-save"></i>
                  Salvar
                </button>
              </div>
              
              <div 
                ref="noteEditor"
                contenteditable="true"
                class="note-content"
                @input="onNoteInput"
                v-html="currentNote.content"
              ></div>
              
              <div class="note-tags">
                <input 
                  v-model="tagInput"
                  @keyup.enter="addTag"
                  placeholder="Adicionar tag..."
                  class="tag-input"
                >
                <div class="tags-list">
                  <span v-for="tag in currentNote.tags" :key="tag" class="tag">
                    {{ tag }}
                    <i @click="removeTag(tag)" class="fas fa-times"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Mind Map Tool -->
          <div v-else-if="activeToolId === 'mindmap'" class="tool-content card-3d">
            <div class="mindmap-container">
              <canvas ref="mindmapCanvas" class="mindmap-canvas"></canvas>
              <div class="mindmap-controls">
                <button @click="addNode" class="map-btn">
                  <i class="fas fa-plus"></i>
                  Adicionar Nó
                </button>
                <button @click="connectNodes" class="map-btn">
                  <i class="fas fa-link"></i>
                  Conectar
                </button>
                <button @click="clearMindmap" class="map-btn danger">
                  <i class="fas fa-trash"></i>
                  Limpar
                </button>
              </div>
            </div>
          </div>

          <!-- Tasks Tool -->
          <div v-else-if="activeToolId === 'tasks'" class="tool-content card-3d">
            <div class="tasks-manager">
              <div class="task-input-section">
                <input 
                  v-model="newTaskInput"
                  @keyup.enter="addTask"
                  placeholder="Nova tarefa..."
                  class="task-input"
                >
                <select v-model="newTaskPriority" class="priority-select">
                  <option value="low">Baixa</option>
                  <option value="medium">Média</option>
                  <option value="high">Alta</option>
                </select>
                <button @click="addTask" class="add-task-btn">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
              
              <div class="tasks-categories">
                <div 
                  v-for="category in taskCategories" 
                  :key="category.id"
                  class="task-category"
                >
                  <h4>{{ category.name }}</h4>
                  <draggable 
                    v-model="category.tasks"
                    group="tasks"
                    class="tasks-list"
                    @change="onTaskDragEnd"
                  >
                    <div 
                      v-for="task in category.tasks" 
                      :key="task.id"
                      class="task-item"
                      :class="{ completed: task.completed }"
                    >
                      <input 
                        type="checkbox"
                        v-model="task.completed"
                        class="task-checkbox"
                      >
                      <span class="task-text">{{ task.text }}</span>
                      <span class="task-priority" :class="`priority-${task.priority}`">
                        {{ task.priority }}
                      </span>
                      <i @click="deleteTask(category.id, task.id)" class="fas fa-trash delete-task"></i>
                    </div>
                  </draggable>
                </div>
              </div>
            </div>
          </div>

          <!-- Resources Tool -->
          <div v-else-if="activeToolId === 'resources'" class="tool-content card-3d">
            <div class="resources-hub">
              <div class="resource-search">
                <i class="fas fa-search"></i>
                <input 
                  v-model="resourceSearch"
                  placeholder="Buscar recursos..."
                  class="search-input"
                >
              </div>
              
              <div class="resources-grid">
                <div 
                  v-for="resource in filteredResources" 
                  :key="resource.id"
                  class="resource-card"
                  @click="openResource(resource)"
                >
                  <div class="resource-icon" :style="{ background: resource.color }">
                    <i :class="resource.icon"></i>
                  </div>
                  <h4>{{ resource.title }}</h4>
                  <p>{{ resource.description }}</p>
                  <div class="resource-meta">
                    <span><i class="fas fa-clock"></i> {{ resource.duration }}</span>
                    <span><i class="fas fa-star"></i> {{ resource.rating }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Panel - Ambient & Analytics -->
        <div class="panel right-panel">
          <!-- Ambient Music Player -->
          <div class="music-player card-3d">
            <h3>Música Ambiente</h3>
            <div class="player-controls">
              <div class="now-playing">
                <div class="album-art" :style="{ backgroundImage: `url(${currentTrack.artwork})` }">
                  <div class="play-overlay" v-if="!isPlaying">
                    <i class="fas fa-play"></i>
                  </div>
                </div>
                <div class="track-info">
                  <h4>{{ currentTrack.title }}</h4>
                  <p>{{ currentTrack.artist }}</p>
                </div>
              </div>
              
              <div class="playback-controls">
                <button @click="previousTrack" class="player-btn">
                  <i class="fas fa-backward"></i>
                </button>
                <button @click="togglePlayback" class="player-btn primary">
                  <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
                </button>
                <button @click="nextTrack" class="player-btn">
                  <i class="fas fa-forward"></i>
                </button>
              </div>
              
              <div class="volume-control">
                <i class="fas fa-volume-down"></i>
                <input 
                  type="range" 
                  v-model="volume" 
                  min="0" 
                  max="100"
                  class="volume-slider"
                >
                <i class="fas fa-volume-up"></i>
              </div>
            </div>
            
            <div class="ambient-presets">
              <button 
                v-for="preset in ambientPresets" 
                :key="preset.id"
                @click="selectPreset(preset)"
                class="preset-btn"
                :class="{ active: currentPreset.id === preset.id }"
              >
                <i :class="preset.icon"></i>
                <span>{{ preset.name }}</span>
              </button>
            </div>
          </div>

          <!-- Study Analytics -->
          <div class="study-analytics card-3d">
            <h3>Análise de Desempenho</h3>
            <div class="analytics-chart">
              <canvas ref="analyticsChart"></canvas>
            </div>
            
            <div class="quick-stats">
              <div class="stat-item" v-for="stat in quickStats" :key="stat.id">
                <div class="stat-icon" :style="{ color: stat.color }">
                  <i :class="stat.icon"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-value">{{ stat.value }}</span>
                  <span class="stat-label">{{ stat.label }}</span>
                </div>
                <div class="stat-trend" :class="stat.trend">
                  <i :class="stat.trend === 'up' ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                  {{ stat.change }}%
                </div>
              </div>
            </div>
          </div>

          <!-- Goals Tracker -->
          <div class="goals-tracker card-3d">
            <h3>Metas de Estudo</h3>
            <div class="goals-list">
              <div v-for="goal in studyGoals" :key="goal.id" class="goal-item">
                <div class="goal-header">
                  <span class="goal-title">{{ goal.title }}</span>
                  <span class="goal-progress">{{ goal.current }}/{{ goal.target }}</span>
                </div>
                <div class="progress-bar">
                  <div 
                    class="progress-fill"
                    :style="{ 
                      width: `${(goal.current / goal.target) * 100}%`,
                      background: goal.color
                    }"
                  ></div>
                </div>
              </div>
            </div>
            
            <button @click="showGoalModal = true" class="add-goal-btn">
              <i class="fas fa-plus"></i>
              Nova Meta
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Achievement Notifications -->
    <transition-group name="achievement" tag="div" class="achievement-container">
      <div 
        v-for="achievement in achievements" 
        :key="achievement.id"
        class="achievement-notification"
      >
        <div class="achievement-icon">
          <i :class="achievement.icon"></i>
        </div>
        <div class="achievement-content">
          <h4>{{ achievement.title }}</h4>
          <p>{{ achievement.description }}</p>
        </div>
        <div class="achievement-points">+{{ achievement.points }} XP</div>
      </div>
    </transition-group>

    <!-- Goal Modal -->
    <transition name="modal">
      <div v-if="showGoalModal" class="modal-overlay" @click.self="showGoalModal = false">
        <div class="modal-content">
          <h2>Nova Meta de Estudo</h2>
          <div class="modal-form">
            <input 
              v-model="newGoal.title"
              placeholder="Título da meta"
              class="modal-input"
            >
            <input 
              v-model.number="newGoal.target"
              type="number"
              placeholder="Valor alvo"
              class="modal-input"
            >
            <select v-model="newGoal.type" class="modal-select">
              <option value="hours">Horas de estudo</option>
              <option value="sessions">Sessões completas</option>
              <option value="tasks">Tarefas concluídas</option>
            </select>
            <div class="modal-actions">
              <button @click="showGoalModal = false" class="modal-btn cancel">
                Cancelar
              </button>
              <button @click="createGoal" class="modal-btn confirm">
                Criar Meta
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import Chart from 'chart.js/auto'

export default {
  name: 'StudyEnvironment',
  components: {
    draggable
  },
  data() {
    return {
      // Ambient Settings
      ambientMode: false,
      particleAnimation: null,
      waveAnimation: null,
      
      // User Stats
      userStats: {
        streak: 7,
        level: 12,
        totalHours: 156,
        experience: 3450
      },
      
      // Timer Data
      isTimerRunning: false,
      timeRemaining: 25 * 60,
      timerDuration: 25 * 60,
      timerInterval: null,
      
      // Session Types
      sessionTypes: [
        { id: 'focus', name: 'Foco Profundo', icon: 'fas fa-brain', duration: 25, description: 'Concentração máxima' },
        { id: 'study', name: 'Estudo', icon: 'fas fa-book', duration: 45, description: 'Sessão de aprendizado' },
        { id: 'review', name: 'Revisão', icon: 'fas fa-redo', duration: 15, description: 'Revisão rápida' },
        { id: 'break', name: 'Pausa', icon: 'fas fa-coffee', duration: 5, description: 'Descanso merecido' }
      ],
      currentSessionType: null,
      
      // Focus Metrics
      focusMetrics: [
        { id: 1, icon: 'fas fa-fire', label: 'Sequência', value: '7 dias', color: 'linear-gradient(135deg, #ff6b6b, #ff8787)' },
        { id: 2, icon: 'fas fa-clock', label: 'Hoje', value: '3.5h', color: 'linear-gradient(135deg, #4ecdc4, #44a3aa)' },
        { id: 3, icon: 'fas fa-check', label: 'Completas', value: '12', color: 'linear-gradient(135deg, #667eea, #764ba2)' },
        { id: 4, icon: 'fas fa-chart-line', label: 'Produtividade', value: '87%', color: 'linear-gradient(135deg, #f093fb, #f5576c)' }
      ],
      
      // Study Tools
      studyTools: [
        { id: 'notes', name: 'Anotações', icon: 'fas fa-sticky-note' },
        { id: 'mindmap', name: 'Mapa Mental', icon: 'fas fa-project-diagram' },
        { id: 'tasks', name: 'Tarefas', icon: 'fas fa-tasks' },
        { id: 'resources', name: 'Recursos', icon: 'fas fa-book-open' }
      ],
      activeToolId: 'notes',
      
      // Notes
      currentNote: {
        content: '<h2>Minhas Anotações</h2><p>Comece a escrever...</p>',
        tags: ['estudo', 'importante']
      },
      tagInput: '',
      
      // Tasks
      newTaskInput: '',
      newTaskPriority: 'medium',
      taskCategories: [
        { 
          id: 'todo', 
          name: 'A Fazer', 
          tasks: [
            { id: 1, text: 'Revisar capítulo 5', priority: 'high', completed: false },
            { id: 2, text: 'Fazer exercícios práticos', priority: 'medium', completed: false }
          ] 
        },
        { 
          id: 'doing', 
          name: 'Em Progresso', 
          tasks: [
            { id: 3, text: 'Estudar algoritmos', priority: 'high', completed: false }
          ] 
        },
        { 
          id: 'done', 
          name: 'Concluído', 
          tasks: [
            { id: 4, text: 'Ler documentação', priority: 'low', completed: true }
          ] 
        }
      ],
      
      // Resources
      resourceSearch: '',
      resources: [
        { id: 1, title: 'JavaScript Avançado', description: 'Curso completo de JS moderno', icon: 'fab fa-js', color: '#f7df1e', duration: '4h', rating: 4.8 },
        { id: 2, title: 'Python para Data Science', description: 'Análise de dados com Python', icon: 'fab fa-python', color: '#3776ab', duration: '6h', rating: 4.9 },
        { id: 3, title: 'Algoritmos e Estruturas', description: 'Fundamentos de computação', icon: 'fas fa-code-branch', color: '#00d4ff', duration: '8h', rating: 4.7 },
        { id: 4, title: 'Machine Learning', description: 'Introdução ao ML', icon: 'fas fa-robot', color: '#ff6b6b', duration: '10h', rating: 4.9 }
      ],
      
      // Music Player
      isPlaying: false,
      volume: 70,
      currentTrack: {
        title: 'Focus Flow',
        artist: 'Ambient Study',
        artwork: '/images/album-art.jpg'
      },
      ambientPresets: [
        { id: 'nature', name: 'Natureza', icon: 'fas fa-tree' },
        { id: 'rain', name: 'Chuva', icon: 'fas fa-cloud-rain' },
        { id: 'lofi', name: 'Lo-Fi', icon: 'fas fa-music' },
        { id: 'white', name: 'Ruído Branco', icon: 'fas fa-wave-square' }
      ],
      currentPreset: { id: 'lofi', name: 'Lo-Fi', icon: 'fas fa-music' },
      
      // Analytics
      analyticsChart: null,
      quickStats: [
        { id: 1, icon: 'fas fa-clock', label: 'Tempo Total', value: '156h', color: '#4ecdc4', trend: 'up', change: 12 },
        { id: 2, icon: 'fas fa-tasks', label: 'Tarefas', value: '234', color: '#667eea', trend: 'up', change: 8 },
        { id: 3, icon: 'fas fa-trophy', label: 'Pontos', value: '3.4k', color: '#f093fb', trend: 'up', change: 15 }
      ],
      
      // Goals
      studyGoals: [
        { id: 1, title: 'Estudar 50 horas este mês', current: 32, target: 50, color: '#4ecdc4' },
        { id: 2, title: 'Completar 100 tarefas', current: 67, target: 100, color: '#667eea' },
        { id: 3, title: 'Manter sequência de 30 dias', current: 7, target: 30, color: '#f093fb' }
      ],
      showGoalModal: false,
      newGoal: {
        title: '',
        target: 0,
        type: 'hours'
      },
      
      // Achievements
      achievements: []
    }
  },
  
  computed: {
    formattedTime() {
      const minutes = Math.floor(this.timeRemaining / 60)
      const seconds = this.timeRemaining % 60
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    },
    
    timerProgressStyle() {
      const progress = (this.timeRemaining / this.timerDuration) * 100
      const circumference = 2 * Math.PI * 110
      const offset = circumference - (progress / 100) * circumference
      return {
        strokeDasharray: circumference,
        strokeDashoffset: offset
      }
    },
    
    filteredResources() {
      if (!this.resourceSearch) return this.resources
      const search = this.resourceSearch.toLowerCase()
      return this.resources.filter(r => 
        r.title.toLowerCase().includes(search) || 
        r.description.toLowerCase().includes(search)
      )
    }
  },
  
  methods: {
    // Ambient Effects
    toggleAmbientMode() {
      this.ambientMode = !this.ambientMode
      if (this.ambientMode) {
        this.startAmbientEffects()
      } else {
        this.stopAmbientEffects()
      }
    },
    
    startAmbientEffects() {
      // Initialize particle animation
      this.initParticles()
      // Initialize wave animation
      this.initWaveAnimation()
    },
    
    stopAmbientEffects() {
      if (this.particleAnimation) {
        cancelAnimationFrame(this.particleAnimation)
      }
      if (this.waveAnimation) {
        cancelAnimationFrame(this.waveAnimation)
      }
    },
    
    initParticles() {
      // Particle animation implementation
      const particleField = this.$refs.particleField
      // Add particle elements and animate
    },
    
    initWaveAnimation() {
      const canvas = this.$refs.waveCanvas
      const ctx = canvas.getContext('2d')
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      
      let time = 0
      const animate = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        
        // Draw ambient waves
        ctx.strokeStyle = 'rgba(99, 102, 241, 0.1)'
        ctx.lineWidth = 2
        
        for (let i = 0; i < 5; i++) {
          ctx.beginPath()
          for (let x = 0; x < canvas.width; x++) {
            const y = canvas.height / 2 + 
              Math.sin((x * 0.01) + (time * 0.002) + (i * 0.5)) * 50 * (i + 1)
            if (x === 0) ctx.moveTo(x, y)
            else ctx.lineTo(x, y)
          }
          ctx.stroke()
        }
        
        time++
        this.waveAnimation = requestAnimationFrame(animate)
      }
      animate()
    },
    
    toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
    },
    
    // Timer Methods
    selectSessionType(type) {
      this.currentSessionType = type
      this.timerDuration = type.duration * 60
      this.timeRemaining = this.timerDuration
      this.resetTimer()
    },
    
    toggleTimer() {
      if (this.isTimerRunning) {
        this.pauseTimer()
      } else {
        this.startTimer()
      }
    },
    
    startTimer() {
      this.isTimerRunning = true
      this.timerInterval = setInterval(() => {
        if (this.timeRemaining > 0) {
          this.timeRemaining--
        } else {
          this.completeSession()
        }
      }, 1000)
    },
    
    pauseTimer() {
      this.isTimerRunning = false
      clearInterval(this.timerInterval)
    },
    
    resetTimer() {
      this.pauseTimer()
      this.timeRemaining = this.timerDuration
    },
    
    completeSession() {
      this.pauseTimer()
      this.showAchievement({
        id: Date.now(),
        title: 'Sessão Completa!',
        description: `Você completou uma sessão de ${this.currentSessionType.name}`,
        icon: 'fas fa-check-circle',
        points: 50
      })
      this.updateStats()
    },
    
    // Notes Methods
    formatText(format) {
      const selection = window.getSelection()
      if (!selection.rangeCount) return
      
      switch(format) {
        case 'bold':
          document.execCommand('bold')
          break
        case 'italic':
          document.execCommand('italic')
          break
        case 'underline':
          document.execCommand('underline')
          break
        case 'h1':
          document.execCommand('formatBlock', false, 'h2')
          break
        case 'bullet':
          document.execCommand('insertUnorderedList')
          break
        case 'code':
          document.execCommand('formatBlock', false, 'pre')
          break
      }
    },
    
    onNoteInput() {
      this.currentNote.content = this.$refs.noteEditor.innerHTML
    },
    
    saveNote() {
      // Save note to storage
      localStorage.setItem('studyNote', JSON.stringify(this.currentNote))
      this.showAchievement({
        id: Date.now(),
        title: 'Nota Salva!',
        description: 'Suas anotações foram salvas com sucesso',
        icon: 'fas fa-save',
        points: 10
      })
    },
    
    addTag() {
      if (this.tagInput.trim() && !this.currentNote.tags.includes(this.tagInput)) {
        this.currentNote.tags.push(this.tagInput.trim())
        this.tagInput = ''
      }
    },
    
    removeTag(tag) {
      const index = this.currentNote.tags.indexOf(tag)
      if (index > -1) {
        this.currentNote.tags.splice(index, 1)
      }
    },
    
    // Tasks Methods
    addTask() {
      if (this.newTaskInput.trim()) {
        const newTask = {
          id: Date.now(),
          text: this.newTaskInput,
          priority: this.newTaskPriority,
          completed: false
        }
        this.taskCategories[0].tasks.push(newTask)
        this.newTaskInput = ''
      }
    },
    
    deleteTask(categoryId, taskId) {
      const category = this.taskCategories.find(c => c.id === categoryId)
      const taskIndex = category.tasks.findIndex(t => t.id === taskId)
      if (taskIndex > -1) {
        category.tasks.splice(taskIndex, 1)
      }
    },
    
    onTaskDragEnd() {
      // Update task status based on category
      this.saveTasks()
    },
    
    saveTasks() {
      localStorage.setItem('studyTasks', JSON.stringify(this.taskCategories))
    },
    
    // Music Player Methods
    togglePlayback() {
      this.isPlaying = !this.isPlaying
      // Implement actual audio playback
    },
    
    previousTrack() {
      // Implement previous track
    },
    
    nextTrack() {
      // Implement next track
    },
    
    selectPreset(preset) {
      this.currentPreset = preset
      // Load preset audio
    },
    
    // Resources Methods
    openResource(resource) {
      // Open resource in new tab or modal
      console.log('Opening resource:', resource)
    },
    
    // Goals Methods
    createGoal() {
      if (this.newGoal.title && this.newGoal.target > 0) {
        this.studyGoals.push({
          id: Date.now(),
          title: this.newGoal.title,
          current: 0,
          target: this.newGoal.target,
          color: '#' + Math.floor(Math.random()*16777215).toString(16)
        })
        this.newGoal = { title: '', target: 0, type: 'hours' }
        this.showGoalModal = false
      }
    },
    
    // Achievement System
    showAchievement(achievement) {
      this.achievements.push(achievement)
      setTimeout(() => {
        const index = this.achievements.findIndex(a => a.id === achievement.id)
        if (index > -1) {
          this.achievements.splice(index, 1)
        }
      }, 5000)
    },
    
    updateStats() {
      // Update user statistics
      this.userStats.experience += 50
      // Check for level up
      if (this.userStats.experience >= (this.userStats.level * 300)) {
        this.userStats.level++
        this.showAchievement({
          id: Date.now(),
          title: 'Level Up!',
          description: `Você alcançou o nível ${this.userStats.level}!`,
          icon: 'fas fa-star',
          points: 200
        })
      }
    },
    
    // Analytics
    initAnalyticsChart() {
      const ctx = this.$refs.analyticsChart.getContext('2d')
      this.analyticsChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'],
          datasets: [{
            label: 'Horas de Estudo',
            data: [2, 3.5, 2.8, 4, 3.2, 5, 4.5],
            borderColor: '#4ecdc4',
            backgroundColor: 'rgba(78, 205, 196, 0.1)',
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          }
        }
      })
    }
  },
  
  mounted() {
    // Initialize session type
    this.currentSessionType = this.sessionTypes[0]
    
    // Initialize analytics chart
    this.$nextTick(() => {
      this.initAnalyticsChart()
    })
    
    // Load saved data
    const savedNote = localStorage.getItem('studyNote')
    if (savedNote) {
      this.currentNote = JSON.parse(savedNote)
    }
    
    const savedTasks = localStorage.getItem('studyTasks')
    if (savedTasks) {
      this.taskCategories = JSON.parse(savedTasks)
    }
  },
  
  beforeUnmount() {
    // Clean up animations and intervals
    this.stopAmbientEffects()
    if (this.timerInterval) {
      clearInterval(this.timerInterval)
    }
  }
}
</script>

<style scoped>
/* Base Styles */
.study-environment-page {
  width: 100%;
  min-height: 100vh;
  background: #0a0b1e;
  color: #e4e6eb;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Ambient Background */
.ambient-background {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.particle-field {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.gradient-overlay {
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(78, 205, 196, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, rgba(240, 147, 251, 0.1) 0%, transparent 50%);
}

.wave-canvas {
  position: absolute;
  inset: 0;
  opacity: 0.5;
  pointer-events: none;
}

/* Main Layout */
.main-layout {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
}

/* Top Bar */
.top-bar {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  position: sticky;
  top: 0;
  z-index: 100;
}

.environment-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.environment-title h1 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.top-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.875rem;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateY(-2px);
}

.control-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: transparent;
  color: white;
}

.user-stats {
  display: flex;
  gap: 1rem;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  font-size: 0.8rem;
  white-space: nowrap;
}

.stat-badge i {
  color: #f59e0b;
}

/* Panel Styles */
.panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: fit-content;
}

/* Content Grid */
.content-grid {
  flex: 1;
  display: grid;
  grid-template-columns: minmax(300px, 350px) minmax(400px, 1fr) minmax(300px, 350px);
  gap: 1.5rem;
  padding: 1.5rem;
  max-width: 1600px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 3D Card Effect */
.card-3d {
  background: rgba(26, 35, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s;
  transform-style: preserve-3d;
  position: relative;
  height: fit-content;
}

.card-3d::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 16px;
  background: linear-gradient(135deg, transparent 40%, rgba(255, 255, 255, 0.05));
  pointer-events: none;
}

.card-3d:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 60px rgba(102, 126, 234, 0.1);
}

/* Focus Timer Card */
.focus-timer-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.card-header h2 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.session-type-selector {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.type-btn {
  padding: 0.4rem 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid transparent;
  border-radius: 8px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.8rem;
  white-space: nowrap;
}

.type-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.type-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
}

/* Timer Display */
.timer-display {
  text-align: center;
  padding: 1rem 0;
}

.timer-ring {
  position: relative;
  width: 180px;
  height: 180px;
  margin: 0 auto 1rem;
}

.timer-svg {
  transform: rotate(-90deg);
}

.timer-track {
  fill: none;
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 8;
}

.timer-progress {
  fill: none;
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.5s ease;
}

.timer-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.time-text {
  font-size: 2.5rem;
  font-weight: 300;
  font-family: 'SF Mono', 'Monaco', monospace;
  letter-spacing: -2px;
}

.session-info {
  font-size: 0.875rem;
  color: #94a3b8;
  margin-top: 0.5rem;
}

.timer-controls {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.timer-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.timer-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.timer-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.timer-btn.secondary {
  background: rgba(255, 255, 255, 0.05);
  color: #94a3b8;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.timer-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Focus Metrics */
.focus-metrics {
  overflow: hidden;
}

.focus-metrics h3 {
  margin: 0 0 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  transition: all 0.2s;
}

.metric-item:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(4px);
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.metric-data {
  flex: 1;
}

.metric-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  color: #94a3b8;
}

/* Study Tools Tabs */
.study-tools-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.tool-tab {
  flex: 1;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid transparent;
  border-radius: 10px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.tool-tab:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.tool-tab.active {
  background: linear-gradient(135deg, #4ecdc4, #44a3aa);
  color: white;
  border-color: transparent;
}

/* Tool Content */
.tool-content {
  min-height: 400px;
  max-height: calc(100vh - 320px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Notes Editor */
.notes-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.toolbar-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.toolbar-btn.save {
  width: auto;
  padding: 0 1rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  gap: 0.5rem;
}

.toolbar-separator {
  width: 1px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 0.5rem;
}

.note-content {
  flex: 1;
  min-height: 200px;
  max-height: 400px;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  outline: none;
  overflow-y: auto;
  font-size: 1rem;
  line-height: 1.6;
}

.note-content:focus {
  background: rgba(255, 255, 255, 0.05);
}

.note-tags {
  margin-top: 1rem;
}

.tag-input {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  outline: none;
  margin-bottom: 1rem;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 20px;
  font-size: 0.875rem;
  color: white;
}

.tag i {
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.tag i:hover {
  opacity: 1;
}

/* Mind Map */
.mindmap-container {
  height: 100%;
  position: relative;
  overflow: hidden;
}

.mindmap-canvas {
  width: 100%;
  min-height: 300px;
  height: calc(100% - 60px);
  max-height: 500px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
}

.mindmap-controls {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
}

.map-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.map-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.map-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

/* Tasks Manager */
.tasks-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.task-input-section {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.task-input {
  flex: 1;
  padding: 0.875rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  outline: none;
}

.priority-select {
  padding: 0.875rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  outline: none;
}

.add-task-btn {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #4ecdc4, #44a3aa);
  border: none;
  border-radius: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.add-task-btn:hover {
  transform: scale(1.05);
}

.tasks-categories {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  overflow: hidden;
}

.task-category {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

.task-category h4 {
  margin: 0 0 1rem;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
}

.tasks-list {
  flex: 1;
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  margin-bottom: 0.5rem;
  cursor: move;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.task-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(4px);
}

.task-item.completed {
  opacity: 0.6;
}

.task-item.completed .task-text {
  text-decoration: line-through;
}

.task-checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.task-text {
  flex: 1;
  font-size: 0.875rem;
}

.task-priority {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  text-transform: uppercase;
  font-weight: 500;
}

.priority-low {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.priority-medium {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.priority-high {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.delete-task {
  color: #ef4444;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.task-item:hover .delete-task {
  opacity: 0.7;
}

.delete-task:hover {
  opacity: 1;
}

/* Resources Hub */
.resources-hub {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.resource-search {
  position: relative;
  margin-bottom: 1.5rem;
}

.resource-search i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
}

.search-input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 3rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  outline: none;
}

.resources-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  overflow-y: auto;
  max-height: 500px;
}

.resource-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.3s;
  height: fit-content;
}

.resource-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.resource-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.resource-card h4 {
  margin: 0 0 0.5rem;
  font-size: 1.125rem;
}

.resource-card p {
  margin: 0 0 1rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.resource-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: #64748b;
}

/* Music Player */
.music-player h3 {
  margin: 0 0 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.now-playing {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  align-items: center;
}

.album-art {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.play-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.track-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.track-info h4 {
  margin: 0 0 0.25rem;
  font-size: 1rem;
}

.track-info p {
  margin: 0;
  font-size: 0.875rem;
  color: #94a3b8;
}

.playback-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.player-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.player-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.player-btn.primary {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #4ecdc4, #44a3aa);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.volume-slider {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #4ecdc4;
  border-radius: 50%;
  cursor: pointer;
}

.ambient-presets {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.5rem;
}

.preset-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #94a3b8;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.preset-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.preset-btn.active {
  background: linear-gradient(135deg, #4ecdc4, #44a3aa);
  border-color: transparent;
  color: white;
}

/* Analytics */
.study-analytics {
  overflow: hidden;
}

.study-analytics h3 {
  margin: 0 0 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.analytics-chart {
  height: 180px;
  margin-bottom: 1rem;
  position: relative;
}

.quick-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  transition: all 0.2s;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.stat-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  font-size: 1.25rem;
}

.stat-info {
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
}

.stat-label {
  font-size: 0.75rem;
  color: #94a3b8;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.stat-trend.up {
  color: #10b981;
}

.stat-trend.down {
  color: #ef4444;
}

/* Goals Tracker */
.goals-tracker {
  overflow: hidden;
}

.goals-tracker h3 {
  margin: 0 0 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.goals-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
  max-height: 250px;
  overflow-y: auto;
}

.goal-item {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  padding: 0.875rem;
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.goal-title {
  font-size: 0.875rem;
  font-weight: 500;
}

.goal-progress {
  font-size: 0.75rem;
  color: #94a3b8;
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.add-goal-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 10px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.add-goal-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Achievement Notifications */
.achievement-container {
  position: fixed;
  top: 100px;
  right: 1rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 400px;
}

.achievement-notification {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  color: white;
  min-width: 320px;
}

.achievement-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.achievement-content {
  flex: 1;
}

.achievement-content h4 {
  margin: 0 0 0.25rem;
  font-size: 1rem;
  font-weight: 600;
}

.achievement-content p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
}

.achievement-points {
  font-size: 1.125rem;
  font-weight: 600;
  color: #fbbf24;
}

/* Modal */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1a2332;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
}

.modal-content h2 {
  margin: 0 0 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.modal-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-input,
.modal-select {
  padding: 0.875rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  outline: none;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.modal-btn {
  flex: 1;
  padding: 0.875rem;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.modal-btn.cancel {
  background: rgba(255, 255, 255, 0.05);
  color: #94a3b8;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-btn.cancel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.modal-btn.confirm {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.modal-btn.confirm:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* Animations */
.achievement-enter-active,
.achievement-leave-active {
  transition: all 0.5s ease;
}

.achievement-enter-from {
  opacity: 0;
  transform: translateX(100px);
}

.achievement-leave-to {
  opacity: 0;
  transform: translateX(100px) scale(0.8);
}

.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-content,
.modal-leave-to .modal-content {
  transform: scale(0.9);
}

/* Responsive */
@media (max-width: 1400px) {
  .content-grid {
    grid-template-columns: minmax(280px, 320px) minmax(350px, 1fr) minmax(280px, 320px);
    gap: 1rem;
    padding: 1rem;
  }
  
  .timer-ring {
    width: 180px;
    height: 180px;
  }
  
  .time-text {
    font-size: 2rem;
  }
}

@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
    max-width: 900px;
  }
  
  .left-panel,
  .right-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1rem;
  }
  
  .center-panel {
    order: -1;
  }
  
  .tool-content {
    min-height: 400px;
    max-height: 600px;
  }
  
  .top-bar {
    padding: 1rem;
  }
  
  .environment-title h1 {
    font-size: 1.125rem;
  }
}

@media (max-width: 768px) {
  .top-bar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    position: relative;
  }
  
  .environment-title {
    justify-content: center;
  }
  
  .top-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .control-btn span {
    display: none;
  }
  
  .content-grid {
    padding: 0.75rem;
    gap: 1rem;
  }
  
  .left-panel,
  .right-panel {
    grid-template-columns: 1fr;
  }
  
  .card-3d {
    padding: 1rem;
  }
  
  .session-type-selector {
    flex-wrap: wrap;
    gap: 0.25rem;
  }
  
  .type-btn {
    padding: 0.5rem;
    font-size: 0.75rem;
  }
  
  .type-btn span {
    display: inline;
  }
  
  .timer-ring {
    width: 160px;
    height: 160px;
  }
  
  .time-text {
    font-size: 1.75rem;
  }
  
  .tasks-categories {
    grid-template-columns: 1fr;
  }
  
  .resources-grid {
    grid-template-columns: 1fr;
  }
  
  .achievement-container {
    right: 0.5rem;
    left: 0.5rem;
    top: 80px;
  }
  
  .achievement-notification {
    min-width: auto;
    font-size: 0.875rem;
    padding: 1rem;
  }
  
  .modal-content {
    margin: 1rem;
  }
}
</style>