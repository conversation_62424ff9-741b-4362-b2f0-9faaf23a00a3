# Marketing Components for Mentor-AI

This directory contains marketing components designed for the Mentor-AI landing page.

## Components

### 1. FeatureSlider.vue (Vue.js 3)

A responsive, interactive carousel component that showcases the key features of the Mentor-AI platform with images, icons, and descriptions.

**Features:**
- Responsive design (adapts to mobile, tablet, and desktop)
- Touch swipe support for mobile devices
- Automatic slideshow with pause on interaction
- Interactive indicators and navigation arrows
- Smooth animations and transitions
- Accessibility compliant

**Usage:**

```vue
<template>
  <div>
    <FeatureSlider />
  </div>
</template>

<script>
import FeatureSlider from '@/components/FeatureSlider.vue';

export default {
  components: {
    FeatureSlider
  }
}
</script>
```

### 2. FeatureSliderReact.jsx (React)

The same slider functionality implemented as a React component. Requires Bootstrap Icons to be installed in your project.

**Usage:**

```jsx
import FeatureSlider from './components/FeatureSliderReact';

function App() {
  return (
    <div className="App">
      <FeatureSlider />
    </div>
  );
}

export default App;
```

Make sure to import the CSS file in your React application:

```jsx
import './components/FeatureSlider.css';
```

### 3. MarketingHomePage.vue

A complete landing page template that incorporates the FeatureSlider component along with additional sections:
- Hero section with call-to-action buttons
- Features section with the slider
- About section
- Testimonials
- Sign-up form

**Usage:**

```vue
<template>
  <MarketingHomePage />
</template>

<script>
import MarketingHomePage from '@/components/MarketingHomePage.vue';

export default {
  components: {
    MarketingHomePage
  }
}
</script>
```

## Customization

### Modifying Feature Content

To change the features displayed in the slider, modify the `features` array in either component:

```js
features: [
  {
    title: 'Your Feature Title',
    description: 'Description of your feature...',
    icon: 'icon-name', // Bootstrap Icon name
    iconClass: 'color-class', // blue, green, purple, red, orange
    image: '/path/to/image.jpg'
  },
  // Add more features
]
```

### Styling

Both components use CSS variables for easy theming. You can override these variables in your main CSS file:

```css
:root {
  --color-primary: #6c55f9; /* Main brand color */
  --color-background: #f5f5fa; /* Light background */
  --color-background-light: #f9f9ff; /* Even lighter background */
  --color-text: #666; /* Body text color */
  --color-text-dark: #333; /* Headings color */
}
```

## Requirements

- Vue.js 3 (for Vue components)
- React (for React components)
- Bootstrap Icons (both versions use these for icons)

## Notes

- The default images used in the examples should be replaced with your own images.
- The Vue component automatically adapts to show 1, 2, or 3 cards based on screen width.
- The components are designed to work with dark mode if your site supports it.