<template>
  <header class="app-header">
    <div class="container">
      <!-- Logo Section - Redesigned -->
      <div class="logo-section">
        <router-link to="/" class="logo-link" @click="closeMenus">
          <div class="logo-content">
            <!-- Enhanced Brain Icon with Creative Animations -->
            <div class="creative-brain-container">
              <div class="brain-core">
                <font-awesome-icon icon="brain" class="brain-main" />
                <div class="brain-aura"></div>
              </div>
              <div class="brain-orbit">
                <div class="orbit-particle"></div>
                <div class="orbit-particle"></div>
                <div class="orbit-particle"></div>
              </div>
              <div class="neural-network">
                <span class="neural-line"></span>
                <span class="neural-line"></span>
                <span class="neural-line"></span>
                <span class="neural-line"></span>
              </div>
            </div>
            
            <!-- Text Container -->
            <div class="text-container">
              <div class="text-primary">Sophos</div>
              <div class="text-divider"></div>
              <div class="text-secondary">Academy</div>
            </div>
          </div>
        </router-link>
        
        <!-- Navigation Menu Trigger -->
        <button class="menu-trigger" @click="toggleLogoMenu" :class="{ 'active': logoMenuOpen }">
          <div class="menu-trigger-creative">
            <div class="trigger-logo">
              <div class="logo-grid">
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
                <div class="grid-dot center-dot">
                  <div class="pulse-ring"></div>
                </div>
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
              </div>
            </div>
          </div>
        </button>

        <!-- Enhanced Navigation Dropdown -->
        <transition name="slide-fade">
          <div v-if="logoMenuOpen" class="navigation-dropdown enhanced" @click.stop>
            <div class="dropdown-inner">
              <div class="dropdown-header">
                <font-awesome-icon icon="compass" />
                <h3>Menu Principal</h3>
                <p class="dropdown-subtitle">Explore todas as ferramentas e recursos</p>
              </div>

              <!-- Quick Stats Bar -->
              <div class="quick-stats-bar" v-if="userStats">
                <div class="stat-item">
                  <font-awesome-icon icon="fa-fire" />
                  <span>{{ userStats.streak }} dias</span>
                </div>
                <div class="stat-item">
                  <font-awesome-icon icon="fa-layer-group" />
                  <span>{{ userStats.pendingCards }} cards</span>
                </div>
                <div class="stat-item">
                  <font-awesome-icon icon="fa-clock" />
                  <span>{{ userStats.todayHours }}h hoje</span>
                </div>
              </div>

              <!-- Featured Tools Section -->
              <div class="featured-tools">
                <h5>Acesso Rápido</h5>
                <div class="featured-grid">
                  <router-link to="/engines" class="featured-card engines" @click="closeLogoMenu">
                    <div class="featured-icon">
                      <font-awesome-icon icon="rocket" />
                    </div>
                    <span>IA Central</span>
                    <div class="featured-badge">Novo</div>
                  </router-link>
                  
                  <router-link to="/flashcards" class="featured-card flashcards" @click="closeLogoMenu">
                    <div class="featured-icon">
                      <font-awesome-icon icon="layer-group" />
                    </div>
                    <span>Flashcards</span>
                    <div class="featured-indicator" v-if="userStats?.pendingCards > 0">
                      {{ userStats.pendingCards }}
                    </div>
                  </router-link>
                  
                  <router-link to="/revision-scheduler" class="featured-card revision" @click="closeLogoMenu">
                    <div class="featured-icon">
                      <font-awesome-icon icon="calendar-alt" />
                    </div>
                    <span>Revisões</span>
                  </router-link>
                  
                </div>
              </div>

              <!-- Navigation Grid -->
              <div class="nav-grid enhanced">
                <div class="nav-column">
                  <div class="column-header">
                    <font-awesome-icon icon="graduation-cap" />
                    <h5>Estudo Diário</h5>
                  </div>
                  <router-link v-for="item in dailyStudyItems" :key="item.path"
                              :to="item.path" class="nav-link" @click="closeLogoMenu">
                    <font-awesome-icon :icon="item.icon" />
                    <div class="link-content">
                      <span class="link-title">{{ item.title }}</span>
                      <span class="link-desc" v-if="item.description">{{ item.description }}</span>
                    </div>
                  </router-link>
                </div>

                <div class="nav-column">
                  <div class="column-header">
                    <font-awesome-icon icon="robot" />
                    <h5>IA Tools</h5>
                  </div>
                  <router-link v-for="item in aiToolsItems" :key="item.path"
                              :to="item.path" class="nav-link" @click="closeLogoMenu">
                    <font-awesome-icon :icon="item.icon" />
                    <div class="link-content">
                      <span class="link-title">{{ item.title }}</span>
                      <span class="link-desc" v-if="item.description">{{ item.description }}</span>
                    </div>
                    <div class="link-badge" v-if="item.isNew">Novo</div>
                  </router-link>
                </div>

                <div class="nav-column">
                  <div class="column-header">
                    <font-awesome-icon icon="book-open" />
                    <h5>Recursos</h5>
                  </div>
                  <router-link v-for="item in resourceItems" :key="item.path"
                              :to="item.path" class="nav-link" @click="closeLogoMenu">
                    <font-awesome-icon :icon="item.icon" />
                    <div class="link-content">
                      <span class="link-title">{{ item.title }}</span>
                      <span class="link-desc" v-if="item.description">{{ item.description }}</span>
                    </div>
                  </router-link>
                </div>
              </div>

              <!-- Footer Actions -->
              <div class="dropdown-footer">
                <router-link to="/help" class="footer-link" @click="closeLogoMenu">
                  <font-awesome-icon icon="question-circle" />
                  <span>Central de Ajuda</span>
                </router-link>
                <router-link to="/settings" class="footer-link" @click="closeLogoMenu">
                  <font-awesome-icon icon="cog" />
                  <span>Configurações</span>
                </router-link>
              </div>
            </div>
          </div>
        </transition>
      </div>

      <!-- Search Bar - Enhanced with AI -->
      <div class="search-container">
        <div class="search-wrapper" :class="{ 'focused': isSearchFocused, 'has-results': searchResults.length > 0 }">
          <font-awesome-icon icon="search" class="search-icon" />
          <input 
            type="text" 
            class="search-input" 
            placeholder="Buscar aulas, flashcards, ferramentas..."
            v-model="searchQuery"
            @focus="handleSearchFocus"
            @blur="handleSearchBlur"
            @input="performSearch"
          />
          <div class="search-ai-indicator" v-if="isSearchFocused">
            <font-awesome-icon icon="brain" />
            <span>IA</span>
          </div>
          
          <!-- Search Results Dropdown -->
          <transition name="fade">
            <div v-if="isSearchFocused && searchResults.length > 0" class="search-results">
              <div v-for="(result, index) in searchResults" :key="index" 
                   class="search-result-item"
                   @click="navigateToResult(result)">
                <font-awesome-icon :icon="result.icon" class="result-icon" />
                <div class="result-content">
                  <span class="result-title">{{ result.title }}</span>
                  <span class="result-type">{{ result.type }}</span>
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>


      <!-- Actions -->
      <div class="actions-container">
        <!-- Notifications -->
        <div class="notifications-trigger">
          <button class="notification-button" @click="toggleNotifications">
            <font-awesome-icon icon="bell" />
            <span class="notification-badge" v-if="unreadNotifications > 0">
              {{ unreadNotifications }}
            </span>
          </button>
        </div>

        <!-- Theme Toggle -->
        <div class="theme-switch">
          <input 
            type="checkbox" 
            id="theme-toggle"
            :checked="isDarkMode"
            @change="toggleTheme"
            class="theme-checkbox"
          >
          <label for="theme-toggle" class="theme-label">
            <span class="theme-slider">
              <font-awesome-icon :icon="isDarkMode ? 'fa-moon' : 'fa-sun'" />
            </span>
          </label>
        </div>

        <!-- User Menu -->
        <div class="user-menu" v-if="currentUser">
          <button class="user-button" @click="toggleUserMenu" :class="{ 'active': userMenuOpen }">
            <div class="user-avatar-creative">
              <div class="avatar-core">
                <span class="avatar-text">{{ userInitials }}</span>
                <div class="avatar-glow"></div>
              </div>
              <div class="avatar-particles">
                <span class="particle"></span>
                <span class="particle"></span>
                <span class="particle"></span>
              </div>
              <div class="neural-connections">
                <span class="connection"></span>
                <span class="connection"></span>
                <span class="connection"></span>
                <span class="connection"></span>
              </div>
            </div>
            <span class="user-name">{{ currentUser.name || currentUser.email.split('@')[0] }}</span>
            <font-awesome-icon icon="fa-chevron-down" class="user-arrow" />
          </button>

          <transition name="dropdown">
            <div v-if="userMenuOpen" class="user-dropdown enhanced">
              <div class="dropdown-user-info">
                <div class="info-avatar">
                  <span>{{ userInitials }}</span>
                </div>
                <div class="info-details">
                  <div class="info-name">{{ currentUser.name || 'Usuário' }}</div>
                  <div class="info-email">{{ currentUser.email }}</div>
                  <div class="info-stats">
                    <span><font-awesome-icon icon="fire" /> {{ userStats?.streak || 0 }} dias</span>
                    <span><font-awesome-icon icon="trophy" /> Nível {{ userLevel }}</span>
                  </div>
                </div>
              </div>

              <div class="dropdown-divider"></div>

              <router-link to="/profile" @click="closeUserMenu" class="dropdown-link">
                <font-awesome-icon icon="fa-user" />
                <span>Meu Perfil</span>
              </router-link>
              <router-link to="/achievements" @click="closeUserMenu" class="dropdown-link">
                <font-awesome-icon icon="fa-medal" />
                <span>Conquistas</span>
                <span class="link-badge">3 novas</span>
              </router-link>
              <router-link to="/settings" @click="closeUserMenu" class="dropdown-link">
                <font-awesome-icon icon="fa-cog" />
                <span>Configurações</span>
              </router-link>
              <router-link to="/help" @click="closeUserMenu" class="dropdown-link">
                <font-awesome-icon icon="fa-question-circle" />
                <span>Ajuda</span>
              </router-link>

              <div class="dropdown-divider"></div>

              <button @click="logout" class="dropdown-link logout">
                <font-awesome-icon icon="fa-sign-out-alt" />
                <span>Sair</span>
              </button>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'AppHeaderEnhanced',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    // Estados reativos
    const logoMenuOpen = ref(false)
    const userMenuOpen = ref(false)
    const searchQuery = ref('')
    const isSearchFocused = ref(false)
    const searchResults = ref([])
    const unreadNotifications = ref(3)
    
    // Computed properties
    const currentUser = computed(() => store.state.user)
    const isDarkMode = computed(() => store.getters.isDarkMode)
    const userInitials = computed(() => {
      if (!currentUser.value?.name) return 'U'
      return currentUser.value.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
    })
    const userLevel = computed(() => {
      const days = userStats.value?.streak || 0
      if (days >= 100) return 'Expert'
      if (days >= 50) return 'Avançado'
      if (days >= 20) return 'Intermediário'
      return 'Iniciante'
    })
    
    // Mock user stats - em produção viria da store
    const userStats = ref({
      streak: 15,
      pendingCards: 23,
      todayHours: 2.5
    })
    
    // Navigation items com descrições
    const dailyStudyItems = ref([
      { 
        path: '/calendar', 
        icon: 'calendar', 
        title: 'Calendário',
        description: 'Organize suas sessões'
      },
      { 
        path: '/revision-scheduler', 
        icon: 'clock', 
        title: 'Agendador de Revisões',
        description: 'Otimizado por IA'
      },
      { 
        path: '/plano-estudo', 
        icon: 'tasks', 
        title: 'Plano de Estudo',
        description: 'Roteiro personalizado'
      },
      { 
        path: '/pomodoro', 
        icon: 'stopwatch', 
        title: 'Pomodoro Timer',
        description: 'Sessões focadas'
      },
      { 
        path: '/performance', 
        icon: 'chart-line', 
        title: 'Desempenho',
        description: 'Análise detalhada'
      }
    ])
    
    const aiToolsItems = ref([
      { 
        path: '/flashcards', 
        icon: 'layer-group', 
        title: 'Flashcards IA',
        description: 'Aprendizado espaçado'
      },
      { 
        path: '/ai-tools/second-brain', 
        icon: 'brain', 
        title: 'Second Brain',
        description: 'Assistente inteligente'
      },
      { 
        path: '/round-ai', 
        icon: 'sync', 
        title: 'Round AI',
        description: 'Revisão circular'
      },
      { 
        path: '/ai-tools/study-assistant', 
        icon: 'robot', 
        title: 'Assistente de Estudo',
        description: 'Tutor personalizado'
      }
    ])
    
    const resourceItems = ref([
      { 
        path: '/resources', 
        icon: 'book', 
        title: 'Recursos',
        description: 'Material de estudo'
      },
      { 
        path: '/videos', 
        icon: 'video', 
        title: 'Videoaulas',
        description: 'Aulas gravadas'
      },
      { 
        path: '/notas', 
        icon: 'sticky-note', 
        title: 'Notas & Resumos',
        description: 'Anotações organizadas'
      },
      { 
        path: '/exames', 
        icon: 'file-medical', 
        title: 'Análise de Exames',
        description: 'IA para exames médicos'
      },
      { 
        path: '/questoes', 
        icon: 'question-circle', 
        title: 'Banco de Questões',
        description: 'Pratique com questões'
      }
    ])
    
    // Métodos
    const toggleLogoMenu = () => {
      logoMenuOpen.value = !logoMenuOpen.value
      if (logoMenuOpen.value) userMenuOpen.value = false
    }
    
    const toggleUserMenu = () => {
      userMenuOpen.value = !userMenuOpen.value
      if (userMenuOpen.value) logoMenuOpen.value = false
    }
    
    const toggleNotifications = () => {
      // Implementar lógica de notificações
      console.log('Toggle notifications')
    }
    
    const closeMenus = () => {
      logoMenuOpen.value = false
      userMenuOpen.value = false
    }
    
    const closeLogoMenu = () => {
      logoMenuOpen.value = false
    }
    
    const closeUserMenu = () => {
      userMenuOpen.value = false
    }
    
    const toggleTheme = () => {
      store.dispatch('toggleTheme')
    }
    
    const logout = async () => {
      await store.dispatch('logout')
      router.push('/login')
    }
    
    // Search functionality
    const handleSearchFocus = () => {
      isSearchFocused.value = true
      if (searchQuery.value.length > 0) {
        performSearch()
      }
    }
    
    const handleSearchBlur = () => {
      // Delay para permitir clique nos resultados
      setTimeout(() => {
        isSearchFocused.value = false
      }, 200)
    }
    
    const performSearch = () => {
      if (searchQuery.value.length < 2) {
        searchResults.value = []
        return
      }
      
      // Mock search results - em produção seria uma API call
      searchResults.value = [
        {
          title: 'Flashcards de Anatomia',
          type: 'Flashcards',
          icon: 'layer-group',
          path: '/flashcards?filter=anatomia'
        },
        {
          title: 'Videoaula: Sistema Nervoso',
          type: 'Vídeo',
          icon: 'video',
          path: '/videos/sistema-nervoso'
        },
      ].filter(item => 
        item.title.toLowerCase().includes(searchQuery.value.toLowerCase())
      )
    }
    
    const navigateToResult = (result) => {
      router.push(result.path)
      searchQuery.value = ''
      searchResults.value = []
      isSearchFocused.value = false
    }
    
    // Click outside handler
    const handleClickOutside = (event) => {
      const header = document.querySelector('.app-header')
      if (header && !header.contains(event.target)) {
        closeMenus()
      }
    }
    
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
    })
    
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })
    
    return {
      logoMenuOpen,
      userMenuOpen,
      searchQuery,
      isSearchFocused,
      searchResults,
      unreadNotifications,
      currentUser,
      isDarkMode,
      userInitials,
      userLevel,
      userStats,
      dailyStudyItems,
      aiToolsItems,
      resourceItems,
      toggleLogoMenu,
      toggleUserMenu,
      toggleNotifications,
      closeMenus,
      closeLogoMenu,
      closeUserMenu,
      toggleTheme,
      logout,
      handleSearchFocus,
      handleSearchBlur,
      performSearch,
      navigateToResult
    }
  }
}
</script>

<style scoped>
/* Base styles - mantém todos os estilos existentes */
@import './AppHeader-styles.css';

/* Enhanced styles */
.navigation-dropdown.enhanced {
  max-width: 720px;
  max-height: 85vh;
  overflow-y: auto;
}

.dropdown-subtitle {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

/* Quick Stats Bar */
.quick-stats-bar {
  display: flex;
  gap: 1.5rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: 0.75rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.stat-item svg {
  color: var(--accent-color);
}

/* Featured Tools */
.featured-tools {
  margin-bottom: 2rem;
}

.featured-tools h5 {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
}

.featured-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--bg-primary);
  border-radius: 0.75rem;
  text-decoration: none;
  color: var(--text-primary);
  font-size: 0.85rem;
  transition: all 0.3s ease;
  position: relative;
}

.featured-card:hover {
  transform: translateY(-2px);
  background: var(--hover-bg);
}

.featured-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  font-size: 1.25rem;
}

.featured-card.engines .featured-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.featured-card.flashcards .featured-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.featured-card.revision .featured-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}


.featured-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--accent-color);
  color: white;
  font-size: 0.7rem;
  padding: 0.15rem 0.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
}

.featured-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: white;
  font-size: 0.75rem;
  padding: 0.1rem 0.4rem;
  border-radius: 50%;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

/* Enhanced Nav Grid */
.nav-grid.enhanced .column-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.column-header svg {
  color: var(--accent-color);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  margin: 0.25rem 0;
  border-radius: 0.5rem;
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  background: var(--hover-bg);
  transform: translateX(5px);
}

.link-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.link-title {
  font-weight: 500;
  font-size: 0.95rem;
}

.link-desc {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.1rem;
}

.link-badge {
  background: var(--accent-color);
  color: white;
  font-size: 0.7rem;
  padding: 0.15rem 0.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
}

/* Dropdown Footer */
.dropdown-footer {
  display: flex;
  gap: 1rem;
  padding-top: 1.5rem;
  margin-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.footer-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  text-decoration: none;
  color: var(--text-secondary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
}

.footer-link:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

/* Enhanced Search */
.search-wrapper.has-results {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.search-ai-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  background: var(--accent-color);
  color: white;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--dropdown-bg);
  border: 1px solid var(--border-color);
  border-top: none;
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  max-height: 300px;
  overflow-y: auto;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.search-result-item:hover {
  background: var(--hover-bg);
}

.result-icon {
  font-size: 1.25rem;
  color: var(--accent-color);
}

.result-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.result-title {
  font-weight: 500;
  color: var(--text-primary);
}

.result-type {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* Notifications */
.notifications-trigger {
  position: relative;
}

.notification-button {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  position: relative;
}

.notification-button:hover {
  background: var(--hover-bg);
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ff4757;
  color: white;
  font-size: 0.7rem;
  padding: 0.1rem 0.4rem;
  border-radius: 50%;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
}

/* Enhanced User Dropdown */
.user-dropdown.enhanced .info-stats {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.user-dropdown.enhanced .info-stats svg {
  color: var(--accent-color);
}

/* Dark mode variables */
:root[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --text-primary: #ffffff;
  --text-secondary: #aaaaaa;
  --border-color: #333333;
  --hover-bg: #2a2a2a;
  --dropdown-bg: #1e1e1e;
  --accent-color: #667eea;
}
</style>