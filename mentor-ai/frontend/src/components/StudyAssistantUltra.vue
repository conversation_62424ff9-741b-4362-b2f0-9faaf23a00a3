<template>
  <div class="study-assistant-ultra">
    <!-- Animated Background -->
    <div class="animated-bg">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="neural-network-bg"></div>
    </div>

    <!-- Header Section Ultra -->
    <header class="assistant-header-ultra">
      <div class="header-content">
        <div class="header-left">
          <div class="ai-badge-ultra">
            <div class="ai-core">
              <i class="fas fa-brain"></i>
              <div class="ai-pulse"></div>
            </div>
            <span class="ai-status">{{ aiStatus }}</span>
          </div>
          
          <div class="title-section">
            <h1 class="page-title-ultra">
              <span class="title-gradient">Assistente de Estudo Neural</span>
              <span class="version-badge">v5.0 Ultra</span>
            </h1>
            <p class="subtitle-ultra">
              Inteligência Artificial Avançada para Otimização do Aprendizado Médico
            </p>
          </div>
        </div>

        <div class="header-right">
          <div class="user-stats-mini">
            <div class="stat-mini">
              <i class="fas fa-fire"></i>
              <span>{{ userStats.streak }} dias</span>
            </div>
            <div class="stat-mini">
              <i class="fas fa-trophy"></i>
              <span>Nível {{ userStats.level }}</span>
            </div>
            <div class="stat-mini">
              <i class="fas fa-star"></i>
              <span>{{ userStats.xp }} XP</span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Dashboard -->
    <div class="main-dashboard-ultra">
      <!-- AI Analysis Panel -->
      <section class="ai-analysis-panel">
        <div class="panel-header">
          <h2>
            <i class="fas fa-chart-network"></i>
            Análise Neural em Tempo Real
          </h2>
          <button class="refresh-btn" @click="refreshAnalysis" :class="{ spinning: isAnalyzing }">
            <i class="fas fa-sync-alt"></i>
          </button>
        </div>

        <div class="analysis-grid">
          <!-- Performance Radar -->
          <div class="analysis-card radar-card">
            <h3>Mapa de Competências</h3>
            <canvas ref="performanceRadar"></canvas>
            <div class="radar-insights">
              <div class="insight-item" v-for="insight in radarInsights" :key="insight.id">
                <i :class="insight.icon" :style="{ color: insight.color }"></i>
                <span>{{ insight.text }}</span>
              </div>
            </div>
          </div>

          <!-- Learning Velocity -->
          <div class="analysis-card velocity-card">
            <h3>Velocidade de Aprendizado</h3>
            <div class="velocity-chart">
              <canvas ref="velocityChart"></canvas>
            </div>
            <div class="velocity-stats">
              <div class="velocity-stat">
                <div class="stat-label">Taxa Atual</div>
                <div class="stat-value">{{ learningVelocity.current }}x</div>
              </div>
              <div class="velocity-stat">
                <div class="stat-label">Previsão</div>
                <div class="stat-value">{{ learningVelocity.prediction }}</div>
              </div>
            </div>
          </div>

          <!-- AI Recommendations -->
          <div class="analysis-card recommendations-card">
            <h3>Recomendações da IA</h3>
            <div class="recommendations-list">
              <div 
                v-for="(rec, index) in aiRecommendations" 
                :key="index"
                class="recommendation-item"
                :class="rec.priority"
                @click="applyRecommendation(rec)"
              >
                <div class="rec-icon">
                  <i :class="rec.icon"></i>
                </div>
                <div class="rec-content">
                  <h4>{{ rec.title }}</h4>
                  <p>{{ rec.description }}</p>
                  <div class="rec-meta">
                    <span class="impact">+{{ rec.impact }}% eficiência</span>
                    <span class="time">{{ rec.time }}</span>
                  </div>
                </div>
                <div class="rec-action">
                  <i class="fas fa-arrow-right"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Smart Study Tools -->
      <section class="smart-tools-section">
        <div class="section-header">
          <h2>
            <i class="fas fa-toolbox"></i>
            Ferramentas Inteligentes de Estudo
          </h2>
        </div>

        <div class="tools-grid-ultra">
          <!-- Neural Study Planner -->
          <div class="tool-card-ultra" @click="openTool('neural-planner')">
            <div class="tool-icon-wrapper neural">
              <i class="fas fa-project-diagram"></i>
              <div class="tool-particles"></div>
            </div>
            <h3>Planejador Neural</h3>
            <p>IA cria plano otimizado baseado em neurociência</p>
            <div class="tool-stats">
              <span><i class="fas fa-users"></i> 15k usuários</span>
              <span><i class="fas fa-star"></i> 4.9</span>
            </div>
          </div>

          <!-- Memory Optimizer -->
          <div class="tool-card-ultra" @click="openTool('memory-optimizer')">
            <div class="tool-icon-wrapper memory">
              <i class="fas fa-brain"></i>
              <div class="tool-particles"></div>
            </div>
            <h3>Otimizador de Memória</h3>
            <p>Técnicas avançadas de memorização e retenção</p>
            <div class="tool-stats">
              <span><i class="fas fa-chart-line"></i> +85% retenção</span>
              <span><i class="fas fa-star"></i> 4.8</span>
            </div>
          </div>

          <!-- Concept Mapper 3D -->
          <div class="tool-card-ultra" @click="openTool('concept-3d')">
            <div class="tool-icon-wrapper concept">
              <i class="fas fa-cube"></i>
              <div class="tool-particles"></div>
            </div>
            <h3>Mapa Conceitual 3D</h3>
            <p>Visualize conexões em realidade aumentada</p>
            <div class="tool-stats">
              <span><i class="fas fa-vr-cardboard"></i> Imersivo</span>
              <span><i class="fas fa-star"></i> 5.0</span>
            </div>
          </div>

          <!-- AI Question Generator -->
          <div class="tool-card-ultra" @click="openTool('ai-questions')">
            <div class="tool-icon-wrapper questions">
              <i class="fas fa-question-circle"></i>
              <div class="tool-particles"></div>
            </div>
            <h3>Gerador de Questões IA</h3>
            <p>Questões personalizadas no seu nível exato</p>
            <div class="tool-stats">
              <span><i class="fas fa-bullseye"></i> Precisão 95%</span>
              <span><i class="fas fa-star"></i> 4.7</span>
            </div>
          </div>

          <!-- Focus Mode Pro -->
          <div class="tool-card-ultra" @click="openTool('focus-pro')">
            <div class="tool-icon-wrapper focus">
              <i class="fas fa-eye"></i>
              <div class="tool-particles"></div>
            </div>
            <h3>Modo Foco Pro</h3>
            <p>Ambiente otimizado com ondas binaurais</p>
            <div class="tool-stats">
              <span><i class="fas fa-headphones"></i> Áudio 3D</span>
              <span><i class="fas fa-star"></i> 4.9</span>
            </div>
          </div>

          <!-- Smart Resources -->
          <div class="tool-card-ultra" @click="openTool('smart-resources')">
            <div class="tool-icon-wrapper resources">
              <i class="fas fa-database"></i>
              <div class="tool-particles"></div>
            </div>
            <h3>Recursos Inteligentes</h3>
            <p>IA encontra os melhores materiais para você</p>
            <div class="tool-stats">
              <span><i class="fas fa-book"></i> 50k+ recursos</span>
              <span><i class="fas fa-star"></i> 4.8</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Active Learning Session -->
      <section class="active-session-section" v-if="activeSession">
        <div class="session-header">
          <h2>
            <i class="fas fa-graduation-cap"></i>
            Sessão Ativa: {{ activeSession.title }}
          </h2>
          <div class="session-controls">
            <button class="control-btn" @click="pauseSession">
              <i class="fas fa-pause"></i>
            </button>
            <button class="control-btn" @click="endSession">
              <i class="fas fa-stop"></i>
            </button>
          </div>
        </div>

        <div class="session-content">
          <div class="session-progress">
            <div class="progress-info">
              <span>Progresso da Sessão</span>
              <span>{{ activeSession.progress }}%</span>
            </div>
            <div class="progress-bar-ultra">
              <div 
                class="progress-fill-ultra" 
                :style="{ width: activeSession.progress + '%' }"
              ></div>
            </div>
          </div>

          <div class="session-metrics">
            <div class="metric-card">
              <i class="fas fa-clock"></i>
              <div class="metric-info">
                <div class="metric-value">{{ sessionTime }}</div>
                <div class="metric-label">Tempo</div>
              </div>
            </div>
            <div class="metric-card">
              <i class="fas fa-brain"></i>
              <div class="metric-info">
                <div class="metric-value">{{ activeSession.focus }}%</div>
                <div class="metric-label">Foco</div>
              </div>
            </div>
            <div class="metric-card">
              <i class="fas fa-tachometer-alt"></i>
              <div class="metric-info">
                <div class="metric-value">{{ activeSession.efficiency }}%</div>
                <div class="metric-label">Eficiência</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Achievements & Gamification -->
      <section class="achievements-section">
        <div class="section-header">
          <h2>
            <i class="fas fa-trophy"></i>
            Conquistas & Progresso
          </h2>
          <button class="view-all-btn" @click="showAllAchievements">
            Ver Todas
          </button>
        </div>

        <div class="achievements-grid">
          <div 
            v-for="achievement in recentAchievements" 
            :key="achievement.id"
            class="achievement-card"
            :class="{ unlocked: achievement.unlocked, legendary: achievement.rarity === 'legendary' }"
          >
            <div class="achievement-icon">
              <i :class="achievement.icon"></i>
              <div class="achievement-glow"></div>
            </div>
            <div class="achievement-info">
              <h4>{{ achievement.title }}</h4>
              <p>{{ achievement.description }}</p>
              <div class="achievement-progress" v-if="!achievement.unlocked">
                <div class="progress-mini">
                  <div 
                    class="progress-mini-fill" 
                    :style="{ width: achievement.progress + '%' }"
                  ></div>
                </div>
                <span>{{ achievement.progress }}%</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Tool Modals -->
    <transition name="modal-fade">
      <div v-if="activeToolModal" class="tool-modal-overlay" @click.self="closeToolModal">
        <div class="tool-modal-content">
          <!-- Neural Planner Modal -->
          <div v-if="activeToolModal === 'neural-planner'" class="neural-planner-modal">
            <div class="modal-header">
              <h2>Planejador Neural de Estudos</h2>
              <button class="close-btn" @click="closeToolModal">
                <i class="fas fa-times"></i>
              </button>
            </div>
            
            <div class="modal-body">
              <div class="planner-config">
                <h3>Configuração do Plano</h3>
                
                <div class="config-grid">
                  <div class="config-item">
                    <label>Objetivo Principal</label>
                    <select v-model="plannerConfig.goal" class="config-select">
                      <option value="residency">Residência Médica</option>
                      <option value="revalida">Revalida</option>
                      <option value="specialty">Especialização</option>
                      <option value="review">Revisão Geral</option>
                    </select>
                  </div>
                  
                  <div class="config-item">
                    <label>Tempo Disponível</label>
                    <div class="time-input">
                      <input type="number" v-model="plannerConfig.hoursPerDay" min="1" max="12">
                      <span>horas/dia</span>
                    </div>
                  </div>
                  
                  <div class="config-item">
                    <label>Prazo</label>
                    <div class="date-picker">
                      <input type="date" v-model="plannerConfig.deadline">
                    </div>
                  </div>
                  
                  <div class="config-item full-width">
                    <label>Áreas de Foco</label>
                    <div class="areas-selector">
                      <div 
                        v-for="area in medicalAreas" 
                        :key="area.id"
                        class="area-chip"
                        :class="{ selected: plannerConfig.selectedAreas.includes(area.id) }"
                        @click="toggleArea(area.id)"
                      >
                        <i :class="area.icon"></i>
                        <span>{{ area.name }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="ai-options">
                  <h4>Opções de IA</h4>
                  <label class="ai-toggle">
                    <input type="checkbox" v-model="plannerConfig.adaptiveLearning">
                    <span>Aprendizado Adaptativo</span>
                    <i class="fas fa-info-circle" title="Ajusta o plano baseado no seu progresso"></i>
                  </label>
                  <label class="ai-toggle">
                    <input type="checkbox" v-model="plannerConfig.spaceRepetition">
                    <span>Repetição Espaçada Inteligente</span>
                    <i class="fas fa-info-circle" title="Otimiza revisões para máxima retenção"></i>
                  </label>
                  <label class="ai-toggle">
                    <input type="checkbox" v-model="plannerConfig.personalizedContent">
                    <span>Conteúdo Personalizado</span>
                    <i class="fas fa-info-circle" title="Adapta materiais ao seu estilo de aprendizado"></i>
                  </label>
                </div>
                
                <button class="generate-plan-btn" @click="generateNeuralPlan" :disabled="isGeneratingPlan">
                  <i class="fas fa-magic"></i>
                  <span v-if="!isGeneratingPlan">Gerar Plano Neural</span>
                  <span v-else>Processando com IA...</span>
                </button>
              </div>
              
              <!-- Generated Plan Display -->
              <div v-if="generatedPlan" class="generated-plan-display">
                <h3>Seu Plano Personalizado</h3>
                
                <div class="plan-overview">
                  <div class="overview-stat">
                    <i class="fas fa-calendar-alt"></i>
                    <div>
                      <strong>{{ generatedPlan.duration }}</strong>
                      <span>Duração Total</span>
                    </div>
                  </div>
                  <div class="overview-stat">
                    <i class="fas fa-book"></i>
                    <div>
                      <strong>{{ generatedPlan.topics }}</strong>
                      <span>Tópicos</span>
                    </div>
                  </div>
                  <div class="overview-stat">
                    <i class="fas fa-chart-line"></i>
                    <div>
                      <strong>{{ generatedPlan.successRate }}%</strong>
                      <span>Taxa de Sucesso</span>
                    </div>
                  </div>
                </div>
                
                <div class="plan-timeline">
                  <h4>Cronograma Detalhado</h4>
                  <div class="timeline-container">
                    <div 
                      v-for="(phase, index) in generatedPlan.phases" 
                      :key="index"
                      class="timeline-phase"
                    >
                      <div class="phase-marker">
                        <i :class="phase.icon"></i>
                      </div>
                      <div class="phase-content">
                        <h5>{{ phase.name }}</h5>
                        <p>{{ phase.duration }} • {{ phase.topics }} tópicos</p>
                        <div class="phase-topics">
                          <span 
                            v-for="topic in phase.mainTopics" 
                            :key="topic"
                            class="topic-tag"
                          >
                            {{ topic }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="plan-actions">
                  <button class="action-btn primary" @click="startPlan">
                    <i class="fas fa-play"></i>
                    Iniciar Plano
                  </button>
                  <button class="action-btn secondary" @click="savePlan">
                    <i class="fas fa-save"></i>
                    Salvar
                  </button>
                  <button class="action-btn secondary" @click="exportPlan">
                    <i class="fas fa-download"></i>
                    Exportar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Floating AI Assistant -->
    <div class="ai-assistant-floating" :class="{ expanded: assistantExpanded }">
      <button class="assistant-toggle" @click="toggleAssistant">
        <i class="fas fa-robot"></i>
        <span class="assistant-notification" v-if="hasNewInsight">!</span>
      </button>
      
      <transition name="slide-up">
        <div v-if="assistantExpanded" class="assistant-panel">
          <div class="assistant-header">
            <h3>Assistente Neural</h3>
            <button class="minimize-btn" @click="toggleAssistant">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          
          <div class="assistant-messages">
            <div 
              v-for="(message, index) in assistantMessages" 
              :key="index"
              class="message"
              :class="message.type"
            >
              <i :class="message.icon"></i>
              <p>{{ message.text }}</p>
            </div>
          </div>
          
          <div class="assistant-input">
            <input 
              type="text" 
              v-model="assistantQuery"
              @keyup.enter="askAssistant"
              placeholder="Pergunte algo..."
            >
            <button @click="askAssistant">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import Chart from 'chart.js/auto'

export default {
  name: 'StudyAssistantUltra',
  
  setup() {
    const store = useStore()
    
    // Refs
    const performanceRadar = ref(null)
    const velocityChart = ref(null)
    
    // State
    const aiStatus = ref('Analisando padrões...')
    const isAnalyzing = ref(false)
    const activeToolModal = ref(null)
    const isGeneratingPlan = ref(false)
    const generatedPlan = ref(null)
    const assistantExpanded = ref(false)
    const hasNewInsight = ref(true)
    const assistantQuery = ref('')
    
    // User Stats
    const userStats = ref({
      streak: 15,
      level: 23,
      xp: 4580
    })
    
    // Learning Data
    const learningVelocity = ref({
      current: '2.3',
      prediction: '+15% esta semana'
    })
    
    const radarInsights = ref([
      { id: 1, icon: 'fas fa-star', color: '#42b983', text: 'Cardiologia em destaque' },
      { id: 2, icon: 'fas fa-exclamation-triangle', color: '#ff9800', text: 'Reforce Neurologia' },
      { id: 3, icon: 'fas fa-chart-line', color: '#2196f3', text: 'Progresso constante' }
    ])
    
    const aiRecommendations = ref([
      {
        id: 1,
        priority: 'high',
        icon: 'fas fa-brain',
        title: 'Sessão de Neurologia Intensiva',
        description: 'IA detectou lacunas em anatomia do SNC. Sessão focada recomendada.',
        impact: 35,
        time: '45 min'
      },
      {
        id: 2,
        priority: 'medium',
        icon: 'fas fa-sync-alt',
        title: 'Revisão Espaçada de Farmacologia',
        description: 'Momento ideal para revisar antibióticos e suas interações.',
        impact: 25,
        time: '30 min'
      },
      {
        id: 3,
        priority: 'low',
        icon: 'fas fa-coffee',
        title: 'Pausa Estratégica',
        description: 'Seus níveis de foco indicam necessidade de descanso.',
        impact: 20,
        time: '15 min'
      }
    ])
    
    // Active Session
    const activeSession = ref({
      title: 'Cardiologia - Arritmias',
      progress: 65,
      focus: 88,
      efficiency: 92
    })
    
    const sessionTime = ref('32:45')
    
    // Achievements
    const recentAchievements = ref([
      {
        id: 1,
        icon: 'fas fa-fire',
        title: 'Maratonista',
        description: 'Estude 7 dias seguidos',
        unlocked: true,
        rarity: 'common'
      },
      {
        id: 2,
        icon: 'fas fa-brain',
        title: 'Mestre Neural',
        description: 'Complete 100 sessões com IA',
        unlocked: false,
        progress: 78,
        rarity: 'legendary'
      },
      {
        id: 3,
        icon: 'fas fa-trophy',
        title: 'Top 1%',
        description: 'Entre os melhores do ranking',
        unlocked: false,
        progress: 45,
        rarity: 'rare'
      }
    ])
    
    // Planner Config
    const plannerConfig = ref({
      goal: 'residency',
      hoursPerDay: 6,
      deadline: '',
      selectedAreas: ['cardiology', 'neurology'],
      adaptiveLearning: true,
      spaceRepetition: true,
      personalizedContent: true
    })
    
    const medicalAreas = ref([
      { id: 'cardiology', name: 'Cardiologia', icon: 'fas fa-heart' },
      { id: 'neurology', name: 'Neurologia', icon: 'fas fa-brain' },
      { id: 'pneumology', name: 'Pneumologia', icon: 'fas fa-lungs' },
      { id: 'gastro', name: 'Gastroenterologia', icon: 'fas fa-stomach' },
      { id: 'endocrine', name: 'Endocrinologia', icon: 'fas fa-dna' },
      { id: 'nephro', name: 'Nefrologia', icon: 'fas fa-kidneys' }
    ])
    
    // Assistant Messages
    const assistantMessages = ref([
      {
        type: 'ai',
        icon: 'fas fa-robot',
        text: 'Olá! Percebi que você tem estudado bastante Cardiologia. Que tal variar com Neurologia hoje?'
      },
      {
        type: 'insight',
        icon: 'fas fa-lightbulb',
        text: 'Dica: Seu desempenho é 40% melhor nas primeiras 2 horas de estudo.'
      }
    ])
    
    // Methods
    const initCharts = () => {
      // Performance Radar Chart
      if (performanceRadar.value) {
        new Chart(performanceRadar.value, {
          type: 'radar',
          data: {
            labels: ['Cardiologia', 'Neurologia', 'Pneumologia', 'Endocrinologia', 'Gastro', 'Nefrologia'],
            datasets: [{
              label: 'Domínio Atual',
              data: [85, 45, 68, 72, 60, 55],
              borderColor: '#42b983',
              backgroundColor: 'rgba(66, 185, 131, 0.2)',
              pointBackgroundColor: '#42b983',
              pointBorderColor: '#fff',
              pointHoverBackgroundColor: '#fff',
              pointHoverBorderColor: '#42b983'
            }, {
              label: 'Meta',
              data: [90, 80, 80, 85, 75, 70],
              borderColor: '#ff9800',
              backgroundColor: 'rgba(255, 152, 0, 0.1)',
              pointBackgroundColor: '#ff9800',
              pointBorderColor: '#fff',
              pointHoverBackgroundColor: '#fff',
              pointHoverBorderColor: '#ff9800'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'bottom',
                labels: {
                  color: '#e2e8f0',
                  font: { size: 12 }
                }
              }
            },
            scales: {
              r: {
                beginAtZero: true,
                max: 100,
                ticks: {
                  color: '#94a3b8',
                  backdropColor: 'transparent'
                },
                grid: {
                  color: 'rgba(148, 163, 184, 0.2)'
                },
                pointLabels: {
                  color: '#e2e8f0',
                  font: { size: 12 }
                }
              }
            }
          }
        })
      }
      
      // Velocity Chart
      if (velocityChart.value) {
        new Chart(velocityChart.value, {
          type: 'line',
          data: {
            labels: ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'],
            datasets: [{
              label: 'Velocidade de Aprendizado',
              data: [1.8, 2.1, 2.0, 2.3, 2.5, 2.2, 2.3],
              borderColor: '#8b5cf6',
              backgroundColor: 'rgba(139, 92, 246, 0.1)',
              tension: 0.4,
              fill: true
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: { display: false }
            },
            scales: {
              x: {
                grid: { color: 'rgba(148, 163, 184, 0.1)' },
                ticks: { color: '#94a3b8' }
              },
              y: {
                beginAtZero: true,
                grid: { color: 'rgba(148, 163, 184, 0.1)' },
                ticks: { color: '#94a3b8' }
              }
            }
          }
        })
      }
    }
    
    const refreshAnalysis = async () => {
      isAnalyzing.value = true
      aiStatus.value = 'Processando novos dados...'
      
      // Simulate analysis
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      aiStatus.value = 'Análise completa!'
      isAnalyzing.value = false
      
      // Update data
      updateRecommendations()
    }
    
    const updateRecommendations = () => {
      // Update AI recommendations based on new analysis
      hasNewInsight.value = true
    }
    
    const applyRecommendation = (rec) => {
      console.log('Applying recommendation:', rec)
      // Implement recommendation application
    }
    
    const openTool = (toolId) => {
      activeToolModal.value = toolId
    }
    
    const closeToolModal = () => {
      activeToolModal.value = null
    }
    
    const toggleArea = (areaId) => {
      const index = plannerConfig.value.selectedAreas.indexOf(areaId)
      if (index > -1) {
        plannerConfig.value.selectedAreas.splice(index, 1)
      } else {
        plannerConfig.value.selectedAreas.push(areaId)
      }
    }
    
    const generateNeuralPlan = async () => {
      isGeneratingPlan.value = true
      
      // Simulate AI processing
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      generatedPlan.value = {
        duration: '12 semanas',
        topics: 248,
        successRate: 87,
        phases: [
          {
            name: 'Fundamentos',
            icon: 'fas fa-book',
            duration: '3 semanas',
            topics: 60,
            mainTopics: ['Anatomia', 'Fisiologia', 'Patologia Geral']
          },
          {
            name: 'Especialidades Core',
            icon: 'fas fa-heartbeat',
            duration: '5 semanas',
            topics: 120,
            mainTopics: ['Cardiologia', 'Neurologia', 'Pneumologia']
          },
          {
            name: 'Revisão Intensiva',
            icon: 'fas fa-fire',
            duration: '3 semanas',
            topics: 68,
            mainTopics: ['Casos Clínicos', 'Diagnóstico Diferencial']
          },
          {
            name: 'Simulados',
            icon: 'fas fa-trophy',
            duration: '1 semana',
            topics: 20,
            mainTopics: ['Provas Completas', 'Análise de Erros']
          }
        ]
      }
      
      isGeneratingPlan.value = false
    }
    
    const startPlan = () => {
      console.log('Starting plan...')
      closeToolModal()
    }
    
    const savePlan = () => {
      console.log('Saving plan...')
    }
    
    const exportPlan = () => {
      console.log('Exporting plan...')
    }
    
    const pauseSession = () => {
      console.log('Pausing session...')
    }
    
    const endSession = () => {
      activeSession.value = null
    }
    
    const showAllAchievements = () => {
      console.log('Showing all achievements...')
    }
    
    const toggleAssistant = () => {
      assistantExpanded.value = !assistantExpanded.value
      if (assistantExpanded.value) {
        hasNewInsight.value = false
      }
    }
    
    const askAssistant = () => {
      if (!assistantQuery.value.trim()) return
      
      assistantMessages.value.push({
        type: 'user',
        icon: 'fas fa-user',
        text: assistantQuery.value
      })
      
      // Simulate AI response
      setTimeout(() => {
        assistantMessages.value.push({
          type: 'ai',
          icon: 'fas fa-robot',
          text: 'Analisando sua pergunta... Vou preparar uma resposta personalizada.'
        })
      }, 1000)
      
      assistantQuery.value = ''
    }
    
    // Lifecycle
    onMounted(() => {
      initCharts()
      
      // Start AI status rotation
      const statusMessages = [
        'Analisando padrões...',
        'Otimizando aprendizado...',
        'Processando insights...',
        'Monitorando progresso...'
      ]
      
      let statusIndex = 0
      setInterval(() => {
        aiStatus.value = statusMessages[statusIndex]
        statusIndex = (statusIndex + 1) % statusMessages.length
      }, 3000)
      
      // Simulate session time
      setInterval(() => {
        // Update session time
        const [minutes, seconds] = sessionTime.value.split(':').map(Number)
        const totalSeconds = minutes * 60 + seconds + 1
        const newMinutes = Math.floor(totalSeconds / 60)
        const newSeconds = totalSeconds % 60
        sessionTime.value = `${newMinutes}:${newSeconds.toString().padStart(2, '0')}`
      }, 1000)
    })
    
    return {
      // Refs
      performanceRadar,
      velocityChart,
      
      // State
      aiStatus,
      isAnalyzing,
      activeToolModal,
      isGeneratingPlan,
      generatedPlan,
      assistantExpanded,
      hasNewInsight,
      assistantQuery,
      
      // Data
      userStats,
      learningVelocity,
      radarInsights,
      aiRecommendations,
      activeSession,
      sessionTime,
      recentAchievements,
      plannerConfig,
      medicalAreas,
      assistantMessages,
      
      // Methods
      refreshAnalysis,
      applyRecommendation,
      openTool,
      closeToolModal,
      toggleArea,
      generateNeuralPlan,
      startPlan,
      savePlan,
      exportPlan,
      pauseSession,
      endSession,
      showAllAchievements,
      toggleAssistant,
      askAssistant
    }
  }
}
</script>

<style scoped>
/* Base Container */
.study-assistant-ultra {
  position: relative;
  min-height: 100vh;
  background: var(--background-color);
  color: var(--text-color);
  overflow-x: hidden;
}

/* Animated Background */
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.15;
  animation: float-orb 20s ease-in-out infinite;
}

.orb-1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  top: -200px;
  left: -200px;
}

.orb-2 {
  width: 500px;
  height: 500px;
  background: linear-gradient(135deg, #10b981, #14b8a6);
  bottom: -150px;
  right: -150px;
  animation-delay: -7s;
}

.orb-3 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -14s;
}

@keyframes float-orb {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.95);
  }
}

.neural-network-bg {
  position: absolute;
  inset: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 10h10m10 0h10m10 0h10m10 0h10m10 0h10M10 30h10m10 0h10m10 0h10m10 0h10m10 0h10M10 50h10m10 0h10m10 0h10m10 0h10m10 0h10M10 70h10m10 0h10m10 0h10m10 0h10m10 0h10M10 90h10m10 0h10m10 0h10m10 0h10m10 0h10' stroke='%23ffffff' stroke-width='0.5' opacity='0.03'/%3E%3C/svg%3E");
  opacity: 0.5;
}

/* Header Ultra */
.assistant-header-ultra {
  position: relative;
  z-index: 10;
  padding: 2rem;
  background: rgba(var(--color-card-bg-rgb), 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.ai-badge-ultra {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.25rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 2rem;
  font-size: 0.875rem;
}

.ai-core {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.ai-core i {
  font-size: 1.2rem;
  color: #6366f1;
  z-index: 1;
}

.ai-pulse {
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  background: #6366f1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.ai-status {
  color: #6366f1;
  font-weight: 500;
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.page-title-ultra {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0;
}

.title-gradient {
  background: linear-gradient(135deg, #6366f1, #8b5cf6, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.version-badge {
  padding: 0.25rem 0.75rem;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  border-radius: 2rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.subtitle-ultra {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

.user-stats-mini {
  display: flex;
  gap: 1.5rem;
}

.stat-mini {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border: 1px solid var(--border-color);
  border-radius: 2rem;
  font-size: 0.875rem;
}

.stat-mini i {
  color: #f59e0b;
}

/* Main Dashboard */
.main-dashboard-ultra {
  position: relative;
  z-index: 5;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* AI Analysis Panel */
.ai-analysis-panel {
  margin-bottom: 3rem;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.panel-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
}

.refresh-btn {
  padding: 0.75rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: #6366f1;
  color: #6366f1;
}

.refresh-btn.spinning i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.analysis-card {
  background: rgba(var(--color-card-bg-rgb), 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.analysis-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-color: rgba(99, 102, 241, 0.3);
}

.analysis-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

/* Performance Radar */
.radar-card canvas {
  height: 250px !important;
}

.radar-insights {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
}

/* Velocity Chart */
.velocity-chart {
  height: 200px;
  margin-bottom: 1rem;
}

.velocity-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.velocity-stat {
  text-align: center;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #8b5cf6;
}

/* AI Recommendations */
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  background: rgba(var(--color-card-bg-rgb), 0.8);
  transform: translateX(4px);
}

.recommendation-item.high {
  border-left: 3px solid #ef4444;
}

.recommendation-item.medium {
  border-left: 3px solid #f59e0b;
}

.recommendation-item.low {
  border-left: 3px solid #10b981;
}

.rec-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 0.5rem;
  color: #6366f1;
}

.rec-content {
  flex: 1;
}

.rec-content h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.rec-content p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
}

.rec-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
}

.impact {
  color: #10b981;
  font-weight: 600;
}

.time {
  color: var(--text-secondary);
}

.rec-action {
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.recommendation-item:hover .rec-action {
  color: #6366f1;
  transform: translateX(4px);
}

/* Smart Tools Section */
.smart-tools-section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
}

.tools-grid-ultra {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.tool-card-ultra {
  position: relative;
  background: rgba(var(--color-card-bg-rgb), 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.tool-card-ultra::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.tool-card-ultra:hover {
  transform: translateY(-6px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(99, 102, 241, 0.3);
}

.tool-icon-wrapper {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.tool-icon-wrapper.neural {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.tool-icon-wrapper.memory {
  background: linear-gradient(135deg, #10b981, #14b8a6);
  color: white;
}

.tool-icon-wrapper.concept {
  background: linear-gradient(135deg, #f59e0b, #f97316);
  color: white;
}

.tool-icon-wrapper.questions {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.tool-icon-wrapper.focus {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.tool-icon-wrapper.resources {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.tool-particles {
  position: absolute;
  inset: -10px;
  border-radius: 1rem;
  overflow: hidden;
}

.tool-particles::before,
.tool-particles::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  animation: particle-float 4s linear infinite;
}

.tool-particles::after {
  animation-delay: -2s;
}

@keyframes particle-float {
  0% {
    transform: translate(0, 60px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translate(0, -60px);
    opacity: 0;
  }
}

.tool-card-ultra h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.tool-card-ultra p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0 0 1rem 0;
}

.tool-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.tool-stats span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Active Session */
.active-session-section {
  background: rgba(var(--color-card-bg-rgb), 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 3rem;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.session-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.session-controls {
  display: flex;
  gap: 0.75rem;
}

.control-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
  color: #ef4444;
}

.session-progress {
  margin-bottom: 2rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.progress-bar-ultra {
  height: 8px;
  background: rgba(var(--color-card-bg-rgb), 0.3);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill-ultra {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.session-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border-radius: 0.75rem;
}

.metric-card i {
  font-size: 1.5rem;
  color: #6366f1;
}

.metric-info {
  display: flex;
  flex-direction: column;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Achievements Section */
.achievements-section {
  margin-bottom: 3rem;
}

.view-all-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: #6366f1;
  color: #6366f1;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.achievement-card {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(var(--color-card-bg-rgb), 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.achievement-card.unlocked {
  opacity: 1;
  border-color: #10b981;
}

.achievement-card.legendary {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.1));
  border-color: #f59e0b;
}

.achievement-icon {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border-radius: 50%;
  font-size: 1.5rem;
  color: var(--text-secondary);
}

.achievement-card.unlocked .achievement-icon {
  background: linear-gradient(135deg, #10b981, #14b8a6);
  color: white;
}

.achievement-glow {
  position: absolute;
  inset: -10px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.4), transparent);
  opacity: 0;
  animation: achievement-glow 2s ease-in-out infinite;
}

.achievement-card.unlocked .achievement-glow {
  opacity: 1;
}

@keyframes achievement-glow {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.achievement-info h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.achievement-info p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

.achievement-progress {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.progress-mini {
  flex: 1;
  height: 4px;
  background: rgba(var(--color-card-bg-rgb), 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-mini-fill {
  height: 100%;
  background: #6366f1;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.achievement-progress span {
  font-size: 0.75rem;
  font-weight: 600;
  color: #6366f1;
}

/* Tool Modal */
.tool-modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.tool-modal-content {
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  background: var(--card-bg);
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
  color: #ef4444;
}

.modal-body {
  padding: 2rem;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

/* Planner Config */
.planner-config h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.config-item.full-width {
  grid-column: 1 / -1;
}

.config-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.config-select,
.time-input input,
.date-picker input {
  padding: 0.75rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-color);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.config-select:focus,
.time-input input:focus,
.date-picker input:focus {
  outline: none;
  border-color: #6366f1;
  background: rgba(var(--color-card-bg-rgb), 0.8);
}

.time-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-input input {
  width: 80px;
}

.areas-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.area-chip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border: 1px solid var(--border-color);
  border-radius: 2rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.area-chip:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: #6366f1;
}

.area-chip.selected {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
  color: #6366f1;
}

.ai-options {
  margin-bottom: 2rem;
}

.ai-options h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.ai-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: rgba(var(--color-card-bg-rgb), 0.3);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ai-toggle:hover {
  background: rgba(var(--color-card-bg-rgb), 0.5);
}

.ai-toggle input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.ai-toggle i {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.generate-plan-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 0.75rem;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.generate-plan-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

.generate-plan-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Generated Plan Display */
.generated-plan-display {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.generated-plan-display h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

.plan-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.overview-stat {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border-radius: 0.75rem;
}

.overview-stat i {
  font-size: 1.5rem;
  color: #6366f1;
}

.overview-stat strong {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
}

.overview-stat span {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.plan-timeline h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.timeline-container {
  position: relative;
  padding-left: 2rem;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 1.5rem;
  bottom: 1.5rem;
  width: 2px;
  background: var(--border-color);
}

.timeline-phase {
  position: relative;
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.phase-marker {
  position: absolute;
  left: -1.25rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--card-bg);
  border: 2px solid #6366f1;
  border-radius: 50%;
  color: #6366f1;
  font-size: 0.875rem;
}

.phase-content {
  flex: 1;
  padding: 1rem;
  background: rgba(var(--color-card-bg-rgb), 0.3);
  border-radius: 0.75rem;
}

.phase-content h5 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.phase-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0 0 0.75rem 0;
}

.phase-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.topic-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 1rem;
  font-size: 0.75rem;
  color: #6366f1;
}

.plan-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

.action-btn.secondary {
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.action-btn.secondary:hover {
  background: rgba(var(--color-card-bg-rgb), 0.8);
  border-color: #6366f1;
}

/* Floating AI Assistant */
.ai-assistant-floating {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 999;
}

.assistant-toggle {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.assistant-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 15px 40px rgba(99, 102, 241, 0.4);
}

.assistant-notification {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ef4444;
  border-radius: 50%;
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  animation: notification-pulse 2s infinite;
}

@keyframes notification-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

.assistant-panel {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 380px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.assistant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border-bottom: 1px solid var(--border-color);
}

.assistant-header h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.minimize-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.minimize-btn:hover {
  background: rgba(var(--color-card-bg-rgb), 0.5);
}

.assistant-messages {
  height: 300px;
  overflow-y: auto;
  padding: 1.5rem;
}

.message {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(var(--color-card-bg-rgb), 0.3);
  border-radius: 0.75rem;
}

.message.ai {
  background: rgba(99, 102, 241, 0.1);
}

.message.user {
  background: rgba(var(--color-card-bg-rgb), 0.5);
  flex-direction: row-reverse;
}

.message i {
  font-size: 1rem;
  color: #6366f1;
}

.message.user i {
  color: var(--text-secondary);
}

.message p {
  flex: 1;
  font-size: 0.875rem;
  margin: 0;
}

.assistant-input {
  display: flex;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border-top: 1px solid var(--border-color);
}

.assistant-input input {
  flex: 1;
  padding: 0.5rem 1rem;
  background: rgba(var(--color-card-bg-rgb), 0.5);
  border: 1px solid var(--border-color);
  border-radius: 2rem;
  color: var(--text-color);
  font-size: 0.875rem;
}

.assistant-input input:focus {
  outline: none;
  border-color: #6366f1;
}

.assistant-input button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.assistant-input button:hover {
  transform: scale(1.1);
}

/* Animations */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active .tool-modal-content,
.modal-fade-leave-active .tool-modal-content {
  transition: transform 0.3s ease;
}

.modal-fade-enter-from .tool-modal-content {
  transform: scale(0.9);
}

.modal-fade-leave-to .tool-modal-content {
  transform: scale(0.9);
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .page-title-ultra {
    font-size: 2rem;
  }
  
  .analysis-grid,
  .tools-grid-ultra,
  .achievements-grid {
    grid-template-columns: 1fr;
  }
  
  .session-metrics {
    grid-template-columns: 1fr;
  }
  
  .assistant-panel {
    width: calc(100vw - 4rem);
    max-width: 380px;
  }
}
</style>