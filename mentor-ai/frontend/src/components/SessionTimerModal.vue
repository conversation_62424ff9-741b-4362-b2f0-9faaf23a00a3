<template>
  <teleport to="body">
    <transition name="modal-fade">
      <div v-if="show" class="timer-modal-overlay" @click.self="$emit('close')">
        <div class="timer-modal" :class="{ 'fullscreen': isFullscreen }">
          <!-- Header -->
          <div class="timer-header">
            <div class="header-left">
              <div class="plan-info">
                <h3>{{ session?.subject || 'Sessão de Estudo' }}</h3>
                <p>{{ session?.topic || 'Estudando...' }}</p>
              </div>
            </div>
            
            <div class="header-actions">
              <button @click="toggleFullscreen" class="action-btn" title="Tela cheia">
                <i :class="isFullscreen ? 'fas fa-compress' : 'fas fa-expand'"></i>
              </button>
              <button @click="$emit('close')" class="action-btn close-btn" title="Fechar">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <!-- Timer Display -->
          <div class="timer-display">
            <div class="time-circle">
              <svg viewBox="0 0 200 200">
                <circle
                  cx="100"
                  cy="100"
                  r="90"
                  fill="none"
                  stroke="rgba(255,255,255,0.1)"
                  stroke-width="8"
                />
                <circle
                  cx="100"
                  cy="100"
                  r="90"
                  fill="none"
                  stroke="url(#gradient)"
                  stroke-width="8"
                  :stroke-dasharray="circumference"
                  :stroke-dashoffset="strokeDashoffset"
                  transform="rotate(-90 100 100)"
                />
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stop-color="#667eea" />
                    <stop offset="100%" stop-color="#764ba2" />
                  </linearGradient>
                </defs>
              </svg>
              
              <div class="time-text">
                <div class="time-main">{{ formatTime(timeRemaining) }}</div>
                <div class="time-label">{{ isPaused ? 'Pausado' : 'Restante' }}</div>
              </div>
            </div>
            
            <div class="session-stats">
              <div class="stat">
                <span class="stat-label">Tempo Total</span>
                <span class="stat-value">{{ session?.duration || 0 }} min</span>
              </div>
              <div class="stat">
                <span class="stat-label">Decorrido</span>
                <span class="stat-value">{{ formatTime(timeElapsed) }}</span>
              </div>
              <div class="stat">
                <span class="stat-label">Progresso</span>
                <span class="stat-value">{{ progressPercent }}%</span>
              </div>
            </div>
          </div>

          <!-- Controls -->
          <div class="timer-controls">
            <button 
              v-if="!isRunning" 
              @click="startTimer" 
              class="control-btn primary"
            >
              <i class="fas fa-play"></i>
              Iniciar
            </button>
            
            <button 
              v-else-if="!isPaused" 
              @click="pauseTimer" 
              class="control-btn pause"
            >
              <i class="fas fa-pause"></i>
              Pausar
            </button>
            
            <button 
              v-else 
              @click="resumeTimer" 
              class="control-btn resume"
            >
              <i class="fas fa-play"></i>
              Continuar
            </button>
            
            <button 
              @click="completeSession" 
              class="control-btn complete"
              :disabled="!isRunning"
            >
              <i class="fas fa-check"></i>
              Concluir
            </button>
          </div>

          <!-- Plan Progress -->
          <div v-if="planData" class="plan-progress-section">
            <h4>Progresso do Plano: {{ planData.name }}</h4>
            <div class="plan-progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: planProgressPercent + '%' }"
              ></div>
              <span class="progress-text">{{ planProgressPercent }}% concluído</span>
            </div>
            <div class="plan-stats">
              <span>{{ hoursCompleted }}h de {{ planData.estimatedTime }}h completadas</span>
              <span>{{ remainingHours }}h restantes</span>
            </div>
          </div>

          <!-- Motivational Messages -->
          <div class="motivation-section">
            <p class="motivation-text">{{ motivationalMessage }}</p>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'SessionTimerModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    session: {
      type: Object,
      required: true
    },
    planId: {
      type: Number,
      default: null
    }
  },
  emits: ['close', 'session-complete', 'update-progress'],
  setup(props, { emit }) {
    const store = useStore()
    
    // Timer state
    const isRunning = ref(false)
    const isPaused = ref(false)
    const timeElapsed = ref(0)
    const isFullscreen = ref(false)
    let timerInterval = null
    
    // Plan data
    const planData = ref(null)
    const hoursCompleted = ref(0)
    
    // Constants
    const circumference = 2 * Math.PI * 90
    
    // Computed
    const timeRemaining = computed(() => {
      const totalSeconds = (props.session?.duration || 0) * 60
      return Math.max(0, totalSeconds - timeElapsed.value)
    })
    
    const progressPercent = computed(() => {
      if (!props.session?.duration) return 0
      return Math.min(100, Math.round((timeElapsed.value / (props.session.duration * 60)) * 100))
    })
    
    const strokeDashoffset = computed(() => {
      return circumference - (progressPercent.value / 100) * circumference
    })
    
    const planProgressPercent = computed(() => {
      if (!planData.value?.estimatedTime) return 0
      return Math.min(100, Math.round((hoursCompleted.value / planData.value.estimatedTime) * 100))
    })
    
    const remainingHours = computed(() => {
      if (!planData.value?.estimatedTime) return 0
      return Math.max(0, planData.value.estimatedTime - hoursCompleted.value)
    })
    
    const motivationalMessage = computed(() => {
      const messages = [
        "Continue assim! Você está indo muito bem! 💪",
        "Foco total! Cada minuto conta! 🎯",
        "Persistência é a chave do sucesso! 🔑",
        "Você está construindo seu futuro! 🌟",
        "Conhecimento é poder! 📚",
        "Mantenha o ritmo! Você consegue! 🚀"
      ]
      return messages[Math.floor(Math.random() * messages.length)]
    })
    
    // Methods
    const formatTime = (seconds) => {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    
    const startTimer = () => {
      isRunning.value = true
      isPaused.value = false
      
      timerInterval = setInterval(() => {
        timeElapsed.value++
        
        // Auto complete when time is up
        if (timeRemaining.value === 0) {
          completeSession()
        }
        
        // Update progress every minute
        if (timeElapsed.value % 60 === 0) {
          updatePlanProgress()
        }
      }, 1000)
    }
    
    const pauseTimer = () => {
      isPaused.value = true
      clearInterval(timerInterval)
    }
    
    const resumeTimer = () => {
      isPaused.value = false
      startTimer()
    }
    
    const completeSession = () => {
      clearInterval(timerInterval)
      
      const minutesStudied = Math.floor(timeElapsed.value / 60)
      const hoursStudied = minutesStudied / 60
      
      // Update plan progress
      if (planData.value) {
        updatePlanProgress(true)
      }
      
      // Emit completion event
      emit('session-complete', {
        sessionId: props.session.id,
        minutesStudied,
        hoursStudied,
        completed: true
      })
      
      // Show success message
      alert(`Parabéns! Você estudou por ${minutesStudied} minutos! 🎉`)
      
      emit('close')
    }
    
    const updatePlanProgress = (isComplete = false) => {
      if (!planData.value || !props.planId) return
      
      const minutesStudied = Math.floor(timeElapsed.value / 60)
      const hoursStudied = minutesStudied / 60
      
      emit('update-progress', {
        planId: props.planId,
        hoursStudied,
        sessionComplete: isComplete
      })
    }
    
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value
    }
    
    const loadPlanData = () => {
      if (props.planId) {
        // Load plan data from localStorage or store
        const plans = JSON.parse(localStorage.getItem('activePlans') || '[]')
        planData.value = plans.find(p => p.id === props.planId)
        
        // Calculate hours completed (mock for now)
        hoursCompleted.value = planData.value?.hoursCompleted || 0
      }
    }
    
    // Lifecycle
    onMounted(() => {
      loadPlanData()
    })
    
    onUnmounted(() => {
      if (timerInterval) {
        clearInterval(timerInterval)
      }
    })
    
    // Watch for session changes
    watch(() => props.session, () => {
      // Reset timer when session changes
      timeElapsed.value = 0
      isRunning.value = false
      isPaused.value = false
      clearInterval(timerInterval)
    })
    
    return {
      // State
      isRunning,
      isPaused,
      timeElapsed,
      timeRemaining,
      isFullscreen,
      planData,
      hoursCompleted,
      
      // Computed
      progressPercent,
      strokeDashoffset,
      circumference,
      planProgressPercent,
      remainingHours,
      motivationalMessage,
      
      // Methods
      formatTime,
      startTimer,
      pauseTimer,
      resumeTimer,
      completeSession,
      toggleFullscreen
    }
  }
}
</script>

<style scoped>
/* Modal Overlay */
.timer-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 2rem;
}

/* Timer Modal */
.timer-modal {
  background: linear-gradient(135deg, #1a1a2e 0%, #0f0e17 100%);
  border-radius: 32px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 100px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.timer-modal.fullscreen {
  max-width: 100%;
  max-height: 100%;
  height: 100%;
  border-radius: 0;
}

/* Header */
.timer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-info h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0 0 0.5rem;
}

.plan-info p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* Timer Display */
.timer-display {
  padding: 3rem 2rem;
  text-align: center;
}

.time-circle {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto 2rem;
}

.time-circle svg {
  transform: rotate(-90deg);
}

.time-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.time-main {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  font-variant-numeric: tabular-nums;
}

.time-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Session Stats */
.session-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 2rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

/* Controls */
.timer-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 0 2rem 2rem;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 16px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.control-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.control-btn.pause {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.control-btn.resume {
  background: rgba(74, 222, 128, 0.2);
  color: #4ade80;
  border: 1px solid rgba(74, 222, 128, 0.3);
}

.control-btn.complete {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Plan Progress */
.plan-progress-section {
  padding: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-progress-section h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0 0 1rem;
}

.plan-progress-bar {
  position: relative;
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.plan-stats {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Motivation */
.motivation-section {
  padding: 1.5rem 2rem;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.motivation-text {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
  margin: 0;
}

/* Transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active .timer-modal {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>