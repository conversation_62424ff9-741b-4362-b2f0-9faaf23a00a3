<template>
  <div class="skeleton-loader" :class="[type, { animated: animated }]" :style="customStyle">
    <div v-if="type === 'card'" class="skeleton-card">
      <div class="skeleton-header">
        <div class="skeleton-avatar"></div>
        <div class="skeleton-header-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-subtitle"></div>
        </div>
      </div>
      <div class="skeleton-body">
        <div class="skeleton-line"></div>
        <div class="skeleton-line"></div>
        <div class="skeleton-line" style="width: 80%"></div>
      </div>
      <div v-if="showActions" class="skeleton-footer">
        <div class="skeleton-button"></div>
        <div class="skeleton-button"></div>
      </div>
    </div>
    
    <div v-else-if="type === 'list'" class="skeleton-list">
      <div v-for="i in count" :key="i" class="skeleton-list-item">
        <div class="skeleton-avatar small"></div>
        <div class="skeleton-list-content">
          <div class="skeleton-line" style="width: 60%"></div>
          <div class="skeleton-line" style="width: 40%"></div>
        </div>
      </div>
    </div>
    
    <div v-else-if="type === 'table'" class="skeleton-table">
      <div class="skeleton-table-header">
        <div v-for="i in columns" :key="`h-${i}`" class="skeleton-table-cell header"></div>
      </div>
      <div v-for="i in rows" :key="`r-${i}`" class="skeleton-table-row">
        <div v-for="j in columns" :key="`c-${j}`" class="skeleton-table-cell"></div>
      </div>
    </div>
    
    <div v-else-if="type === 'article'" class="skeleton-article">
      <div class="skeleton-title large"></div>
      <div class="skeleton-metadata">
        <div class="skeleton-avatar small"></div>
        <div class="skeleton-line" style="width: 150px"></div>
        <div class="skeleton-line" style="width: 100px"></div>
      </div>
      <div class="skeleton-paragraph">
        <div v-for="i in 5" :key="`p1-${i}`" class="skeleton-line"></div>
      </div>
      <div class="skeleton-image"></div>
      <div class="skeleton-paragraph">
        <div v-for="i in 3" :key="`p2-${i}`" class="skeleton-line"></div>
      </div>
    </div>
    
    <div v-else-if="type === 'stat'" class="skeleton-stat">
      <div class="skeleton-stat-icon"></div>
      <div class="skeleton-stat-content">
        <div class="skeleton-stat-value"></div>
        <div class="skeleton-stat-label"></div>
      </div>
    </div>
    
    <div v-else class="skeleton-custom">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SkeletonLoader',
  props: {
    type: {
      type: String,
      default: 'card',
      validator: value => ['card', 'list', 'table', 'article', 'stat', 'custom'].includes(value)
    },
    count: {
      type: Number,
      default: 3
    },
    columns: {
      type: Number,
      default: 4
    },
    rows: {
      type: Number,
      default: 5
    },
    animated: {
      type: Boolean,
      default: true
    },
    showActions: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: 'auto'
    },
    width: {
      type: String,
      default: '100%'
    }
  },
  computed: {
    customStyle() {
      return {
        height: this.height,
        width: this.width
      }
    }
  }
}
</script>

<style scoped>
/* Base Skeleton Styles */
.skeleton-loader {
  position: relative;
}

.skeleton-loader * {
  background: var(--skeleton-base, #e0e0e0);
  border-radius: 4px;
}

/* Shimmer Animation */
.skeleton-loader.animated *::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transform: translateX(-100%);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Common Elements */
.skeleton-line {
  height: 12px;
  margin-bottom: 8px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.skeleton-title {
  height: 20px;
  width: 50%;
  margin-bottom: 12px;
  position: relative;
  overflow: hidden;
}

.skeleton-title.large {
  height: 32px;
  width: 70%;
}

.skeleton-subtitle {
  height: 14px;
  width: 30%;
  position: relative;
  overflow: hidden;
}

.skeleton-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.skeleton-avatar.small {
  width: 32px;
  height: 32px;
}

.skeleton-button {
  height: 36px;
  width: 100px;
  border-radius: 18px;
  position: relative;
  overflow: hidden;
}

.skeleton-image {
  height: 200px;
  width: 100%;
  margin: 20px 0;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

/* Card Skeleton */
.skeleton-card {
  padding: 20px;
  background: var(--card-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.skeleton-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.skeleton-header-content {
  flex: 1;
}

.skeleton-body {
  margin-bottom: 20px;
}

.skeleton-footer {
  display: flex;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

/* List Skeleton */
.skeleton-list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.skeleton-list-item:last-child {
  border-bottom: none;
}

.skeleton-list-content {
  flex: 1;
}

/* Table Skeleton */
.skeleton-table {
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.skeleton-table-header {
  display: flex;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.skeleton-table-row {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.skeleton-table-row:last-child {
  border-bottom: none;
}

.skeleton-table-cell {
  flex: 1;
  height: 16px;
  margin: 12px;
  position: relative;
  overflow: hidden;
}

.skeleton-table-cell.header {
  height: 20px;
  margin: 16px 12px;
}

/* Article Skeleton */
.skeleton-article {
  max-width: 800px;
  margin: 0 auto;
}

.skeleton-metadata {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 20px 0;
}

.skeleton-paragraph {
  margin: 20px 0;
}

/* Stat Skeleton */
.skeleton-stat {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--card-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.skeleton-stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.skeleton-stat-content {
  flex: 1;
}

.skeleton-stat-value {
  height: 32px;
  width: 120px;
  margin-bottom: 8px;
  position: relative;
  overflow: hidden;
}

.skeleton-stat-label {
  height: 16px;
  width: 80px;
  position: relative;
  overflow: hidden;
}

/* Dark Mode */
:root[data-theme="dark"] .skeleton-loader * {
  background: var(--skeleton-base-dark, #2a2a2a);
}

:root[data-theme="dark"] .skeleton-loader.animated *::after {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

/* Responsive */
@media (max-width: 768px) {
  .skeleton-card {
    padding: 16px;
  }
  
  .skeleton-avatar {
    width: 40px;
    height: 40px;
  }
  
  .skeleton-image {
    height: 150px;
  }
}
</style>