<template>
  <div class="stat-card" :class="[variant, { 'animated': animated }]">
    <div class="stat-background">
      <div class="stat-pattern"></div>
    </div>
    
    <div class="stat-content">
      <div class="stat-header">
        <div class="stat-icon-wrapper">
          <font-awesome-icon :icon="icon" />
        </div>
        <div v-if="trend" class="stat-trend" :class="trendType">
          <font-awesome-icon :icon="trendType === 'up' ? 'fa-arrow-up' : 'fa-arrow-down'" />
          <span>{{ trend }}</span>
        </div>
      </div>
      
      <div class="stat-body">
        <div class="stat-value-wrapper">
          <transition name="number" mode="out-in">
            <span :key="value" class="stat-value">{{ animatedValue }}</span>
          </transition>
          <span v-if="suffix" class="stat-suffix">{{ suffix }}</span>
        </div>
        <h4 class="stat-title">{{ title }}</h4>
        <p v-if="description" class="stat-description">{{ description }}</p>
      </div>
      
      <div v-if="progress !== null" class="stat-progress">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: progress + '%' }"
            :class="progressColor"
          >
            <div class="progress-glow"></div>
          </div>
        </div>
        <span class="progress-text">{{ progress }}% completo</span>
      </div>
    </div>
    
    <div class="stat-decoration">
      <div class="decoration-circle"></div>
      <div class="decoration-dots"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      required: true
    },
    suffix: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      required: true
    },
    trend: {
      type: String,
      default: null
    },
    trendType: {
      type: String,
      default: 'up',
      validator: value => ['up', 'down'].includes(value)
    },
    progress: {
      type: Number,
      default: null,
      validator: value => value === null || (value >= 0 && value <= 100)
    },
    progressColor: {
      type: String,
      default: 'primary'
    },
    variant: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
    },
    animated: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      animatedValue: 0
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (this.animated && typeof newVal === 'number') {
          this.animateValue(newVal)
        } else {
          this.animatedValue = newVal
        }
      }
    }
  },
  methods: {
    animateValue(endValue) {
      const startValue = this.animatedValue || 0
      const duration = 1000
      const startTime = Date.now()
      
      const animate = () => {
        const currentTime = Date.now()
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / duration, 1)
        
        this.animatedValue = Math.round(startValue + (endValue - startValue) * progress)
        
        if (progress < 1) {
          requestAnimationFrame(animate)
        }
      }
      
      animate()
    }
  }
}
</script>

<style scoped>
.stat-card {
  position: relative;
  background: var(--card-bg);
  border-radius: 1.5rem;
  padding: 2rem;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

/* Variants */
.stat-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.stat-card.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.stat-card.danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

/* Background Pattern */
.stat-background {
  position: absolute;
  top: 0;
  right: 0;
  width: 60%;
  height: 100%;
  opacity: 0.1;
  overflow: hidden;
}

.stat-pattern {
  width: 200%;
  height: 200%;
  background-image: 
    radial-gradient(circle at 20% 50%, currentColor 1px, transparent 1px),
    radial-gradient(circle at 80% 50%, currentColor 1px, transparent 1px);
  background-size: 50px 50px;
  transform: rotate(45deg);
}

/* Content */
.stat-content {
  position: relative;
  z-index: 1;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.stat-icon-wrapper {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
  font-size: 1.5rem;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-card.default .stat-icon-wrapper {
  background: var(--bg-secondary);
  color: var(--accent-color);
}

/* Trend */
.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.85rem;
  font-weight: 600;
}

.stat-trend.up {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.stat-trend.down {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.stat-card.primary .stat-trend,
.stat-card.success .stat-trend,
.stat-card.warning .stat-trend,
.stat-card.danger .stat-trend {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Value */
.stat-body {
  margin-bottom: 1rem;
}

.stat-value-wrapper {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  color: var(--text-primary);
}

.stat-suffix {
  font-size: 1.5rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.stat-card.primary .stat-value,
.stat-card.success .stat-value,
.stat-card.warning .stat-value,
.stat-card.danger .stat-value {
  color: white;
}

.stat-card.primary .stat-suffix,
.stat-card.success .stat-suffix,
.stat-card.warning .stat-suffix,
.stat-card.danger .stat-suffix {
  color: rgba(255, 255, 255, 0.8);
}

.stat-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.stat-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.stat-card.primary .stat-title,
.stat-card.success .stat-title,
.stat-card.warning .stat-title,
.stat-card.danger .stat-title {
  color: white;
}

.stat-card.primary .stat-description,
.stat-card.success .stat-description,
.stat-card.warning .stat-description,
.stat-card.danger .stat-description {
  color: rgba(255, 255, 255, 0.8);
}

/* Progress */
.stat-progress {
  margin-top: 1.5rem;
}

.progress-bar {
  height: 8px;
  background: var(--bg-secondary);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.stat-card.primary .progress-bar,
.stat-card.success .progress-bar,
.stat-card.warning .progress-bar,
.stat-card.danger .progress-bar {
  background: rgba(255, 255, 255, 0.2);
}

.progress-fill {
  height: 100%;
  background: var(--accent-color);
  transition: width 0.6s ease;
  position: relative;
}

.progress-fill.success {
  background: var(--success-color);
}

.progress-fill.warning {
  background: var(--warning-color);
}

.progress-fill.danger {
  background: var(--danger-color);
}

.progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 20px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
  animation: progressGlow 2s infinite;
}

@keyframes progressGlow {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.progress-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

/* Decoration */
.stat-decoration {
  position: absolute;
  bottom: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  opacity: 0.1;
}

.decoration-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid currentColor;
}

.decoration-dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 60%;
  background-image: radial-gradient(circle, currentColor 2px, transparent 2px);
  background-size: 15px 15px;
}

/* Hover Effects */
.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.stat-card:hover .stat-icon-wrapper {
  transform: scale(1.1);
}

/* Animations */
.number-enter-active,
.number-leave-active {
  transition: all 0.3s ease;
}

.number-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.number-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* Responsive */
@media (max-width: 768px) {
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-value {
    font-size: 2rem;
  }
  
  .stat-suffix {
    font-size: 1.25rem;
  }
}
</style>