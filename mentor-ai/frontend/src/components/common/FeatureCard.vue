<template>
  <div 
    class="feature-card" 
    :class="[
      variant, 
      { 'hover-enabled': hoverable, 'clickable': clickable }
    ]"
    @click="handleClick"
  >
    <!-- Badge opcional -->
    <div v-if="badge" class="card-badge" :class="badgeType">
      {{ badge }}
    </div>
    
    <!-- Header com ícone -->
    <div class="card-header">
      <div class="icon-wrapper" :class="iconClass">
        <font-awesome-icon :icon="icon" />
      </div>
      <div v-if="stats" class="card-stats">
        <span class="stat-value">{{ stats.value }}</span>
        <span class="stat-label">{{ stats.label }}</span>
      </div>
    </div>
    
    <!-- Conteúdo -->
    <div class="card-content">
      <h3 class="card-title">{{ title }}</h3>
      <p class="card-description">{{ description }}</p>
      
      <!-- Features list opcional -->
      <div v-if="features && features.length" class="features-list">
        <span v-for="(feature, index) in features" :key="index" class="feature-item">
          <font-awesome-icon :icon="feature.icon" />
          {{ feature.text }}
        </span>
      </div>
    </div>
    
    <!-- Footer com ações -->
    <div v-if="showAction" class="card-footer">
      <slot name="footer">
        <span class="action-text">{{ actionText }}</span>
        <font-awesome-icon icon="fa-arrow-right" class="action-icon" />
      </slot>
    </div>
    
    <!-- Efeitos visuais -->
    <div class="card-effects">
      <div class="glow-effect"></div>
      <div class="shine-effect"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FeatureCard',
  props: {
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      required: true
    },
    iconClass: {
      type: String,
      default: ''
    },
    variant: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'secondary', 'gradient', 'glass'].includes(value)
    },
    badge: {
      type: String,
      default: ''
    },
    badgeType: {
      type: String,
      default: 'primary'
    },
    features: {
      type: Array,
      default: () => []
    },
    stats: {
      type: Object,
      default: null
    },
    hoverable: {
      type: Boolean,
      default: true
    },
    clickable: {
      type: Boolean,
      default: false
    },
    showAction: {
      type: Boolean,
      default: false
    },
    actionText: {
      type: String,
      default: 'Explorar'
    }
  },
  methods: {
    handleClick() {
      if (this.clickable) {
        this.$emit('click')
      }
    }
  }
}
</script>

<style scoped>
.feature-card {
  position: relative;
  background: var(--card-bg);
  border-radius: 1.5rem;
  padding: 2rem;
  overflow: hidden;
  transition: all 0.4s ease;
  border: 1px solid var(--border-color);
}

/* Variantes */
.feature-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.feature-card.secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.feature-card.gradient {
  background: linear-gradient(135deg, var(--card-bg) 0%, rgba(102, 126, 234, 0.1) 100%);
}

.feature-card.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Badge */
.card-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  background: var(--accent-color);
  color: white;
  z-index: 1;
}

.card-badge.success {
  background: var(--success-color);
}

.card-badge.warning {
  background: var(--warning-color);
}

/* Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.icon-wrapper {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
  font-size: 1.75rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
  color: var(--accent-color);
  transition: all 0.3s ease;
}

.feature-card.primary .icon-wrapper,
.feature-card.secondary .icon-wrapper {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Stats */
.card-stats {
  text-align: right;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

/* Content */
.card-content {
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.feature-card.primary .card-title,
.feature-card.secondary .card-title {
  color: white;
}

.card-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.feature-card.primary .card-description,
.feature-card.secondary .card-description {
  color: rgba(255, 255, 255, 0.9);
}

/* Features List */
.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 1rem;
}

.feature-item {
  background: var(--bg-secondary);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.feature-card.primary .feature-item,
.feature-card.secondary .feature-item {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Footer */
.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
  color: var(--accent-color);
  font-weight: 600;
}

.feature-card.primary .card-footer,
.feature-card.secondary .card-footer {
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.action-icon {
  transition: transform 0.3s ease;
}

/* Hover Effects */
.feature-card.hover-enabled:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.feature-card.hover-enabled:hover .icon-wrapper {
  transform: scale(1.1);
}

.feature-card.hover-enabled:hover .action-icon {
  transform: translateX(5px);
}

.feature-card.clickable {
  cursor: pointer;
}

/* Effects */
.card-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover .card-effects {
  opacity: 1;
}

.glow-effect {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
}

.shine-effect {
  position: absolute;
  top: -50%;
  left: -100%;
  width: 50%;
  height: 200%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transform: skewX(-45deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 200%;
  }
}

/* Dark Mode */
:root[data-theme="dark"] .feature-card.glass {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .feature-card {
    padding: 1.5rem;
  }
  
  .icon-wrapper {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}
</style>