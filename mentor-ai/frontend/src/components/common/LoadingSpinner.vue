<template>
  <div class="loading-spinner-wrapper" :class="[size, { fullscreen: fullscreen }]">
    <div class="loading-spinner" :class="[variant, { inline: inline }]">
      <div v-if="variant === 'default'" class="spinner-default">
        <div class="spinner-circle"></div>
      </div>
      
      <div v-else-if="variant === 'dots'" class="spinner-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
      
      <div v-else-if="variant === 'pulse'" class="spinner-pulse">
        <div class="pulse-ring"></div>
        <div class="pulse-core"></div>
      </div>
      
      <div v-else-if="variant === 'bars'" class="spinner-bars">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
      
      <div v-else-if="variant === 'cube'" class="spinner-cube">
        <div class="cube-face cube-face-front"></div>
        <div class="cube-face cube-face-back"></div>
        <div class="cube-face cube-face-left"></div>
        <div class="cube-face cube-face-right"></div>
        <div class="cube-face cube-face-top"></div>
        <div class="cube-face cube-face-bottom"></div>
      </div>
      
      <div v-if="text" class="spinner-text">{{ text }}</div>
    </div>
    
    <div v-if="overlay" class="loading-overlay" @click="$emit('overlay-click')"></div>
  </div>
</template>

<script>
export default {
  name: 'LoadingSpinner',
  props: {
    variant: {
      type: String,
      default: 'default',
      validator: value => ['default', 'dots', 'pulse', 'bars', 'cube'].includes(value)
    },
    size: {
      type: String,
      default: 'md',
      validator: value => ['sm', 'md', 'lg', 'xl'].includes(value)
    },
    text: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    overlay: {
      type: Boolean,
      default: false
    },
    inline: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
/* Wrapper */
.loading-spinner-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner-wrapper.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: var(--bg-primary);
}

/* Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Base Spinner */
.loading-spinner {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-spinner.inline {
  display: inline-flex;
  flex-direction: row;
  vertical-align: middle;
}

/* Size Variants */
.sm .spinner-default .spinner-circle,
.sm .spinner-pulse {
  width: 20px;
  height: 20px;
}

.md .spinner-default .spinner-circle,
.md .spinner-pulse {
  width: 40px;
  height: 40px;
}

.lg .spinner-default .spinner-circle,
.lg .spinner-pulse {
  width: 60px;
  height: 60px;
}

.xl .spinner-default .spinner-circle,
.xl .spinner-pulse {
  width: 80px;
  height: 80px;
}

/* Default Spinner */
.spinner-default .spinner-circle {
  border: 3px solid var(--border-color);
  border-top-color: var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Dots Spinner */
.spinner-dots {
  display: flex;
  gap: 0.5rem;
}

.spinner-dots span {
  width: 12px;
  height: 12px;
  background: var(--accent-color);
  border-radius: 50%;
  animation: dots 1.4s ease-in-out infinite both;
}

.spinner-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Pulse Spinner */
.spinner-pulse {
  position: relative;
}

.pulse-ring {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 3px solid var(--accent-color);
  border-radius: 50%;
  opacity: 0;
  animation: pulse-ring 1.5s ease-out infinite;
}

.pulse-core {
  width: 100%;
  height: 100%;
  background: var(--accent-color);
  border-radius: 50%;
  animation: pulse-core 1.5s ease-out infinite;
}

@keyframes pulse-ring {
  0% {
    opacity: 0.8;
    transform: scale(0.5);
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

@keyframes pulse-core {
  0%, 100% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1);
  }
}

/* Bars Spinner */
.spinner-bars {
  display: flex;
  gap: 4px;
  height: 40px;
}

.spinner-bars span {
  width: 6px;
  background: var(--accent-color);
  animation: bars 1.2s ease-in-out infinite;
}

.spinner-bars span:nth-child(1) {
  animation-delay: -0.4s;
}

.spinner-bars span:nth-child(2) {
  animation-delay: -0.3s;
}

.spinner-bars span:nth-child(3) {
  animation-delay: -0.2s;
}

.spinner-bars span:nth-child(4) {
  animation-delay: -0.1s;
}

@keyframes bars {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/* Cube Spinner */
.spinner-cube {
  width: 40px;
  height: 40px;
  transform-style: preserve-3d;
  animation: cube-rotate 2s infinite linear;
}

.cube-face {
  position: absolute;
  width: 100%;
  height: 100%;
  background: var(--accent-color);
  opacity: 0.8;
  border: 1px solid var(--border-color);
}

.cube-face-front {
  transform: translateZ(20px);
}

.cube-face-back {
  transform: rotateY(180deg) translateZ(20px);
}

.cube-face-left {
  transform: rotateY(-90deg) translateZ(20px);
}

.cube-face-right {
  transform: rotateY(90deg) translateZ(20px);
}

.cube-face-top {
  transform: rotateX(90deg) translateZ(20px);
}

.cube-face-bottom {
  transform: rotateX(-90deg) translateZ(20px);
}

@keyframes cube-rotate {
  0% {
    transform: rotateX(0deg) rotateY(0deg);
  }
  100% {
    transform: rotateX(360deg) rotateY(360deg);
  }
}

/* Spinner Text */
.spinner-text {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-top: 0.5rem;
  text-align: center;
}

.loading-spinner.inline .spinner-text {
  margin-top: 0;
  margin-left: 0.5rem;
}

/* Size adjustments for other variants */
.sm .spinner-dots span {
  width: 8px;
  height: 8px;
}

.lg .spinner-dots span {
  width: 16px;
  height: 16px;
}

.xl .spinner-dots span {
  width: 20px;
  height: 20px;
}

.sm .spinner-bars {
  height: 24px;
}

.sm .spinner-bars span {
  width: 4px;
}

.lg .spinner-bars {
  height: 48px;
}

.lg .spinner-bars span {
  width: 8px;
}

.xl .spinner-bars {
  height: 60px;
}

.xl .spinner-bars span {
  width: 10px;
}

.sm .spinner-cube {
  width: 24px;
  height: 24px;
}

.lg .spinner-cube {
  width: 56px;
  height: 56px;
}

.xl .spinner-cube {
  width: 72px;
  height: 72px;
}

/* Dark Mode */
:root[data-theme="dark"] .loading-spinner-wrapper.fullscreen {
  background: var(--bg-primary-dark);
}

:root[data-theme="dark"] .loading-overlay {
  background: rgba(0, 0, 0, 0.7);
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .spinner-circle,
  .spinner-dots span,
  .pulse-ring,
  .pulse-core,
  .spinner-bars span,
  .spinner-cube {
    animation: none !important;
  }
}
</style>