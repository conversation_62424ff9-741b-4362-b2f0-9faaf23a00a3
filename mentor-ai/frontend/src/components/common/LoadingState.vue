<template>
  <transition name="fade" mode="out-in">
    <div v-if="isLoading" class="loading-state" :class="[variant, { centered: centered }]">
      <!-- Full Page Loading -->
      <div v-if="variant === 'fullpage'" class="fullpage-loading">
        <div class="loading-content">
          <LoadingSpinner 
            :variant="spinnerVariant" 
            size="xl" 
            :text="loadingText"
          />
          <div v-if="progress !== null" class="loading-progress">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            </div>
            <span class="progress-text">{{ progress }}%</span>
          </div>
        </div>
      </div>
      
      <!-- Inline Loading -->
      <div v-else-if="variant === 'inline'" class="inline-loading">
        <LoadingSpinner 
          :variant="spinnerVariant" 
          :size="spinnerSize" 
          :text="loadingText"
          :inline="true"
        />
      </div>
      
      <!-- Card Loading -->
      <div v-else-if="variant === 'card'" class="card-loading">
        <SkeletonLoader type="card" :show-actions="true" />
      </div>
      
      <!-- List Loading -->
      <div v-else-if="variant === 'list'" class="list-loading">
        <SkeletonLoader type="list" :count="skeletonCount" />
      </div>
      
      <!-- Table Loading -->
      <div v-else-if="variant === 'table'" class="table-loading">
        <SkeletonLoader 
          type="table" 
          :columns="tableColumns" 
          :rows="tableRows" 
        />
      </div>
      
      <!-- Content Loading -->
      <div v-else-if="variant === 'content'" class="content-loading">
        <SkeletonLoader type="article" />
      </div>
      
      <!-- Custom Loading -->
      <div v-else-if="variant === 'custom'" class="custom-loading">
        <slot name="loading">
          <LoadingSpinner :variant="spinnerVariant" :size="spinnerSize" />
        </slot>
      </div>
      
      <!-- Overlay Loading -->
      <div v-else-if="variant === 'overlay'" class="overlay-loading">
        <LoadingSpinner 
          :variant="spinnerVariant" 
          :size="spinnerSize" 
          :text="loadingText"
          :overlay="true"
        />
      </div>
    </div>
    
    <!-- Content when not loading -->
    <div v-else class="loaded-content">
      <transition :name="contentTransition" mode="out-in">
        <slot></slot>
      </transition>
    </div>
  </transition>
</template>

<script>
import SkeletonLoader from './SkeletonLoader.vue'
import LoadingSpinner from './LoadingSpinner.vue'

export default {
  name: 'LoadingState',
  components: {
    SkeletonLoader,
    LoadingSpinner
  },
  props: {
    isLoading: {
      type: Boolean,
      required: true
    },
    variant: {
      type: String,
      default: 'inline',
      validator: value => [
        'fullpage', 'inline', 'card', 'list', 
        'table', 'content', 'custom', 'overlay'
      ].includes(value)
    },
    loadingText: {
      type: String,
      default: 'Carregando...'
    },
    spinnerVariant: {
      type: String,
      default: 'default'
    },
    spinnerSize: {
      type: String,
      default: 'md'
    },
    skeletonCount: {
      type: Number,
      default: 3
    },
    tableColumns: {
      type: Number,
      default: 4
    },
    tableRows: {
      type: Number,
      default: 5
    },
    progress: {
      type: Number,
      default: null,
      validator: value => value === null || (value >= 0 && value <= 100)
    },
    centered: {
      type: Boolean,
      default: false
    },
    contentTransition: {
      type: String,
      default: 'fade'
    },
    minLoadTime: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      actualLoading: this.isLoading,
      loadingTimer: null
    }
  },
  watch: {
    isLoading(newVal) {
      if (newVal) {
        // Start loading
        this.actualLoading = true
      } else if (this.minLoadTime > 0) {
        // Ensure minimum loading time
        if (this.loadingTimer) {
          clearTimeout(this.loadingTimer)
        }
        this.loadingTimer = setTimeout(() => {
          this.actualLoading = false
        }, this.minLoadTime)
      } else {
        this.actualLoading = false
      }
    }
  },
  beforeUnmount() {
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer)
    }
  }
}
</script>

<style scoped>
/* Base Styles */
.loading-state {
  position: relative;
  min-height: 100px;
}

.loading-state.centered {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

/* Full Page Loading */
.fullpage-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
}

/* Progress Bar */
.loading-progress {
  margin-top: 2rem;
  width: 200px;
}

.progress-bar {
  height: 4px;
  background: var(--bg-secondary);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: var(--accent-color);
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 50px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: progress-shine 1s infinite;
}

@keyframes progress-shine {
  0% {
    transform: translateX(-50px);
  }
  100% {
    transform: translateX(50px);
  }
}

.progress-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Inline Loading */
.inline-loading {
  display: inline-block;
  vertical-align: middle;
}

/* Card Loading */
.card-loading {
  width: 100%;
}

/* List Loading */
.list-loading {
  width: 100%;
}

/* Table Loading */
.table-loading {
  width: 100%;
}

/* Content Loading */
.content-loading {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

/* Custom Loading */
.custom-loading {
  width: 100%;
}

/* Overlay Loading */
.overlay-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Slide Transition */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

/* Scale Transition */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0.9);
  opacity: 0;
}

/* Loaded Content */
.loaded-content {
  animation: content-fade-in 0.3s ease;
}

@keyframes content-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Dark Mode */
:root[data-theme="dark"] .fullpage-loading {
  background: var(--bg-primary-dark);
}

:root[data-theme="dark"] .progress-bar {
  background: var(--bg-secondary-dark);
}

/* Responsive */
@media (max-width: 768px) {
  .loading-state.centered {
    min-height: 200px;
  }
  
  .loading-progress {
    width: 160px;
  }
}

/* Error State (can be extended) */
.loading-state.error {
  color: var(--danger-color);
}

.loading-state.error .loading-content {
  animation: shake 0.5s ease;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-10px);
  }
  75% {
    transform: translateX(10px);
  }
}
</style>