<template>
  <div class="ai-menu-integrated">
    <!-- Header Principal -->
    <div class="ai-header">
      <div class="header-content">
        <div class="header-title">
          <font-awesome-icon icon="brain" class="header-icon" />
          <h1>Inteligência Artificial</h1>
        </div>
        <p class="header-subtitle">Ferramentas avançadas de IA para otimizar seus estudos</p>
      </div>
    </div>

    <!-- <PERSON><PERSON> de Ferramentas -->
    <div class="ai-tools-grid">
      <!-- Second Brain -->
      <router-link to="/ia/second-brain" class="ai-tool-card second-brain">
        <div class="tool-icon">
          <font-awesome-icon icon="brain" />
        </div>
        <div class="tool-content">
          <h2>Second Brain</h2>
          <p>Organize e conecte seu conhecimento com IA avançada</p>
          <div class="tool-features">
            <span class="feature">
              <font-awesome-icon icon="check" /> Organização inteligente
            </span>
            <span class="feature">
              <font-awesome-icon icon="check" /> Conexões automáticas
            </span>
            <span class="feature">
              <font-awesome-icon icon="check" /> Busca semântica
            </span>
          </div>
        </div>
        <div class="tool-action">
          <span>Acessar</span>
          <font-awesome-icon icon="arrow-right" />
        </div>
      </router-link>

      <!-- Round AI -->
      <router-link to="/ia/round-ai" class="ai-tool-card round-ai">
        <div class="tool-icon">
          <font-awesome-icon icon="user-md" />
        </div>
        <div class="tool-content">
          <h2>Round AI</h2>
          <p>Transcrição e análise inteligente de áudio médico</p>
          <div class="tool-features">
            <span class="feature">
              <font-awesome-icon icon="check" /> Transcrição precisa
            </span>
            <span class="feature">
              <font-awesome-icon icon="check" /> Análise contextual
            </span>
            <span class="feature">
              <font-awesome-icon icon="check" /> Resumos automáticos
            </span>
          </div>
        </div>
        <div class="tool-action">
          <span>Acessar</span>
          <font-awesome-icon icon="arrow-right" />
        </div>
      </router-link>

      <!-- Flashcards AI -->
      <router-link to="/ia/flashcards" class="ai-tool-card flashcards-ai">
        <div class="tool-icon">
          <font-awesome-icon icon="layer-group" />
        </div>
        <div class="tool-content">
          <h2>Flashcards AI</h2>
          <p>Crie flashcards inteligentes com IA a partir de qualquer conteúdo</p>
          <div class="tool-features">
            <span class="feature">
              <font-awesome-icon icon="check" /> Geração automática
            </span>
            <span class="feature">
              <font-awesome-icon icon="check" /> Repetição espaçada
            </span>
            <span class="feature">
              <font-awesome-icon icon="check" /> Múltiplos formatos
            </span>
          </div>
        </div>
        <div class="tool-action">
          <span>Acessar</span>
          <font-awesome-icon icon="arrow-right" />
        </div>
      </router-link>

      <!-- Questions AI -->
      <router-link to="/ia/questions" class="ai-tool-card questions-ai">
        <div class="tool-icon">
          <font-awesome-icon icon="tasks" />
        </div>
        <div class="tool-content">
          <h2>Questions AI</h2>
          <p>Gerador inteligente de questões de concurso com IA avançada</p>
          <div class="tool-features">
            <span class="feature">
              <font-awesome-icon icon="check" /> Questões personalizadas
            </span>
            <span class="feature">
              <font-awesome-icon icon="check" /> Simulados completos
            </span>
            <span class="feature">
              <font-awesome-icon icon="check" /> Estatísticas detalhadas
            </span>
          </div>
        </div>
        <div class="tool-action">
          <span>Acessar</span>
          <font-awesome-icon icon="arrow-right" />
        </div>
      </router-link>
    </div>

    <!-- Footer Informativo -->
    <div class="ai-footer">
      <div class="footer-info">
        <font-awesome-icon icon="info-circle" />
        <p>Ferramentas de IA cuidadosamente desenvolvidas para atender necessidades específicas do estudo médico.</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AIMenuIntegrated',
  
  mounted() {
    // Log para garantir que o componente foi montado
    console.log('AI Menu Integrado carregado - Second Brain, Round AI, Flashcards AI e Questions AI disponíveis');
  }
}
</script>

<style scoped>
.ai-menu-integrated {
  min-height: 100vh;
  background: linear-gradient(135deg, #0F172A 0%, #1E293B 100%);
  padding: 2rem;
}

/* Header */
.ai-header {
  text-align: center;
  margin-bottom: 4rem;
  animation: fadeInDown 0.6s ease-out;
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
}

.header-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.header-icon {
  font-size: 2.5rem;
  color: #60A5FA;
}

.ai-header h1 {
  font-size: 2.5rem;
  color: #F1F5F9;
  font-weight: 700;
  margin: 0;
}

.header-subtitle {
  font-size: 1.125rem;
  color: #94A3B8;
  margin: 0;
}

/* Grid de Ferramentas */
.ai-tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto 3rem;
}

.ai-tool-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 2rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.ai-tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.second-brain {
  --gradient-start: #60A5FA;
  --gradient-end: #3B82F6;
}

.round-ai {
  --gradient-start: #34D399;
  --gradient-end: #10B981;
}

.flashcards-ai {
  --gradient-start: #A78BFA;
  --gradient-end: #7C3AED;
}

.questions-ai {
  --gradient-start: #FF6B6B;
  --gradient-end: #FF8E53;
}

.ai-tool-card:hover {
  transform: translateY(-4px);
  border-color: rgba(148, 163, 184, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.ai-tool-card:hover::before {
  opacity: 1;
}

.tool-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: #60A5FA;
  transition: all 0.3s ease;
}

.round-ai .tool-icon {
  color: #34D399;
}

.flashcards-ai .tool-icon {
  color: #A78BFA;
}

.questions-ai .tool-icon {
  color: #FF6B6B;
}

.ai-tool-card:hover .tool-icon {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.tool-content h2 {
  font-size: 1.75rem;
  color: #F1F5F9;
  margin: 0 0 0.5rem;
  font-weight: 600;
}

.tool-content p {
  color: #94A3B8;
  line-height: 1.6;
  margin: 0 0 1.5rem;
}

.tool-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #CBD5E1;
  font-size: 0.875rem;
}

.feature svg {
  color: #34D399;
  font-size: 0.75rem;
}

.tool-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  color: #60A5FA;
  font-weight: 500;
  transition: gap 0.3s ease;
}

.round-ai .tool-action {
  color: #34D399;
}

.flashcards-ai .tool-action {
  color: #A78BFA;
}

.questions-ai .tool-action {
  color: #FF6B6B;
}

.ai-tool-card:hover .tool-action {
  gap: 0.5rem;
}

/* Footer */
.ai-footer {
  max-width: 800px;
  margin: 0 auto;
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.footer-info {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.footer-info svg {
  color: #60A5FA;
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.footer-info p {
  color: #94A3B8;
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

/* Animações */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .ai-menu-integrated {
    padding: 1rem;
  }
  
  .ai-header h1 {
    font-size: 2rem;
  }
  
  .header-icon {
    font-size: 2rem;
  }
  
  .ai-tools-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .ai-tool-card {
    padding: 1.5rem;
  }
  
  .tool-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
  
  .tool-content h2 {
    font-size: 1.5rem;
  }
}
</style>