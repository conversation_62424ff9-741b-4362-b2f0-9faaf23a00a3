<template>
  <div class="faq-page">
    <div class="page-header">
      <h1>
        <font-awesome-icon icon="fa-question-circle" />
        Perguntas Frequentes
      </h1>
      <p>Encontre respostas para as dúvidas mais comuns</p>
    </div>
    
    <div class="faq-container">
      <div class="faq-item" v-for="(faq, index) in faqs" :key="index">
        <div class="faq-question" @click="toggleFaq(index)">
          <h3>{{ faq.question }}</h3>
          <font-awesome-icon 
            :icon="faq.open ? 'fa-chevron-up' : 'fa-chevron-down'"
            class="toggle-icon"
          />
        </div>
        <transition name="faq-answer">
          <div v-if="faq.open" class="faq-answer">
            <p>{{ faq.answer }}</p>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FAQ',
  data() {
    return {
      faqs: [
        {
          question: 'Como funciona o sistema de flashcards?',
          answer: 'Nosso sistema de flashcards utiliza IA para otimizar o aprendizado através de repetição espaçada.',
          open: false
        },
        {
          question: 'Posso acessar offline?',
          answer: 'Sim, muitas funcionalidades funcionam offline e sincronizam quando você volta a ter conexão.',
          open: false
        },
        {
          question: 'Como personalizo meu plano de estudos?',
          answer: 'Acesse a seção Plano de Estudo e nossa IA criará um plano personalizado baseado em seus objetivos.',
          open: false
        }
      ]
    }
  },
  methods: {
    toggleFaq(index) {
      this.faqs[index].open = !this.faqs[index].open
    }
  }
}
</script>

<style scoped>
.faq-page {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.faq-container {
  background: var(--card-bg);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--shadow-md);
}

.faq-item {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
}

.faq-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 1rem 0;
}

.faq-question h3 {
  color: var(--text-primary);
  font-size: 1.1rem;
  margin: 0;
}

.toggle-icon {
  color: var(--accent-color);
  transition: transform 0.3s ease;
}

.faq-answer {
  padding: 1rem 0;
}

.faq-answer p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.faq-answer-enter-active,
.faq-answer-leave-active {
  transition: all 0.3s ease;
  max-height: 200px;
  overflow: hidden;
}

.faq-answer-enter-from,
.faq-answer-leave-to {
  max-height: 0;
  opacity: 0;
}
</style>