<template>
  <div class="cookies-page">
    <div class="container">
      <h1>Política de Cookies</h1>
      <p class="last-update">Última atualização: {{ currentDate }}</p>
      
      <section>
        <h2>1. O que são Cookies?</h2>
        <p>Cookies são pequenos arquivos de texto armazenados em seu dispositivo quando você visita nosso site.</p>
      </section>
      
      <section>
        <h2>2. Como Usamos Cookies</h2>
        <p>Utilizamos cookies para melhorar sua experiência, lembrar suas preferências e analisar o uso da plataforma.</p>
      </section>
      
      <section>
        <h2>3. Tipos de Cookies</h2>
        <ul>
          <li><strong>Cookies Essenciais:</strong> Necessários para o funcionamento básico da plataforma</li>
          <li><strong>Cookies de Performance:</strong> Ajudam a melhorar o desempenho do site</li>
          <li><strong>Cookies de Funcionalidade:</strong> Lembram suas preferências e configurações</li>
        </ul>
      </section>
      
      <section>
        <h2>4. Gerenciar Cookies</h2>
        <p>Você pode controlar e/ou deletar cookies através das configurações do seu navegador.</p>
      </section>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Cookies',
  computed: {
    currentDate() {
      return new Date().toLocaleDateString('pt-BR', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    }
  }
}
</script>

<style scoped>
.cookies-page {
  padding: 2rem;
  min-height: 100vh;
  background: var(--background-color);
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--card-bg);
  padding: 3rem;
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
}

h1 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.last-update {
  color: var(--text-secondary);
  font-style: italic;
  margin-bottom: 2rem;
}

section {
  margin-bottom: 2rem;
}

h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

p {
  color: var(--text-secondary);
  line-height: 1.6;
}

ul {
  color: var(--text-secondary);
  line-height: 1.8;
  padding-left: 1.5rem;
}
</style>