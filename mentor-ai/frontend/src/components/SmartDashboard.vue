<template>
  <div class="smart-dashboard">
    <div class="dashboard-header">
      <h2>
        <i class="fas fa-th-large"></i>
        Painel Inteligente
      </h2>
      <div class="view-toggles">
        <button 
          v-for="view in viewOptions" 
          :key="view.id"
          @click="currentView = view.id"
          :class="['view-btn', { active: currentView === view.id }]"
        >
          <i :class="view.icon"></i>
          {{ view.label }}
        </button>
      </div>
    </div>

    <!-- Grid View -->
    <transition name="fade" mode="out-in">
      <div v-if="currentView === 'grid'" class="grid-view">
        <div 
          v-for="tile in dashboardTiles" 
          :key="tile.id"
          :class="['dashboard-tile', tile.size, tile.type]"
          @click="handleTileClick(tile)"
        >
          <div class="tile-content">
            <div class="tile-header">
              <div class="tile-icon" :style="{ backgroundColor: tile.color + '20' }">
                <i :class="tile.icon" :style="{ color: tile.color }"></i>
              </div>
              <h3>{{ tile.title }}</h3>
            </div>
            
            <!-- Dynamic content based on tile type -->
            <component 
              :is="getTileComponent(tile.type)"
              :data="tile.data"
              :color="tile.color"
            />
            
            <div class="tile-action" v-if="tile.action">
              <span>{{ tile.action.text }}</span>
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Timeline View -->
      <div v-else-if="currentView === 'timeline'" class="timeline-view">
        <div class="timeline-container">
          <div 
            v-for="(event, index) in timelineEvents" 
            :key="event.id"
            class="timeline-event"
            :class="{ 'future': event.isFuture }"
          >
            <div class="timeline-marker" :style="{ backgroundColor: event.color }">
              <i :class="event.icon"></i>
            </div>
            <div class="timeline-content">
              <div class="event-time">{{ event.time }}</div>
              <h4>{{ event.title }}</h4>
              <p>{{ event.description }}</p>
              <button v-if="event.action" class="event-action" @click="executeEventAction(event)">
                {{ event.action.text }}
              </button>
            </div>
            <div class="timeline-line" v-if="index < timelineEvents.length - 1"></div>
          </div>
        </div>
      </div>

      <!-- Analytics View -->
      <div v-else-if="currentView === 'analytics'" class="analytics-view">
        <div class="analytics-grid">
          <div class="analytics-card main-chart">
            <h3>Progresso Semanal</h3>
            <canvas ref="weeklyChart"></canvas>
          </div>
          
          <div class="analytics-card stats">
            <h3>Estatísticas Rápidas</h3>
            <div class="stat-list">
              <div v-for="stat in quickStats" :key="stat.id" class="stat-item">
                <div class="stat-icon" :style="{ backgroundColor: stat.color + '20' }">
                  <i :class="stat.icon" :style="{ color: stat.color }"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stat.value }}</div>
                  <div class="stat-label">{{ stat.label }}</div>
                  <div class="stat-change" :class="stat.trend">
                    <i :class="stat.trend === 'up' ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                    {{ stat.change }}%
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="analytics-card goals">
            <h3>Metas do Mês</h3>
            <div class="goals-list">
              <div v-for="goal in monthlyGoals" :key="goal.id" class="goal-item">
                <div class="goal-header">
                  <span>{{ goal.name }}</span>
                  <span class="goal-target">{{ goal.current }}/{{ goal.target }}</span>
                </div>
                <div class="goal-progress">
                  <div 
                    class="goal-fill" 
                    :style="{ 
                      width: (goal.current / goal.target * 100) + '%',
                      backgroundColor: goal.color 
                    }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import Chart from 'chart.js/auto'

// Tile Components
const StatTile = {
  props: ['data', 'color'],
  template: `
    <div class="stat-tile">
      <div class="stat-main">
        <div class="stat-number">{{ data.value }}</div>
        <div class="stat-unit">{{ data.unit }}</div>
      </div>
      <div class="stat-comparison">
        <i :class="data.trend === 'up' ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
        {{ data.change }}% vs última semana
      </div>
    </div>
  `
}

const ProgressTile = {
  props: ['data', 'color'],
  template: `
    <div class="progress-tile">
      <div class="progress-stats">
        <div class="progress-item">
          <span class="label">Concluído</span>
          <span class="value">{{ data.completed }}</span>
        </div>
        <div class="progress-item">
          <span class="label">Restante</span>
          <span class="value">{{ data.remaining }}</span>
        </div>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: data.percentage + '%', backgroundColor: color }"></div>
      </div>
      <div class="progress-label">{{ data.percentage }}% completo</div>
    </div>
  `
}

const ChartTile = {
  props: ['data', 'color'],
  template: `
    <div class="chart-tile">
      <canvas ref="tileChart"></canvas>
    </div>
  `,
  mounted() {
    const ctx = this.$refs.tileChart.getContext('2d')
    new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: this.data.labels,
        datasets: [{
          data: this.data.values,
          backgroundColor: [this.color, '#e0e0e0']
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false }
        }
      }
    })
  }
}

const ListTile = {
  props: ['data', 'color'],
  template: `
    <div class="list-tile">
      <div v-for="(item, index) in data.items.slice(0, 3)" :key="index" class="list-item">
        <i :class="item.icon" :style="{ color: color }"></i>
        <span>{{ item.text }}</span>
        <span class="item-time">{{ item.time }}</span>
      </div>
      <div v-if="data.items.length > 3" class="more-items">
        +{{ data.items.length - 3 }} mais
      </div>
    </div>
  `
}

export default {
  name: 'SmartDashboard',
  
  components: {
    StatTile,
    ProgressTile,
    ChartTile,
    ListTile
  },
  
  setup() {
    const currentView = ref('grid')
    const weeklyChart = ref(null)
    
    const viewOptions = [
      { id: 'grid', label: 'Grade', icon: 'fas fa-th' },
      { id: 'timeline', label: 'Linha do Tempo', icon: 'fas fa-stream' },
      { id: 'analytics', label: 'Análises', icon: 'fas fa-chart-bar' }
    ]
    
    const dashboardTiles = ref([
      {
        id: 1,
        type: 'stat',
        size: 'small',
        title: 'Horas Hoje',
        icon: 'fas fa-clock',
        color: '#667eea',
        data: { value: 4.5, unit: 'h', trend: 'up', change: 12 },
        action: { text: 'Ver detalhes', path: '/progress-dashboard' }
      },
      {
        id: 2,
        type: 'stat',
        size: 'small',
        title: 'Cards Revisados',
        icon: 'fas fa-layer-group',
        color: '#48bb78',
        data: { value: 127, unit: 'cards', trend: 'up', change: 8 },
        action: { text: 'Continuar', path: '/ai-tools/flashcards' }
      },
      {
        id: 3,
        type: 'progress',
        size: 'medium',
        title: 'Meta Semanal',
        icon: 'fas fa-bullseye',
        color: '#ed8936',
        data: { completed: 18, remaining: 7, percentage: 72 },
        action: { text: 'Ver metas', path: '/plano-estudo' }
      },
      {
        id: 4,
        type: 'chart',
        size: 'small',
        title: 'Taxa de Acerto',
        icon: 'fas fa-chart-pie',
        color: '#38b2ac',
        data: { labels: ['Acertos', 'Erros'], values: [87, 13] }
      },
      {
        id: 5,
        type: 'list',
        size: 'medium',
        title: 'Próximas Atividades',
        icon: 'fas fa-list',
        color: '#9f7aea',
        data: {
          items: [
            { icon: 'fas fa-brain', text: 'Revisar Anatomia', time: '14:00' },
            { icon: 'fas fa-clock', text: 'Pomodoro Farmaco', time: '15:30' },
            { icon: 'fas fa-video', text: 'Aula ao vivo', time: '17:00' },
            { icon: 'fas fa-check', text: 'Simulado Cardio', time: '19:00' }
          ]
        }
      }
    ])
    
    const timelineEvents = ref([
      {
        id: 1,
        time: '08:00',
        title: 'Sessão de Estudo Iniciada',
        description: 'Começou revisão de Anatomia Cardiovascular',
        icon: 'fas fa-play',
        color: '#48bb78',
        isFuture: false
      },
      {
        id: 2,
        time: '10:30',
        title: 'Marco Atingido',
        description: '100 cards revisados hoje!',
        icon: 'fas fa-trophy',
        color: '#ecc94b',
        isFuture: false
      },
      {
        id: 3,
        time: '14:00',
        title: 'Próxima Revisão',
        description: 'Farmacologia - 45 cards pendentes',
        icon: 'fas fa-clock',
        color: '#667eea',
        isFuture: true,
        action: { text: 'Iniciar agora' }
      },
      {
        id: 4,
        time: '17:00',
        title: 'Aula ao Vivo',
        description: 'Fisiologia Renal com Dr. Silva',
        icon: 'fas fa-video',
        color: '#ed8936',
        isFuture: true,
        action: { text: 'Entrar na sala' }
      }
    ])
    
    const quickStats = ref([
      { id: 1, icon: 'fas fa-fire', label: 'Sequência', value: '28 dias', color: '#e53e3e', trend: 'up', change: 15 },
      { id: 2, icon: 'fas fa-brain', label: 'Retenção', value: '89%', color: '#48bb78', trend: 'up', change: 3 },
      { id: 3, icon: 'fas fa-clock', label: 'Tempo médio', value: '15s', color: '#667eea', trend: 'down', change: 8 },
      { id: 4, icon: 'fas fa-star', label: 'Ranking', value: '#12', color: '#ecc94b', trend: 'up', change: 5 }
    ])
    
    const monthlyGoals = ref([
      { id: 1, name: 'Horas de Estudo', current: 89, target: 120, color: '#667eea' },
      { id: 2, name: 'Cards Criados', current: 450, target: 500, color: '#48bb78' },
      { id: 3, name: 'Taxa de Conclusão', current: 78, target: 90, color: '#ed8936' },
      { id: 4, name: 'Simulados', current: 6, target: 8, color: '#9f7aea' }
    ])
    
    const getTileComponent = (type) => {
      const components = {
        stat: 'StatTile',
        progress: 'ProgressTile',
        chart: 'ChartTile',
        list: 'ListTile'
      }
      return components[type] || 'div'
    }
    
    const handleTileClick = (tile) => {
      if (tile.action && tile.action.path) {
        // Navigate to path
        console.log('Navigate to:', tile.action.path)
      }
    }
    
    const executeEventAction = (event) => {
      console.log('Execute action for event:', event.id)
    }
    
    const createWeeklyChart = () => {
      if (!weeklyChart.value) return
      
      const ctx = weeklyChart.value.getContext('2d')
      new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'],
          datasets: [
            {
              label: 'Horas de Estudo',
              data: [4, 5, 3.5, 6, 4.5, 3, 2],
              borderColor: '#667eea',
              backgroundColor: 'rgba(102, 126, 234, 0.1)',
              tension: 0.4
            },
            {
              label: 'Cards Revisados',
              data: [120, 150, 100, 180, 160, 90, 60],
              borderColor: '#48bb78',
              backgroundColor: 'rgba(72, 187, 120, 0.1)',
              tension: 0.4,
              yAxisID: 'y1'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'index',
            intersect: false
          },
          scales: {
            y: {
              type: 'linear',
              display: true,
              position: 'left',
              title: {
                display: true,
                text: 'Horas'
              }
            },
            y1: {
              type: 'linear',
              display: true,
              position: 'right',
              title: {
                display: true,
                text: 'Cards'
              },
              grid: {
                drawOnChartArea: false
              }
            }
          }
        }
      })
    }
    
    onMounted(() => {
      if (currentView.value === 'analytics') {
        setTimeout(createWeeklyChart, 300)
      }
    })
    
    return {
      currentView,
      viewOptions,
      dashboardTiles,
      timelineEvents,
      quickStats,
      monthlyGoals,
      weeklyChart,
      getTileComponent,
      handleTileClick,
      executeEventAction
    }
  }
}
</script>

<style scoped>
.smart-dashboard {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.dashboard-header h2 {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-toggles {
  display: flex;
  gap: 0.5rem;
  background: #f7fafc;
  padding: 0.25rem;
  border-radius: 8px;
}

.view-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #718096;
  font-weight: 500;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Grid View */
.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  grid-auto-rows: minmax(150px, auto);
}

.dashboard-tile {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-tile:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dashboard-tile.small {
  grid-column: span 1;
}

.dashboard-tile.medium {
  grid-column: span 2;
}

.dashboard-tile.large {
  grid-column: span 3;
}

.tile-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.tile-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.tile-header h3 {
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0;
  color: #2d3748;
}

.tile-action {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #667eea;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dashboard-tile:hover .tile-action {
  opacity: 1;
}

/* Tile Components */
.stat-tile .stat-main {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
}

.stat-unit {
  font-size: 1rem;
  color: #718096;
}

.stat-comparison {
  font-size: 0.85rem;
  color: #718096;
}

.progress-tile .progress-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.progress-item .label {
  display: block;
  font-size: 0.8rem;
  color: #718096;
}

.progress-item .value {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
}

.progress-bar {
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  transition: width 0.5s ease;
}

.progress-label {
  font-size: 0.85rem;
  color: #718096;
  text-align: center;
}

.chart-tile {
  height: 120px;
}

.list-tile .list-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item i {
  width: 20px;
  text-align: center;
}

.list-item span {
  flex: 1;
  font-size: 0.9rem;
  color: #4a5568;
}

.item-time {
  font-size: 0.8rem;
  color: #a0aec0;
}

.more-items {
  text-align: center;
  font-size: 0.85rem;
  color: #718096;
  margin-top: 0.5rem;
}

/* Timeline View */
.timeline-view {
  padding: 1rem 0;
}

.timeline-container {
  position: relative;
  padding-left: 3rem;
}

.timeline-event {
  position: relative;
  margin-bottom: 2rem;
}

.timeline-marker {
  position: absolute;
  left: -3rem;
  top: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.timeline-content {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.timeline-event.future .timeline-content {
  border-style: dashed;
  opacity: 0.8;
}

.event-time {
  font-size: 0.85rem;
  color: #718096;
  margin-bottom: 0.5rem;
}

.timeline-content h4 {
  margin: 0 0 0.5rem;
  font-size: 1.1rem;
  color: #2d3748;
}

.timeline-content p {
  margin: 0;
  color: #4a5568;
  font-size: 0.9rem;
}

.event-action {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.event-action:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.timeline-line {
  position: absolute;
  left: -2.8rem;
  top: 45px;
  width: 2px;
  height: calc(100% + 1rem);
  background: #e2e8f0;
}

/* Analytics View */
.analytics-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: auto auto;
  gap: 1.5rem;
}

.analytics-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.analytics-card h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: #2d3748;
}

.main-chart {
  grid-column: 1 / 2;
  grid-row: 1 / 3;
  height: 400px;
}

.stat-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a202c;
}

.stat-label {
  font-size: 0.85rem;
  color: #718096;
}

.stat-change {
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.stat-change.up {
  color: #48bb78;
}

.stat-change.down {
  color: #e53e3e;
}

.goals-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.goal-item {
  margin-bottom: 0.5rem;
}

.goal-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.goal-target {
  color: #718096;
}

.goal-progress {
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.goal-fill {
  height: 100%;
  transition: width 0.5s ease;
}

/* Animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .main-chart {
    grid-column: 1;
    grid-row: auto;
    height: 300px;
  }
}

@media (max-width: 768px) {
  .grid-view {
    grid-template-columns: 1fr;
  }
  
  .dashboard-tile.medium,
  .dashboard-tile.large {
    grid-column: span 1;
  }
  
  .view-toggles {
    width: 100%;
    justify-content: space-between;
  }
}
</style>