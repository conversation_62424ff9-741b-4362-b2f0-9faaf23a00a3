<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>{{ modalTitle }}</h2>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <!-- Session Type Selector -->
          <div class="session-type-selector">
            <label>Tipo de Sessão</label>
            <div class="type-options">
              <button 
                v-for="type in sessionTypes" 
                :key="type.id"
                type="button"
                @click="formData.type = type.id"
                :class="['type-option', { selected: formData.type === type.id }]"
              >
                <i :class="type.icon"></i>
                <span>{{ type.label }}</span>
                <span class="type-duration">{{ type.duration }}min</span>
              </button>
            </div>
          </div>
          
          <!-- Subject & Topic -->
          <div class="form-row">
            <div class="form-group">
              <label>Matéria</label>
              <select v-model="formData.subject" required>
                <option value="">Selecione...</option>
                <option v-for="subject in availableSubjects" :key="subject" :value="subject">
                  {{ subject }}
                </option>
              </select>
            </div>
            
            <div class="form-group">
              <label>Tópico</label>
              <input 
                v-model="formData.topic" 
                type="text" 
                placeholder="Ex: Sistema Cardiovascular"
                required
              />
            </div>
          </div>
          
          <!-- Time Settings -->
          <div class="form-group">
            <label>Horário</label>
            <div class="time-selector">
              <div class="time-input">
                <i class="fas fa-clock"></i>
                <input 
                  v-model="formData.startTime" 
                  type="time" 
                  required
                />
              </div>
              <span class="time-separator">até</span>
              <div class="time-input">
                <i class="fas fa-clock"></i>
                <input 
                  v-model="formData.endTime" 
                  type="time" 
                  :min="formData.startTime"
                  required
                />
              </div>
              <div class="duration-display">
                <i class="fas fa-hourglass-half"></i>
                <span>{{ calculatedDuration }} min</span>
              </div>
            </div>
          </div>
          
          <!-- Advanced Settings -->
          <div class="advanced-settings">
            <button 
              type="button"
              @click="showAdvanced = !showAdvanced"
              class="advanced-toggle"
            >
              <i :class="showAdvanced ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
              Configurações Avançadas
            </button>
            
            <transition name="slide">
              <div v-if="showAdvanced" class="advanced-content">
                <div class="form-row">
                  <div class="form-group">
                    <label>Dificuldade</label>
                    <select v-model="formData.difficulty">
                      <option value="Fácil">Fácil</option>
                      <option value="Médio">Médio</option>
                      <option value="Difícil">Difícil</option>
                    </select>
                  </div>
                  
                  <div class="form-group">
                    <label>Foco Necessário</label>
                    <div class="focus-slider">
                      <input 
                        v-model="formData.focusRequired" 
                        type="range" 
                        min="50" 
                        max="100" 
                        step="5"
                      />
                      <span>{{ formData.focusRequired }}%</span>
                    </div>
                  </div>
                </div>
                
                <div class="form-group">
                  <label>Lembrete</label>
                  <select v-model="formData.reminder">
                    <option value="0">Sem lembrete</option>
                    <option value="5">5 minutos antes</option>
                    <option value="15">15 minutos antes</option>
                    <option value="30">30 minutos antes</option>
                  </select>
                </div>
              </div>
            </transition>
          </div>
          
          <!-- Form Actions -->
          <div class="form-actions">
            <button type="button" @click="$emit('close')" class="btn-cancel">
              Cancelar
            </button>
            <button type="submit" class="btn-save">
              <i class="fas fa-check"></i>
              {{ session ? 'Atualizar' : 'Criar' }} Sessão
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'SessionModalOptimized',
  props: {
    session: Object,
    suggestedTime: String,
    availableSubjects: {
      type: Array,
      default: () => []
    }
  },
  emits: ['save', 'close'],
  setup(props, { emit }) {
    const formData = ref({
      subject: '',
      topic: '',
      type: 'focus',
      startTime: '',
      endTime: '',
      difficulty: 'Médio',
      focusRequired: 80,
      reminder: '15'
    })
    
    const showAdvanced = ref(false)
    
    const sessionTypes = [
      { id: 'pomodoro', label: 'Pomodoro', icon: 'fas fa-stopwatch', duration: 25 },
      { id: 'focus', label: 'Foco Profundo', icon: 'fas fa-brain', duration: 50 },
      { id: 'review', label: 'Revisão', icon: 'fas fa-redo', duration: 30 },
      { id: 'practice', label: 'Prática', icon: 'fas fa-dumbbell', duration: 45 },
      { id: 'custom', label: 'Personalizada', icon: 'fas fa-cog', duration: '?' }
    ]
    
    const modalTitle = computed(() => {
      return props.session ? 'Editar Sessão' : 'Nova Sessão de Estudo'
    })
    
    const calculatedDuration = computed(() => {
      if (!formData.value.startTime || !formData.value.endTime) return 0
      
      const [startHour, startMin] = formData.value.startTime.split(':').map(Number)
      const [endHour, endMin] = formData.value.endTime.split(':').map(Number)
      
      const startMinutes = startHour * 60 + startMin
      const endMinutes = endHour * 60 + endMin
      
      return endMinutes > startMinutes ? endMinutes - startMinutes : 0
    })
    
    // Auto-set end time based on session type
    watch(() => formData.value.type, (newType) => {
      if (newType !== 'custom' && formData.value.startTime) {
        const type = sessionTypes.find(t => t.id === newType)
        if (type && typeof type.duration === 'number') {
          const [hours, minutes] = formData.value.startTime.split(':').map(Number)
          const endDate = new Date()
          endDate.setHours(hours, minutes + type.duration)
          
          const endHours = endDate.getHours().toString().padStart(2, '0')
          const endMinutes = endDate.getMinutes().toString().padStart(2, '0')
          formData.value.endTime = `${endHours}:${endMinutes}`
        }
      }
    })
    
    // Initialize with session data if editing
    if (props.session) {
      Object.assign(formData.value, props.session)
    } else if (props.suggestedTime) {
      formData.value.startTime = props.suggestedTime
    }
    
    const handleSubmit = () => {
      if (calculatedDuration.value <= 0) {
        alert('O horário de término deve ser posterior ao horário de início')
        return
      }
      
      emit('save', {
        ...formData.value,
        duration: calculatedDuration.value
      })
    }
    
    return {
      formData,
      showAdvanced,
      sessionTypes,
      modalTitle,
      calculatedDuration,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: #1a1a2e;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: white;
}

.close-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.modal-body {
  padding: 2rem;
}

/* Session Type Selector */
.session-type-selector {
  margin-bottom: 2rem;
}

.session-type-selector label {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.type-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
  gap: 0.75rem;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.type-option:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.type-option.selected {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  color: #667eea;
}

.type-option i {
  font-size: 1.25rem;
}

.type-option span {
  font-size: 0.813rem;
  font-weight: 500;
}

.type-duration {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* Form Elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(102, 126, 234, 0.5);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Time Selector */
.time-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.time-input {
  position: relative;
  flex: 1;
}

.time-input i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.4);
  font-size: 0.875rem;
}

.time-input input {
  padding-left: 2.5rem;
}

.time-separator {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.875rem;
}

.duration-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 10px;
  color: #667eea;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Advanced Settings */
.advanced-settings {
  margin: 2rem 0;
}

.advanced-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.advanced-toggle:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.advanced-content {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 10px;
}

.focus-slider {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.focus-slider input[type="range"] {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.focus-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #667eea;
  border-radius: 50%;
  cursor: pointer;
}

.focus-slider span {
  font-size: 0.875rem;
  font-weight: 600;
  color: #667eea;
  min-width: 40px;
  text-align: right;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel,
.btn-save {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.btn-save {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-save:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* Transitions */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>