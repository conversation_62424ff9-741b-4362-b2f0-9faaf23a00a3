# AdvancedAnalytics Component Integration Guide

## Installation Requirements

Before using the AdvancedAnalytics component, you need to install the following dependencies:

```bash
# Install D3.js for knowledge graph visualization
npm install d3

# Install Three.js for 3D card preview
npm install three

# Optional: Install additional libraries for enhanced features
npm install d3-force d3-hierarchy
npm install three-orbitcontrols
```

## Basic Usage

### 1. Import the Component

```vue
<template>
  <div id="app">
    <AdvancedAnalytics :analytics-data="analyticsData" />
  </div>
</template>

<script>
import AdvancedAnalytics from '@/components/AdvancedAnalytics.vue'

export default {
  name: 'App',
  components: {
    AdvancedAnalytics
  },
  data() {
    return {
      analyticsData: {} // Will be populated from your analytics service
    }
  },
  async mounted() {
    // Fetch analytics data from your service
    await this.loadAnalyticsData()
  },
  methods: {
    async loadAnalyticsData() {
      // Example: Load data from SpacedRepetitionServiceUltra
      const spacedRepetitionService = new SpacedRepetitionServiceUltra()
      const userStats = await spacedRepetitionService.analyzePerformance(userId)
      const fullReport = await spacedRepetitionService.generateFullReport(userStats)
      
      this.analyticsData = fullReport
    }
  }
}
</script>
```

### 2. Integration with Vue Router

Add a route for the analytics page:

```javascript
// In your router/index.js
{
  path: '/analytics',
  name: 'AdvancedAnalytics',
  component: () => import('@/components/AdvancedAnalytics.vue'),
  meta: { requiresAuth: true }
}
```

### 3. Integration with Vuex Store

If you're using Vuex, you can manage the analytics data in the store:

```javascript
// In your store module
const analyticsModule = {
  state: {
    analyticsData: null,
    isLoading: false
  },
  mutations: {
    SET_ANALYTICS_DATA(state, data) {
      state.analyticsData = data
    },
    SET_LOADING(state, loading) {
      state.isLoading = loading
    }
  },
  actions: {
    async fetchAnalytics({ commit }, userId) {
      commit('SET_LOADING', true)
      try {
        const service = new SpacedRepetitionServiceUltra()
        const data = await service.generateFullReport(userId)
        commit('SET_ANALYTICS_DATA', data)
      } finally {
        commit('SET_LOADING', false)
      }
    }
  }
}
```

## Advanced Features Implementation

### 1. D3.js Knowledge Graph

To implement the actual D3.js knowledge graph, add this code to the component:

```javascript
import * as d3 from 'd3'

const initializeD3Graph = () => {
  const width = 800
  const height = 400
  
  const svg = d3.select('#knowledge-graph')
    .append('svg')
    .attr('width', width)
    .attr('height', height)
  
  // Sample data structure
  const data = {
    nodes: [
      { id: 1, name: 'Anatomy', category: 'mastered', value: 30 },
      { id: 2, name: 'Physiology', category: 'in-progress', value: 20 },
      { id: 3, name: 'Pathology', category: 'new', value: 15 }
    ],
    links: [
      { source: 1, target: 2, value: 10 },
      { source: 2, target: 3, value: 5 }
    ]
  }
  
  // Create force simulation
  const simulation = d3.forceSimulation(data.nodes)
    .force('link', d3.forceLink(data.links).id(d => d.id))
    .force('charge', d3.forceManyBody().strength(-300))
    .force('center', d3.forceCenter(width / 2, height / 2))
  
  // Add links
  const link = svg.append('g')
    .selectAll('line')
    .data(data.links)
    .enter().append('line')
    .attr('stroke', '#999')
    .attr('stroke-opacity', 0.6)
    .attr('stroke-width', d => Math.sqrt(d.value))
  
  // Add nodes
  const node = svg.append('g')
    .selectAll('circle')
    .data(data.nodes)
    .enter().append('circle')
    .attr('r', d => Math.sqrt(d.value) * 2)
    .attr('fill', d => {
      const colors = {
        'mastered': '#10b981',
        'in-progress': '#3b82f6',
        'new': '#8b5cf6'
      }
      return colors[d.category] || '#999'
    })
    .call(d3.drag()
      .on('start', dragstarted)
      .on('drag', dragged)
      .on('end', dragended))
  
  // Add labels
  const label = svg.append('g')
    .selectAll('text')
    .data(data.nodes)
    .enter().append('text')
    .text(d => d.name)
    .attr('font-size', 12)
    .attr('dx', 15)
    .attr('dy', 4)
  
  // Update positions on tick
  simulation.on('tick', () => {
    link
      .attr('x1', d => d.source.x)
      .attr('y1', d => d.source.y)
      .attr('x2', d => d.target.x)
      .attr('y2', d => d.target.y)
    
    node
      .attr('cx', d => d.x)
      .attr('cy', d => d.y)
    
    label
      .attr('x', d => d.x)
      .attr('y', d => d.y)
  })
  
  // Drag functions
  function dragstarted(event, d) {
    if (!event.active) simulation.alphaTarget(0.3).restart()
    d.fx = d.x
    d.fy = d.y
  }
  
  function dragged(event, d) {
    d.fx = event.x
    d.fy = event.y
  }
  
  function dragended(event, d) {
    if (!event.active) simulation.alphaTarget(0)
    d.fx = null
    d.fy = null
  }
}
```

### 2. Three.js 3D Card Preview

To implement the 3D card preview with Three.js:

```javascript
import * as THREE from 'three'

const initializeThreeJS = () => {
  const container = document.getElementById('three-card-preview')
  const width = container.clientWidth
  const height = 400
  
  // Scene setup
  const scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf0f0f0)
  
  // Camera setup
  const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
  camera.position.z = 5
  
  // Renderer setup
  const renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  container.appendChild(renderer.domElement)
  
  // Card geometry
  const geometry = new THREE.BoxGeometry(3, 4, 0.1)
  
  // Card materials (front and back)
  const materials = [
    new THREE.MeshBasicMaterial({ color: 0xffffff }), // right
    new THREE.MeshBasicMaterial({ color: 0xffffff }), // left
    new THREE.MeshBasicMaterial({ color: 0xffffff }), // top
    new THREE.MeshBasicMaterial({ color: 0xffffff }), // bottom
    new THREE.MeshBasicMaterial({ 
      map: new THREE.TextureLoader().load('/path/to/card-front.png')
    }), // front
    new THREE.MeshBasicMaterial({ 
      map: new THREE.TextureLoader().load('/path/to/card-back.png')
    }) // back
  ]
  
  // Create mesh
  const card = new THREE.Mesh(geometry, materials)
  scene.add(card)
  
  // Lighting
  const light = new THREE.DirectionalLight(0xffffff, 1)
  light.position.set(0, 0, 10)
  scene.add(light)
  
  // Animation
  const animate = () => {
    requestAnimationFrame(animate)
    
    if (isRotating.value) {
      card.rotation.y += 0.01
    }
    
    renderer.render(scene, camera)
  }
  
  animate()
  
  // Handle window resize
  window.addEventListener('resize', () => {
    const newWidth = container.clientWidth
    camera.aspect = newWidth / height
    camera.updateProjectionMatrix()
    renderer.setSize(newWidth, height)
  })
  
  // Store references for interaction
  threeScene.value = { scene, camera, renderer, card }
}
```

## Data Structure Example

The component expects analytics data in this format:

```javascript
const analyticsData = {
  generatedAt: "2024-01-15T10:30:00Z",
  user: "user123",
  summary: {
    totalStudyTime: 3600, // minutes
    totalCards: 500,
    masteredCards: 350,
    averageAccuracy: 0.87,
    currentStreak: 28
  },
  detailedMetrics: {
    retentionRates: {
      shortTerm: 95,
      mediumTerm: 87,
      longTerm: 78
    },
    categoryBreakdown: [
      { category: "Anatomy", cards: 150, mastered: 120, accuracy: 0.89 },
      { category: "Physiology", cards: 100, mastered: 75, accuracy: 0.82 }
    ],
    difficultyDistribution: {
      easy: 0.3,
      medium: 0.5,
      hard: 0.2
    },
    learningCurve: [
      { date: "2024-01-01", accuracy: 0.65 },
      { date: "2024-01-08", accuracy: 0.75 },
      { date: "2024-01-15", accuracy: 0.87 }
    ]
  },
  insights: [
    {
      id: "irregular-study",
      icon: "fas fa-exclamation-triangle",
      title: "Irregular Study Pattern",
      description: "Your study pattern has been irregular...",
      action: "/settings/schedule",
      actionText: "Set Schedule"
    }
  ],
  recommendations: [
    {
      type: "practice",
      priority: "high",
      message: "Increase review frequency for better retention"
    }
  ],
  predictions: {
    masteryDate: "2024-02-15T00:00:00Z",
    nextMilestone: {
      label: "Expert",
      cards: 1000,
      cardsNeeded: 150,
      daysToReach: 30
    }
  }
}
```

## Customization Options

### 1. Theme Customization

You can customize the component's appearance by overriding CSS variables:

```css
.advanced-analytics {
  --analytics-primary: #4f46e5;
  --analytics-accent: #ec4899;
  --analytics-success: #10b981;
  --analytics-warning: #f59e0b;
  --analytics-danger: #ef4444;
}
```

### 2. Feature Toggles

You can control which features are displayed:

```vue
<AdvancedAnalytics 
  :analytics-data="analyticsData"
  :show-knowledge-graph="true"
  :show-3d-preview="true"
  :enable-dark-mode="true"
  :enable-export="true"
/>
```

### 3. Event Handlers

The component emits several events:

```vue
<AdvancedAnalytics 
  @suggestion-applied="handleSuggestionApplied"
  @export-complete="handleExportComplete"
  @milestone-selected="handleMilestoneSelected"
/>
```

## Performance Considerations

1. **Lazy Loading**: The component uses dynamic imports for heavy libraries
2. **Data Virtualization**: Large datasets are paginated
3. **Debounced Updates**: Real-time updates are debounced to prevent excessive re-renders
4. **Canvas Optimization**: WebGL contexts are properly disposed when component unmounts

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

WebGL support is required for the 3D card preview feature.