<template>
  <div class="visualizer-3d-container">
    <!-- 3D Knowledge Universe -->
    <div class="knowledge-universe">
      <h3 class="universe-title">
        <i class="fas fa-globe"></i>
        Universo do Conhecimento 3D
      </h3>
      
      <div class="universe-viewport" ref="universeContainer">
        <canvas ref="universeCanvas"></canvas>
        
        <!-- Universe Controls -->
        <div class="universe-controls">
          <div class="control-panel">
            <button @click="resetView" class="control-btn" title="Resetar Visualização">
              <i class="fas fa-home"></i>
            </button>
            <button @click="toggleRotation" class="control-btn" :class="{ active: autoRotate }" title="Rotação Automática">
              <i class="fas fa-sync-alt"></i>
            </button>
            <button @click="toggleLabels" class="control-btn" :class="{ active: showLabels }" title="Mostrar Rótulos">
              <i class="fas fa-tag"></i>
            </button>
            <button @click="toggleConnections" class="control-btn" :class="{ active: showConnections }" title="Mostrar Conexões">
              <i class="fas fa-project-diagram"></i>
            </button>
          </div>
          
          <div class="zoom-controls">
            <button @click="zoomIn" class="zoom-btn">
              <i class="fas fa-plus"></i>
            </button>
            <input type="range" v-model="zoomLevel" min="0.5" max="3" step="0.1" class="zoom-slider">
            <button @click="zoomOut" class="zoom-btn">
              <i class="fas fa-minus"></i>
            </button>
          </div>
        </div>
        
        <!-- Node Information Panel -->
        <transition name="slide-fade">
          <div v-if="selectedNode" class="node-info-panel">
            <div class="panel-header">
              <h4>{{ selectedNode.name }}</h4>
              <button @click="selectedNode = null" class="close-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            
            <div class="panel-content">
              <div class="info-section">
                <label>Categoria</label>
                <span>{{ selectedNode.category }}</span>
              </div>
              
              <div class="info-section">
                <label>Nível de Domínio</label>
                <div class="mastery-bar">
                  <div class="mastery-fill" :style="{ width: selectedNode.mastery + '%' }"></div>
                </div>
                <span class="mastery-text">{{ selectedNode.mastery }}%</span>
              </div>
              
              <div class="info-section">
                <label>Conexões</label>
                <div class="connections-list">
                  <div v-for="conn in selectedNode.connections" :key="conn.id" 
                       class="connection-item"
                       @click="selectNodeById(conn.id)">
                    <i class="fas fa-link"></i>
                    <span>{{ conn.name }}</span>
                    <span class="strength">{{ conn.strength }}%</span>
                  </div>
                </div>
              </div>
              
              <div class="info-section">
                <label>Última Revisão</label>
                <span>{{ formatDate(selectedNode.lastReview) }}</span>
              </div>
              
              <div class="action-buttons">
                <button @click="studyNode(selectedNode)" class="action-btn primary">
                  <i class="fas fa-book"></i>
                  Estudar
                </button>
                <button @click="reviewNode(selectedNode)" class="action-btn secondary">
                  <i class="fas fa-redo"></i>
                  Revisar
                </button>
              </div>
            </div>
          </div>
        </transition>
        
        <!-- Legend -->
        <div class="universe-legend">
          <h5>Legenda</h5>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-color" style="background: #8b5cf6"></div>
              <span>Anatomia</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #3b82f6"></div>
              <span>Fisiologia</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #10b981"></div>
              <span>Farmacologia</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #f59e0b"></div>
              <span>Patologia</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #ef4444"></div>
              <span>Clínica</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Neural Pathways Visualization -->
    <div class="neural-pathways">
      <h3 class="pathways-title">
        <i class="fas fa-brain"></i>
        Caminhos Neurais de Aprendizado
      </h3>
      
      <div class="pathways-container">
        <div class="pathway-visualization" ref="pathwayCanvas">
          <!-- SVG Neural Network -->
          <svg class="neural-svg" viewBox="0 0 800 600">
            <!-- Input Layer -->
            <g class="input-layer">
              <text x="50" y="30" class="layer-label">Entrada</text>
              <circle v-for="i in 5" :key="'input-' + i"
                      :cx="100" 
                      :cy="50 + i * 100"
                      r="20"
                      class="neuron input-neuron"
                      :class="{ active: activeNeurons.includes('input-' + i) }"
                      @click="activateNeuron('input-' + i)">
              </circle>
            </g>
            
            <!-- Hidden Layers -->
            <g class="hidden-layer-1">
              <text x="250" y="30" class="layer-label">Processamento 1</text>
              <circle v-for="i in 7" :key="'hidden1-' + i"
                      :cx="300" 
                      :cy="30 + i * 80"
                      r="20"
                      class="neuron hidden-neuron"
                      :class="{ active: activeNeurons.includes('hidden1-' + i) }"
                      @click="activateNeuron('hidden1-' + i)">
              </circle>
            </g>
            
            <g class="hidden-layer-2">
              <text x="450" y="30" class="layer-label">Processamento 2</text>
              <circle v-for="i in 7" :key="'hidden2-' + i"
                      :cx="500" 
                      :cy="30 + i * 80"
                      r="20"
                      class="neuron hidden-neuron"
                      :class="{ active: activeNeurons.includes('hidden2-' + i) }"
                      @click="activateNeuron('hidden2-' + i)">
              </circle>
            </g>
            
            <!-- Output Layer -->
            <g class="output-layer">
              <text x="650" y="30" class="layer-label">Saída</text>
              <circle v-for="i in 5" :key="'output-' + i"
                      :cx="700" 
                      :cy="50 + i * 100"
                      r="20"
                      class="neuron output-neuron"
                      :class="{ active: activeNeurons.includes('output-' + i) }"
                      @click="activateNeuron('output-' + i)">
              </circle>
            </g>
            
            <!-- Connections -->
            <g class="connections">
              <line v-for="conn in neuralConnections" :key="conn.id"
                    :x1="conn.x1" :y1="conn.y1"
                    :x2="conn.x2" :y2="conn.y2"
                    class="neural-connection"
                    :class="{ active: conn.active, firing: conn.firing }"
                    :stroke-width="conn.weight * 3">
              </line>
            </g>
            
            <!-- Synaptic Pulses -->
            <g class="synaptic-pulses">
              <circle v-for="pulse in synapticPulses" :key="pulse.id"
                      :cx="pulse.x" 
                      :cy="pulse.y"
                      r="5"
                      class="synaptic-pulse">
                <animateMotion :dur="pulse.duration + 's'" repeatCount="indefinite">
                  <mpath :href="'#path-' + pulse.pathId" />
                </animateMotion>
              </circle>
            </g>
          </svg>
        </div>
        
        <div class="pathway-controls">
          <h4>Simulação Neural</h4>
          
          <div class="simulation-controls">
            <div class="control-group">
              <label>Velocidade de Propagação</label>
              <input type="range" v-model="propagationSpeed" min="0.5" max="5" step="0.5">
              <span>{{ propagationSpeed }}x</span>
            </div>
            
            <div class="control-group">
              <label>Força das Conexões</label>
              <input type="range" v-model="connectionStrength" min="0" max="1" step="0.1">
              <span>{{ (connectionStrength * 100).toFixed(0) }}%</span>
            </div>
            
            <div class="control-group">
              <label>Taxa de Aprendizado</label>
              <input type="range" v-model="learningRate" min="0.01" max="0.5" step="0.01">
              <span>{{ learningRate }}</span>
            </div>
          </div>
          
          <div class="simulation-buttons">
            <button @click="startSimulation" class="sim-btn primary">
              <i class="fas fa-play"></i>
              Iniciar Simulação
            </button>
            <button @click="resetSimulation" class="sim-btn secondary">
              <i class="fas fa-redo"></i>
              Resetar
            </button>
            <button @click="trainNetwork" class="sim-btn accent">
              <i class="fas fa-graduation-cap"></i>
              Treinar Rede
            </button>
          </div>
          
          <div class="network-stats">
            <div class="stat-item">
              <label>Neurônios Ativos</label>
              <span>{{ activeNeurons.length }}</span>
            </div>
            <div class="stat-item">
              <label>Sinapses Ativas</label>
              <span>{{ activeSynapses }}</span>
            </div>
            <div class="stat-item">
              <label>Eficiência</label>
              <span>{{ networkEfficiency }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Immersive Study Environment -->
    <div class="immersive-environment">
      <h3 class="environment-title">
        <i class="fas fa-vr-cardboard"></i>
        Ambiente Imersivo de Estudo
      </h3>
      
      <div class="environment-container">
        <div class="vr-viewport" ref="vrContainer">
          <div class="vr-scene" :class="{ active: vrMode }">
            <!-- 3D Room -->
            <div class="room-3d">
              <div class="wall wall-back">
                <div class="knowledge-board">
                  <h4>{{ currentTopic }}</h4>
                  <div class="board-content">
                    <div v-for="concept in topicConcepts" :key="concept.id" 
                         class="concept-card-3d"
                         @click="selectConcept(concept)">
                      <div class="card-face card-front">
                        <i :class="concept.icon"></i>
                        <span>{{ concept.title }}</span>
                      </div>
                      <div class="card-face card-back">
                        <p>{{ concept.description }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="wall wall-left">
                <div class="progress-display">
                  <h5>Progresso de Hoje</h5>
                  <div class="progress-3d">
                    <div class="progress-bar-3d">
                      <div class="progress-fill-3d" :style="{ height: dailyProgress + '%' }"></div>
                    </div>
                    <span class="progress-text">{{ dailyProgress }}%</span>
                  </div>
                </div>
              </div>
              
              <div class="wall wall-right">
                <div class="achievements-display">
                  <h5>Conquistas Recentes</h5>
                  <div class="achievement-badges">
                    <div v-for="badge in recentBadges" :key="badge.id" 
                         class="badge-3d"
                         :class="badge.rarity">
                      <i :class="badge.icon"></i>
                      <span>{{ badge.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="floor">
                <div class="study-area">
                  <div class="holographic-desk">
                    <div class="holo-display">
                      <div class="holo-content" v-if="selectedConcept">
                        <h4>{{ selectedConcept.title }}</h4>
                        <div class="holo-visualization">
                          <!-- 3D model would go here -->
                          <div class="placeholder-3d-model">
                            <i :class="selectedConcept.icon" class="model-icon"></i>
                          </div>
                        </div>
                        <p>{{ selectedConcept.details }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="ceiling">
                <div class="ambient-particles">
                  <span v-for="i in 50" :key="i" class="particle-3d"></span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="vr-controls">
            <button @click="toggleVRMode" class="vr-btn" :class="{ active: vrMode }">
              <i class="fas fa-vr-cardboard"></i>
              {{ vrMode ? 'Sair do Modo VR' : 'Entrar em Modo VR' }}
            </button>
            
            <div class="view-controls">
              <button @click="rotateView('left')" class="view-btn">
                <i class="fas fa-arrow-left"></i>
              </button>
              <button @click="rotateView('up')" class="view-btn">
                <i class="fas fa-arrow-up"></i>
              </button>
              <button @click="rotateView('down')" class="view-btn">
                <i class="fas fa-arrow-down"></i>
              </button>
              <button @click="rotateView('right')" class="view-btn">
                <i class="fas fa-arrow-right"></i>
              </button>
            </div>
          </div>
        </div>
        
        <div class="environment-settings">
          <h4>Configurações do Ambiente</h4>
          
          <div class="setting-group">
            <label>Tema do Ambiente</label>
            <select v-model="environmentTheme" @change="updateEnvironment">
              <option value="space">Espaço Sideral</option>
              <option value="library">Biblioteca Clássica</option>
              <option value="nature">Natureza Relaxante</option>
              <option value="lab">Laboratório Futurista</option>
              <option value="abstract">Abstrato Minimalista</option>
            </select>
          </div>
          
          <div class="setting-group">
            <label>Música Ambiente</label>
            <select v-model="ambientMusic" @change="updateMusic">
              <option value="none">Sem Música</option>
              <option value="lofi">Lo-fi Study</option>
              <option value="classical">Clássica</option>
              <option value="nature">Sons da Natureza</option>
              <option value="binaural">Batidas Binaurais</option>
            </select>
          </div>
          
          <div class="setting-group">
            <label>Iluminação</label>
            <div class="lighting-controls">
              <input type="range" v-model="lightingIntensity" min="0" max="100">
              <input type="color" v-model="lightingColor" @change="updateLighting">
            </div>
          </div>
          
          <div class="setting-group">
            <label>Efeitos Visuais</label>
            <div class="effects-toggles">
              <label class="toggle-item">
                <input type="checkbox" v-model="effects.particles">
                <span>Partículas</span>
              </label>
              <label class="toggle-item">
                <input type="checkbox" v-model="effects.glow">
                <span>Brilho</span>
              </label>
              <label class="toggle-item">
                <input type="checkbox" v-model="effects.reflections">
                <span>Reflexões</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mind Map 3D -->
    <div class="mind-map-3d">
      <h3 class="mindmap-title">
        <i class="fas fa-sitemap"></i>
        Mapa Mental Tridimensional
      </h3>
      
      <div class="mindmap-container">
        <div class="mindmap-viewport" ref="mindmapCanvas">
          <!-- 3D Mind Map would be rendered here -->
          <div class="mindmap-placeholder">
            <div class="central-node">
              <div class="node-sphere">
                <span>{{ centralTopic }}</span>
              </div>
            </div>
            
            <div class="branch-nodes">
              <div v-for="(branch, index) in mindMapBranches" :key="branch.id"
                   class="branch-node"
                   :style="getBranchStyle(index)">
                <div class="node-sphere" :class="branch.category">
                  <span>{{ branch.title }}</span>
                </div>
                
                <div class="sub-branches">
                  <div v-for="(sub, subIndex) in branch.children" :key="sub.id"
                       class="sub-branch"
                       :style="getSubBranchStyle(index, subIndex)">
                    <div class="sub-node">
                      <span>{{ sub.title }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="mindmap-tools">
          <h4>Ferramentas do Mapa Mental</h4>
          
          <div class="tool-buttons">
            <button @click="addBranch" class="tool-btn">
              <i class="fas fa-plus"></i>
              Adicionar Ramo
            </button>
            <button @click="editMode = !editMode" class="tool-btn" :class="{ active: editMode }">
              <i class="fas fa-edit"></i>
              Editar
            </button>
            <button @click="exportMindMap" class="tool-btn">
              <i class="fas fa-download"></i>
              Exportar
            </button>
            <button @click="shareMindMap" class="tool-btn">
              <i class="fas fa-share"></i>
              Compartilhar
            </button>
          </div>
          
          <div class="mindmap-stats">
            <div class="stat">
              <label>Nós Totais</label>
              <span>{{ totalNodes }}</span>
            </div>
            <div class="stat">
              <label>Conexões</label>
              <span>{{ totalConnections }}</span>
            </div>
            <div class="stat">
              <label>Profundidade</label>
              <span>{{ mapDepth }} níveis</span>
            </div>
          </div>
          
          <div class="collaboration-info" v-if="collaborators.length > 0">
            <h5>Colaboradores Online</h5>
            <div class="collaborator-list">
              <div v-for="user in collaborators" :key="user.id" 
                   class="collaborator-avatar"
                   :title="user.name">
                <img :src="user.avatar" :alt="user.name">
                <span class="status-dot" :class="user.status"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'StudyAssistant3DVisualizer',
  
  setup() {
    const store = useStore();
    
    // Refs
    const universeContainer = ref(null);
    const universeCanvas = ref(null);
    const pathwayCanvas = ref(null);
    const vrContainer = ref(null);
    const mindmapCanvas = ref(null);
    
    // Knowledge Universe State
    const knowledgeNodes = ref([]);
    const selectedNode = ref(null);
    const autoRotate = ref(true);
    const showLabels = ref(true);
    const showConnections = ref(true);
    const zoomLevel = ref(1);
    
    // Neural Pathways State
    const activeNeurons = ref([]);
    const neuralConnections = ref([]);
    const synapticPulses = ref([]);
    const propagationSpeed = ref(1);
    const connectionStrength = ref(0.7);
    const learningRate = ref(0.1);
    const activeSynapses = ref(0);
    const networkEfficiency = ref(85);
    
    // VR Environment State
    const vrMode = ref(false);
    const currentTopic = ref('Sistema Cardiovascular');
    const selectedConcept = ref(null);
    const dailyProgress = ref(65);
    const environmentTheme = ref('space');
    const ambientMusic = ref('lofi');
    const lightingIntensity = ref(70);
    const lightingColor = ref('#ffffff');
    const effects = ref({
      particles: true,
      glow: true,
      reflections: false
    });
    
    const topicConcepts = ref([
      {
        id: 1,
        title: 'Anatomia Cardíaca',
        icon: 'fas fa-heart',
        description: 'Estrutura e componentes do coração',
        details: 'O coração é dividido em quatro câmaras...'
      },
      {
        id: 2,
        title: 'Ciclo Cardíaco',
        icon: 'fas fa-heartbeat',
        description: 'Fases do ciclo de contração e relaxamento',
        details: 'O ciclo cardíaco consiste em sístole e diástole...'
      },
      {
        id: 3,
        title: 'Sistema de Condução',
        icon: 'fas fa-bolt',
        description: 'Propagação do impulso elétrico',
        details: 'O nó sinoatrial inicia o impulso elétrico...'
      }
    ]);
    
    const recentBadges = ref([
      { id: 1, name: 'Mestre Cardio', icon: 'fas fa-heart', rarity: 'epic' },
      { id: 2, name: 'Velocista', icon: 'fas fa-tachometer-alt', rarity: 'rare' },
      { id: 3, name: 'Persistente', icon: 'fas fa-fire', rarity: 'common' }
    ]);
    
    // Mind Map State
    const centralTopic = ref('Medicina');
    const mindMapBranches = ref([
      {
        id: 1,
        title: 'Anatomia',
        category: 'anatomy',
        children: [
          { id: 11, title: 'Sistema Nervoso' },
          { id: 12, title: 'Sistema Cardiovascular' },
          { id: 13, title: 'Sistema Respiratório' }
        ]
      },
      {
        id: 2,
        title: 'Fisiologia',
        category: 'physiology',
        children: [
          { id: 21, title: 'Metabolismo' },
          { id: 22, title: 'Homeostase' },
          { id: 23, title: 'Neurotransmissão' }
        ]
      },
      {
        id: 3,
        title: 'Patologia',
        category: 'pathology',
        children: [
          { id: 31, title: 'Inflamação' },
          { id: 32, title: 'Neoplasias' },
          { id: 33, title: 'Degeneração' }
        ]
      }
    ]);
    
    const editMode = ref(false);
    const collaborators = ref([]);
    
    // Computed Properties
    const totalNodes = computed(() => {
      return 1 + mindMapBranches.value.reduce((acc, branch) => {
        return acc + 1 + (branch.children ? branch.children.length : 0);
      }, 0);
    });
    
    const totalConnections = computed(() => {
      return mindMapBranches.value.reduce((acc, branch) => {
        return acc + 1 + (branch.children ? branch.children.length : 0);
      }, 0);
    });
    
    const mapDepth = computed(() => {
      return 3; // Central + Branch + Sub-branch
    });
    
    // Methods
    const initializeKnowledgeUniverse = () => {
      // Initialize 3D knowledge universe
      generateKnowledgeNodes();
      if (universeCanvas.value) {
        // Setup Three.js or custom 3D rendering
        render3DUniverse();
      }
    };
    
    const generateKnowledgeNodes = () => {
      const categories = ['anatomy', 'physiology', 'pharmacology', 'pathology', 'clinical'];
      const nodes = [];
      
      for (let i = 0; i < 50; i++) {
        nodes.push({
          id: i,
          name: `Conceito ${i + 1}`,
          category: categories[Math.floor(Math.random() * categories.length)],
          mastery: Math.floor(Math.random() * 100),
          x: Math.random() * 800 - 400,
          y: Math.random() * 600 - 300,
          z: Math.random() * 400 - 200,
          connections: generateConnections(i),
          lastReview: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
        });
      }
      
      knowledgeNodes.value = nodes;
    };
    
    const generateConnections = (nodeId) => {
      const connections = [];
      const numConnections = Math.floor(Math.random() * 5) + 1;
      
      for (let i = 0; i < numConnections; i++) {
        connections.push({
          id: Math.floor(Math.random() * 50),
          name: `Conceito ${Math.floor(Math.random() * 50) + 1}`,
          strength: Math.floor(Math.random() * 100)
        });
      }
      
      return connections;
    };
    
    const render3DUniverse = () => {
      // Placeholder for 3D rendering logic
      // In a real implementation, this would use Three.js
      console.log('Rendering 3D universe...');
    };
    
    const resetView = () => {
      zoomLevel.value = 1;
      // Reset camera position
    };
    
    const toggleRotation = () => {
      autoRotate.value = !autoRotate.value;
    };
    
    const toggleLabels = () => {
      showLabels.value = !showLabels.value;
    };
    
    const toggleConnections = () => {
      showConnections.value = !showConnections.value;
    };
    
    const zoomIn = () => {
      zoomLevel.value = Math.min(3, zoomLevel.value + 0.2);
    };
    
    const zoomOut = () => {
      zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.2);
    };
    
    const selectNodeById = (id) => {
      const node = knowledgeNodes.value.find(n => n.id === id);
      if (node) {
        selectedNode.value = node;
      }
    };
    
    const formatDate = (date) => {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).format(date);
    };
    
    const studyNode = (node) => {
      console.log('Studying node:', node);
      // Navigate to study page or open study modal
    };
    
    const reviewNode = (node) => {
      console.log('Reviewing node:', node);
      // Start review session
    };
    
    // Neural Pathways Methods
    const initializeNeuralNetwork = () => {
      generateNeuralConnections();
      generateSynapticPulses();
    };
    
    const generateNeuralConnections = () => {
      const connections = [];
      
      // Connect input to hidden1
      for (let i = 1; i <= 5; i++) {
        for (let j = 1; j <= 7; j++) {
          connections.push({
            id: `input${i}-hidden1${j}`,
            x1: 100,
            y1: 50 + i * 100,
            x2: 300,
            y2: 30 + j * 80,
            weight: Math.random(),
            active: false,
            firing: false
          });
        }
      }
      
      // Connect hidden1 to hidden2
      for (let i = 1; i <= 7; i++) {
        for (let j = 1; j <= 7; j++) {
          connections.push({
            id: `hidden1${i}-hidden2${j}`,
            x1: 300,
            y1: 30 + i * 80,
            x2: 500,
            y2: 30 + j * 80,
            weight: Math.random(),
            active: false,
            firing: false
          });
        }
      }
      
      // Connect hidden2 to output
      for (let i = 1; i <= 7; i++) {
        for (let j = 1; j <= 5; j++) {
          connections.push({
            id: `hidden2${i}-output${j}`,
            x1: 500,
            y1: 30 + i * 80,
            x2: 700,
            y2: 50 + j * 100,
            weight: Math.random(),
            active: false,
            firing: false
          });
        }
      }
      
      neuralConnections.value = connections;
    };
    
    const generateSynapticPulses = () => {
      const pulses = [];
      
      for (let i = 0; i < 10; i++) {
        pulses.push({
          id: i,
          x: 100,
          y: 150,
          duration: 2 + Math.random() * 3,
          pathId: Math.floor(Math.random() * neuralConnections.value.length)
        });
      }
      
      synapticPulses.value = pulses;
    };
    
    const activateNeuron = (neuronId) => {
      const index = activeNeurons.value.indexOf(neuronId);
      if (index > -1) {
        activeNeurons.value.splice(index, 1);
      } else {
        activeNeurons.value.push(neuronId);
      }
      
      updateNetworkActivity();
    };
    
    const updateNetworkActivity = () => {
      activeSynapses.value = Math.floor(activeNeurons.value.length * 3.5);
      networkEfficiency.value = Math.min(95, 70 + activeNeurons.value.length * 2);
    };
    
    const startSimulation = () => {
      console.log('Starting neural simulation...');
      // Implement neural network simulation
    };
    
    const resetSimulation = () => {
      activeNeurons.value = [];
      activeSynapses.value = 0;
      networkEfficiency.value = 85;
    };
    
    const trainNetwork = () => {
      console.log('Training neural network...');
      // Implement training logic
    };
    
    // VR Environment Methods
    const toggleVRMode = () => {
      vrMode.value = !vrMode.value;
      if (vrMode.value) {
        enterVRMode();
      } else {
        exitVRMode();
      }
    };
    
    const enterVRMode = () => {
      // Request fullscreen
      if (vrContainer.value && vrContainer.value.requestFullscreen) {
        vrContainer.value.requestFullscreen();
      }
    };
    
    const exitVRMode = () => {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    };
    
    const rotateView = (direction) => {
      console.log('Rotating view:', direction);
      // Implement view rotation
    };
    
    const selectConcept = (concept) => {
      selectedConcept.value = concept;
    };
    
    const updateEnvironment = () => {
      console.log('Updating environment theme:', environmentTheme.value);
      // Apply environment theme changes
    };
    
    const updateMusic = () => {
      console.log('Updating ambient music:', ambientMusic.value);
      // Change background music
    };
    
    const updateLighting = () => {
      console.log('Updating lighting:', lightingIntensity.value, lightingColor.value);
      // Apply lighting changes
    };
    
    // Mind Map Methods
    const getBranchStyle = (index) => {
      const angle = (index / mindMapBranches.value.length) * 360;
      const radius = 200;
      const x = Math.cos(angle * Math.PI / 180) * radius;
      const y = Math.sin(angle * Math.PI / 180) * radius;
      
      return {
        transform: `translate(${x}px, ${y}px)`
      };
    };
    
    const getSubBranchStyle = (parentIndex, index) => {
      const parentAngle = (parentIndex / mindMapBranches.value.length) * 360;
      const offset = (index - 1) * 30 - 30;
      const angle = parentAngle + offset;
      const radius = 100;
      const x = Math.cos(angle * Math.PI / 180) * radius;
      const y = Math.sin(angle * Math.PI / 180) * radius;
      
      return {
        transform: `translate(${x}px, ${y}px)`
      };
    };
    
    const addBranch = () => {
      const newBranch = {
        id: Date.now(),
        title: 'Novo Ramo',
        category: 'custom',
        children: []
      };
      mindMapBranches.value.push(newBranch);
    };
    
    const exportMindMap = () => {
      console.log('Exporting mind map...');
      // Export as image or data file
    };
    
    const shareMindMap = () => {
      console.log('Sharing mind map...');
      // Share functionality
    };
    
    // Lifecycle
    onMounted(() => {
      initializeKnowledgeUniverse();
      initializeNeuralNetwork();
      
      // Start animations
      animateUniverse();
      animateNeuralPulses();
    });
    
    const animateUniverse = () => {
      if (autoRotate.value) {
        // Rotate universe
      }
      requestAnimationFrame(animateUniverse);
    };
    
    const animateNeuralPulses = () => {
      // Animate synaptic pulses
      requestAnimationFrame(animateNeuralPulses);
    };
    
    onUnmounted(() => {
      // Cleanup
      if (vrMode.value) {
        exitVRMode();
      }
    });
    
    return {
      // Refs
      universeContainer,
      universeCanvas,
      pathwayCanvas,
      vrContainer,
      mindmapCanvas,
      
      // Knowledge Universe
      knowledgeNodes,
      selectedNode,
      autoRotate,
      showLabels,
      showConnections,
      zoomLevel,
      resetView,
      toggleRotation,
      toggleLabels,
      toggleConnections,
      zoomIn,
      zoomOut,
      selectNodeById,
      formatDate,
      studyNode,
      reviewNode,
      
      // Neural Pathways
      activeNeurons,
      neuralConnections,
      synapticPulses,
      propagationSpeed,
      connectionStrength,
      learningRate,
      activeSynapses,
      networkEfficiency,
      activateNeuron,
      startSimulation,
      resetSimulation,
      trainNetwork,
      
      // VR Environment
      vrMode,
      currentTopic,
      topicConcepts,
      selectedConcept,
      dailyProgress,
      recentBadges,
      environmentTheme,
      ambientMusic,
      lightingIntensity,
      lightingColor,
      effects,
      toggleVRMode,
      rotateView,
      selectConcept,
      updateEnvironment,
      updateMusic,
      updateLighting,
      
      // Mind Map
      centralTopic,
      mindMapBranches,
      editMode,
      collaborators,
      totalNodes,
      totalConnections,
      mapDepth,
      getBranchStyle,
      getSubBranchStyle,
      addBranch,
      exportMindMap,
      shareMindMap
    };
  }
};
</script>

<style scoped>
.visualizer-3d-container {
  padding: 2rem;
  background: var(--background-color);
  color: var(--text-color);
}

/* Knowledge Universe Styles */
.knowledge-universe {
  margin-bottom: 3rem;
}

.universe-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #8b5cf6;
}

.universe-viewport {
  position: relative;
  background: linear-gradient(135deg, #1a1a2e, #0f0f1e);
  border-radius: 20px;
  padding: 2rem;
  min-height: 600px;
  overflow: hidden;
}

.universe-viewport canvas {
  width: 100%;
  height: 500px;
  border-radius: 10px;
  background: radial-gradient(ellipse at center, #2a2a4e 0%, #0a0a1e 100%);
}

.universe-controls {
  position: absolute;
  top: 1rem;
  left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.control-panel {
  display: flex;
  gap: 0.5rem;
  background: rgba(0, 0, 0, 0.6);
  padding: 0.5rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.control-btn {
  width: 40px;
  height: 40px;
  background: rgba(139, 92, 246, 0.2);
  border: 1px solid rgba(139, 92, 246, 0.5);
  border-radius: 8px;
  color: #e0e0e0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(139, 92, 246, 0.3);
  transform: translateY(-2px);
}

.control-btn.active {
  background: #8b5cf6;
  color: white;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 0, 0, 0.6);
  padding: 0.5rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.zoom-btn {
  width: 30px;
  height: 30px;
  background: rgba(139, 92, 246, 0.2);
  border: 1px solid rgba(139, 92, 246, 0.5);
  border-radius: 50%;
  color: #e0e0e0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.zoom-slider {
  width: 100px;
  height: 4px;
  -webkit-appearance: none;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
}

.zoom-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  background: #8b5cf6;
  border-radius: 50%;
  cursor: pointer;
}

.node-info-panel {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 300px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(139, 92, 246, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.panel-header h4 {
  margin: 0;
  color: #8b5cf6;
}

.close-btn {
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  color: #e0e0e0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.info-section {
  margin-bottom: 1rem;
}

.info-section label {
  display: block;
  font-size: 0.9rem;
  color: #a0a0a0;
  margin-bottom: 0.5rem;
}

.mastery-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.mastery-fill {
  height: 100%;
  background: linear-gradient(90deg, #8b5cf6, #ec4899);
  transition: width 0.5s ease;
}

.mastery-text {
  font-size: 0.9rem;
  color: #e0e0e0;
}

.connections-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 150px;
  overflow-y: auto;
}

.connection-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.connection-item:hover {
  background: rgba(139, 92, 246, 0.2);
}

.connection-item i {
  color: #8b5cf6;
  font-size: 0.8rem;
}

.connection-item span {
  flex: 1;
  font-size: 0.9rem;
}

.strength {
  font-size: 0.8rem;
  color: #a0a0a0;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.action-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #8b5cf6;
  color: white;
}

.action-btn.secondary {
  background: rgba(139, 92, 246, 0.2);
  color: #8b5cf6;
  border: 1px solid #8b5cf6;
}

.action-btn:hover {
  transform: translateY(-2px);
}

.universe-legend {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  background: rgba(0, 0, 0, 0.6);
  padding: 1rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.universe-legend h5 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: #e0e0e0;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

/* Neural Pathways Styles */
.neural-pathways {
  margin-bottom: 3rem;
}

.pathways-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #3b82f6;
}

.pathways-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  background: var(--card-background);
  border-radius: 20px;
  padding: 2rem;
}

.pathway-visualization {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 15px;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.neural-svg {
  width: 100%;
  height: 100%;
}

.layer-label {
  fill: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.neuron {
  fill: rgba(59, 130, 246, 0.3);
  stroke: #3b82f6;
  stroke-width: 2;
  cursor: pointer;
  transition: all 0.3s ease;
}

.neuron:hover {
  fill: rgba(59, 130, 246, 0.5);
  transform: scale(1.1);
}

.neuron.active {
  fill: #3b82f6;
  filter: drop-shadow(0 0 10px #3b82f6);
}

.input-neuron {
  fill: rgba(139, 92, 246, 0.3);
  stroke: #8b5cf6;
}

.hidden-neuron {
  fill: rgba(16, 185, 129, 0.3);
  stroke: #10b981;
}

.output-neuron {
  fill: rgba(245, 158, 11, 0.3);
  stroke: #f59e0b;
}

.neural-connection {
  stroke: rgba(255, 255, 255, 0.2);
  fill: none;
  transition: all 0.3s ease;
}

.neural-connection.active {
  stroke: rgba(59, 130, 246, 0.6);
}

.neural-connection.firing {
  stroke: #3b82f6;
  filter: drop-shadow(0 0 5px #3b82f6);
  animation: pulse-connection 1s ease-in-out infinite;
}

@keyframes pulse-connection {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.synaptic-pulse {
  fill: #fbbf24;
  filter: drop-shadow(0 0 5px #fbbf24);
}

.pathway-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.pathway-controls h4 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--primary-color);
}

.simulation-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.control-group {
  display: grid;
  grid-template-columns: 1fr auto auto;
  align-items: center;
  gap: 1rem;
}

.control-group label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.control-group input[type="range"] {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
}

.control-group span {
  font-weight: 500;
  color: #3b82f6;
  min-width: 50px;
  text-align: right;
}

.simulation-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.sim-btn {
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.sim-btn.primary {
  background: #3b82f6;
  color: white;
}

.sim-btn.secondary {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.sim-btn.accent {
  background: linear-gradient(135deg, #8b5cf6, #ec4899);
  color: white;
}

.sim-btn:hover {
  transform: translateY(-2px);
}

.network-stats {
  display: grid;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.stat-item span {
  font-weight: bold;
  color: #3b82f6;
}

/* VR Environment Styles */
.immersive-environment {
  margin-bottom: 3rem;
}

.environment-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #10b981;
}

.environment-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  background: var(--card-background);
  border-radius: 20px;
  padding: 2rem;
}

.vr-viewport {
  position: relative;
  background: #000;
  border-radius: 15px;
  overflow: hidden;
  height: 500px;
}

.vr-scene {
  width: 100%;
  height: 100%;
  perspective: 1000px;
  transition: all 0.5s ease;
}

.vr-scene.active {
  transform: scale(1.1);
}

.room-3d {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transform: rotateX(-20deg) rotateY(-30deg);
  transition: transform 0.5s ease;
}

.wall {
  position: absolute;
  background: rgba(30, 30, 40, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.wall-back {
  width: 100%;
  height: 100%;
  transform: translateZ(-200px);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.1));
}

.wall-left {
  width: 400px;
  height: 100%;
  left: 0;
  transform: rotateY(90deg) translateZ(-200px);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1));
}

.wall-right {
  width: 400px;
  height: 100%;
  right: 0;
  transform: rotateY(-90deg) translateZ(-200px);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(239, 68, 68, 0.1));
}

.floor {
  width: 100%;
  height: 400px;
  bottom: 0;
  transform: rotateX(90deg) translateZ(-200px);
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.2), rgba(0, 0, 0, 0.5));
}

.ceiling {
  width: 100%;
  height: 400px;
  top: 0;
  transform: rotateX(-90deg) translateZ(-200px);
  background: rgba(0, 0, 0, 0.8);
}

.knowledge-board {
  width: 80%;
  height: 80%;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 15px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.knowledge-board h4 {
  margin: 0 0 1.5rem 0;
  text-align: center;
  color: #8b5cf6;
  font-size: 1.5rem;
}

.board-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  height: calc(100% - 3rem);
}

.concept-card-3d {
  position: relative;
  width: 100%;
  height: 120px;
  transform-style: preserve-3d;
  transition: transform 0.5s ease;
  cursor: pointer;
}

.concept-card-3d:hover {
  transform: rotateY(180deg);
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(139, 92, 246, 0.2);
  border: 1px solid #8b5cf6;
  border-radius: 10px;
}

.card-front {
  z-index: 2;
}

.card-back {
  transform: rotateY(180deg);
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
}

.card-face i {
  font-size: 2rem;
  color: #8b5cf6;
}

.card-face span {
  font-size: 0.9rem;
  text-align: center;
}

.card-face p {
  font-size: 0.8rem;
  text-align: center;
  margin: 0;
  color: #e0e0e0;
}

.progress-display,
.achievements-display {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 10px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.progress-display h5,
.achievements-display h5 {
  margin: 0 0 1rem 0;
  text-align: center;
  color: var(--primary-color);
}

.progress-3d {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.progress-bar-3d {
  width: 50px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  position: relative;
  overflow: hidden;
}

.progress-fill-3d {
  position: absolute;
  bottom: 0;
  width: 100%;
  background: linear-gradient(to top, #10b981, #3b82f6);
  border-radius: 25px;
  transition: height 0.5s ease;
}

.progress-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #10b981;
}

.achievement-badges {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.badge-3d {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  border: 1px solid;
  transition: all 0.3s ease;
}

.badge-3d.common {
  border-color: #94a3b8;
}

.badge-3d.rare {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.badge-3d.epic {
  border-color: #8b5cf6;
  background: rgba(139, 92, 246, 0.1);
}

.badge-3d i {
  font-size: 1.2rem;
}

.badge-3d span {
  font-size: 0.9rem;
}

.study-area {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.holographic-desk {
  width: 300px;
  height: 200px;
  background: rgba(59, 130, 246, 0.1);
  border: 2px solid #3b82f6;
  border-radius: 10px;
  position: relative;
  transform: rotateX(-30deg);
}

.holo-display {
  position: absolute;
  top: -100px;
  left: 50%;
  transform: translateX(-50%);
  width: 250px;
  height: 150px;
  background: rgba(139, 92, 246, 0.2);
  border: 1px solid #8b5cf6;
  border-radius: 10px;
  padding: 1rem;
  backdrop-filter: blur(10px);
}

.holo-content h4 {
  margin: 0 0 0.5rem 0;
  text-align: center;
  color: #8b5cf6;
  font-size: 1rem;
}

.holo-visualization {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.placeholder-3d-model {
  width: 60px;
  height: 60px;
  background: rgba(139, 92, 246, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.model-icon {
  font-size: 2rem;
  color: #8b5cf6;
}

.holo-content p {
  font-size: 0.8rem;
  text-align: center;
  margin: 0;
  color: #e0e0e0;
}

.ambient-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle-3d {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #fff;
  border-radius: 50%;
  opacity: 0.5;
  animation: float-particle 10s linear infinite;
}

.particle-3d:nth-child(odd) {
  animation-duration: 15s;
}

.particle-3d:nth-child(even) {
  animation-duration: 20s;
}

@keyframes float-particle {
  from {
    transform: translateY(100%) translateX(0);
  }
  to {
    transform: translateY(-100%) translateX(100px);
  }
}

.vr-controls {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.vr-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(16, 185, 129, 0.2);
  border: 2px solid #10b981;
  border-radius: 8px;
  color: #10b981;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.vr-btn.active {
  background: #10b981;
  color: white;
}

.vr-btn:hover {
  transform: translateY(-2px);
}

.view-controls {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 0.25rem;
  background: rgba(0, 0, 0, 0.6);
  padding: 0.5rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.view-btn {
  width: 30px;
  height: 30px;
  background: rgba(16, 185, 129, 0.2);
  border: 1px solid #10b981;
  border-radius: 4px;
  color: #10b981;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.view-btn:nth-child(1) { grid-column: 1; grid-row: 2; }
.view-btn:nth-child(2) { grid-column: 2; grid-row: 1; }
.view-btn:nth-child(3) { grid-column: 2; grid-row: 3; }
.view-btn:nth-child(4) { grid-column: 3; grid-row: 2; }

.view-btn:hover {
  background: rgba(16, 185, 129, 0.3);
}

.environment-settings {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.environment-settings h4 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--primary-color);
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-group label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.setting-group select {
  padding: 0.5rem;
  background: var(--background-color);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: var(--text-color);
  cursor: pointer;
}

.lighting-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.lighting-controls input[type="range"] {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.lighting-controls input[type="color"] {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.effects-toggles {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.toggle-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.toggle-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

/* Mind Map 3D Styles */
.mind-map-3d {
  margin-bottom: 3rem;
}

.mindmap-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #f59e0b;
}

.mindmap-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  background: var(--card-background);
  border-radius: 20px;
  padding: 2rem;
}

.mindmap-viewport {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(239, 68, 68, 0.1));
  border-radius: 15px;
  padding: 2rem;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.mindmap-placeholder {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.central-node {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.node-sphere {
  width: 120px;
  height: 120px;
  background: radial-gradient(circle at 30% 30%, #f59e0b, #dc2626);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
  box-shadow: 0 0 30px rgba(245, 158, 11, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.node-sphere:hover {
  transform: scale(1.1);
  box-shadow: 0 0 40px rgba(245, 158, 11, 0.7);
}

.branch-nodes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.branch-node {
  position: absolute;
  top: 50%;
  left: 50%;
  transition: all 0.5s ease;
}

.branch-node .node-sphere {
  width: 80px;
  height: 80px;
  font-size: 0.9rem;
}

.branch-node.anatomy .node-sphere {
  background: radial-gradient(circle at 30% 30%, #8b5cf6, #7c3aed);
}

.branch-node.physiology .node-sphere {
  background: radial-gradient(circle at 30% 30%, #3b82f6, #2563eb);
}

.branch-node.pathology .node-sphere {
  background: radial-gradient(circle at 30% 30%, #ef4444, #dc2626);
}

.sub-branches {
  position: absolute;
  width: 200px;
  height: 200px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.sub-branch {
  position: absolute;
  transition: all 0.5s ease;
}

.sub-node {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 0.7rem;
  padding: 0.5rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.sub-node:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.mindmap-tools {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.mindmap-tools h4 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--primary-color);
}

.tool-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.tool-btn {
  padding: 0.75rem;
  background: rgba(245, 158, 11, 0.2);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  color: #f59e0b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.tool-btn.active {
  background: #f59e0b;
  color: white;
}

.tool-btn:hover {
  transform: translateY(-2px);
}

.mindmap-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding: 1rem;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 10px;
}

.stat {
  text-align: center;
}

.stat label {
  display: block;
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.stat span {
  font-size: 1.2rem;
  font-weight: bold;
  color: #f59e0b;
}

.collaboration-info h5 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: var(--text-secondary);
}

.collaborator-list {
  display: flex;
  gap: 0.5rem;
}

.collaborator-avatar {
  position: relative;
  width: 40px;
  height: 40px;
}

.collaborator-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid var(--background-color);
}

.status-dot {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid var(--background-color);
}

.status-dot.online {
  background: #10b981;
}

.status-dot.busy {
  background: #f59e0b;
}

.status-dot.away {
  background: #94a3b8;
}

/* Transitions */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateX(20px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .pathways-container,
  .environment-container,
  .mindmap-container {
    grid-template-columns: 1fr;
  }
  
  .vr-viewport {
    height: 400px;
  }
  
  .tool-buttons {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .universe-controls {
    position: static;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 1rem;
  }
  
  .node-info-panel {
    position: static;
    width: 100%;
    margin-top: 1rem;
  }
  
  .universe-legend {
    position: static;
    margin-top: 1rem;
  }
  
  .board-content {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .mindmap-stats {
    grid-template-columns: 1fr;
  }
}

/* Dark Theme Adjustments */
:root[data-theme="dark"] {
  --card-background: rgba(30, 30, 30, 0.8);
}

/* Light Theme Adjustments */
:root[data-theme="light"] {
  --card-background: rgba(255, 255, 255, 0.9);
  
  .universe-viewport {
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  }
  
  .node-info-panel {
    background: rgba(255, 255, 255, 0.95);
    color: #333;
  }
  
  .control-btn,
  .zoom-btn {
    background: rgba(139, 92, 246, 0.1);
    color: #333;
  }
}
</style>