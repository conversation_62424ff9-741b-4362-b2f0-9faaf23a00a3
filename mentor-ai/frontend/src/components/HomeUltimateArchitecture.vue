<template>
  <div class="ultimate-architecture" @mousemove="handleMouseMove">
    <!-- Ambiente 3D de Realidade Aumentada Virtual -->
    <div class="ar-environment">
      <!-- Sistema de Partículas Quânticas -->
      <div class="quantum-particles">
        <div 
          v-for="i in 50" 
          :key="`particle-${i}`" 
          class="quantum-particle"
          :style="{ 
            '--delay': Math.random() * 10 + 's',
            '--duration': Math.random() * 20 + 10 + 's',
            '--x': Math.random() * 100 + '%',
            '--y': Math.random() * 100 + '%'
          }"
        ></div>
      </div>

      <!-- Grid Neural de Fundo -->
      <svg class="neural-grid" viewBox="0 0 1920 1080">
        <defs>
          <pattern id="neural-pattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
            <circle cx="25" cy="25" r="1" fill="rgba(102, 126, 234, 0.3)" />
          </pattern>
          <radialGradient id="neural-glow">
            <stop offset="0%" stop-color="rgba(102, 126, 234, 0.5)" />
            <stop offset="100%" stop-color="rgba(102, 126, 234, 0)" />
          </radialGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#neural-pattern)" />
        <circle 
          v-for="connection in neuralConnections" 
          :key="connection.id"
          :cx="connection.x" 
          :cy="connection.y" 
          r="50"
          fill="url(#neural-glow)"
          class="neural-pulse"
        />
      </svg>
    </div>

    <!-- Centro de Comando Espacial -->
    <div class="space-command-center">
      <!-- Avatar Holográfico 3D -->
      <div class="holographic-avatar">
        <div class="hologram-container">
          <div class="hologram-base">
            <div class="hologram-rings">
              <div class="ring ring-1"></div>
              <div class="ring ring-2"></div>
              <div class="ring ring-3"></div>
            </div>
            <div class="avatar-core">
              <img :src="userAvatar" alt="User Avatar" />
              <div class="neural-aura"></div>
            </div>
            <div class="data-streams">
              <div v-for="i in 8" :key="`stream-${i}`" class="data-stream"></div>
            </div>
          </div>
          <div class="hologram-display">
            <h2 class="user-name">{{ userName }}</h2>
            <div class="neural-stats">
              <div class="stat-orb" v-for="stat in neuralStats" :key="stat.id">
                <i :class="stat.icon"></i>
                <span>{{ stat.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Portal System - Navegação 3D -->
      <div class="portal-system">
        <div 
          v-for="portal in portals" 
          :key="portal.id"
          class="portal-container"
          :style="{ '--rotation': portal.rotation + 'deg' }"
          @click="enterPortal(portal)"
          @mouseenter="activatePortal(portal)"
          @mouseleave="deactivatePortal(portal)"
        >
          <div class="portal" :class="{ active: portal.active }">
            <div class="portal-frame">
              <div class="portal-energy"></div>
              <div class="portal-vortex">
                <div class="vortex-layer" v-for="i in 5" :key="`vortex-${i}`"></div>
              </div>
            </div>
            <div class="portal-content">
              <i :class="portal.icon"></i>
              <h3>{{ portal.name }}</h3>
              <p>{{ portal.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Dashboard Quântico Multidimensional -->
      <div class="quantum-dashboard">
        <div class="dimension-selector">
          <button 
            v-for="dim in dimensions" 
            :key="dim.id"
            @click="currentDimension = dim.id"
            :class="['dim-btn', { active: currentDimension === dim.id }]"
          >
            <i :class="dim.icon"></i>
            {{ dim.name }}
          </button>
        </div>

        <div class="dimension-view" :class="`dimension-${currentDimension}`">
          <!-- Dimensão Temporal -->
          <div v-if="currentDimension === 'temporal'" class="temporal-view">
            <div class="timeline-4d">
              <div class="time-axis past">
                <h4>Passado</h4>
                <div class="time-events">
                  <div v-for="event in pastEvents" :key="event.id" class="time-node">
                    <div class="node-content">
                      <i :class="event.icon"></i>
                      <span>{{ event.title }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="time-axis present">
                <h4>Presente</h4>
                <div class="time-flow">
                  <div class="flow-indicator"></div>
                  <div class="current-activities">
                    <div v-for="activity in currentActivities" :key="activity.id" class="activity-bubble">
                      {{ activity.name }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="time-axis future">
                <h4>Futuro Predito</h4>
                <div class="predictions">
                  <div v-for="pred in predictions" :key="pred.id" class="prediction-card">
                    <div class="prob-meter" :style="{ '--prob': pred.probability + '%' }"></div>
                    <span>{{ pred.event }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Dimensão Conhecimento -->
          <div v-if="currentDimension === 'knowledge'" class="knowledge-view">
            <div class="knowledge-matrix">
              <div class="matrix-3d">
                <div 
                  v-for="node in knowledgeNodes" 
                  :key="node.id"
                  class="knowledge-node"
                  :style="{ 
                    '--x': node.x + 'px', 
                    '--y': node.y + 'px', 
                    '--z': node.z + 'px',
                    '--size': node.size + 'px'
                  }"
                >
                  <div class="node-core">
                    <i :class="node.icon"></i>
                  </div>
                  <div class="node-connections">
                    <svg class="connection-lines">
                      <line 
                        v-for="conn in node.connections" 
                        :key="conn"
                        :x1="50" :y1="50"
                        :x2="getNodePosition(conn).x" 
                        :y2="getNodePosition(conn).y"
                        stroke="rgba(102, 126, 234, 0.3)"
                      />
                    </svg>
                  </div>
                  <div class="node-label">{{ node.label }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Dimensão Performance -->
          <div v-if="currentDimension === 'performance'" class="performance-view">
            <div class="performance-hologram">
              <canvas ref="performanceCanvas"></canvas>
              <div class="perf-metrics">
                <div v-for="metric in performanceMetrics" :key="metric.id" class="metric-hologram">
                  <div class="metric-visual">
                    <div class="metric-ring" :style="{ '--progress': metric.value + '%' }">
                      <div class="ring-fill"></div>
                    </div>
                    <div class="metric-value">{{ metric.value }}%</div>
                  </div>
                  <div class="metric-label">{{ metric.label }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Dimensão Social -->
          <div v-if="currentDimension === 'social'" class="social-view">
            <div class="social-constellation">
              <div class="constellation-center">
                <div class="user-star"></div>
              </div>
              <div 
                v-for="connection in socialConnections" 
                :key="connection.id"
                class="social-star"
                :style="{ 
                  '--angle': connection.angle + 'deg',
                  '--distance': connection.distance + 'px'
                }"
              >
                <img :src="connection.avatar" :alt="connection.name" />
                <div class="connection-line"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Ecosystem Vivo -->
      <div class="living-ecosystem">
        <div class="eco-organisms">
          <div 
            v-for="organism in ecoOrganisms" 
            :key="organism.id"
            class="organism"
            :class="organism.type"
            :style="{ 
              '--x': organism.x + 'px',
              '--y': organism.y + 'px',
              '--color': organism.color
            }"
          >
            <div class="organism-body">
              <div class="nucleus"></div>
              <div class="membrane"></div>
              <div class="energy-field"></div>
            </div>
          </div>
        </div>

        <div class="eco-info">
          <h3>Ecosystem Health</h3>
          <div class="health-indicator">
            <div class="health-bar" :style="{ width: ecosystemHealth + '%' }"></div>
          </div>
          <div class="eco-stats">
            <div class="stat">
              <i class="fas fa-seedling"></i>
              <span>{{ activeOrganisms }} Active</span>
            </div>
            <div class="stat">
              <i class="fas fa-link"></i>
              <span>{{ connections }} Connections</span>
            </div>
          </div>
        </div>
      </div>

      <!-- AI Command Interface -->
      <div class="ai-command-interface">
        <div class="command-input">
          <div class="input-field">
            <span class="prompt">></span>
            <input 
              v-model="commandInput" 
              @keyup.enter="executeCommand"
              placeholder="Digite um comando ou pergunta..."
              class="command-line"
            />
          </div>
          <div class="suggestions" v-if="showSuggestions">
            <div 
              v-for="suggestion in commandSuggestions" 
              :key="suggestion"
              @click="commandInput = suggestion"
              class="suggestion-item"
            >
              {{ suggestion }}
            </div>
          </div>
        </div>

        <div class="command-output" v-if="commandOutput">
          <div class="output-content">
            {{ commandOutput }}
          </div>
        </div>
      </div>

      <!-- Quick Action Pods -->
      <div class="action-pods">
        <div 
          v-for="pod in actionPods" 
          :key="pod.id"
          class="action-pod"
          :class="{ active: pod.active }"
          @click="executePodAction(pod)"
        >
          <div class="pod-core">
            <i :class="pod.icon"></i>
          </div>
          <div class="pod-ring"></div>
          <div class="pod-label">{{ pod.label }}</div>
        </div>
      </div>
    </div>

    <!-- Notification System Holográfico -->
    <div class="holographic-notifications">
      <transition-group name="notification">
        <div 
          v-for="notif in notifications" 
          :key="notif.id"
          class="holo-notification"
          :class="notif.type"
        >
          <div class="notif-hologram">
            <i :class="notif.icon"></i>
            <div class="notif-content">
              <h4>{{ notif.title }}</h4>
              <p>{{ notif.message }}</p>
            </div>
          </div>
        </div>
      </transition-group>
    </div>

    <!-- Menu Radial 3D -->
    <div class="radial-menu-3d" v-if="showRadialMenu">
      <div class="menu-center" @click="toggleRadialMenu">
        <i class="fas fa-atom"></i>
      </div>
      <div 
        v-for="(item, index) in radialMenuItems" 
        :key="item.id"
        class="menu-item-3d"
        :style="{ 
          '--angle': (360 / radialMenuItems.length * index) + 'deg',
          '--delay': index * 0.05 + 's'
        }"
        @click="handleRadialAction(item)"
      >
        <div class="item-sphere">
          <i :class="item.icon"></i>
        </div>
        <div class="item-label">{{ item.label }}</div>
      </div>
    </div>

    <!-- Botão de Ativação do Menu Radial -->
    <button class="radial-menu-trigger" @click="toggleRadialMenu">
      <div class="trigger-icon">
        <i class="fas fa-cube"></i>
      </div>
    </button>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import Chart from 'chart.js/auto'

export default {
  name: 'HomeUltimateArchitecture',

  setup() {
    const router = useRouter()
    const store = useStore()
    const performanceCanvas = ref(null)
    
    // Estados reativos
    const mousePosition = reactive({ x: 0, y: 0 })
    const currentDimension = ref('temporal')
    const commandInput = ref('')
    const commandOutput = ref('')
    const showSuggestions = ref(false)
    const showRadialMenu = ref(false)
    const ecosystemHealth = ref(92)
    const activeOrganisms = ref(24)
    const connections = ref(156)

    // Dados do usuário
    const userName = computed(() => store.state.auth?.user?.name || 'Estudante')
    const userAvatar = computed(() => store.state.auth?.user?.avatar || '/default-avatar.png')

    // Neural Stats
    const neuralStats = ref([
      { id: 1, icon: 'fas fa-brain', value: '89%', label: 'Neural Activity' },
      { id: 2, icon: 'fas fa-bolt', value: '2.4k', label: 'Synapses' },
      { id: 3, icon: 'fas fa-infinity', value: '∞', label: 'Potential' },
      { id: 4, icon: 'fas fa-star', value: 'S+', label: 'Rank' }
    ])

    // Neural Connections
    const neuralConnections = ref([
      { id: 1, x: 200, y: 150 },
      { id: 2, x: 600, y: 300 },
      { id: 3, x: 1000, y: 200 },
      { id: 4, x: 1400, y: 400 },
      { id: 5, x: 800, y: 500 }
    ])

    // Portal System
    const portals = ref([
      {
        id: 1,
        name: 'Centro de IA',
        description: 'Ferramentas de Inteligência Artificial',
        icon: 'fas fa-robot',
        rotation: 0,
        path: '/engines',
        active: false
      },
      {
        id: 2,
        name: 'Área de Estudo',
        description: 'Foco e Produtividade',
        icon: 'fas fa-book-open',
        rotation: 60,
        path: '/pomodoro',
        active: false
      },
      {
        id: 3,
        name: 'FlashcardsAI',
        description: 'Sistema de Memorização',
        icon: 'fas fa-layer-group',
        rotation: 120,
        path: '/ai-tools/flashcards',
        active: false
      },
      {
        id: 4,
        name: 'Performance',
        description: 'Analytics Avançados',
        icon: 'fas fa-chart-line',
        rotation: 180,
        path: '/progress-dashboard',
        active: false
      },
      {
        id: 5,
        name: 'Second Brain',
        description: 'Base de Conhecimento',
        icon: 'fas fa-brain',
        rotation: 240,
        path: '/second-brain',
        active: false
      },
      {
        id: 6,
        name: 'Plano de Estudo',
        description: 'Organização e Metas',
        icon: 'fas fa-tasks',
        rotation: 300,
        path: '/plano-estudo',
        active: false
      }
    ])

    // Dimensions
    const dimensions = ref([
      { id: 'temporal', name: 'Temporal', icon: 'fas fa-clock' },
      { id: 'knowledge', name: 'Conhecimento', icon: 'fas fa-network-wired' },
      { id: 'performance', name: 'Performance', icon: 'fas fa-chart-area' },
      { id: 'social', name: 'Social', icon: 'fas fa-users' }
    ])

    // Timeline 4D Data
    const pastEvents = ref([
      { id: 1, title: '127 cards revisados', icon: 'fas fa-check', time: '2h atrás' },
      { id: 2, title: 'Sessão Pomodoro completa', icon: 'fas fa-clock', time: '4h atrás' },
      { id: 3, title: 'Quiz de Anatomia - 92%', icon: 'fas fa-trophy', time: 'Ontem' }
    ])

    const currentActivities = ref([
      { id: 1, name: 'Estudando Farmacologia' },
      { id: 2, name: '15 cards pendentes' },
      { id: 3, name: 'Streak: 28 dias' }
    ])

    const predictions = ref([
      { id: 1, event: 'Domínio de Cardiologia', probability: 87 },
      { id: 2, event: 'Meta mensal alcançada', probability: 94 },
      { id: 3, event: 'Nível S+ em Anatomia', probability: 78 }
    ])

    // Knowledge Matrix
    const knowledgeNodes = ref([
      { id: 1, label: 'Anatomia', icon: 'fas fa-user', x: 400, y: 200, z: 0, size: 80, connections: [2, 3] },
      { id: 2, label: 'Fisiologia', icon: 'fas fa-heartbeat', x: 600, y: 300, z: 50, size: 70, connections: [1, 4] },
      { id: 3, label: 'Patologia', icon: 'fas fa-virus', x: 300, y: 400, z: -30, size: 60, connections: [1, 5] },
      { id: 4, label: 'Farmacologia', icon: 'fas fa-pills', x: 700, y: 450, z: 20, size: 75, connections: [2, 5] },
      { id: 5, label: 'Clínica', icon: 'fas fa-stethoscope', x: 500, y: 500, z: -50, size: 85, connections: [3, 4] }
    ])

    // Performance Metrics
    const performanceMetrics = ref([
      { id: 1, label: 'Eficiência', value: 89 },
      { id: 2, label: 'Retenção', value: 92 },
      { id: 3, label: 'Velocidade', value: 76 },
      { id: 4, label: 'Precisão', value: 94 }
    ])

    // Social Connections
    const socialConnections = ref([
      { id: 1, name: 'Dr. Silva', avatar: '/avatar1.jpg', angle: 0, distance: 150 },
      { id: 2, name: 'Ana Study', avatar: '/avatar2.jpg', angle: 72, distance: 180 },
      { id: 3, name: 'Carlos Med', avatar: '/avatar3.jpg', angle: 144, distance: 160 },
      { id: 4, name: 'Julia Doc', avatar: '/avatar4.jpg', angle: 216, distance: 170 },
      { id: 5, name: 'Pedro Res', avatar: '/avatar5.jpg', angle: 288, distance: 155 }
    ])

    // Ecosystem Organisms
    const ecoOrganisms = ref([
      { id: 1, type: 'knowledge', x: 100, y: 100, color: '#667eea' },
      { id: 2, type: 'practice', x: 300, y: 200, color: '#48bb78' },
      { id: 3, type: 'memory', x: 500, y: 150, color: '#ed8936' },
      { id: 4, type: 'insight', x: 700, y: 250, color: '#9f7aea' },
      { id: 5, type: 'connection', x: 400, y: 300, color: '#38b2ac' }
    ])

    // Command Suggestions
    const commandSuggestions = ref([
      'Mostrar próximas revisões',
      'Analisar performance semanal',
      'Criar novo deck de flashcards',
      'Iniciar sessão de estudo',
      'Ver insights de aprendizado'
    ])

    // Action Pods
    const actionPods = ref([
      { id: 1, icon: 'fas fa-play', label: 'Iniciar', active: false },
      { id: 2, icon: 'fas fa-brain', label: 'IA', active: true },
      { id: 3, icon: 'fas fa-chart-line', label: 'Stats', active: false },
      { id: 4, icon: 'fas fa-users', label: 'Social', active: false }
    ])

    // Radial Menu Items
    const radialMenuItems = ref([
      { id: 1, icon: 'fas fa-home', label: 'Home' },
      { id: 2, icon: 'fas fa-cog', label: 'Config' },
      { id: 3, icon: 'fas fa-bell', label: 'Notif' },
      { id: 4, icon: 'fas fa-search', label: 'Buscar' },
      { id: 5, icon: 'fas fa-plus', label: 'Novo' },
      { id: 6, icon: 'fas fa-user', label: 'Perfil' }
    ])

    // Notifications
    const notifications = ref([
      {
        id: 1,
        type: 'success',
        icon: 'fas fa-check-circle',
        title: 'Meta Alcançada!',
        message: '100 cards revisados hoje'
      }
    ])

    // Methods
    const handleMouseMove = (e) => {
      mousePosition.x = e.clientX
      mousePosition.y = e.clientY
    }

    const enterPortal = (portal) => {
      router.push(portal.path)
    }

    const activatePortal = (portal) => {
      portal.active = true
    }

    const deactivatePortal = (portal) => {
      portal.active = false
    }

    const getNodePosition = (nodeId) => {
      const node = knowledgeNodes.value.find(n => n.id === nodeId)
      return node ? { x: node.x, y: node.y } : { x: 0, y: 0 }
    }

    const executeCommand = () => {
      // Simular execução de comando
      commandOutput.value = `Executando: ${commandInput.value}`
      setTimeout(() => {
        commandOutput.value = ''
        commandInput.value = ''
      }, 3000)
    }

    const executePodAction = (pod) => {
      pod.active = !pod.active
      // Executar ação do pod
    }

    const toggleRadialMenu = () => {
      showRadialMenu.value = !showRadialMenu.value
    }

    const handleRadialAction = (item) => {
      console.log('Radial action:', item.label)
      showRadialMenu.value = false
    }

    const initPerformanceChart = () => {
      if (!performanceCanvas.value) return

      const ctx = performanceCanvas.value.getContext('2d')
      new Chart(ctx, {
        type: 'radar',
        data: {
          labels: ['Velocidade', 'Precisão', 'Retenção', 'Eficiência', 'Consistência'],
          datasets: [{
            label: 'Performance Atual',
            data: [85, 92, 88, 79, 94],
            borderColor: 'rgba(102, 126, 234, 1)',
            backgroundColor: 'rgba(102, 126, 234, 0.2)',
            pointBackgroundColor: 'rgba(102, 126, 234, 1)',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: 'rgba(102, 126, 234, 1)'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              angleLines: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              pointLabels: {
                color: 'rgba(255, 255, 255, 0.7)'
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.5)',
                backdropColor: 'transparent'
              }
            }
          },
          plugins: {
            legend: {
              display: false
            }
          }
        }
      })
    }

    // Lifecycle
    onMounted(() => {
      // Animar organismos do ecossistema
      const animateOrganisms = () => {
        ecoOrganisms.value.forEach(org => {
          org.x += (Math.random() - 0.5) * 2
          org.y += (Math.random() - 0.5) * 2
          
          // Manter dentro dos limites
          org.x = Math.max(50, Math.min(window.innerWidth - 50, org.x))
          org.y = Math.max(50, Math.min(400, org.y))
        })
      }

      const interval = setInterval(animateOrganisms, 100)

      // Init performance chart quando na dimensão performance
      const checkDimension = () => {
        if (currentDimension.value === 'performance') {
          setTimeout(initPerformanceChart, 300)
        }
      }

      onUnmounted(() => {
        clearInterval(interval)
      })
    })

    return {
      // Refs
      performanceCanvas,
      
      // State
      mousePosition,
      currentDimension,
      commandInput,
      commandOutput,
      showSuggestions,
      showRadialMenu,
      ecosystemHealth,
      activeOrganisms,
      connections,
      
      // Computed
      userName,
      userAvatar,
      
      // Data
      neuralStats,
      neuralConnections,
      portals,
      dimensions,
      pastEvents,
      currentActivities,
      predictions,
      knowledgeNodes,
      performanceMetrics,
      socialConnections,
      ecoOrganisms,
      commandSuggestions,
      actionPods,
      radialMenuItems,
      notifications,
      
      // Methods
      handleMouseMove,
      enterPortal,
      activatePortal,
      deactivatePortal,
      getNodePosition,
      executeCommand,
      executePodAction,
      toggleRadialMenu,
      handleRadialAction
    }
  }
}
</script>

<style scoped>
.ultimate-architecture {
  position: relative;
  min-height: 100vh;
  background: #000411;
  overflow: hidden;
  perspective: 1000px;
}

/* AR Environment */
.ar-environment {
  position: fixed;
  inset: 0;
  pointer-events: none;
}

/* Quantum Particles */
.quantum-particles {
  position: absolute;
  inset: 0;
}

.quantum-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  animation: quantum-float var(--duration) linear infinite;
  animation-delay: var(--delay);
  left: var(--x);
  top: var(--y);
}

@keyframes quantum-float {
  0% {
    transform: translate(0, 0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translate(calc(var(--x) - 50vw), -100vh) scale(1.5);
    opacity: 0;
  }
}

/* Neural Grid */
.neural-grid {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}

.neural-pulse {
  animation: pulse-glow 4s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

/* Space Command Center */
.space-command-center {
  position: relative;
  z-index: 10;
  padding: 2rem;
  pointer-events: auto;
}

/* Holographic Avatar */
.holographic-avatar {
  position: absolute;
  top: 3rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20;
}

.hologram-container {
  position: relative;
  width: 300px;
  height: 400px;
}

.hologram-base {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: hologram-rotate 20s linear infinite;
}

@keyframes hologram-rotate {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.hologram-rings {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ring {
  position: absolute;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  animation: ring-pulse 3s ease-in-out infinite;
}

.ring-1 {
  width: 250px;
  height: 250px;
  animation-delay: 0s;
}

.ring-2 {
  width: 200px;
  height: 200px;
  animation-delay: 1s;
}

.ring-3 {
  width: 150px;
  height: 150px;
  animation-delay: 2s;
}

@keyframes ring-pulse {
  0%, 100% {
    transform: scale(1) rotateX(60deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1) rotateX(60deg);
    opacity: 0.8;
  }
}

.avatar-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 0 50px rgba(102, 126, 234, 0.8);
}

.avatar-core img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.neural-aura {
  position: absolute;
  inset: -20px;
  background: radial-gradient(circle, transparent 40%, rgba(102, 126, 234, 0.4) 70%, transparent 100%);
  animation: aura-pulse 2s ease-in-out infinite;
}

@keyframes aura-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.data-streams {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.data-stream {
  position: absolute;
  width: 1px;
  height: 300px;
  background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.6), transparent);
  transform-origin: center;
  animation: stream-flow 2s linear infinite;
}

.data-stream:nth-child(1) { transform: rotate(0deg); }
.data-stream:nth-child(2) { transform: rotate(45deg); }
.data-stream:nth-child(3) { transform: rotate(90deg); }
.data-stream:nth-child(4) { transform: rotate(135deg); }
.data-stream:nth-child(5) { transform: rotate(180deg); }
.data-stream:nth-child(6) { transform: rotate(225deg); }
.data-stream:nth-child(7) { transform: rotate(270deg); }
.data-stream:nth-child(8) { transform: rotate(315deg); }

@keyframes stream-flow {
  0% {
    transform: rotate(var(--rotation, 0deg)) translateY(-150px);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: rotate(var(--rotation, 0deg)) translateY(150px);
    opacity: 0;
  }
}

.hologram-display {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
}

.user-name {
  font-size: 1.5rem;
  margin: 0;
  text-shadow: 0 0 20px rgba(102, 126, 234, 0.8);
}

.neural-stats {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.stat-orb {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 8px;
}

.stat-orb i {
  font-size: 1.2rem;
  color: #667eea;
}

.stat-orb span {
  font-size: 0.9rem;
  font-weight: bold;
}

/* Portal System */
.portal-system {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  height: 600px;
}

.portal-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(var(--rotation)) translateX(250px) rotate(calc(-1 * var(--rotation)));
  cursor: pointer;
}

.portal {
  position: relative;
  width: 150px;
  height: 150px;
  transition: all 0.3s ease;
}

.portal:hover,
.portal.active {
  transform: scale(1.1);
}

.portal-frame {
  position: absolute;
  inset: 0;
  border: 3px solid rgba(102, 126, 234, 0.5);
  border-radius: 50%;
  overflow: hidden;
}

.portal-energy {
  position: absolute;
  inset: -50%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
  animation: portal-energy 3s linear infinite;
}

@keyframes portal-energy {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.portal-vortex {
  position: absolute;
  inset: 10px;
  border-radius: 50%;
  overflow: hidden;
}

.vortex-layer {
  position: absolute;
  inset: 0;
  background: conic-gradient(from 0deg, transparent, rgba(102, 126, 234, 0.3), transparent);
  animation: vortex-spin 2s linear infinite;
}

.vortex-layer:nth-child(2) { animation-delay: 0.4s; }
.vortex-layer:nth-child(3) { animation-delay: 0.8s; }
.vortex-layer:nth-child(4) { animation-delay: 1.2s; }
.vortex-layer:nth-child(5) { animation-delay: 1.6s; }

@keyframes vortex-spin {
  0% {
    transform: rotate(0deg) scale(1);
  }
  100% {
    transform: rotate(360deg) scale(1.5);
    opacity: 0;
  }
}

.portal-content {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 10;
}

.portal-content i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #667eea;
}

.portal-content h3 {
  font-size: 0.9rem;
  margin: 0;
}

.portal-content p {
  font-size: 0.7rem;
  opacity: 0.7;
  margin: 0.25rem 0 0;
}

/* Quantum Dashboard */
.quantum-dashboard {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  background: rgba(0, 4, 17, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 20px;
  padding: 2rem;
}

.dimension-selector {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.dim-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 25px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dim-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
}

.dim-btn.active {
  background: rgba(102, 126, 234, 0.4);
  border-color: #667eea;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

/* Dimension Views */
.dimension-view {
  min-height: 300px;
  position: relative;
}

/* Temporal View */
.timeline-4d {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 2rem;
  height: 100%;
}

.time-axis {
  padding: 1rem;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.time-axis h4 {
  color: #667eea;
  margin: 0 0 1rem;
  text-align: center;
}

.time-node {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.time-node:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateX(4px);
}

.node-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 0.9rem;
}

.time-flow {
  position: relative;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flow-indicator {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.3), transparent);
  animation: flow-pulse 2s ease-in-out infinite;
}

@keyframes flow-pulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

.current-activities {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.activity-bubble {
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.5);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  color: white;
  font-size: 0.85rem;
  text-align: center;
}

.predictions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.prediction-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  color: white;
  overflow: hidden;
}

.prob-meter {
  position: absolute;
  top: 0;
  left: 0;
  width: var(--prob);
  height: 3px;
  background: linear-gradient(to right, #667eea, #48bb78);
}

/* Knowledge View */
.knowledge-matrix {
  position: relative;
  height: 400px;
  perspective: 1000px;
}

.matrix-3d {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transform: rotateX(-20deg) rotateY(20deg);
}

.knowledge-node {
  position: absolute;
  transform: translate3d(var(--x), var(--y), var(--z));
  transition: all 0.5s ease;
}

.node-core {
  width: var(--size);
  height: var(--size);
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, rgba(102, 126, 234, 0.1) 70%);
  border: 2px solid rgba(102, 126, 234, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: calc(var(--size) * 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.knowledge-node:hover .node-core {
  transform: scale(1.2);
  background: radial-gradient(circle, rgba(102, 126, 234, 0.6) 0%, rgba(102, 126, 234, 0.2) 70%);
}

.node-connections {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.connection-lines {
  position: absolute;
  inset: -500px;
  width: calc(100% + 1000px);
  height: calc(100% + 1000px);
}

.node-label {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 0.8rem;
  white-space: nowrap;
}

/* Performance View */
.performance-hologram {
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.performance-hologram canvas {
  width: 100%;
  max-width: 500px;
  height: 300px;
}

.perf-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

.metric-hologram {
  text-align: center;
}

.metric-visual {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto;
}

.metric-ring {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    #667eea 0deg,
    #667eea calc(var(--progress) * 3.6deg),
    rgba(255, 255, 255, 0.1) calc(var(--progress) * 3.6deg)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.metric-ring::before {
  content: '';
  position: absolute;
  inset: 10px;
  background: #000411;
  border-radius: 50%;
}

.metric-value {
  position: relative;
  z-index: 10;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

.metric-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

/* Social View */
.social-constellation {
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.constellation-center {
  position: relative;
  z-index: 10;
}

.user-star {
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, #667eea 0%, rgba(102, 126, 234, 0.3) 70%);
  border-radius: 50%;
  box-shadow: 0 0 50px rgba(102, 126, 234, 0.8);
}

.social-star {
  position: absolute;
  transform: rotate(var(--angle)) translateX(var(--distance)) rotate(calc(-1 * var(--angle)));
}

.social-star img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid rgba(102, 126, 234, 0.5);
}

.connection-line {
  position: absolute;
  top: 50%;
  left: 50%;
  width: var(--distance);
  height: 1px;
  background: linear-gradient(to right, rgba(102, 126, 234, 0.5), transparent);
  transform-origin: left center;
  transform: rotate(calc(-1 * var(--angle))) translateX(-100%);
}

/* Living Ecosystem */
.living-ecosystem {
  position: absolute;
  top: 2rem;
  right: 2rem;
  width: 300px;
  background: rgba(0, 4, 17, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
}

.eco-organisms {
  position: relative;
  height: 200px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.organism {
  position: absolute;
  transform: translate(var(--x), var(--y));
  transition: all 0.5s ease;
}

.organism-body {
  position: relative;
  width: 30px;
  height: 30px;
}

.nucleus {
  position: absolute;
  inset: 10px;
  background: var(--color);
  border-radius: 50%;
  animation: nucleus-pulse 2s ease-in-out infinite;
}

@keyframes nucleus-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

.membrane {
  position: absolute;
  inset: 5px;
  border: 2px solid var(--color);
  border-radius: 50%;
  opacity: 0.5;
  animation: membrane-rotate 4s linear infinite;
}

@keyframes membrane-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.energy-field {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, transparent 40%, var(--color) 70%, transparent 100%);
  opacity: 0.3;
  animation: field-pulse 3s ease-in-out infinite;
}

@keyframes field-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.6;
  }
}

.eco-info h3 {
  color: white;
  font-size: 1rem;
  margin: 0 0 1rem;
}

.health-indicator {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.health-bar {
  height: 100%;
  background: linear-gradient(to right, #48bb78, #667eea);
  transition: width 0.5s ease;
}

.eco-stats {
  display: flex;
  justify-content: space-between;
}

.eco-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
}

.eco-stats .stat i {
  color: #667eea;
}

/* AI Command Interface */
.ai-command-interface {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 600px;
  background: rgba(0, 4, 17, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 10px;
  padding: 1rem;
  z-index: 100;
}

.command-input {
  position: relative;
}

.input-field {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 0.75rem;
}

.prompt {
  color: #667eea;
  font-weight: bold;
}

.command-line {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  font-family: 'Courier New', monospace;
  font-size: 0.95rem;
  outline: none;
}

.suggestions {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: rgba(0, 4, 17, 0.95);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 6px;
  margin-bottom: 0.5rem;
  overflow: hidden;
}

.suggestion-item {
  padding: 0.5rem 1rem;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-item:hover {
  background: rgba(102, 126, 234, 0.2);
  color: white;
}

.command-output {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #48bb78;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

/* Action Pods */
.action-pods {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.action-pod {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-pod:hover {
  transform: scale(1.1);
}

.pod-core {
  width: 60px;
  height: 60px;
  background: rgba(0, 4, 17, 0.8);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 1.5rem;
  position: relative;
  z-index: 10;
}

.action-pod.active .pod-core {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.pod-ring {
  position: absolute;
  inset: -10px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 50%;
  animation: pod-ring 2s ease-in-out infinite;
}

@keyframes pod-ring {
  0%, 100% {
    transform: scale(1);
    opacity: 0;
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
  }
}

.pod-label {
  position: absolute;
  right: 80px;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  font-size: 0.85rem;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-pod:hover .pod-label {
  opacity: 1;
}

/* Holographic Notifications */
.holographic-notifications {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.holo-notification {
  margin-bottom: 1rem;
  animation: notif-slide-in 0.5s ease;
}

@keyframes notif-slide-in {
  0% {
    transform: translateY(-50px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.notif-hologram {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(0, 4, 17, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(102, 126, 234, 0.5);
  border-radius: 10px;
  box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
}

.notif-hologram i {
  font-size: 1.5rem;
  color: #667eea;
}

.notif-content h4 {
  margin: 0;
  color: white;
  font-size: 0.95rem;
}

.notif-content p {
  margin: 0.25rem 0 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
}

/* Radial Menu 3D */
.radial-menu-trigger {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 70px;
  height: 70px;
  background: rgba(102, 126, 234, 0.2);
  border: 2px solid rgba(102, 126, 234, 0.5);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1000;
}

.radial-menu-trigger:hover {
  background: rgba(102, 126, 234, 0.3);
  transform: scale(1.1);
}

.trigger-icon {
  font-size: 1.8rem;
  color: white;
  animation: icon-rotate 20s linear infinite;
}

@keyframes icon-rotate {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.radial-menu-3d {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 400px;
  height: 400px;
  pointer-events: none;
  z-index: 999;
}

.menu-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: rgba(102, 126, 234, 0.4);
  border: 2px solid #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  pointer-events: auto;
  z-index: 10;
}

.menu-item-3d {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(var(--angle)) translateX(150px) rotate(calc(-1 * var(--angle)));
  pointer-events: auto;
  cursor: pointer;
  animation: menu-item-appear 0.5s ease forwards;
  animation-delay: var(--delay);
  opacity: 0;
}

@keyframes menu-item-appear {
  to {
    opacity: 1;
  }
}

.item-sphere {
  width: 60px;
  height: 60px;
  background: rgba(0, 4, 17, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(102, 126, 234, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.menu-item-3d:hover .item-sphere {
  background: rgba(102, 126, 234, 0.3);
  transform: scale(1.2);
  border-color: #667eea;
}

.item-label {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-item-3d:hover .item-label {
  opacity: 1;
}

/* Responsive */
@media (max-width: 1024px) {
  .portal-system {
    width: 400px;
    height: 400px;
  }
  
  .portal-container {
    transform: translate(-50%, -50%) rotate(var(--rotation)) translateX(150px) rotate(calc(-1 * var(--rotation)));
  }
  
  .portal {
    width: 100px;
    height: 100px;
  }
  
  .quantum-dashboard {
    position: relative;
    margin-top: 2rem;
  }
}

@media (max-width: 768px) {
  .space-command-center {
    padding: 1rem;
  }
  
  .holographic-avatar {
    position: relative;
    top: 0;
    margin-bottom: 2rem;
  }
  
  .portal-system {
    position: relative;
    width: 100%;
    height: auto;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .portal-container {
    position: relative;
    transform: none;
  }
  
  .living-ecosystem {
    position: relative;
    width: 100%;
    margin-top: 2rem;
  }
  
  .action-pods {
    position: fixed;
    bottom: 5rem;
    right: 1rem;
    flex-direction: row;
    gap: 1rem;
  }
  
  .ai-command-interface {
    width: calc(100% - 2rem);
    left: 1rem;
    transform: none;
  }
}
</style>