<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="revision-form-modal">
      <div class="modal-header">
        <h2><i class="fas fa-calendar-plus"></i> Nova Revisão</h2>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="modal-body">
        <div class="form-group">
          <label>Título</label>
          <input 
            v-model="formData.title" 
            type="text" 
            class="form-control" 
            placeholder="Ex: Revisão de Anatomia"
            required
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Data</label>
            <input 
              v-model="formData.date" 
              type="date" 
              class="form-control"
              :min="today"
              required
            />
          </div>

          <div class="form-group">
            <label><PERSON><PERSON><PERSON><PERSON></label>
            <div class="time-inputs">
              <input 
                v-model="formData.startTime" 
                type="time" 
                class="form-control"
                required
              />
              <span class="time-separator">até</span>
              <input 
                v-model="formData.endTime" 
                type="time" 
                class="form-control"
                required
              />
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Disciplina</label>
            <select v-model="formData.subject" class="form-control" required>
              <option value="">Selecione...</option>
              <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                {{ subject.name }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label>Tipo de Revisão</label>
            <select v-model="formData.revisionType" class="form-control" required>
              <option value="Teórica">Teórica</option>
              <option value="Prática">Prática</option>
              <option value="Mista">Mista</option>
              <option value="Flashcards">Flashcards</option>
              <option value="Resumo">Resumo</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>Prioridade</label>
          <div class="priority-selector">
            <button 
              v-for="priority in ['Baixa', 'Média', 'Alta']" 
              :key="priority"
              type="button"
              @click="formData.priority = priority"
              :class="{ active: formData.priority === priority }"
              class="priority-btn"
            >
              {{ priority }}
            </button>
          </div>
        </div>

        <div class="form-group">
          <label>Descrição</label>
          <textarea 
            v-model="formData.description" 
            class="form-control" 
            rows="3"
            placeholder="Adicione detalhes sobre a revisão..."
          ></textarea>
        </div>

        <div class="modal-footer">
          <button type="button" @click="$emit('close')" class="btn-secondary">
            Cancelar
          </button>
          <button type="submit" class="btn-primary">
            <i class="fas fa-save"></i> Salvar Revisão
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RevisionFormModal',
  props: {
    subjects: {
      type: Array,
      required: true
    },
    initialDate: {
      type: Date,
      default: () => new Date()
    }
  },
  data() {
    return {
      formData: {
        title: '',
        date: this.initialDate.toISOString().split('T')[0],
        startTime: '09:00',
        endTime: '10:00',
        subject: '',
        revisionType: 'Teórica',
        priority: 'Média',
        description: ''
      }
    };
  },
  computed: {
    today() {
      return new Date().toISOString().split('T')[0];
    }
  },
  methods: {
    handleSubmit() {
      const startDateTime = new Date(`${this.formData.date}T${this.formData.startTime}`);
      const endDateTime = new Date(`${this.formData.date}T${this.formData.endTime}`);
      
      const revision = {
        title: this.formData.title,
        start: startDateTime.toISOString(),
        end: endDateTime.toISOString(),
        subject: this.formData.subject,
        revisionType: this.formData.revisionType,
        priority: this.formData.priority,
        description: this.formData.description,
        isRevision: true,
        progress: 0,
        completed: false
      };
      
      this.$emit('save', revision);
    }
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s;
}

.revision-form-modal {
  background: #1e293b;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.modal-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0;
}

.modal-header h2 i {
  color: #6366f1;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  color: #e4e6eb;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #6366f1;
  background: rgba(30, 41, 59, 0.8);
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-separator {
  color: #64748b;
  font-size: 0.875rem;
}

.priority-selector {
  display: flex;
  gap: 0.5rem;
}

.priority-btn {
  flex: 1;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.priority-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
}

.priority-btn.active {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
  color: #6366f1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.btn-secondary,
.btn-primary {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

.btn-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}
</style>