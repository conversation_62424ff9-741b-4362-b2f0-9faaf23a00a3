<template>
  <div class="unified-resources-container">
    <!-- Hero Section with Animated Background -->
    <div class="hero-section">
      <div class="animated-bg">
        <div class="particle" v-for="n in 20" :key="n"></div>
        <div class="wave wave-1"></div>
        <div class="wave wave-2"></div>
        <div class="wave wave-3"></div>
      </div>
      
      <div class="hero-content">
        <h1 class="hero-title">
          <span class="title-icon">📚</span>
          <span class="title-text">Central de Recursos</span>
          <span class="title-subtitle">Todo o conteúdo educacional em um só lugar</span>
        </h1>
        
        <!-- Search and Filters -->
        <div class="search-section">
          <div class="search-bar">
            <i class="fas fa-search"></i>
            <input 
              v-model="searchQuery" 
              type="text" 
              placeholder="Buscar recursos..."
              @input="filterResources"
            >
            <button v-if="searchQuery" @click="clearSearch" class="clear-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="filter-tags">
            <button 
              v-for="tag in popularTags" 
              :key="tag"
              @click="toggleTag(tag)"
              :class="['tag-btn', { active: selectedTags.includes(tag) }]"
            >
              {{ tag }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Banner -->
    <div class="stats-banner">
      <div class="stat-item">
        <div class="stat-number">{{ totalResources }}</div>
        <div class="stat-label">Recursos</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ totalVideos }}</div>
        <div class="stat-label">Videoaulas</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ totalDocuments }}</div>
        <div class="stat-label">Documentos</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ savedResources }}</div>
        <div class="stat-label">Salvos</div>
      </div>
    </div>

    <!-- Categories Section -->
    <div class="categories-section">
      <h2 class="section-title">Tipos de Recursos</h2>
      <div class="categories-carousel">
        <div 
          v-for="category in categories" 
          :key="category.id"
          @click="selectCategory(category)"
          :class="['category-card', { active: selectedCategory?.id === category.id }]"
        >
          <div class="category-icon" :style="{ background: category.color }">
            <i :class="category.icon"></i>
          </div>
          <h3>{{ category.name }}</h3>
          <p>{{ category.count }} recursos</p>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions-section">
      <button @click="openRequestModal" class="action-card">
        <i class="fas fa-comment-dots"></i>
        <span>Solicitar Recurso</span>
      </button>
      <router-link to="/recursos/favoritos" class="action-card">
        <i class="fas fa-star"></i>
        <span>Favoritos</span>
      </router-link>
      <router-link to="/recursos/recentes" class="action-card">
        <i class="fas fa-clock"></i>
        <span>Recentes</span>
      </router-link>
    </div>

    <!-- Featured Resources -->
    <div class="featured-section" v-if="featuredResources.length">
      <h2 class="section-title">
        <i class="fas fa-star"></i>
        Recursos em Destaque
      </h2>
      <div class="featured-carousel">
        <div 
          v-for="resource in featuredResources" 
          :key="resource.id"
          class="featured-card"
          @click="openResource(resource)"
        >
          <div class="resource-preview">
            <div v-if="resource.type === 'video'" class="video-thumbnail">
              <img :src="resource.thumbnail" :alt="resource.title">
              <div class="video-duration">{{ resource.duration }}</div>
              <div class="play-overlay">
                <i class="fas fa-play-circle"></i>
              </div>
            </div>
            <div v-else class="resource-icon-preview">
              <i :class="getResourceIcon(resource.type)"></i>
            </div>
            <div class="resource-badges">
              <span v-if="resource.isNew" class="badge new">Novo</span>
              <span v-if="resource.isPremium" class="badge premium">Premium</span>
            </div>
          </div>
          <div class="resource-info">
            <h3>{{ resource.title }}</h3>
            <p class="resource-description">{{ resource.description }}</p>
            <div class="resource-meta">
              <span><i :class="getCategoryIcon(resource.type)"></i> {{ getTypeName(resource.type) }}</span>
              <span v-if="resource.size"><i class="fas fa-file"></i> {{ resource.size }}</span>
              <span><i class="fas fa-calendar"></i> {{ formatDate(resource.date) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Resources Grid -->
    <div class="resources-section">
      <div class="section-header">
        <h2 class="section-title">{{ currentSectionTitle }}</h2>
        <div class="view-options">
          <button 
            @click="viewMode = 'grid'"
            :class="['view-btn', { active: viewMode === 'grid' }]"
          >
            <i class="fas fa-th"></i>
          </button>
          <button 
            @click="viewMode = 'list'"
            :class="['view-btn', { active: viewMode === 'list' }]"
          >
            <i class="fas fa-list"></i>
          </button>
        </div>
      </div>

      <div :class="['resources-container', viewMode]">
        <transition-group name="resource-list" tag="div" class="resources-grid">
          <div 
            v-for="resource in filteredResources" 
            :key="resource.id"
            :class="['resource-card', { watched: resource.watched }]"
          >
            <!-- Resource Preview -->
            <div class="resource-preview-container" @click="openResource(resource)">
              <div v-if="resource.type === 'video'" class="video-thumbnail">
                <img :src="resource.thumbnail" :alt="resource.title">
                <div class="video-duration">{{ resource.duration }}</div>
                <div class="play-overlay">
                  <i class="fas fa-play-circle"></i>
                </div>
                <div class="progress-bar" v-if="resource.progress > 0">
                  <div class="progress-fill" :style="{ width: resource.progress + '%' }"></div>
                </div>
              </div>
              <div v-else-if="resource.type === 'image'" class="image-thumbnail">
                <img :src="resource.thumbnail || resource.url" :alt="resource.title">
                <div class="image-overlay">
                  <i class="fas fa-search-plus"></i>
                </div>
              </div>
              <div v-else-if="resource.type === 'audio'" class="audio-thumbnail">
                <div class="audio-waves">
                  <span v-for="i in 20" :key="i" class="wave-bar"></span>
                </div>
                <i class="fas fa-headphones"></i>
                <div class="audio-duration" v-if="resource.duration">{{ resource.duration }}</div>
              </div>
              <div v-else class="file-thumbnail">
                <i :class="getResourceIcon(resource.type)"></i>
                <span class="file-extension">{{ getFileExtension(resource.type) }}</span>
              </div>
            </div>
            
            <!-- Resource Content -->
            <div class="resource-content">
              <h3 @click="openResource(resource)">{{ resource.title }}</h3>
              <p class="resource-description">{{ resource.description }}</p>
              
              <div class="resource-details">
                <div class="resource-meta">
                  <span class="meta-item">
                    <i :class="getCategoryIcon(resource.type)"></i>
                    {{ getTypeName(resource.type) }}
                  </span>
                  <span class="meta-item" v-if="resource.size">
                    <i class="fas fa-file"></i>
                    {{ resource.size }}
                  </span>
                  <span class="meta-item" v-if="resource.professor">
                    <img :src="resource.professor.avatar" :alt="resource.professor.name">
                    {{ resource.professor.name }}
                  </span>
                </div>
                
                <div class="resource-tags" v-if="resource.tags?.length">
                  <span v-for="tag in resource.tags.slice(0, 3)" :key="tag" class="tag">{{ tag }}</span>
                  <span v-if="resource.tags.length > 3" class="tag more">+{{ resource.tags.length - 3 }}</span>
                </div>
              </div>
              
              <div class="resource-footer">
                <div class="resource-stats">
                  <span v-if="resource.views"><i class="fas fa-eye"></i> {{ formatViews(resource.views) }}</span>
                  <span v-if="resource.rating"><i class="fas fa-star"></i> {{ resource.rating }}</span>
                  <span><i class="fas fa-clock"></i> {{ formatDate(resource.date) }}</span>
                </div>
                <div class="resource-actions">
                  <button @click.stop="toggleSave(resource)" :class="['action-btn', { active: resource.saved }]">
                    <i :class="resource.saved ? 'fas fa-bookmark' : 'far fa-bookmark'"></i>
                  </button>
                  <button @click.stop="shareResource(resource)" class="action-btn">
                    <i class="fas fa-share-alt"></i>
                  </button>
                  <button @click.stop="downloadResource(resource)" class="action-btn">
                    <i class="fas fa-download"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </transition-group>
      </div>

      <!-- Load More -->
      <div v-if="hasMoreResources" class="load-more">
        <button @click="loadMore" class="load-more-btn">
          <span v-if="!loading">Carregar mais</span>
          <span v-else><i class="fas fa-spinner fa-spin"></i> Carregando...</span>
        </button>
      </div>
    </div>

    <!-- Resource Modal -->
    <transition name="modal">
      <div v-if="selectedResource" class="resource-modal" @click.self="closeResource">
        <div class="modal-content">
          <button @click="closeResource" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
          
          <!-- Video Player -->
          <div v-if="selectedResource.type === 'video'" class="video-player-container">
            <video 
              ref="videoPlayer"
              :src="selectedResource.url" 
              controls
              @timeupdate="updateProgress"
              @ended="onVideoEnd"
            >
            </video>
          </div>
          
          <!-- Audio Player -->
          <div v-else-if="selectedResource.type === 'audio'" class="audio-player-container">
            <div class="audio-visualizer">
              <span v-for="i in 50" :key="i" class="frequency-bar"></span>
            </div>
            <audio 
              ref="audioPlayer"
              :src="selectedResource.url" 
              controls
              class="audio-element"
            >
            </audio>
          </div>
          
          <!-- Image Viewer -->
          <div v-else-if="selectedResource.type === 'image'" class="image-viewer-container">
            <img :src="selectedResource.url" :alt="selectedResource.title">
            <div class="image-controls">
              <button @click="zoomIn" class="zoom-btn"><i class="fas fa-search-plus"></i></button>
              <button @click="zoomOut" class="zoom-btn"><i class="fas fa-search-minus"></i></button>
              <button @click="resetZoom" class="zoom-btn"><i class="fas fa-compress"></i></button>
            </div>
          </div>
          
          <!-- PDF/Document Viewer -->
          <div v-else-if="['pdf', 'doc'].includes(selectedResource.type)" class="document-viewer-container">
            <iframe :src="selectedResource.url" frameborder="0"></iframe>
          </div>
          
          <!-- Other Files -->
          <div v-else class="file-preview-container">
            <i :class="getResourceIcon(selectedResource.type)"></i>
            <h3>{{ selectedResource.title }}</h3>
            <p>{{ selectedResource.description }}</p>
            <button @click="downloadResource(selectedResource)" class="download-primary-btn">
              <i class="fas fa-download"></i>
              Baixar {{ getTypeName(selectedResource.type) }}
            </button>
          </div>
          
          <!-- Resource Details -->
          <div class="resource-details">
            <h2>{{ selectedResource.title }}</h2>
            <p class="resource-full-description">{{ selectedResource.fullDescription || selectedResource.description }}</p>
            
            <div class="resource-tabs">
              <button 
                v-for="tab in resourceTabs" 
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="['tab-btn', { active: activeTab === tab.id }]"
              >
                {{ tab.label }}
              </button>
            </div>
            
            <div class="tab-content">
              <!-- Info Tab -->
              <div v-if="activeTab === 'info'" class="info-section">
                <div class="info-grid">
                  <div class="info-item">
                    <label>Tipo:</label>
                    <span>{{ getTypeName(selectedResource.type) }}</span>
                  </div>
                  <div class="info-item" v-if="selectedResource.size">
                    <label>Tamanho:</label>
                    <span>{{ selectedResource.size }}</span>
                  </div>
                  <div class="info-item" v-if="selectedResource.professor">
                    <label>Professor:</label>
                    <span>{{ selectedResource.professor.name }}</span>
                  </div>
                  <div class="info-item">
                    <label>Data:</label>
                    <span>{{ formatFullDate(selectedResource.date) }}</span>
                  </div>
                  <div class="info-item" v-if="selectedResource.views">
                    <label>Visualizações:</label>
                    <span>{{ selectedResource.views.toLocaleString() }}</span>
                  </div>
                  <div class="info-item" v-if="selectedResource.rating">
                    <label>Avaliação:</label>
                    <span>{{ selectedResource.rating }} <i class="fas fa-star"></i></span>
                  </div>
                </div>
                <div class="info-tags" v-if="selectedResource.tags?.length">
                  <h4>Tags:</h4>
                  <div class="tags-list">
                    <span v-for="tag in selectedResource.tags" :key="tag" class="tag">{{ tag }}</span>
                  </div>
                </div>
              </div>
              
              <!-- Notes Tab -->
              <div v-if="activeTab === 'notes'" class="notes-section">
                <div class="notes-editor">
                  <textarea 
                    v-model="resourceNotes" 
                    placeholder="Adicione suas anotações aqui..."
                    @input="saveNotes"
                  ></textarea>
                </div>
              </div>
              
              <!-- Related Tab -->
              <div v-if="activeTab === 'related'" class="related-section">
                <div class="related-grid">
                  <div 
                    v-for="related in relatedResources" 
                    :key="related.id"
                    class="related-item"
                    @click="openResource(related)"
                  >
                    <div class="related-icon">
                      <i :class="getResourceIcon(related.type)"></i>
                    </div>
                    <div class="related-info">
                      <h4>{{ related.title }}</h4>
                      <span>{{ getTypeName(related.type) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Request Resource Modal -->
    <transition name="modal">
      <div v-if="showRequestModal" class="request-modal-overlay" @click.self="closeRequestModal">
        <div class="request-modal">
          <div class="modal-header">
            <h2>Solicitar Novo Recurso</h2>
            <button @click="closeRequestModal" class="close-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <p class="modal-description">
              Não encontrou o recurso que precisa? Envie uma solicitação e nossa equipe irá analisar.
            </p>
            
            <div class="form-group">
              <label>Título do Recurso</label>
              <input 
                v-model="requestForm.title" 
                type="text" 
                placeholder="Ex: Atlas de Anatomia Humana"
              >
            </div>
            
            <div class="form-group">
              <label>Descrição</label>
              <textarea 
                v-model="requestForm.description" 
                rows="4"
                placeholder="Descreva o recurso que você está procurando"
              ></textarea>
            </div>
            
            <div class="form-group">
              <label>Tipo de Recurso</label>
              <select v-model="requestForm.type">
                <option value="">Selecione um tipo...</option>
                <option v-for="cat in categories" :key="cat.id" :value="cat.id">
                  {{ cat.name }}
                </option>
              </select>
            </div>
            
            <div class="form-group">
              <label>Urgência</label>
              <select v-model="requestForm.urgency">
                <option value="low">Baixa - Quando possível</option>
                <option value="medium">Média - Nas próximas semanas</option>
                <option value="high">Alta - Nos próximos dias</option>
              </select>
            </div>
          </div>
          
          <div class="modal-footer">
            <button @click="closeRequestModal" class="btn-cancel">Cancelar</button>
            <button @click="submitRequest" :disabled="!canSubmitRequest" class="btn-submit">
              <i class="fas fa-paper-plane"></i>
              Enviar Solicitação
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Floating Study Timer -->
    <div class="floating-timer" v-if="isStudying">
      <div class="timer-display">
        <i class="fas fa-clock"></i>
        <span>{{ formatStudyTime(studyTime) }}</span>
      </div>
      <button @click="toggleStudyTimer" class="timer-btn">
        <i :class="timerRunning ? 'fas fa-pause' : 'fas fa-play'"></i>
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, reactive } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'

export default {
  name: 'UnifiedResourcesPage',
  setup() {
    const store = useStore()
    const router = useRouter()
    const toast = useToast()
    
    // State
    const searchQuery = ref('')
    const selectedTags = ref([])
    const selectedCategory = ref(null)
    const viewMode = ref('grid')
    const loading = ref(false)
    const hasMoreResources = ref(true)
    const currentPage = ref(1)
    const selectedResource = ref(null)
    const activeTab = ref('info')
    const resourceNotes = ref('')
    const showRequestModal = ref(false)
    const zoomLevel = ref(1)
    
    // Study Timer
    const isStudying = ref(false)
    const timerRunning = ref(false)
    const studyTime = ref(0)
    const studyInterval = ref(null)
    
    // Request Form
    const requestForm = reactive({
      title: '',
      description: '',
      type: '',
      urgency: 'medium'
    })
    
    // Data
    const resources = ref([])
    const filteredResources = ref([])
    const featuredResources = ref([])
    const relatedResources = ref([])
    
    // Stats
    const totalResources = ref(1247)
    const totalVideos = ref(487)
    const totalDocuments = ref(342)
    const savedResources = ref(89)
    
    // Categories
    const categories = [
      { id: 'video', name: 'Videoaulas', icon: 'fas fa-video', color: '#8b5cf6', count: 487 },
      { id: 'pdf', name: 'PDFs', icon: 'fas fa-file-pdf', color: '#ef4444', count: 267 },
      { id: 'audio', name: 'Áudios', icon: 'fas fa-headphones', color: '#10b981', count: 134 },
      { id: 'image', name: 'Imagens', icon: 'fas fa-image', color: '#f59e0b', count: 189 },
      { id: 'doc', name: 'Documentos', icon: 'fas fa-file-word', color: '#3b82f6', count: 75 },
      { id: 'zip', name: 'Arquivos', icon: 'fas fa-file-archive', color: '#ec4899', count: 95 }
    ]
    
    // Popular Tags
    const popularTags = ['Anatomia', 'Fisiologia', 'Farmacologia', 'Patologia', 'Residência', 'USMLE', 'Revisão']
    
    // Resource Tabs
    const resourceTabs = [
      { id: 'info', label: 'Informações' },
      { id: 'notes', label: 'Anotações' },
      { id: 'related', label: 'Relacionados' }
    ]
    
    // Computed
    const currentSectionTitle = computed(() => {
      if (selectedCategory.value) {
        return selectedCategory.value.name
      }
      if (searchQuery.value) {
        return `Resultados para "${searchQuery.value}"`
      }
      if (selectedTags.value.length > 0) {
        return `Recursos: ${selectedTags.value.join(', ')}`
      }
      return 'Todos os Recursos'
    })
    
    const canSubmitRequest = computed(() => {
      return requestForm.title.trim() && requestForm.description.trim() && requestForm.type
    })
    
    // Methods
    const initResources = () => {
      resources.value = generateMockResources()
      filteredResources.value = resources.value.slice(0, 12)
      featuredResources.value = resources.value.filter(r => r.featured).slice(0, 3)
    }
    
    const generateMockResources = () => {
      const professors = [
        { name: 'Dr. João Silva', avatar: 'https://i.pravatar.cc/150?img=33' },
        { name: 'Dra. Maria Santos', avatar: 'https://i.pravatar.cc/150?img=32' }
      ]
      
      const resourceTitles = {
        video: ['Anatomia do Sistema Cardiovascular', 'Farmacologia dos Anti-hipertensivos', 'Semiologia Respiratória'],
        pdf: ['Manual de Farmacologia Clínica', 'Atlas de Anatomia Humana', 'Guia de Patologia'],
        audio: ['Podcast: Casos Clínicos', 'Audiobook: Fisiologia Médica', 'Resumo: Cardiologia'],
        image: ['Radiografia Torácica', 'Esquemas de Anatomia', 'Fluxogramas de Tratamento'],
        doc: ['Protocolo de Emergência', 'Roteiro de Estudo', 'Casos Clínicos Comentados'],
        zip: ['Banco de Questões', 'Material Completo - Cardiologia', 'Simulados Residência']
      }
      
      const mockResources = []
      
      categories.forEach(category => {
        const titles = resourceTitles[category.id] || []
        for (let i = 0; i < 20; i++) {
          const title = titles[i % titles.length] + ` - ${i + 1}`
          mockResources.push({
            id: `${category.id}_${i}`,
            type: category.id,
            title,
            description: `Material de estudo completo sobre ${title.toLowerCase()}`,
            fullDescription: `Este recurso aborda de forma completa e detalhada os principais conceitos sobre ${title.toLowerCase()}, incluindo teoria, prática e casos clínicos.`,
            url: category.id === 'video' ? 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4' : '#',
            thumbnail: category.id === 'video' || category.id === 'image' ? `https://picsum.photos/seed/${i}/400/225` : null,
            size: category.id !== 'video' ? `${Math.floor(Math.random() * 50 + 1)} MB` : null,
            duration: category.id === 'video' || category.id === 'audio' ? `${Math.floor(Math.random() * 60 + 10)}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}` : null,
            professor: category.id === 'video' ? professors[i % professors.length] : null,
            views: Math.floor(Math.random() * 10000 + 100),
            rating: (Math.random() * 0.5 + 4.5).toFixed(1),
            date: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
            tags: getRandomTags(),
            progress: category.id === 'video' && Math.random() > 0.7 ? Math.floor(Math.random() * 100) : 0,
            watched: Math.random() > 0.8,
            saved: Math.random() > 0.9,
            featured: Math.random() > 0.95,
            isNew: Math.random() > 0.9,
            isPremium: Math.random() > 0.8
          })
        }
      })
      
      return mockResources.sort(() => Math.random() - 0.5)
    }
    
    const getRandomTags = () => {
      const numTags = Math.floor(Math.random() * 3) + 1
      const tags = []
      const availableTags = [...popularTags]
      
      for (let i = 0; i < numTags; i++) {
        const index = Math.floor(Math.random() * availableTags.length)
        tags.push(availableTags[index])
        availableTags.splice(index, 1)
      }
      
      return tags
    }
    
    const filterResources = () => {
      let filtered = [...resources.value]
      
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(resource => 
          resource.title.toLowerCase().includes(query) ||
          resource.description.toLowerCase().includes(query) ||
          resource.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }
      
      if (selectedCategory.value) {
        filtered = filtered.filter(resource => resource.type === selectedCategory.value.id)
      }
      
      if (selectedTags.value.length > 0) {
        filtered = filtered.filter(resource => 
          selectedTags.value.some(tag => resource.tags.includes(tag))
        )
      }
      
      filteredResources.value = filtered.slice(0, 12)
      currentPage.value = 1
      hasMoreResources.value = filtered.length > 12
    }
    
    const clearSearch = () => {
      searchQuery.value = ''
      filterResources()
    }
    
    const toggleTag = (tag) => {
      const index = selectedTags.value.indexOf(tag)
      if (index > -1) {
        selectedTags.value.splice(index, 1)
      } else {
        selectedTags.value.push(tag)
      }
      filterResources()
    }
    
    const selectCategory = (category) => {
      selectedCategory.value = selectedCategory.value?.id === category.id ? null : category
      filterResources()
    }
    
    const openResource = (resource) => {
      selectedResource.value = resource
      resourceNotes.value = localStorage.getItem(`resource_notes_${resource.id}`) || ''
      relatedResources.value = resources.value
        .filter(r => r.id !== resource.id && r.type === resource.type)
        .slice(0, 6)
      
      if (resource.type === 'video' || resource.type === 'audio') {
        isStudying.value = true
        startStudyTimer()
      }
    }
    
    const closeResource = () => {
      selectedResource.value = null
      activeTab.value = 'info'
      pauseStudyTimer()
      zoomLevel.value = 1
    }
    
    const toggleSave = (resource) => {
      resource.saved = !resource.saved
      if (resource.saved) {
        savedResources.value++
        toast.success('Recurso salvo!')
      } else {
        savedResources.value--
        toast.info('Recurso removido dos salvos')
      }
    }
    
    const shareResource = (resource) => {
      const url = `${window.location.origin}/recurso/${resource.id}`
      navigator.clipboard.writeText(url)
      toast.success('Link copiado!')
    }
    
    const downloadResource = (resource) => {
      toast.info(`Baixando ${resource.title}...`)
      // In a real app, this would trigger the actual download
      if (resource.url && resource.url !== '#') {
        window.open(resource.url, '_blank')
      }
    }
    
    const updateProgress = (event) => {
      if (selectedResource.value && selectedResource.value.type === 'video') {
        const progress = (event.target.currentTime / event.target.duration) * 100
        selectedResource.value.progress = progress
      }
    }
    
    const onVideoEnd = () => {
      if (selectedResource.value && !selectedResource.value.watched) {
        selectedResource.value.watched = true
        toast.success('Parabéns! Você completou este recurso! 🎉')
      }
    }
    
    const saveNotes = () => {
      if (selectedResource.value) {
        localStorage.setItem(`resource_notes_${selectedResource.value.id}`, resourceNotes.value)
      }
    }
    
    const openRequestModal = () => {
      showRequestModal.value = true
    }
    
    const closeRequestModal = () => {
      showRequestModal.value = false
      requestForm.title = ''
      requestForm.description = ''
      requestForm.type = ''
      requestForm.urgency = 'medium'
    }
    
    const submitRequest = () => {
      if (canSubmitRequest.value) {
        toast.success('Solicitação enviada com sucesso!')
        closeRequestModal()
      }
    }
    
    const loadMore = () => {
      loading.value = true
      
      setTimeout(() => {
        const start = currentPage.value * 12
        const end = start + 12
        const moreResources = resources.value.slice(start, end)
        
        filteredResources.value.push(...moreResources)
        currentPage.value++
        hasMoreResources.value = end < resources.value.length
        loading.value = false
      }, 1000)
    }
    
    // Study Timer
    const toggleStudyTimer = () => {
      if (timerRunning.value) {
        pauseStudyTimer()
      } else {
        startStudyTimer()
      }
    }
    
    const startStudyTimer = () => {
      if (!timerRunning.value) {
        timerRunning.value = true
        studyInterval.value = setInterval(() => {
          studyTime.value++
        }, 1000)
      }
    }
    
    const pauseStudyTimer = () => {
      timerRunning.value = false
      if (studyInterval.value) {
        clearInterval(studyInterval.value)
      }
    }
    
    // Image Zoom
    const zoomIn = () => {
      zoomLevel.value = Math.min(zoomLevel.value + 0.25, 3)
    }
    
    const zoomOut = () => {
      zoomLevel.value = Math.max(zoomLevel.value - 0.25, 0.5)
    }
    
    const resetZoom = () => {
      zoomLevel.value = 1
    }
    
    // Utilities
    const getResourceIcon = (type) => {
      const iconMap = {
        video: 'fas fa-video',
        pdf: 'fas fa-file-pdf',
        audio: 'fas fa-headphones',
        image: 'fas fa-image',
        doc: 'fas fa-file-word',
        zip: 'fas fa-file-archive'
      }
      return iconMap[type] || 'fas fa-file'
    }
    
    const getCategoryIcon = (type) => {
      return getResourceIcon(type)
    }
    
    const getTypeName = (type) => {
      const category = categories.find(c => c.id === type)
      return category ? category.name : 'Arquivo'
    }
    
    const getFileExtension = (type) => {
      const extensions = {
        pdf: 'PDF',
        doc: 'DOC',
        zip: 'ZIP'
      }
      return extensions[type] || 'FILE'
    }
    
    const formatViews = (views) => {
      if (views >= 1000) {
        return (views / 1000).toFixed(1) + 'k'
      }
      return views.toString()
    }
    
    const formatDate = (date) => {
      const days = Math.floor((new Date() - date) / (1000 * 60 * 60 * 24))
      if (days === 0) return 'Hoje'
      if (days === 1) return 'Ontem'
      if (days < 7) return `${days} dias atrás`
      if (days < 30) return `${Math.floor(days / 7)} semanas atrás`
      return `${Math.floor(days / 30)} meses atrás`
    }
    
    const formatFullDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: 'long',
        year: 'numeric'
      })
    }
    
    const formatStudyTime = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
      return `${minutes}:${secs.toString().padStart(2, '0')}`
    }
    
    // Lifecycle
    onMounted(() => {
      initResources()
    })
    
    return {
      // State
      searchQuery,
      selectedTags,
      selectedCategory,
      viewMode,
      loading,
      hasMoreResources,
      selectedResource,
      activeTab,
      resourceNotes,
      showRequestModal,
      requestForm,
      zoomLevel,
      
      // Data
      resources,
      filteredResources,
      featuredResources,
      relatedResources,
      categories,
      popularTags,
      resourceTabs,
      
      // Stats
      totalResources,
      totalVideos,
      totalDocuments,
      savedResources,
      
      // Study Timer
      isStudying,
      timerRunning,
      studyTime,
      
      // Computed
      currentSectionTitle,
      canSubmitRequest,
      
      // Methods
      filterResources,
      clearSearch,
      toggleTag,
      selectCategory,
      openResource,
      closeResource,
      toggleSave,
      shareResource,
      downloadResource,
      updateProgress,
      onVideoEnd,
      saveNotes,
      openRequestModal,
      closeRequestModal,
      submitRequest,
      loadMore,
      toggleStudyTimer,
      zoomIn,
      zoomOut,
      resetZoom,
      
      // Utilities
      getResourceIcon,
      getCategoryIcon,
      getTypeName,
      getFileExtension,
      formatViews,
      formatDate,
      formatFullDate,
      formatStudyTime
    }
  }
}
</script>

<style scoped>
/* Container */
.unified-resources-container {
  min-height: 100vh;
  background: #0a0b1e;
  color: #fff;
}

/* Hero Section */
.hero-section {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.animated-bg {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Particles Animation */
.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: float-particle 20s infinite;
}

@keyframes float-particle {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-10vh) rotate(360deg);
    opacity: 0;
  }
}

.particle:nth-child(odd) {
  animation-duration: 25s;
  animation-delay: calc(var(--n) * 1s);
}

/* Waves */
.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%230a0b1e" fill-opacity="1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-size: cover;
  opacity: 0.8;
}

.wave-1 {
  animation: wave 10s linear infinite;
}

.wave-2 {
  animation: wave 15s linear infinite;
  opacity: 0.5;
  height: 120px;
}

.wave-3 {
  animation: wave 20s linear infinite;
  opacity: 0.3;
  height: 140px;
}

@keyframes wave {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Hero Content */
.hero-content {
  position: relative;
  z-index: 10;
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;
  text-align: center;
}

.hero-title {
  margin-bottom: 3rem;
}

.title-icon {
  font-size: 4rem;
  display: block;
  margin-bottom: 1rem;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.title-text {
  font-size: 3rem;
  font-weight: 800;
  display: block;
  margin-bottom: 0.5rem;
}

.title-subtitle {
  font-size: 1.2rem;
  opacity: 0.8;
  display: block;
}

/* Search Section */
.search-section {
  max-width: 600px;
  margin: 0 auto;
}

.search-bar {
  position: relative;
  margin-bottom: 1.5rem;
}

.search-bar input {
  width: 100%;
  padding: 1rem 3rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: white;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.search-bar input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  outline: none;
}

.search-bar input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-bar i {
  position: absolute;
  left: 1.2rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
}

.clear-btn {
  position: absolute;
  right: 1.2rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.3s ease;
}

.clear-btn:hover {
  color: white;
}

/* Filter Tags */
.filter-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.tag-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tag-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-btn.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: white;
}

/* Stats Banner */
.stats-banner {
  background: rgba(255, 255, 255, 0.05);
  padding: 2rem;
  display: flex;
  justify-content: space-around;
  max-width: 1200px;
  margin: -2rem auto 3rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #8b5cf6;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.7;
  margin-top: 0.5rem;
}

/* Categories Section */
.categories-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 3rem;
}

.section-title {
  font-size: 2rem;
  margin-bottom: 2rem;
}

.categories-carousel {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  padding-bottom: 1rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) transparent;
}

.categories-carousel::-webkit-scrollbar {
  height: 6px;
}

.categories-carousel::-webkit-scrollbar-track {
  background: transparent;
}

.categories-carousel::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 3px;
}

.category-card {
  flex: 0 0 180px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.category-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-5px);
}

.category-card.active {
  border-color: #8b5cf6;
  background: rgba(139, 92, 246, 0.1);
}

.category-icon {
  width: 60px;
  height: 60px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: white;
}

.category-card h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.category-card p {
  font-size: 0.9rem;
  opacity: 0.7;
}

/* Quick Actions */
.quick-actions-section {
  max-width: 1200px;
  margin: 0 auto 3rem;
  padding: 0 2rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

/* Featured Section */
.featured-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 3rem;
}

.featured-carousel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.featured-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.featured-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -10px rgba(139, 92, 246, 0.3);
}

/* Resource Preview */
.resource-preview {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.video-thumbnail,
.image-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-thumbnail img,
.image-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.featured-card:hover .video-thumbnail img,
.featured-card:hover .image-thumbnail img {
  transform: scale(1.05);
}

.resource-icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.5);
}

.video-duration {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  padding: 0.3rem 0.6rem;
  border-radius: 5px;
  font-size: 0.85rem;
}

.play-overlay,
.image-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.featured-card:hover .play-overlay,
.featured-card:hover .image-overlay {
  opacity: 1;
}

.play-overlay i,
.image-overlay i {
  font-size: 4rem;
  color: white;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
}

.resource-badges {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  gap: 0.5rem;
}

.badge {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.badge.new {
  background: #10b981;
  color: white;
}

.badge.premium {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
}

/* Resource Info */
.resource-info {
  padding: 1.5rem;
}

.resource-info h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.resource-description {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.resource-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
}

.resource-meta span {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

/* Resources Section */
.resources-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 3rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.view-options {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  padding: 0.5rem 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  color: white;
}

.view-btn.active {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  color: white;
}

/* Resources Grid */
.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

.resources-container.list .resources-grid {
  grid-template-columns: 1fr;
}

.resource-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.resource-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px -10px rgba(139, 92, 246, 0.3);
}

.resource-card.watched {
  opacity: 0.7;
}

.resources-container.list .resource-card {
  display: flex;
  min-height: 200px;
}

/* Resource Preview Container */
.resource-preview-container {
  position: relative;
  cursor: pointer;
}

.resources-container.grid .resource-preview-container {
  height: 200px;
}

.resources-container.list .resource-preview-container {
  flex: 0 0 300px;
  height: 200px;
}

.audio-thumbnail,
.file-thumbnail {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  position: relative;
}

.audio-waves {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-bottom: 1rem;
}

.wave-bar {
  width: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  animation: wave-animation 1.5s ease-in-out infinite;
}

.wave-bar:nth-child(odd) {
  height: 20px;
  animation-delay: 0s;
}

.wave-bar:nth-child(even) {
  height: 30px;
  animation-delay: 0.5s;
}

@keyframes wave-animation {
  0%, 100% {
    height: 10px;
  }
  50% {
    height: 40px;
  }
}

.audio-thumbnail i,
.file-thumbnail i {
  font-size: 3rem;
  color: rgba(255, 255, 255, 0.5);
}

.audio-duration {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  padding: 0.3rem 0.6rem;
  border-radius: 5px;
  font-size: 0.85rem;
}

.file-extension {
  margin-top: 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  opacity: 0.7;
}

.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(0, 0, 0, 0.5);
}

.progress-fill {
  height: 100%;
  background: #8b5cf6;
  transition: width 0.3s ease;
}

/* Resource Content */
.resource-content {
  padding: 1.5rem;
  flex: 1;
}

.resource-content h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.resource-content h3:hover {
  color: #8b5cf6;
}

.resource-details {
  margin-bottom: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
}

.meta-item img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.resource-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.tag {
  padding: 0.2rem 0.6rem;
  background: rgba(139, 92, 246, 0.2);
  border-radius: 12px;
  font-size: 0.75rem;
  color: #a78bfa;
}

.tag.more {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

/* Resource Footer */
.resource-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.resource-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
}

.resource-stats span {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.resource-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.action-btn.active {
  color: #8b5cf6;
}

/* Load More */
.load-more {
  text-align: center;
  margin-top: 3rem;
}

.load-more-btn {
  padding: 1rem 3rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px -10px rgba(102, 126, 234, 0.6);
}

/* Resource Modal */
.resource-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.modal-content {
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  background: #1a1b3a;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Media Players */
.video-player-container {
  aspect-ratio: 16/9;
  background: black;
}

.video-player-container video {
  width: 100%;
  height: 100%;
}

.audio-player-container {
  padding: 3rem;
  background: linear-gradient(135deg, #1e1e2e 0%, #2d2d3f 100%);
  text-align: center;
}

.audio-visualizer {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 100px;
  gap: 3px;
  margin-bottom: 2rem;
}

.frequency-bar {
  width: 4px;
  background: linear-gradient(to top, #8b5cf6, #667eea);
  border-radius: 2px;
  animation: frequency-dance 0.5s ease-in-out infinite alternate;
}

.frequency-bar:nth-child(odd) {
  animation-delay: 0.1s;
  height: 50%;
}

.frequency-bar:nth-child(even) {
  animation-delay: 0.2s;
  height: 70%;
}

@keyframes frequency-dance {
  from {
    transform: scaleY(0.5);
  }
  to {
    transform: scaleY(1);
  }
}

.audio-element {
  width: 100%;
  max-width: 600px;
}

/* Image Viewer */
.image-viewer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  background: #000;
  position: relative;
  overflow: auto;
  max-height: 70vh;
}

.image-viewer-container img {
  max-width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}

.image-controls {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  background: rgba(0, 0, 0, 0.8);
  padding: 0.5rem;
  border-radius: 25px;
}

.zoom-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.zoom-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Document Viewer */
.document-viewer-container {
  width: 100%;
  height: 70vh;
}

.document-viewer-container iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* File Preview */
.file-preview-container {
  padding: 4rem;
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
}

.file-preview-container i {
  font-size: 5rem;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 2rem;
}

.file-preview-container h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.file-preview-container p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
}

.download-primary-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.download-primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px -10px rgba(102, 126, 234, 0.6);
}

/* Resource Details */
.resource-details {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
}

.resource-details h2 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

.resource-full-description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Resource Tabs */
.resource-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
  padding: 1rem 2rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  color: white;
}

.tab-btn.active {
  color: #8b5cf6;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: #8b5cf6;
}

/* Tab Content */
.tab-content {
  min-height: 200px;
}

/* Info Section */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
}

.info-item span {
  font-size: 1rem;
  color: white;
}

.info-tags h4 {
  margin-bottom: 1rem;
}

.tags-list {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Notes Section */
.notes-editor textarea {
  width: 100%;
  min-height: 200px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  font-size: 1rem;
  resize: vertical;
}

.notes-editor textarea:focus {
  outline: none;
  border-color: #8b5cf6;
}

/* Related Section */
.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.related-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.related-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
}

.related-icon {
  width: 50px;
  height: 50px;
  background: rgba(139, 92, 246, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #8b5cf6;
}

.related-info h4 {
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
}

.related-info span {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Request Modal */
.request-modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.request-modal {
  width: 100%;
  max-width: 600px;
  background: #1a1b3a;
  border-radius: 20px;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-description {
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.7);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #8b5cf6;
  background: rgba(255, 255, 255, 0.08);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel,
.btn-submit {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.15);
}

.btn-submit {
  background: #8b5cf6;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-submit:hover:not(:disabled) {
  background: #7c3aed;
  transform: translateY(-2px);
}

.btn-submit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Floating Timer */
.floating-timer {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: #1a1b3a;
  border-radius: 50px;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.timer-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.timer-btn {
  width: 40px;
  height: 40px;
  background: #8b5cf6;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.timer-btn:hover {
  background: #7c3aed;
  transform: scale(1.1);
}

/* Animations */
.resource-list-enter-active,
.resource-list-leave-active {
  transition: all 0.3s ease;
}

.resource-list-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.resource-list-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-from {
  opacity: 0;
}

.modal-enter-from .modal-content,
.modal-leave-to .modal-content {
  transform: scale(0.9);
}

/* Responsive */
@media (max-width: 768px) {
  .hero-content {
    padding: 2rem 1rem;
  }
  
  .title-text {
    font-size: 2rem;
  }
  
  .title-subtitle {
    font-size: 1rem;
  }
  
  .stats-banner {
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .stat-item {
    flex: 0 0 45%;
  }
  
  .categories-carousel {
    gap: 1rem;
  }
  
  .category-card {
    flex: 0 0 150px;
  }
  
  .quick-actions-section {
    flex-wrap: wrap;
  }
  
  .featured-carousel {
    grid-template-columns: 1fr;
  }
  
  .resources-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .resource-modal {
    padding: 0;
  }
  
  .modal-content {
    border-radius: 0;
    max-height: 100vh;
  }
  
  .floating-timer {
    bottom: 1rem;
    right: 1rem;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7);
}
</style>