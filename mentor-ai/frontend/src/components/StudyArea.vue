<template>
  <div class="study-area">
    <!-- Clean Header -->
    <header class="study-header">
      <div class="header-container">
        <div class="header-title">
          <div class="title-icon">
            <i class="fas fa-book-open"></i>
          </div>
          <h1>Área de Estudo</h1>
          <p class="subtitle">Maximize seu foco e produtividade</p>
        </div>
        
        <!-- Quick Stats -->
        <div class="quick-stats">
          <div class="stat">
            <span class="stat-value">{{ totalFocusTime }}</span>
            <span class="stat-label">min hoje</span>
          </div>
          <div class="stat">
            <span class="stat-value">{{ currentStreak }}</span>
            <span class="stat-label">dias seguidos</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content Grid -->
    <div class="content-grid">
      <!-- Timer Card -->
      <div class="card timer-card">
        <div class="timer-display">
          <svg class="progress-circle" viewBox="0 0 200 200">
            <circle class="progress-bg" cx="100" cy="100" r="90"/>
            <circle 
              class="progress-bar" 
              cx="100" 
              cy="100" 
              r="90"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="progressOffset"
            />
          </svg>
          
          <div class="timer-info">
            <div class="time">{{ formatTime }}</div>
            <div class="session-type">{{ sessionName }}</div>
          </div>
        </div>

        <div class="timer-controls">
          <button @click="toggleTimer" class="btn-primary">
            <i :class="isRunning ? 'fas fa-pause' : 'fas fa-play'"></i>
            {{ isRunning ? 'Pausar' : 'Iniciar' }}
          </button>
          <button @click="resetTimer" class="btn-secondary">
            <i class="fas fa-redo"></i>
          </button>
          <button @click="skipSession" class="btn-secondary">
            <i class="fas fa-forward"></i>
          </button>
        </div>

        <!-- Session Presets -->
        <div class="session-presets">
          <button 
            v-for="preset in presets" 
            :key="preset.id"
            @click="selectPreset(preset)"
            :class="['preset', { active: activePreset === preset.id }]"
          >
            <i :class="preset.icon"></i>
            <span>{{ preset.name }}</span>
            <small>{{ preset.duration }}min</small>
          </button>
        </div>
      </div>

      <!-- Focus Tools -->
      <div class="card focus-tools">
        <h2>Ferramentas de Foco</h2>
        
        <div class="tools-grid">
          <!-- Ambient Sound -->
          <div class="tool-item">
            <div class="tool-header">
              <i class="fas fa-volume-up"></i>
              <span>Som Ambiente</span>
              <label class="switch">
                <input type="checkbox" v-model="ambientSound.enabled">
                <span class="slider"></span>
              </label>
            </div>
            <div v-if="ambientSound.enabled" class="tool-content">
              <div class="sound-options">
                <button 
                  v-for="sound in sounds" 
                  :key="sound.id"
                  @click="selectSound(sound.id)"
                  :class="['sound-btn', { active: ambientSound.current === sound.id }]"
                >
                  <i :class="sound.icon"></i>
                  <span>{{ sound.name }}</span>
                </button>
              </div>
              <div class="volume-control">
                <i class="fas fa-volume-down"></i>
                <input 
                  type="range" 
                  v-model="ambientSound.volume" 
                  min="0" 
                  max="100"
                  class="volume-slider"
                >
                <i class="fas fa-volume-up"></i>
              </div>
            </div>
          </div>

          <!-- Focus Mode -->
          <div class="tool-item">
            <div class="tool-header">
              <i class="fas fa-eye"></i>
              <span>Modo Foco</span>
              <label class="switch">
                <input type="checkbox" v-model="focusMode">
                <span class="slider"></span>
              </label>
            </div>
            <p class="tool-description">
              Oculta distrações e notificações durante o estudo
            </p>
          </div>

          <!-- Break Reminder -->
          <div class="tool-item">
            <div class="tool-header">
              <i class="fas fa-coffee"></i>
              <span>Lembrete de Pausa</span>
              <label class="switch">
                <input type="checkbox" v-model="breakReminder">
                <span class="slider"></span>
              </label>
            </div>
            <div v-if="breakReminder" class="tool-content">
              <div class="break-interval">
                <span>A cada</span>
                <input 
                  type="number" 
                  v-model="breakInterval" 
                  min="15" 
                  max="120"
                  class="interval-input"
                >
                <span>minutos</span>
              </div>
            </div>
          </div>

          <!-- Goal Tracker -->
          <div class="tool-item">
            <div class="tool-header">
              <i class="fas fa-bullseye"></i>
              <span>Meta Diária</span>
            </div>
            <div class="goal-progress">
              <div class="progress-bar-container">
                <div 
                  class="progress-fill" 
                  :style="{ width: goalProgress + '%' }"
                ></div>
              </div>
              <div class="goal-info">
                <span>{{ todayMinutes }} / {{ dailyGoal }} min</span>
                <button @click="showGoalModal = true" class="edit-btn">
                  <i class="fas fa-edit"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tasks & Notes -->
      <div class="card tasks-section">
        <div class="section-tabs">
          <button 
            @click="activeTab = 'tasks'" 
            :class="['tab', { active: activeTab === 'tasks' }]"
          >
            <i class="fas fa-tasks"></i>
            Tarefas
          </button>
          <button 
            @click="activeTab = 'notes'" 
            :class="['tab', { active: activeTab === 'notes' }]"
          >
            <i class="fas fa-sticky-note"></i>
            Notas
          </button>
        </div>

        <!-- Tasks Tab -->
        <div v-if="activeTab === 'tasks'" class="tab-content">
          <div class="add-item">
            <input 
              v-model="newTask"
              @keyup.enter="addTask"
              placeholder="Adicionar tarefa..."
              class="input-clean"
            >
            <button @click="addTask" class="add-btn">
              <i class="fas fa-plus"></i>
            </button>
          </div>

          <div class="tasks-list">
            <div 
              v-for="task in tasks" 
              :key="task.id"
              :class="['task-item', { completed: task.completed }]"
            >
              <label class="checkbox-container">
                <input 
                  type="checkbox" 
                  v-model="task.completed"
                >
                <span class="checkmark"></span>
              </label>
              <span class="task-text">{{ task.text }}</span>
              <button @click="deleteTask(task.id)" class="delete-btn">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Notes Tab -->
        <div v-if="activeTab === 'notes'" class="tab-content">
          <textarea 
            v-model="studyNotes"
            placeholder="Adicione suas anotações de estudo aqui..."
            class="notes-textarea"
          ></textarea>
        </div>
      </div>

      <!-- Analytics -->
      <div class="card analytics-card">
        <h2>Análise de Produtividade</h2>
        
        <div class="analytics-grid">
          <div class="metric">
            <div class="metric-icon">
              <i class="fas fa-fire"></i>
            </div>
            <div class="metric-info">
              <span class="metric-value">{{ productivityScore }}%</span>
              <span class="metric-label">Produtividade</span>
            </div>
          </div>

          <div class="metric">
            <div class="metric-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="metric-info">
              <span class="metric-value">{{ averageSession }}min</span>
              <span class="metric-label">Sessão Média</span>
            </div>
          </div>

          <div class="metric">
            <div class="metric-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="metric-info">
              <span class="metric-value">{{ completedToday }}</span>
              <span class="metric-label">Tarefas Hoje</span>
            </div>
          </div>
        </div>

        <!-- Weekly Chart -->
        <div class="weekly-chart">
          <h3>Última Semana</h3>
          <div class="chart-container">
            <div 
              v-for="day in weekData" 
              :key="day.name"
              class="chart-bar"
            >
              <div 
                class="bar-fill" 
                :style="{ height: (day.minutes / maxMinutes * 100) + '%' }"
              >
                <span class="bar-value">{{ day.minutes }}</span>
              </div>
              <span class="bar-label">{{ day.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Breathing Exercise Modal -->
    <transition name="modal">
      <div v-if="showBreathingExercise" class="modal-overlay" @click="closeBreathing">
        <div class="modal breathing-modal" @click.stop>
          <h2>Exercício de Respiração</h2>
          <div class="breathing-circle" :class="breathingPhase">
            <span>{{ breathingText }}</span>
          </div>
          <button @click="closeBreathing" class="close-modal">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'StudyArea',
  data() {
    return {
      // Timer
      isRunning: false,
      timeLeft: 25 * 60,
      duration: 25 * 60,
      sessionType: 'focus',
      interval: null,
      activePreset: 'pomodoro',

      // Stats
      totalFocusTime: 0,
      currentStreak: 0,
      todayMinutes: 0,
      dailyGoal: 120,
      productivityScore: 85,
      averageSession: 23,
      completedToday: 12,

      // Focus Tools
      ambientSound: {
        enabled: false,
        current: 'rain',
        volume: 50
      },
      focusMode: false,
      breakReminder: true,
      breakInterval: 45,

      // Tasks & Notes
      activeTab: 'tasks',
      newTask: '',
      tasks: [],
      studyNotes: '',

      // UI State
      showGoalModal: false,
      showBreathingExercise: false,
      breathingPhase: 'inhale',
      breathingText: 'Inspire',

      // Presets
      presets: [
        { id: 'pomodoro', name: 'Pomodoro', duration: 25, icon: 'fas fa-tomato' },
        { id: 'short', name: 'Curta', duration: 15, icon: 'fas fa-mug-hot' },
        { id: 'long', name: 'Longa', duration: 45, icon: 'fas fa-mountain' },
        { id: 'custom', name: 'Custom', duration: 30, icon: 'fas fa-cog' }
      ],

      // Sounds
      sounds: [
        { id: 'rain', name: 'Chuva', icon: 'fas fa-cloud-rain' },
        { id: 'waves', name: 'Ondas', icon: 'fas fa-water' },
        { id: 'forest', name: 'Floresta', icon: 'fas fa-tree' },
        { id: 'cafe', name: 'Café', icon: 'fas fa-coffee' },
        { id: 'white', name: 'Ruído Branco', icon: 'fas fa-broadcast-tower' }
      ],

      // Weekly Data
      weekData: [
        { name: 'Seg', minutes: 45 },
        { name: 'Ter', minutes: 120 },
        { name: 'Qua', minutes: 90 },
        { name: 'Qui', minutes: 135 },
        { name: 'Sex', minutes: 60 },
        { name: 'Sáb', minutes: 180 },
        { name: 'Dom', minutes: 95 }
      ]
    }
  },

  computed: {
    circumference() {
      return 2 * Math.PI * 90
    },

    progressOffset() {
      const progress = (this.duration - this.timeLeft) / this.duration
      return this.circumference - (progress * this.circumference)
    },

    formatTime() {
      const minutes = Math.floor(this.timeLeft / 60)
      const seconds = this.timeLeft % 60
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    },

    sessionName() {
      switch(this.sessionType) {
        case 'focus': return 'Foco'
        case 'break': return 'Pausa'
        case 'longBreak': return 'Pausa Longa'
        default: return 'Sessão'
      }
    },

    goalProgress() {
      return Math.min((this.todayMinutes / this.dailyGoal) * 100, 100)
    },

    maxMinutes() {
      return Math.max(...this.weekData.map(d => d.minutes))
    }
  },

  methods: {
    toggleTimer() {
      if (this.isRunning) {
        this.pauseTimer()
      } else {
        this.startTimer()
      }
    },

    startTimer() {
      this.isRunning = true
      this.interval = setInterval(() => {
        if (this.timeLeft > 0) {
          this.timeLeft--
          if (this.timeLeft === 0) {
            this.completeSession()
          }
        }
      }, 1000)
    },

    pauseTimer() {
      this.isRunning = false
      clearInterval(this.interval)
    },

    resetTimer() {
      this.pauseTimer()
      this.timeLeft = this.duration
    },

    skipSession() {
      this.completeSession()
    },

    completeSession() {
      this.pauseTimer()
      this.playNotification()
      
      // Update stats
      if (this.sessionType === 'focus') {
        this.totalFocusTime += Math.floor(this.duration / 60)
        this.todayMinutes += Math.floor(this.duration / 60)
      }

      // Switch session type
      if (this.sessionType === 'focus') {
        this.sessionType = 'break'
        this.duration = 5 * 60
      } else {
        this.sessionType = 'focus'
        this.duration = 25 * 60
      }
      
      this.timeLeft = this.duration
    },

    selectPreset(preset) {
      this.activePreset = preset.id
      this.duration = preset.duration * 60
      this.timeLeft = this.duration
      this.sessionType = 'focus'
    },

    selectSound(soundId) {
      this.ambientSound.current = soundId
      // Here you would actually play the sound
    },

    addTask() {
      if (this.newTask.trim()) {
        this.tasks.push({
          id: Date.now(),
          text: this.newTask,
          completed: false
        })
        this.newTask = ''
      }
    },

    deleteTask(id) {
      this.tasks = this.tasks.filter(task => task.id !== id)
    },

    playNotification() {
      // Play notification sound
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBzGH0fPTgjMGHm7A7+OZURE')
      audio.play()
    },

    closeBreathing() {
      this.showBreathingExercise = false
    }
  },

  mounted() {
    // Load saved data
    const savedData = localStorage.getItem('studyAreaData')
    if (savedData) {
      const data = JSON.parse(savedData)
      this.totalFocusTime = data.totalFocusTime || 0
      this.currentStreak = data.currentStreak || 0
      this.tasks = data.tasks || []
      this.studyNotes = data.studyNotes || ''
    }
  },

  beforeUnmount() {
    if (this.interval) {
      clearInterval(this.interval)
    }
    
    // Save data
    localStorage.setItem('studyAreaData', JSON.stringify({
      totalFocusTime: this.totalFocusTime,
      currentStreak: this.currentStreak,
      tasks: this.tasks,
      studyNotes: this.studyNotes
    }))
  }
}
</script>

<style scoped>
/* Reset & Base */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.study-area {
  min-height: 100vh;
  background: #fafbfc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  color: #2c3e50;
}

/* Header */
.study-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.header-title h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
}

.subtitle {
  color: #718096;
  font-size: 1rem;
  margin-top: 0.25rem;
}

.quick-stats {
  display: flex;
  gap: 2rem;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  color: #718096;
  font-size: 0.875rem;
}

/* Content Grid */
.content-grid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

/* Cards */
.card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #1a202c;
}

/* Timer Card */
.timer-card {
  grid-column: span 1;
}

.timer-display {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto 2rem;
}

.progress-circle {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-bg {
  fill: none;
  stroke: #e9ecef;
  stroke-width: 8;
}

.progress-bar {
  fill: none;
  stroke: url(#gradient);
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.5s ease;
}

.timer-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.time {
  font-size: 3rem;
  font-weight: 700;
  color: #1a202c;
  font-variant-numeric: tabular-nums;
}

.session-type {
  font-size: 1rem;
  color: #718096;
  margin-top: 0.5rem;
}

/* Timer Controls */
.timer-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.btn-primary,
.btn-secondary {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #edf2f7;
}

/* Session Presets */
.session-presets {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
}

.preset {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.preset:hover {
  border-color: #667eea;
  background: #f7f9ff;
}

.preset.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.preset i {
  display: block;
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.preset span {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
}

.preset small {
  display: block;
  font-size: 0.75rem;
  opacity: 0.8;
}

/* Focus Tools */
.focus-tools {
  grid-column: span 1;
}

.tools-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.tool-item {
  padding: 1rem;
  background: #f7fafc;
  border-radius: 12px;
}

.tool-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.tool-header i {
  color: #667eea;
  font-size: 1.25rem;
}

.tool-header span {
  flex: 1;
  font-weight: 500;
}

.tool-description {
  color: #718096;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cbd5e0;
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

input:checked + .slider:before {
  transform: translateX(24px);
}

/* Sound Options */
.sound-options {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.sound-btn {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.sound-btn:hover {
  border-color: #667eea;
}

.sound-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.sound-btn i {
  display: block;
  margin-bottom: 0.25rem;
}

.sound-btn span {
  font-size: 0.75rem;
}

/* Volume Control */
.volume-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.volume-slider {
  flex: 1;
  height: 4px;
  -webkit-appearance: none;
  appearance: none;
  background: #e2e8f0;
  border-radius: 2px;
  outline: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #667eea;
  border-radius: 50%;
  cursor: pointer;
}

/* Tasks Section */
.tasks-section {
  grid-column: span 1;
}

.section-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.tab {
  padding: 0.75rem 1.5rem;
  border: none;
  background: none;
  color: #718096;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

/* Add Item */
.add-item {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.input-clean {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.input-clean:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.add-btn {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  background: #667eea;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-btn:hover {
  background: #5a67d8;
}

/* Tasks List */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.task-item.completed {
  opacity: 0.6;
}

.task-item.completed .task-text {
  text-decoration: line-through;
  color: #a0aec0;
}

/* Checkbox */
.checkbox-container {
  display: block;
  position: relative;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: relative;
  height: 20px;
  width: 20px;
  background-color: white;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.checkbox-container:hover input ~ .checkmark {
  border-color: #667eea;
}

.checkbox-container input:checked ~ .checkmark {
  background-color: #667eea;
  border-color: #667eea;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

.task-text {
  flex: 1;
  font-size: 0.95rem;
}

.delete-btn {
  padding: 0.5rem;
  border: none;
  background: none;
  color: #e53e3e;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
}

.task-item:hover .delete-btn {
  opacity: 1;
}

/* Notes */
.notes-textarea {
  width: 100%;
  min-height: 300px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  resize: vertical;
  font-family: inherit;
}

.notes-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Analytics Card */
.analytics-card {
  grid-column: span 1;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.metric {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 12px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea20, #764ba220);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 1.25rem;
}

.metric-info {
  flex: 1;
}

.metric-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
}

.metric-label {
  display: block;
  font-size: 0.875rem;
  color: #718096;
}

/* Weekly Chart */
.weekly-chart h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #4a5568;
}

.chart-container {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 150px;
  gap: 0.5rem;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 4px 4px 0 0;
  position: relative;
  transition: height 0.3s ease;
}

.bar-value {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  font-weight: 600;
  color: #4a5568;
}

.bar-label {
  font-size: 0.75rem;
  color: #718096;
}

/* Goal Progress */
.goal-progress {
  margin-top: 0.75rem;
}

.progress-bar-container {
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.goal-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: #718096;
}

.edit-btn {
  padding: 0.25rem 0.5rem;
  border: none;
  background: none;
  color: #667eea;
  cursor: pointer;
}

/* Breathing Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  position: relative;
}

.breathing-modal {
  text-align: center;
}

.breathing-circle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  margin: 2rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  font-weight: 500;
  transition: transform 4s ease-in-out;
}

.breathing-circle.inhale {
  transform: scale(1.2);
}

.breathing-circle.exhale {
  transform: scale(0.8);
}

.close-modal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.5rem;
  border: none;
  background: none;
  color: #718096;
  cursor: pointer;
  font-size: 1.25rem;
}

/* Animations */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .header-container {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }
  
  .session-presets {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
}

/* SVG Gradient */
.study-area svg {
  position: absolute;
  width: 0;
  height: 0;
}

.study-area defs linearGradient {
  id: gradient;
}

.study-area defs linearGradient stop:first-child {
  stop-color: #667eea;
}

.study-area defs linearGradient stop:last-child {
  stop-color: #764ba2;
}
</style>