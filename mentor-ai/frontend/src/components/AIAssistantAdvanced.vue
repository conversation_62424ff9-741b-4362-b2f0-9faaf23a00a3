<template>
  <div class="ai-assistant-advanced">
    <!-- Futuristic Background -->
    <div class="quantum-background">
      <div class="quantum-grid"></div>
      <div class="energy-waves"></div>
      <div class="neural-particles"></div>
    </div>

    <!-- Main Header -->
    <header class="assistant-header">
      <div class="header-content">
        <div class="ai-core">
          <div class="core-animation">
            <div class="core-ring ring-1"></div>
            <div class="core-ring ring-2"></div>
            <div class="core-ring ring-3"></div>
            <div class="core-center">
              <i class="fas fa-brain"></i>
            </div>
          </div>
          <div class="ai-status">
            <span class="status-dot" :class="{ active: isProcessing }"></span>
            <span>{{ isProcessing ? 'Processing...' : 'AI Ready' }}</span>
          </div>
        </div>
        
        <div class="header-info">
          <h1 class="main-title">
            <span class="title-main">AI Assistant</span>
            <span class="title-advanced">Advanced</span>
          </h1>
          <p class="subtitle">
            Intelligent Question Generation Based on Your Documents
          </p>
        </div>

        <div class="metrics-display">
          <div class="metric">
            <i class="fas fa-chart-line"></i>
            <span class="metric-value">{{ questionsGenerated }}</span>
            <span class="metric-label">Questions</span>
          </div>
          <div class="metric">
            <i class="fas fa-layer-group"></i>
            <span class="metric-value">{{ levelsAvailable }}</span>
            <span class="metric-label">Levels</span>
          </div>
          <div class="metric">
            <i class="fas fa-file-alt"></i>
            <span class="metric-value">{{ documentsProcessed }}</span>
            <span class="metric-label">Documents</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Document Upload Section -->
    <section class="upload-section">
      <div class="section-header">
        <h2><i class="fas fa-upload"></i> Upload Documents</h2>
        <p>Support for PDF, DOCX, TXT, Images, and more</p>
      </div>

      <div class="upload-area" 
           :class="{ 'drag-over': isDragging }"
           @drop="handleDrop"
           @dragover.prevent="isDragging = true"
           @dragleave.prevent="isDragging = false">
        
        <div v-if="!uploadedFiles.length" class="upload-prompt">
          <div class="upload-icon">
            <i class="fas fa-cloud-upload-alt"></i>
          </div>
          <h3>Drag & Drop Your Documents Here</h3>
          <p>or</p>
          <label class="upload-button">
            <input type="file" 
                   multiple 
                   @change="handleFileSelect"
                   accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg">
            <span><i class="fas fa-folder-open"></i> Browse Files</span>
          </label>
        </div>

        <div v-else class="uploaded-files">
          <div v-for="(file, index) in uploadedFiles" 
               :key="index" 
               class="file-card">
            <div class="file-icon">
              <i :class="getFileIcon(file.type)"></i>
            </div>
            <div class="file-details">
              <h4>{{ file.name }}</h4>
              <p>{{ formatFileSize(file.size) }} • {{ file.type }}</p>
              <div class="file-progress" v-if="file.processing">
                <div class="progress-bar" :style="{ width: file.progress + '%' }"></div>
              </div>
            </div>
            <div class="file-actions">
              <button @click="analyzeFile(file)" 
                      class="analyze-btn"
                      :disabled="file.analyzed">
                <i class="fas fa-microscope"></i>
                {{ file.analyzed ? 'Analyzed' : 'Analyze' }}
              </button>
              <button @click="removeFile(index)" class="remove-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Text Input Alternative -->
      <div class="text-input-section">
        <h3><i class="fas fa-keyboard"></i> Or Paste Your Text</h3>
        <div class="text-editor">
          <textarea 
            v-model="inputText"
            placeholder="Paste your text here for analysis..."
            class="text-input"
            @input="analyzeTextContent">
          </textarea>
          <div class="text-stats">
            <span><i class="fas fa-font"></i> {{ wordCount }} words</span>
            <span><i class="fas fa-paragraph"></i> {{ characterCount }} characters</span>
          </div>
        </div>
      </div>
    </section>

    <!-- AI Configuration -->
    <section class="config-section" v-if="hasContent">
      <div class="section-header">
        <h2><i class="fas fa-cogs"></i> AI Configuration</h2>
      </div>

      <div class="config-grid">
        <!-- Difficulty Levels -->
        <div class="config-card">
          <h3><i class="fas fa-signal"></i> Difficulty Levels</h3>
          <div class="level-selector">
            <label v-for="level in difficultyLevels" :key="level.id" class="level-option">
              <input type="checkbox" 
                     v-model="selectedLevels" 
                     :value="level.id">
              <span class="level-card" :class="level.class">
                <i :class="level.icon"></i>
                <span class="level-name">{{ level.name }}</span>
                <span class="level-desc">{{ level.description }}</span>
              </span>
            </label>
          </div>
        </div>

        <!-- Question Types -->
        <div class="config-card">
          <h3><i class="fas fa-list-ul"></i> Question Types</h3>
          <div class="type-selector">
            <label v-for="type in questionTypes" :key="type.id" class="type-option">
              <input type="checkbox" 
                     v-model="selectedTypes" 
                     :value="type.id">
              <span class="type-badge">
                <i :class="type.icon"></i>
                {{ type.name }}
              </span>
            </label>
          </div>
        </div>

        <!-- Quantity & Options -->
        <div class="config-card">
          <h3><i class="fas fa-sliders-h"></i> Generation Options</h3>
          <div class="options-grid">
            <div class="option-group">
              <label>Questions per level</label>
              <input type="number" 
                     v-model="questionsPerLevel" 
                     min="1" 
                     max="50"
                     class="number-input">
            </div>
            <div class="option-group">
              <label>Language</label>
              <select v-model="selectedLanguage" class="select-input">
                <option value="pt">Português</option>
                <option value="en">English</option>
                <option value="es">Español</option>
              </select>
            </div>
            <div class="option-group">
              <label>Include explanations</label>
              <label class="switch">
                <input type="checkbox" v-model="includeExplanations">
                <span class="slider"></span>
              </label>
            </div>
            <div class="option-group">
              <label>Generate references</label>
              <label class="switch">
                <input type="checkbox" v-model="generateReferences">
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Content Analysis -->
    <section class="analysis-section" v-if="contentAnalysis">
      <div class="section-header">
        <h2><i class="fas fa-chart-pie"></i> Content Analysis</h2>
      </div>

      <div class="analysis-grid">
        <div class="analysis-card">
          <h3>Key Topics Identified</h3>
          <div class="topics-cloud">
            <span v-for="topic in contentAnalysis.topics" 
                  :key="topic.name"
                  class="topic-tag"
                  :style="{ fontSize: topic.relevance * 1.5 + 'rem' }">
              {{ topic.name }}
            </span>
          </div>
        </div>

        <div class="analysis-card">
          <h3>Content Complexity</h3>
          <div class="complexity-meter">
            <div class="meter-bar">
              <div class="meter-fill" 
                   :style="{ width: contentAnalysis.complexity + '%' }">
              </div>
            </div>
            <p>{{ getComplexityLabel(contentAnalysis.complexity) }}</p>
          </div>
        </div>

        <div class="analysis-card">
          <h3>Recommended Focus</h3>
          <ul class="recommendations">
            <li v-for="(rec, index) in contentAnalysis.recommendations" :key="index">
              <i class="fas fa-lightbulb"></i>
              {{ rec }}
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- Generate Button -->
    <div class="action-section" v-if="hasContent">
      <button @click="generateQuestions" 
              class="generate-button"
              :disabled="isProcessing || !selectedLevels.length || !selectedTypes.length">
        <div class="button-content">
          <i :class="isProcessing ? 'fas fa-spinner fa-spin' : 'fas fa-magic'"></i>
          <span>{{ isProcessing ? 'Generating Questions...' : 'Generate Questions' }}</span>
        </div>
        <div class="button-glow"></div>
      </button>
    </div>

    <!-- Generated Questions -->
    <section class="results-section" v-if="generatedQuestions.length">
      <div class="section-header">
        <h2><i class="fas fa-clipboard-list"></i> Generated Questions</h2>
        <div class="results-actions">
          <button @click="exportQuestions('pdf')" class="export-btn">
            <i class="fas fa-file-pdf"></i> Export PDF
          </button>
          <button @click="exportQuestions('docx')" class="export-btn">
            <i class="fas fa-file-word"></i> Export DOCX
          </button>
          <button @click="exportQuestions('json')" class="export-btn">
            <i class="fas fa-code"></i> Export JSON
          </button>
        </div>
      </div>

      <div class="questions-container">
        <div v-for="(levelGroup, levelIndex) in groupedQuestions" 
             :key="levelIndex"
             class="level-group">
          <h3 class="level-header" :class="levelGroup.class">
            <i :class="levelGroup.icon"></i>
            {{ levelGroup.name }} ({{ levelGroup.questions.length }} questions)
          </h3>
          
          <div class="questions-list">
            <div v-for="(question, qIndex) in levelGroup.questions" 
                 :key="qIndex"
                 class="question-card">
              <div class="question-header">
                <span class="question-number">Q{{ qIndex + 1 }}</span>
                <span class="question-type">
                  <i :class="getTypeIcon(question.type)"></i>
                  {{ question.type }}
                </span>
              </div>
              
              <div class="question-content">
                <p class="question-text">{{ question.text }}</p>
                
                <!-- Multiple Choice Options -->
                <div v-if="question.type === 'multiple-choice'" class="options-list">
                  <div v-for="(option, oIndex) in question.options" 
                       :key="oIndex"
                       class="option-item">
                    <span class="option-letter">{{ String.fromCharCode(65 + oIndex) }}</span>
                    <span class="option-text">{{ option }}</span>
                  </div>
                </div>
                
                <!-- Answer & Explanation -->
                <div class="answer-section" v-if="showAnswers">
                  <div class="answer">
                    <i class="fas fa-check-circle"></i>
                    <strong>Answer:</strong> {{ question.answer }}
                  </div>
                  <div class="explanation" v-if="question.explanation">
                    <i class="fas fa-info-circle"></i>
                    <strong>Explanation:</strong> {{ question.explanation }}
                  </div>
                  <div class="reference" v-if="question.reference">
                    <i class="fas fa-book"></i>
                    <strong>Reference:</strong> {{ question.reference }}
                  </div>
                </div>
              </div>
              
              <div class="question-actions">
                <button @click="editQuestion(question)" class="action-btn">
                  <i class="fas fa-edit"></i>
                </button>
                <button @click="regenerateQuestion(question)" class="action-btn">
                  <i class="fas fa-sync"></i>
                </button>
                <button @click="deleteQuestion(question)" class="action-btn">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="answers-toggle">
        <label class="toggle-switch">
          <input type="checkbox" v-model="showAnswers">
          <span class="toggle-label">
            <i :class="showAnswers ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
            {{ showAnswers ? 'Hide Answers' : 'Show Answers' }}
          </span>
        </label>
      </div>
    </section>

    <!-- AI Insights -->
    <section class="insights-section" v-if="aiInsights">
      <div class="section-header">
        <h2><i class="fas fa-brain"></i> AI Insights</h2>
      </div>
      
      <div class="insights-grid">
        <div class="insight-card">
          <h3>Coverage Analysis</h3>
          <div class="coverage-chart">
            <canvas ref="coverageChart"></canvas>
          </div>
        </div>
        
        <div class="insight-card">
          <h3>Question Quality</h3>
          <div class="quality-metrics">
            <div class="metric-item">
              <span class="metric-label">Clarity</span>
              <div class="metric-bar">
                <div class="metric-fill" :style="{ width: aiInsights.clarity + '%' }"></div>
              </div>
            </div>
            <div class="metric-item">
              <span class="metric-label">Relevance</span>
              <div class="metric-bar">
                <div class="metric-fill" :style="{ width: aiInsights.relevance + '%' }"></div>
              </div>
            </div>
            <div class="metric-item">
              <span class="metric-label">Difficulty Balance</span>
              <div class="metric-bar">
                <div class="metric-fill" :style="{ width: aiInsights.balance + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="insight-card">
          <h3>Improvement Suggestions</h3>
          <ul class="suggestions-list">
            <li v-for="(suggestion, index) in aiInsights.suggestions" :key="index">
              <i class="fas fa-arrow-right"></i>
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import AdvancedQuestionService from '@/services/AdvancedQuestionService';
import Chart from 'chart.js/auto';

export default {
  name: 'AIAssistantAdvanced',
  
  setup() {
    // State
    const isProcessing = ref(false);
    const isDragging = ref(false);
    const uploadedFiles = ref([]);
    const inputText = ref('');
    const selectedLevels = ref([]);
    const selectedTypes = ref([]);
    const questionsPerLevel = ref(10);
    const selectedLanguage = ref('pt');
    const includeExplanations = ref(true);
    const generateReferences = ref(true);
    const generatedQuestions = ref([]);
    const showAnswers = ref(false);
    const contentAnalysis = ref(null);
    const aiInsights = ref(null);
    
    // Metrics
    const questionsGenerated = ref(0);
    const levelsAvailable = ref(5);
    const documentsProcessed = ref(0);
    
    // Configuration Options
    const difficultyLevels = ref([
      {
        id: 'beginner',
        name: 'Beginner',
        description: 'Basic concepts',
        icon: 'fas fa-seedling',
        class: 'level-beginner'
      },
      {
        id: 'intermediate',
        name: 'Intermediate',
        description: 'Applied knowledge',
        icon: 'fas fa-graduation-cap',
        class: 'level-intermediate'
      },
      {
        id: 'advanced',
        name: 'Advanced',
        description: 'Complex analysis',
        icon: 'fas fa-user-graduate',
        class: 'level-advanced'
      },
      {
        id: 'expert',
        name: 'Expert',
        description: 'Professional level',
        icon: 'fas fa-crown',
        class: 'level-expert'
      },
      {
        id: 'master',
        name: 'Master',
        description: 'Specialist knowledge',
        icon: 'fas fa-gem',
        class: 'level-master'
      }
    ]);
    
    const questionTypes = ref([
      { id: 'multiple-choice', name: 'Multiple Choice', icon: 'fas fa-list-ol' },
      { id: 'true-false', name: 'True/False', icon: 'fas fa-check-double' },
      { id: 'short-answer', name: 'Short Answer', icon: 'fas fa-pen' },
      { id: 'essay', name: 'Essay', icon: 'fas fa-file-alt' },
      { id: 'case-study', name: 'Case Study', icon: 'fas fa-briefcase-medical' },
      { id: 'matching', name: 'Matching', icon: 'fas fa-puzzle-piece' }
    ]);
    
    // Service
    const questionService = new AdvancedQuestionService();
    
    // Computed
    const hasContent = computed(() => {
      return uploadedFiles.value.length > 0 || inputText.value.length > 50;
    });
    
    const wordCount = computed(() => {
      return inputText.value.trim().split(/\s+/).filter(word => word.length > 0).length;
    });
    
    const characterCount = computed(() => {
      return inputText.value.length;
    });
    
    const groupedQuestions = computed(() => {
      const groups = [];
      selectedLevels.value.forEach(levelId => {
        const level = difficultyLevels.value.find(l => l.id === levelId);
        const questions = generatedQuestions.value.filter(q => q.level === levelId);
        if (questions.length > 0) {
          groups.push({
            ...level,
            questions
          });
        }
      });
      return groups;
    });
    
    // Methods
    const handleDrop = (e) => {
      e.preventDefault();
      isDragging.value = false;
      const files = Array.from(e.dataTransfer.files);
      processFiles(files);
    };
    
    const handleFileSelect = (e) => {
      const files = Array.from(e.target.files);
      processFiles(files);
    };
    
    const processFiles = (files) => {
      files.forEach(file => {
        uploadedFiles.value.push({
          name: file.name,
          size: file.size,
          type: file.type,
          file: file,
          processing: false,
          progress: 0,
          analyzed: false
        });
      });
    };
    
    const getFileIcon = (type) => {
      if (type.includes('pdf')) return 'fas fa-file-pdf';
      if (type.includes('word') || type.includes('doc')) return 'fas fa-file-word';
      if (type.includes('text')) return 'fas fa-file-alt';
      if (type.includes('image')) return 'fas fa-file-image';
      return 'fas fa-file';
    };
    
    const formatFileSize = (bytes) => {
      if (bytes < 1024) return bytes + ' B';
      if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    };
    
    const analyzeFile = async (file) => {
      file.processing = true;
      try {
        const analysis = await questionService.analyzeDocument(file.file);
        file.analyzed = true;
        documentsProcessed.value++;
        updateContentAnalysis(analysis);
      } catch (error) {
        console.error('Error analyzing file:', error);
      } finally {
        file.processing = false;
      }
    };
    
    const removeFile = (index) => {
      uploadedFiles.value.splice(index, 1);
    };
    
    const analyzeTextContent = async () => {
      if (inputText.value.length > 100) {
        try {
          const analysis = await questionService.analyzeText(inputText.value);
          updateContentAnalysis(analysis);
        } catch (error) {
          console.error('Error analyzing text:', error);
        }
      }
    };
    
    const updateContentAnalysis = (analysis) => {
      contentAnalysis.value = analysis;
    };
    
    const getComplexityLabel = (complexity) => {
      if (complexity < 20) return 'Very Simple';
      if (complexity < 40) return 'Simple';
      if (complexity < 60) return 'Moderate';
      if (complexity < 80) return 'Complex';
      return 'Very Complex';
    };
    
    const generateQuestions = async () => {
      isProcessing.value = true;
      try {
        const config = {
          levels: selectedLevels.value,
          types: selectedTypes.value,
          quantity: questionsPerLevel.value,
          language: selectedLanguage.value,
          includeExplanations: includeExplanations.value,
          generateReferences: generateReferences.value
        };
        
        const content = {
          files: uploadedFiles.value.filter(f => f.analyzed),
          text: inputText.value
        };
        
        const result = await questionService.generateQuestions(content, config);
        generatedQuestions.value = result.questions;
        aiInsights.value = result.insights;
        questionsGenerated.value += result.questions.length;
        
        // Draw coverage chart if available
        if (aiInsights.value && aiInsights.value.coverage) {
          drawCoverageChart();
        }
      } catch (error) {
        console.error('Error generating questions:', error);
      } finally {
        isProcessing.value = false;
      }
    };
    
    const getTypeIcon = (type) => {
      const typeObj = questionTypes.value.find(t => t.id === type);
      return typeObj ? typeObj.icon : 'fas fa-question';
    };
    
    const editQuestion = (question) => {
      // Implement edit functionality
      console.log('Edit question:', question);
    };
    
    const regenerateQuestion = async (question) => {
      // Implement regenerate functionality
      console.log('Regenerate question:', question);
    };
    
    const deleteQuestion = (question) => {
      const index = generatedQuestions.value.findIndex(q => q === question);
      if (index > -1) {
        generatedQuestions.value.splice(index, 1);
      }
    };
    
    const exportQuestions = async (format) => {
      try {
        await questionService.exportQuestions(generatedQuestions.value, format);
      } catch (error) {
        console.error('Error exporting questions:', error);
      }
    };
    
    const drawCoverageChart = () => {
      const ctx = document.querySelector('.coverage-chart canvas');
      if (ctx && aiInsights.value.coverage) {
        new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: Object.keys(aiInsights.value.coverage),
            datasets: [{
              data: Object.values(aiInsights.value.coverage),
              backgroundColor: [
                '#6366f1',
                '#8b5cf6',
                '#ec4899',
                '#f59e0b',
                '#10b981'
              ]
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false
          }
        });
      }
    };
    
    return {
      // State
      isProcessing,
      isDragging,
      uploadedFiles,
      inputText,
      selectedLevels,
      selectedTypes,
      questionsPerLevel,
      selectedLanguage,
      includeExplanations,
      generateReferences,
      generatedQuestions,
      showAnswers,
      contentAnalysis,
      aiInsights,
      
      // Metrics
      questionsGenerated,
      levelsAvailable,
      documentsProcessed,
      
      // Configuration
      difficultyLevels,
      questionTypes,
      
      // Computed
      hasContent,
      wordCount,
      characterCount,
      groupedQuestions,
      
      // Methods
      handleDrop,
      handleFileSelect,
      getFileIcon,
      formatFileSize,
      analyzeFile,
      removeFile,
      analyzeTextContent,
      getComplexityLabel,
      generateQuestions,
      getTypeIcon,
      editQuestion,
      regenerateQuestion,
      deleteQuestion,
      exportQuestions
    };
  }
};
</script>

<style scoped>
/* CSS Variables */
:root {
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --secondary: #8b5cf6;
  --success: #10b981;
  --danger: #ef4444;
  --warning: #f59e0b;
  --info: #3b82f6;
  --dark: #0f172a;
  --dark-lighter: #1e293b;
  --dark-card: #334155;
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --border: #475569;
}

/* Base Styles */
.ai-assistant-advanced {
  min-height: 100vh;
  background: var(--dark);
  color: var(--text-primary);
  position: relative;
  overflow-x: hidden;
}

/* Quantum Background */
.quantum-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.quantum-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

.energy-waves {
  position: absolute;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.15) 0%, transparent 50%);
  animation: wave-pulse 10s ease-in-out infinite;
}

.neural-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20% 30%, var(--primary), transparent),
    radial-gradient(2px 2px at 40% 70%, var(--secondary), transparent),
    radial-gradient(1px 1px at 50% 50%, white, transparent);
  background-size: 200px 200px;
  animation: particle-float 30s linear infinite;
}

/* Animations */
@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes wave-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

@keyframes particle-float {
  0% { transform: translateY(0) rotate(0deg); }
  100% { transform: translateY(-100vh) rotate(360deg); }
}

/* Header */
.assistant-header {
  position: relative;
  z-index: 10;
  padding: 3rem 2rem;
  background: linear-gradient(to bottom, rgba(15, 23, 42, 0.9), transparent);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: 3rem;
}

/* AI Core Animation */
.ai-core {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.core-animation {
  position: relative;
  width: 120px;
  height: 120px;
}

.core-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid var(--primary);
  border-radius: 50%;
  opacity: 0.6;
}

.ring-1 {
  width: 100%;
  height: 100%;
  animation: ring-pulse 3s ease-in-out infinite;
}

.ring-2 {
  width: 80%;
  height: 80%;
  animation: ring-pulse 3s ease-in-out infinite 0.5s;
}

.ring-3 {
  width: 60%;
  height: 60%;
  animation: ring-pulse 3s ease-in-out infinite 1s;
}

.core-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 0 30px rgba(99, 102, 241, 0.8);
}

@keyframes ring-pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.3;
  }
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--dark-lighter);
  border-radius: 20px;
  font-size: 0.875rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: var(--text-secondary);
  border-radius: 50%;
  transition: all 0.3s;
}

.status-dot.active {
  background: var(--success);
  box-shadow: 0 0 10px var(--success);
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Header Info */
.header-info {
  flex: 1;
}

.main-title {
  font-size: 3rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  display: flex;
  gap: 1rem;
  align-items: baseline;
}

.title-main {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.title-advanced {
  font-size: 2rem;
  color: var(--text-secondary);
}

.subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Metrics Display */
.metrics-display {
  display: flex;
  gap: 2rem;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: var(--dark-lighter);
  border-radius: 12px;
  border: 1px solid var(--border);
}

.metric i {
  font-size: 1.5rem;
  color: var(--primary);
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.metric-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Sections */
section {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto 3rem;
  padding: 0 2rem;
}

.section-header {
  margin-bottom: 2rem;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.section-header p {
  color: var(--text-secondary);
  margin: 0;
}

/* Upload Section */
.upload-area {
  background: var(--dark-lighter);
  border: 2px dashed var(--border);
  border-radius: 16px;
  padding: 3rem;
  transition: all 0.3s;
}

.upload-area.drag-over {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

.upload-prompt {
  text-align: center;
}

.upload-icon {
  font-size: 4rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.upload-prompt h3 {
  font-size: 1.5rem;
  margin: 0 0 1rem 0;
}

.upload-button {
  display: inline-block;
  padding: 1rem 2rem;
  background: var(--primary);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-button:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

.upload-button input {
  display: none;
}

/* Uploaded Files */
.uploaded-files {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.file-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--dark-card);
  border-radius: 12px;
  border: 1px solid var(--border);
}

.file-icon {
  font-size: 2rem;
  color: var(--primary);
}

.file-details {
  flex: 1;
}

.file-details h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
}

.file-details p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.file-progress {
  margin-top: 0.5rem;
  height: 4px;
  background: var(--border);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary);
  transition: width 0.3s;
}

.file-actions {
  display: flex;
  gap: 0.5rem;
}

.analyze-btn,
.remove-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.3s;
}

.analyze-btn {
  background: var(--primary);
  color: white;
}

.analyze-btn:disabled {
  background: var(--success);
  opacity: 0.7;
  cursor: not-allowed;
}

.remove-btn {
  background: var(--danger);
  color: white;
}

/* Text Input Section */
.text-input-section {
  margin-top: 2rem;
}

.text-input-section h3 {
  font-size: 1.25rem;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.text-editor {
  position: relative;
}

.text-input {
  width: 100%;
  min-height: 200px;
  padding: 1rem;
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  border-radius: 12px;
  color: var(--text-primary);
  font-family: inherit;
  font-size: 1rem;
  resize: vertical;
}

.text-stats {
  display: flex;
  gap: 2rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Configuration Section */
.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.config-card {
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  border-radius: 16px;
  padding: 2rem;
}

.config-card h3 {
  font-size: 1.25rem;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Level Selector */
.level-selector {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.level-option {
  display: block;
  cursor: pointer;
}

.level-option input {
  display: none;
}

.level-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--dark-card);
  border: 2px solid transparent;
  border-radius: 12px;
  transition: all 0.3s;
}

.level-option input:checked + .level-card {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

.level-card i {
  font-size: 1.5rem;
}

.level-name {
  font-weight: 600;
  flex: 1;
}

.level-desc {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Level specific colors */
.level-beginner { color: #10b981; }
.level-intermediate { color: #3b82f6; }
.level-advanced { color: #f59e0b; }
.level-expert { color: #ef4444; }
.level-master { color: #8b5cf6; }

/* Type Selector */
.type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.type-option {
  cursor: pointer;
}

.type-option input {
  display: none;
}

.type-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--dark-card);
  border: 2px solid transparent;
  border-radius: 20px;
  font-size: 0.875rem;
  transition: all 0.3s;
}

.type-option input:checked + .type-badge {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

/* Options Grid */
.options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.option-group label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.number-input,
.select-input {
  padding: 0.5rem 1rem;
  background: var(--dark-card);
  border: 1px solid var(--border);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 1rem;
}

/* Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--dark-card);
  border: 1px solid var(--border);
  border-radius: 24px;
  transition: 0.3s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 3px;
  background: var(--text-secondary);
  border-radius: 50%;
  transition: 0.3s;
}

input:checked + .slider {
  background: var(--primary);
  border-color: var(--primary);
}

input:checked + .slider:before {
  transform: translateX(26px);
  background: white;
}

/* Analysis Section */
.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.analysis-card {
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 1.5rem;
}

.analysis-card h3 {
  font-size: 1.125rem;
  margin: 0 0 1rem 0;
}

/* Topics Cloud */
.topics-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
  padding: 1rem 0;
}

.topic-tag {
  padding: 0.5rem 1rem;
  background: rgba(99, 102, 241, 0.2);
  border: 1px solid var(--primary);
  border-radius: 20px;
  color: var(--primary);
  font-weight: 600;
  transition: all 0.3s;
}

.topic-tag:hover {
  background: var(--primary);
  color: white;
}

/* Complexity Meter */
.complexity-meter {
  text-align: center;
}

.meter-bar {
  width: 100%;
  height: 20px;
  background: var(--dark-card);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.meter-fill {
  height: 100%;
  background: linear-gradient(to right, var(--success), var(--warning), var(--danger));
  transition: width 0.5s;
}

/* Recommendations */
.recommendations {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendations li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border);
}

.recommendations li:last-child {
  border-bottom: none;
}

.recommendations i {
  color: var(--primary);
  margin-top: 0.25rem;
}

/* Action Section */
.action-section {
  text-align: center;
  padding: 3rem 0;
}

.generate-button {
  position: relative;
  padding: 1.5rem 3rem;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1.25rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
}

.generate-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4);
}

.generate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.button-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s;
}

.generate-button:hover .button-glow {
  opacity: 1;
}

/* Results Section */
.results-actions {
  display: flex;
  gap: 1rem;
}

.export-btn {
  padding: 0.75rem 1.5rem;
  background: var(--dark-card);
  border: 1px solid var(--border);
  border-radius: 8px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.export-btn:hover {
  background: var(--primary);
  border-color: var(--primary);
}

/* Questions Container */
.questions-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.level-group {
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  border-radius: 16px;
  overflow: hidden;
}

.level-header {
  padding: 1.5rem;
  background: var(--dark-card);
  margin: 0;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.questions-list {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.question-card {
  background: var(--dark-card);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 1.5rem;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.question-number {
  font-weight: 700;
  font-size: 1.125rem;
  color: var(--primary);
}

.question-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid var(--primary);
  border-radius: 20px;
  font-size: 0.875rem;
  color: var(--primary);
}

.question-text {
  font-size: 1.125rem;
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

/* Options List */
.options-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.option-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border);
  border-radius: 8px;
}

.option-letter {
  font-weight: 700;
  color: var(--primary);
  min-width: 24px;
}

.option-text {
  flex: 1;
}

/* Answer Section */
.answer-section {
  border-top: 1px solid var(--border);
  padding-top: 1rem;
  margin-top: 1rem;
}

.answer,
.explanation,
.reference {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

.answer i { color: var(--success); }
.explanation i { color: var(--info); }
.reference i { color: var(--warning); }

/* Question Actions */
.question-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.action-btn {
  padding: 0.5rem 1rem;
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  border-radius: 6px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn:hover {
  background: var(--primary);
  border-color: var(--primary);
}

/* Answers Toggle */
.answers-toggle {
  text-align: center;
  margin-top: 2rem;
}

.toggle-switch {
  cursor: pointer;
}

.toggle-switch input {
  display: none;
}

.toggle-label {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  background: var(--dark-card);
  border: 1px solid var(--border);
  border-radius: 8px;
  transition: all 0.3s;
}

.toggle-switch input:checked + .toggle-label {
  background: var(--primary);
  border-color: var(--primary);
}

/* Insights Section */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 1.5rem;
}

.insight-card h3 {
  font-size: 1.125rem;
  margin: 0 0 1rem 0;
}

.coverage-chart {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quality-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.metric-label {
  min-width: 120px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.metric-bar {
  flex: 1;
  height: 8px;
  background: var(--dark-card);
  border-radius: 4px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(to right, var(--primary), var(--secondary));
  transition: width 0.5s;
}

.suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestions-list li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border);
}

.suggestions-list li:last-child {
  border-bottom: none;
}

.suggestions-list i {
  color: var(--primary);
  margin-top: 0.25rem;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .metrics-display {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .config-grid,
  .analysis-grid,
  .insights-grid {
    grid-template-columns: 1fr;
  }
  
  .options-grid {
    grid-template-columns: 1fr;
  }
  
  .main-title {
    font-size: 2rem;
    flex-direction: column;
    align-items: center;
  }
  
  .title-advanced {
    font-size: 1.5rem;
  }
}
</style>