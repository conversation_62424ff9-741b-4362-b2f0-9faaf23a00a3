<template>
  <div class="ultra-central-ia" @mousemove="handleMouseMove">
    <!-- Matrix Background -->
    <canvas ref="matrixCanvas" class="matrix-canvas"></canvas>
    
    <!-- Quantum Particles -->
    <div class="quantum-field">
      <div v-for="i in 150" :key="`particle-${i}`" 
           class="quantum-particle"
           :style="{
             '--delay': Math.random() * 10 + 's',
             '--duration': 15 + Math.random() * 20 + 's',
             '--x': Math.random() * 100 + '%',
             '--y': Math.random() * 100 + '%'
           }">
      </div>
    </div>

    <!-- Energy Grid -->
    <svg class="energy-grid" viewBox="0 0 1920 1080">
      <defs>
        <linearGradient id="gridGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.3" />
          <stop offset="50%" style="stop-color:#ff00ff;stop-opacity:0.2" />
          <stop offset="100%" style="stop-color:#ffff00;stop-opacity:0.3" />
        </linearGradient>
      </defs>
      <g class="grid-lines">
        <line v-for="i in 20" :key="`h-${i}`"
              :x1="0" :y1="i * 54" :x2="1920" :y2="i * 54"
              stroke="url(#gridGradient)" stroke-width="0.5" />
        <line v-for="i in 36" :key="`v-${i}`"
              :x1="i * 54" :y1="0" :x2="i * 54" :y2="1080"
              stroke="url(#gridGradient)" stroke-width="0.5" />
      </g>
    </svg>

    <!-- Boot Sequence -->
    <transition name="boot">
      <div v-if="showBootSequence" class="boot-sequence">
        <div class="boot-text">{{ bootText }}</div>
        <div class="boot-progress">
          <div class="boot-bar" :style="{ width: bootProgress + '%' }"></div>
        </div>
      </div>
    </transition>

    <!-- Main Interface -->
    <div v-if="!showBootSequence" class="main-interface">
      <!-- Header HUD -->
      <div class="hud-header">
        <div class="hud-left">
          <div class="system-status">
            <span class="status-indicator" :class="{ active: systemActive }"></span>
            <span class="status-text">SYSTEM {{ systemActive ? 'ONLINE' : 'OFFLINE' }}</span>
          </div>
          <div class="timestamp">{{ currentTime }}</div>
        </div>
        
        <div class="hud-center">
          <h1 class="main-title" data-text="ENGINES - CENTRAL DE IA">
            <span class="glitch-1">ENGINES - CENTRAL DE IA</span>
            <span class="glitch-2">ENGINES - CENTRAL DE IA</span>
            ENGINES - CENTRAL DE IA
          </h1>
          <div class="subtitle">CENTRO DE COMANDO NEURAL AVANÇADO</div>
        </div>
        
        <div class="hud-right">
          <div class="ai-status">
            <div class="status-item">
              <span class="status-label">CPU</span>
              <span class="status-value">{{ cpuUsage }}%</span>
            </div>
            <div class="status-item">
              <span class="status-label">RAM</span>
              <span class="status-value">{{ ramUsage }}%</span>
            </div>
            <div class="status-item">
              <span class="status-label">NEURAL</span>
              <span class="status-value">{{ neuralActivity }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Central Orbital System -->
      <div class="orbital-container" :style="orbitalStyle">
        <!-- Central Core -->
        <div class="central-core" @click="toggleVoiceCommand">
          <div class="core-inner">
            <div class="core-pulse"></div>
            <div class="core-icon">
              <svg viewBox="0 0 100 100" class="brain-icon">
                <path d="M50,20 C30,20 20,35 20,50 C20,65 30,80 50,80 C70,80 80,65 80,50 C80,35 70,20 50,20" 
                      fill="none" stroke="currentColor" stroke-width="2"/>
                <circle cx="35" cy="40" r="5" fill="currentColor"/>
                <circle cx="65" cy="40" r="5" fill="currentColor"/>
                <path d="M35,60 Q50,70 65,60" fill="none" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="core-text">NEURAL CORE</div>
          </div>
          
          <!-- Voice Visualizer -->
          <div v-if="voiceActive" class="voice-visualizer">
            <div v-for="i in 20" :key="`bar-${i}`" class="voice-bar"></div>
          </div>
        </div>

        <!-- Orbital Tools -->
        <div class="orbital-ring">
          <div v-for="(tool, index) in tools" 
               :key="tool.id"
               class="orbital-tool"
               :class="{ active: hoveredTool === tool.id }"
               :style="getToolPosition(index)"
               @mouseenter="hoveredTool = tool.id"
               @mouseleave="hoveredTool = null"
               @click="selectTool(tool)">
            
            <div class="tool-node">
              <div class="node-glow" :style="{ background: tool.color }"></div>
              <div class="node-icon">
                <font-awesome-icon :icon="tool.icon" />
              </div>
              <div class="node-ring"></div>
              <div class="node-particles">
                <span v-for="j in 6" :key="`p-${j}`" class="mini-particle"></span>
              </div>
            </div>
            
            <div class="tool-label">{{ tool.name }}</div>
          </div>
        </div>

        <!-- Connection Lines -->
        <svg class="connection-svg" :viewBox="`0 0 ${svgSize} ${svgSize}`">
          <defs>
            <filter id="glow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          <g v-if="hoveredTool">
            <path v-for="(tool, index) in tools"
                  :key="`path-${tool.id}`"
                  v-if="tool.id === hoveredTool"
                  :d="getConnectionPath(index)"
                  fill="none"
                  :stroke="tool.color"
                  stroke-width="2"
                  filter="url(#glow)"
                  class="connection-path"/>
          </g>
        </svg>
      </div>

      <!-- Tool Details Panel -->
      <transition name="panel-slide">
        <div v-if="selectedTool" class="tool-details-panel">
          <div class="panel-header">
            <h2>{{ selectedTool.name }}</h2>
            <button @click="selectedTool = null" class="close-btn">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          
          <div class="panel-content">
            <div class="tool-description">{{ selectedTool.description }}</div>
            
            <div class="tool-stats">
              <div class="stat-item" v-for="stat in selectedTool.stats" :key="stat.label">
                <span class="stat-label">{{ stat.label }}</span>
                <div class="stat-bar">
                  <div class="stat-fill" :style="{ width: stat.value + '%', background: selectedTool.color }"></div>
                </div>
                <span class="stat-value">{{ stat.value }}%</span>
              </div>
            </div>
            
            <div class="tool-features">
              <h3>Capacidades</h3>
              <div class="feature-grid">
                <div v-for="feature in selectedTool.features" :key="feature" class="feature-item">
                  <font-awesome-icon icon="check-circle" />
                  <span>{{ feature }}</span>
                </div>
              </div>
            </div>
            
            <button @click="launchTool(selectedTool)" class="launch-btn" :style="{ background: selectedTool.color }">
              <span>INICIAR {{ selectedTool.name.toUpperCase() }}</span>
              <font-awesome-icon icon="rocket" />
            </button>
          </div>
        </div>
      </transition>

      <!-- Command Terminal -->
      <div class="command-terminal" :class="{ active: terminalActive }">
        <div class="terminal-header">
          <span class="terminal-title">NEURAL COMMAND INTERFACE</span>
          <button @click="toggleTerminal" class="terminal-toggle">
            <font-awesome-icon :icon="terminalActive ? 'chevron-down' : 'terminal'" />
          </button>
        </div>
        
        <div v-if="terminalActive" class="terminal-body">
          <div class="terminal-output" ref="terminalOutput">
            <div v-for="(line, index) in terminalLines" :key="index" class="terminal-line">
              <span class="line-prefix">{{ line.prefix }}</span>
              <span class="line-content" :class="line.type">{{ line.content }}</span>
            </div>
          </div>
          <div class="terminal-input">
            <span class="input-prefix">$</span>
            <input v-model="terminalCommand" 
                   @keyup.enter="executeCommand"
                   placeholder="Digite um comando..."
                   class="command-input">
          </div>
        </div>
      </div>

      <!-- Activity Monitor -->
      <div class="activity-monitor">
        <h3>ATIVIDADE NEURAL</h3>
        <canvas ref="activityCanvas" class="activity-canvas"></canvas>
      </div>

      <!-- Quick Stats -->
      <div class="quick-stats">
        <div class="stat-card" v-for="stat in quickStats" :key="stat.label">
          <div class="stat-icon">
            <font-awesome-icon :icon="stat.icon" />
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <font-awesome-icon :icon="stat.trend === 'up' ? 'arrow-up' : 'arrow-down'" />
            {{ stat.change }}%
          </div>
        </div>
      </div>
    </div>
    
    <!-- Easter Egg -->
    <div v-if="easterEggActive" class="easter-egg" @click="easterEggActive = false">
      <div class="egg-content">
        <h2>🧠 MODO NEURAL SUPREMO ATIVADO 🧠</h2>
        <p>Você desbloqueou o poder máximo da Central de IA!</p>
        <p class="egg-subtitle">Processamento quântico amplificado em 1000%</p>
        <div class="matrix-rain"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'UltraCentralIA',
  
  setup() {
    const router = useRouter()
    
    // Boot sequence
    const showBootSequence = ref(true)
    const bootProgress = ref(0)
    const bootText = ref('INITIALIZING NEURAL CORE...')
    
    // System state
    const systemActive = ref(false)
    const currentTime = ref('')
    const cpuUsage = ref(0)
    const ramUsage = ref(0)
    const neuralActivity = ref(0)
    
    // Interaction state
    const hoveredTool = ref(null)
    const selectedTool = ref(null)
    const voiceActive = ref(false)
    const terminalActive = ref(false)
    const easterEggActive = ref(false)
    
    // Terminal
    const terminalCommand = ref('')
    const terminalLines = ref([
      { prefix: 'SYSTEM>', content: 'Interface de Comando Neural v4.0', type: 'info' },
      { prefix: 'SYSTEM>', content: 'Digite "ajuda" para comandos disponíveis', type: 'info' }
    ])
    
    // Mouse tracking
    const mouseX = ref(0)
    const mouseY = ref(0)
    
    // Canvas refs
    const matrixCanvas = ref(null)
    const activityCanvas = ref(null)
    const terminalOutput = ref(null)
    
    // Tools configuration - Sistema com 4 ferramentas de IA integradas
    const tools = [
      {
        id: 'second-brain',
        name: 'Second Brain',
        icon: 'brain',
        color: '#00ffff',
        route: '/ia/second-brain',
        description: 'Sistema avançado de organização e conexão de conhecimento com IA neural integrada para otimização de estudos médicos',
        stats: [
          { label: 'Precisão Neural', value: 98 },
          { label: 'Velocidade Quântica', value: 95 },
          { label: 'Eficiência Cognitiva', value: 97 }
        ],
        features: [
          'Organização Neural Avançada',
          'Conexões Quânticas Inteligentes',
          'Busca Semântica com IA',
          'Memória Adaptativa Profunda',
          'Análise de Padrões de Estudo',
          'Sincronização Multi-dispositivo'
        ]
      },
      {
        id: 'round-ai',
        name: 'Round AI',
        icon: 'microphone',
        color: '#ff00ff',
        route: '/ia/round-ai',
        description: 'Transcrição e análise inteligente de áudio médico com processamento neural avançado e compreensão contextual profunda',
        stats: [
          { label: 'Acurácia de Transcrição', value: 96 },
          { label: 'Processamento Neural', value: 92 },
          { label: 'Compreensão Contextual', value: 94 }
        ],
        features: [
          'Transcrição Neural Precisa',
          'Análise Contextual Médica',
          'Resumos Inteligentes Automáticos',
          'Suporte Multi-idiomas',
          'Identificação de Termos Técnicos',
          'Exportação em Múltiplos Formatos'
        ]
      },
      {
        id: 'flashcards-ai',
        name: 'Flashcards AI',
        icon: 'layer-group',
        color: '#00ff88',
        route: '/ia/flashcards',
        description: 'Criação inteligente de flashcards com IA avançada para memorização eficiente e aprendizado acelerado',
        stats: [
          { label: 'Taxa de Retenção', value: 94 },
          { label: 'Geração Automática', value: 99 },
          { label: 'Personalização', value: 96 }
        ],
        features: [
          'Geração Automática de Cards',
          'Repetição Espaçada Inteligente',
          'Múltiplos Formatos de Questões',
          'Análise de Performance',
          'Sincronização em Nuvem',
          'Algoritmo de Memorização Avançado'
        ]
      },
      {
        id: 'questions-ai',
        name: 'Questions AI',
        icon: 'tasks',
        color: '#ff6b6b',
        route: '/ia/questions',
        description: 'Gerador inteligente de questões de concurso com IA de última geração para simulações realistas',
        stats: [
          { label: 'Precisão das Questões', value: 97 },
          { label: 'Variedade de Tópicos', value: 95 },
          { label: 'Nível de Dificuldade', value: 98 }
        ],
        features: [
          'Questões Personalizadas por Nível',
          'Simulados Completos Cronometrados',
          'Estatísticas Detalhadas de Desempenho',
          'Banco com 10.000+ Questões',
          'Comentários e Explicações',
          'Modo Prova Real'
        ]
      }
    ]
    
    // Quick stats
    const quickStats = ref([
      { label: 'Sessões Ativas', value: '1.2K', icon: 'users', trend: 'up', change: 12 },
      { label: 'Processamentos/h', value: '45K', icon: 'microchip', trend: 'up', change: 8 },
      { label: 'Taxa de Sucesso', value: '98%', icon: 'check-circle', trend: 'up', change: 2 },
      { label: 'Latência Média', value: '12ms', icon: 'tachometer-alt', trend: 'down', change: 5 }
    ])
    
    // Orbital positioning
    const svgSize = 800
    const orbitalRadius = 280
    
    const getToolPosition = (index) => {
      const angle = (index * 90) - 45 // Ajustado para 4 ferramentas
      const radian = angle * Math.PI / 180
      const x = Math.cos(radian) * orbitalRadius
      const y = Math.sin(radian) * orbitalRadius
      
      return {
        transform: `translate(${x}px, ${y}px)`,
        '--rotation': `${-angle}deg`
      }
    }
    
    const getConnectionPath = (index) => {
      const angle = (index * 90) - 45 // Ajustado para 4 ferramentas
      const radian = angle * Math.PI / 180
      const x = svgSize / 2 + Math.cos(radian) * orbitalRadius
      const y = svgSize / 2 + Math.sin(radian) * orbitalRadius
      
      return `M ${svgSize/2} ${svgSize/2} L ${x} ${y}`
    }
    
    const orbitalStyle = computed(() => ({
      '--mouse-x': mouseX.value + 'px',
      '--mouse-y': mouseY.value + 'px'
    }))
    
    // Methods
    const handleMouseMove = (e) => {
      const rect = e.currentTarget.getBoundingClientRect()
      mouseX.value = e.clientX - rect.left - rect.width / 2
      mouseY.value = e.clientY - rect.top - rect.height / 2
    }
    
    const selectTool = async (tool) => {
      selectedTool.value = tool
      addTerminalLine(`SYSTEM>`, `Inicializando ${tool.name}...`, 'info')
      addTerminalLine(`NEURAL>`, `Carregando módulos de IA...`, 'success')
      
      // Simulação de carregamento com feedback visual
      const loadingSteps = [
        { delay: 300, message: 'Conectando ao núcleo neural...', type: 'info' },
        { delay: 600, message: 'Sincronizando dados...', type: 'info' },
        { delay: 900, message: 'Sistema pronto!', type: 'success' }
      ]
      
      for (const step of loadingSteps) {
        setTimeout(() => {
          addTerminalLine(`LOAD>`, step.message, step.type)
        }, step.delay)
      }
      
      // Navigate to tool route after loading animation
      setTimeout(async () => {
        try {
          await router.push(tool.route)
          addTerminalLine(`SUCCESS>`, `${tool.name} iniciado com sucesso!`, 'success')
        } catch (error) {
          console.error('Navigation error:', error)
          addTerminalLine(`ERROR>`, `Erro ao iniciar ${tool.name}: ${error.message}`, 'error')
          selectedTool.value = null
        }
      }, 1200)
    }
    
    const toggleVoiceCommand = () => {
      voiceActive.value = !voiceActive.value
      if (voiceActive.value) {
        addTerminalLine('VOICE>', 'Voice command activated', 'success')
      }
    }
    
    const toggleTerminal = () => {
      terminalActive.value = !terminalActive.value
    }
    
    const addTerminalLine = (prefix, content, type = 'default') => {
      terminalLines.value.push({ prefix, content, type })
      if (terminalOutput.value) {
        setTimeout(() => {
          terminalOutput.value.scrollTop = terminalOutput.value.scrollHeight
        }, 10)
      }
    }
    
    const executeCommand = () => {
      const cmd = terminalCommand.value.toLowerCase().trim()
      addTerminalLine('USER>', terminalCommand.value, 'input')
      
      switch(cmd) {
        case 'help':
        case 'ajuda':
          addTerminalLine('SYSTEM>', 'Comandos disponíveis:', 'info')
          addTerminalLine('', 'status - Mostrar status do sistema', 'default')
          addTerminalLine('', 'tools - Listar ferramentas de IA', 'default')
          addTerminalLine('', 'matrix - Ativar modo matrix', 'default')
          addTerminalLine('', 'clear - Limpar terminal', 'default')
          break
          
        case 'status':
          addTerminalLine('SYSTEM>', `CPU: ${cpuUsage.value}% | RAM: ${ramUsage.value}% | Neural: ${neuralActivity.value}%`, 'success')
          break
          
        case 'tools':
        case 'ferramentas':
          addTerminalLine('SYSTEM>', 'Ferramentas de IA disponíveis:', 'info')
          tools.forEach(tool => {
            addTerminalLine('TOOL>', `${tool.name} - ${tool.description.substring(0, 60)}...`, 'info')
          })
          break
          
        case 'matrix':
          easterEggActive.value = true
          addTerminalLine('SYSTEM>', 'MODO MATRIX ATIVADO', 'success')
          break
          
        case 'clear':
          terminalLines.value = [
            { prefix: 'SYSTEM>', content: 'Terminal limpo', type: 'info' }
          ]
          break
          
        default:
          addTerminalLine('ERROR>', `Comando não encontrado: ${cmd}`, 'error')
      }
      
      terminalCommand.value = ''
    }
    
    // Boot sequence
    const runBootSequence = async () => {
      const bootSteps = [
        { progress: 20, text: 'CARREGANDO REDES NEURAIS...' },
        { progress: 40, text: 'CALIBRANDO PROCESSADORES QUÂNTICOS...' },
        { progress: 60, text: 'SINCRONIZANDO ENGINES DE IA...' },
        { progress: 80, text: 'ESTABELECENDO LINKS NEURAIS...' },
        { progress: 100, text: 'SISTEMA PRONTO' }
      ]
      
      for (const step of bootSteps) {
        await new Promise(resolve => setTimeout(resolve, 600))
        bootProgress.value = step.progress
        bootText.value = step.text
      }
      
      await new Promise(resolve => setTimeout(resolve, 500))
      showBootSequence.value = false
      systemActive.value = true
    }
    
    // Matrix rain effect
    const initMatrixRain = () => {
      if (!matrixCanvas.value) return
      
      const canvas = matrixCanvas.value
      const ctx = canvas.getContext('2d')
      
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%^&*()あいうえおかきくけこ'
      const fontSize = 14
      const columns = canvas.width / fontSize
      const drops = Array(Math.floor(columns)).fill(1)
      
      const draw = () => {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.03)'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        
        ctx.fillStyle = '#0F0'
        ctx.font = fontSize + 'px monospace'
        
        for (let i = 0; i < drops.length; i++) {
          const text = characters.charAt(Math.floor(Math.random() * characters.length))
          ctx.fillText(text, i * fontSize, drops[i] * fontSize)
          
          if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
            drops[i] = 0
          }
          drops[i]++
        }
      }
      
      const matrixInterval = setInterval(draw, 35)
      
      return () => clearInterval(matrixInterval)
    }
    
    // Activity monitor
    const initActivityMonitor = () => {
      if (!activityCanvas.value) return
      
      const canvas = activityCanvas.value
      const ctx = canvas.getContext('2d')
      
      canvas.width = 300
      canvas.height = 100
      
      const data = Array(50).fill(0)
      
      const drawActivity = () => {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        
        // Add new data point
        data.shift()
        data.push(Math.random() * 80 + 10)
        
        // Draw line
        ctx.strokeStyle = '#00ffff'
        ctx.lineWidth = 2
        ctx.beginPath()
        
        data.forEach((value, index) => {
          const x = (index / (data.length - 1)) * canvas.width
          const y = canvas.height - (value / 100) * canvas.height
          
          if (index === 0) {
            ctx.moveTo(x, y)
          } else {
            ctx.lineTo(x, y)
          }
        })
        
        ctx.stroke()
        
        // Draw glow
        ctx.shadowBlur = 10
        ctx.shadowColor = '#00ffff'
        ctx.stroke()
        ctx.shadowBlur = 0
      }
      
      const activityInterval = setInterval(drawActivity, 100)
      
      return () => clearInterval(activityInterval)
    }
    
    // Update system stats
    const updateStats = () => {
      cpuUsage.value = Math.floor(Math.random() * 20 + 75)
      ramUsage.value = Math.floor(Math.random() * 15 + 80)
      neuralActivity.value = Math.floor(Math.random() * 30 + 65)
      
      // Update time
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      })
    }
    
    // Lifecycle
    let matrixCleanup = null
    let activityCleanup = null
    let statsInterval = null
    
    onMounted(async () => {
      await runBootSequence()
      
      setTimeout(() => {
        matrixCleanup = initMatrixRain()
        activityCleanup = initActivityMonitor()
      }, 100)
      
      statsInterval = setInterval(updateStats, 1000)
      updateStats()
    })
    
    onUnmounted(() => {
      if (matrixCleanup) matrixCleanup()
      if (activityCleanup) activityCleanup()
      if (statsInterval) clearInterval(statsInterval)
    })
    
    return {
      // Boot
      showBootSequence,
      bootProgress,
      bootText,
      
      // System
      systemActive,
      currentTime,
      cpuUsage,
      ramUsage,
      neuralActivity,
      
      // Interaction
      hoveredTool,
      selectedTool,
      voiceActive,
      terminalActive,
      easterEggActive,
      
      // Terminal
      terminalCommand,
      terminalLines,
      terminalOutput,
      
      // Data
      tools,
      quickStats,
      svgSize,
      
      // Refs
      matrixCanvas,
      activityCanvas,
      
      // Methods
      handleMouseMove,
      getToolPosition,
      getConnectionPath,
      selectTool,
      toggleVoiceCommand,
      toggleTerminal,
      executeCommand,
      orbitalStyle,
      launchTool: selectTool
    }
  }
}
</script>

<style scoped>
/* Base Container */
.ultra-central-ia {
  position: relative;
  min-height: 100vh;
  background: radial-gradient(ellipse at center, #0a0a0a 0%, #000000 70%);
  overflow: hidden;
  cursor: crosshair;
}

.ultra-central-ia::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 50%, rgba(255, 0, 255, 0.1) 0%, transparent 50%);
  animation: pulse-bg 8s ease-in-out infinite;
  pointer-events: none;
}

@keyframes pulse-bg {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* Matrix Canvas */
.matrix-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  pointer-events: none;
}

/* Quantum Field */
.quantum-field {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.quantum-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #00ffff;
  border-radius: 50%;
  box-shadow: 0 0 10px #00ffff;
  animation: quantum-float var(--duration) infinite linear;
  animation-delay: var(--delay);
  left: var(--x);
  top: var(--y);
}

@keyframes quantum-float {
  0% {
    transform: translate(0, 0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translate(10px, -10px) scale(1);
  }
  90% {
    opacity: 1;
    transform: translate(-100px, -300px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-150px, -400px) scale(0);
  }
}

/* Energy Grid */
.energy-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  animation: grid-pulse 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes grid-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.1; }
}

/* Boot Sequence */
.boot-sequence {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #000;
  z-index: 1000;
}

.boot-text {
  font-family: 'Courier New', monospace;
  font-size: 1.5rem;
  color: #00ff00;
  margin-bottom: 2rem;
  text-transform: uppercase;
  letter-spacing: 0.2em;
}

.boot-progress {
  width: 400px;
  height: 4px;
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid rgba(0, 255, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.boot-bar {
  height: 100%;
  background: #00ff00;
  transition: width 0.6s ease;
  box-shadow: 0 0 20px #00ff00;
}

/* Main Interface */
.main-interface {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  animation: interface-boot 1s ease-out;
}

@keyframes interface-boot {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* HUD Header */
.hud-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 3rem;
  padding: 1rem;
  border: 1px solid rgba(0, 255, 255, 0.3);
  background: rgba(0, 255, 255, 0.05);
  position: relative;
}

.hud-header::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ffff);
  z-index: -1;
  filter: blur(10px);
  opacity: 0.3;
  animation: border-rotate 4s linear infinite;
}

@keyframes border-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.hud-left, .hud-right {
  flex: 1;
}

.hud-center {
  flex: 2;
  text-align: center;
}

/* System Status */
.system-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ff0000;
  transition: all 0.3s ease;
}

.status-indicator.active {
  background: #00ff00;
  box-shadow: 0 0 10px #00ff00;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.status-text {
  font-family: 'Courier New', monospace;
  color: #00ffff;
  font-size: 0.875rem;
  text-transform: uppercase;
}

.timestamp {
  font-family: 'Courier New', monospace;
  color: #ffff00;
  font-size: 0.875rem;
}

/* Main Title */
.main-title {
  font-size: 3.5rem;
  font-weight: 900;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0.3em;
  margin: 0;
  position: relative;
  text-shadow: 0 0 30px rgba(0, 255, 255, 0.8);
  background: linear-gradient(45deg, #00ffff, #ff00ff, #00ffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% auto;
  animation: gradient-shift 4s linear infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  100% { background-position: 200% 50%; }
}

.glitch-1, .glitch-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.glitch-1 {
  color: #00ffff;
  animation: glitch-1 0.3s infinite;
}

.glitch-2 {
  color: #ff00ff;
  animation: glitch-2 0.3s infinite reverse;
}

@keyframes glitch-1 {
  0%, 100% { clip-path: inset(0 0 0 0); transform: translate(0); }
  20% { clip-path: inset(20% 0 60% 0); transform: translate(-2px, 2px); }
  40% { clip-path: inset(40% 0 40% 0); transform: translate(2px, -2px); }
  60% { clip-path: inset(60% 0 20% 0); transform: translate(-2px, 2px); }
  80% { clip-path: inset(80% 0 0 0); transform: translate(2px, -2px); }
}

@keyframes glitch-2 {
  0%, 100% { clip-path: inset(0 0 0 0); transform: translate(0); }
  20% { clip-path: inset(0 0 80% 0); transform: translate(2px, -2px); }
  40% { clip-path: inset(20% 0 60% 0); transform: translate(-2px, 2px); }
  60% { clip-path: inset(40% 0 40% 0); transform: translate(2px, -2px); }
  80% { clip-path: inset(60% 0 20% 0); transform: translate(-2px, 2px); }
}

.subtitle {
  font-size: 1.25rem;
  color: #00ffff;
  text-transform: uppercase;
  letter-spacing: 0.6em;
  margin-top: 0.75rem;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
  animation: pulse-text 2s ease-in-out infinite;
}

@keyframes pulse-text {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* AI Status */
.ai-status {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-label {
  font-family: 'Courier New', monospace;
  color: #94a3b8;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.status-value {
  font-family: 'Courier New', monospace;
  color: #00ffff;
  font-size: 0.875rem;
  font-weight: bold;
}

/* Orbital Container */
.orbital-container {
  position: relative;
  width: 800px;
  height: 800px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: perspective(1000px) rotateX(15deg);
  transform-style: preserve-3d;
  animation: orbital-rotate 45s linear infinite;
}

@keyframes orbital-rotate {
  0% { 
    transform: perspective(1000px) rotateX(15deg) rotateZ(0deg) scale(1); 
  }
  50% { 
    transform: perspective(1000px) rotateX(15deg) rotateZ(180deg) scale(1.05); 
  }
  100% { 
    transform: perspective(1000px) rotateX(15deg) rotateZ(360deg) scale(1); 
  }
}

/* Central Core */
.central-core {
  position: absolute;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

.core-inner {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.3), transparent);
  border: 2px solid #00ffff;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.central-core:hover .core-inner {
  transform: scale(1.1);
  border-color: #ff00ff;
  background: radial-gradient(circle, rgba(255, 0, 255, 0.3), transparent);
}

.core-pulse {
  position: absolute;
  inset: -20px;
  border: 2px solid #00ffff;
  border-radius: 50%;
  animation: core-pulse 2s ease-out infinite;
}

@keyframes core-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.core-icon {
  width: 60px;
  height: 60px;
  color: #00ffff;
  margin-bottom: 0.5rem;
}

.brain-icon {
  width: 100%;
  height: 100%;
  animation: brain-pulse 3s ease-in-out infinite;
}

@keyframes brain-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.core-text {
  font-size: 0.75rem;
  color: #00ffff;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  font-weight: bold;
}

/* Voice Visualizer */
.voice-visualizer {
  position: absolute;
  inset: -40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  pointer-events: none;
}

.voice-bar {
  width: 3px;
  height: 20px;
  background: #ff00ff;
  border-radius: 3px;
  animation: voice-wave 0.5s ease-in-out infinite;
  animation-delay: calc(var(--i) * 0.05s);
}

@keyframes voice-wave {
  0%, 100% { transform: scaleY(0.3); }
  50% { transform: scaleY(1); }
}

/* Orbital Tools */
.orbital-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.orbital-tool {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: all;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-node {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(var(--rotation));
}

.node-glow {
  position: absolute;
  inset: -10px;
  border-radius: 50%;
  filter: blur(15px);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.node-icon {
  position: relative;
  width: 90px;
  height: 90px;
  background: rgba(0, 0, 0, 0.8);
  border: 3px solid currentColor;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: #fff;
  z-index: 2;
  transition: all 0.3s ease;
}

.node-ring {
  position: absolute;
  inset: -5px;
  border: 1px solid currentColor;
  border-radius: 50%;
  opacity: 0.3;
  animation: ring-rotate 3s linear infinite;
}

@keyframes ring-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.node-particles {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.mini-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: currentColor;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
}

.orbital-tool.active .mini-particle {
  animation: particle-burst 1s ease-out infinite;
}

@keyframes particle-burst {
  0% {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
  100% {
    transform: translate(
      calc(-50% + var(--tx) * 40px),
      calc(-50% + var(--ty) * 40px)
    );
    opacity: 0;
  }
}

.mini-particle:nth-child(1) { --tx: 1; --ty: 0; animation-delay: 0s; }
.mini-particle:nth-child(2) { --tx: 0.5; --ty: 0.866; animation-delay: 0.1s; }
.mini-particle:nth-child(3) { --tx: -0.5; --ty: 0.866; animation-delay: 0.2s; }
.mini-particle:nth-child(4) { --tx: -1; --ty: 0; animation-delay: 0.3s; }
.mini-particle:nth-child(5) { --tx: -0.5; --ty: -0.866; animation-delay: 0.4s; }
.mini-particle:nth-child(6) { --tx: 0.5; --ty: -0.866; animation-delay: 0.5s; }

.tool-label {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%) rotate(var(--rotation));
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #00ffff;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  white-space: nowrap;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.orbital-tool:hover .tool-label {
  opacity: 1;
  text-shadow: 0 0 10px currentColor;
}

.orbital-tool:hover .node-glow {
  opacity: 1;
  transform: scale(1.5);
}

.orbital-tool:hover .node-icon {
  transform: scale(1.1);
  border-width: 3px;
}

/* Connection SVG */
.connection-svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.connection-path {
  stroke-dasharray: 5, 5;
  animation: dash-flow 1s linear infinite;
}

@keyframes dash-flow {
  from { stroke-dashoffset: 0; }
  to { stroke-dashoffset: -10; }
}

/* Tool Details Panel */
.tool-details-panel {
  position: fixed;
  right: 0;
  top: 0;
  width: 450px;
  height: 100vh;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.98), rgba(10, 10, 20, 0.95));
  border-left: 2px solid;
  border-image: linear-gradient(to bottom, #00ffff, #ff00ff, #00ffff) 1;
  padding: 2.5rem;
  overflow-y: auto;
  z-index: 100;
  backdrop-filter: blur(20px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3);
}

.panel-header h2 {
  font-size: 1.5rem;
  color: #00ffff;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.close-btn {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  border-color: #ff00ff;
  color: #ff00ff;
  transform: rotate(90deg);
}

.tool-description {
  color: #94a3b8;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.tool-stats {
  margin-bottom: 2rem;
}

.stat-item {
  margin-bottom: 1rem;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: #94a3b8;
  margin-bottom: 0.25rem;
}

.stat-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.stat-fill {
  height: 100%;
  transition: width 0.6s ease;
  box-shadow: 0 0 10px currentColor;
}

.stat-value {
  font-size: 0.75rem;
  color: #00ffff;
  font-family: 'Courier New', monospace;
}

.tool-features h3 {
  font-size: 1.125rem;
  color: #fff;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.feature-grid {
  display: grid;
  gap: 0.75rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #cbd5e1;
  font-size: 0.875rem;
}

.feature-item svg {
  color: #00ff00;
}

.launch-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  padding: 1.25rem;
  margin-top: 2rem;
  border: 2px solid transparent;
  border-radius: 12px;
  color: #fff;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.15em;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
  font-family: inherit;
  font-size: 1rem;
}

.launch-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--color, #00ffff), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.launch-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 255, 255, 0.6);
  border-color: currentColor;
}

.launch-btn:active {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 255, 255, 0.4);
}

.launch-btn:hover::before {
  opacity: 0.3;
}

.launch-btn span {
  position: relative;
  z-index: 1;
}

.launch-btn svg {
  position: relative;
  z-index: 1;
  animation: rocket-hover 0.6s ease-in-out infinite alternate;
}

@keyframes rocket-hover {
  0% { transform: translateX(0); }
  100% { transform: translateX(5px); }
}

/* Command Terminal */
.command-terminal {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  width: 400px;
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 100;
}

.command-terminal.active {
  height: 300px;
}

.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: rgba(0, 255, 255, 0.1);
  border-bottom: 1px solid rgba(0, 255, 255, 0.3);
}

.terminal-title {
  font-size: 0.75rem;
  color: #00ffff;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.terminal-toggle {
  background: none;
  border: none;
  color: #00ffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.terminal-toggle:hover {
  color: #ff00ff;
}

.terminal-body {
  padding: 1rem;
}

.terminal-output {
  height: 200px;
  overflow-y: auto;
  margin-bottom: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.terminal-line {
  margin-bottom: 0.5rem;
}

.line-prefix {
  color: #00ffff;
  margin-right: 0.5rem;
}

.line-content {
  color: #94a3b8;
}

.line-content.info { color: #60a5fa; }
.line-content.success { color: #00ff00; }
.line-content.error { color: #ff0000; }
.line-content.input { color: #ffff00; }

.terminal-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.input-prefix {
  color: #00ffff;
  font-family: 'Courier New', monospace;
}

.command-input {
  flex: 1;
  background: none;
  border: none;
  color: #fff;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  outline: none;
}

/* Activity Monitor */
.activity-monitor {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: 1rem;
  z-index: 100;
}

.activity-monitor h3 {
  font-size: 0.875rem;
  color: #00ffff;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin: 0 0 0.75rem;
}

.activity-canvas {
  display: block;
}

/* Quick Stats */
.quick-stats {
  position: fixed;
  top: 50%;
  left: 2rem;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  z-index: 50;
}

.stat-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: #00ffff;
  transform: translateX(10px);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: rgba(0, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00ffff;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 1.25rem;
  font-weight: bold;
  color: #fff;
}

.stat-label {
  font-size: 0.75rem;
  color: #94a3b8;
  text-transform: uppercase;
}

.stat-trend {
  font-size: 0.875rem;
  font-weight: bold;
}

.stat-trend.up { color: #00ff00; }
.stat-trend.down { color: #ff00ff; }

/* Easter Egg */
.easter-egg {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  cursor: pointer;
}

.egg-content {
  text-align: center;
  animation: egg-reveal 0.5s ease-out;
}

@keyframes egg-reveal {
  from {
    transform: scale(0) rotate(180deg);
    opacity: 0;
  }
  to {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.egg-content h2 {
  font-size: 2rem;
  color: #00ff00;
  margin-bottom: 1rem;
  animation: rainbow 2s linear infinite;
}

@keyframes rainbow {
  0% { color: #ff0000; }
  16% { color: #ff8800; }
  33% { color: #ffff00; }
  50% { color: #00ff00; }
  66% { color: #0088ff; }
  83% { color: #8800ff; }
  100% { color: #ff0000; }
}

.egg-content p {
  color: #00ffff;
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
}

.egg-subtitle {
  color: #ff00ff !important;
  font-size: 0.95rem !important;
  font-style: italic;
  animation: pulse-text 1s ease-in-out infinite;
}

.matrix-rain {
  position: absolute;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
}

/* Transitions */
.boot-enter-active, .boot-leave-active {
  transition: all 0.5s ease;
}

.boot-enter-from, .boot-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

.panel-slide-enter-active, .panel-slide-leave-active {
  transition: transform 0.3s ease;
}

.panel-slide-enter-from, .panel-slide-leave-to {
  transform: translateX(100%);
}

/* Responsive */
@media (max-width: 1200px) {
  .orbital-container {
    transform: scale(0.8);
  }
  
  .quick-stats {
    display: none;
  }
}

@media (max-width: 768px) {
  .hud-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .main-title {
    font-size: 2rem;
  }
  
  .orbital-container {
    transform: scale(0.6);
  }
  
  .tool-details-panel {
    width: 100%;
  }
  
  .command-terminal {
    width: calc(100% - 4rem);
  }
  
  .activity-monitor {
    display: none;
  }
}
</style>