<template>
  <div class="home-ultra-advanced">
    <!-- Aurora Background Effect -->
    <div class="aurora-container">
      <div class="aurora aurora-1"></div>
      <div class="aurora aurora-2"></div>
      <div class="aurora aurora-3"></div>
      <div class="neural-grid"></div>
    </div>

    <!-- Glassmorphic Header -->
    <header class="ultra-header-glass">
      <div class="header-inner">
        <!-- 3D Avatar System -->
        <div class="avatar-3d-system">
          <div class="avatar-orbit">
            <div class="orbit-ring ring-1"></div>
            <div class="orbit-ring ring-2"></div>
            <div class="orbit-ring ring-3"></div>
          </div>
          <div class="avatar-core-3d">
            <div class="avatar-face">{{ userInitials }}</div>
            <div class="avatar-glow"></div>
          </div>
          <div class="status-badges">
            <span class="badge-3d online">
              <i class="fas fa-wifi"></i>
            </span>
            <span class="badge-3d streak" v-if="streakDays > 7">
              <i class="fas fa-fire"></i>
              {{ streakDays }}
            </span>
          </div>
        </div>

        <!-- Dynamic Welcome -->
        <div class="welcome-dynamic">
          <h1 class="welcome-title-3d">
            <span class="greeting-text">{{ dynamicGreeting }}</span>
            <span class="user-name-glow">{{ userName }}</span>
            <span class="emoji-animate">{{ greetingEmoji }}</span>
          </h1>
          <div class="ai-status-bar">
            <div class="ai-pulse"></div>
            <span>{{ aiStatus }}</span>
            <div class="neural-activity">
              <span v-for="i in 5" :key="i" class="activity-bar" :style="{ height: neuralActivity[i-1] + '%' }"></span>
            </div>
          </div>
        </div>

        <!-- Real-time Stats Orbs -->
        <div class="stats-orbs">
          <div class="orb-container" v-for="stat in liveStats" :key="stat.id">
            <div class="orb-3d" :style="{ '--orb-color': stat.color }">
              <div class="orb-value">{{ stat.value }}</div>
              <div class="orb-label">{{ stat.label }}</div>
              <div class="orb-progress">
                <svg viewBox="0 0 100 100">
                  <circle cx="50" cy="50" r="45" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="5"/>
                  <circle 
                    cx="50" cy="50" r="45" 
                    fill="none" 
                    :stroke="stat.color" 
                    stroke-width="5"
                    stroke-linecap="round"
                    :stroke-dasharray="`${stat.progress * 2.83} 283`"
                    transform="rotate(-90 50 50)"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- AI Command Bridge -->
    <section class="ai-command-bridge">
      <div class="bridge-container">
        <h2 class="section-title-hologram">
          <span class="hologram-text">Neural Command Center</span>
          <span class="hologram-shadow">Neural Command Center</span>
        </h2>

        <!-- Hexagon Grid -->
        <div class="hexagon-grid">
          <div 
            v-for="tool in aiTools" 
            :key="tool.id"
            class="hexagon-wrapper"
            @click="navigateTo(tool.path)"
            @mouseenter="showToolPreview(tool)"
            @mouseleave="hideToolPreview"
          >
            <div class="hexagon" :style="{ '--hex-color': tool.color }">
              <div class="hex-content">
                <i :class="tool.icon"></i>
                <span class="hex-title">{{ tool.title }}</span>
                <span class="hex-count" v-if="tool.count">{{ tool.count }}</span>
              </div>
              <div class="hex-glow"></div>
            </div>
          </div>
        </div>

        <!-- Tool Preview Panel -->
        <transition name="preview">
          <div v-if="activePreview" class="tool-preview-panel">
            <div class="preview-header">
              <i :class="activePreview.icon"></i>
              <h3>{{ activePreview.title }}</h3>
            </div>
            <p>{{ activePreview.description }}</p>
            <div class="preview-stats">
              <div v-for="stat in activePreview.stats" :key="stat.label" class="preview-stat">
                <span class="stat-value">{{ stat.value }}</span>
                <span class="stat-label">{{ stat.label }}</span>
              </div>
            </div>
            <div class="preview-actions">
              <button class="action-primary">
                Acessar Agora
                <i class="fas fa-arrow-right"></i>
              </button>
            </div>
          </div>
        </transition>
      </div>
    </section>

    <!-- Knowledge Graph 3D -->
    <section class="knowledge-graph-section">
      <div class="graph-container">
        <h2 class="section-title-glow">
          <i class="fas fa-project-diagram"></i>
          Mapa do Conhecimento
        </h2>
        
        <div class="knowledge-3d-view">
          <canvas ref="knowledgeCanvas" class="knowledge-canvas"></canvas>
          <div class="graph-controls">
            <button @click="rotateGraph('left')"><i class="fas fa-chevron-left"></i></button>
            <button @click="centerGraph"><i class="fas fa-crosshairs"></i></button>
            <button @click="rotateGraph('right')"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <!-- Knowledge Stats -->
        <div class="knowledge-stats">
          <div class="k-stat" v-for="kstat in knowledgeStats" :key="kstat.id">
            <div class="k-stat-icon" :style="{ backgroundColor: kstat.color + '20' }">
              <i :class="kstat.icon" :style="{ color: kstat.color }"></i>
            </div>
            <div class="k-stat-info">
              <div class="k-value">{{ kstat.value }}</div>
              <div class="k-label">{{ kstat.label }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Study Flow System -->
    <section class="study-flow-system">
      <div class="flow-header">
        <h2>
          <i class="fas fa-stream"></i>
          Fluxo de Estudo Inteligente
        </h2>
        <div class="flow-controls">
          <button @click="flowView = 'timeline'" :class="{ active: flowView === 'timeline' }">
            <i class="fas fa-clock"></i>
            Timeline
          </button>
          <button @click="flowView = 'kanban'" :class="{ active: flowView === 'kanban' }">
            <i class="fas fa-columns"></i>
            Kanban
          </button>
          <button @click="flowView = 'calendar'" :class="{ active: flowView === 'calendar' }">
            <i class="fas fa-calendar"></i>
            Calendar
          </button>
        </div>
      </div>

      <!-- Dynamic Flow View -->
      <transition name="flow-transition" mode="out-in">
        <!-- Timeline View -->
        <div v-if="flowView === 'timeline'" key="timeline" class="flow-timeline">
          <div class="timeline-track">
            <div 
              v-for="(item, index) in studyTimeline" 
              :key="item.id"
              class="timeline-node"
              :class="{ 
                'completed': item.status === 'completed',
                'active': item.status === 'active',
                'future': item.status === 'future'
              }"
            >
              <div class="node-time">{{ item.time }}</div>
              <div class="node-marker">
                <i :class="item.icon"></i>
                <div class="node-pulse" v-if="item.status === 'active'"></div>
              </div>
              <div class="node-content">
                <h4>{{ item.title }}</h4>
                <p>{{ item.description }}</p>
                <div class="node-actions" v-if="item.actions">
                  <button 
                    v-for="action in item.actions" 
                    :key="action.id"
                    @click="executeAction(action)"
                    class="node-action"
                  >
                    {{ action.label }}
                  </button>
                </div>
              </div>
              <div class="timeline-connector" v-if="index < studyTimeline.length - 1"></div>
            </div>
          </div>
        </div>

        <!-- Kanban View -->
        <div v-else-if="flowView === 'kanban'" key="kanban" class="flow-kanban">
          <div class="kanban-board">
            <div 
              v-for="column in kanbanColumns" 
              :key="column.id"
              class="kanban-column"
            >
              <div class="column-header" :style="{ borderColor: column.color }">
                <h3>{{ column.title }}</h3>
                <span class="column-count">{{ column.items.length }}</span>
              </div>
              <draggable
                v-model="column.items"
                group="tasks"
                class="column-items"
                @change="onKanbanChange"
              >
                <div 
                  v-for="item in column.items" 
                  :key="item.id"
                  class="kanban-item"
                >
                  <div class="item-header">
                    <i :class="item.icon"></i>
                    <span class="item-priority" :class="item.priority">
                      {{ item.priority }}
                    </span>
                  </div>
                  <h4>{{ item.title }}</h4>
                  <p>{{ item.description }}</p>
                  <div class="item-footer">
                    <span class="item-time">
                      <i class="fas fa-clock"></i>
                      {{ item.estimatedTime }}
                    </span>
                    <div class="item-tags">
                      <span v-for="tag in item.tags" :key="tag" class="tag">
                        {{ tag }}
                      </span>
                    </div>
                  </div>
                </div>
              </draggable>
            </div>
          </div>
        </div>

        <!-- Calendar View -->
        <div v-else-if="flowView === 'calendar'" key="calendar" class="flow-calendar">
          <div class="calendar-grid">
            <div class="calendar-header">
              <button @click="previousMonth"><i class="fas fa-chevron-left"></i></button>
              <h3>{{ currentMonth }}</h3>
              <button @click="nextMonth"><i class="fas fa-chevron-right"></i></button>
            </div>
            <div class="calendar-days">
              <div v-for="day in calendarDays" :key="day.date" class="calendar-day" :class="{ 
                'has-events': day.events.length > 0,
                'is-today': day.isToday
              }">
                <span class="day-number">{{ day.number }}</span>
                <div class="day-events">
                  <div 
                    v-for="event in day.events.slice(0, 3)" 
                    :key="event.id"
                    class="mini-event"
                    :style="{ backgroundColor: event.color }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </section>

    <!-- Immersive Content Hub -->
    <section class="immersive-content-hub">
      <div class="hub-header">
        <h2 class="glitch-text">
          <span>Content Universe</span>
          <span>Content Universe</span>
          <span>Content Universe</span>
        </h2>
      </div>

      <div class="content-galaxy">
        <!-- Second Brain Integration -->
        <div class="galaxy-cluster second-brain">
          <div class="cluster-core">
            <i class="fas fa-brain"></i>
            <h3>Second Brain</h3>
          </div>
          <div class="cluster-nodes">
            <div 
              v-for="node in secondBrainNodes" 
              :key="node.id"
              class="node"
              @click="openNode(node)"
            >
              <i :class="node.icon"></i>
              <span>{{ node.count }}</span>
            </div>
          </div>
        </div>

        <!-- Video Learning -->
        <div class="galaxy-cluster video-learning">
          <div class="cluster-core">
            <i class="fas fa-video"></i>
            <h3>Videoaulas</h3>
          </div>
          <div class="video-preview">
            <div class="preview-thumbnail">
              <img :src="latestVideo.thumbnail" :alt="latestVideo.title">
              <div class="play-overlay">
                <i class="fas fa-play"></i>
              </div>
            </div>
            <div class="video-info">
              <h4>{{ latestVideo.title }}</h4>
              <p>{{ latestVideo.professor }}</p>
              <div class="video-stats">
                <span><i class="fas fa-eye"></i> {{ latestVideo.views }}</span>
                <span><i class="fas fa-clock"></i> {{ latestVideo.duration }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Exam Simulator -->
        <div class="galaxy-cluster exam-simulator">
          <div class="cluster-core">
            <i class="fas fa-clipboard-check"></i>
            <h3>Simulados</h3>
          </div>
          <div class="exam-stats-3d">
            <div class="stat-cube" v-for="exam in examStats" :key="exam.id">
              <div class="cube-face front">{{ exam.score }}%</div>
              <div class="cube-face back">{{ exam.name }}</div>
              <div class="cube-face top">Top {{ exam.ranking }}%</div>
              <div class="cube-face bottom">{{ exam.questions }}Q</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Predictive Analytics Dashboard -->
    <section class="predictive-analytics">
      <div class="analytics-header">
        <h2>
          <i class="fas fa-chart-line"></i>
          Análise Preditiva IA
        </h2>
        <div class="time-selector">
          <button 
            v-for="range in timeRanges" 
            :key="range.id"
            @click="selectedRange = range.id"
            :class="{ active: selectedRange === range.id }"
          >
            {{ range.label }}
          </button>
        </div>
      </div>

      <div class="predictions-grid">
        <!-- Performance Prediction -->
        <div class="prediction-card performance">
          <h3>Previsão de Performance</h3>
          <div class="prediction-chart">
            <canvas ref="performanceChart"></canvas>
          </div>
          <div class="prediction-insights">
            <div class="insight" v-for="insight in performanceInsights" :key="insight.id">
              <i :class="insight.icon" :style="{ color: insight.color }"></i>
              <p>{{ insight.text }}</p>
            </div>
          </div>
        </div>

        <!-- Learning Path Optimization -->
        <div class="prediction-card learning-path">
          <h3>Otimização de Caminho</h3>
          <div class="path-visualization">
            <div class="path-node" v-for="node in learningPath" :key="node.id">
              <div class="node-icon" :style="{ backgroundColor: node.color }">
                <i :class="node.icon"></i>
              </div>
              <div class="node-info">
                <h4>{{ node.title }}</h4>
                <p>{{ node.duration }}</p>
              </div>
              <div class="node-connector" v-if="node.id < learningPath.length"></div>
            </div>
          </div>
        </div>

        <!-- Risk Analysis -->
        <div class="prediction-card risk-analysis">
          <h3>Análise de Risco</h3>
          <div class="risk-meters">
            <div class="risk-meter" v-for="risk in riskFactors" :key="risk.id">
              <div class="meter-label">{{ risk.label }}</div>
              <div class="meter-bar">
                <div 
                  class="meter-fill" 
                  :style="{ 
                    width: risk.level + '%',
                    backgroundColor: getRiskColor(risk.level)
                  }"
                ></div>
              </div>
              <div class="meter-actions" v-if="risk.level > 60">
                <button @click="mitigateRisk(risk)">
                  <i class="fas fa-shield-alt"></i>
                  Mitigar
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Floating AI Assistant -->
    <div class="ai-assistant-float" :class="{ expanded: assistantExpanded }">
      <div class="assistant-header" @click="toggleAssistant">
        <div class="assistant-avatar">
          <div class="avatar-ring"></div>
          <i class="fas fa-robot"></i>
        </div>
        <span v-if="!assistantExpanded">Assistente IA</span>
        <div class="notification-dot" v-if="hasNotifications"></div>
      </div>
      
      <transition name="expand">
        <div v-if="assistantExpanded" class="assistant-body">
          <div class="assistant-tabs">
            <button 
              v-for="tab in assistantTabs" 
              :key="tab.id"
              @click="activeAssistantTab = tab.id"
              :class="{ active: activeAssistantTab === tab.id }"
            >
              <i :class="tab.icon"></i>
              {{ tab.label }}
            </button>
          </div>
          
          <div class="assistant-content">
            <!-- Chat Tab -->
            <div v-if="activeAssistantTab === 'chat'" class="chat-container">
              <div class="messages">
                <div 
                  v-for="msg in chatMessages" 
                  :key="msg.id"
                  class="message"
                  :class="msg.sender"
                >
                  <div class="message-bubble">{{ msg.text }}</div>
                </div>
              </div>
              <div class="chat-input">
                <input 
                  v-model="chatInput"
                  @keyup.enter="sendMessage"
                  placeholder="Pergunte qualquer coisa..."
                >
                <button @click="sendMessage">
                  <i class="fas fa-paper-plane"></i>
                </button>
              </div>
            </div>
            
            <!-- Insights Tab -->
            <div v-if="activeAssistantTab === 'insights'" class="insights-container">
              <div class="insight-card" v-for="insight in aiInsights" :key="insight.id">
                <i :class="insight.icon" :style="{ color: insight.color }"></i>
                <h4>{{ insight.title }}</h4>
                <p>{{ insight.text }}</p>
                <button @click="applyInsight(insight)">Aplicar</button>
              </div>
            </div>
            
            <!-- Actions Tab -->
            <div v-if="activeAssistantTab === 'actions'" class="actions-container">
              <button 
                v-for="action in quickAIActions" 
                :key="action.id"
                @click="executeAIAction(action)"
                class="ai-action-btn"
              >
                <i :class="action.icon"></i>
                <span>{{ action.label }}</span>
              </button>
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- Notification System -->
    <div class="notification-center" :class="{ active: showNotifications }">
      <div class="notification-header">
        <h3>Notificações</h3>
        <button @click="showNotifications = false">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="notification-list">
        <div 
          v-for="notif in notifications" 
          :key="notif.id"
          class="notification-item"
          :class="notif.type"
        >
          <i :class="notif.icon"></i>
          <div class="notif-content">
            <h4>{{ notif.title }}</h4>
            <p>{{ notif.message }}</p>
            <span class="notif-time">{{ notif.time }}</span>
          </div>
          <button v-if="notif.action" @click="handleNotificationAction(notif)">
            {{ notif.action.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- Quick Command Palette -->
    <transition name="palette">
      <div v-if="showCommandPalette" class="command-palette-overlay" @click="closeCommandPalette">
        <div class="command-palette" @click.stop>
          <input 
            ref="commandInput"
            v-model="commandQuery"
            placeholder="Digite um comando ou busque..."
            @keydown.esc="closeCommandPalette"
          >
          <div class="command-results">
            <div 
              v-for="(result, index) in filteredCommands" 
              :key="result.id"
              class="command-result"
              :class="{ active: selectedCommandIndex === index }"
              @click="executeCommand(result)"
              @mouseenter="selectedCommandIndex = index"
            >
              <i :class="result.icon"></i>
              <div class="command-info">
                <span class="command-title">{{ result.title }}</span>
                <span class="command-desc">{{ result.description }}</span>
              </div>
              <kbd v-if="result.shortcut">{{ result.shortcut }}</kbd>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import Chart from 'chart.js/auto'
import draggable from 'vuedraggable'

export default {
  name: 'HomeUltraAdvanced',
  components: { draggable },
  
  setup() {
    const store = useStore()
    const router = useRouter()
    
    // State
    const userName = ref('Leonardo')
    const userInitials = ref('LM')
    const streakDays = ref(28)
    const dynamicGreeting = ref('Boa tarde,')
    const greetingEmoji = ref('🌟')
    const aiStatus = ref('Neural Network Ativa • 5 IAs Online')
    const neuralActivity = ref([75, 82, 90, 78, 85])
    
    // Live Stats
    const liveStats = ref([
      { id: 1, label: 'Foco', value: '92%', color: '#667eea', progress: 92 },
      { id: 2, label: 'Energia', value: '85%', color: '#48bb78', progress: 85 },
      { id: 3, label: 'Progresso', value: '78%', color: '#ed8936', progress: 78 }
    ])
    
    // AI Tools Hexagon Grid
    const aiTools = ref([
      { id: 1, title: 'FlashcardsAI', icon: 'fas fa-layer-group', color: '#667eea', path: '/ai-tools/flashcards', count: 127 },
      { id: 2, title: 'Study Area', icon: 'fas fa-book-open', color: '#ed8936', path: '/pomodoro' },
      { id: 3, title: 'AI Tutor', icon: 'fas fa-robot', color: '#48bb78', path: '/ai-tutor' },
      { id: 5, title: 'Engines Hub', icon: 'fas fa-cogs', color: '#9f7aea', path: '/engines' },
      { id: 6, title: 'Analytics', icon: 'fas fa-chart-line', color: '#e53e3e', path: '/progress-dashboard' },
      { id: 7, title: 'Neural Viz', icon: 'fas fa-brain', color: '#ecc94b', path: '/neural-visualizer' }
    ])
    
    // Preview system
    const activePreview = ref(null)
    
    // Knowledge Graph
    const knowledgeStats = ref([
      { id: 1, icon: 'fas fa-network-wired', label: 'Conexões', value: '2,847', color: '#667eea' },
      { id: 2, icon: 'fas fa-atom', label: 'Tópicos', value: '156', color: '#48bb78' },
      { id: 3, icon: 'fas fa-link', label: 'Links', value: '5.2K', color: '#ed8936' },
      { id: 4, icon: 'fas fa-chart-pie', label: 'Cobertura', value: '89%', color: '#38b2ac' }
    ])
    
    // Study Flow
    const flowView = ref('timeline')
    const studyTimeline = ref([
      {
        id: 1,
        time: '08:00',
        title: 'Revisão Matinal',
        description: 'Anatomia Cardiovascular - 45 cards',
        icon: 'fas fa-sun',
        status: 'completed',
        actions: [{ id: 1, label: 'Ver resultados' }]
      },
      {
        id: 2,
        time: '14:00',
        title: 'Sessão Ativa',
        description: 'Farmacologia - Estudo em andamento',
        icon: 'fas fa-bolt',
        status: 'active',
        actions: [{ id: 1, label: 'Continuar' }, { id: 2, label: 'Pausar' }]
      },
      {
        id: 3,
        time: '16:30',
        title: 'Próxima: Videoaula',
        description: 'Fisiologia Renal com Dr. Silva',
        icon: 'fas fa-video',
        status: 'future',
        actions: [{ id: 1, label: 'Definir lembrete' }]
      }
    ])
    
    // Kanban
    const kanbanColumns = ref([
      {
        id: 'todo',
        title: 'A Fazer',
        color: '#e53e3e',
        items: [
          {
            id: 1,
            title: 'Revisar Anatomia',
            description: '150 cards pendentes',
            icon: 'fas fa-brain',
            priority: 'high',
            estimatedTime: '2h',
            tags: ['anatomia', 'revisão']
          }
        ]
      },
      {
        id: 'doing',
        title: 'Em Progresso',
        color: '#ed8936',
        items: [
          {
            id: 2,
            title: 'Estudar Farmacologia',
            description: 'Capítulo 5 - Antibióticos',
            icon: 'fas fa-pills',
            priority: 'medium',
            estimatedTime: '1.5h',
            tags: ['farmaco', 'estudo']
          }
        ]
      },
      {
        id: 'done',
        title: 'Concluído',
        color: '#48bb78',
        items: [
          {
            id: 3,
            title: 'Simulado Cardio',
            description: 'Score: 87%',
            icon: 'fas fa-heart',
            priority: 'low',
            estimatedTime: '1h',
            tags: ['simulado', 'cardio']
          }
        ]
      }
    ])
    
    // Calendar
    const currentMonth = ref('Novembro 2024')
    const calendarDays = ref(generateCalendarDays())
    
    // Content Hub
    const secondBrainNodes = ref([
      { id: 1, icon: 'fas fa-file-alt', count: 234 },
      { id: 2, icon: 'fas fa-sticky-note', count: 567 },
      { id: 3, icon: 'fas fa-bookmark', count: 89 },
      { id: 4, icon: 'fas fa-link', count: 156 }
    ])
    
    const latestVideo = ref({
      thumbnail: '/api/placeholder/320/180',
      title: 'Fisiologia Renal Avançada',
      professor: 'Dr. Carlos Silva',
      views: '2.3K',
      duration: '45min'
    })
    
    const examStats = ref([
      { id: 1, name: 'Cardio', score: 87, ranking: 12, questions: 100 },
      { id: 2, name: 'Neuro', score: 92, ranking: 8, questions: 80 },
      { id: 3, name: 'Farmaco', score: 85, ranking: 15, questions: 120 }
    ])
    
    // Predictive Analytics
    const selectedRange = ref('week')
    const timeRanges = ref([
      { id: 'week', label: 'Semana' },
      { id: 'month', label: 'Mês' },
      { id: 'quarter', label: 'Trimestre' }
    ])
    
    const performanceInsights = ref([
      { id: 1, icon: 'fas fa-arrow-up', color: '#48bb78', text: 'Performance aumentando 15% esta semana' },
      { id: 2, icon: 'fas fa-fire', color: '#ed8936', text: 'Melhor horário: 14h-16h (85% eficiência)' },
      { id: 3, icon: 'fas fa-brain', color: '#667eea', text: 'Retenção média: 89% (+3% vs mês passado)' }
    ])
    
    const learningPath = ref([
      { id: 1, title: 'Anatomia Base', icon: 'fas fa-user', color: '#667eea', duration: '2 semanas' },
      { id: 2, title: 'Fisiologia', icon: 'fas fa-heartbeat', color: '#e53e3e', duration: '3 semanas' },
      { id: 3, title: 'Patologia', icon: 'fas fa-virus', color: '#ed8936', duration: '4 semanas' }
    ])
    
    const riskFactors = ref([
      { id: 1, label: 'Burnout', level: 25 },
      { id: 2, label: 'Sobrecarga', level: 65 },
      { id: 3, label: 'Gaps de Conhecimento', level: 40 },
      { id: 4, label: 'Procrastinação', level: 15 }
    ])
    
    // AI Assistant
    const assistantExpanded = ref(false)
    const hasNotifications = ref(true)
    const activeAssistantTab = ref('chat')
    const assistantTabs = ref([
      { id: 'chat', label: 'Chat', icon: 'fas fa-comments' },
      { id: 'insights', label: 'Insights', icon: 'fas fa-lightbulb' },
      { id: 'actions', label: 'Ações', icon: 'fas fa-bolt' }
    ])
    
    const chatMessages = ref([
      { id: 1, sender: 'ai', text: 'Olá! Notei que você tem ótimo desempenho em Anatomia. Que tal revisar Farmacologia agora?' },
      { id: 2, sender: 'user', text: 'Sim, vamos lá!' }
    ])
    
    const chatInput = ref('')
    
    const aiInsights = ref([
      { id: 1, icon: 'fas fa-brain', color: '#667eea', title: 'Padrão Detectado', text: 'Você aprende melhor com sessões de 45 minutos' },
      { id: 2, icon: 'fas fa-chart-line', color: '#48bb78', title: 'Oportunidade', text: 'Revisar Neuro agora = 92% retenção' }
    ])
    
    const quickAIActions = ref([
      { id: 1, icon: 'fas fa-magic', label: 'Gerar Deck IA' },
      { id: 2, icon: 'fas fa-robot', label: 'Tutor Instantâneo' },
      { id: 3, icon: 'fas fa-brain', label: 'Análise Neural' },
      { id: 4, icon: 'fas fa-rocket', label: 'Modo Foco' }
    ])
    
    // Notifications
    const showNotifications = ref(false)
    const notifications = ref([
      {
        id: 1,
        type: 'success',
        icon: 'fas fa-check',
        title: 'Meta Atingida!',
        message: '100 cards revisados hoje',
        time: '5 min atrás',
        action: { label: 'Ver detalhes' }
      },
      {
        id: 2,
        type: 'info',
        icon: 'fas fa-info',
        title: 'Lembrete',
        message: 'Videoaula começa em 30 minutos',
        time: '10 min atrás',
        action: { label: 'Entrar agora' }
      }
    ])
    
    // Command Palette
    const showCommandPalette = ref(false)
    const commandQuery = ref('')
    const selectedCommandIndex = ref(0)
    
    const allCommands = ref([
      { id: 1, title: 'Iniciar Revisão', description: 'Começar sessão de flashcards', icon: 'fas fa-play', shortcut: 'Ctrl+R' },
      { id: 2, title: 'Novo Deck', description: 'Criar novo deck de cards', icon: 'fas fa-plus', shortcut: 'Ctrl+N' },
      { id: 3, title: 'Abrir AI Tutor', description: 'Conversar com tutor IA', icon: 'fas fa-robot', shortcut: 'Ctrl+T' },
      { id: 4, title: 'Ver Dashboard', description: 'Análises de performance', icon: 'fas fa-chart-line', shortcut: 'Ctrl+D' }
    ])
    
    const filteredCommands = computed(() => {
      if (!commandQuery.value) return allCommands.value
      return allCommands.value.filter(cmd => 
        cmd.title.toLowerCase().includes(commandQuery.value.toLowerCase()) ||
        cmd.description.toLowerCase().includes(commandQuery.value.toLowerCase())
      )
    })
    
    // Methods
    const showToolPreview = (tool) => {
      activePreview.value = {
        ...tool,
        description: 'Ferramenta avançada de IA para otimizar seus estudos',
        stats: [
          { label: 'Uso hoje', value: '2.5h' },
          { label: 'Eficiência', value: '89%' },
          { label: 'Cards', value: tool.count || '0' }
        ]
      }
    }
    
    const hideToolPreview = () => {
      activePreview.value = null
    }
    
    const navigateTo = (path) => {
      router.push(path)
    }
    
    const toggleAssistant = () => {
      assistantExpanded.value = !assistantExpanded.value
      if (assistantExpanded.value) {
        hasNotifications.value = false
      }
    }
    
    const sendMessage = () => {
      if (chatInput.value.trim()) {
        chatMessages.value.push({
          id: Date.now(),
          sender: 'user',
          text: chatInput.value
        })
        
        // Simulate AI response
        setTimeout(() => {
          chatMessages.value.push({
            id: Date.now() + 1,
            sender: 'ai',
            text: 'Entendi! Vou preparar o melhor caminho de estudo para você.'
          })
        }, 1000)
        
        chatInput.value = ''
      }
    }
    
    const getRiskColor = (level) => {
      if (level < 30) return '#48bb78'
      if (level < 60) return '#ed8936'
      return '#e53e3e'
    }
    
    const openCommandPalette = () => {
      showCommandPalette.value = true
      nextTick(() => {
        document.querySelector('.command-palette input')?.focus()
      })
    }
    
    const closeCommandPalette = () => {
      showCommandPalette.value = false
      commandQuery.value = ''
      selectedCommandIndex.value = 0
    }
    
    const executeCommand = (command) => {
      console.log('Executing command:', command)
      closeCommandPalette()
    }
    
    const rotateGraph = (direction) => {
      console.log('Rotating graph:', direction)
    }
    
    const centerGraph = () => {
      console.log('Centering graph')
    }
    
    const onKanbanChange = () => {
      console.log('Kanban changed')
    }
    
    function generateCalendarDays() {
      const days = []
      for (let i = 1; i <= 30; i++) {
        days.push({
          date: `2024-11-${i}`,
          number: i,
          isToday: i === 23,
          events: i % 3 === 0 ? [
            { id: 1, color: '#667eea' },
            { id: 2, color: '#48bb78' }
          ] : []
        })
      }
      return days
    }
    
    // Keyboard shortcuts
    const handleKeyboard = (e) => {
      if (e.ctrlKey || e.metaKey) {
        if (e.key === 'k') {
          e.preventDefault()
          openCommandPalette()
        }
      }
    }
    
    // Dynamic greeting based on time
    const updateGreeting = () => {
      const hour = new Date().getHours()
      if (hour < 12) {
        dynamicGreeting.value = 'Bom dia,'
        greetingEmoji.value = '☀️'
      } else if (hour < 18) {
        dynamicGreeting.value = 'Boa tarde,'
        greetingEmoji.value = '🌟'
      } else {
        dynamicGreeting.value = 'Boa noite,'
        greetingEmoji.value = '🌙'
      }
    }
    
    // Neural activity animation
    const animateNeuralActivity = () => {
      setInterval(() => {
        neuralActivity.value = neuralActivity.value.map(() => 
          60 + Math.floor(Math.random() * 40)
        )
      }, 2000)
    }
    
    // Lifecycle
    onMounted(() => {
      updateGreeting()
      animateNeuralActivity()
      document.addEventListener('keydown', handleKeyboard)
      
      // Initialize 3D knowledge graph
      // initializeKnowledgeGraph()
      
      // Create charts
      // createPerformanceChart()
    })
    
    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleKeyboard)
    })
    
    return {
      // User
      userName,
      userInitials,
      streakDays,
      dynamicGreeting,
      greetingEmoji,
      aiStatus,
      neuralActivity,
      
      // Stats
      liveStats,
      
      // AI Tools
      aiTools,
      activePreview,
      showToolPreview,
      hideToolPreview,
      
      // Knowledge Graph
      knowledgeStats,
      rotateGraph,
      centerGraph,
      
      // Study Flow
      flowView,
      studyTimeline,
      kanbanColumns,
      currentMonth,
      calendarDays,
      onKanbanChange,
      
      // Content Hub
      secondBrainNodes,
      latestVideo,
      examStats,
      
      // Analytics
      selectedRange,
      timeRanges,
      performanceInsights,
      learningPath,
      riskFactors,
      
      // AI Assistant
      assistantExpanded,
      hasNotifications,
      activeAssistantTab,
      assistantTabs,
      chatMessages,
      chatInput,
      aiInsights,
      quickAIActions,
      toggleAssistant,
      sendMessage,
      
      // Notifications
      showNotifications,
      notifications,
      
      // Command Palette
      showCommandPalette,
      commandQuery,
      selectedCommandIndex,
      filteredCommands,
      executeCommand,
      closeCommandPalette,
      
      // Methods
      navigateTo,
      getRiskColor
    }
  }
}
</script>

<style scoped>
/* Ultra Advanced Styles */
.home-ultra-advanced {
  min-height: 100vh;
  background: #0a0b14;
  color: #ffffff;
  overflow-x: hidden;
  position: relative;
}

/* Aurora Background */
.aurora-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.aurora {
  position: absolute;
  width: 200%;
  height: 200%;
  opacity: 0.3;
  filter: blur(60px);
  animation: aurora-drift 20s ease-in-out infinite;
}

.aurora-1 {
  background: radial-gradient(ellipse at center, #667eea 0%, transparent 70%);
  top: -50%;
  left: -50%;
}

.aurora-2 {
  background: radial-gradient(ellipse at center, #48bb78 0%, transparent 70%);
  bottom: -50%;
  right: -50%;
  animation-delay: -7s;
}

.aurora-3 {
  background: radial-gradient(ellipse at center, #ed8936 0%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -14s;
}

@keyframes aurora-drift {
  0%, 100% { transform: rotate(0deg) translateY(0); }
  33% { transform: rotate(120deg) translateY(-10%); }
  66% { transform: rotate(240deg) translateY(10%); }
}

.neural-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(102, 126, 234, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(102, 126, 234, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 10s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Glassmorphic Header */
.ultra-header-glass {
  position: relative;
  z-index: 10;
  padding: 2rem 0;
  background: rgba(13, 14, 23, 0.6);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-inner {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  gap: 3rem;
  flex-wrap: wrap;
}

/* 3D Avatar System */
.avatar-3d-system {
  position: relative;
  width: 120px;
  height: 120px;
}

.avatar-orbit {
  position: absolute;
  inset: -20px;
  pointer-events: none;
}

.orbit-ring {
  position: absolute;
  inset: 0;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 50%;
  animation: orbit-rotate 10s linear infinite;
}

.orbit-ring.ring-2 {
  inset: 10px;
  animation-duration: 15s;
  animation-direction: reverse;
  border-color: rgba(72, 187, 120, 0.2);
}

.orbit-ring.ring-3 {
  inset: 20px;
  animation-duration: 20s;
  border-color: rgba(237, 137, 54, 0.2);
}

@keyframes orbit-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.avatar-core-3d {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: avatar-float 4s ease-in-out infinite;
}

@keyframes avatar-float {
  0%, 100% { transform: translateY(0) rotateY(0deg); }
  50% { transform: translateY(-10px) rotateY(180deg); }
}

.avatar-face {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  box-shadow: 
    0 10px 40px rgba(102, 126, 234, 0.5),
    inset 0 0 20px rgba(255, 255, 255, 0.2);
  animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
  0%, 100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
  50% { border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%; }
}

.avatar-glow {
  position: absolute;
  inset: -30px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, transparent 70%);
  filter: blur(20px);
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
}

.status-badges {
  position: absolute;
  bottom: -10px;
  right: -10px;
  display: flex;
  gap: 0.5rem;
}

.badge-3d {
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.badge-3d.online {
  background: rgba(72, 187, 120, 0.2);
  border-color: rgba(72, 187, 120, 0.5);
  color: #48bb78;
}

.badge-3d.streak {
  background: rgba(237, 137, 54, 0.2);
  border-color: rgba(237, 137, 54, 0.5);
  color: #ed8936;
}

/* Dynamic Welcome */
.welcome-dynamic {
  flex: 1;
}

.welcome-title-3d {
  font-size: 2.5rem;
  font-weight: 300;
  margin: 0 0 1rem;
  display: flex;
  align-items: baseline;
  gap: 1rem;
  flex-wrap: wrap;
}

.greeting-text {
  opacity: 0.9;
}

.user-name-glow {
  font-weight: 600;
  background: linear-gradient(135deg, #667eea, #48bb78);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.5));
}

.emoji-animate {
  font-size: 2rem;
  animation: emoji-bounce 2s ease-in-out infinite;
}

@keyframes emoji-bounce {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(10deg); }
}

.ai-status-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  width: fit-content;
}

.ai-pulse {
  width: 8px;
  height: 8px;
  background: #48bb78;
  border-radius: 50%;
  box-shadow: 0 0 10px #48bb78;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

.neural-activity {
  display: flex;
  gap: 3px;
  align-items: flex-end;
  height: 20px;
}

.activity-bar {
  width: 3px;
  background: linear-gradient(to top, #667eea, #48bb78);
  border-radius: 2px;
  transition: height 0.5s ease;
}

/* Stats Orbs */
.stats-orbs {
  display: flex;
  gap: 2rem;
}

.orb-container {
  position: relative;
}

.orb-3d {
  position: relative;
  width: 120px;
  height: 120px;
  background: conic-gradient(
    from 180deg at 50% 50%,
    var(--orb-color) 0deg,
    transparent 180deg,
    var(--orb-color) 360deg
  );
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 0 40px var(--orb-color),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
  animation: orb-rotate 10s linear infinite;
}

@keyframes orb-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.orb-value {
  font-size: 1.8rem;
  font-weight: 700;
  z-index: 1;
}

.orb-label {
  font-size: 0.8rem;
  opacity: 0.8;
  z-index: 1;
}

.orb-progress {
  position: absolute;
  inset: -5px;
  pointer-events: none;
}

.orb-progress svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

/* Sections */
section {
  position: relative;
  z-index: 10;
  padding: 4rem 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

/* Hologram Title */
.section-title-hologram {
  position: relative;
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
}

.hologram-text {
  position: relative;
  z-index: 2;
  background: linear-gradient(45deg, #667eea, #48bb78, #ed8936);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: brightness(1.2);
}

.hologram-shadow {
  position: absolute;
  top: 2px;
  left: 2px;
  z-index: 1;
  opacity: 0.3;
  filter: blur(3px);
  background: linear-gradient(45deg, #667eea, #48bb78, #ed8936);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Hexagon Grid */
.hexagon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  justify-items: center;
  margin-bottom: 3rem;
}

.hexagon-wrapper {
  position: relative;
  width: 120px;
  height: 140px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.hexagon-wrapper:hover {
  transform: translateY(-10px);
}

.hexagon {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--hex-color);
  clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.hexagon::before {
  content: '';
  position: absolute;
  inset: 3px;
  background: rgba(13, 14, 23, 0.9);
  clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
}

.hex-content {
  position: relative;
  z-index: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.hex-content i {
  font-size: 2rem;
  color: var(--hex-color);
}

.hex-title {
  font-size: 0.9rem;
  font-weight: 600;
}

.hex-count {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #e53e3e;
  color: white;
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 20px;
  font-weight: 600;
}

.hex-glow {
  position: absolute;
  inset: -20px;
  background: radial-gradient(circle, var(--hex-color) 0%, transparent 70%);
  opacity: 0;
  filter: blur(20px);
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.hexagon-wrapper:hover .hex-glow {
  opacity: 0.5;
}

/* Tool Preview Panel */
.tool-preview-panel {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 2rem;
  background: rgba(13, 14, 23, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  min-width: 300px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.preview-header i {
  font-size: 2rem;
  color: #667eea;
}

.preview-header h3 {
  font-size: 1.3rem;
  margin: 0;
}

.preview-stats {
  display: flex;
  gap: 2rem;
  margin: 1.5rem 0;
}

.preview-stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  font-size: 0.8rem;
  opacity: 0.7;
}

.preview-actions {
  margin-top: 1.5rem;
}

.action-primary {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 10px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.action-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

/* Preview animation */
.preview-enter-active,
.preview-leave-active {
  transition: all 0.3s ease;
}

.preview-enter-from,
.preview-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-20px);
}

/* Knowledge Graph Section */
.knowledge-graph-section {
  background: rgba(13, 14, 23, 0.5);
  border-radius: 30px;
  padding: 3rem;
  margin: 2rem auto;
}

.section-title-glow {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.section-title-glow i {
  color: #667eea;
}

.knowledge-3d-view {
  position: relative;
  height: 400px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 2rem;
}

.knowledge-canvas {
  width: 100%;
  height: 100%;
}

.graph-controls {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  padding: 0.5rem;
  border-radius: 50px;
}

.graph-controls button {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.graph-controls button:hover {
  background: rgba(102, 126, 234, 0.3);
  transform: scale(1.1);
}

.knowledge-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.k-stat {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  transition: all 0.3s ease;
}

.k-stat:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.k-stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.k-value {
  font-size: 1.5rem;
  font-weight: 700;
}

.k-label {
  font-size: 0.9rem;
  opacity: 0.7;
}

/* Study Flow System */
.study-flow-system {
  background: rgba(13, 14, 23, 0.5);
  border-radius: 30px;
  padding: 3rem;
  margin: 2rem auto;
}

.flow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.flow-header h2 {
  font-size: 1.8rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.flow-controls {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem;
  border-radius: 10px;
}

.flow-controls button {
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.flow-controls button.active {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

/* Timeline View */
.flow-timeline {
  padding: 2rem 0;
}

.timeline-track {
  position: relative;
  padding-left: 4rem;
}

.timeline-node {
  position: relative;
  margin-bottom: 3rem;
}

.node-time {
  position: absolute;
  left: -4rem;
  top: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.7;
}

.node-marker {
  position: absolute;
  left: -1.5rem;
  top: 0;
  width: 3rem;
  height: 3rem;
  background: rgba(102, 126, 234, 0.2);
  border: 2px solid #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.timeline-node.completed .node-marker {
  background: rgba(72, 187, 120, 0.2);
  border-color: #48bb78;
}

.timeline-node.active .node-marker {
  background: rgba(237, 137, 54, 0.2);
  border-color: #ed8936;
}

.node-pulse {
  position: absolute;
  inset: -10px;
  border: 2px solid #ed8936;
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;
}

@keyframes pulse-ring {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(1.5); opacity: 0; }
}

.node-content {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  margin-left: 2rem;
}

.node-content h4 {
  margin: 0 0 0.5rem;
  font-size: 1.1rem;
}

.node-content p {
  margin: 0 0 1rem;
  opacity: 0.8;
}

.node-actions {
  display: flex;
  gap: 0.5rem;
}

.node-action {
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid #667eea;
  border-radius: 8px;
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
}

.node-action:hover {
  background: #667eea;
  color: white;
}

.timeline-connector {
  position: absolute;
  left: -0.5rem;
  top: 3.5rem;
  width: 2px;
  height: calc(100% + 2rem);
  background: linear-gradient(to bottom, #667eea, transparent);
  z-index: 1;
}

/* Kanban View */
.kanban-board {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.kanban-column {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 1.5rem;
  min-height: 400px;
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  border-bottom: 2px solid;
}

.column-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.column-count {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
}

.column-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: 300px;
}

.kanban-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  cursor: move;
  transition: all 0.3s ease;
}

.kanban-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.item-priority {
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 5px;
  font-weight: 600;
}

.item-priority.high {
  background: rgba(229, 62, 62, 0.2);
  color: #e53e3e;
}

.item-priority.medium {
  background: rgba(237, 137, 54, 0.2);
  color: #ed8936;
}

.item-priority.low {
  background: rgba(72, 187, 120, 0.2);
  color: #48bb78;
}

.kanban-item h4 {
  margin: 0 0 0.5rem;
  font-size: 1rem;
}

.kanban-item p {
  margin: 0 0 1rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.item-time {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.85rem;
  opacity: 0.7;
}

.item-tags {
  display: flex;
  gap: 0.5rem;
}

.tag {
  background: rgba(102, 126, 234, 0.2);
  padding: 0.2rem 0.5rem;
  border-radius: 5px;
  font-size: 0.75rem;
  color: #667eea;
}

/* Calendar View */
.calendar-grid {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 1.5rem;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.calendar-header h3 {
  margin: 0;
  font-size: 1.3rem;
}

.calendar-header button {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.calendar-header button:hover {
  background: rgba(102, 126, 234, 0.3);
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.calendar-day {
  aspect-ratio: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.calendar-day:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.calendar-day.has-events {
  border-color: rgba(102, 126, 234, 0.5);
}

.calendar-day.is-today {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.day-number {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.day-events {
  display: flex;
  gap: 2px;
  margin-top: auto;
}

.mini-event {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

/* Flow transitions */
.flow-transition-enter-active,
.flow-transition-leave-active {
  transition: all 0.5s ease;
}

.flow-transition-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.flow-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* Content Universe */
.immersive-content-hub {
  padding: 4rem 2rem;
  background: linear-gradient(180deg, transparent, rgba(102, 126, 234, 0.05), transparent);
}

.glitch-text {
  position: relative;
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  text-transform: uppercase;
  letter-spacing: 3px;
}

.glitch-text span {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.glitch-text span:nth-child(1) {
  animation: glitch-1 3s infinite linear;
  color: #667eea;
  z-index: 2;
}

.glitch-text span:nth-child(2) {
  animation: glitch-2 3s infinite linear;
  color: #48bb78;
  z-index: 1;
}

.glitch-text span:nth-child(3) {
  animation: glitch-3 3s infinite linear;
  color: #ed8936;
  z-index: 0;
}

@keyframes glitch-1 {
  0%, 100% { clip-path: inset(0 0 0 0); transform: translate(0); }
  20% { clip-path: inset(20% 0 60% 0); transform: translate(-2px, 2px); }
  40% { clip-path: inset(50% 0 20% 0); transform: translate(2px, -2px); }
  60% { clip-path: inset(10% 0 80% 0); transform: translate(-2px, -2px); }
  80% { clip-path: inset(80% 0 10% 0); transform: translate(2px, 2px); }
}

@keyframes glitch-2 {
  0%, 100% { clip-path: inset(0 0 0 0); transform: translate(0); }
  20% { clip-path: inset(80% 0 10% 0); transform: translate(2px, -2px); }
  40% { clip-path: inset(10% 0 80% 0); transform: translate(-2px, 2px); }
  60% { clip-path: inset(50% 0 20% 0); transform: translate(2px, 2px); }
  80% { clip-path: inset(20% 0 60% 0); transform: translate(-2px, -2px); }
}

@keyframes glitch-3 {
  0%, 100% { clip-path: inset(0 0 0 0); transform: translate(0); }
  20% { clip-path: inset(50% 0 20% 0); transform: translate(-2px, -2px); }
  40% { clip-path: inset(80% 0 10% 0); transform: translate(2px, 2px); }
  60% { clip-path: inset(20% 0 60% 0); transform: translate(-2px, 2px); }
  80% { clip-path: inset(10% 0 80% 0); transform: translate(2px, -2px); }
}

.content-galaxy {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.galaxy-cluster {
  position: relative;
  background: rgba(13, 14, 23, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 2rem;
  transition: all 0.5s ease;
  overflow: hidden;
}

.galaxy-cluster::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  animation: galaxy-rotate 20s linear infinite;
  pointer-events: none;
}

@keyframes galaxy-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.galaxy-cluster:hover {
  transform: translateY(-5px) scale(1.02);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

.cluster-core {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 1.5rem;
}

.cluster-core i {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1rem;
  display: block;
}

.cluster-core h3 {
  font-size: 1.3rem;
  margin: 0;
}

.cluster-nodes {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.node {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.node:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  transform: translateY(-5px);
}

.node i {
  font-size: 1.2rem;
  margin-bottom: 0.25rem;
  color: #667eea;
}

.node span {
  font-size: 0.8rem;
  font-weight: 600;
}

/* Video Preview */
.video-preview {
  position: relative;
  z-index: 1;
}

.preview-thumbnail {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 1rem;
  cursor: pointer;
}

.preview-thumbnail img {
  width: 100%;
  height: auto;
  display: block;
}

.play-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.play-overlay i {
  font-size: 3rem;
  color: white;
  filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.5));
}

.preview-thumbnail:hover .play-overlay {
  background: rgba(0, 0, 0, 0.6);
}

.video-info h4 {
  margin: 0 0 0.5rem;
  font-size: 1.1rem;
}

.video-info p {
  margin: 0 0 0.5rem;
  opacity: 0.8;
}

.video-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  opacity: 0.7;
}

/* Exam Stats 3D */
.exam-stats-3d {
  display: flex;
  justify-content: center;
  gap: 2rem;
  perspective: 1000px;
  position: relative;
  z-index: 1;
}

.stat-cube {
  width: 80px;
  height: 80px;
  transform-style: preserve-3d;
  animation: cube-rotate 10s linear infinite;
  cursor: pointer;
}

.stat-cube:hover {
  animation-play-state: paused;
}

@keyframes cube-rotate {
  from { transform: rotateX(0) rotateY(0); }
  to { transform: rotateX(360deg) rotateY(360deg); }
}

.cube-face {
  position: absolute;
  width: 80px;
  height: 80px;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.cube-face.front { transform: translateZ(40px); }
.cube-face.back { transform: rotateY(180deg) translateZ(40px); }
.cube-face.top { transform: rotateX(90deg) translateZ(40px); }
.cube-face.bottom { transform: rotateX(-90deg) translateZ(40px); }

/* Predictive Analytics */
.predictive-analytics {
  background: rgba(13, 14, 23, 0.5);
  border-radius: 30px;
  padding: 3rem;
  margin: 2rem auto;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.analytics-header h2 {
  font-size: 1.8rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.time-selector {
  display: flex;
  gap: 0.5rem;
}

.time-selector button {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-selector button.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

.predictions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.prediction-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
}

.prediction-card h3 {
  font-size: 1.2rem;
  margin: 0 0 1.5rem;
}

.prediction-chart {
  height: 200px;
  margin-bottom: 1.5rem;
}

.prediction-insights {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.insight i {
  font-size: 1.2rem;
}

.insight p {
  margin: 0;
  font-size: 0.9rem;
  flex: 1;
}

/* Learning Path */
.path-visualization {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.path-node {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.node-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.node-info h4 {
  margin: 0;
  font-size: 1rem;
}

.node-info p {
  margin: 0;
  font-size: 0.85rem;
  opacity: 0.7;
}

.node-connector {
  position: absolute;
  left: 25px;
  top: 50px;
  width: 2px;
  height: 50px;
  background: linear-gradient(to bottom, currentColor, transparent);
  opacity: 0.3;
}

/* Risk Analysis */
.risk-meters {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.risk-meter {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.meter-label {
  font-size: 0.9rem;
  font-weight: 600;
}

.meter-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.meter-fill {
  height: 100%;
  transition: width 0.5s ease;
}

.meter-actions {
  margin-top: 0.5rem;
}

.meter-actions button {
  padding: 0.4rem 0.8rem;
  background: rgba(229, 62, 62, 0.2);
  border: 1px solid #e53e3e;
  border-radius: 6px;
  color: #e53e3e;
  cursor: pointer;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.meter-actions button:hover {
  background: #e53e3e;
  color: white;
}

/* AI Assistant Float */
.ai-assistant-float {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
  background: rgba(13, 14, 23, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.ai-assistant-float.expanded {
  width: 400px;
  height: 600px;
}

.assistant-header {
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.assistant-avatar {
  position: relative;
  width: 40px;
  height: 40px;
}

.avatar-ring {
  position: absolute;
  inset: -5px;
  border: 2px solid #667eea;
  border-radius: 50%;
  animation: ring-pulse 2s ease-in-out infinite;
}

@keyframes ring-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.5; }
}

.assistant-avatar i {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #667eea;
}

.notification-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background: #e53e3e;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.assistant-body {
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
}

.assistant-tabs {
  display: flex;
  padding: 0.5rem;
  gap: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.assistant-tabs button {
  flex: 1;
  padding: 0.5rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.assistant-tabs button.active {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.assistant-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* Chat Container */
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message-bubble {
  max-width: 80%;
  padding: 0.75rem 1rem;
  border-radius: 15px;
  font-size: 0.9rem;
}

.message.ai .message-bubble {
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.message.user .message-bubble {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.chat-input {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-input input {
  flex: 1;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  outline: none;
  transition: all 0.3s ease;
}

.chat-input input:focus {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.08);
}

.chat-input button {
  padding: 0.75rem 1rem;
  background: #667eea;
  border: none;
  border-radius: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chat-input button:hover {
  background: #5a67d8;
}

/* Insights Container */
.insights-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
}

.insight-card i {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.insight-card h4 {
  margin: 0 0 0.5rem;
  font-size: 1.1rem;
}

.insight-card p {
  margin: 0 0 1rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.insight-card button {
  width: 100%;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid #667eea;
  border-radius: 8px;
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
}

.insight-card button:hover {
  background: #667eea;
  color: white;
}

/* Actions Container */
.actions-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.ai-action-btn {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.ai-action-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  transform: translateY(-2px);
}

.ai-action-btn i {
  font-size: 1.5rem;
  color: #667eea;
}

/* Assistant expand animation */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* Notification Center */
.notification-center {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: rgba(13, 14, 23, 0.98);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  transition: right 0.3s ease;
  z-index: 1001;
  overflow-y: auto;
}

.notification-center.active {
  right: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-header h3 {
  margin: 0;
  font-size: 1.3rem;
}

.notification-header button {
  width: 40px;
  height: 40px;
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.notification-header button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.notification-list {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  gap: 1rem;
}

.notification-item.success {
  border-color: rgba(72, 187, 120, 0.3);
}

.notification-item.info {
  border-color: rgba(102, 126, 234, 0.3);
}

.notification-item i {
  font-size: 1.5rem;
  color: #667eea;
}

.notif-content {
  flex: 1;
}

.notif-content h4 {
  margin: 0 0 0.25rem;
  font-size: 1rem;
}

.notif-content p {
  margin: 0 0 0.5rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.notif-time {
  font-size: 0.8rem;
  opacity: 0.6;
}

.notification-item button {
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid #667eea;
  border-radius: 8px;
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.notification-item button:hover {
  background: #667eea;
  color: white;
}

/* Command Palette */
.command-palette-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10vh;
  z-index: 2000;
}

.command-palette {
  width: 90%;
  max-width: 600px;
  background: rgba(13, 14, 23, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
}

.command-palette input {
  width: 100%;
  padding: 1.5rem;
  background: transparent;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1.1rem;
  outline: none;
}

.command-results {
  max-height: 400px;
  overflow-y: auto;
}

.command-result {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.command-result:hover,
.command-result.active {
  background: rgba(102, 126, 234, 0.2);
}

.command-result i {
  font-size: 1.2rem;
  color: #667eea;
  width: 30px;
  text-align: center;
}

.command-info {
  flex: 1;
}

.command-title {
  display: block;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.command-desc {
  display: block;
  font-size: 0.85rem;
  opacity: 0.7;
}

.command-result kbd {
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  font-size: 0.8rem;
  font-family: monospace;
}

/* Palette animation */
.palette-enter-active,
.palette-leave-active {
  transition: all 0.3s ease;
}

.palette-enter-from,
.palette-leave-to {
  opacity: 0;
}

.palette-enter-from .command-palette,
.palette-leave-to .command-palette {
  transform: translateY(-20px) scale(0.95);
}

/* Responsive */
@media (max-width: 1400px) {
  .hexagon-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .content-galaxy {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1024px) {
  .header-inner {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stats-orbs {
    width: 100%;
    justify-content: space-between;
  }
  
  .predictions-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .avatar-3d-system {
    width: 80px;
    height: 80px;
  }
  
  .welcome-title-3d {
    font-size: 1.8rem;
  }
  
  .orb-3d {
    width: 80px;
    height: 80px;
  }
  
  .flow-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .kanban-board {
    grid-template-columns: 1fr;
  }
  
  .ai-assistant-float.expanded {
    width: calc(100vw - 2rem);
    left: 1rem;
    right: 1rem;
  }
  
  .notification-center {
    width: 100%;
    right: -100%;
  }
  
  .command-palette {
    width: 95%;
  }
}

/* Print styles */
@media print {
  .home-ultra-advanced {
    background: white;
    color: black;
  }
  
  .aurora-container,
  .ai-assistant-float,
  .notification-center,
  .command-palette-overlay {
    display: none;
  }
}
</style>