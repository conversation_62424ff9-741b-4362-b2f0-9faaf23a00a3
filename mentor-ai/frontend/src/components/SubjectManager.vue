<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="subject-manager-modal">
      <div class="modal-header">
        <h2><i class="fas fa-folder-plus"></i> Gerenciar Disciplinas</h2>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <!-- Add Subject Form -->
        <div class="add-subject-section">
          <h3>Adicionar Nova Disciplina</h3>
          <form @submit.prevent="addSubject" class="add-subject-form">
            <div class="form-row">
              <input 
                v-model="newSubject.name" 
                type="text" 
                class="form-control" 
                placeholder="Nome da disciplina"
                required
              />
              <input 
                v-model="newSubject.color" 
                type="color" 
                class="color-picker"
              />
              <button type="submit" class="btn-add">
                <i class="fas fa-plus"></i>
              </button>
            </div>
          </form>
        </div>

        <!-- Subjects List -->
        <div class="subjects-list">
          <h3>Disciplinas Existentes</h3>
          <div v-if="subjects.length === 0" class="empty-state">
            <i class="fas fa-folder-open"></i>
            <p>Nenhuma disciplina cadastrada</p>
          </div>
          
          <div v-else class="subject-items">
            <div 
              v-for="subject in subjects" 
              :key="subject.id" 
              class="subject-item"
            >
              <div class="subject-info">
                <div 
                  class="subject-color" 
                  :style="{ backgroundColor: subject.color }"
                ></div>
                <span class="subject-name">{{ subject.name }}</span>
              </div>
              
              <div class="subject-actions">
                <button 
                  @click="editSubject(subject)" 
                  class="btn-edit"
                  title="Editar"
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button 
                  @click="deleteSubject(subject.id)" 
                  class="btn-delete"
                  title="Excluir"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubjectManager',
  data() {
    return {
      newSubject: {
        name: '',
        color: '#6366f1'
      }
    };
  },
  computed: {
    subjects() {
      return this.$store.state.calendar?.subjects || [];
    }
  },
  methods: {
    async addSubject() {
      if (!this.newSubject.name.trim()) return;
      
      try {
        await this.$store.dispatch('calendar/createSubject', this.newSubject);
        this.newSubject = { name: '', color: '#6366f1' };
        this.$emit('update');
      } catch (error) {
        console.error('Erro ao adicionar disciplina:', error);
      }
    },
    
    editSubject(subject) {
      // TODO: Implement edit functionality
      console.log('Edit subject:', subject);
    },
    
    async deleteSubject(subjectId) {
      if (!confirm('Tem certeza que deseja excluir esta disciplina?')) return;
      
      try {
        await this.$store.dispatch('calendar/deleteSubject', subjectId);
        this.$emit('update');
      } catch (error) {
        console.error('Erro ao excluir disciplina:', error);
      }
    }
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.subject-manager-modal {
  background: #1e293b;
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.modal-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0;
}

.modal-header h2 i {
  color: #6366f1;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-body {
  padding: 1.5rem;
}

.add-subject-section,
.subjects-list {
  margin-bottom: 2rem;
}

.add-subject-section h3,
.subjects-list h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #94a3b8;
  margin-bottom: 1rem;
}

.add-subject-form {
  margin-bottom: 1.5rem;
}

.form-row {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.form-control {
  flex: 1;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  color: #e4e6eb;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #6366f1;
  background: rgba(30, 41, 59, 0.8);
}

.color-picker {
  width: 50px;
  height: 40px;
  background: transparent;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  cursor: pointer;
}

.btn-add {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-add:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #64748b;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.subject-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.subject-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 8px;
  transition: all 0.2s;
}

.subject-item:hover {
  background: rgba(30, 41, 59, 0.8);
}

.subject-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.subject-color {
  width: 24px;
  height: 24px;
  border-radius: 6px;
}

.subject-name {
  font-weight: 500;
  color: #e4e6eb;
}

.subject-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-edit,
.btn-delete {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 6px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-edit:hover {
  background: rgba(99, 102, 241, 0.2);
  color: #6366f1;
}

.btn-delete:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}
</style>