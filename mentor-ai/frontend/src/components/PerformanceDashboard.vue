<template>
  <div class="performance-page">
    <!-- Background -->
    <div class="bg-gradient"></div>
    
    <!-- Main Container -->
    <div class="main-container">
      <!-- Left Panel -->
      <aside class="left-panel">
        <div class="panel-container">
          <!-- Header -->
          <header class="panel-header">
            <div class="header-content">
              <div class="header-icon">
                <font-awesome-icon icon="chart-line" />
              </div>
              <div class="header-text">
                <h1>Central de <span>Performance</span></h1>
                <p>Análise completa do seu progresso</p>
              </div>
            </div>
          </header>

          <!-- Navigation Menu -->
          <nav class="panel-nav">
            <button
              v-for="item in navigationItems"
              :key="item.id"
              @click="activeSection = item.id"
              :class="['nav-button', { active: activeSection === item.id }]"
            >
              <font-awesome-icon :icon="item.icon" />
              <span>{{ item.label }}</span>
            </button>
          </nav>

          <!-- Quick Stats -->
          <div class="quick-stats">
            <div class="stat-card">
              <div class="stat-header">
                <div class="stat-icon success">
                  <font-awesome-icon icon="trophy" />
                </div>
                <span class="stat-label">Score Geral</span>
              </div>
              <div class="stat-value">{{ overallScore }}%</div>
            </div>
            
            <div class="stat-card">
              <div class="stat-header">
                <div class="stat-icon fire">
                  <font-awesome-icon icon="fire" />
                </div>
                <span class="stat-label">Dias Seguidos</span>
              </div>
              <div class="stat-value">{{ streakDays }}</div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="quick-actions">
            <button @click="showExportModal = true; console.log('Export clicked:', showExportModal)" class="action-btn" type="button">
              <font-awesome-icon icon="download" />
              <span>Exportar</span>
            </button>
            <button @click="showGoalsModal = true" class="action-btn primary" type="button">
              <font-awesome-icon icon="bullseye" />
              <span>Metas</span>
            </button>
            <button @click="showInsights = true" class="action-btn" type="button">
              <font-awesome-icon icon="lightbulb" />
              <span>Insights</span>
            </button>
          </div>

          <!-- Time Filter Card -->
          <div class="filter-card">
            <h3 class="card-title">
              <font-awesome-icon icon="calendar" />
              <span>Período de Análise</span>
            </h3>
            
            <div class="period-selector">
              <div class="period-buttons">
                <button
                  v-for="period in timePeriods"
                  :key="period.value"
                  @click="selectedPeriod = period.value"
                  :class="['period-btn', { active: selectedPeriod === period.value }]"
                >
                  {{ period.label }}
                </button>
              </div>

              <div class="date-range">
                <div class="date-input">
                  <label>De</label>
                  <input type="date" v-model="startDate" />
                </div>
                <div class="date-input">
                  <label>Até</label>
                  <input type="date" v-model="endDate" />
                </div>
              </div>
            </div>
          </div>

          <!-- AI Insights Card -->
          <div class="insights-card">
            <h3 class="card-title">
              <font-awesome-icon icon="brain" />
              <span>Insights IA</span>
            </h3>
            
            <div class="insights-list">
              <div class="insight-item" v-for="insight in topInsights" :key="insight.id" :class="insight.type">
                <div class="insight-icon" :class="insight.type">
                  <font-awesome-icon :icon="insight.icon" />
                </div>
                <div class="insight-content">
                  <h4>{{ insight.title }}</h4>
                  <p>{{ insight.message }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </aside>

      <!-- Main Content -->
      <main class="main-content">
        <!-- Content Header -->
        <div class="content-header">
          <h2 class="section-title">{{ currentSectionTitle }}</h2>
          <div class="header-actions">
            <button class="header-action-btn" @click="refreshData">
              <font-awesome-icon icon="sync" :class="{ 'fa-spin': isRefreshing }" />
            </button>
            <button class="header-action-btn" @click="toggleFullscreen">
              <font-awesome-icon icon="expand" />
            </button>
          </div>
        </div>

        <!-- Dynamic Content Based on Active Section -->
        <div class="content-area">
          <!-- Overview Section -->
          <section v-if="activeSection === 'overview'" class="overview-section">
            <!-- Hero Performance Card -->
            <div class="hero-performance-card">
              <!-- Background Effects -->
              <div class="hero-bg-effects">
                <div class="floating-orb orb-1"></div>
                <div class="floating-orb orb-2"></div>
                <div class="floating-orb orb-3"></div>
              </div>
              
              <!-- Main Score Display -->
              <div class="hero-score-section">
                <div class="score-circle-enhanced">
                  <svg viewBox="0 0 200 200">
                    <!-- Outer decorative ring -->
                    <circle cx="120" cy="120" r="110" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/>
                    <circle cx="120" cy="120" r="105" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
                    
                    <!-- Background circle -->
                    <circle cx="100" cy="100" r="80" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="10"/>
                    
                    <!-- Progress circle -->
                    <circle 
                      cx="100" 
                      cy="100" 
                      r="80" 
                      fill="none" 
                      stroke="url(#enhancedGradient)" 
                      stroke-width="10"
                      :stroke-dasharray="`${scoreProgress * 0.95} ${scoreCircumference * 0.95}`"
                      stroke-linecap="round"
                      transform="rotate(-90 100 100)"
                      class="score-progress-circle"
                    />
                    
                    <!-- Inner decorative elements -->
                    <circle cx="100" cy="100" r="70" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1" stroke-dasharray="5,5"/>
                    
                    <defs>
                      <linearGradient id="enhancedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#9f7aea;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
                      </linearGradient>
                    </defs>
                  </svg>
                  
                  <div class="score-content">
                    <div class="score-value-enhanced">
                      <span class="score-number">{{ overallScore }}</span>
                      <span class="score-percent">%</span>
                    </div>
                    <div class="score-label-enhanced">Performance Geral</div>
                    <div class="score-subtitle">{{ getPerformanceLevel() }}</div>
                  </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="hero-quick-stats">
                  <div class="quick-stat-item">
                    <div class="stat-icon-wrapper">
                      <font-awesome-icon icon="fire" />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ streakDays }}</div>
                      <div class="stat-label">Dias em sequência</div>
                    </div>
                  </div>
                  
                  <div class="quick-stat-item">
                    <div class="stat-icon-wrapper">
                      <font-awesome-icon icon="trophy" />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ totalAchievements }}</div>
                      <div class="stat-label">Conquistas</div>
                    </div>
                  </div>
                  
                  <div class="quick-stat-item">
                    <div class="stat-icon-wrapper">
                      <font-awesome-icon icon="chart-line" />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">+{{ weeklyGrowth }}%</div>
                      <div class="stat-label">Crescimento semanal</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Detailed Performance Metrics -->
              <div class="hero-details-section">
                <h3 class="details-title">Análise Detalhada</h3>
                
                <div class="performance-breakdown">
                  <div class="breakdown-item" v-for="item in performanceBreakdown" :key="item.id">
                    <div class="breakdown-header">
                      <div class="breakdown-info">
                        <font-awesome-icon :icon="item.icon" :style="{ color: item.color }" />
                        <span>{{ item.label }}</span>
                      </div>
                      <span class="breakdown-value">{{ item.value }}</span>
                    </div>
                    <div class="breakdown-bar">
                      <div class="bar-fill" :style="{ width: item.percentage + '%', backgroundColor: item.color }">
                        <span class="bar-label">{{ item.percentage }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Mini Chart -->
                <div class="mini-chart-container">
                  <h4>Evolução Semanal</h4>
                  <canvas ref="miniChart" height="120"></canvas>
                </div>
              </div>
            </div>

            <!-- Enhanced Metrics Grid -->
            <div class="enhanced-metrics-grid">
              <div class="enhanced-metric-card" v-for="metric in enhancedMetrics" :key="metric.id">
                <!-- Card Background -->
                <div class="card-bg-gradient" :style="{ background: `linear-gradient(135deg, ${metric.color}15, ${metric.color}05)` }"></div>
                
                <div class="metric-content">
                  <div class="metric-header-enhanced">
                    <div class="metric-icon-enhanced" :style="{ backgroundColor: metric.color + '25', color: metric.color }">
                      <font-awesome-icon :icon="metric.icon" />
                    </div>
                    <div class="metric-badge" :class="metric.trend">
                      <font-awesome-icon :icon="metric.trend === 'up' ? 'arrow-trend-up' : 'arrow-trend-down'" />
                      {{ Math.abs(metric.change) }}%
                    </div>
                  </div>
                  
                  <h3 class="metric-title">{{ metric.label }}</h3>
                  <div class="metric-value-enhanced">{{ metric.value }}</div>
                  <p class="metric-description">{{ metric.description }}</p>
                  
                  <!-- Sparkline -->
                  <div class="metric-sparkline">
                    <svg viewBox="0 0 100 40" preserveAspectRatio="none">
                      <polyline
                        fill="none"
                        :stroke="metric.color"
                        stroke-width="2"
                        :points="metric.sparklineData"
                      />
                      <polyline
                        :fill="metric.color + '20'"
                        :points="`0,40 ${metric.sparklineData} 100,40`"
                      />
                    </svg>
                  </div>
                  
                  <div class="metric-footer">
                    <span class="metric-period">Últimos 7 dias</span>
                    <button class="metric-action" @click="viewMetricDetails(metric)">
                      Ver detalhes
                      <font-awesome-icon icon="arrow-right" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Performance Comparison -->
            <div class="comparison-section">
              <div class="section-header">
                <h3>Comparativo de Performance</h3>
                <div class="period-selector">
                  <button 
                    v-for="period in comparisonPeriods" 
                    :key="period.value"
                    @click="selectedComparisonPeriod = period.value"
                    :class="['period-btn', { active: selectedComparisonPeriod === period.value }]"
                  >
                    {{ period.label }}
                  </button>
                </div>
              </div>
              
              <div class="comparison-grid">
                <div class="comparison-card">
                  <h4>Este Período</h4>
                  <div class="comparison-stats">
                    <div class="stat">
                      <span class="stat-label">Score Médio</span>
                      <span class="stat-value">{{ currentPeriodStats.avgScore }}%</span>
                    </div>
                    <div class="stat">
                      <span class="stat-label">Horas Estudadas</span>
                      <span class="stat-value">{{ currentPeriodStats.hours }}h</span>
                    </div>
                    <div class="stat">
                      <span class="stat-label">Exercícios</span>
                      <span class="stat-value">{{ currentPeriodStats.exercises }}</span>
                    </div>
                  </div>
                </div>
                
                <div class="comparison-visual">
                  <div class="comparison-bar current" :style="{ height: currentPeriodStats.avgScore + '%' }">
                    <span>{{ currentPeriodStats.avgScore }}%</span>
                  </div>
                  <div class="vs-indicator">VS</div>
                  <div class="comparison-bar previous" :style="{ height: previousPeriodStats.avgScore + '%' }">
                    <span>{{ previousPeriodStats.avgScore }}%</span>
                  </div>
                </div>
                
                <div class="comparison-card">
                  <h4>Período Anterior</h4>
                  <div class="comparison-stats">
                    <div class="stat">
                      <span class="stat-label">Score Médio</span>
                      <span class="stat-value">{{ previousPeriodStats.avgScore }}%</span>
                    </div>
                    <div class="stat">
                      <span class="stat-label">Horas Estudadas</span>
                      <span class="stat-value">{{ previousPeriodStats.hours }}h</span>
                    </div>
                    <div class="stat">
                      <span class="stat-label">Exercícios</span>
                      <span class="stat-value">{{ previousPeriodStats.exercises }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- AI Predictions & Insights -->
            <div class="ai-insights-section">
              <div class="section-header">
                <div class="header-content">
                  <font-awesome-icon icon="brain" class="brain-icon" />
                  <h3>Insights e Previsões IA</h3>
                </div>
                <button class="refresh-insights-btn" @click="refreshInsights">
                  <font-awesome-icon icon="sync" :class="{ 'fa-spin': isLoadingInsights }" />
                </button>
              </div>
              
              <div class="insights-grid">
                <div class="insight-card" v-for="insight in aiInsights" :key="insight.id" :class="insight.type">
                  <div class="insight-icon">
                    <font-awesome-icon :icon="insight.icon" />
                  </div>
                  <div class="insight-content">
                    <h4>{{ insight.title }}</h4>
                    <p>{{ insight.description }}</p>
                    <div class="insight-action" v-if="insight.actionable">
                      <button @click="takeAction(insight)">
                        {{ insight.actionText }}
                        <font-awesome-icon icon="arrow-right" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Performance Prediction -->
              <div class="prediction-card">
                <h4>Previsão de Performance</h4>
                <div class="prediction-content">
                  <div class="prediction-chart">
                    <canvas ref="predictionChart" height="180"></canvas>
                  </div>
                  <div class="prediction-stats">
                    <div class="prediction-item">
                      <span class="prediction-label">Score Previsto (30 dias)</span>
                      <span class="prediction-value">{{ predictedScore }}%</span>
                    </div>
                    <div class="prediction-item">
                      <span class="prediction-label">Meta Recomendada</span>
                      <span class="prediction-value">{{ recommendedTarget }}%</span>
                    </div>
                    <div class="prediction-item">
                      <span class="prediction-label">Probabilidade de Alcance</span>
                      <span class="prediction-value">{{ achievementProbability }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Enhanced Activity Timeline -->
            <div class="enhanced-timeline-card">
              <div class="timeline-header">
                <h3>Linha do Tempo de Atividades</h3>
                <div class="timeline-filters">
                  <button 
                    v-for="filter in timelineFilters" 
                    :key="filter.value"
                    @click="selectedTimelineFilter = filter.value"
                    :class="['filter-btn', { active: selectedTimelineFilter === filter.value }]"
                  >
                    <font-awesome-icon :icon="filter.icon" />
                    {{ filter.label }}
                  </button>
                </div>
              </div>
              
              <div class="enhanced-timeline">
                <div class="timeline-date-group" v-for="(group, date) in groupedActivities" :key="date">
                  <div class="date-label">{{ formatDateLabel(date) }}</div>
                  <div class="timeline-items">
                    <div class="timeline-item-enhanced" v-for="activity in group" :key="activity.id" :class="activity.type">
                      <div class="timeline-time">{{ formatTime(activity.date) }}</div>
                      <div class="timeline-marker-enhanced">
                        <font-awesome-icon :icon="activity.icon" />
                      </div>
                      <div class="timeline-content-enhanced">
                        <h4>{{ activity.title }}</h4>
                        <p>{{ activity.description }}</p>
                        <div class="timeline-meta">
                          <span class="meta-item" v-if="activity.subject">
                            <font-awesome-icon icon="book" />
                            {{ activity.subject }}
                          </span>
                          <span class="meta-item" v-if="activity.duration">
                            <font-awesome-icon icon="clock" />
                            {{ activity.duration }}
                          </span>
                          <span class="meta-item" v-if="activity.score">
                            <font-awesome-icon icon="star" />
                            {{ activity.score }}/10
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Analytics Section -->
          <section v-if="activeSection === 'analytics'" class="analytics-section">
            <!-- Chart Selector -->
            <div class="chart-selector">
              <button
                v-for="chart in chartTypes"
                :key="chart.id"
                @click="selectedChart = chart.id"
                :class="['chart-btn', { active: selectedChart === chart.id }]"
              >
                <font-awesome-icon :icon="chart.icon" />
                {{ chart.label }}
              </button>
            </div>

            <!-- Main Chart -->
            <div class="chart-container">
              <canvas ref="performanceChart"></canvas>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
              <div class="stat-card-detailed" v-for="stat in detailedStats" :key="stat.id">
                <div class="stat-header">
                  <h4>{{ stat.title }}</h4>
                  <span class="stat-badge" :class="stat.status">{{ stat.status }}</span>
                </div>
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-comparison">
                  <span>vs. período anterior:</span>
                  <span :class="stat.change > 0 ? 'positive' : 'negative'">
                    {{ stat.change > 0 ? '+' : '' }}{{ stat.change }}%
                  </span>
                </div>
              </div>
            </div>
          </section>

          <!-- Subjects Section -->
          <section v-if="activeSection === 'subjects'" class="subjects-section">
            <div class="subjects-grid">
              <div 
                v-for="subject in subjects" 
                :key="subject.id"
                class="subject-card"
                @click="selectedSubject = subject"
                :class="{ selected: selectedSubject?.id === subject.id }"
              >
                <div class="subject-header">
                  <div class="subject-icon" :style="{ backgroundColor: subject.color + '20' }">
                    <font-awesome-icon :icon="subject.icon" :style="{ color: subject.color }" />
                  </div>
                  <div class="subject-info">
                    <h3>{{ subject.name }}</h3>
                    <p>{{ subject.totalTopics }} tópicos</p>
                  </div>
                </div>

                <div class="subject-stats">
                  <div class="circular-progress" :style="{ '--progress': subject.progress + '%', '--color': subject.color }">
                    <svg viewBox="0 0 36 36">
                      <path
                        d="M18 2.0845
                          a 15.9155 15.9155 0 0 1 0 31.831
                          a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="rgba(255,255,255,0.1)"
                        stroke-width="3"
                      />
                      <path
                        d="M18 2.0845
                          a 15.9155 15.9155 0 0 1 0 31.831
                          a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        :stroke="subject.color"
                        stroke-width="3"
                        :stroke-dasharray="`${subject.progress}, 100`"
                      />
                    </svg>
                    <div class="progress-text">{{ subject.progress }}%</div>
                  </div>

                  <div class="subject-metrics">
                    <div class="metric">
                      <span class="metric-label">Média</span>
                      <span class="metric-value">{{ subject.average }}</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Revisões</span>
                      <span class="metric-value">{{ subject.revisions }}</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Tempo</span>
                      <span class="metric-value">{{ subject.studyTime }}h</span>
                    </div>
                  </div>
                </div>

                <div class="subject-footer">
                  <div class="last-study">
                    <font-awesome-icon icon="clock" />
                    Último estudo: {{ formatRelativeDate(subject.lastStudy) }}
                  </div>
                  <button class="subject-action" @click.stop="openSubjectDetails(subject)">
                    <font-awesome-icon icon="arrow-right" />
                  </button>
                </div>
              </div>
            </div>

            <!-- Subject Details Panel -->
            <transition name="slide-fade">
              <div v-if="selectedSubject" class="subject-details">
                <h3>{{ selectedSubject.name }} - Detalhes</h3>
                
                <div class="topics-list">
                  <div class="topic-item" v-for="topic in selectedSubject.topics" :key="topic.id">
                    <div class="topic-status" :class="topic.status"></div>
                    <div class="topic-info">
                      <h4>{{ topic.name }}</h4>
                      <p>{{ topic.completedExercises }}/{{ topic.totalExercises }} exercícios</p>
                    </div>
                    <div class="topic-progress">
                      <span>{{ topic.progress }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </transition>
          </section>

          <!-- Goals Section -->
          <section v-if="activeSection === 'goals'" class="goals-section">
            <!-- Add Goal Button -->
            <button class="add-goal-btn" @click="showGoalsModal = true">
              <font-awesome-icon icon="plus" />
              Nova Meta
            </button>

            <!-- Goals Grid -->
            <div class="goals-grid">
              <div 
                v-for="goal in goals" 
                :key="goal.id"
                class="goal-card"
                :class="goal.status"
              >
                <div class="goal-header">
                  <div class="goal-icon">
                    <font-awesome-icon :icon="goal.icon" />
                  </div>
                  <div class="goal-menu">
                    <button class="menu-btn" @click="toggleGoalMenu(goal.id)">
                      <font-awesome-icon icon="ellipsis-v" />
                    </button>
                  </div>
                </div>

                <h3 class="goal-title">{{ goal.title }}</h3>
                <p class="goal-description">{{ goal.description }}</p>

                <div class="goal-progress">
                  <div class="progress-info">
                    <span>{{ goal.current }}/{{ goal.target }} {{ goal.unit }}</span>
                    <span class="progress-percentage">{{ Math.round((goal.current / goal.target) * 100) }}%</span>
                  </div>
                  <div class="progress-bar">
                    <div 
                      class="progress-fill" 
                      :style="{ width: Math.min((goal.current / goal.target) * 100, 100) + '%' }"
                    ></div>
                  </div>
                </div>

                <div class="goal-footer">
                  <div class="goal-deadline">
                    <font-awesome-icon icon="calendar-alt" />
                    {{ formatDeadline(goal.deadline) }}
                  </div>
                  <div class="goal-status-badge" :class="goal.status">
                    {{ getStatusLabel(goal.status) }}
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Activity Heatmap Section -->
          <section v-if="activeSection === 'activity'" class="activity-section">
            <div class="heatmap-container">
              <div class="heatmap-header">
                <h3>Mapa de Atividades - {{ currentYear }}</h3>
                <div class="heatmap-stats">
                  <span>Total: {{ totalStudyDays }} dias</span>
                  <span>Sequência: {{ maxStreak }} dias</span>
                </div>
              </div>

              <div class="heatmap-wrapper">
                <div class="month-labels">
                  <span v-for="month in months" :key="month">{{ month }}</span>
                </div>
                
                <div class="heatmap-grid">
                  <div class="weekday-labels">
                    <span>Seg</span>
                    <span>Qua</span>
                    <span>Sex</span>
                  </div>
                  
                  <div class="days-grid">
                    <div 
                      v-for="(day, index) in heatmapData" 
                      :key="index"
                      class="day-cell"
                      :class="`level-${day.level}`"
                      :title="`${day.date}: ${day.hours}h de estudo`"
                      @click="showDayDetails(day)"
                    ></div>
                  </div>
                </div>

                <div class="heatmap-legend">
                  <span>Menos</span>
                  <div class="legend-cells">
                    <div class="day-cell level-0"></div>
                    <div class="day-cell level-1"></div>
                    <div class="day-cell level-2"></div>
                    <div class="day-cell level-3"></div>
                    <div class="day-cell level-4"></div>
                  </div>
                  <span>Mais</span>
                </div>
              </div>

              <!-- Study Patterns -->
              <div class="study-patterns">
                <h3>Padrões de Estudo</h3>
                <div class="patterns-grid">
                  <div class="pattern-card">
                    <div class="pattern-icon">
                      <font-awesome-icon icon="sun" />
                    </div>
                    <h4>Manhã</h4>
                    <p>{{ morningPercentage }}% das sessões</p>
                  </div>
                  <div class="pattern-card">
                    <div class="pattern-icon">
                      <font-awesome-icon icon="cloud-sun" />
                    </div>
                    <h4>Tarde</h4>
                    <p>{{ afternoonPercentage }}% das sessões</p>
                  </div>
                  <div class="pattern-card">
                    <div class="pattern-icon">
                      <font-awesome-icon icon="moon" />
                    </div>
                    <h4>Noite</h4>
                    <p>{{ nightPercentage }}% das sessões</p>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>

    <!-- Export Modal -->
    <transition name="modal">
      <div v-if="showExportModal" class="modal-overlay" @click.self="showExportModal = false">
        <div class="modal">
          <div class="modal-header">
            <h3>Exportar Relatório</h3>
            <button class="close-btn" @click="showExportModal = false">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          
          <div class="modal-body">
            <div class="export-options">
              <label class="export-option">
                <input type="radio" name="format" value="pdf" v-model="exportFormat">
                <div class="option-card">
                  <font-awesome-icon icon="file-pdf" />
                  <span>PDF</span>
                </div>
              </label>
              
              <label class="export-option">
                <input type="radio" name="format" value="excel" v-model="exportFormat">
                <div class="option-card">
                  <font-awesome-icon icon="file-excel" />
                  <span>Excel</span>
                </div>
              </label>
              
              <label class="export-option">
                <input type="radio" name="format" value="csv" v-model="exportFormat">
                <div class="option-card">
                  <font-awesome-icon icon="file-csv" />
                  <span>CSV</span>
                </div>
              </label>
            </div>

            <div class="export-sections">
              <h4>Incluir seções:</h4>
              <label v-for="section in exportSections" :key="section.id" class="checkbox-label">
                <input type="checkbox" v-model="section.included">
                <span>{{ section.label }}</span>
              </label>
            </div>
          </div>
          
          <div class="modal-footer">
            <button class="btn-secondary" @click="showExportModal = false">Cancelar</button>
            <button class="btn-primary" @click="exportReport">
              <font-awesome-icon icon="download" />
              Exportar
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Goals Modal -->
    <transition name="modal">
      <div v-if="showGoalsModal" class="modal-overlay" @click.self="showGoalsModal = false">
        <div class="modal">
          <div class="modal-header">
            <h3>Nova Meta</h3>
            <button class="close-btn" @click="showGoalsModal = false">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          
          <div class="modal-body">
            <form @submit.prevent="createGoal">
              <div class="form-group">
                <label>Título</label>
                <input 
                  type="text" 
                  v-model="newGoal.title" 
                  placeholder="Ex: Completar módulo de Anatomia"
                  required
                >
              </div>

              <div class="form-group">
                <label>Descrição</label>
                <textarea 
                  v-model="newGoal.description" 
                  placeholder="Descreva sua meta..."
                  rows="3"
                ></textarea>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>Meta</label>
                  <input 
                    type="number" 
                    v-model="newGoal.target" 
                    placeholder="100"
                    required
                  >
                </div>

                <div class="form-group">
                  <label>Unidade</label>
                  <select v-model="newGoal.unit" required>
                    <option value="horas">Horas</option>
                    <option value="exercícios">Exercícios</option>
                    <option value="tópicos">Tópicos</option>
                    <option value="pontos">Pontos</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label>Prazo</label>
                <input 
                  type="date" 
                  v-model="newGoal.deadline"
                  :min="today"
                  required
                >
              </div>

              <div class="form-group">
                <label>Ícone</label>
                <div class="icon-selector">
                  <button
                    v-for="icon in goalIcons"
                    :key="icon"
                    type="button"
                    @click="newGoal.icon = icon"
                    :class="['icon-option', { selected: newGoal.icon === icon }]"
                  >
                    <font-awesome-icon :icon="icon" />
                  </button>
                </div>
              </div>

              <div class="form-actions">
                <button type="button" class="btn-secondary" @click="showGoalsModal = false">
                  Cancelar
                </button>
                <button type="submit" class="btn-primary">
                  Criar Meta
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </transition>

    <!-- Insights Modal -->
    <transition name="modal">
      <div v-if="showInsights" class="modal-overlay" @click.self="showInsights = false">
        <div class="modal insights-modal">
          <div class="modal-header">
            <h3>
              <font-awesome-icon icon="brain" />
              Central de Insights
            </h3>
            <button class="close-btn" @click="showInsights = false">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          
          <div class="modal-body">
            <!-- Insights Tabs -->
            <div class="insights-tabs">
              <button 
                v-for="tab in insightsTabs" 
                :key="tab.id"
                @click="activeInsightTab = tab.id"
                :class="['insight-tab', { active: activeInsightTab === tab.id }]"
              >
                <font-awesome-icon :icon="tab.icon" />
                {{ tab.label }}
              </button>
            </div>

            <!-- Performance Insights -->
            <div v-if="activeInsightTab === 'performance'" class="insights-content">
              <h4>Análise de Performance</h4>
              <div class="insight-cards">
                <div class="insight-card-modal" v-for="insight in performanceInsights" :key="insight.id">
                  <div class="insight-header">
                    <div class="insight-icon-modal" :class="insight.type">
                      <font-awesome-icon :icon="insight.icon" />
                    </div>
                    <div class="insight-info">
                      <h5>{{ insight.title }}</h5>
                      <p>{{ insight.description }}</p>
                    </div>
                  </div>
                  <div class="insight-metric">
                    <span class="metric-value">{{ insight.value }}</span>
                    <span class="metric-label">{{ insight.metric }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Recommendations -->
            <div v-if="activeInsightTab === 'recommendations'" class="insights-content">
              <h4>Recomendações Personalizadas</h4>
              <div class="recommendations-list">
                <div class="recommendation-item" v-for="rec in recommendations" :key="rec.id">
                  <div class="rec-priority" :class="rec.priority">
                    {{ rec.priority === 'high' ? 'Alta' : rec.priority === 'medium' ? 'Média' : 'Baixa' }}
                  </div>
                  <div class="rec-content">
                    <h5>{{ rec.title }}</h5>
                    <p>{{ rec.description }}</p>
                    <div class="rec-actions">
                      <button class="rec-action-btn" @click="applyRecommendation(rec)">
                        <font-awesome-icon icon="check" />
                        Aplicar
                      </button>
                      <button class="rec-action-btn secondary" @click="dismissRecommendation(rec)">
                        <font-awesome-icon icon="times" />
                        Dispensar
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Predictions -->
            <div v-if="activeInsightTab === 'predictions'" class="insights-content">
              <h4>Previsões de Desempenho</h4>
              <div class="predictions-chart">
                <canvas ref="predictionsChart"></canvas>
              </div>
              <div class="predictions-summary">
                <div class="prediction-item">
                  <h5>Score Previsto (30 dias)</h5>
                  <p class="prediction-value">{{ predictedScore30 }}%</p>
                  <span class="prediction-trend" :class="predictedTrend30">
                    <font-awesome-icon :icon="predictedTrend30 === 'up' ? 'arrow-up' : 'arrow-down'" />
                    {{ predictedChange30 }}%
                  </span>
                </div>
                <div class="prediction-item">
                  <h5>Meta Recomendada</h5>
                  <p class="prediction-value">{{ recommendedGoal }}%</p>
                  <span class="prediction-info">Baseado no seu progresso atual</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import Chart from 'chart.js/auto'

export default {
  name: 'PerformanceDashboard',
  setup() {
    // Refs
    const performanceChart = ref(null)
    const miniChart = ref(null)
    const predictionChart = ref(null)
    let chartInstance = null
    let miniChartInstance = null
    let predictionChartInstance = null
    
    // State
    const activeSection = ref('overview')
    const selectedPeriod = ref('month')
    const startDate = ref('')
    const endDate = ref('')
    const selectedChart = ref('line')
    const selectedSubject = ref(null)
    const showExportModal = ref(false)
    const showGoalsModal = ref(false)
    const showInsights = ref(false)
    const isRefreshing = ref(false)
    const exportFormat = ref('pdf')
    
    // Data
    const overallScore = ref(87)
    const streakDays = ref(23)
    const bestSubject = ref('Anatomia')
    const completionRate = ref(92)
    const studyTime = ref(145.5)
    const totalStudyDays = ref(156)
    const maxStreak = ref(45)
    const currentYear = ref(new Date().getFullYear())
    const morningPercentage = ref(35)
    const afternoonPercentage = ref(45)
    const nightPercentage = ref(20)
    const totalAchievements = ref(42)
    const weeklyGrowth = ref(12)
    const selectedComparisonPeriod = ref('week')
    const selectedTimelineFilter = ref('all')
    const isLoadingInsights = ref(false)
    const predictedScore = ref(92)
    const recommendedTarget = ref(95)
    const achievementProbability = ref(78)
    
    // Navigation
    const navigationItems = ref([
      { id: 'overview', label: 'Visão Geral', icon: 'home' },
      { id: 'analytics', label: 'Análises', icon: 'chart-line' },
      { id: 'subjects', label: 'Matérias', icon: 'book' },
      { id: 'goals', label: 'Metas', icon: 'bullseye' },
      { id: 'activity', label: 'Atividade', icon: 'calendar-alt' }
    ])
    
    // Time periods
    const timePeriods = ref([
      { value: 'week', label: 'Semana' },
      { value: 'month', label: 'Mês' },
      { value: 'quarter', label: 'Trimestre' },
      { value: 'year', label: 'Ano' }
    ])
    
    // Insights
    const topInsights = ref([
      {
        id: 1,
        type: 'success',
        icon: 'check-circle',
        title: 'Excelente progresso!',
        message: 'Você manteve uma sequência de 23 dias.'
      },
      {
        id: 2,
        type: 'warning',
        icon: 'exclamation-triangle',
        title: 'Atenção em Física',
        message: 'Performance caiu 12% este mês.'
      },
      {
        id: 3,
        type: 'info',
        icon: 'info-circle',
        title: 'Novo recorde',
        message: '145.5 horas estudadas este mês!'
      }
    ])
    
    // Key metrics
    const keyMetrics = ref([
      {
        id: 1,
        icon: 'graduation-cap',
        label: 'Taxa de Aprovação',
        value: '94%',
        change: 8,
        trend: 'up',
        progress: 94,
        color: '#667eea'
      },
      {
        id: 2,
        icon: 'clock',
        label: 'Tempo Médio/Dia',
        value: '4.8h',
        change: -5,
        trend: 'down',
        progress: 80,
        color: '#f093fb'
      },
      {
        id: 3,
        icon: 'tasks',
        label: 'Exercícios Resolvidos',
        value: '1,234',
        change: 15,
        trend: 'up',
        progress: 88,
        color: '#4facfe'
      },
      {
        id: 4,
        icon: 'award',
        label: 'Metas Alcançadas',
        value: '12/15',
        change: 20,
        trend: 'up',
        progress: 80,
        color: '#fa709a'
      }
    ])
    
    // Enhanced metrics for new overview
    const enhancedMetrics = ref([
      {
        id: 1,
        icon: 'brain',
        label: 'Retenção de Conhecimento',
        value: '92%',
        change: 15,
        trend: 'up',
        color: '#667eea',
        description: 'Taxa de memorização após 7 dias',
        sparklineData: '0,20 10,25 20,15 30,30 40,35 50,28 60,45 70,40 80,55 90,50 100,60'
      },
      {
        id: 2,
        icon: 'chart-line',
        label: 'Eficiência de Estudo',
        value: '87%',
        change: 8,
        trend: 'up',
        color: '#f093fb',
        description: 'Aproveitamento do tempo de estudo',
        sparklineData: '0,30 10,35 20,28 30,40 40,38 50,45 60,50 70,48 80,55 90,52 100,58'
      },
      {
        id: 3,
        icon: 'trophy',
        label: 'Ranking Geral',
        value: '#127',
        change: -23,
        trend: 'up',
        color: '#4facfe',
        description: 'Posição entre todos os estudantes',
        sparklineData: '0,40 10,38 20,42 30,35 40,32 50,30 60,28 70,25 80,22 90,20 100,18'
      },
      {
        id: 4,
        icon: 'fire',
        label: 'Consistência',
        value: '96%',
        change: 4,
        trend: 'up',
        color: '#fa709a',
        description: 'Regularidade nos estudos diários',
        sparklineData: '0,50 10,52 20,51 30,53 40,54 50,56 60,55 70,57 80,58 90,59 100,60'
      }
    ])
    
    // Performance breakdown
    const performanceBreakdown = ref([
      { id: 1, label: 'Questões Corretas', value: '892/950', percentage: 94, icon: 'check-circle', color: '#22c55e' },
      { id: 2, label: 'Simulados Completos', value: '18/20', percentage: 90, icon: 'file-alt', color: '#3b82f6' },
      { id: 3, label: 'Revisões no Prazo', value: '156/170', percentage: 92, icon: 'redo', color: '#f59e0b' },
      { id: 4, label: 'Metas Semanais', value: '11/12', percentage: 92, icon: 'bullseye', color: '#8b5cf6' }
    ])
    
    // Comparison periods
    const comparisonPeriods = ref([
      { value: 'week', label: 'Semana' },
      { value: 'month', label: 'Mês' },
      { value: 'quarter', label: 'Trimestre' }
    ])
    
    // Period stats
    const currentPeriodStats = ref({
      avgScore: 87,
      hours: 32,
      exercises: 245
    })
    
    const previousPeriodStats = ref({
      avgScore: 75,
      hours: 28,
      exercises: 198
    })
    
    // AI Insights
    const aiInsights = ref([
      {
        id: 1,
        type: 'success',
        icon: 'lightbulb',
        title: 'Melhor horário identificado',
        description: 'Sua performance é 23% melhor entre 19h-22h. Recomendamos focar matérias difíceis neste período.',
        actionable: true,
        actionText: 'Ajustar cronograma'
      },
      {
        id: 2,
        type: 'warning',
        icon: 'exclamation-triangle',
        title: 'Atenção em Farmacologia',
        description: 'Detectamos queda de 15% na taxa de acerto. Sugerimos revisão intensiva dos últimos tópicos.',
        actionable: true,
        actionText: 'Iniciar revisão'
      },
      {
        id: 3,
        type: 'info',
        icon: 'chart-line',
        title: 'Tendência positiva',
        description: 'Se mantiver o ritmo atual, você alcançará 95% de performance em 3 semanas.',
        actionable: false
      }
    ])
    
    // Timeline filters
    const timelineFilters = ref([
      { value: 'all', label: 'Todas', icon: 'list' },
      { value: 'study', label: 'Estudos', icon: 'book' },
      { value: 'test', label: 'Testes', icon: 'file-alt' },
      { value: 'achievement', label: 'Conquistas', icon: 'trophy' }
    ])
    
    // Recent activities
    const recentActivities = ref([
      {
        id: 1,
        type: 'success',
        icon: 'check-circle',
        title: 'Simulado de Anatomia',
        description: 'Nota 9.2/10 - Melhor resultado até agora!',
        date: new Date(),
        subject: 'Anatomia',
        duration: '2h 30min',
        score: '9.2'
      },
      {
        id: 2,
        type: 'study',
        icon: 'book',
        title: 'Revisão de Fisiologia',
        description: '45 flashcards revisados com 87% de acerto',
        date: new Date(Date.now() - 86400000),
        subject: 'Fisiologia',
        duration: '1h 15min'
      },
      {
        id: 3,
        type: 'milestone',
        icon: 'trophy',
        title: 'Meta Semanal Alcançada',
        description: '25 horas de estudo completadas - novo recorde pessoal!',
        date: new Date(Date.now() - 172800000),
        duration: '25h'
      },
      {
        id: 4,
        type: 'study',
        icon: 'book',
        title: 'Estudo de Farmacologia',
        description: 'Completado capítulo sobre antibióticos',
        date: new Date(Date.now() - 259200000),
        subject: 'Farmacologia',
        duration: '3h 45min'
      },
      {
        id: 5,
        type: 'achievement',
        icon: 'award',
        title: 'Sequência de 30 dias',
        description: 'Manteve consistência de estudos por 30 dias seguidos!',
        date: new Date(Date.now() - 345600000)
      }
    ])
    
    // Chart types
    const chartTypes = ref([
      { id: 'line', label: 'Linha', icon: 'chart-line' },
      { id: 'bar', label: 'Barras', icon: 'chart-bar' },
      { id: 'radar', label: 'Radar', icon: 'chart-area' },
      { id: 'pie', label: 'Pizza', icon: 'chart-pie' }
    ])
    
    // Subjects
    const subjects = ref([
      {
        id: 1,
        name: 'Anatomia',
        icon: 'user-md',
        color: '#667eea',
        progress: 87,
        average: 8.7,
        revisions: 234,
        studyTime: 45.2,
        totalTopics: 24,
        lastStudy: new Date(),
        topics: [
          { id: 1, name: 'Sistema Nervoso', status: 'completed', progress: 100, completedExercises: 45, totalExercises: 45 },
          { id: 2, name: 'Sistema Cardiovascular', status: 'in-progress', progress: 75, completedExercises: 30, totalExercises: 40 },
          { id: 3, name: 'Sistema Respiratório', status: 'pending', progress: 20, completedExercises: 8, totalExercises: 40 }
        ]
      },
      {
        id: 2,
        name: 'Fisiologia',
        icon: 'heartbeat',
        color: '#f093fb',
        progress: 75,
        average: 7.5,
        revisions: 189,
        studyTime: 38.7,
        totalTopics: 20,
        lastStudy: new Date(Date.now() - 86400000),
        topics: []
      },
      {
        id: 3,
        name: 'Farmacologia',
        icon: 'pills',
        color: '#4facfe',
        progress: 92,
        average: 9.2,
        revisions: 312,
        studyTime: 52.1,
        totalTopics: 28,
        lastStudy: new Date(Date.now() - 172800000),
        topics: []
      },
      {
        id: 4,
        name: 'Patologia',
        icon: 'microscope',
        color: '#fa709a',
        progress: 68,
        average: 6.8,
        revisions: 145,
        studyTime: 32.4,
        totalTopics: 18,
        lastStudy: new Date(Date.now() - 259200000),
        topics: []
      }
    ])
    
    // Goals
    const goals = ref([
      {
        id: 1,
        title: 'Completar módulo de Anatomia',
        description: 'Finalizar todos os tópicos e exercícios',
        icon: 'graduation-cap',
        current: 87,
        target: 100,
        unit: 'tópicos',
        deadline: new Date('2024-03-31'),
        status: 'on-track'
      },
      {
        id: 2,
        title: 'Estudar 150 horas este mês',
        description: 'Manter média de 5 horas por dia',
        icon: 'clock',
        current: 145.5,
        target: 150,
        unit: 'horas',
        deadline: new Date('2024-02-29'),
        status: 'completed'
      },
      {
        id: 3,
        title: 'Nota 8+ em todos os simulados',
        description: 'Melhorar performance geral',
        icon: 'star',
        current: 6,
        target: 10,
        unit: 'simulados',
        deadline: new Date('2024-04-15'),
        status: 'at-risk'
      }
    ])
    
    // Export sections
    const exportSections = ref([
      { id: 'overview', label: 'Visão Geral', included: true },
      { id: 'analytics', label: 'Análises', included: true },
      { id: 'subjects', label: 'Performance por Matéria', included: true },
      { id: 'goals', label: 'Metas e Objetivos', included: true },
      { id: 'activity', label: 'Mapa de Atividades', included: false }
    ])
    
    // New goal
    const newGoal = ref({
      title: '',
      description: '',
      target: null,
      unit: 'horas',
      deadline: '',
      icon: 'star'
    })
    
    const goalIcons = ref([
      'star', 'trophy', 'flag', 'bullseye', 'rocket', 'fire', 'bolt', 'gem'
    ])
    
    // Heatmap
    const months = ref(['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'])
    const heatmapData = ref(generateHeatmapData())
    
    // Detailed stats
    const detailedStats = ref([
      { id: 1, title: 'Taxa de Acerto', value: '87%', change: 12, status: 'excellent' },
      { id: 2, title: 'Questões/Dia', value: '45', change: -8, status: 'good' },
      { id: 3, title: 'Tempo Médio', value: '2.5min', change: 15, status: 'warning' },
      { id: 4, title: 'Eficiência', value: '92%', change: 5, status: 'excellent' }
    ])
    
    // Insights Modal Data
    const activeInsightTab = ref('performance')
    const insightsTabs = ref([
      { id: 'performance', label: 'Performance', icon: 'chart-line' },
      { id: 'recommendations', label: 'Recomendações', icon: 'lightbulb' },
      { id: 'patterns', label: 'Padrões', icon: 'brain' }
    ])
    
    const performanceInsights = ref([
      {
        id: 1,
        title: 'Taxa de Acerto em Alta',
        description: 'Sua taxa de acerto aumentou 15% nas últimas 2 semanas',
        type: 'positive',
        metric: '+15%',
        icon: 'trending-up'
      },
      {
        id: 2,
        title: 'Eficiência de Estudo',
        description: 'Você está 23% mais eficiente entre 19h-22h',
        type: 'info',
        metric: '19h-22h',
        icon: 'clock'
      },
      {
        id: 3,
        title: 'Área de Atenção',
        description: 'Farmacologia precisa de revisão - queda de 12% detectada',
        type: 'warning',
        metric: '-12%',
        icon: 'alert-triangle'
      }
    ])
    
    const recommendations = ref([
      {
        id: 1,
        title: 'Otimize seu Horário de Estudo',
        description: 'Baseado em sua performance, recomendamos focar matérias difíceis entre 19h-22h',
        action: 'Ajustar Cronograma',
        priority: 'high',
        icon: 'calendar'
      },
      {
        id: 2,
        title: 'Revisão Intensiva',
        description: 'Revisar Farmacologia nos próximos 3 dias para melhorar retenção',
        action: 'Iniciar Revisão',
        priority: 'medium',
        icon: 'book-open'
      },
      {
        id: 3,
        title: 'Mantenha a Consistência',
        description: 'Sua sequência de 24 dias é excelente! Continue assim para maximizar resultados',
        action: 'Ver Progresso',
        priority: 'low',
        icon: 'award'
      }
    ])
    
    const patterns = ref([
      {
        id: 1,
        title: 'Melhor Performance à Noite',
        data: [
          { time: '6h-9h', score: 72 },
          { time: '9h-12h', score: 78 },
          { time: '12h-15h', score: 75 },
          { time: '15h-18h', score: 82 },
          { time: '18h-21h', score: 91 },
          { time: '21h-24h', score: 88 }
        ]
      },
      {
        id: 2,
        title: 'Progresso por Dia da Semana',
        data: [
          { day: 'Seg', hours: 4.5, score: 85 },
          { day: 'Ter', hours: 5.2, score: 87 },
          { day: 'Qua', hours: 4.8, score: 86 },
          { day: 'Qui', hours: 5.5, score: 89 },
          { day: 'Sex', hours: 3.2, score: 82 },
          { day: 'Sáb', hours: 6.1, score: 91 },
          { day: 'Dom', hours: 4.3, score: 88 }
        ]
      }
    ])
    
    // Computed
    const scoreCircumference = computed(() => 2 * Math.PI * 85)
    const scoreProgress = computed(() => (overallScore.value / 100) * scoreCircumference.value)
    const today = computed(() => new Date().toISOString().split('T')[0])
    
    const currentSectionTitle = computed(() => {
      const section = navigationItems.value.find(item => item.id === activeSection.value)
      return section ? section.label : ''
    })
    
    const groupedActivities = computed(() => {
      const filtered = selectedTimelineFilter.value === 'all' 
        ? recentActivities.value 
        : recentActivities.value.filter(a => a.type === selectedTimelineFilter.value)
      
      return filtered.reduce((groups, activity) => {
        const date = activity.date.toDateString()
        if (!groups[date]) groups[date] = []
        groups[date].push(activity)
        return groups
      }, {})
    })
    
    // Methods
    function generateHeatmapData() {
      const data = []
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - 11)
      startDate.setDate(1)
      
      for (let i = 0; i < 365; i++) {
        const date = new Date(startDate)
        date.setDate(date.getDate() + i)
        
        data.push({
          date: date.toISOString().split('T')[0],
          level: Math.floor(Math.random() * 5),
          hours: Math.floor(Math.random() * 8)
        })
      }
      
      return data
    }
    
    function initChart() {
      const ctx = performanceChart.value?.getContext('2d')
      if (!ctx) return
      
      if (chartInstance) {
        chartInstance.destroy()
      }
      
      // Dados dinâmicos baseados no tipo de gráfico
      const chartData = getChartData()
      
      const chartConfig = {
        type: selectedChart.value,
        data: chartData,
        options: getChartOptions()
      }
      
      chartInstance = new Chart(ctx, chartConfig)
    }
    
    function getChartData() {
      const labels = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun']
      
      switch (selectedChart.value) {
        case 'line':
          return {
            labels,
            datasets: [
              {
                label: 'Performance Score',
                data: [75, 78, 82, 79, 85, 87],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
              },
              {
                label: 'Taxa de Acerto',
                data: [80, 82, 78, 85, 88, 92],
                borderColor: '#f093fb',
                backgroundColor: 'rgba(240, 147, 251, 0.1)',
                tension: 0.4,
                fill: true
              }
            ]
          }
          
        case 'bar':
          return {
            labels,
            datasets: [{
              label: 'Horas de Estudo',
              data: [120, 135, 128, 145, 152, 165],
              backgroundColor: [
                'rgba(102, 126, 234, 0.8)',
                'rgba(240, 147, 251, 0.8)',
                'rgba(79, 172, 254, 0.8)',
                'rgba(250, 112, 154, 0.8)',
                'rgba(102, 126, 234, 0.8)',
                'rgba(240, 147, 251, 0.8)'
              ],
              borderColor: [
                '#667eea',
                '#f093fb',
                '#4facfe',
                '#fa709a',
                '#667eea',
                '#f093fb'
              ],
              borderWidth: 2
            }]
          }
          
        case 'radar':
          return {
            labels: ['Anatomia', 'Fisiologia', 'Farmacologia', 'Patologia', 'Clínica', 'Cirurgia'],
            datasets: [{
              label: 'Performance Atual',
              data: [87, 75, 92, 68, 85, 79],
              backgroundColor: 'rgba(102, 126, 234, 0.2)',
              borderColor: '#667eea',
              pointBackgroundColor: '#667eea',
              pointBorderColor: '#fff',
              pointHoverBackgroundColor: '#fff',
              pointHoverBorderColor: '#667eea'
            },
            {
              label: 'Meta',
              data: [90, 85, 85, 80, 90, 85],
              backgroundColor: 'rgba(240, 147, 251, 0.2)',
              borderColor: '#f093fb',
              pointBackgroundColor: '#f093fb',
              pointBorderColor: '#fff',
              pointHoverBackgroundColor: '#fff',
              pointHoverBorderColor: '#f093fb'
            }]
          }
          
        case 'pie':
          return {
            labels: ['Anatomia', 'Fisiologia', 'Farmacologia', 'Patologia', 'Clínica'],
            datasets: [{
              label: 'Distribuição do Tempo',
              data: [25, 20, 18, 15, 22],
              backgroundColor: [
                'rgba(102, 126, 234, 0.8)',
                'rgba(240, 147, 251, 0.8)',
                'rgba(79, 172, 254, 0.8)',
                'rgba(250, 112, 154, 0.8)',
                'rgba(34, 197, 94, 0.8)'
              ],
              borderColor: [
                '#667eea',
                '#f093fb',
                '#4facfe',
                '#fa709a',
                '#22c55e'
              ],
              borderWidth: 2
            }]
          }
          
        default:
          return {
            labels,
            datasets: [{
              label: 'Performance Score',
              data: [75, 78, 82, 79, 85, 87],
              borderColor: '#667eea',
              backgroundColor: 'rgba(102, 126, 234, 0.1)',
              tension: 0.4,
              fill: true
            }]
          }
      }
    }
    
    function getChartOptions() {
      const baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: selectedChart.value === 'pie' || selectedChart.value === 'radar',
            position: 'bottom',
            labels: {
              color: 'rgba(255, 255, 255, 0.7)',
              font: {
                size: 12
              },
              padding: 20
            }
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: 'white',
            bodyColor: 'white',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1,
            cornerRadius: 8,
            padding: 12
          }
        }
      }
      
      if (selectedChart.value === 'radar') {
        return {
          ...baseOptions,
          scales: {
            r: {
              angleLines: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              pointLabels: {
                color: 'rgba(255, 255, 255, 0.7)',
                font: {
                  size: 12
                }
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.5)',
                backdropColor: 'transparent'
              },
              beginAtZero: true,
              max: 100
            }
          }
        }
      }
      
      if (selectedChart.value === 'pie') {
        return baseOptions
      }
      
      return {
        ...baseOptions,
        scales: {
          y: {
            beginAtZero: true,
            max: selectedChart.value === 'line' ? 100 : undefined,
            grid: {
              color: 'rgba(255, 255, 255, 0.05)'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.5)'
            }
          },
          x: {
            grid: {
              display: false
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.5)'
            }
          }
        }
      }
    }
    
    function formatDate(date) {
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) return 'Hoje'
      if (days === 1) return 'Ontem'
      if (days < 7) return `${days} dias atrás`
      
      return date.toLocaleDateString('pt-BR')
    }
    
    function formatRelativeDate(date) {
      const now = new Date()
      const diff = now - date
      const hours = Math.floor(diff / (1000 * 60 * 60))
      
      if (hours < 24) return `${hours}h atrás`
      const days = Math.floor(hours / 24)
      if (days < 30) return `${days}d atrás`
      
      return date.toLocaleDateString('pt-BR')
    }
    
    function formatDeadline(date) {
      const days = Math.ceil((date - new Date()) / (1000 * 60 * 60 * 24))
      if (days < 0) return 'Expirado'
      if (days === 0) return 'Hoje'
      if (days === 1) return 'Amanhã'
      if (days < 7) return `${days} dias`
      if (days < 30) return `${Math.floor(days / 7)} semanas`
      return `${Math.floor(days / 30)} meses`
    }
    
    function getStatusLabel(status) {
      const labels = {
        'on-track': 'No prazo',
        'completed': 'Concluída',
        'at-risk': 'Em risco'
      }
      return labels[status] || status
    }
    
    async function refreshData() {
      isRefreshing.value = true
      
      try {
        // Simular busca de dados
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Atualizar dados aleatoriamente para demonstração
        overallScore.value = Math.floor(Math.random() * 20) + 80
        streakDays.value = Math.floor(Math.random() * 10) + 20
        completionRate.value = Math.floor(Math.random() * 15) + 85
        studyTime.value = Math.floor(Math.random() * 50) + 120
        
        // Atualizar métricas
        keyMetrics.value.forEach(metric => {
          metric.change = Math.floor(Math.random() * 40) - 20
          metric.trend = metric.change > 0 ? 'up' : 'down'
          metric.progress = Math.floor(Math.random() * 30) + 70
        })
        
        // Re-inicializar gráfico com novos dados
        if (activeSection.value === 'analytics' && chartInstance) {
          initChart()
        }
        
        // Gerar novos insights
        generateNewInsights()
        
      } catch (error) {
        console.error('Erro ao atualizar dados:', error)
      } finally {
        isRefreshing.value = false
      }
    }
    
    function generateNewInsights() {
      const insightTypes = [
        {
          type: 'success',
          icon: 'check-circle',
          messages: [
            'Excelente semana de estudos!',
            'Novo recorde de performance!',
            'Meta semanal alcançada!'
          ]
        },
        {
          type: 'warning',
          icon: 'exclamation-triangle',
          messages: [
            'Revisar conteúdo de Farmacologia',
            'Tempo de estudo abaixo da média',
            'Atenção com prazos próximos'
          ]
        },
        {
          type: 'info',
          icon: 'info-circle',
          messages: [
            'Novo material disponível',
            'Simulado agendado para amanhã',
            'Atualização no cronograma'
          ]
        }
      ]
      
      topInsights.value = insightTypes.map((type, index) => ({
        id: index + 1,
        type: type.type,
        icon: type.icon,
        title: type.messages[Math.floor(Math.random() * type.messages.length)],
        message: `Detalhes sobre ${type.type} #${Math.floor(Math.random() * 100)}`
      }))
    }
    
    function toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
    }
    
    function openSubjectDetails(subject) {
      selectedSubject.value = subject
    }
    
    function showDayDetails(day) {
      // Criar modal ou tooltip com detalhes do dia
      alert(`${day.date}: ${day.hours} horas de estudo`)
    }
    
    async function exportReport() {
      isRefreshing.value = true
      
      try {
        // Simular exportação
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        // Coletar dados para exportação
        const exportData = {
          overview: {
            score: overallScore.value,
            streak: streakDays.value,
            completionRate: completionRate.value,
            studyTime: studyTime.value,
            bestSubject: bestSubject.value
          },
          metrics: keyMetrics.value,
          subjects: subjects.value,
          goals: goals.value,
          activities: recentActivities.value
        }
        
        const selectedSections = exportSections.value.filter(s => s.included)
        
        switch(exportFormat.value) {
          case 'pdf':
            generatePDF(exportData, selectedSections)
            break
          case 'excel':
            generateExcel(exportData, selectedSections)
            break
          case 'csv':
            generateCSV(exportData, selectedSections)
            break
        }
        
        // Mostrar notificação de sucesso
        alert(`Relatório exportado com sucesso em formato ${exportFormat.value.toUpperCase()}!`)
      } catch (error) {
        console.error('Erro ao exportar:', error)
        alert('Erro ao exportar relatório. Tente novamente.')
      } finally {
        isRefreshing.value = false
        showExportModal.value = false
      }
    }
    
    // Helper functions for export
    function generatePDF(data, sections) {
      console.log('Gerando PDF com dados:', data, 'Seções:', sections)
      // Implementação real do PDF viria aqui
      // Usando bibliotecas como jsPDF ou similares
    }
    
    function generateExcel(data, sections) {
      console.log('Gerando Excel com dados:', data, 'Seções:', sections)
      // Implementação real do Excel viria aqui
      // Usando bibliotecas como xlsx ou similares
    }
    
    function generateCSV(data, sections) {
      console.log('Gerando CSV com dados:', data, 'Seções:', sections)
      // Implementação básica de CSV
      let csv = 'Relatório de Performance\n\n'
      
      if (sections.some(s => s.id === 'overview')) {
        csv += 'Visão Geral\n'
        csv += `Score Geral,${data.overview.score}%\n`
        csv += `Sequência de Dias,${data.overview.streak}\n`
        csv += `Taxa de Conclusão,${data.overview.completionRate}%\n`
        csv += `Horas de Estudo,${data.overview.studyTime}\n`
        csv += `Melhor Matéria,${data.overview.bestSubject}\n\n`
      }
      
      // Download do arquivo
      const blob = new Blob([csv], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `relatorio_performance_${new Date().toISOString().split('T')[0]}.csv`
      a.click()
    }
    
    function createGoal() {
      goals.value.push({
        id: Date.now(),
        ...newGoal.value,
        current: 0,
        deadline: new Date(newGoal.value.deadline),
        status: 'on-track'
      })
      
      // Reset form
      newGoal.value = {
        title: '',
        description: '',
        target: null,
        unit: 'horas',
        deadline: '',
        icon: 'star'
      }
      
      showGoalsModal.value = false
    }
    
    function toggleGoalMenu(goalId) {
      console.log('Toggle menu for goal:', goalId)
    }
    
    // Watchers
    watch(selectedChart, () => {
      if (activeSection.value === 'analytics') {
        initChart()
      }
    })
    
    watch(activeSection, (newSection) => {
      if (newSection === 'analytics') {
        setTimeout(() => initChart(), 100)
      } else if (newSection === 'overview') {
        setTimeout(() => {
          initMiniChart()
          initPredictionChart()
        }, 100)
      }
    })
    
    // Lifecycle
    onMounted(() => {
      const now = new Date()
      startDate.value = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0]
      endDate.value = now.toISOString().split('T')[0]
      
      // Garantir que o gráfico seja inicializado após o DOM estar pronto
      nextTick(() => {
        if (activeSection.value === 'analytics') {
          setTimeout(() => initChart(), 200)
        }
      })
      
      // Adicionar animação inicial aos números
      animateNumbers()
    })
    
    function animateNumbers() {
      // Animar score principal
      const targetScore = overallScore.value
      overallScore.value = 0
      const scoreInterval = setInterval(() => {
        if (overallScore.value < targetScore) {
          overallScore.value += 2
        } else {
          overallScore.value = targetScore
          clearInterval(scoreInterval)
        }
      }, 30)
      
      // Animar outras métricas
      const targetStreak = streakDays.value
      streakDays.value = 0
      const streakInterval = setInterval(() => {
        if (streakDays.value < targetStreak) {
          streakDays.value += 1
        } else {
          streakDays.value = targetStreak
          clearInterval(streakInterval)
        }
      }, 50)
    }
    
    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.destroy()
      }
      if (miniChartInstance) {
        miniChartInstance.destroy()
      }
      if (predictionChartInstance) {
        predictionChartInstance.destroy()
      }
    })
    
    // Adicionar método para deletar meta
    function deleteGoal(goalId) {
      if (confirm('Tem certeza que deseja excluir esta meta?')) {
        goals.value = goals.value.filter(g => g.id !== goalId)
      }
    }
    
    // Adicionar método para editar meta
    function editGoal(goal) {
      // Implementar lógica de edição
      console.log('Editando meta:', goal)
    }
    
    // Adicionar método para comparar períodos
    function comparePeriods() {
      // Implementar comparação entre períodos
      console.log('Comparando períodos:', selectedPeriod.value)
    }
    
    // Novos métodos para overview aprimorado
    function getPerformanceLevel() {
      if (overallScore.value >= 90) return 'Excelente Performance'
      if (overallScore.value >= 80) return 'Muito Bom'
      if (overallScore.value >= 70) return 'Bom Progresso'
      if (overallScore.value >= 60) return 'Em Desenvolvimento'
      return 'Precisa Melhorar'
    }
    
    function viewMetricDetails(metric) {
      console.log('Visualizando detalhes da métrica:', metric)
      // Implementar modal ou navegação para detalhes
    }
    
    function refreshInsights() {
      isLoadingInsights.value = true
      setTimeout(() => {
        // Simular atualização de insights
        generateNewInsights()
        isLoadingInsights.value = false
      }, 1500)
    }
    
    function takeAction(insight) {
      console.log('Executando ação do insight:', insight)
      // Implementar ações específicas
    }
    
    function formatDateLabel(date) {
      const d = new Date(date)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      
      if (d.toDateString() === today.toDateString()) return 'Hoje'
      if (d.toDateString() === yesterday.toDateString()) return 'Ontem'
      
      return d.toLocaleDateString('pt-BR', { weekday: 'long', day: 'numeric', month: 'short' })
    }
    
    function formatTime(date) {
      return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
    }
    
    function initMiniChart() {
      const ctx = miniChart.value?.getContext('2d')
      if (!ctx) return
      
      // Destruir instância anterior se existir
      if (miniChartInstance) {
        miniChartInstance.destroy()
      }
      
      miniChartInstance = new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'],
          datasets: [{
            label: 'Score',
            data: [82, 85, 83, 87, 86, 89, 87],
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true,
            pointRadius: 3,
            pointBackgroundColor: '#667eea',
            pointBorderColor: '#fff',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false }
          },
          scales: {
            y: {
              beginAtZero: false,
              min: 70,
              max: 100,
              grid: { display: false },
              ticks: { display: false }
            },
            x: {
              grid: { display: false },
              ticks: { 
                color: 'rgba(255, 255, 255, 0.5)',
                font: { size: 10 }
              }
            }
          }
        }
      })
    }
    
    function initPredictionChart() {
      const ctx = predictionChart.value?.getContext('2d')
      if (!ctx) return
      
      // Destruir instância anterior se existir
      if (predictionChartInstance) {
        predictionChartInstance.destroy()
      }
      
      predictionChartInstance = new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['Atual', 'Sem 1', 'Sem 2', 'Sem 3', 'Sem 4'],
          datasets: [
            {
              label: 'Projeção',
              data: [87, 89, 90, 91, 92],
              borderColor: '#667eea',
              backgroundColor: 'rgba(102, 126, 234, 0.1)',
              borderDash: [5, 5],
              tension: 0.4,
              fill: true
            },
            {
              label: 'Meta',
              data: [87, 90, 92, 94, 95],
              borderColor: '#f093fb',
              borderWidth: 2,
              borderDash: [10, 5],
              fill: false
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { 
              display: true,
              position: 'bottom',
              labels: {
                color: 'rgba(255, 255, 255, 0.7)',
                font: { size: 11 }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: false,
              min: 80,
              max: 100,
              grid: { color: 'rgba(255, 255, 255, 0.05)' },
              ticks: { color: 'rgba(255, 255, 255, 0.5)' }
            },
            x: {
              grid: { display: false },
              ticks: { color: 'rgba(255, 255, 255, 0.5)' }
            }
          }
        }
      })
    }
    
    // Insights methods
    function applyRecommendation(recommendation) {
      console.log('Aplicando recomendação:', recommendation)
      // Implementar ação específica para cada recomendação
      switch(recommendation.id) {
        case 1:
          // Ajustar cronograma
          alert('Abrindo configurações de cronograma...')
          break
        case 2:
          // Iniciar revisão
          alert('Iniciando sessão de revisão de Farmacologia...')
          break
        case 3:
          // Ver progresso
          activeSection.value = 'overview'
          showInsights.value = false
          break
      }
    }
    
    function dismissRecommendation(id) {
      recommendations.value = recommendations.value.filter(r => r.id !== id)
    }
    
    
    return {
      // Refs
      performanceChart,
      miniChart,
      predictionChart,
      
      // State
      activeSection,
      selectedPeriod,
      startDate,
      endDate,
      selectedChart,
      selectedSubject,
      showExportModal,
      showGoalsModal,
      showInsights,
      isRefreshing,
      exportFormat,
      
      // Data
      overallScore,
      streakDays,
      bestSubject,
      completionRate,
      studyTime,
      totalStudyDays,
      maxStreak,
      currentYear,
      morningPercentage,
      afternoonPercentage,
      nightPercentage,
      totalAchievements,
      weeklyGrowth,
      selectedComparisonPeriod,
      selectedTimelineFilter,
      isLoadingInsights,
      predictedScore,
      recommendedTarget,
      achievementProbability,
      navigationItems,
      timePeriods,
      topInsights,
      keyMetrics,
      enhancedMetrics,
      performanceBreakdown,
      comparisonPeriods,
      currentPeriodStats,
      previousPeriodStats,
      aiInsights,
      timelineFilters,
      recentActivities,
      chartTypes,
      subjects,
      goals,
      exportSections,
      newGoal,
      goalIcons,
      months,
      heatmapData,
      detailedStats,
      activeInsightTab,
      insightsTabs,
      performanceInsights,
      recommendations,
      patterns,
      
      // Computed
      scoreCircumference,
      scoreProgress,
      today,
      currentSectionTitle,
      groupedActivities,
      
      // Methods
      formatDate,
      formatRelativeDate,
      formatDeadline,
      getStatusLabel,
      refreshData,
      toggleFullscreen,
      openSubjectDetails,
      showDayDetails,
      exportReport,
      createGoal,
      toggleGoalMenu,
      deleteGoal,
      editGoal,
      comparePeriods,
      getPerformanceLevel,
      viewMetricDetails,
      refreshInsights,
      takeAction,
      formatDateLabel,
      formatTime,
      applyRecommendation,
      dismissRecommendation,
      generatePDF,
      generateExcel,
      generateCSV
    }
  }
}
</script>

<style scoped>
/* Base Styles - Following RevisionScheduler pattern */
.performance-page {
  min-height: calc(100vh - 72px); /* Ajusta para o height do navbar */
  background: #000000;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-x: hidden;
  width: 100%;
  margin-top: 72px; /* Espaço para o navbar */
}

/* Background */
.bg-gradient {
  position: fixed;
  top: 72px; /* Começa abaixo do navbar */
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(240, 147, 251, 0.15) 0%, transparent 50%),
    #000000;
  z-index: 0;
}

/* Main Container */
.main-container {
  position: relative;
  z-index: 1;
  display: flex;
  gap: 2rem;
  max-width: 100%;
  margin: 0;
  padding: 1.5rem;
  min-height: calc(100vh - 72px); /* Ajusta para o navbar */
  width: 100%;
  box-sizing: border-box;
}

/* Left Panel */
.left-panel {
  width: 320px;
  flex-shrink: 0;
  background: #0a0a14;
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  height: calc(100vh - 72px); /* Ajusta para o navbar */
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: sticky;
  top: 72px; /* Fixa abaixo do navbar */
}

.panel-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.3) transparent;
}

.panel-container::-webkit-scrollbar {
  width: 4px;
}

.panel-container::-webkit-scrollbar-track {
  background: transparent;
}

.panel-container::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 2px;
}

.panel-container::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
  box-sizing: border-box;
}

/* Panel Header */
.panel-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08), rgba(99, 102, 241, 0.04));
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 0;
  position: relative;
  overflow: hidden;
}

.panel-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.panel-header::after {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 30% 50%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.panel-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
}

.panel-header:hover .header-icon {
  transform: scale(1.05);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.5);
}

.panel-header:hover .header-text h1 {
  filter: brightness(1.1);
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #9f7aea);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
  height: 52px;
  background: linear-gradient(135deg, #667eea, #f093fb);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  color: white;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.header-icon::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, #667eea, #f093fb);
  border-radius: 16px;
  opacity: 0.3;
  filter: blur(10px);
  z-index: -1;
}

.header-icon::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 16px;
}

.header-text {
  flex: 1;
}

.header-text h1 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
  color: #ffffff;
}

.header-text h1 span {
  color: #8b5cf6;
  font-weight: 700;
}

.header-text h1 span {
  background: linear-gradient(135deg, #667eea, #f093fb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 900;
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.header-text p {
  font-size: 0.813rem;
  color: #94a3b8;
  margin: 0.25rem 0 0;
  line-height: 1.4;
  font-weight: 400;
}

/* Navigation */
.panel-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin: 0;
  padding: 0;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 10px;
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  min-height: 44px;
  box-sizing: border-box;
}

.nav-button::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #8b5cf6;
  transform: translateX(-100%);
  transition: transform 0.2s ease;
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #e2e8f0;
}

.nav-button.active {
  background: rgba(139, 92, 246, 0.1);
  color: #a78bfa;
  border-color: rgba(139, 92, 246, 0.2);
}

.nav-button.active::before {
  transform: translateX(0);
}

.nav-button svg {
  font-size: 1.125rem;
  width: 20px;
  flex-shrink: 0;
}

.nav-button svg {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(4px);
}

.nav-button.active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(240, 147, 251, 0.2));
  border-color: rgba(102, 126, 234, 0.5);
  color: white;
}

/* Stats Row */
.panel-nav {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quick-stats {
  padding: 0 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  padding: 1rem;
  transition: all 0.2s;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(139, 92, 246, 0.3);
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.stat-icon {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.stat-icon.success {
  background: rgba(16, 185, 129, 0.15);
  color: #10b981;
}

.stat-icon.fire {
  background: rgba(245, 158, 11, 0.15);
  color: #f59e0b;
}

.stat-label {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
  margin: 0;
  padding: 0;
}

.stat-card {
  padding: 1.25rem;
  background: rgba(40, 40, 60, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.stat-card svg {
  font-size: 24px;
  color: #667eea;
  margin-bottom: 8px;
  width: 24px;
  height: 24px;
}

.stat-card strong {
  display: block;
  font-size: 24px;
  font-weight: 700;
  margin: 4px 0;
  line-height: 1.2;
}

.stat-card span {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  white-space: nowrap;
  line-height: 1.3;
  margin-top: 2px;
}

/* Quick Actions */
.quick-actions {
  padding: 0 1rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 20;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.375rem;
  padding: 0.75rem 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  color: #94a3b8;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  z-index: 10;
  -webkit-user-select: none;
  user-select: none;
  outline: none;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
  color: #e2e8f0;
  transform: translateY(-1px);
}

.action-btn.primary {
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.2);
  color: #a78bfa;
}

.action-btn.primary:hover {
  background: rgba(139, 92, 246, 0.15);
  border-color: rgba(139, 92, 246, 0.3);
}

.action-btn svg {
  font-size: 1.125rem;
}

.quick-btn {
  padding: 12px 8px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  width: 100%;
  box-sizing: border-box;
  min-height: 48px;
  gap: 6px;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.quick-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  color: white;
  transform: translateY(-2px);
}

/* Goals Button Special Style */
.goals-btn {
  position: relative;
  overflow: hidden;
}

.goals-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(240, 147, 251, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.goals-btn:hover::before {
  opacity: 1;
}

.goals-logo {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.goals-logo svg {
  width: 100%;
  height: 100%;
  color: #667eea;
  transition: all 0.3s ease;
}

.goals-btn:hover .goals-logo svg {
  color: #f093fb;
  transform: rotate(180deg) scale(1.1);
  filter: drop-shadow(0 0 8px rgba(240, 147, 251, 0.6));
}

.quick-btn svg {
  font-size: 18px;
  margin-right: 6px;
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

/* Filter Card */
.filter-card,
.insights-card {
  margin: 0 1rem 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0 0 1rem 0;
}

.card-title svg {
  font-size: 1rem;
  color: #8b5cf6;
  box-sizing: border-box;
  margin: 0;
  backdrop-filter: blur(10px);
}

.period-selector {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.period-selector {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.period-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.375rem;
}

.period-btn {
  padding: 0.5rem 0.75rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 6px;
  color: #94a3b8;
  font-size: 0.688rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s;
  white-space: nowrap;
}

.period-btn:hover {
  background: rgba(255, 255, 255, 0.03);
  border-color: rgba(255, 255, 255, 0.15);
  color: #e2e8f0;
}

.period-btn.active {
  background: rgba(139, 92, 246, 0.15);
  border-color: rgba(139, 92, 246, 0.3);
  color: #a78bfa;
}

.date-range {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.date-input {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date-input label {
  font-size: 0.688rem;
  color: #64748b;
  font-weight: 500;
}

.date-input input {
  padding: 0.375rem 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 4px;
  color: #e2e8f0;
  font-size: 0.75rem;
  width: 100%;
}

.date-input input:focus {
  outline: none;
  border-color: rgba(139, 92, 246, 0.4);
  background: rgba(255, 255, 255, 0.05);
  gap: 10px;
  line-height: 1.3;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.insight-item {
  display: flex;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  transition: all 0.15s;
  border: 1px solid transparent;
}

.insight-item:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.08);
}

.insight-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 0.875rem;
}

.insight-icon.success {
  background: rgba(16, 185, 129, 0.15);
  color: #10b981;
}

.insight-icon.warning {
  background: rgba(245, 158, 11, 0.15);
  color: #f59e0b;
}

.insight-icon.info {
  background: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
}

.insight-content {
  flex: 1;
  min-width: 0;
}

.insight-content h4 {
  font-size: 0.75rem;
  font-weight: 600;
  margin: 0 0 0.125rem 0;
  color: #ffffff;
  line-height: 1.2;
}

.insight-content p {
  font-size: 0.688rem;
  color: #94a3b8;
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Time Filter */
.time-filter-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 1rem;
  width: 100%;
  box-sizing: border-box;
}

.time-btn {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  font-size: 12px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.time-btn.active {
  background: linear-gradient(135deg, #667eea, #f093fb);
  border-color: transparent;
  color: white;
}

/* Date Range */
.date-range {
  display: flex;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
}

.date-input {
  flex: 1;
  min-width: 0;
  box-sizing: border-box;
}

.date-input label {
  display: block;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 6px;
  line-height: 1.3;
}

.date-input input {
  width: 100%;
  padding: 8px 10px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  color: white;
  font-size: 12px;
  box-sizing: border-box;
  min-height: 36px;
  line-height: 1.3;
}

.date-input input:focus {
  outline: none;
  border-color: #667eea;
}

/* Insights */
.insight-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.02);
  width: 100%;
  box-sizing: border-box;
  align-items: flex-start;
  border-radius: 12px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.insight-item:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(4px);
}

.insight-item:last-child {
  margin-bottom: 0;
}

.insight-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 16px;
}

.insight-icon svg {
  width: 16px;
  height: 16px;
}

.insight-icon.success {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.insight-icon.warning {
  background: rgba(251, 146, 60, 0.2);
  color: #fb923c;
}

.insight-icon.info {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.insight-content h4 {
  font-size: 13px;
  font-weight: 600;
  margin: 0 0 4px;
  line-height: 1.3;
}

.insight-content p {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  line-height: 1.4;
}

/* Main Content */
.main-content {
  flex: 1;
  background: rgba(20, 20, 30, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Content Header */
.content-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-action-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.header-action-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
  border-color: #667eea;
}

/* Content Area */
.content-area {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  overflow-x: hidden;
  min-width: 0;
  max-width: calc(100vw - 360px);
  box-sizing: border-box;
}

/* Overview Section - Enhanced */
.overview-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Hero Performance Card */
.hero-performance-card {
  position: relative;
  background: rgba(30, 30, 40, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 2rem;
  overflow: visible;
  backdrop-filter: blur(10px);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.hero-bg-effects {
  position: absolute;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
}

.floating-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.3;
  animation: float 20s ease-in-out infinite;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: #667eea;
  top: -100px;
  right: -100px;
  animation-delay: 0s;
}

.orb-2 {
  width: 200px;
  height: 200px;
  background: #f093fb;
  bottom: -50px;
  left: -50px;
  animation-delay: 7s;
}

.orb-3 {
  width: 250px;
  height: 250px;
  background: #4facfe;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 14s;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
}

.hero-score-section {
  position: relative;
  z-index: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.score-circle-enhanced {
  position: relative;
  width: 200px;
  height: 200px;
  flex-shrink: 0;
}

.score-circle-enhanced svg {
  width: 100%;
  height: 100%;
}

.score-progress-circle {
  filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.5));
  transition: stroke-dasharray 1s ease-out;
}

.score-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.score-value-enhanced {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.score-number {
  font-size: 64px;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea, #f093fb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
}

.score-percent {
  font-size: 32px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
}

.score-label-enhanced {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 8px;
  font-weight: 500;
}

.score-subtitle {
  font-size: 14px;
  color: #667eea;
  margin-top: 4px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Hero Quick Stats */
.hero-quick-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  width: 100%;
  box-sizing: border-box;
  flex: 1;
}

.quick-stat-item {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  padding: 1.25rem;
  background: rgba(40, 40, 60, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 140px;
  box-sizing: border-box;
}

.quick-stat-item:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.stat-icon-wrapper {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #f093fb);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-content .stat-value {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 4px;
  color: #ffffff;
}

.stat-content .stat-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Hero Details Section */
.hero-details-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.details-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 2rem;
  color: #ffffff;
}

.performance-breakdown {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  width: 100%;
  box-sizing: border-box;
}

.breakdown-item {
  background: rgba(50, 50, 70, 0.8);
  border-radius: 12px;
  padding: 0.875rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  box-sizing: border-box;
}

.breakdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.breakdown-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 14px;
  color: #ffffff;
}

.breakdown-info svg {
  font-size: 16px;
}

.breakdown-value {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.breakdown-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.breakdown-bar .bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 1s ease-out;
  position: relative;
  overflow: visible;
}

.bar-label {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  font-weight: 600;
  color: white;
}

/* Mini Chart */
.mini-chart-container {
  background: rgba(50, 50, 70, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.mini-chart-container h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Enhanced Metrics Grid */
.enhanced-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  width: 100%;
  box-sizing: border-box;
}

.enhanced-metric-card {
  position: relative;
  background: rgba(30, 30, 50, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.enhanced-metric-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.1);
}

.card-bg-gradient {
  position: absolute;
  inset: 0;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.enhanced-metric-card:hover .card-bg-gradient {
  opacity: 0.8;
}

.metric-content {
  position: relative;
  z-index: 1;
}

.metric-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.metric-icon-enhanced {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.metric-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.05);
}

.metric-badge.up {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
}

.metric-badge.down {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.metric-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #ffffff;
}

.metric-value-enhanced {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.metric-description {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.85);
  margin: 0 0 1.5rem;
  line-height: 1.5;
}

.metric-sparkline {
  height: 40px;
  margin-bottom: 1rem;
}

.metric-sparkline svg {
  width: 100%;
  height: 100%;
}

.metric-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.metric-period {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.metric-action {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.metric-action:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
  border-color: #667eea;
  transform: translateX(2px);
}

/* Comparison Section */
.comparison-section {
  background: rgba(30, 30, 50, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
}

.period-selector {
  display: flex;
  gap: 8px;
}

.period-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.period-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.period-btn.active {
  background: linear-gradient(135deg, #667eea, #f093fb);
  border-color: transparent;
  color: white;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
}

.comparison-card {
  background: rgba(50, 50, 70, 0.8);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.comparison-card h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 1rem;
  color: #ffffff;
}

.comparison-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.comparison-stats .stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comparison-stats .stat-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.85);
}

.comparison-stats .stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.comparison-visual {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  height: 200px;
  position: relative;
}

.comparison-bar {
  width: 60px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px 8px 0 0;
  position: relative;
  transition: height 0.5s ease-out;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 1rem;
}

.comparison-bar.current {
  background: linear-gradient(to top, #667eea, #9f7aea);
}

.comparison-bar.previous {
  background: linear-gradient(to top, #f093fb, #f5b8fc);
  opacity: 0.8;
}

.comparison-bar span {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.vs-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.5);
  background: rgba(0, 0, 0, 0.5);
  padding: 4px 12px;
  border-radius: 20px;
}

/* AI Insights Section */
.ai-insights-section {
  background: rgba(30, 30, 50, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brain-icon {
  font-size: 24px;
  color: #667eea;
}

.refresh-insights-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-insights-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
  border-color: #667eea;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.insight-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(40, 40, 60, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.1);
}

.insight-card.success {
  border-left: 3px solid #22c55e;
}

.insight-card.warning {
  border-left: 3px solid #fb923c;
}

.insight-card.info {
  border-left: 3px solid #3b82f6;
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 18px;
}

.insight-card.success .insight-icon {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.insight-card.warning .insight-icon {
  background: rgba(251, 146, 60, 0.1);
  color: #fb923c;
}

.insight-card.info .insight-icon {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.insight-content {
  flex: 1;
}

.insight-content h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #ffffff;
}

.insight-content p {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 1rem;
  line-height: 1.5;
}

.insight-action button {
  padding: 6px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.insight-action button:hover {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

/* Prediction Card */
.prediction-card {
  background: rgba(50, 50, 70, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
}

.prediction-card h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: #ffffff;
}

.prediction-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 2rem;
}

.prediction-chart {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 1rem;
}

.prediction-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  justify-content: center;
}

.prediction-item {
  text-align: center;
  padding: 1rem;
  background: rgba(60, 60, 80, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.prediction-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 0.5rem;
  display: block;
}

.prediction-value {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
}

/* Enhanced Timeline */
.enhanced-timeline-card {
  background: rgba(30, 30, 50, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.timeline-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
}

.timeline-filters {
  display: flex;
  gap: 8px;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.filter-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.enhanced-timeline {
  position: relative;
}

.timeline-date-group {
  margin-bottom: 2rem;
}

.timeline-date-group:last-child {
  margin-bottom: 0;
}

.date-label {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  padding-left: 60px;
}

.timeline-items {
  position: relative;
}

.timeline-items::before {
  content: '';
  position: absolute;
  left: 29px;
  top: 20px;
  bottom: 20px;
  width: 2px;
  background: rgba(255, 255, 255, 0.1);
}

.timeline-item-enhanced {
  display: grid;
  grid-template-columns: 60px auto 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: start;
}

.timeline-item-enhanced:last-child {
  margin-bottom: 0;
}

.timeline-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  text-align: right;
  padding-top: 8px;
}

.timeline-marker-enhanced {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  position: relative;
  z-index: 1;
}

.timeline-item-enhanced.success .timeline-marker-enhanced {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 2px solid #22c55e;
}

.timeline-item-enhanced.study .timeline-marker-enhanced {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 2px solid #3b82f6;
}

.timeline-item-enhanced.milestone .timeline-marker-enhanced {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 2px solid #f59e0b;
}

.timeline-content-enhanced {
  background: rgba(40, 40, 60, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.timeline-item-enhanced:hover .timeline-content-enhanced {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(4px);
}

.timeline-content-enhanced h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: #ffffff;
}

.timeline-content-enhanced p {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 0.75rem;
  line-height: 1.5;
}

.timeline-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
}

.meta-item svg {
  font-size: 12px;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
}

.metric-trend.up {
  color: #22c55e;
}

.metric-trend.down {
  color: #ef4444;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 1rem;
}

.metric-progress {
  margin-top: auto;
}

.progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
}

/* Timeline */
.timeline-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
}

.timeline-card h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 1.5rem;
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 8px;
  bottom: 8px;
  width: 2px;
  background: rgba(255, 255, 255, 0.1);
}

.timeline-item {
  position: relative;
  padding-bottom: 1.5rem;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: -24px;
  top: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #0f0e17;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.timeline-marker.success {
  border-color: #22c55e;
  background: #22c55e;
}

.timeline-marker.study {
  border-color: #3b82f6;
  background: #3b82f6;
}

.timeline-marker.milestone {
  border-color: #f59e0b;
  background: #f59e0b;
}

.timeline-content h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px;
}

.timeline-content p {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 4px;
}

.timeline-date {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.4);
}

/* Analytics Section */
.chart-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 1.5rem;
}

.chart-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.chart-btn.active {
  background: linear-gradient(135deg, #667eea, #f093fb);
  border-color: transparent;
  color: white;
}

.chart-container {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 2rem;
  height: 400px;
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card-detailed {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1.25rem;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.stat-header h4 {
  font-size: 13px;
  font-weight: 500;
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
}

.stat-badge {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  text-transform: uppercase;
}

.stat-badge.excellent {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.stat-badge.good {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.stat-badge.warning {
  background: rgba(251, 146, 60, 0.2);
  color: #fb923c;
}

.stat-card-detailed .stat-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-comparison {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.stat-comparison span:last-child {
  font-weight: 600;
}

.stat-comparison .positive {
  color: #22c55e;
}

.stat-comparison .negative {
  color: #ef4444;
}

/* Subjects Section */
.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.subject-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.subject-card:hover,
.subject-card.selected {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.2);
}

.subject-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.subject-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.subject-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px;
}

.subject-info p {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.subject-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.circular-progress {
  position: relative;
  width: 80px;
  height: 80px;
}

.circular-progress svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circular-progress path:last-child {
  stroke-dasharray: var(--progress), 100;
  transition: stroke-dasharray 0.5s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  font-weight: 700;
}

.subject-metrics {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.5rem;
}

.subject-metrics .metric {
  display: flex;
  justify-content: space-between;
}

.subject-metrics .metric-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.subject-metrics .metric-value {
  font-size: 13px;
  font-weight: 600;
}

.subject-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.last-study {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  gap: 6px;
}

.subject-action {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.subject-action:hover {
  background: #667eea;
  border-color: #667eea;
  color: white;
  transform: scale(1.1);
}

/* Subject Details */
.subject-details {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
}

.subject-details h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 1.5rem;
}

.topics-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.topic-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.topic-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.topic-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.topic-status.completed {
  background: #22c55e;
}

.topic-status.in-progress {
  background: #3b82f6;
}

.topic-status.pending {
  background: rgba(255, 255, 255, 0.3);
}

.topic-info {
  flex: 1;
}

.topic-info h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px;
}

.topic-info p {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.topic-progress {
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
}

/* Goals Section */
.add-goal-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea, #f093fb);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

.add-goal-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.goals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.goal-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.goal-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.1);
}

.goal-card.completed {
  background: rgba(34, 197, 94, 0.05);
  border-color: rgba(34, 197, 94, 0.2);
}

.goal-card.at-risk {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.goal-icon {
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 18px;
}

.menu-btn {
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.menu-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.goal-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 0.5rem;
}

.goal-description {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 1.5rem;
  line-height: 1.5;
}

.goal-progress {
  margin-bottom: 1rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 13px;
}

.progress-percentage {
  font-weight: 600;
  color: #667eea;
}

.goal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.goal-deadline {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.goal-status-badge {
  font-size: 11px;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 6px;
  text-transform: uppercase;
}

.goal-status-badge.on-track {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.goal-status-badge.completed {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.goal-status-badge.at-risk {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* Activity Heatmap */
.heatmap-container {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 2rem;
}

.heatmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.heatmap-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.heatmap-stats {
  display: flex;
  gap: 1.5rem;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.heatmap-wrapper {
  position: relative;
}

.month-labels {
  display: flex;
  gap: 2.5rem;
  margin-bottom: 0.5rem;
  margin-left: 3rem;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.heatmap-grid {
  display: flex;
  gap: 0.5rem;
}

.weekday-labels {
  display: flex;
  flex-direction: column;
  gap: 15px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.4);
  width: 2.5rem;
  padding-top: 4px;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(53, 1fr);
  grid-template-rows: repeat(7, 1fr);
  gap: 3px;
  grid-auto-flow: column;
}

.day-cell {
  width: 13px;
  height: 13px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.day-cell:hover {
  transform: scale(1.3);
  border: 1px solid #667eea;
}

.day-cell.level-0 {
  background: rgba(255, 255, 255, 0.05);
}

.day-cell.level-1 {
  background: rgba(102, 126, 234, 0.25);
}

.day-cell.level-2 {
  background: rgba(102, 126, 234, 0.5);
}

.day-cell.level-3 {
  background: rgba(102, 126, 234, 0.75);
}

.day-cell.level-4 {
  background: #667eea;
}

.heatmap-legend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  margin-left: 3rem;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
}

.legend-cells {
  display: flex;
  gap: 3px;
}

/* Study Patterns */
.study-patterns {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.study-patterns h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 1.5rem;
}

.patterns-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.pattern-card {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.pattern-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.pattern-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #f093fb);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  margin: 0 auto 1rem;
}

.pattern-card h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 0.5rem;
}

.pattern-card p {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal {
  background: #1a1a2e;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Export Options */
.export-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.export-option {
  position: relative;
}

.export-option input {
  position: absolute;
  opacity: 0;
}

.option-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.export-option input:checked + .option-card {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.option-card svg {
  font-size: 24px;
  color: #667eea;
}

.export-sections h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 1rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  cursor: pointer;
  font-size: 14px;
}

.checkbox-label input {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
}

/* Form */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.05);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.icon-selector {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 0.5rem;
}

.icon-option {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.icon-option:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.icon-option.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

/* Buttons */
.btn-primary,
.btn-secondary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #f093fb);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Transitions */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* Responsive */
@media (max-width: 1200px) {
  .main-container {
    gap: 1.5rem;
    padding: 1.5rem;
  }
  
  .left-panel {
    width: 280px;
  }
  
  .hero-score-section {
    grid-template-columns: auto 1fr;
    gap: 2rem;
  }
  
  .hero-performance-card {
    padding: 2rem;
  }
  
  .enhanced-metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

/* Responsive adjustments for left panel */
@media (max-width: 1024px) {
  .left-panel {
    width: 280px;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .period-buttons {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    padding: 1rem;
  }
  
  .left-panel {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }
  
  .stats-row {
    grid-template-columns: 1fr;
  }
  
  .content-area {
    max-width: 100%;
    padding: 1rem;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .subjects-grid {
    grid-template-columns: 1fr;
  }
  
  .goals-grid {
    grid-template-columns: 1fr;
  }
  
  .patterns-grid {
    grid-template-columns: 1fr;
  }
  
  .score-circle-card {
    flex-direction: column;
    text-align: center;
  }
  
  .export-options {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .days-grid {
    grid-template-columns: repeat(26, 1fr);
  }
  
  .month-labels {
    gap: 1rem;
  }
}

/* Animations */
@keyframes fa-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fa-spin {
  animation: fa-spin 1s linear infinite;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
  }
}

/* Aplicar animações */
.score-circle-card,
.metric-card,
.subject-card,
.goal-card {
  animation: slideIn 0.5s ease-out;
}

.nav-button.active {
  animation: pulse 2s ease-in-out infinite;
}

.add-goal-btn:hover,
.btn-primary:hover {
  animation: glow 1.5s ease-in-out infinite;
}

.chart-container canvas {
  animation: slideIn 0.8s ease-out;
}

/* Transições suaves para mudanças de valor */
.score-value,
.metric-value,
.stat-card strong {
  transition: all 0.5s ease-out;
}

/* Efeito de hover melhorado */
.metric-card:hover,
.subject-card:hover,
.goal-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

/* Loading state */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: loading 1.5s linear infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Insights Modal Styles */
.insights-modal {
  max-width: 800px;
}

.insights-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 1rem;
}

.insight-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.insight-tab:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #e2e8f0;
}

.insight-tab.active {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  color: #a78bfa;
}

.insights-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  color: #e2e8f0;
}

.insight-cards {
  display: grid;
  gap: 1rem;
}

.insight-card-modal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  transition: all 0.2s;
}

.insight-card-modal:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
}

.insight-header {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex: 1;
}

.insight-icon-modal {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.insight-icon-modal.success {
  background: rgba(16, 185, 129, 0.15);
  color: #10b981;
}

.insight-icon-modal.warning {
  background: rgba(245, 158, 11, 0.15);
  color: #f59e0b;
}

.insight-icon-modal.info {
  background: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
}

.insight-info h5 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #ffffff;
}

.insight-info p {
  font-size: 0.813rem;
  color: #94a3b8;
  margin: 0;
}

.insight-metric {
  text-align: right;
}

.metric-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  color: #64748b;
}

/* Recommendations */
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  gap: 1rem;
  padding: 1.25rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  transition: all 0.2s;
}

.recommendation-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
}

.rec-priority {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  height: fit-content;
}

.rec-priority.high {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.rec-priority.medium {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.rec-priority.low {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.rec-content {
  flex: 1;
}

.rec-content h5 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #ffffff;
}

.rec-content p {
  font-size: 0.813rem;
  color: #94a3b8;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.rec-actions {
  display: flex;
  gap: 0.5rem;
}

.rec-action-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.rec-action-btn:hover {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  color: #a78bfa;
}

.rec-action-btn.secondary {
  background: transparent;
  color: #94a3b8;
}

.rec-action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
  color: #e2e8f0;
}

/* Predictions */
.predictions-chart {
  height: 200px;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 10px;
}

.predictions-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.prediction-item {
  padding: 1.25rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  text-align: center;
}

.prediction-item h5 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #94a3b8;
  margin: 0 0 0.5rem 0;
}

.prediction-value {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
}

.prediction-trend {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.813rem;
  font-weight: 600;
}

.prediction-trend.up {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.prediction-trend.down {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.prediction-info {
  font-size: 0.75rem;
  color: #64748b;
}
</style>