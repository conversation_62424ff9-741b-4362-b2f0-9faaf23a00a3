<template>
  <header class="fixed-header">
    <div class="header-container">
      <!-- Logo Section -->
      <div class="header-logo">
        <router-link to="/" class="logo-link">
          <div class="logo-wrapper">
            <div class="logo-icon">
              <svg width="36" height="36" viewBox="0 0 36 36" fill="none">
                <circle cx="18" cy="18" r="17" stroke="url(#gradient1)" stroke-width="2"/>
                <path d="M18 6C18 6 10 10 10 18C10 26 18 30 18 30C18 30 26 26 26 18C26 10 18 6 18 6Z" 
                      fill="url(#gradient2)" opacity="0.9"/>
                <circle cx="18" cy="18" r="4" fill="#fff"/>
                <defs>
                  <linearGradient id="gradient1" x1="0" y1="0" x2="36" y2="36">
                    <stop stop-color="#42b983"/>
                    <stop offset="1" stop-color="#52d399"/>
                  </linearGradient>
                  <linearGradient id="gradient2" x1="0" y1="0" x2="36" y2="36">
                    <stop stop-color="#42b983" stop-opacity="0.8"/>
                    <stop offset="1" stop-color="#52d399" stop-opacity="0.6"/>
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <div class="logo-text">
              <span class="logo-primary">Sophos</span>
              <span class="logo-divider">|</span>
              <span class="logo-secondary">Academy</span>
            </div>
          </div>
        </router-link>

        <!-- Menu Button -->
        <button class="menu-button" @click="toggleMenu" :class="{ 'active': menuOpen }">
          <span class="menu-icon">
            <span></span>
            <span></span>
            <span></span>
          </span>
        </button>
      </div>

      <!-- Center Search -->
      <div class="header-search">
        <div class="search-box" :class="{ 'focused': searchFocused }">
          <svg class="search-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M7 13C10.3137 13 13 10.3137 13 7C13 3.68629 10.3137 1 7 1C3.68629 1 1 3.68629 1 7C1 10.3137 3.68629 13 7 13Z" 
                  stroke="currentColor" stroke-width="1.5"/>
            <path d="M15 15L11.5 11.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
          <input 
            type="text" 
            placeholder="Pesquisar cursos, aulas, recursos..."
            v-model="searchQuery"
            @focus="searchFocused = true"
            @blur="searchFocused = false"
          />
          <span class="search-shortcut" v-if="!searchFocused">⌘K</span>
        </div>
      </div>

      <!-- Right Section -->
      <div class="header-actions">
        <!-- Notifications -->
        <button class="action-button notifications" @click="toggleNotifications">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M15 7C15 5.67392 14.4732 4.40215 13.5355 3.46447C12.5979 2.52678 11.3261 2 10 2C8.67392 2 7.40215 2.52678 6.46447 3.46447C5.52678 4.40215 5 5.67392 5 7C5 13 2 15 2 15H18C18 15 15 13 15 7Z" 
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M11.73 18C11.5542 18.3031 11.3019 18.5547 10.9982 18.7295C10.6946 18.9044 10.3504 18.9965 10 18.9965C9.64964 18.9965 9.30541 18.9044 9.00179 18.7295C8.69818 18.5547 8.44583 18.3031 8.27 18" 
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span class="notification-badge" v-if="notificationCount > 0">{{ notificationCount }}</span>
        </button>

        <!-- Theme Toggle -->
        <button class="action-button theme-toggle" @click="toggleTheme">
          <svg v-if="!isDarkMode" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M10 1V3M10 17V19M19 10H17M3 10H1M16.364 16.364L15 15M5 5L3.636 3.636M16.364 3.636L15 5M5 15L3.636 16.364" 
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            <circle cx="10" cy="10" r="4" stroke="currentColor" stroke-width="1.5"/>
          </svg>
          <svg v-else width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M19 10.79C18.8427 12.4922 18.2039 14.1144 17.1583 15.4668C16.1127 16.8192 14.7035 17.8458 13.0957 18.4265C11.4879 19.0073 9.74801 19.1181 8.07974 18.7461C6.41148 18.3741 4.88302 17.5345 3.67425 16.3258C2.46548 15.117 1.62596 13.5885 1.25393 11.9203C0.881899 10.252 0.992729 8.51208 1.57348 6.90428C2.15423 5.29648 3.18085 3.88731 4.53324 2.84175C5.88562 1.79619 7.50782 1.15731 9.21 1C8.21344 2.34827 7.86392 4.04927 8.25052 5.68151C8.63712 7.31375 9.72461 8.71956 11.2333 9.53041C12.7421 10.3413 14.5328 10.4823 16.1473 9.92229C17.7618 9.36229 19.0524 8.15067 19.71 6.58" 
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <!-- User Profile -->
        <div class="user-section">
          <button class="user-button" @click="toggleUserMenu" :class="{ 'active': userMenuOpen }">
            <div class="user-avatar">
              <span>{{ userInitials }}</span>
            </div>
            <span class="user-name">{{ userName }}</span>
            <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Menu Dropdown -->
    <transition name="menu-slide">
      <div v-if="menuOpen" class="menu-dropdown">
        <div class="menu-content">
          <div class="menu-section">
            <h4>Estudos</h4>
            <nav class="menu-nav">
              <router-link to="/calendar" @click="closeMenu" class="menu-item">
                <div class="menu-item-icon">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                    <rect x="2" y="3" width="14" height="13" rx="2" stroke="currentColor" stroke-width="1.5"/>
                    <path d="M12 1V5M6 1V5M2 7H16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                  </svg>
                </div>
                <span>Calendário</span>
              </router-link>
              <router-link to="/plano-estudo" @click="closeMenu" class="menu-item">
                <div class="menu-item-icon">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                    <path d="M5 4.5H13M5 9H13M5 13.5H10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                  </svg>
                </div>
                <span>Plano de Estudo</span>
              </router-link>
              <router-link to="/progress-dashboard" @click="closeMenu" class="menu-item">
                <div class="menu-item-icon">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                    <path d="M2 16L7 11L11 13L16 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M11 8H16V13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <span>Desempenho</span>
              </router-link>
            </nav>
          </div>

          <div class="menu-section">
            <h4>IA Tools</h4>
            <nav class="menu-nav">
              <router-link to="/ia" @click="closeMenu" class="menu-item featured">
                <div class="menu-item-icon">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                    <circle cx="9" cy="9" r="7" stroke="currentColor" stroke-width="1.5"/>
                    <circle cx="9" cy="9" r="2" fill="currentColor"/>
                    <path d="M9 2V4M9 14V16M16 9H14M4 9H2" stroke="currentColor" stroke-width="1.5"/>
                  </svg>
                </div>
                <span>Central de IA</span>
                <span class="badge">NOVO</span>
              </router-link>
            </nav>
          </div>

          <div class="menu-section">
            <h4>Recursos</h4>
            <nav class="menu-nav">
              <router-link to="/recursos" @click="closeMenu" class="menu-item">
                <div class="menu-item-icon">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                    <path d="M2 6L9 2L16 6V13C16 14.5 13 16 9 16C5 16 2 14.5 2 13V6Z" stroke="currentColor" stroke-width="1.5"/>
                    <path d="M2 6L9 10L16 6" stroke="currentColor" stroke-width="1.5"/>
                    <path d="M9 10V16" stroke="currentColor" stroke-width="1.5"/>
                  </svg>
                </div>
                <span>Biblioteca</span>
              </router-link>
              <router-link to="/videos" @click="closeMenu" class="menu-item">
                <div class="menu-item-icon">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                    <rect x="2" y="4" width="14" height="10" rx="2" stroke="currentColor" stroke-width="1.5"/>
                    <path d="M7 11L12 8.5L7 6V11Z" fill="currentColor"/>
                  </svg>
                </div>
                <span>Videoaulas</span>
              </router-link>
            </nav>
          </div>
        </div>
      </div>
    </transition>

    <!-- User Dropdown -->
    <transition name="dropdown-fade">
      <div v-if="userMenuOpen" class="user-dropdown">
        <div class="dropdown-header">
          <div class="dropdown-avatar">
            <span>{{ userInitials }}</span>
          </div>
          <div class="dropdown-info">
            <div class="dropdown-name">{{ userName }}</div>
            <div class="dropdown-email">{{ userEmail }}</div>
          </div>
        </div>
        <div class="dropdown-divider"></div>
        <nav class="dropdown-nav">
          <router-link to="/profile" @click="closeUserMenu" class="dropdown-item">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <circle cx="8" cy="6" r="3" stroke="currentColor" stroke-width="1.5"/>
              <path d="M2 14C2 12 4 10 8 10C12 10 14 12 14 14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
            <span>Meu Perfil</span>
          </router-link>
          <router-link to="/settings" @click="closeUserMenu" class="dropdown-item">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <circle cx="8" cy="8" r="2" stroke="currentColor" stroke-width="1.5"/>
              <path d="M8 1V3M8 13V15M15 8H13M3 8H1M12.5 3.5L11 5M5 11L3.5 12.5M12.5 12.5L11 11M5 5L3.5 3.5" 
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
            <span>Configurações</span>
          </router-link>
          <router-link to="/help" @click="closeUserMenu" class="dropdown-item">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <circle cx="8" cy="8" r="7" stroke="currentColor" stroke-width="1.5"/>
              <path d="M6 6C6 5 7 4 8 4C9 4 10 5 10 6C10 7 9 7.5 8 8V9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
              <circle cx="8" cy="11.5" r="0.5" fill="currentColor"/>
            </svg>
            <span>Ajuda</span>
          </router-link>
        </nav>
        <div class="dropdown-divider"></div>
        <button @click="logout" class="dropdown-item logout">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M6 14H3C2 14 1 13 1 12V4C1 3 2 2 3 2H6M11 11L14 8M14 8L11 5M14 8H5" 
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span>Sair</span>
        </button>
      </div>
    </transition>

    <!-- Notification Panel -->
    <transition name="panel-slide">
      <div v-if="notificationsOpen" class="notifications-panel">
        <div class="panel-header">
          <h3>Notificações</h3>
          <button @click="notificationsOpen = false" class="panel-close">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        <div class="panel-content">
          <div class="notification-item unread">
            <div class="notification-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <circle cx="10" cy="10" r="8" fill="#42b983" opacity="0.2"/>
                <path d="M7 10L9 12L13 8" stroke="#42b983" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="notification-content">
              <p class="notification-title">Parabéns! Meta de estudos alcançada</p>
              <p class="notification-time">Há 5 minutos</p>
            </div>
          </div>
          <div class="notification-item">
            <div class="notification-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <circle cx="10" cy="10" r="8" fill="#8b949e" opacity="0.2"/>
                <path d="M10 6V10L12 12" stroke="#8b949e" stroke-width="1.5" stroke-linecap="round"/>
              </svg>
            </div>
            <div class="notification-content">
              <p class="notification-title">Lembrete: Revisar flashcards de Anatomia</p>
              <p class="notification-time">Há 2 horas</p>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Overlay -->
    <transition name="fade">
      <div v-if="menuOpen || userMenuOpen || notificationsOpen" 
           class="overlay" 
           @click="closeAll"></div>
    </transition>
  </header>
</template>

<script>
export default {
  name: 'AppHeaderFixed',
  data() {
    return {
      searchQuery: '',
      searchFocused: false,
      menuOpen: false,
      userMenuOpen: false,
      notificationsOpen: false,
      notificationCount: 2,
      isDarkMode: true
    }
  },
  computed: {
    currentUser() {
      return this.$store.state.auth?.user || { email: '<EMAIL>' }
    },
    userName() {
      return this.currentUser.name || this.currentUser.email.split('@')[0]
    },
    userEmail() {
      return this.currentUser.email
    },
    userInitials() {
      if (this.currentUser.name) {
        return this.currentUser.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
      }
      return this.currentUser.email.charAt(0).toUpperCase()
    }
  },
  mounted() {
    // Keyboard shortcuts
    document.addEventListener('keydown', this.handleKeyboard)
  },
  beforeUnmount() {
    document.removeEventListener('keydown', this.handleKeyboard)
  },
  methods: {
    toggleMenu() {
      this.menuOpen = !this.menuOpen
      this.userMenuOpen = false
      this.notificationsOpen = false
    },
    toggleUserMenu() {
      this.userMenuOpen = !this.userMenuOpen
      this.menuOpen = false
      this.notificationsOpen = false
    },
    toggleNotifications() {
      this.notificationsOpen = !this.notificationsOpen
      this.menuOpen = false
      this.userMenuOpen = false
    },
    toggleTheme() {
      this.isDarkMode = !this.isDarkMode
      document.documentElement.setAttribute('data-theme', this.isDarkMode ? 'dark' : 'light')
    },
    closeMenu() {
      this.menuOpen = false
    },
    closeUserMenu() {
      this.userMenuOpen = false
    },
    closeAll() {
      this.menuOpen = false
      this.userMenuOpen = false
      this.notificationsOpen = false
    },
    handleKeyboard(e) {
      // CMD/CTRL + K for search
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        const searchInput = this.$el.querySelector('.header-search input')
        if (searchInput) searchInput.focus()
      }
      // ESC to close all
      if (e.key === 'Escape') {
        this.closeAll()
      }
    },
    async logout() {
      try {
        await this.$store.dispatch('auth/logout')
        this.$router.push('/login')
      } catch (error) {
        console.error('Logout error:', error)
      }
    }
  }
}
</script>

<style scoped>
/* CSS Variables */
:root {
  --header-height: 64px;
  --header-bg: #0a0d13;
  --header-border: rgba(255, 255, 255, 0.08);
  --text-primary: #ffffff;
  --text-secondary: #8b949e;
  --text-tertiary: #6e7681;
  --accent-color: #42b983;
  --accent-hover: #52d399;
  --surface-bg: rgba(255, 255, 255, 0.04);
  --surface-hover: rgba(255, 255, 255, 0.08);
  --surface-active: rgba(255, 255, 255, 0.12);
  --dropdown-bg: #161b22;
  --dropdown-border: rgba(255, 255, 255, 0.1);
  --danger-color: #f85149;
  --transition-fast: 0.15s ease;
  --transition-medium: 0.25s ease;
  --transition-slow: 0.35s ease;
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Header Base */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background: var(--header-bg);
  border-bottom: 1px solid var(--header-border);
  z-index: 1000;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.header-container {
  height: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 32px;
}

/* Logo Section */
.header-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.logo-link {
  text-decoration: none;
  display: flex;
  align-items: center;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  transition: transform var(--transition-fast);
}

.logo-wrapper:hover {
  transform: translateY(-1px);
}

.logo-icon {
  width: 36px;
  height: 36px;
  position: relative;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

.logo-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

.logo-primary {
  color: var(--text-primary);
}

.logo-divider {
  color: var(--text-tertiary);
  opacity: 0.5;
}

.logo-secondary {
  color: var(--text-secondary);
}

/* Menu Button */
.menu-button {
  width: 36px;
  height: 36px;
  background: var(--surface-bg);
  border: 1px solid var(--header-border);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.menu-button:hover {
  background: var(--surface-hover);
  border-color: var(--accent-color);
}

.menu-button.active {
  background: var(--accent-color);
  border-color: var(--accent-color);
}

.menu-icon {
  width: 16px;
  height: 12px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.menu-icon span {
  display: block;
  width: 100%;
  height: 2px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: all var(--transition-medium);
}

.menu-button.active .menu-icon span:nth-child(1) {
  transform: rotate(45deg) translate(3px, 3px);
}

.menu-button.active .menu-icon span:nth-child(2) {
  opacity: 0;
}

.menu-button.active .menu-icon span:nth-child(3) {
  transform: rotate(-45deg) translate(3px, -3px);
}

/* Search Section */
.header-search {
  flex: 1;
  max-width: 480px;
}

.search-box {
  position: relative;
  width: 100%;
  height: 36px;
  background: var(--surface-bg);
  border: 1px solid var(--header-border);
  border-radius: 8px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  gap: 8px;
  transition: all var(--transition-fast);
}

.search-box:hover {
  background: var(--surface-hover);
}

.search-box.focused {
  background: var(--surface-hover);
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(66, 185, 131, 0.1);
}

.search-icon {
  color: var(--text-tertiary);
  flex-shrink: 0;
}

.search-box input {
  flex: 1;
  background: none;
  border: none;
  outline: none;
  color: var(--text-primary);
  font-size: 14px;
  font-family: inherit;
}

.search-box input::placeholder {
  color: var(--text-tertiary);
}

.search-shortcut {
  padding: 2px 6px;
  background: var(--surface-bg);
  border: 1px solid var(--header-border);
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  color: var(--text-tertiary);
  flex-shrink: 0;
}

/* Actions Section */
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.action-button {
  width: 36px;
  height: 36px;
  background: var(--surface-bg);
  border: 1px solid var(--header-border);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  color: var(--text-secondary);
}

.action-button:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 18px;
  height: 18px;
  background: var(--accent-color);
  border: 2px solid var(--header-bg);
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
  color: white;
  padding: 0 4px;
}

/* User Section */
.user-section {
  margin-left: 8px;
}

.user-button {
  height: 36px;
  background: var(--surface-bg);
  border: 1px solid var(--header-border);
  border-radius: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 12px 2px 2px;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-primary);
}

.user-button:hover {
  background: var(--surface-hover);
  border-color: var(--accent-color);
}

.user-button.active {
  background: var(--surface-active);
  border-color: var(--accent-color);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-arrow {
  color: var(--text-tertiary);
  transition: transform var(--transition-fast);
}

.user-button.active .dropdown-arrow {
  transform: rotate(180deg);
}

/* Menu Dropdown */
.menu-dropdown {
  position: fixed;
  top: var(--header-height);
  left: 0;
  right: 0;
  background: var(--dropdown-bg);
  border-bottom: 1px solid var(--header-border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.24);
  overflow: hidden;
  z-index: 999;
}

.menu-content {
  max-width: 1440px;
  margin: 0 auto;
  padding: 24px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
}

.menu-section h4 {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.menu-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  border-radius: 8px;
  text-decoration: none;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.menu-item:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
  transform: translateX(4px);
}

.menu-item.featured {
  background: var(--surface-bg);
  border: 1px solid var(--accent-color);
  color: var(--text-primary);
}

.menu-item-icon {
  width: 32px;
  height: 32px;
  background: var(--surface-bg);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
}

.menu-item:hover .menu-item-icon {
  background: var(--surface-active);
  color: var(--accent-color);
}

.badge {
  margin-left: auto;
  padding: 2px 8px;
  background: var(--accent-color);
  border-radius: 4px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
}

/* User Dropdown */
.user-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 280px;
  background: var(--dropdown-bg);
  border: 1px solid var(--dropdown-border);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.24);
  overflow: hidden;
  z-index: 999;
}

.dropdown-header {
  padding: 16px;
  background: var(--surface-bg);
  display: flex;
  align-items: center;
  gap: 12px;
}

.dropdown-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.dropdown-info {
  flex: 1;
  overflow: hidden;
}

.dropdown-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.dropdown-email {
  font-size: 12px;
  color: var(--text-tertiary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-divider {
  height: 1px;
  background: var(--header-border);
}

.dropdown-nav {
  padding: 8px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  border-radius: 6px;
  text-decoration: none;
  color: var(--text-secondary);
  font-size: 14px;
  transition: all var(--transition-fast);
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.dropdown-item:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

.dropdown-item.logout {
  color: var(--danger-color);
}

.dropdown-item.logout:hover {
  background: rgba(248, 81, 73, 0.1);
}

/* Notifications Panel */
.notifications-panel {
  position: fixed;
  top: var(--header-height);
  right: 0;
  width: 360px;
  height: calc(100vh - var(--header-height));
  background: var(--dropdown-bg);
  border-left: 1px solid var(--header-border);
  box-shadow: -8px 0 32px rgba(0, 0, 0, 0.24);
  overflow: hidden;
  z-index: 999;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 20px;
  border-bottom: 1px solid var(--header-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.panel-close {
  width: 32px;
  height: 32px;
  background: var(--surface-bg);
  border: 1px solid var(--header-border);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-tertiary);
}

.panel-close:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.notification-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all var(--transition-fast);
}

.notification-item:hover {
  background: var(--surface-hover);
}

.notification-item.unread {
  background: var(--surface-bg);
}

.notification-icon {
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.notification-time {
  font-size: 12px;
  color: var(--text-tertiary);
}

/* Overlay */
.overlay {
  position: fixed;
  top: var(--header-height);
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

/* Transitions */
.menu-slide-enter-active,
.menu-slide-leave-active {
  transition: all var(--transition-medium);
}

.menu-slide-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}

.menu-slide-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

.dropdown-fade-enter-active,
.dropdown-fade-leave-active {
  transition: all var(--transition-fast);
}

.dropdown-fade-enter-from,
.dropdown-fade-leave-to {
  transform: translateY(-8px);
  opacity: 0;
}

.panel-slide-enter-active,
.panel-slide-leave-active {
  transition: all var(--transition-medium);
}

.panel-slide-enter-from,
.panel-slide-leave-to {
  transform: translateX(100%);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-fast);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
    gap: 16px;
  }

  .logo-text {
    display: none;
  }

  .header-search {
    display: none;
  }

  .user-name {
    display: none;
  }

  .user-button {
    padding: 2px;
  }

  .notifications-panel {
    width: 100%;
  }

  .menu-content {
    grid-template-columns: 1fr;
  }
}

/* Dark Theme Support */
[data-theme="light"] {
  --header-bg: #ffffff;
  --header-border: rgba(0, 0, 0, 0.08);
  --text-primary: #1f2328;
  --text-secondary: #656d76;
  --text-tertiary: #8c959f;
  --surface-bg: rgba(0, 0, 0, 0.04);
  --surface-hover: rgba(0, 0, 0, 0.08);
  --surface-active: rgba(0, 0, 0, 0.12);
  --dropdown-bg: #ffffff;
  --dropdown-border: rgba(0, 0, 0, 0.1);
}
</style>