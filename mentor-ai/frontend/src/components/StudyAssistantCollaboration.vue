<template>
  <div class="collaboration-container">
    <!-- Collaboration Hub -->
    <div class="collab-hub">
      <h2 class="hub-title">
        <i class="fas fa-users"></i>
        Centro de Colaboração em Tempo Real
      </h2>
      
      <!-- Connection Status -->
      <div class="connection-status">
        <div class="status-indicator" :class="connectionStatus">
          <div class="status-dot"></div>
          <span>{{ connectionStatusText }}</span>
        </div>
        
        <div class="network-stats">
          <div class="stat">
            <i class="fas fa-wifi"></i>
            <span>{{ networkLatency }}ms</span>
          </div>
          <div class="stat">
            <i class="fas fa-users"></i>
            <span>{{ connectedPeers.length }} online</span>
          </div>
          <div class="stat">
            <i class="fas fa-exchange-alt"></i>
            <span>{{ bandwidthUsage }}MB/s</span>
          </div>
        </div>
      </div>
      
      <!-- Room Controls -->
      <div class="room-controls">
        <div class="room-input">
          <input 
            v-model="roomId" 
            type="text" 
            placeholder="ID da Sala (ex: MEDIC-2024)"
            class="room-id-input"
          >
          <button @click="createRoom" class="create-btn">
            <i class="fas fa-plus"></i>
            Criar Sala
          </button>
          <button @click="joinRoom" class="join-btn">
            <i class="fas fa-sign-in-alt"></i>
            Entrar
          </button>
        </div>
        
        <div v-if="currentRoom" class="current-room">
          <h3>Sala: {{ currentRoom.name }}</h3>
          <div class="room-code">
            <span>{{ currentRoom.code }}</span>
            <button @click="copyRoomCode" class="copy-btn">
              <i class="fas fa-copy"></i>
            </button>
          </div>
          <button @click="leaveRoom" class="leave-btn">
            <i class="fas fa-sign-out-alt"></i>
            Sair da Sala
          </button>
        </div>
      </div>
    </div>
    
    <!-- Video Conference -->
    <div class="video-conference" v-if="currentRoom">
      <div class="video-grid" :class="`grid-${Math.min(connectedPeers.length + 1, 6)}`">
        <!-- Local Video -->
        <div class="video-container local">
          <video ref="localVideo" autoplay muted></video>
          <div class="video-overlay">
            <span class="user-name">Você</span>
            <div class="video-controls">
              <button @click="toggleVideo" class="control-btn" :class="{ muted: !videoEnabled }">
                <i :class="videoEnabled ? 'fas fa-video' : 'fas fa-video-slash'"></i>
              </button>
              <button @click="toggleAudio" class="control-btn" :class="{ muted: !audioEnabled }">
                <i :class="audioEnabled ? 'fas fa-microphone' : 'fas fa-microphone-slash'"></i>
              </button>
              <button @click="shareScreen" class="control-btn" :class="{ active: isScreenSharing }">
                <i class="fas fa-desktop"></i>
              </button>
            </div>
          </div>
          <div class="audio-indicator" v-if="audioEnabled">
            <div v-for="i in 5" :key="i" 
                 class="audio-bar" 
                 :style="{ height: audioLevels[0] > i * 20 ? '100%' : '20%' }">
            </div>
          </div>
        </div>
        
        <!-- Remote Videos -->
        <div v-for="peer in connectedPeers" :key="peer.id" 
             class="video-container remote">
          <video :ref="`remoteVideo-${peer.id}`" autoplay></video>
          <div class="video-overlay">
            <span class="user-name">{{ peer.name }}</span>
            <div class="user-status">
              <i v-if="peer.isSpeaking" class="fas fa-volume-up speaking"></i>
              <i v-if="peer.isPresenting" class="fas fa-presentation presenting"></i>
              <i v-if="peer.isWriting" class="fas fa-pen writing"></i>
            </div>
          </div>
          <div class="audio-indicator" v-if="peer.audioEnabled">
            <div v-for="i in 5" :key="i" 
                 class="audio-bar" 
                 :style="{ height: audioLevels[peer.id] > i * 20 ? '100%' : '20%' }">
            </div>
          </div>
        </div>
      </div>
      
      <!-- Screen Share View -->
      <transition name="slide-up">
        <div v-if="activeScreenShare" class="screen-share-view">
          <div class="share-header">
            <span>{{ activeScreenShare.userName }} está compartilhando a tela</span>
            <button @click="closeScreenShare" class="close-share-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <video ref="screenShareVideo" autoplay></video>
        </div>
      </transition>
    </div>
    
    <!-- Collaborative Whiteboard -->
    <div class="collaborative-whiteboard" v-if="currentRoom">
      <div class="whiteboard-header">
        <h3>
          <i class="fas fa-chalkboard"></i>
          Quadro Colaborativo
        </h3>
        <div class="whiteboard-tools">
          <button v-for="tool in drawingTools" :key="tool.id"
                  @click="selectTool(tool)"
                  class="tool-btn"
                  :class="{ active: selectedTool?.id === tool.id }"
                  :title="tool.name">
            <i :class="tool.icon"></i>
          </button>
          
          <div class="color-picker">
            <input type="color" v-model="selectedColor" class="color-input">
          </div>
          
          <input type="range" v-model="brushSize" min="1" max="50" class="brush-size">
          
          <button @click="clearWhiteboard" class="clear-btn">
            <i class="fas fa-trash"></i>
            Limpar
          </button>
          
          <button @click="saveWhiteboard" class="save-btn">
            <i class="fas fa-save"></i>
            Salvar
          </button>
        </div>
      </div>
      
      <div class="whiteboard-canvas-container">
        <canvas ref="whiteboardCanvas" 
                @mousedown="startDrawing"
                @mousemove="draw"
                @mouseup="stopDrawing"
                @mouseleave="stopDrawing"
                @touchstart="startDrawing"
                @touchmove="draw"
                @touchend="stopDrawing">
        </canvas>
        
        <!-- Remote Cursors -->
        <div v-for="cursor in remoteCursors" :key="cursor.userId"
             class="remote-cursor"
             :style="{ 
               left: cursor.x + 'px', 
               top: cursor.y + 'px',
               color: cursor.color
             }">
          <i class="fas fa-mouse-pointer"></i>
          <span class="cursor-name">{{ cursor.userName }}</span>
        </div>
      </div>
    </div>
    
    <!-- Real-time Chat -->
    <div class="realtime-chat" v-if="currentRoom">
      <div class="chat-header">
        <h3>
          <i class="fas fa-comments"></i>
          Chat em Tempo Real
        </h3>
        <div class="chat-actions">
          <button @click="toggleEmojis" class="emoji-btn">
            <i class="fas fa-smile"></i>
          </button>
          <button @click="attachFile" class="attach-btn">
            <i class="fas fa-paperclip"></i>
          </button>
        </div>
      </div>
      
      <div class="chat-messages" ref="chatContainer">
        <div v-for="message in messages" :key="message.id"
             class="message"
             :class="{ 
               'own': message.userId === userId,
               'system': message.type === 'system'
             }">
          <div v-if="message.type !== 'system'" class="message-avatar">
            {{ message.userInitials }}
          </div>
          
          <div class="message-content">
            <div v-if="message.type !== 'system'" class="message-header">
              <span class="message-author">{{ message.userName }}</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>
            
            <div class="message-body">
              <p v-if="message.type === 'text'">{{ message.content }}</p>
              
              <div v-if="message.type === 'image'" class="message-image">
                <img :src="message.content" @click="viewImage(message.content)">
              </div>
              
              <div v-if="message.type === 'file'" class="message-file">
                <i class="fas fa-file"></i>
                <span>{{ message.fileName }}</span>
                <button @click="downloadFile(message.content, message.fileName)" class="download-btn">
                  <i class="fas fa-download"></i>
                </button>
              </div>
              
              <div v-if="message.type === 'code'" class="message-code">
                <pre><code>{{ message.content }}</code></pre>
                <button @click="copyCode(message.content)" class="copy-code-btn">
                  <i class="fas fa-copy"></i>
                </button>
              </div>
              
              <p v-if="message.type === 'system'" class="system-message">
                {{ message.content }}
              </p>
            </div>
            
            <div v-if="message.reactions && message.reactions.length" class="message-reactions">
              <span v-for="reaction in message.reactions" :key="reaction.emoji"
                    class="reaction"
                    @click="toggleReaction(message.id, reaction.emoji)">
                {{ reaction.emoji }} {{ reaction.count }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- Typing Indicators -->
        <div v-if="typingUsers.length" class="typing-indicator">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <span class="typing-text">
            {{ typingUsersText }}
          </span>
        </div>
      </div>
      
      <!-- Emoji Picker -->
      <transition name="fade">
        <div v-if="showEmojiPicker" class="emoji-picker">
          <div class="emoji-grid">
            <span v-for="emoji in emojis" :key="emoji"
                  @click="insertEmoji(emoji)"
                  class="emoji">
              {{ emoji }}
            </span>
          </div>
        </div>
      </transition>
      
      <!-- Message Input -->
      <div class="message-input-container">
        <textarea
          ref="messageInput"
          v-model="messageText"
          @keypress.enter.prevent="sendMessage"
          @input="handleTyping"
          placeholder="Digite sua mensagem..."
          class="message-input"
          rows="2"
        ></textarea>
        
        <button @click="sendMessage" class="send-btn" :disabled="!messageText.trim()">
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>
    </div>
    
    <!-- Shared Documents -->
    <div class="shared-documents" v-if="currentRoom">
      <h3 class="documents-title">
        <i class="fas fa-folder-open"></i>
        Documentos Compartilhados
      </h3>
      
      <div class="documents-grid">
        <div v-for="doc in sharedDocuments" :key="doc.id"
             class="document-card"
             @click="openDocument(doc)">
          <div class="document-icon">
            <i :class="getFileIcon(doc.type)"></i>
          </div>
          <div class="document-info">
            <h4>{{ doc.name }}</h4>
            <p>{{ doc.size }} • {{ doc.owner }}</p>
            <span class="document-date">{{ formatDate(doc.uploadedAt) }}</span>
          </div>
          <div class="document-actions">
            <button @click.stop="downloadDocument(doc)" class="doc-action-btn">
              <i class="fas fa-download"></i>
            </button>
            <button @click.stop="shareDocument(doc)" class="doc-action-btn">
              <i class="fas fa-share"></i>
            </button>
            <button v-if="doc.ownerId === userId" @click.stop="deleteDocument(doc)" class="doc-action-btn danger">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        
        <!-- Upload Area -->
        <div class="upload-area" 
             @drop="handleDrop"
             @dragover.prevent
             @dragenter.prevent>
          <input type="file" ref="fileInput" @change="handleFileSelect" multiple hidden>
          <i class="fas fa-cloud-upload-alt"></i>
          <p>Arraste arquivos aqui ou</p>
          <button @click="$refs.fileInput.click()" class="browse-btn">
            Procurar Arquivos
          </button>
        </div>
      </div>
    </div>
    
    <!-- Live Code Editor -->
    <div class="live-code-editor" v-if="currentRoom && showCodeEditor">
      <div class="editor-header">
        <h3>
          <i class="fas fa-code"></i>
          Editor de Código Colaborativo
        </h3>
        <div class="editor-controls">
          <select v-model="selectedLanguage" class="language-select">
            <option value="javascript">JavaScript</option>
            <option value="python">Python</option>
            <option value="html">HTML</option>
            <option value="css">CSS</option>
            <option value="java">Java</option>
            <option value="cpp">C++</option>
          </select>
          
          <button @click="runCode" class="run-btn">
            <i class="fas fa-play"></i>
            Executar
          </button>
          
          <button @click="shareCode" class="share-code-btn">
            <i class="fas fa-share-alt"></i>
            Compartilhar
          </button>
          
          <button @click="closeCodeEditor" class="close-editor-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      
      <div class="editor-container">
        <div class="code-editor">
          <div class="line-numbers">
            <div v-for="i in codeLines" :key="i" class="line-number">{{ i }}</div>
          </div>
          <textarea
            ref="codeTextarea"
            v-model="codeContent"
            @input="handleCodeChange"
            class="code-textarea"
            :style="{ fontFamily: 'monospace' }"
            spellcheck="false"
          ></textarea>
        </div>
        
        <!-- Active Collaborators -->
        <div class="code-collaborators">
          <div v-for="collaborator in codeCollaborators" :key="collaborator.id"
               class="collaborator-indicator"
               :style="{ 
                 top: collaborator.line * 20 + 'px',
                 backgroundColor: collaborator.color
               }">
            <span class="collaborator-name">{{ collaborator.name }}</span>
          </div>
        </div>
        
        <!-- Code Output -->
        <div class="code-output" v-if="codeOutput">
          <div class="output-header">
            <span>Saída</span>
            <button @click="clearOutput" class="clear-output-btn">
              <i class="fas fa-trash"></i>
            </button>
          </div>
          <pre>{{ codeOutput }}</pre>
        </div>
      </div>
    </div>
    
    <!-- Study Groups -->
    <div class="study-groups">
      <h3 class="groups-title">
        <i class="fas fa-user-friends"></i>
        Grupos de Estudo Ativos
      </h3>
      
      <div class="groups-list">
        <div v-for="group in studyGroups" :key="group.id"
             class="group-card"
             :class="{ active: group.isActive }">
          <div class="group-header">
            <div class="group-icon" :style="{ backgroundColor: group.color }">
              <i :class="group.icon"></i>
            </div>
            <div class="group-info">
              <h4>{{ group.name }}</h4>
              <p>{{ group.subject }}</p>
            </div>
            <div class="group-stats">
              <span class="member-count">
                <i class="fas fa-users"></i>
                {{ group.members }}/{{ group.maxMembers }}
              </span>
            </div>
          </div>
          
          <div class="group-schedule">
            <i class="fas fa-calendar"></i>
            <span>{{ group.nextSession }}</span>
          </div>
          
          <div class="group-tags">
            <span v-for="tag in group.tags" :key="tag" class="tag">
              {{ tag }}
            </span>
          </div>
          
          <button @click="joinStudyGroup(group)" class="join-group-btn">
            <i class="fas fa-sign-in-alt"></i>
            Entrar no Grupo
          </button>
        </div>
      </div>
      
      <button @click="createStudyGroup" class="create-group-btn">
        <i class="fas fa-plus"></i>
        Criar Novo Grupo
      </button>
    </div>
    
    <!-- Collaboration Analytics -->
    <div class="collaboration-analytics">
      <h3 class="analytics-title">
        <i class="fas fa-chart-line"></i>
        Análise de Colaboração
      </h3>
      
      <div class="analytics-grid">
        <div class="analytics-card">
          <div class="card-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="card-content">
            <h4>Tempo de Estudo</h4>
            <p class="metric">{{ totalStudyTime }}</p>
            <span class="trend up">+12% esta semana</span>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="card-icon">
            <i class="fas fa-user-graduate"></i>
          </div>
          <div class="card-content">
            <h4>Sessões Colaborativas</h4>
            <p class="metric">{{ collaborativeSessions }}</p>
            <span class="trend up">+5 esta semana</span>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="card-icon">
            <i class="fas fa-comments"></i>
          </div>
          <div class="card-content">
            <h4>Interações</h4>
            <p class="metric">{{ totalInteractions }}</p>
            <span class="trend">Muito ativo</span>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="card-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <div class="card-content">
            <h4>Pontuação Colaborativa</h4>
            <p class="metric">{{ collaborationScore }}</p>
            <div class="score-bar">
              <div class="score-fill" :style="{ width: collaborationScore + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Activity Timeline -->
      <div class="activity-timeline">
        <h4>Atividade Recente</h4>
        <div class="timeline">
          <div v-for="activity in recentActivities" :key="activity.id"
               class="timeline-item">
            <div class="timeline-marker" :style="{ backgroundColor: activity.color }">
              <i :class="activity.icon"></i>
            </div>
            <div class="timeline-content">
              <p>{{ activity.description }}</p>
              <span class="timeline-time">{{ formatTimeAgo(activity.timestamp) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'StudyAssistantCollaboration',
  setup() {
    const store = useStore();
    
    // Refs
    const localVideo = ref(null);
    const whiteboardCanvas = ref(null);
    const chatContainer = ref(null);
    const messageInput = ref(null);
    const codeTextarea = ref(null);
    
    // WebRTC
    const localStream = ref(null);
    const peerConnections = ref({});
    const dataChannels = ref({});
    const signalingSocket = ref(null);
    
    // User State
    const userId = ref(`user_${Math.random().toString(36).substr(2, 9)}`);
    const userName = ref('Estudante');
    
    // Connection State
    const connectionStatus = ref('disconnected');
    const networkLatency = ref(0);
    const bandwidthUsage = ref(0);
    
    // Room State
    const roomId = ref('');
    const currentRoom = ref(null);
    const connectedPeers = ref([]);
    
    // Media State
    const videoEnabled = ref(true);
    const audioEnabled = ref(true);
    const isScreenSharing = ref(false);
    const activeScreenShare = ref(null);
    const audioLevels = ref({});
    
    // Whiteboard State
    const isDrawing = ref(false);
    const drawingTools = ref([
      { id: 'pen', name: 'Caneta', icon: 'fas fa-pen' },
      { id: 'eraser', name: 'Borracha', icon: 'fas fa-eraser' },
      { id: 'line', name: 'Linha', icon: 'fas fa-minus' },
      { id: 'rectangle', name: 'Retângulo', icon: 'far fa-square' },
      { id: 'circle', name: 'Círculo', icon: 'far fa-circle' },
      { id: 'text', name: 'Texto', icon: 'fas fa-font' }
    ]);
    const selectedTool = ref(drawingTools.value[0]);
    const selectedColor = ref('#667eea');
    const brushSize = ref(3);
    const remoteCursors = ref([]);
    const drawingContext = ref(null);
    const lastPosition = ref({ x: 0, y: 0 });
    
    // Chat State
    const messages = ref([]);
    const messageText = ref('');
    const typingUsers = ref([]);
    const typingTimeout = ref(null);
    const showEmojiPicker = ref(false);
    const emojis = ref(['😀', '😃', '😄', '😁', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙', '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥', '😌', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐', '😕', '😟', '🙁', '☹️', '😮', '😯', '😲', '😳', '🥺', '😦', '😧', '😨', '😰', '😥', '😢', '😭', '😱', '😖', '😣', '😞', '😓', '😩', '😫', '🥱', '😤', '😡', '😠', '🤬', '😈', '👿', '💀', '☠️', '💩', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💪', '🦵', '🦶', '👂', '🦻', '👃', '🧠', '🦷', '🦴', '👀', '👁️', '👅', '👄', '💋', '👶', '🧒', '👦', '👧', '🧑', '👱', '👨', '🧔', '👨‍🦰', '👨‍🦱', '👨‍🦳', '👨‍🦲', '👩', '👩‍🦰', '🧑‍🦰', '👩‍🦱', '🧑‍🦱', '👩‍🦳', '🧑‍🦳', '👩‍🦲', '🧑‍🦲', '👱‍♀️', '👱‍♂️', '🧓', '👴', '👵', '🙍', '🙍‍♂️', '🙍‍♀️', '🙎', '🙎‍♂️', '🙎‍♀️', '🙅', '🙅‍♂️', '🙅‍♀️', '🙆', '🙆‍♂️', '🙆‍♀️', '💁', '💁‍♂️', '💁‍♀️', '🙋', '🙋‍♂️', '🙋‍♀️', '🧏', '🧏‍♂️', '🧏‍♀️', '🙇', '🙇‍♂️', '🙇‍♀️', '🤦', '🤦‍♂️', '🤦‍♀️', '🤷', '🤷‍♂️', '🤷‍♀️', '🧑‍⚕️', '👨‍⚕️', '👩‍⚕️', '🧑‍🎓', '👨‍🎓', '👩‍🎓', '🧑‍🏫', '👨‍🏫', '👩‍🏫']);
    
    // Documents State
    const sharedDocuments = ref([]);
    const showCodeEditor = ref(false);
    const selectedLanguage = ref('javascript');
    const codeContent = ref('// Escreva seu código colaborativo aqui\n');
    const codeOutput = ref('');
    const codeCollaborators = ref([]);
    
    // Study Groups
    const studyGroups = ref([
      {
        id: 1,
        name: 'Cardiologia Avançada',
        subject: 'Medicina',
        icon: 'fas fa-heartbeat',
        color: '#ef4444',
        members: 8,
        maxMembers: 12,
        nextSession: 'Hoje, 19:00',
        tags: ['ECG', 'Arritmias', 'Casos Clínicos'],
        isActive: true
      },
      {
        id: 2,
        name: 'Neuroanatomia',
        subject: 'Medicina',
        icon: 'fas fa-brain',
        color: '#a855f7',
        members: 5,
        maxMembers: 8,
        nextSession: 'Amanhã, 14:00',
        tags: ['Anatomia', 'Sistema Nervoso', 'Prática'],
        isActive: false
      },
      {
        id: 3,
        name: 'Farmacologia Clínica',
        subject: 'Medicina',
        icon: 'fas fa-pills',
        color: '#10b981',
        members: 10,
        maxMembers: 15,
        nextSession: 'Sexta, 16:00',
        tags: ['Farmácos', 'Interações', 'Casos'],
        isActive: false
      }
    ]);
    
    // Analytics
    const totalStudyTime = ref('45h 32m');
    const collaborativeSessions = ref(23);
    const totalInteractions = ref(342);
    const collaborationScore = ref(87);
    const recentActivities = ref([
      {
        id: 1,
        description: 'Participou de sessão de Cardiologia',
        timestamp: Date.now() - 3600000,
        icon: 'fas fa-video',
        color: '#667eea'
      },
      {
        id: 2,
        description: 'Compartilhou anotações de Neurologia',
        timestamp: Date.now() - 7200000,
        icon: 'fas fa-share',
        color: '#10b981'
      },
      {
        id: 3,
        description: 'Completou quiz colaborativo',
        timestamp: Date.now() - 10800000,
        icon: 'fas fa-check-circle',
        color: '#f59e0b'
      }
    ]);
    
    // Computed
    const connectionStatusText = computed(() => {
      switch (connectionStatus.value) {
        case 'connected': return 'Conectado';
        case 'connecting': return 'Conectando...';
        case 'disconnected': return 'Desconectado';
        case 'error': return 'Erro de Conexão';
        default: return 'Desconhecido';
      }
    });
    
    const codeLines = computed(() => {
      return codeContent.value.split('\n').length;
    });
    
    const typingUsersText = computed(() => {
      if (typingUsers.value.length === 0) return '';
      if (typingUsers.value.length === 1) return `${typingUsers.value[0]} está digitando...`;
      if (typingUsers.value.length === 2) return `${typingUsers.value[0]} e ${typingUsers.value[1]} estão digitando...`;
      return `${typingUsers.value.slice(0, -1).join(', ')} e ${typingUsers.value.slice(-1)} estão digitando...`;
    });
    
    // WebRTC Methods
    const initializeWebRTC = async () => {
      try {
        // Get user media
        localStream.value = await navigator.mediaDevices.getUserMedia({
          video: { 
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: 'user'
          },
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        });
        
        if (localVideo.value) {
          localVideo.value.srcObject = localStream.value;
        }
        
        // Initialize audio level monitoring
        initializeAudioLevelMonitoring();
        
      } catch (error) {
        console.error('Erro ao acessar mídia:', error);
      }
    };
    
    const initializeSignaling = () => {
      // Simulate WebSocket connection
      connectionStatus.value = 'connecting';
      
      // In production, connect to actual signaling server
      setTimeout(() => {
        connectionStatus.value = 'connected';
        networkLatency.value = Math.floor(Math.random() * 50) + 10;
      }, 1000);
      
      // Simulate network stats update
      setInterval(() => {
        networkLatency.value = Math.floor(Math.random() * 50) + 10;
        bandwidthUsage.value = (Math.random() * 2).toFixed(1);
      }, 5000);
    };
    
    const createRoom = async () => {
      if (!roomId.value) {
        roomId.value = `ROOM-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
      }
      
      currentRoom.value = {
        id: roomId.value,
        name: roomId.value,
        code: roomId.value,
        createdAt: Date.now(),
        ownerId: userId.value
      };
      
      // Send system message
      messages.value.push({
        id: Date.now(),
        type: 'system',
        content: `Sala ${roomId.value} criada com sucesso!`,
        timestamp: Date.now()
      });
      
      // Initialize room features
      initializeWhiteboard();
      await initializeWebRTC();
    };
    
    const joinRoom = async () => {
      if (!roomId.value) return;
      
      currentRoom.value = {
        id: roomId.value,
        name: roomId.value,
        code: roomId.value,
        joinedAt: Date.now()
      };
      
      // Simulate joining room
      messages.value.push({
        id: Date.now(),
        type: 'system',
        content: `Você entrou na sala ${roomId.value}`,
        timestamp: Date.now()
      });
      
      // Simulate other users
      setTimeout(() => {
        connectedPeers.value = [
          {
            id: 'peer1',
            name: 'Maria Silva',
            audioEnabled: true,
            videoEnabled: true,
            isSpeaking: false,
            isPresenting: false,
            isWriting: false
          },
          {
            id: 'peer2',
            name: 'João Santos',
            audioEnabled: true,
            videoEnabled: false,
            isSpeaking: false,
            isPresenting: false,
            isWriting: true
          }
        ];
      }, 1500);
      
      // Initialize room features
      initializeWhiteboard();
      await initializeWebRTC();
    };
    
    const leaveRoom = () => {
      // Clean up peer connections
      Object.values(peerConnections.value).forEach(pc => pc.close());
      peerConnections.value = {};
      
      // Stop local stream
      if (localStream.value) {
        localStream.value.getTracks().forEach(track => track.stop());
      }
      
      // Reset room state
      currentRoom.value = null;
      connectedPeers.value = [];
      messages.value = [];
      
      // Send system message
      console.log('Saiu da sala');
    };
    
    const toggleVideo = () => {
      if (localStream.value) {
        const videoTrack = localStream.value.getVideoTracks()[0];
        if (videoTrack) {
          videoTrack.enabled = !videoTrack.enabled;
          videoEnabled.value = videoTrack.enabled;
        }
      }
    };
    
    const toggleAudio = () => {
      if (localStream.value) {
        const audioTrack = localStream.value.getAudioTracks()[0];
        if (audioTrack) {
          audioTrack.enabled = !audioTrack.enabled;
          audioEnabled.value = audioTrack.enabled;
        }
      }
    };
    
    const shareScreen = async () => {
      try {
        if (isScreenSharing.value) {
          // Stop screen sharing
          const videoTrack = localStream.value.getVideoTracks()[0];
          if (videoTrack) {
            videoTrack.stop();
          }
          
          // Restore camera
          const stream = await navigator.mediaDevices.getUserMedia({ video: true });
          const newVideoTrack = stream.getVideoTracks()[0];
          
          // Replace track in peer connections
          Object.values(peerConnections.value).forEach(pc => {
            const sender = pc.getSenders().find(s => s.track?.kind === 'video');
            if (sender) {
              sender.replaceTrack(newVideoTrack);
            }
          });
          
          localVideo.value.srcObject = stream;
          isScreenSharing.value = false;
        } else {
          // Start screen sharing
          const displayStream = await navigator.mediaDevices.getDisplayMedia({
            video: true,
            audio: false
          });
          
          const screenTrack = displayStream.getVideoTracks()[0];
          
          // Replace track in peer connections
          Object.values(peerConnections.value).forEach(pc => {
            const sender = pc.getSenders().find(s => s.track?.kind === 'video');
            if (sender) {
              sender.replaceTrack(screenTrack);
            }
          });
          
          screenTrack.onended = () => {
            isScreenSharing.value = false;
            shareScreen(); // Restore camera
          };
          
          isScreenSharing.value = true;
        }
      } catch (error) {
        console.error('Erro ao compartilhar tela:', error);
      }
    };
    
    const initializeAudioLevelMonitoring = () => {
      if (!localStream.value) return;
      
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(localStream.value);
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      
      microphone.connect(analyser);
      
      const checkAudioLevel = () => {
        analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
        audioLevels.value[0] = average;
        
        requestAnimationFrame(checkAudioLevel);
      };
      
      checkAudioLevel();
    };
    
    // Whiteboard Methods
    const initializeWhiteboard = () => {
      nextTick(() => {
        if (whiteboardCanvas.value) {
          const canvas = whiteboardCanvas.value;
          canvas.width = canvas.offsetWidth;
          canvas.height = 600;
          
          drawingContext.value = canvas.getContext('2d');
          drawingContext.value.lineCap = 'round';
          drawingContext.value.lineJoin = 'round';
        }
      });
    };
    
    const startDrawing = (e) => {
      if (!drawingContext.value) return;
      
      isDrawing.value = true;
      const rect = whiteboardCanvas.value.getBoundingClientRect();
      
      const x = (e.clientX || e.touches[0].clientX) - rect.left;
      const y = (e.clientY || e.touches[0].clientY) - rect.top;
      
      lastPosition.value = { x, y };
      
      // Broadcast cursor position
      broadcastCursorPosition(x, y);
    };
    
    const draw = (e) => {
      if (!isDrawing.value || !drawingContext.value) return;
      
      const rect = whiteboardCanvas.value.getBoundingClientRect();
      const x = (e.clientX || e.touches[0].clientX) - rect.left;
      const y = (e.clientY || e.touches[0].clientY) - rect.top;
      
      drawingContext.value.strokeStyle = selectedColor.value;
      drawingContext.value.lineWidth = brushSize.value;
      
      if (selectedTool.value.id === 'pen') {
        drawingContext.value.globalCompositeOperation = 'source-over';
        drawingContext.value.beginPath();
        drawingContext.value.moveTo(lastPosition.value.x, lastPosition.value.y);
        drawingContext.value.lineTo(x, y);
        drawingContext.value.stroke();
      } else if (selectedTool.value.id === 'eraser') {
        drawingContext.value.globalCompositeOperation = 'destination-out';
        drawingContext.value.beginPath();
        drawingContext.value.arc(x, y, brushSize.value, 0, Math.PI * 2);
        drawingContext.value.fill();
      }
      
      lastPosition.value = { x, y };
      
      // Broadcast drawing data
      broadcastDrawing({
        tool: selectedTool.value.id,
        color: selectedColor.value,
        size: brushSize.value,
        from: lastPosition.value,
        to: { x, y }
      });
    };
    
    const stopDrawing = () => {
      isDrawing.value = false;
    };
    
    const selectTool = (tool) => {
      selectedTool.value = tool;
    };
    
    const clearWhiteboard = () => {
      if (drawingContext.value) {
        drawingContext.value.clearRect(0, 0, whiteboardCanvas.value.width, whiteboardCanvas.value.height);
        broadcastClear();
      }
    };
    
    const saveWhiteboard = () => {
      if (whiteboardCanvas.value) {
        const dataURL = whiteboardCanvas.value.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = `whiteboard_${Date.now()}.png`;
        link.href = dataURL;
        link.click();
      }
    };
    
    const broadcastDrawing = (drawingData) => {
      // Send drawing data through data channel
      Object.values(dataChannels.value).forEach(channel => {
        if (channel.readyState === 'open') {
          channel.send(JSON.stringify({
            type: 'drawing',
            data: drawingData
          }));
        }
      });
    };
    
    const broadcastCursorPosition = (x, y) => {
      // Broadcast cursor position to peers
      Object.values(dataChannels.value).forEach(channel => {
        if (channel.readyState === 'open') {
          channel.send(JSON.stringify({
            type: 'cursor',
            data: { x, y, userId: userId.value, userName: userName.value, color: selectedColor.value }
          }));
        }
      });
    };
    
    const broadcastClear = () => {
      Object.values(dataChannels.value).forEach(channel => {
        if (channel.readyState === 'open') {
          channel.send(JSON.stringify({ type: 'clear' }));
        }
      });
    };
    
    // Chat Methods
    const sendMessage = () => {
      if (!messageText.value.trim()) return;
      
      const message = {
        id: Date.now(),
        userId: userId.value,
        userName: userName.value,
        userInitials: userName.value.split(' ').map(n => n[0]).join('').toUpperCase(),
        type: 'text',
        content: messageText.value,
        timestamp: Date.now(),
        reactions: []
      };
      
      messages.value.push(message);
      messageText.value = '';
      
      // Broadcast message
      broadcastMessage(message);
      
      // Scroll to bottom
      nextTick(() => {
        if (chatContainer.value) {
          chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
        }
      });
    };
    
    const handleTyping = () => {
      // Broadcast typing indicator
      broadcastTyping();
      
      // Clear previous timeout
      if (typingTimeout.value) {
        clearTimeout(typingTimeout.value);
      }
      
      // Set new timeout
      typingTimeout.value = setTimeout(() => {
        broadcastStopTyping();
      }, 1000);
    };
    
    const broadcastMessage = (message) => {
      Object.values(dataChannels.value).forEach(channel => {
        if (channel.readyState === 'open') {
          channel.send(JSON.stringify({
            type: 'message',
            data: message
          }));
        }
      });
    };
    
    const broadcastTyping = () => {
      Object.values(dataChannels.value).forEach(channel => {
        if (channel.readyState === 'open') {
          channel.send(JSON.stringify({
            type: 'typing',
            data: { userId: userId.value, userName: userName.value }
          }));
        }
      });
    };
    
    const broadcastStopTyping = () => {
      Object.values(dataChannels.value).forEach(channel => {
        if (channel.readyState === 'open') {
          channel.send(JSON.stringify({
            type: 'stopTyping',
            data: { userId: userId.value }
          }));
        }
      });
    };
    
    const toggleEmojis = () => {
      showEmojiPicker.value = !showEmojiPicker.value;
    };
    
    const insertEmoji = (emoji) => {
      messageText.value += emoji;
      showEmojiPicker.value = false;
      messageInput.value.focus();
    };
    
    const attachFile = () => {
      // Implement file attachment
      console.log('Attach file');
    };
    
    const toggleReaction = (messageId, emoji) => {
      const message = messages.value.find(m => m.id === messageId);
      if (!message) return;
      
      if (!message.reactions) {
        message.reactions = [];
      }
      
      const existingReaction = message.reactions.find(r => r.emoji === emoji);
      
      if (existingReaction) {
        if (existingReaction.users.includes(userId.value)) {
          existingReaction.users = existingReaction.users.filter(u => u !== userId.value);
          existingReaction.count--;
          
          if (existingReaction.count === 0) {
            message.reactions = message.reactions.filter(r => r.emoji !== emoji);
          }
        } else {
          existingReaction.users.push(userId.value);
          existingReaction.count++;
        }
      } else {
        message.reactions.push({
          emoji,
          count: 1,
          users: [userId.value]
        });
      }
    };
    
    const viewImage = (imageUrl) => {
      window.open(imageUrl, '_blank');
    };
    
    const downloadFile = (fileUrl, fileName) => {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      link.click();
    };
    
    const copyCode = (code) => {
      navigator.clipboard.writeText(code);
      // Show toast notification
    };
    
    // Document Methods
    const handleDrop = (e) => {
      e.preventDefault();
      const files = Array.from(e.dataTransfer.files);
      uploadFiles(files);
    };
    
    const handleFileSelect = (e) => {
      const files = Array.from(e.target.files);
      uploadFiles(files);
    };
    
    const uploadFiles = (files) => {
      files.forEach(file => {
        const document = {
          id: Date.now() + Math.random(),
          name: file.name,
          type: file.type,
          size: formatFileSize(file.size),
          owner: userName.value,
          ownerId: userId.value,
          uploadedAt: Date.now(),
          url: URL.createObjectURL(file)
        };
        
        sharedDocuments.value.push(document);
        
        // Broadcast document upload
        broadcastDocument(document);
      });
    };
    
    const broadcastDocument = (document) => {
      Object.values(dataChannels.value).forEach(channel => {
        if (channel.readyState === 'open') {
          channel.send(JSON.stringify({
            type: 'document',
            data: document
          }));
        }
      });
    };
    
    const openDocument = (doc) => {
      window.open(doc.url, '_blank');
    };
    
    const downloadDocument = (doc) => {
      const link = document.createElement('a');
      link.href = doc.url;
      link.download = doc.name;
      link.click();
    };
    
    const shareDocument = (doc) => {
      // Implement share functionality
      console.log('Share document:', doc.name);
    };
    
    const deleteDocument = (doc) => {
      const index = sharedDocuments.value.findIndex(d => d.id === doc.id);
      if (index > -1) {
        sharedDocuments.value.splice(index, 1);
      }
    };
    
    const getFileIcon = (type) => {
      if (type.includes('pdf')) return 'fas fa-file-pdf';
      if (type.includes('word') || type.includes('document')) return 'fas fa-file-word';
      if (type.includes('sheet') || type.includes('excel')) return 'fas fa-file-excel';
      if (type.includes('presentation') || type.includes('powerpoint')) return 'fas fa-file-powerpoint';
      if (type.includes('image')) return 'fas fa-file-image';
      if (type.includes('video')) return 'fas fa-file-video';
      if (type.includes('audio')) return 'fas fa-file-audio';
      if (type.includes('zip') || type.includes('rar')) return 'fas fa-file-archive';
      if (type.includes('text')) return 'fas fa-file-alt';
      return 'fas fa-file';
    };
    
    // Code Editor Methods
    const handleCodeChange = () => {
      // Broadcast code changes
      broadcastCode();
      
      // Update collaborator position
      const cursorPosition = codeTextarea.value.selectionStart;
      const lines = codeContent.value.substring(0, cursorPosition).split('\n');
      const currentLine = lines.length;
      
      broadcastCodeCursor(currentLine);
    };
    
    const broadcastCode = () => {
      Object.values(dataChannels.value).forEach(channel => {
        if (channel.readyState === 'open') {
          channel.send(JSON.stringify({
            type: 'code',
            data: {
              content: codeContent.value,
              language: selectedLanguage.value
            }
          }));
        }
      });
    };
    
    const broadcastCodeCursor = (line) => {
      Object.values(dataChannels.value).forEach(channel => {
        if (channel.readyState === 'open') {
          channel.send(JSON.stringify({
            type: 'codeCursor',
            data: {
              userId: userId.value,
              userName: userName.value,
              line,
              color: selectedColor.value
            }
          }));
        }
      });
    };
    
    const runCode = () => {
      // Simulate code execution
      codeOutput.value = 'Executando código...\n';
      
      setTimeout(() => {
        if (selectedLanguage.value === 'javascript') {
          try {
            // Simple eval for demo (not safe for production)
            const result = eval(codeContent.value);
            codeOutput.value = `Saída: ${result}`;
          } catch (error) {
            codeOutput.value = `Erro: ${error.message}`;
          }
        } else {
          codeOutput.value = `Linguagem ${selectedLanguage.value} não suportada nesta demo`;
        }
      }, 1000);
    };
    
    const shareCode = () => {
      const message = {
        id: Date.now(),
        userId: userId.value,
        userName: userName.value,
        userInitials: userName.value.split(' ').map(n => n[0]).join('').toUpperCase(),
        type: 'code',
        content: codeContent.value,
        language: selectedLanguage.value,
        timestamp: Date.now()
      };
      
      messages.value.push(message);
      broadcastMessage(message);
    };
    
    const closeCodeEditor = () => {
      showCodeEditor.value = false;
    };
    
    const clearOutput = () => {
      codeOutput.value = '';
    };
    
    // Study Group Methods
    const joinStudyGroup = (group) => {
      console.log('Joining study group:', group.name);
      // Implement join logic
    };
    
    const createStudyGroup = () => {
      console.log('Create new study group');
      // Implement create group modal
    };
    
    // Utility Methods
    const copyRoomCode = () => {
      if (currentRoom.value) {
        navigator.clipboard.writeText(currentRoom.value.code);
        // Show toast notification
      }
    };
    
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    };
    
    const formatDate = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleDateString('pt-BR');
    };
    
    const formatTimeAgo = (timestamp) => {
      const diff = Date.now() - timestamp;
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);
      
      if (days > 0) return `${days}d atrás`;
      if (hours > 0) return `${hours}h atrás`;
      if (minutes > 0) return `${minutes}m atrás`;
      return 'Agora';
    };
    
    const formatFileSize = (bytes) => {
      if (bytes < 1024) return bytes + ' B';
      if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
      if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
      return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
    };
    
    const closeScreenShare = () => {
      activeScreenShare.value = null;
    };
    
    // Lifecycle
    onMounted(() => {
      initializeSignaling();
      
      // Simulate some initial data
      setTimeout(() => {
        messages.value.push({
          id: 1,
          type: 'system',
          content: 'Bem-vindo ao Centro de Colaboração!',
          timestamp: Date.now()
        });
      }, 500);
    });
    
    onUnmounted(() => {
      // Clean up
      if (currentRoom.value) {
        leaveRoom();
      }
      
      if (signalingSocket.value) {
        signalingSocket.value.close();
      }
    });
    
    return {
      // Refs
      localVideo,
      whiteboardCanvas,
      chatContainer,
      messageInput,
      codeTextarea,
      
      // State
      userId,
      userName,
      connectionStatus,
      connectionStatusText,
      networkLatency,
      bandwidthUsage,
      roomId,
      currentRoom,
      connectedPeers,
      videoEnabled,
      audioEnabled,
      isScreenSharing,
      activeScreenShare,
      audioLevels,
      drawingTools,
      selectedTool,
      selectedColor,
      brushSize,
      remoteCursors,
      messages,
      messageText,
      typingUsers,
      typingUsersText,
      showEmojiPicker,
      emojis,
      sharedDocuments,
      showCodeEditor,
      selectedLanguage,
      codeContent,
      codeOutput,
      codeLines,
      codeCollaborators,
      studyGroups,
      totalStudyTime,
      collaborativeSessions,
      totalInteractions,
      collaborationScore,
      recentActivities,
      
      // Methods
      createRoom,
      joinRoom,
      leaveRoom,
      toggleVideo,
      toggleAudio,
      shareScreen,
      closeScreenShare,
      startDrawing,
      draw,
      stopDrawing,
      selectTool,
      clearWhiteboard,
      saveWhiteboard,
      sendMessage,
      handleTyping,
      toggleEmojis,
      insertEmoji,
      attachFile,
      toggleReaction,
      viewImage,
      downloadFile,
      copyCode,
      handleDrop,
      handleFileSelect,
      openDocument,
      downloadDocument,
      shareDocument,
      deleteDocument,
      getFileIcon,
      handleCodeChange,
      runCode,
      shareCode,
      closeCodeEditor,
      clearOutput,
      joinStudyGroup,
      createStudyGroup,
      copyRoomCode,
      formatTime,
      formatDate,
      formatTimeAgo
    };
  }
};
</script>

<style scoped>
.collaboration-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f1e 0%, #1a1a2e 100%);
  color: #fff;
  padding: 2rem;
  display: grid;
  gap: 2rem;
}

/* Collaboration Hub */
.collab-hub {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.hub-title {
  font-size: 2rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Connection Status */
.connection-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #6b7280;
}

.status-indicator.connected .status-dot {
  background: #10b981;
  box-shadow: 0 0 10px #10b981;
}

.status-indicator.connecting .status-dot {
  background: #f59e0b;
  animation: pulse 1s infinite;
}

.status-indicator.error .status-dot {
  background: #ef4444;
}

.network-stats {
  display: flex;
  gap: 2rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.9rem;
}

.stat i {
  color: #667eea;
}

/* Room Controls */
.room-controls {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 2rem;
  align-items: center;
}

.room-input {
  display: flex;
  gap: 1rem;
}

.room-id-input {
  flex: 1;
  padding: 0.8rem 1.2rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 1rem;
}

.room-id-input::placeholder {
  color: #6b7280;
}

.create-btn,
.join-btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.create-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.join-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.join-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.current-room {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.current-room h3 {
  font-size: 1.1rem;
  color: #e5e7eb;
}

.room-code {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 8px;
}

.room-code span {
  font-family: monospace;
  font-size: 1.1rem;
  color: #667eea;
}

.copy-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  padding: 0.3rem;
  transition: all 0.3s ease;
}

.copy-btn:hover {
  color: #764ba2;
}

.leave-btn {
  padding: 0.6rem 1.2rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.leave-btn:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* Video Conference */
.video-conference {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.video-grid {
  display: grid;
  gap: 1rem;
  height: 600px;
}

.video-grid.grid-1 {
  grid-template-columns: 1fr;
}

.video-grid.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.video-grid.grid-3,
.video-grid.grid-4 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.video-grid.grid-5,
.video-grid.grid-6 {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: 1fr 1fr;
}

.video-container {
  position: relative;
  background: #000;
  border-radius: 15px;
  overflow: hidden;
}

.video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, transparent 60%, rgba(0, 0, 0, 0.7));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-container:hover .video-overlay {
  opacity: 1;
}

.user-name {
  font-size: 0.9rem;
  font-weight: 600;
}

.video-controls {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.control-btn.muted {
  background: rgba(239, 68, 68, 0.5);
}

.control-btn.active {
  background: rgba(102, 126, 234, 0.5);
}

.user-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
}

.user-status i {
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.user-status i.speaking {
  color: #10b981;
  animation: pulse 1s infinite;
}

.user-status i.presenting {
  color: #667eea;
}

.user-status i.writing {
  color: #f59e0b;
}

.audio-indicator {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 2px;
  height: 20px;
}

.audio-bar {
  width: 3px;
  background: #10b981;
  border-radius: 2px;
  transition: height 0.1s ease;
}

/* Screen Share View */
.screen-share-view {
  position: fixed;
  inset: 2rem;
  background: rgba(0, 0, 0, 0.95);
  border-radius: 20px;
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.close-share-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-share-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.screen-share-view video {
  flex: 1;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Collaborative Whiteboard */
.collaborative-whiteboard {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.whiteboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.whiteboard-header h3 {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.whiteboard-tools {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.tool-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.tool-btn.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

.color-picker {
  position: relative;
}

.color-input {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.brush-size {
  width: 100px;
}

.clear-btn,
.save-btn {
  padding: 0.6rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.clear-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #ef4444;
}

.save-btn:hover {
  background: rgba(16, 185, 129, 0.2);
  border-color: #10b981;
  color: #10b981;
}

.whiteboard-canvas-container {
  position: relative;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
}

.whiteboard-canvas-container canvas {
  display: block;
  cursor: crosshair;
}

.remote-cursor {
  position: absolute;
  pointer-events: none;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.remote-cursor i {
  font-size: 1.2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.cursor-name {
  position: absolute;
  top: 20px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  white-space: nowrap;
}

/* Real-time Chat */
.realtime-chat {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  height: 700px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chat-header h3 {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.chat-actions {
  display: flex;
  gap: 0.5rem;
}

.emoji-btn,
.attach-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: none;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-btn:hover,
.attach-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin-bottom: 1rem;
}

.message {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  animation: messageSlide 0.3s ease;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.own {
  flex-direction: row-reverse;
}

.message.system {
  justify-content: center;
}

.message-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.message.own .message-avatar {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.message-content {
  max-width: 70%;
}

.message-header {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 0.3rem;
}

.message-author {
  font-weight: 600;
  color: #e5e7eb;
  font-size: 0.9rem;
}

.message-time {
  color: #6b7280;
  font-size: 0.75rem;
}

.message-body {
  background: rgba(255, 255, 255, 0.05);
  padding: 0.8rem 1rem;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.message.own .message-body {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
}

.message-body p {
  color: #e5e7eb;
  line-height: 1.5;
  margin: 0;
}

.message-image img {
  max-width: 300px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.message-image img:hover {
  transform: scale(1.05);
}

.message-file {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
}

.message-file i {
  color: #667eea;
  font-size: 1.5rem;
}

.download-btn {
  margin-left: auto;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.3rem;
  transition: color 0.3s ease;
}

.download-btn:hover {
  color: #667eea;
}

.message-code {
  position: relative;
}

.message-code pre {
  background: rgba(0, 0, 0, 0.5);
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  margin: 0;
}

.message-code code {
  color: #60a5fa;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
}

.copy-code-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #9ca3af;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.copy-code-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.system-message {
  color: #9ca3af;
  font-style: italic;
  text-align: center;
}

.message-reactions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.reaction {
  padding: 0.2rem 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reaction:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  color: #9ca3af;
  font-size: 0.9rem;
}

.typing-dots {
  display: flex;
  gap: 0.3rem;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  background: #667eea;
  border-radius: 50%;
  animation: typingDot 1.4s infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingDot {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Emoji Picker */
.emoji-picker {
  position: absolute;
  bottom: 120px;
  right: 2rem;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  z-index: 10;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.emoji {
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.3rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  text-align: center;
}

.emoji:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.2);
}

/* Message Input */
.message-input-container {
  display: flex;
  gap: 1rem;
}

.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 0.8rem 1rem;
  color: #fff;
  font-size: 1rem;
  resize: none;
}

.message-input::placeholder {
  color: #6b7280;
}

.send-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Shared Documents */
.shared-documents {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.documents-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.documents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.document-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.document-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  border-color: rgba(102, 126, 234, 0.3);
}

.document-icon {
  width: 50px;
  height: 50px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.document-icon i {
  font-size: 1.5rem;
  color: #667eea;
}

.document-info {
  flex: 1;
}

.document-info h4 {
  font-size: 1rem;
  margin-bottom: 0.3rem;
  color: #e5e7eb;
}

.document-info p {
  color: #9ca3af;
  font-size: 0.85rem;
  margin-bottom: 0.3rem;
}

.document-date {
  color: #6b7280;
  font-size: 0.8rem;
}

.document-actions {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.doc-action-btn {
  background: rgba(255, 255, 255, 0.05);
  border: none;
  color: #9ca3af;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.doc-action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.doc-action-btn.danger:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* Upload Area */
.upload-area {
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.upload-area i {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.upload-area p {
  color: #9ca3af;
  margin-bottom: 1rem;
}

.browse-btn {
  padding: 0.6rem 1.2rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid #667eea;
  border-radius: 8px;
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
}

.browse-btn:hover {
  background: rgba(102, 126, 234, 0.3);
}

/* Live Code Editor */
.live-code-editor {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.editor-header h3 {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.editor-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.language-select {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #fff;
}

.language-select option {
  background: #1a1a2e;
}

.run-btn,
.share-code-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.run-btn:hover {
  background: rgba(16, 185, 129, 0.2);
  border-color: #10b981;
  color: #10b981;
}

.share-code-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

.close-editor-btn {
  background: rgba(239, 68, 68, 0.1);
  border: none;
  color: #ef4444;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-editor-btn:hover {
  background: rgba(239, 68, 68, 0.2);
}

.editor-container {
  position: relative;
}

.code-editor {
  display: flex;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.line-numbers {
  background: rgba(255, 255, 255, 0.03);
  padding: 1rem 0.5rem;
  color: #6b7280;
  font-family: monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  user-select: none;
}

.line-number {
  text-align: right;
  height: 20px;
}

.code-textarea {
  flex: 1;
  background: transparent;
  border: none;
  color: #e5e7eb;
  padding: 1rem;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  resize: none;
  min-height: 400px;
}

.code-collaborators {
  position: absolute;
  top: 0;
  right: -200px;
  width: 180px;
}

.collaborator-indicator {
  position: absolute;
  right: 0;
  width: 150px;
  height: 2px;
  display: flex;
  align-items: center;
  transition: top 0.2s ease;
}

.collaborator-indicator::before {
  content: '';
  width: 100%;
  height: 100%;
  background: currentColor;
  opacity: 0.5;
}

.collaborator-name {
  position: absolute;
  right: 0;
  top: -20px;
  background: rgba(0, 0, 0, 0.8);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  white-space: nowrap;
}

.code-output {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  padding: 1rem;
}

.output-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  color: #9ca3af;
}

.clear-output-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.3rem;
  transition: color 0.3s ease;
}

.clear-output-btn:hover {
  color: #ef4444;
}

.code-output pre {
  color: #10b981;
  font-family: monospace;
  font-size: 0.9rem;
  margin: 0;
}

/* Study Groups */
.study-groups {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.groups-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.groups-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.group-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.group-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.group-card.active {
  border-color: rgba(102, 126, 234, 0.3);
  background: rgba(102, 126, 234, 0.05);
}

.group-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.group-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.group-icon i {
  font-size: 1.5rem;
  color: #fff;
}

.group-info {
  flex: 1;
}

.group-info h4 {
  font-size: 1.1rem;
  margin-bottom: 0.3rem;
  color: #e5e7eb;
}

.group-info p {
  color: #9ca3af;
  font-size: 0.9rem;
}

.group-stats {
  text-align: right;
}

.member-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.9rem;
}

.group-schedule {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #9ca3af;
  font-size: 0.9rem;
}

.group-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.group-tags .tag {
  padding: 0.3rem 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  font-size: 0.8rem;
  color: #e5e7eb;
}

.join-group-btn {
  width: 100%;
  padding: 0.8rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 8px;
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.join-group-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.create-group-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 10px;
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
}

.create-group-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

/* Collaboration Analytics */
.collaboration-analytics {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.analytics-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.analytics-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.analytics-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.card-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.card-icon i {
  font-size: 1.5rem;
  color: #667eea;
}

.card-content h4 {
  color: #9ca3af;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.metric {
  font-size: 2rem;
  font-weight: 700;
  color: #e5e7eb;
  margin-bottom: 0.5rem;
}

.trend {
  font-size: 0.85rem;
  color: #9ca3af;
}

.trend.up {
  color: #10b981;
}

.trend.down {
  color: #ef4444;
}

.score-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.5s ease;
}

/* Activity Timeline */
.activity-timeline {
  margin-top: 2rem;
}

.activity-timeline h4 {
  color: #e5e7eb;
  margin-bottom: 1rem;
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(255, 255, 255, 0.1);
}

.timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
}

.timeline-marker {
  position: absolute;
  left: -22px;
  top: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 0.8rem;
}

.timeline-content p {
  color: #e5e7eb;
  margin-bottom: 0.3rem;
}

.timeline-time {
  color: #6b7280;
  font-size: 0.85rem;
}

/* Transitions */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .collaboration-container {
    padding: 1rem;
  }
  
  .room-controls {
    grid-template-columns: 1fr;
  }
  
  .video-grid {
    height: 400px;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}
</style>