<template>
  <div class="neural-engine-container">
    <!-- Neural Network Visualization -->
    <div class="neural-network-3d" ref="neuralCanvas">
      <canvas ref="brainCanvas"></canvas>
      <div class="neural-overlay">
        <div class="neural-stats">
          <div class="stat-item">
            <i class="fas fa-brain"></i>
            <span>{{ activeNeurons }} neurônios ativos</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-link"></i>
            <span>{{ synapticConnections }} conexões</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-bolt"></i>
            <span>{{ processingSpeed }}ms latência</span>
          </div>
        </div>
      </div>
    </div>

    <!-- AI Prediction Engine -->
    <div class="prediction-engine">
      <h3 class="engine-title">
        <i class="fas fa-crystal-ball"></i>
        Motor de Predição Neural
      </h3>
      
      <div class="predictions-grid">
        <!-- Learning Path Prediction -->
        <div class="prediction-card">
          <div class="prediction-header">
            <i class="fas fa-route"></i>
            <h4>Caminho de Aprendizado Ótimo</h4>
          </div>
          <div class="prediction-content">
            <div class="path-visualization">
              <div v-for="(node, index) in learningPath" :key="index" 
                   class="path-node" 
                   :class="{ active: node.current, completed: node.completed }">
                <div class="node-icon">
                  <i :class="node.icon"></i>
                </div>
                <div class="node-info">
                  <h5>{{ node.title }}</h5>
                  <p>{{ node.duration }} • {{ node.difficulty }}</p>
                  <div class="node-progress">
                    <div class="progress-fill" :style="{ width: node.progress + '%' }"></div>
                  </div>
                </div>
                <div v-if="index < learningPath.length - 1" class="path-connector">
                  <div class="connector-line"></div>
                  <div class="connector-pulse"></div>
                </div>
              </div>
            </div>
            <div class="path-insights">
              <div class="insight-box">
                <i class="fas fa-clock"></i>
                <span>Tempo estimado: {{ totalPathTime }}</span>
              </div>
              <div class="insight-box">
                <i class="fas fa-trophy"></i>
                <span>Taxa de sucesso: {{ successRate }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Prediction -->
        <div class="prediction-card">
          <div class="prediction-header">
            <i class="fas fa-chart-line"></i>
            <h4>Previsão de Desempenho</h4>
          </div>
          <div class="prediction-content">
            <canvas ref="performancePredictionChart"></canvas>
            <div class="prediction-metrics">
              <div class="metric">
                <span class="metric-label">Próxima semana</span>
                <span class="metric-value">+{{ weeklyImprovement }}%</span>
              </div>
              <div class="metric">
                <span class="metric-label">Próximo mês</span>
                <span class="metric-value">+{{ monthlyImprovement }}%</span>
              </div>
              <div class="metric">
                <span class="metric-label">Meta anual</span>
                <span class="metric-value">{{ yearlyTarget }}% domínio</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Knowledge Gap Analysis -->
        <div class="prediction-card">
          <div class="prediction-header">
            <i class="fas fa-microscope"></i>
            <h4>Análise de Lacunas de Conhecimento</h4>
          </div>
          <div class="prediction-content">
            <div class="knowledge-map">
              <svg ref="knowledgeMapSvg" class="knowledge-svg"></svg>
            </div>
            <div class="gap-list">
              <div v-for="gap in knowledgeGaps" :key="gap.id" 
                   class="gap-item"
                   :class="{ critical: gap.priority === 'high' }">
                <div class="gap-indicator">
                  <div class="gap-severity" :style="{ width: gap.severity + '%' }"></div>
                </div>
                <div class="gap-info">
                  <h5>{{ gap.topic }}</h5>
                  <p>{{ gap.description }}</p>
                  <button @click="addressGap(gap)" class="gap-action">
                    <i class="fas fa-wrench"></i>
                    Corrigir
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quantum Learning Optimizer -->
    <div class="quantum-optimizer">
      <h3 class="optimizer-title">
        <i class="fas fa-atom"></i>
        Otimizador Quântico de Aprendizado
      </h3>
      
      <div class="quantum-interface">
        <div class="quantum-state">
          <div class="quantum-orb">
            <div class="orb-core"></div>
            <div class="orb-ring ring-1"></div>
            <div class="orb-ring ring-2"></div>
            <div class="orb-ring ring-3"></div>
            <div class="quantum-particles">
              <span v-for="i in 8" :key="i" class="particle"></span>
            </div>
          </div>
          <div class="quantum-readout">
            <h4>Estado Quântico: {{ quantumState }}</h4>
            <p>Coerência: {{ coherenceLevel }}%</p>
          </div>
        </div>

        <div class="optimization-controls">
          <div class="control-group">
            <label>Modo de Otimização</label>
            <div class="mode-selector">
              <button v-for="mode in optimizationModes" :key="mode.id"
                      @click="setOptimizationMode(mode)"
                      class="mode-btn"
                      :class="{ active: currentMode === mode.id }">
                <i :class="mode.icon"></i>
                {{ mode.name }}
              </button>
            </div>
          </div>

          <div class="control-group">
            <label>Parâmetros Quânticos</label>
            <div class="quantum-sliders">
              <div class="slider-item">
                <span>Superposição</span>
                <input type="range" v-model="quantumParams.superposition" 
                       min="0" max="100" @input="updateQuantumState">
                <span>{{ quantumParams.superposition }}%</span>
              </div>
              <div class="slider-item">
                <span>Entrelaçamento</span>
                <input type="range" v-model="quantumParams.entanglement" 
                       min="0" max="100" @input="updateQuantumState">
                <span>{{ quantumParams.entanglement }}%</span>
              </div>
              <div class="slider-item">
                <span>Decoerência</span>
                <input type="range" v-model="quantumParams.decoherence" 
                       min="0" max="100" @input="updateQuantumState">
                <span>{{ quantumParams.decoherence }}%</span>
              </div>
            </div>
          </div>

          <button @click="executeQuantumOptimization" class="quantum-execute-btn">
            <i class="fas fa-play"></i>
            Executar Otimização Quântica
          </button>
        </div>

        <div class="optimization-results" v-if="optimizationResults">
          <h4>Resultados da Otimização</h4>
          <div class="results-grid">
            <div class="result-item">
              <i class="fas fa-brain"></i>
              <span>Eficiência Neural</span>
              <strong>+{{ optimizationResults.neuralEfficiency }}%</strong>
            </div>
            <div class="result-item">
              <i class="fas fa-memory"></i>
              <span>Retenção de Memória</span>
              <strong>+{{ optimizationResults.memoryRetention }}%</strong>
            </div>
            <div class="result-item">
              <i class="fas fa-tachometer-alt"></i>
              <span>Velocidade de Processamento</span>
              <strong>+{{ optimizationResults.processingSpeed }}%</strong>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Holographic Study Interface -->
    <div class="holographic-interface">
      <h3 class="holo-title">
        <i class="fas fa-cube"></i>
        Interface Holográfica de Estudo
      </h3>
      
      <div class="holo-viewport">
        <div class="holo-stage" ref="holoStage">
          <div class="holo-content">
            <div class="holo-model" :class="{ active: holoActive }">
              <div class="holo-layer layer-1"></div>
              <div class="holo-layer layer-2"></div>
              <div class="holo-layer layer-3"></div>
              <div class="holo-data">
                <h4>{{ currentHoloTopic }}</h4>
                <div class="holo-info-grid">
                  <div v-for="info in holoInfo" :key="info.id" class="holo-info-item">
                    <i :class="info.icon"></i>
                    <span>{{ info.value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="holo-controls">
            <button @click="rotateHolo('left')" class="holo-btn">
              <i class="fas fa-chevron-left"></i>
            </button>
            <button @click="toggleHolo" class="holo-btn main">
              <i :class="holoActive ? 'fas fa-pause' : 'fas fa-play'"></i>
            </button>
            <button @click="rotateHolo('right')" class="holo-btn">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>

        <div class="holo-topics">
          <h5>Tópicos Disponíveis</h5>
          <div class="topic-grid">
            <button v-for="topic in holoTopics" :key="topic.id"
                    @click="loadHoloTopic(topic)"
                    class="topic-btn"
                    :class="{ active: currentHoloTopic === topic.name }">
              <i :class="topic.icon"></i>
              <span>{{ topic.name }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Biometric Learning Tracker -->
    <div class="biometric-tracker">
      <h3 class="tracker-title">
        <i class="fas fa-heartbeat"></i>
        Rastreador Biométrico de Aprendizado
      </h3>
      
      <div class="biometric-dashboard">
        <div class="biometric-monitors">
          <!-- Heart Rate Variability -->
          <div class="monitor-card">
            <div class="monitor-header">
              <i class="fas fa-heart"></i>
              <span>Variabilidade Cardíaca</span>
            </div>
            <div class="monitor-display">
              <canvas ref="hrvChart"></canvas>
              <div class="monitor-value">
                <span class="value">{{ hrv }}</span>
                <span class="unit">ms</span>
              </div>
            </div>
            <div class="monitor-status" :class="hrvStatus">
              {{ hrvStatusText }}
            </div>
          </div>

          <!-- Brain Wave Activity -->
          <div class="monitor-card">
            <div class="monitor-header">
              <i class="fas fa-brain"></i>
              <span>Atividade Cerebral</span>
            </div>
            <div class="monitor-display">
              <div class="brainwave-viz">
                <div v-for="wave in brainwaves" :key="wave.type" 
                     class="wave-bar">
                  <span class="wave-label">{{ wave.type }}</span>
                  <div class="wave-meter">
                    <div class="wave-fill" 
                         :style="{ width: wave.level + '%', backgroundColor: wave.color }">
                    </div>
                  </div>
                  <span class="wave-value">{{ wave.level }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Focus Level -->
          <div class="monitor-card">
            <div class="monitor-header">
              <i class="fas fa-eye"></i>
              <span>Nível de Foco</span>
            </div>
            <div class="monitor-display">
              <div class="focus-gauge">
                <svg class="gauge-svg" viewBox="0 0 200 100">
                  <path d="M 10 90 A 80 80 0 0 1 190 90" 
                        fill="none" 
                        stroke="#e0e0e0" 
                        stroke-width="20"/>
                  <path d="M 10 90 A 80 80 0 0 1 190 90" 
                        fill="none" 
                        :stroke="focusColor" 
                        stroke-width="20"
                        :stroke-dasharray="`${focusLevel * 2.5} 250`"/>
                </svg>
                <div class="gauge-center">
                  <span class="gauge-value">{{ focusLevel }}%</span>
                  <span class="gauge-label">Foco</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="biometric-recommendations">
          <h4>Recomendações Baseadas em Biometria</h4>
          <div class="recommendation-list">
            <div v-for="rec in biometricRecommendations" :key="rec.id" 
                 class="bio-recommendation"
                 :class="rec.type">
              <i :class="rec.icon"></i>
              <div class="rec-content">
                <h5>{{ rec.title }}</h5>
                <p>{{ rec.description }}</p>
              </div>
              <button @click="applyRecommendation(rec)" class="rec-action">
                Aplicar
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Time Dilation Study Mode -->
    <div class="time-dilation-mode">
      <h3 class="dilation-title">
        <i class="fas fa-hourglass-half"></i>
        Modo de Dilatação Temporal
      </h3>
      
      <div class="dilation-interface">
        <div class="time-visualizer">
          <div class="time-spiral">
            <div class="spiral-core"></div>
            <div class="spiral-ring" v-for="i in 5" :key="i" 
                 :style="{ animationDelay: i * 0.2 + 's' }"></div>
            <div class="time-particles">
              <span v-for="j in 20" :key="j" class="time-particle"></span>
            </div>
          </div>
          
          <div class="time-metrics">
            <div class="metric-item">
              <label>Tempo Real</label>
              <span>{{ realTime }}</span>
            </div>
            <div class="metric-item">
              <label>Tempo Percebido</label>
              <span>{{ perceivedTime }}</span>
            </div>
            <div class="metric-item">
              <label>Fator de Dilatação</label>
              <span>{{ dilationFactor }}x</span>
            </div>
          </div>
        </div>

        <div class="dilation-controls">
          <div class="control-row">
            <label>Intensidade da Dilatação</label>
            <div class="intensity-selector">
              <button v-for="level in dilationLevels" :key="level.value"
                      @click="setDilationLevel(level)"
                      class="intensity-btn"
                      :class="{ active: currentDilation === level.value }">
                {{ level.name }}
              </button>
            </div>
          </div>

          <div class="control-row">
            <label>Modo de Estudo</label>
            <select v-model="studyMode" @change="updateDilationMode" class="mode-select">
              <option value="deep">Aprendizado Profundo</option>
              <option value="speed">Leitura Rápida</option>
              <option value="memorization">Memorização</option>
              <option value="practice">Prática Intensiva</option>
            </select>
          </div>

          <button @click="activateTimeDilation" 
                  class="activate-dilation-btn"
                  :class="{ active: dilationActive }">
            <i class="fas fa-power-off"></i>
            {{ dilationActive ? 'Desativar' : 'Ativar' }} Dilatação Temporal
          </button>
        </div>

        <div class="dilation-effects" v-if="dilationActive">
          <div class="effect-item" v-for="effect in dilationEffects" :key="effect.id">
            <i :class="effect.icon" :style="{ color: effect.color }"></i>
            <span>{{ effect.description }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useStore } from 'vuex';
import Chart from 'chart.js/auto';

export default {
  name: 'StudyAssistantNeural',
  
  setup() {
    const store = useStore();
    
    // Refs
    const neuralCanvas = ref(null);
    const brainCanvas = ref(null);
    const performancePredictionChart = ref(null);
    const knowledgeMapSvg = ref(null);
    const hrvChart = ref(null);
    const holoStage = ref(null);
    
    // Neural Network State
    const activeNeurons = ref(1247);
    const synapticConnections = ref(8934);
    const processingSpeed = ref(23);
    
    // Learning Path
    const learningPath = ref([
      {
        id: 1,
        title: 'Fundamentos de Anatomia',
        icon: 'fas fa-bone',
        duration: '2h',
        difficulty: 'Básico',
        progress: 100,
        completed: true,
        current: false
      },
      {
        id: 2,
        title: 'Sistema Cardiovascular',
        icon: 'fas fa-heart',
        duration: '3h',
        difficulty: 'Intermediário',
        progress: 65,
        completed: false,
        current: true
      },
      {
        id: 3,
        title: 'Farmacologia Aplicada',
        icon: 'fas fa-pills',
        duration: '4h',
        difficulty: 'Avançado',
        progress: 0,
        completed: false,
        current: false
      }
    ]);
    
    const totalPathTime = computed(() => {
      const totalMinutes = learningPath.value.reduce((acc, node) => {
        const hours = parseInt(node.duration);
        return acc + (hours * 60);
      }, 0);
      return `${Math.floor(totalMinutes / 60)}h ${totalMinutes % 60}min`;
    });
    
    const successRate = ref(87);
    const weeklyImprovement = ref(12);
    const monthlyImprovement = ref(35);
    const yearlyTarget = ref(95);
    
    // Knowledge Gaps
    const knowledgeGaps = ref([
      {
        id: 1,
        topic: 'Neurotransmissores',
        description: 'Lacuna em mecanismos de ação dos neurotransmissores',
        severity: 75,
        priority: 'high'
      },
      {
        id: 2,
        topic: 'Diagnóstico Diferencial',
        description: 'Dificuldade em diferenciar condições similares',
        severity: 45,
        priority: 'medium'
      }
    ]);
    
    // Quantum Optimizer
    const quantumState = ref('Superposição');
    const coherenceLevel = ref(92);
    const currentMode = ref('balanced');
    const optimizationModes = ref([
      { id: 'speed', name: 'Velocidade', icon: 'fas fa-rocket' },
      { id: 'depth', name: 'Profundidade', icon: 'fas fa-anchor' },
      { id: 'balanced', name: 'Balanceado', icon: 'fas fa-balance-scale' },
      { id: 'creative', name: 'Criativo', icon: 'fas fa-lightbulb' }
    ]);
    
    const quantumParams = ref({
      superposition: 75,
      entanglement: 60,
      decoherence: 15
    });
    
    const optimizationResults = ref(null);
    
    // Holographic Interface
    const holoActive = ref(false);
    const currentHoloTopic = ref('Sistema Nervoso');
    const holoInfo = ref([
      { id: 1, icon: 'fas fa-dna', value: '1.2M sinapses' },
      { id: 2, icon: 'fas fa-network-wired', value: '847 conexões' },
      { id: 3, icon: 'fas fa-microchip', value: '99.7% precisão' }
    ]);
    
    const holoTopics = ref([
      { id: 1, name: 'Sistema Nervoso', icon: 'fas fa-brain' },
      { id: 2, name: 'Sistema Cardiovascular', icon: 'fas fa-heart' },
      { id: 3, name: 'Sistema Respiratório', icon: 'fas fa-lungs' },
      { id: 4, name: 'Sistema Digestivo', icon: 'fas fa-stomach' }
    ]);
    
    // Biometric Tracking
    const hrv = ref(62);
    const hrvStatus = ref('optimal');
    const hrvStatusText = ref('Ótimo para aprendizado');
    const focusLevel = ref(78);
    const focusColor = computed(() => {
      if (focusLevel.value > 80) return '#4ade80';
      if (focusLevel.value > 60) return '#facc15';
      return '#ef4444';
    });
    
    const brainwaves = ref([
      { type: 'Delta', level: 10, color: '#7c3aed' },
      { type: 'Theta', level: 25, color: '#2563eb' },
      { type: 'Alpha', level: 45, color: '#10b981' },
      { type: 'Beta', level: 65, color: '#f59e0b' },
      { type: 'Gamma', level: 30, color: '#ef4444' }
    ]);
    
    const biometricRecommendations = ref([
      {
        id: 1,
        type: 'break',
        icon: 'fas fa-pause-circle',
        title: 'Pausa Recomendada',
        description: 'Sua HRV indica necessidade de descanso'
      },
      {
        id: 2,
        type: 'hydration',
        icon: 'fas fa-tint',
        title: 'Hidratação',
        description: 'Mantenha-se hidratado para melhor foco'
      }
    ]);
    
    // Time Dilation
    const dilationActive = ref(false);
    const realTime = ref('00:00:00');
    const perceivedTime = ref('00:00:00');
    const dilationFactor = ref(1.0);
    const currentDilation = ref(1);
    const studyMode = ref('deep');
    
    const dilationLevels = ref([
      { value: 1, name: 'Normal' },
      { value: 1.5, name: 'Moderado' },
      { value: 2, name: 'Intenso' },
      { value: 3, name: 'Extremo' }
    ]);
    
    const dilationEffects = ref([
      {
        id: 1,
        icon: 'fas fa-brain',
        description: 'Processamento neural acelerado',
        color: '#8b5cf6'
      },
      {
        id: 2,
        icon: 'fas fa-memory',
        description: 'Consolidação de memória otimizada',
        color: '#3b82f6'
      },
      {
        id: 3,
        icon: 'fas fa-bolt',
        description: 'Sinapses hiper-ativas',
        color: '#fbbf24'
      }
    ]);
    
    // Methods
    const initializeNeuralNetwork = () => {
      // Implementar visualização 3D da rede neural
      if (brainCanvas.value) {
        const ctx = brainCanvas.value.getContext('2d');
        // Desenhar rede neural animada
        animateNeuralNetwork(ctx);
      }
    };
    
    const animateNeuralNetwork = (ctx) => {
      // Animação da rede neural
      const animate = () => {
        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
        
        // Desenhar neurônios e conexões
        drawNeurons(ctx);
        drawSynapses(ctx);
        
        // Atualizar estatísticas
        activeNeurons.value = Math.floor(1200 + Math.random() * 100);
        synapticConnections.value = Math.floor(8900 + Math.random() * 200);
        processingSpeed.value = Math.floor(20 + Math.random() * 10);
        
        requestAnimationFrame(animate);
      };
      
      animate();
    };
    
    const drawNeurons = (ctx) => {
      // Implementação de desenho de neurônios
      const neurons = 50;
      for (let i = 0; i < neurons; i++) {
        const x = Math.random() * ctx.canvas.width;
        const y = Math.random() * ctx.canvas.height;
        const radius = 2 + Math.random() * 3;
        
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(139, 92, 246, ${0.5 + Math.random() * 0.5})`;
        ctx.fill();
      }
    };
    
    const drawSynapses = (ctx) => {
      // Implementação de desenho de sinapses
      const synapses = 30;
      for (let i = 0; i < synapses; i++) {
        ctx.beginPath();
        ctx.moveTo(Math.random() * ctx.canvas.width, Math.random() * ctx.canvas.height);
        ctx.lineTo(Math.random() * ctx.canvas.width, Math.random() * ctx.canvas.height);
        ctx.strokeStyle = `rgba(59, 130, 246, ${0.2 + Math.random() * 0.3})`;
        ctx.stroke();
      }
    };
    
    const initializePerformanceChart = () => {
      if (performancePredictionChart.value) {
        const ctx = performancePredictionChart.value.getContext('2d');
        new Chart(ctx, {
          type: 'line',
          data: {
            labels: ['Agora', 'Semana 1', 'Semana 2', 'Semana 3', 'Mês 1', 'Mês 2', 'Mês 3'],
            datasets: [
              {
                label: 'Desempenho Previsto',
                data: [75, 78, 82, 85, 88, 91, 95],
                borderColor: '#8b5cf6',
                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                tension: 0.4
              },
              {
                label: 'Intervalo de Confiança',
                data: [75, 76, 79, 82, 84, 87, 90],
                borderColor: '#94a3b8',
                borderDash: [5, 5],
                fill: false
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              y: {
                beginAtZero: false,
                min: 70,
                max: 100
              }
            }
          }
        });
      }
    };
    
    const addressGap = (gap) => {
      console.log('Addressing knowledge gap:', gap);
      // Implementar lógica para abordar lacuna de conhecimento
    };
    
    const setOptimizationMode = (mode) => {
      currentMode.value = mode.id;
      updateQuantumState();
    };
    
    const updateQuantumState = () => {
      const { superposition, entanglement, decoherence } = quantumParams.value;
      
      if (superposition > 80 && entanglement > 70) {
        quantumState.value = 'Entrelaçamento Máximo';
        coherenceLevel.value = 95;
      } else if (decoherence > 50) {
        quantumState.value = 'Decoerência';
        coherenceLevel.value = 45;
      } else {
        quantumState.value = 'Superposição';
        coherenceLevel.value = 75;
      }
    };
    
    const executeQuantumOptimization = async () => {
      // Simular otimização quântica
      optimizationResults.value = null;
      
      setTimeout(() => {
        optimizationResults.value = {
          neuralEfficiency: Math.floor(15 + Math.random() * 20),
          memoryRetention: Math.floor(20 + Math.random() * 25),
          processingSpeed: Math.floor(10 + Math.random() * 30)
        };
      }, 2000);
    };
    
    const toggleHolo = () => {
      holoActive.value = !holoActive.value;
    };
    
    const rotateHolo = (direction) => {
      // Implementar rotação do holograma
      console.log('Rotating holo:', direction);
    };
    
    const loadHoloTopic = (topic) => {
      currentHoloTopic.value = topic.name;
      // Atualizar informações do holograma
      holoInfo.value = [
        { id: 1, icon: 'fas fa-dna', value: `${Math.floor(Math.random() * 2000)}K dados` },
        { id: 2, icon: 'fas fa-network-wired', value: `${Math.floor(Math.random() * 1000)} conexões` },
        { id: 3, icon: 'fas fa-microchip', value: `${(95 + Math.random() * 4.9).toFixed(1)}% precisão` }
      ];
    };
    
    const initializeBiometrics = () => {
      // Simular dados biométricos
      setInterval(() => {
        hrv.value = 55 + Math.floor(Math.random() * 20);
        focusLevel.value = 70 + Math.floor(Math.random() * 25);
        
        // Atualizar ondas cerebrais
        brainwaves.value = brainwaves.value.map(wave => ({
          ...wave,
          level: Math.max(5, Math.min(95, wave.level + (Math.random() - 0.5) * 10))
        }));
        
        // Atualizar status HRV
        if (hrv.value > 65) {
          hrvStatus.value = 'optimal';
          hrvStatusText.value = 'Ótimo para aprendizado';
        } else if (hrv.value > 50) {
          hrvStatus.value = 'good';
          hrvStatusText.value = 'Bom para estudo';
        } else {
          hrvStatus.value = 'low';
          hrvStatusText.value = 'Considere uma pausa';
        }
      }, 3000);
    };
    
    const applyRecommendation = (rec) => {
      console.log('Applying recommendation:', rec);
      // Implementar aplicação de recomendação
    };
    
    const setDilationLevel = (level) => {
      currentDilation.value = level.value;
      dilationFactor.value = level.value;
    };
    
    const updateDilationMode = () => {
      // Atualizar efeitos baseados no modo
      console.log('Study mode updated:', studyMode.value);
    };
    
    const activateTimeDilation = () => {
      dilationActive.value = !dilationActive.value;
      
      if (dilationActive.value) {
        startTimeDilation();
      } else {
        stopTimeDilation();
      }
    };
    
    let realTimeInterval;
    let perceivedTimeInterval;
    
    const startTimeDilation = () => {
      let realSeconds = 0;
      let perceivedSeconds = 0;
      
      realTimeInterval = setInterval(() => {
        realSeconds++;
        realTime.value = formatTime(realSeconds);
      }, 1000);
      
      perceivedTimeInterval = setInterval(() => {
        perceivedSeconds += dilationFactor.value;
        perceivedTime.value = formatTime(Math.floor(perceivedSeconds));
      }, 1000);
    };
    
    const stopTimeDilation = () => {
      clearInterval(realTimeInterval);
      clearInterval(perceivedTimeInterval);
      realTime.value = '00:00:00';
      perceivedTime.value = '00:00:00';
    };
    
    const formatTime = (seconds) => {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };
    
    // Lifecycle
    onMounted(() => {
      initializeNeuralNetwork();
      initializePerformanceChart();
      initializeBiometrics();
      
      // Ajustar canvas para tela cheia
      if (brainCanvas.value) {
        brainCanvas.value.width = neuralCanvas.value.offsetWidth;
        brainCanvas.value.height = 200;
      }
    });
    
    onUnmounted(() => {
      stopTimeDilation();
    });
    
    return {
      // Refs
      neuralCanvas,
      brainCanvas,
      performancePredictionChart,
      knowledgeMapSvg,
      hrvChart,
      holoStage,
      
      // Neural Network
      activeNeurons,
      synapticConnections,
      processingSpeed,
      
      // Learning Path
      learningPath,
      totalPathTime,
      successRate,
      weeklyImprovement,
      monthlyImprovement,
      yearlyTarget,
      
      // Knowledge Gaps
      knowledgeGaps,
      addressGap,
      
      // Quantum Optimizer
      quantumState,
      coherenceLevel,
      currentMode,
      optimizationModes,
      quantumParams,
      optimizationResults,
      setOptimizationMode,
      updateQuantumState,
      executeQuantumOptimization,
      
      // Holographic Interface
      holoActive,
      currentHoloTopic,
      holoInfo,
      holoTopics,
      toggleHolo,
      rotateHolo,
      loadHoloTopic,
      
      // Biometric Tracking
      hrv,
      hrvStatus,
      hrvStatusText,
      focusLevel,
      focusColor,
      brainwaves,
      biometricRecommendations,
      applyRecommendation,
      
      // Time Dilation
      dilationActive,
      realTime,
      perceivedTime,
      dilationFactor,
      currentDilation,
      studyMode,
      dilationLevels,
      dilationEffects,
      setDilationLevel,
      updateDilationMode,
      activateTimeDilation
    };
  }
};
</script>

<style scoped>
.neural-engine-container {
  padding: 2rem;
  background: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
}

/* Neural Network Visualization */
.neural-network-3d {
  position: relative;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.1));
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 3rem;
  overflow: hidden;
}

.neural-network-3d canvas {
  width: 100%;
  height: 200px;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}

.neural-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.8);
  padding: 1rem;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.neural-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #e0e0e0;
}

.stat-item i {
  color: #8b5cf6;
}

/* Prediction Engine */
.prediction-engine {
  margin-bottom: 3rem;
}

.engine-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--primary-color);
}

.predictions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.prediction-card {
  background: var(--card-background);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.prediction-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.prediction-header i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.prediction-header h4 {
  margin: 0;
  font-size: 1.1rem;
}

.path-visualization {
  margin-bottom: 1.5rem;
}

.path-node {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 10px;
  margin-bottom: 1rem;
  position: relative;
  transition: all 0.3s ease;
}

.path-node.active {
  background: rgba(139, 92, 246, 0.2);
  border: 2px solid #8b5cf6;
}

.path-node.completed {
  opacity: 0.7;
}

.node-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.node-info {
  flex: 1;
}

.node-info h5 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.node-info p {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.node-progress {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.5s ease;
}

.path-connector {
  position: absolute;
  left: 25px;
  bottom: -1rem;
  width: 2px;
  height: 1rem;
}

.connector-line {
  width: 100%;
  height: 100%;
  background: var(--primary-color);
  opacity: 0.3;
}

.connector-pulse {
  position: absolute;
  top: 0;
  left: -4px;
  width: 10px;
  height: 10px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: pulse-down 2s ease-in-out infinite;
}

@keyframes pulse-down {
  0%, 100% {
    transform: translateY(0);
    opacity: 0;
  }
  50% {
    transform: translateY(1rem);
    opacity: 1;
  }
}

.path-insights {
  display: flex;
  gap: 1rem;
}

.insight-box {
  flex: 1;
  padding: 0.75rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.insight-box i {
  color: var(--primary-color);
}

/* Performance Prediction Chart */
.prediction-content canvas {
  height: 200px !important;
  margin-bottom: 1rem;
}

.prediction-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.metric {
  text-align: center;
  padding: 0.75rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 8px;
}

.metric-label {
  display: block;
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.metric-value {
  display: block;
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--primary-color);
}

/* Knowledge Gap Analysis */
.knowledge-map {
  height: 200px;
  margin-bottom: 1rem;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.knowledge-svg {
  width: 100%;
  height: 100%;
}

.gap-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.gap-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.gap-item.critical {
  border-left: 3px solid #ef4444;
}

.gap-indicator {
  width: 60px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.gap-severity {
  height: 100%;
  background: linear-gradient(90deg, #fbbf24, #ef4444);
  transition: width 0.5s ease;
}

.gap-info {
  flex: 1;
}

.gap-info h5 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
}

.gap-info p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.gap-action {
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.gap-action:hover {
  background: var(--primary-hover);
  transform: translateY(-2px);
}

/* Quantum Optimizer */
.quantum-optimizer {
  margin-bottom: 3rem;
}

.optimizer-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #10b981;
}

.quantum-interface {
  background: var(--card-background);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.quantum-state {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.quantum-orb {
  width: 150px;
  height: 150px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.orb-core {
  width: 50px;
  height: 50px;
  background: radial-gradient(circle, #10b981, #059669);
  border-radius: 50%;
  box-shadow: 0 0 50px #10b981;
  animation: pulse 2s ease-in-out infinite;
}

.orb-ring {
  position: absolute;
  border: 2px solid #10b981;
  border-radius: 50%;
  opacity: 0.3;
}

.ring-1 {
  width: 80px;
  height: 80px;
  animation: rotate 10s linear infinite;
}

.ring-2 {
  width: 110px;
  height: 110px;
  animation: rotate 15s linear infinite reverse;
}

.ring-3 {
  width: 140px;
  height: 140px;
  animation: rotate 20s linear infinite;
}

.quantum-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #10b981;
  border-radius: 50%;
  animation: orbit 5s linear infinite;
}

.particle:nth-child(1) { animation-delay: 0s; }
.particle:nth-child(2) { animation-delay: 0.625s; }
.particle:nth-child(3) { animation-delay: 1.25s; }
.particle:nth-child(4) { animation-delay: 1.875s; }
.particle:nth-child(5) { animation-delay: 2.5s; }
.particle:nth-child(6) { animation-delay: 3.125s; }
.particle:nth-child(7) { animation-delay: 3.75s; }
.particle:nth-child(8) { animation-delay: 4.375s; }

@keyframes orbit {
  from {
    transform: rotate(0deg) translateX(70px) rotate(0deg);
  }
  to {
    transform: rotate(360deg) translateX(70px) rotate(-360deg);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.quantum-readout h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
}

.quantum-readout p {
  margin: 0;
  color: var(--text-secondary);
}

.optimization-controls {
  margin-bottom: 2rem;
}

.control-group {
  margin-bottom: 1.5rem;
}

.control-group label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.mode-selector {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.mode-btn {
  padding: 0.75rem;
  background: rgba(16, 185, 129, 0.1);
  border: 2px solid transparent;
  border-radius: 8px;
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.mode-btn.active {
  background: rgba(16, 185, 129, 0.2);
  border-color: #10b981;
}

.mode-btn:hover {
  transform: translateY(-2px);
}

.quantum-sliders {
  display: grid;
  gap: 1rem;
}

.slider-item {
  display: grid;
  grid-template-columns: 120px 1fr 60px;
  align-items: center;
  gap: 1rem;
}

.slider-item input[type="range"] {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.slider-item input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: #10b981;
  border-radius: 50%;
  cursor: pointer;
}

.quantum-execute-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
}

.quantum-execute-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px -5px rgba(16, 185, 129, 0.3);
}

.optimization-results {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.optimization-results h4 {
  margin: 0 0 1rem 0;
  color: #10b981;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.result-item {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.result-item i {
  font-size: 1.5rem;
  color: #10b981;
  margin-bottom: 0.5rem;
  display: block;
}

.result-item span {
  display: block;
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.result-item strong {
  display: block;
  font-size: 1.2rem;
  color: #10b981;
}

/* Holographic Interface */
.holographic-interface {
  margin-bottom: 3rem;
}

.holo-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #3b82f6;
}

.holo-viewport {
  background: var(--card-background);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.holo-stage {
  position: relative;
  height: 300px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 15px;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.holo-content {
  position: relative;
  width: 200px;
  height: 200px;
  transform-style: preserve-3d;
  transform: rotateX(20deg) rotateY(45deg);
}

.holo-model {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.5s ease;
}

.holo-model.active {
  animation: holo-rotate 10s linear infinite;
}

@keyframes holo-rotate {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(360deg);
  }
}

.holo-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 2px solid;
  border-radius: 10px;
  box-shadow: 0 0 20px currentColor;
}

.layer-1 {
  border-color: #3b82f6;
  transform: translateZ(30px);
}

.layer-2 {
  border-color: #8b5cf6;
  transform: translateZ(0);
}

.layer-3 {
  border-color: #ec4899;
  transform: translateZ(-30px);
}

.holo-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.holo-data h4 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.holo-info-grid {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
}

.holo-info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.holo-controls {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
}

.holo-btn {
  width: 40px;
  height: 40px;
  background: rgba(59, 130, 246, 0.2);
  border: 2px solid #3b82f6;
  border-radius: 50%;
  color: #3b82f6;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.holo-btn.main {
  width: 50px;
  height: 50px;
  background: #3b82f6;
  color: white;
}

.holo-btn:hover {
  transform: scale(1.1);
}

.holo-topics h5 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.topic-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.topic-btn {
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  border: 2px solid transparent;
  border-radius: 8px;
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
}

.topic-btn.active {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
}

.topic-btn:hover {
  transform: translateY(-2px);
}

/* Biometric Tracker */
.biometric-tracker {
  margin-bottom: 3rem;
}

.tracker-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #ef4444;
}

.biometric-dashboard {
  background: var(--card-background);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.biometric-monitors {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.monitor-card {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 1.5rem;
}

.monitor-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.monitor-display {
  margin-bottom: 1rem;
}

.monitor-display canvas {
  width: 100%;
  height: 100px;
}

.monitor-value {
  text-align: center;
  margin-top: 1rem;
}

.monitor-value .value {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-color);
}

.monitor-value .unit {
  font-size: 1rem;
  color: var(--text-secondary);
  margin-left: 0.5rem;
}

.monitor-status {
  text-align: center;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.9rem;
}

.monitor-status.optimal {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.monitor-status.good {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.monitor-status.low {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.brainwave-viz {
  display: grid;
  gap: 0.75rem;
}

.wave-bar {
  display: grid;
  grid-template-columns: 60px 1fr 50px;
  align-items: center;
  gap: 1rem;
}

.wave-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.wave-meter {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.wave-fill {
  height: 100%;
  transition: width 0.5s ease;
}

.wave-value {
  text-align: right;
  font-size: 0.9rem;
  font-weight: 500;
}

.focus-gauge {
  position: relative;
  width: 200px;
  height: 100px;
  margin: 0 auto;
}

.gauge-svg {
  width: 100%;
  height: 100%;
}

.gauge-center {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.gauge-value {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--primary-color);
}

.gauge-label {
  display: block;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.biometric-recommendations h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.recommendation-list {
  display: grid;
  gap: 1rem;
}

.bio-recommendation {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
}

.bio-recommendation.break {
  border-left-color: #fbbf24;
}

.bio-recommendation.hydration {
  border-left-color: #3b82f6;
}

.bio-recommendation i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.rec-content {
  flex: 1;
}

.rec-content h5 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
}

.rec-content p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.rec-action {
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rec-action:hover {
  background: var(--primary-hover);
}

/* Time Dilation Mode */
.time-dilation-mode {
  margin-bottom: 3rem;
}

.dilation-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #f59e0b;
}

.dilation-interface {
  background: var(--card-background);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.time-visualizer {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.time-spiral {
  width: 150px;
  height: 150px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spiral-core {
  width: 30px;
  height: 30px;
  background: #f59e0b;
  border-radius: 50%;
  box-shadow: 0 0 30px #f59e0b;
  z-index: 10;
}

.spiral-ring {
  position: absolute;
  border: 2px solid #f59e0b;
  border-radius: 50%;
  opacity: 0.3;
  animation: spiral-expand 3s ease-in-out infinite;
}

.spiral-ring:nth-child(2) { width: 50px; height: 50px; }
.spiral-ring:nth-child(3) { width: 70px; height: 70px; }
.spiral-ring:nth-child(4) { width: 90px; height: 90px; }
.spiral-ring:nth-child(5) { width: 110px; height: 110px; }
.spiral-ring:nth-child(6) { width: 130px; height: 130px; }

@keyframes spiral-expand {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 0.6;
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0.3;
  }
}

.time-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.time-particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: #f59e0b;
  border-radius: 50%;
  animation: time-flow 4s linear infinite;
}

.time-particle:nth-child(1) { top: 10%; left: 50%; animation-delay: 0s; }
.time-particle:nth-child(2) { top: 20%; left: 70%; animation-delay: 0.2s; }
.time-particle:nth-child(3) { top: 30%; left: 80%; animation-delay: 0.4s; }
.time-particle:nth-child(4) { top: 50%; left: 90%; animation-delay: 0.6s; }
.time-particle:nth-child(5) { top: 70%; left: 80%; animation-delay: 0.8s; }
.time-particle:nth-child(6) { top: 80%; left: 70%; animation-delay: 1s; }
.time-particle:nth-child(7) { top: 90%; left: 50%; animation-delay: 1.2s; }
.time-particle:nth-child(8) { top: 80%; left: 30%; animation-delay: 1.4s; }
.time-particle:nth-child(9) { top: 70%; left: 20%; animation-delay: 1.6s; }
.time-particle:nth-child(10) { top: 50%; left: 10%; animation-delay: 1.8s; }
.time-particle:nth-child(11) { top: 30%; left: 20%; animation-delay: 2s; }
.time-particle:nth-child(12) { top: 20%; left: 30%; animation-delay: 2.2s; }
.time-particle:nth-child(13) { top: 15%; left: 40%; animation-delay: 2.4s; }
.time-particle:nth-child(14) { top: 25%; left: 60%; animation-delay: 2.6s; }
.time-particle:nth-child(15) { top: 40%; left: 75%; animation-delay: 2.8s; }
.time-particle:nth-child(16) { top: 60%; left: 75%; animation-delay: 3s; }
.time-particle:nth-child(17) { top: 75%; left: 60%; animation-delay: 3.2s; }
.time-particle:nth-child(18) { top: 75%; left: 40%; animation-delay: 3.4s; }
.time-particle:nth-child(19) { top: 60%; left: 25%; animation-delay: 3.6s; }
.time-particle:nth-child(20) { top: 40%; left: 25%; animation-delay: 3.8s; }

@keyframes time-flow {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: scale(1) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: scale(0) rotate(360deg);
    opacity: 0;
  }
}

.time-metrics {
  flex: 1;
  display: grid;
  gap: 1rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 8px;
}

.metric-item label {
  color: var(--text-secondary);
}

.metric-item span {
  font-weight: bold;
  color: #f59e0b;
}

.dilation-controls {
  margin-bottom: 2rem;
}

.control-row {
  margin-bottom: 1.5rem;
}

.control-row label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.intensity-selector {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.intensity-btn {
  padding: 0.75rem;
  background: rgba(245, 158, 11, 0.1);
  border: 2px solid transparent;
  border-radius: 8px;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.intensity-btn.active {
  background: rgba(245, 158, 11, 0.2);
  border-color: #f59e0b;
}

.mode-select {
  width: 100%;
  padding: 0.75rem;
  background: var(--background-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--text-color);
  cursor: pointer;
}

.activate-dilation-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #f59e0b, #dc2626);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
}

.activate-dilation-btn.active {
  background: linear-gradient(135deg, #dc2626, #991b1b);
}

.activate-dilation-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px -5px rgba(245, 158, 11, 0.3);
}

.dilation-effects {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.effect-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.effect-item i {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .predictions-grid,
  .biometric-monitors,
  .time-metrics {
    grid-template-columns: 1fr;
  }
  
  .mode-selector,
  .intensity-selector {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quantum-state,
  .time-visualizer {
    flex-direction: column;
  }
  
  .path-node {
    flex-direction: column;
    text-align: center;
  }
  
  .node-icon {
    margin-bottom: 0.5rem;
  }
  
  .path-connector {
    display: none;
  }
}

/* Dark Theme Adjustments */
:root[data-theme="dark"] {
  --card-background: rgba(30, 30, 30, 0.8);
  --primary-hover: #7c3aed;
}

/* Light Theme Adjustments */
:root[data-theme="light"] {
  --card-background: rgba(255, 255, 255, 0.9);
  --primary-hover: #6d28d9;
}
</style>