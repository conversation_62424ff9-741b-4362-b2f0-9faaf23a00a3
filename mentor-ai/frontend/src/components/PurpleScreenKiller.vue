<template>
  <div class="purple-screen-killer">
    <!-- Component exists only to inject global styles via mounted hook -->
  </div>
</template>

<script>
export default {
  name: 'PurpleScreenKiller',
  
  mounted() {
    // Inject global styles to kill purple screens
    this.injectGlobalStyles()
    
    // Aggressive DOM cleaning
    this.killPurpleScreens()
    
    // Monitor for any new purple elements
    this.observer = new MutationObserver(() => {
      this.killPurpleScreens()
    })
    
    this.observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    })
    
    // Check every second for safety
    this.interval = setInterval(() => {
      this.killPurpleScreens()
    }, 1000)
  },
  
  beforeUnmount() {
    if (this.observer) {
      this.observer.disconnect()
    }
    if (this.interval) {
      clearInterval(this.interval)
    }
  },
  
  methods: {
    injectGlobalStyles() {
      const style = document.createElement('style')
      style.textContent = `
        /* NUCLEAR OPTION - Kill all purple screens */
        * {
          background-image: none !important;
        }
        
        *[style*="#764ba2"],
        *[style*="118, 75, 162"],
        *[style*="purple"],
        *[style*="gradient"] {
          background: transparent !important;
          background-color: transparent !important;
          background-image: none !important;
        }
        
        body > div[style*="position: fixed"],
        body > div[style*="position: absolute"] {
          display: none !important;
        }
        
        .performance-dashboard {
          position: relative !important;
          z-index: 9999 !important;
          isolation: isolate !important;
        }
        
        .overlay,
        [class*="overlay"],
        [class*="modal-backdrop"],
        [class*="backdrop"] {
          display: none !important;
        }
        
        *::before,
        *::after {
          content: "" !important;
          background: transparent !important;
          z-index: -1 !important;
        }
        
        .performance-dashboard .card,
        .performance-dashboard .dashboard-header,
        .performance-dashboard .overview-section {
          background-color: white !important;
          position: relative !important;
          z-index: 100 !important;
        }
        
        * {
          opacity: 1 !important;
          visibility: visible !important;
          filter: none !important;
          backdrop-filter: none !important;
        }
      `
      document.head.appendChild(style)
    },
    
    killPurpleScreens() {
      // Find and destroy any purple elements
      const purpleSelectors = [
        '[style*="#764ba2"]',
        '[style*="118, 75, 162"]',
        '[style*="purple"]',
        '[style*="gradient"]',
        '.overlay',
        '[class*="overlay"]'
      ]
      
      purpleSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector)
        elements.forEach(el => {
          // Skip if it's a small element (like an icon)
          const rect = el.getBoundingClientRect()
          if (rect.width > 200 && rect.height > 200) {
            const styles = window.getComputedStyle(el)
            const bg = styles.backgroundColor + styles.background
            
            if (bg.includes('purple') || bg.includes('764ba2') || bg.includes('118, 75, 162')) {
              console.warn('Killing purple element:', el)
              el.style.cssText = 'display: none !important;'
            }
          }
        })
      })
      
      // Ensure no full-screen overlays
      const allDivs = document.querySelectorAll('div')
      allDivs.forEach(div => {
        const rect = div.getBoundingClientRect()
        const styles = window.getComputedStyle(div)
        
        // Check if it's a full-screen overlay
        if (rect.width >= window.innerWidth * 0.8 && 
            rect.height >= window.innerHeight * 0.8 &&
            (styles.position === 'fixed' || styles.position === 'absolute')) {
          
          // Check if it has content (not just an overlay)
          if (div.children.length === 0 || div.textContent.trim() === '') {
            console.warn('Killing potential overlay:', div)
            div.style.cssText = 'display: none !important;'
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.purple-screen-killer {
  display: none;
  position: absolute;
  pointer-events: none;
}
</style>