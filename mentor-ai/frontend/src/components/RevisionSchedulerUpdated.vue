<template>
  <div class="revision-scheduler">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Header -->
    <div class="revision-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <i class="fas fa-brain"></i>
          </div>
          <div class="page-info">
            <h1 class="page-title">Sistema de Revisões Espaçadas</h1>
            <p class="page-subtitle">Maximize sua retenção com metodologia científica</p>
          </div>
        </div>

        <div class="header-actions">
          <button @click="showQuickAdd = true" class="action-button primary">
            <i class="fas fa-plus"></i>
            <span>Nova Revisão</span>
          </button>
          <button @click="showSpacedRepetition = true" class="action-button">
            <i class="fas fa-magic"></i>
            <span>Gerar Revisões</span>
          </button>
          <button @click="showCycleGenerator = true" class="action-button special">
            <i class="fas fa-sync-alt"></i>
            <span>Ciclo Completo</span>
          </button>
          <button @click="showAnalytics = true" class="action-button">
            <i class="fas fa-chart-bar"></i>
            <span>Análise</span>
          </button>
        </div>
      </div>

      <!-- Stats Overview -->
      <div class="stats-overview">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-calendar-check"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ todayRevisions.length }}</div>
            <div class="stat-label">Revisões Hoje</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-fire"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ completedToday }}</div>
            <div class="stat-label">Concluídas Hoje</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ pendingRevisions.length }}</div>
            <div class="stat-label">Pendentes</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ completionRate }}%</div>
            <div class="stat-label">Taxa de Conclusão</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="revision-content">
      <!-- Smart Tools Section -->
      <section class="smart-tools-section">
        <h2 class="section-title">
          <i class="fas fa-tools"></i>
          Ferramentas Inteligentes de Revisão
        </h2>
        
        <div class="tools-grid">
          <!-- Complete Cycle Generator -->
          <div class="tool-card" @click="showCycleGenerator = true">
            <div class="tool-icon">
              <i class="fas fa-sync-alt"></i>
            </div>
            <h3>Gerador de Ciclo Completo</h3>
            <p>Crie um ciclo completo de revisões desde o estudo teórico até a maestria</p>
            <button class="tool-action">
              <i class="fas fa-play"></i>
              Configurar Ciclo
            </button>
          </div>
          
          <!-- Smart Templates -->
          <div class="tool-card" @click="showTemplates = true">
            <div class="tool-icon">
              <i class="fas fa-layer-group"></i>
            </div>
            <h3>Templates Inteligentes</h3>
            <p>Use templates pré-configurados baseados em matérias específicas</p>
            <button class="tool-action">
              <i class="fas fa-list"></i>
              Ver Templates
            </button>
          </div>
          
          <!-- Performance Predictor -->
          <div class="tool-card" @click="showPredictor = true">
            <div class="tool-icon">
              <i class="fas fa-brain"></i>
            </div>
            <h3>Preditor de Desempenho</h3>
            <p>IA que prevê seu desempenho e sugere ajustes no cronograma</p>
            <button class="tool-action">
              <i class="fas fa-chart-line"></i>
              Analisar
            </button>
          </div>
          
          <!-- Batch Scheduler -->
          <div class="tool-card" @click="showBatchScheduler = true">
            <div class="tool-icon">
              <i class="fas fa-calendar-plus"></i>
            </div>
            <h3>Agendamento em Lote</h3>
            <p>Agende múltiplas revisões de uma vez com base em padrões</p>
            <button class="tool-action">
              <i class="fas fa-plus-circle"></i>
              Agendar Lote
            </button>
          </div>
        </div>
      </section>
      
      <!-- Progress Overview -->
      <section class="progress-overview-section">
        <div class="overview-header">
          <h2 class="section-title">
            <i class="fas fa-tachometer-alt"></i>
            Visão Geral do Progresso
          </h2>
          <div class="time-filter">
            <button 
              v-for="period in ['7d', '30d', '90d', 'Tudo']" 
              :key="period"
              @click="selectedPeriod = period"
              :class="{ active: selectedPeriod === period }"
              class="filter-btn"
            >
              {{ period }}
            </button>
          </div>
        </div>
        
        <div class="progress-cards">
          <div class="progress-card">
            <div class="progress-icon success">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="progress-info">
              <h4>Taxa de Conclusão</h4>
              <div class="progress-value">{{ completionRate }}%</div>
              <div class="progress-trend" :class="completionTrend">
                <i :class="completionTrendIcon"></i>
                {{ completionTrendText }}
              </div>
            </div>
          </div>
          
          <div class="progress-card">
            <div class="progress-icon warning">
              <i class="fas fa-clock"></i>
            </div>
            <div class="progress-info">
              <h4>Tempo Médio</h4>
              <div class="progress-value">{{ averageStudyTime }}</div>
              <div class="progress-subtitle">por sessão</div>
            </div>
          </div>
          
          <div class="progress-card">
            <div class="progress-icon info">
              <i class="fas fa-brain"></i>
            </div>
            <div class="progress-info">
              <h4>Retenção Média</h4>
              <div class="progress-value">{{ averageRetention }}%</div>
              <div class="progress-subtitle">últimas 10 revisões</div>
            </div>
          </div>
          
          <div class="progress-card">
            <div class="progress-icon primary">
              <i class="fas fa-fire"></i>
            </div>
            <div class="progress-info">
              <h4>Sequência Atual</h4>
              <div class="progress-value">{{ currentStreak }} dias</div>
              <div class="progress-subtitle">melhor: {{ bestStreak }} dias</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Revision Type Tabs -->
      <div class="revision-tabs">
        <button 
          @click="activeTab = 'theoretical'" 
          :class="{ active: activeTab === 'theoretical' }"
          class="tab-button"
        >
          <i class="fas fa-book"></i>
          Revisões Teóricas
        </button>
        <button 
          @click="activeTab = 'questions'" 
          :class="{ active: activeTab === 'questions' }"
          class="tab-button"
        >
          <i class="fas fa-question-circle"></i>
          Revisões por Questões
        </button>
      </div>

      <!-- Theoretical Revisions Section -->
      <div v-if="activeTab === 'theoretical'" class="tab-content">
        <!-- First Contact Scheduler -->
        <section class="revision-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-calendar-plus"></i>
              Agendador de Primeiro Contato
            </h2>
            <button @click="showFirstContactModal = true" class="action-button mini">
              <i class="fas fa-plus"></i>
              Agendar
            </button>
          </div>
          
          <div class="methodology-info">
            <i class="fas fa-info-circle"></i>
            <p>Após o estudo teórico, agende o primeiro contato com questões baseado na dificuldade:</p>
            <ul>
              <li><strong>Fácil:</strong> Primeiro contato após 2 dias</li>
              <li><strong>Difícil:</strong> Primeiro contato após 1 dia</li>
            </ul>
          </div>
        </section>

        <!-- Today's Theoretical Revisions -->
        <section class="revision-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-sun"></i>
              Revisões Teóricas de Hoje
            </h2>
            <span class="section-count">{{ todayTheoreticalRevisions.length }}</span>
          </div>

          <div v-if="todayTheoreticalRevisions.length === 0" class="empty-state">
            <i class="fas fa-coffee"></i>
            <p>Nenhuma revisão teórica agendada para hoje</p>
            <button @click="showFirstContactModal = true" class="btn-primary">
              Agendar Primeiro Contato
            </button>
          </div>

          <div v-else class="revision-cards">
            <div 
              v-for="revision in todayTheoreticalRevisions" 
              :key="revision.id"
              class="revision-card"
              :class="{ completed: revision.completed }"
            >
            <div class="card-header">
              <div class="card-title">
                <h3>{{ revision.title }}</h3>
                <span class="subject-tag" :style="{ backgroundColor: getSubjectColor(revision.subject) }">
                  {{ getSubjectName(revision.subject) }}
                </span>
              </div>
              <div class="card-time">
                <i class="fas fa-clock"></i>
                {{ formatTime(revision.start) }}
              </div>
            </div>

            <div class="card-body">
              <div class="revision-meta">
                <span class="meta-item">
                  <i class="fas fa-tag"></i>
                  {{ revision.revisionType || 'Teórica' }}
                </span>
                <span class="meta-item priority" :data-priority="revision.priority?.toLowerCase()">
                  <i :class="getPriorityIcon(revision.priority)"></i>
                  {{ revision.priority }}
                </span>
              </div>

              <div class="progress-section">
                <div class="progress-header">
                  <span>Progresso</span>
                  <span>{{ revision.progress || 0 }}%</span>
                </div>
                <div class="progress-bar">
                  <div 
                    class="progress-fill" 
                    :style="{ width: (revision.progress || 0) + '%' }"
                  ></div>
                </div>
              </div>
            </div>

            <div class="card-actions">
              <button 
                v-if="!revision.completed"
                @click="startRevision(revision)" 
                class="btn-start"
              >
                <i class="fas fa-play"></i>
                Iniciar
              </button>
              <button 
                v-else
                class="btn-completed"
                disabled
              >
                <i class="fas fa-check"></i>
                Concluída
              </button>
              <button @click="viewDetails(revision)" class="btn-details">
                <i class="fas fa-eye"></i>
              </button>
            </div>
            </div>
          </div>
        </section>
      </div>

      <!-- Questions-based Revisions Section -->
      <div v-if="activeTab === 'questions'" class="tab-content">
        <!-- Performance-based Scheduler -->
        <section class="revision-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-chart-line"></i>
              Agendador Baseado em Desempenho
            </h2>
            <button @click="showQuestionsSchedulerModal = true" class="action-button mini">
              <i class="fas fa-plus"></i>
              Registrar Desempenho
            </button>
          </div>
          
          <div class="methodology-info">
            <i class="fas fa-info-circle"></i>
            <p>Após resolver blocos de 20-30 questões, o próximo contato é agendado baseado no seu desempenho:</p>
            <ul>
              <li><strong>≤ 50%:</strong> Revisão em 2 dias</li>
              <li><strong>50-55%:</strong> Revisão em 7 dias</li>
              <li><strong>55-60%:</strong> Revisão em 14 dias</li>
              <li><strong>60-65%:</strong> Revisão em 18 dias</li>
              <li><strong>65-75%:</strong> Revisão em 24 dias</li>
              <li><strong>75-80%:</strong> Revisão em 30 dias</li>
              <li><strong>> 80%:</strong> Revisão em 35 dias</li>
            </ul>
          </div>
        </section>

        <!-- Today's Question-based Revisions -->
        <section class="revision-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-tasks"></i>
              Revisões por Questões de Hoje
            </h2>
            <span class="section-count">{{ todayQuestionsRevisions.length }}</span>
          </div>

          <div v-if="todayQuestionsRevisions.length === 0" class="empty-state">
            <i class="fas fa-clipboard-check"></i>
            <p>Nenhuma revisão por questões agendada para hoje</p>
            <button @click="showQuestionsSchedulerModal = true" class="btn-primary">
              Registrar Desempenho
            </button>
          </div>

          <div v-else class="revision-cards">
            <div 
              v-for="revision in todayQuestionsRevisions" 
              :key="revision.id"
              class="revision-card questions-card"
              :class="{ completed: revision.completed }"
            >
              <div class="card-header">
                <div class="card-title">
                  <h3>{{ revision.title }}</h3>
                  <span class="subject-tag" :style="{ backgroundColor: getSubjectColor(revision.subject) }">
                    {{ getSubjectName(revision.subject) }}
                  </span>
                </div>
                <div class="card-time">
                  <i class="fas fa-clock"></i>
                  {{ formatTime(revision.start) }}
                </div>
              </div>

              <div class="card-body">
                <div class="revision-meta">
                  <span class="meta-item">
                    <i class="fas fa-percentage"></i>
                    Última performance: {{ revision.lastPerformance || '—' }}%
                  </span>
                  <span class="meta-item">
                    <i class="fas fa-list-ol"></i>
                    {{ revision.questionsCount || 30 }} questões
                  </span>
                </div>

                <div class="performance-indicators">
                  <div class="indicator" v-if="revision.lastPerformance">
                    <span class="indicator-label">Tendência</span>
                    <span :class="getPerformanceTrend(revision)">
                      <i :class="getPerformanceTrendIcon(revision)"></i>
                    </span>
                  </div>
                </div>
              </div>

              <div class="card-actions">
                <button 
                  v-if="!revision.completed"
                  @click="startQuestionsRevision(revision)" 
                  class="btn-start"
                >
                  <i class="fas fa-play"></i>
                  Fazer Questões
                </button>
                <button 
                  v-else
                  class="btn-completed"
                  disabled
                >
                  <i class="fas fa-check"></i>
                  Concluída
                </button>
                <button @click="viewDetails(revision)" class="btn-details">
                  <i class="fas fa-eye"></i>
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>

      <!-- Upcoming Revisions -->
      <section class="revision-section">
        <div class="section-header">
          <h2>
            <i class="fas fa-calendar-alt"></i>
            Próximas Revisões
          </h2>
          <span class="section-count">{{ upcomingRevisions.length }}</span>
        </div>

        <div v-if="upcomingRevisions.length === 0" class="empty-state">
          <i class="fas fa-calendar-times"></i>
          <p>Nenhuma revisão agendada</p>
        </div>

        <div v-else class="revision-timeline">
          <div 
            v-for="(group, date) in groupedUpcomingRevisions" 
            :key="date"
            class="timeline-group"
          >
            <div class="timeline-date">
              <span class="date-label">{{ formatDateLabel(date) }}</span>
              <span class="date-count">{{ group.length }} revisões</span>
            </div>

            <div class="timeline-items">
              <div 
                v-for="revision in group" 
                :key="revision.id"
                class="timeline-item"
              >
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                  <div class="timeline-header">
                    <h4>{{ revision.title }}</h4>
                    <span class="timeline-time">{{ formatTime(revision.start) }}</span>
                  </div>
                  <div class="timeline-meta">
                    <span class="subject-tag mini" :style="{ backgroundColor: getSubjectColor(revision.subject) }">
                      {{ getSubjectName(revision.subject) }}
                    </span>
                    <span class="type-tag">{{ revision.revisionType || 'Teórica' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Overdue Revisions -->
      <section v-if="overdueRevisions.length > 0" class="revision-section">
        <div class="section-header warning">
          <h2>
            <i class="fas fa-exclamation-triangle"></i>
            Revisões Atrasadas
          </h2>
          <span class="section-count">{{ overdueRevisions.length }}</span>
        </div>

        <div class="overdue-list">
          <div 
            v-for="revision in overdueRevisions" 
            :key="revision.id"
            class="overdue-item"
          >
            <div class="overdue-info">
              <h4>{{ revision.title }}</h4>
              <p>Atrasada por {{ getDaysOverdue(revision.start) }} dias</p>
            </div>
            <button @click="rescheduleRevision(revision)" class="btn-reschedule">
              <i class="fas fa-redo"></i>
              Reagendar
            </button>
          </div>
        </div>
      </section>
    </div>

    <!-- Modals -->
    <RevisionFormModal 
      v-if="showQuickAdd" 
      :subjects="subjects"
      @close="showQuickAdd = false"
      @save="handleNewRevision"
    />

    <!-- Spaced Repetition Modal (same as in CalendarView) -->
    <div v-if="showSpacedRepetition" class="modal-overlay" @click.self="showSpacedRepetition = false">
      <div class="spaced-repetition-modal">
        <div class="modal-header">
          <h2><i class="fas fa-brain"></i> Gerar Revisões Espaçadas</h2>
          <button @click="showSpacedRepetition = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="form-group">
            <label>Disciplina</label>
            <select v-model="spacedRepetitionForm.subject" class="form-control">
              <option value="">Selecione uma disciplina</option>
              <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                {{ subject.name }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label>Dificuldade</label>
            <div class="difficulty-selector">
              <button 
                v-for="level in ['Fácil', 'Médio', 'Difícil']" 
                :key="level"
                @click="spacedRepetitionForm.difficulty = level"
                :class="{ active: spacedRepetitionForm.difficulty === level }"
                class="difficulty-btn"
              >
                {{ level }}
              </button>
            </div>
          </div>
          
          <div class="form-group">
            <label>Data de Início</label>
            <input 
              type="date" 
              v-model="spacedRepetitionForm.startDate" 
              class="form-control"
              :min="today"
            />
          </div>
          
          <div class="form-group">
            <label>Tipo de Revisão</label>
            <select v-model="spacedRepetitionForm.revisionType" class="form-control">
              <option value="Teórica">Teórica</option>
              <option value="Prática">Prática</option>
              <option value="Mista">Mista</option>
              <option value="Flashcards">Flashcards</option>
              <option value="Resumo">Resumo</option>
            </select>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="showSpacedRepetition = false" class="btn-secondary">
            Cancelar
          </button>
          <button @click="generateSpacedRepetition" class="btn-primary" :disabled="!canGenerateSpacedRepetition">
            <i class="fas fa-magic"></i> Gerar Revisões
          </button>
        </div>
      </div>
    </div>
    
    <!-- First Contact Modal -->
    <div v-if="showFirstContactModal" class="modal-overlay" @click.self="showFirstContactModal = false">
      <div class="spaced-repetition-modal">
        <div class="modal-header">
          <h2><i class="fas fa-calendar-plus"></i> Agendar Primeiro Contato</h2>
          <button @click="showFirstContactModal = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="form-group">
            <label>Título do Conteúdo</label>
            <input 
              v-model="firstContactForm.title" 
              type="text" 
              class="form-control"
              placeholder="Ex: Anatomia Cardiovascular"
            />
          </div>
          
          <div class="form-group">
            <label>Disciplina</label>
            <select v-model="firstContactForm.subject" class="form-control">
              <option value="">Selecione uma disciplina</option>
              <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                {{ subject.name }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label>Data do Estudo Teórico</label>
            <input 
              type="date" 
              v-model="firstContactForm.studyDate" 
              class="form-control"
            />
          </div>
          
          <div class="form-group">
            <label>Dificuldade do Conteúdo</label>
            <div class="difficulty-selector">
              <button 
                v-for="level in ['Fácil', 'Difícil']" 
                :key="level"
                @click="firstContactForm.difficulty = level"
                :class="{ active: firstContactForm.difficulty === level }"
                class="difficulty-btn"
              >
                {{ level }}
              </button>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="showFirstContactModal = false" class="btn-secondary">
            Cancelar
          </button>
          <button @click="scheduleFirstContact" class="btn-primary" 
            :disabled="!firstContactForm.subject || !firstContactForm.title">
            <i class="fas fa-calendar-check"></i> Agendar Primeiro Contato
          </button>
        </div>
      </div>
    </div>
    
    <!-- Questions Performance Modal -->
    <div v-if="showQuestionsSchedulerModal" class="modal-overlay" @click.self="showQuestionsSchedulerModal = false">
      <div class="spaced-repetition-modal">
        <div class="modal-header">
          <h2><i class="fas fa-chart-line"></i> Registrar Desempenho em Questões</h2>
          <button @click="showQuestionsSchedulerModal = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="form-group">
            <label>Título do Conteúdo</label>
            <input 
              v-model="questionsPerformanceForm.title" 
              type="text" 
              class="form-control"
              placeholder="Ex: Farmacologia - Antibióticos"
            />
          </div>
          
          <div class="form-group">
            <label>Disciplina</label>
            <select v-model="questionsPerformanceForm.subject" class="form-control">
              <option value="">Selecione uma disciplina</option>
              <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                {{ subject.name }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label>Data da Realização</label>
            <input 
              type="date" 
              v-model="questionsPerformanceForm.date" 
              class="form-control"
            />
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>Total de Questões</label>
              <input 
                v-model.number="questionsPerformanceForm.totalQuestions" 
                type="number" 
                class="form-control"
                min="1"
                max="100"
              />
            </div>
            
            <div class="form-group">
              <label>Questões Corretas</label>
              <input 
                v-model.number="questionsPerformanceForm.correctAnswers" 
                type="number" 
                class="form-control"
                min="0"
                :max="questionsPerformanceForm.totalQuestions"
              />
            </div>
          </div>
          
          <div class="performance-preview" v-if="questionsPerformanceForm.totalQuestions > 0">
            <div class="preview-header">
              <span>Desempenho:</span>
              <span class="percentage">{{ ((questionsPerformanceForm.correctAnswers / questionsPerformanceForm.totalQuestions) * 100).toFixed(1) }}%</span>
            </div>
            <div class="preview-recommendation">
              <i class="fas fa-info-circle"></i>
              Próxima revisão será agendada em: 
              <strong>{{ getRecommendedDays() }} dias</strong>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="showQuestionsSchedulerModal = false" class="btn-secondary">
            Cancelar
          </button>
          <button @click="schedulePerformanceBasedRevision" class="btn-primary" 
            :disabled="!questionsPerformanceForm.subject || !questionsPerformanceForm.title || questionsPerformanceForm.totalQuestions <= 0">
            <i class="fas fa-calendar-check"></i> Agendar Revisão
          </button>
        </div>
      </div>
    </div>
    
    <!-- Complete Cycle Generator Modal -->
    <div v-if="showCycleGenerator" class="modal-overlay" @click.self="showCycleGenerator = false">
      <div class="cycle-generator-modal">
        <div class="modal-header">
          <h2><i class="fas fa-sync-alt"></i> Gerador de Ciclo Completo de Revisão</h2>
          <button @click="showCycleGenerator = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="cycle-steps">
            <div class="step" :class="{ active: cycleStep === 1 }">
              <div class="step-number">1</div>
              <div class="step-content">
                <h3>Configuração Inicial</h3>
                <div v-if="cycleStep === 1">
                  <div class="form-group">
                    <label>Nome do Ciclo</label>
                    <input 
                      v-model="cycleForm.name" 
                      type="text" 
                      class="form-control"
                      placeholder="Ex: Ciclo de Anatomia Completo"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label>Disciplina</label>
                    <select v-model="cycleForm.subject" class="form-control">
                      <option value="">Selecione uma disciplina</option>
                      <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                        {{ subject.name }}
                      </option>
                    </select>
                  </div>
                  
                  <div class="form-group">
                    <label>Número de Tópicos</label>
                    <input 
                      v-model.number="cycleForm.topicsCount" 
                      type="number" 
                      class="form-control"
                      min="1"
                      max="20"
                    />
                  </div>
                  
                  <div class="form-group">
                    <label>Data de Início</label>
                    <input 
                      type="date" 
                      v-model="cycleForm.startDate" 
                      class="form-control"
                      :min="today"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <div class="step" :class="{ active: cycleStep === 2 }">
              <div class="step-number">2</div>
              <div class="step-content">
                <h3>Tópicos e Dificuldade</h3>
                <div v-if="cycleStep === 2">
                  <div class="topics-list">
                    <div 
                      v-for="(topic, index) in cycleForm.topics" 
                      :key="index"
                      class="topic-item"
                    >
                      <div class="topic-number">{{ index + 1 }}</div>
                      <input 
                        v-model="topic.name" 
                        type="text" 
                        class="form-control"
                        :placeholder="`Tópico ${index + 1}`"
                      />
                      <div class="difficulty-mini">
                        <button 
                          v-for="level in ['Fácil', 'Difícil']" 
                          :key="level"
                          @click="topic.difficulty = level"
                          :class="{ active: topic.difficulty === level }"
                          class="diff-btn-mini"
                        >
                          {{ level[0] }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="step" :class="{ active: cycleStep === 3 }">
              <div class="step-number">3</div>
              <div class="step-content">
                <h3>Revisão e Confirmação</h3>
                <div v-if="cycleStep === 3" class="cycle-preview">
                  <h4>Resumo do Ciclo</h4>
                  <div class="preview-info">
                    <p><strong>Nome:</strong> {{ cycleForm.name }}</p>
                    <p><strong>Disciplina:</strong> {{ getSubjectName(cycleForm.subject) }}</p>
                    <p><strong>Início:</strong> {{ formatDate(cycleForm.startDate) }}</p>
                    <p><strong>Total de Revisões:</strong> {{ calculateTotalRevisions() }}</p>
                  </div>
                  
                  <div class="timeline-preview">
                    <h4>Cronograma Gerado</h4>
                    <div class="timeline-scroll">
                      <div 
                        v-for="(revision, index) in previewRevisions" 
                        :key="index"
                        class="preview-revision"
                      >
                        <div class="revision-date">{{ formatDate(revision.date) }}</div>
                        <div class="revision-info">
                          <span class="revision-title">{{ revision.title }}</span>
                          <span class="revision-type" :class="revision.type">{{ revision.typeLabel }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="cycle-progress">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: (cycleStep * 33.33) + '%' }"></div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button 
            v-if="cycleStep > 1" 
            @click="cycleStep--" 
            class="btn-secondary"
          >
            <i class="fas fa-arrow-left"></i> Voltar
          </button>
          <button 
            v-if="cycleStep < 3" 
            @click="nextCycleStep" 
            class="btn-primary"
            :disabled="!canProceedToNextStep"
          >
            Próximo <i class="fas fa-arrow-right"></i>
          </button>
          <button 
            v-if="cycleStep === 3" 
            @click="generateCompleteCycle" 
            class="btn-primary"
          >
            <i class="fas fa-magic"></i> Gerar Ciclo Completo
          </button>
        </div>
      </div>
    </div>
    
    <!-- Smart Templates Modal -->
    <div v-if="showTemplates" class="modal-overlay" @click.self="showTemplates = false">
      <div class="templates-modal">
        <div class="modal-header">
          <h2><i class="fas fa-layer-group"></i> Templates Inteligentes de Revisão</h2>
          <button @click="showTemplates = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="templates-grid">
            <div 
              v-for="template in revisionTemplates" 
              :key="template.id"
              class="template-card"
              @click="selectTemplate(template)"
            >
              <div class="template-icon" :style="{ background: template.color }">
                <i :class="template.icon"></i>
              </div>
              <h4>{{ template.name }}</h4>
              <p>{{ template.description }}</p>
              <div class="template-meta">
                <span><i class="fas fa-clock"></i> {{ template.duration }}</span>
                <span><i class="fas fa-layer-group"></i> {{ template.revisions }} revisões</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Batch Scheduler Modal -->
    <div v-if="showBatchScheduler" class="modal-overlay" @click.self="showBatchScheduler = false">
      <div class="batch-scheduler-modal">
        <div class="modal-header">
          <h2><i class="fas fa-calendar-plus"></i> Agendamento em Lote</h2>
          <button @click="showBatchScheduler = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="batch-options">
            <h3>Tipo de Agendamento</h3>
            <div class="batch-type-selector">
              <div 
                v-for="type in batchTypes" 
                :key="type.id"
                @click="selectedBatchType = type.id"
                :class="{ active: selectedBatchType === type.id }"
                class="batch-type-card"
              >
                <i :class="type.icon"></i>
                <h4>{{ type.name }}</h4>
                <p>{{ type.description }}</p>
              </div>
            </div>
          </div>
          
          <div v-if="selectedBatchType" class="batch-config">
            <div class="form-group">
              <label>Disciplina</label>
              <select v-model="batchForm.subject" class="form-control">
                <option value="">Todas as disciplinas</option>
                <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                  {{ subject.name }}
                </option>
              </select>
            </div>
            
            <div class="form-group">
              <label>Período</label>
              <div class="date-range">
                <input 
                  type="date" 
                  v-model="batchForm.startDate" 
                  class="form-control"
                />
                <span>até</span>
                <input 
                  type="date" 
                  v-model="batchForm.endDate" 
                  class="form-control"
                />
              </div>
            </div>
            
            <div class="form-group">
              <label>Quantidade de Revisões</label>
              <input 
                v-model.number="batchForm.count" 
                type="number" 
                class="form-control"
                min="1"
                max="50"
              />
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="showBatchScheduler = false" class="btn-secondary">
            Cancelar
          </button>
          <button 
            @click="generateBatchRevisions" 
            class="btn-primary"
            :disabled="!selectedBatchType || !batchForm.startDate"
          >
            <i class="fas fa-calendar-check"></i> Gerar Agendamento
          </button>
        </div>
      </div>
    </div>
    
    <!-- Performance Predictor Modal -->
    <div v-if="showPredictor" class="modal-overlay" @click.self="showPredictor = false">
      <div class="predictor-modal">
        <div class="modal-header">
          <h2><i class="fas fa-brain"></i> Preditor de Desempenho com IA</h2>
          <button @click="showPredictor = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <!-- AI Analysis Section -->
          <div class="ai-analysis-section">
            <h3><i class="fas fa-robot"></i> Análise Preditiva</h3>
            
            <div class="prediction-cards">
              <div class="prediction-card success">
                <div class="prediction-header">
                  <i class="fas fa-chart-line"></i>
                  <span>Taxa de Sucesso Prevista</span>
                </div>
                <div class="prediction-value">85%</div>
                <div class="prediction-detail">
                  Baseado em seu histórico de revisões
                </div>
              </div>
              
              <div class="prediction-card warning">
                <div class="prediction-header">
                  <i class="fas fa-exclamation-triangle"></i>
                  <span>Áreas de Atenção</span>
                </div>
                <div class="prediction-value">3</div>
                <div class="prediction-detail">
                  Tópicos que precisam mais foco
                </div>
              </div>
              
              <div class="prediction-card info">
                <div class="prediction-header">
                  <i class="fas fa-calendar-alt"></i>
                  <span>Tempo até Domínio</span>
                </div>
                <div class="prediction-value">45 dias</div>
                <div class="prediction-detail">
                  Mantendo o ritmo atual
                </div>
              </div>
            </div>
          </div>
          
          <!-- Performance Chart -->
          <div class="performance-chart-section">
            <h3><i class="fas fa-chart-area"></i> Curva de Aprendizado</h3>
            <div class="chart-container">
              <canvas ref="performanceChart"></canvas>
            </div>
          </div>
          
          <!-- AI Recommendations -->
          <div class="ai-recommendations">
            <h3><i class="fas fa-lightbulb"></i> Recomendações da IA</h3>
            
            <div class="recommendation-list">
              <div class="recommendation-item">
                <div class="recommendation-icon success">
                  <i class="fas fa-check"></i>
                </div>
                <div class="recommendation-content">
                  <h4>Continue o Ritmo</h4>
                  <p>Seu desempenho em Anatomia está excelente. Mantenha a frequência atual de revisões.</p>
                </div>
              </div>
              
              <div class="recommendation-item">
                <div class="recommendation-icon warning">
                  <i class="fas fa-redo"></i>
                </div>
                <div class="recommendation-content">
                  <h4>Aumentar Frequência</h4>
                  <p>Farmacologia precisa de mais atenção. Recomendo aumentar para 3 revisões semanais.</p>
                </div>
              </div>
              
              <div class="recommendation-item">
                <div class="recommendation-icon info">
                  <i class="fas fa-brain"></i>
                </div>
                <div class="recommendation-content">
                  <h4>Novo Método Sugerido</h4>
                  <p>Experimente técnica de Active Recall para melhorar retenção em Fisiologia.</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Optimization Actions -->
          <div class="optimization-actions">
            <button @click="applyOptimizations" class="btn-optimize">
              <i class="fas fa-magic"></i>
              Aplicar Otimizações Sugeridas
            </button>
            <button @click="exportAnalysis" class="btn-export">
              <i class="fas fa-download"></i>
              Exportar Análise
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Analytics Modal -->
    <div v-if="showAnalytics" class="modal-overlay" @click.self="showAnalytics = false">
      <div class="analytics-modal">
        <div class="modal-header">
          <h2><i class="fas fa-chart-bar"></i> Análise Detalhada de Desempenho</h2>
          <button @click="showAnalytics = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <!-- Analytics Tabs -->
          <div class="analytics-tabs">
            <button 
              v-for="tab in analyticsTabs" 
              :key="tab.id"
              @click="activeAnalyticsTab = tab.id"
              :class="{ active: activeAnalyticsTab === tab.id }"
              class="analytics-tab"
            >
              <i :class="tab.icon"></i>
              {{ tab.label }}
            </button>
          </div>
          
          <!-- Overview Tab -->
          <div v-if="activeAnalyticsTab === 'overview'" class="analytics-content">
            <div class="metrics-grid">
              <div class="metric-card">
                <div class="metric-header">
                  <i class="fas fa-graduation-cap"></i>
                  <span>Total de Horas</span>
                </div>
                <div class="metric-value">127h</div>
                <div class="metric-change positive">
                  <i class="fas fa-arrow-up"></i> +12% este mês
                </div>
              </div>
              
              <div class="metric-card">
                <div class="metric-header">
                  <i class="fas fa-book"></i>
                  <span>Tópicos Dominados</span>
                </div>
                <div class="metric-value">42/85</div>
                <div class="metric-progress">
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 49.4%"></div>
                  </div>
                </div>
              </div>
              
              <div class="metric-card">
                <div class="metric-header">
                  <i class="fas fa-trophy"></i>
                  <span>Melhor Sequência</span>
                </div>
                <div class="metric-value">21 dias</div>
                <div class="metric-subtitle">
                  Atual: 7 dias
                </div>
              </div>
              
              <div class="metric-card">
                <div class="metric-header">
                  <i class="fas fa-bullseye"></i>
                  <span>Precisão Média</span>
                </div>
                <div class="metric-value">78.5%</div>
                <div class="metric-change positive">
                  <i class="fas fa-arrow-up"></i> +5.2% evolução
                </div>
              </div>
            </div>
            
            <!-- Heatmap Calendar -->
            <div class="heatmap-section">
              <h3>Atividade de Estudo</h3>
              <div class="heatmap-calendar">
                <!-- Heatmap visualization would go here -->
                <div class="heatmap-placeholder">
                  <div class="heatmap-months">
                    <span>Jan</span><span>Fev</span><span>Mar</span><span>Abr</span>
                  </div>
                  <div class="heatmap-grid">
                    <div 
                      v-for="day in 120" 
                      :key="day"
                      class="heatmap-cell"
                      :data-intensity="getHeatmapIntensity(day)"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Subject Performance Tab -->
          <div v-if="activeAnalyticsTab === 'subjects'" class="analytics-content">
            <div class="subject-performance-list">
              <div 
                v-for="subject in subjectPerformance" 
                :key="subject.id"
                class="subject-performance-item"
              >
                <div class="subject-header">
                  <div class="subject-info">
                    <div class="subject-color" :style="{ background: subject.color }"></div>
                    <span class="subject-name">{{ subject.name }}</span>
                  </div>
                  <span class="subject-score" :class="getScoreClass(subject.score)">
                    {{ subject.score }}%
                  </span>
                </div>
                
                <div class="subject-stats">
                  <div class="stat">
                    <i class="fas fa-clock"></i>
                    <span>{{ subject.hours }}h estudadas</span>
                  </div>
                  <div class="stat">
                    <i class="fas fa-redo"></i>
                    <span>{{ subject.revisions }} revisões</span>
                  </div>
                  <div class="stat">
                    <i class="fas fa-fire"></i>
                    <span>{{ subject.streak }} dias seguidos</span>
                  </div>
                </div>
                
                <div class="subject-progress">
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: subject.progress + '%' }"></div>
                  </div>
                  <span class="progress-label">{{ subject.progress }}% completo</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Time Analysis Tab -->
          <div v-if="activeAnalyticsTab === 'time'" class="analytics-content">
            <div class="time-analysis">
              <h3>Distribuição de Tempo por Período</h3>
              
              <div class="time-distribution">
                <div class="time-period-card">
                  <h4>Manhã</h4>
                  <div class="time-stats">
                    <div class="circular-progress" data-progress="65">
                      <svg viewBox="0 0 36 36">
                        <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                        <path class="circle" stroke-dasharray="65, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                      </svg>
                      <div class="percentage">65%</div>
                    </div>
                    <p>Melhor desempenho</p>
                  </div>
                </div>
                
                <div class="time-period-card">
                  <h4>Tarde</h4>
                  <div class="time-stats">
                    <div class="circular-progress" data-progress="45">
                      <svg viewBox="0 0 36 36">
                        <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                        <path class="circle" stroke-dasharray="45, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                      </svg>
                      <div class="percentage">45%</div>
                    </div>
                    <p>Desempenho médio</p>
                  </div>
                </div>
                
                <div class="time-period-card">
                  <h4>Noite</h4>
                  <div class="time-stats">
                    <div class="circular-progress" data-progress="25">
                      <svg viewBox="0 0 36 36">
                        <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                        <path class="circle" stroke-dasharray="25, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                      </svg>
                      <div class="percentage">25%</div>
                    </div>
                    <p>Menor produtividade</p>
                  </div>
                </div>
              </div>
              
              <div class="best-time-recommendation">
                <i class="fas fa-info-circle"></i>
                <p>Seus melhores horários de estudo são entre <strong>8h-11h</strong>. Considere agendar suas revisões mais importantes neste período.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { format, isToday, isFuture, isPast, differenceInDays, startOfDay, endOfDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import RevisionFormModal from './RevisionFormModal.vue';

export default {
  name: 'RevisionSchedulerUpdated',
  components: {
    RevisionFormModal
  },
  data() {
    return {
      activeTab: 'theoretical',
      selectedPeriod: '30d',
      showQuickAdd: false,
      showSpacedRepetition: false,
      showFirstContactModal: false,
      showQuestionsSchedulerModal: false,
      showCycleGenerator: false,
      showTemplates: false,
      showBatchScheduler: false,
      showPredictor: false,
      showAnalytics: false,
      cycleStep: 1,
      selectedBatchType: null,
      activeAnalyticsTab: 'overview',
      spacedRepetitionForm: {
        subject: '',
        difficulty: 'Médio',
        startDate: new Date().toISOString().split('T')[0],
        revisionType: 'Teórica'
      },
      firstContactForm: {
        subject: '',
        title: '',
        studyDate: new Date().toISOString().split('T')[0],
        difficulty: 'Médio'
      },
      questionsPerformanceForm: {
        subject: '',
        title: '',
        totalQuestions: 30,
        correctAnswers: 0,
        date: new Date().toISOString().split('T')[0]
      },
      cycleForm: {
        name: '',
        subject: '',
        topicsCount: 5,
        startDate: new Date().toISOString().split('T')[0],
        topics: []
      },
      batchForm: {
        subject: '',
        startDate: new Date().toISOString().split('T')[0],
        endDate: '',
        count: 10
      },
      revisionTemplates: [
        {
          id: 1,
          name: 'Ciclo Básico de Anatomia',
          description: 'Template otimizado para estudo de anatomia com revisões focadas em memorização',
          icon: 'fas fa-bone',
          color: 'linear-gradient(135deg, #667eea, #764ba2)',
          duration: '3 meses',
          revisions: 45
        },
        {
          id: 2,
          name: 'Farmacologia Intensiva',
          description: 'Revisões frequentes para memorização de fármacos e mecanismos de ação',
          icon: 'fas fa-pills',
          color: 'linear-gradient(135deg, #f093fb, #f5576c)',
          duration: '2 meses',
          revisions: 60
        },
        {
          id: 3,
          name: 'Fisiologia Sistêmica',
          description: 'Abordagem integrada dos sistemas com revisões progressivas',
          icon: 'fas fa-heartbeat',
          color: 'linear-gradient(135deg, #4facfe, #00f2fe)',
          duration: '4 meses',
          revisions: 40
        },
        {
          id: 4,
          name: 'Patologia Clínica',
          description: 'Revisões baseadas em casos clínicos e diagnósticos diferenciais',
          icon: 'fas fa-microscope',
          color: 'linear-gradient(135deg, #fa709a, #fee140)',
          duration: '3 meses',
          revisions: 50
        }
      ],
      batchTypes: [
        {
          id: 'weekly',
          name: 'Revisões Semanais',
          description: 'Distribui revisões uniformemente por semana',
          icon: 'fas fa-calendar-week'
        },
        {
          id: 'performance',
          name: 'Baseado em Performance',
          description: 'Ajusta frequência com base no desempenho anterior',
          icon: 'fas fa-chart-line'
        },
        {
          id: 'intensive',
          name: 'Intensivo Pré-Prova',
          description: 'Concentra revisões antes de uma data importante',
          icon: 'fas fa-fire'
        }
      ],
      analyticsTabs: [
        {
          id: 'overview',
          label: 'Visão Geral',
          icon: 'fas fa-chart-pie'
        },
        {
          id: 'subjects',
          label: 'Por Disciplina',
          icon: 'fas fa-book'
        },
        {
          id: 'time',
          label: 'Análise Temporal',
          icon: 'fas fa-clock'
        }
      ],
      subjectPerformance: [
        {
          id: 1,
          name: 'Anatomia',
          color: '#6366f1',
          score: 85,
          hours: 45,
          revisions: 120,
          streak: 7,
          progress: 78
        },
        {
          id: 2,
          name: 'Farmacologia',
          color: '#f5576c',
          score: 72,
          hours: 38,
          revisions: 95,
          streak: 3,
          progress: 65
        },
        {
          id: 3,
          name: 'Fisiologia',
          color: '#4facfe',
          score: 91,
          hours: 52,
          revisions: 140,
          streak: 14,
          progress: 88
        },
        {
          id: 4,
          name: 'Patologia',
          color: '#fa709a',
          score: 68,
          hours: 30,
          revisions: 75,
          streak: 2,
          progress: 55
        }
      ]
    };
  },
  computed: {
    subjects() {
      return this.$store.state.calendar?.subjects || [];
    },
    
    allRevisions() {
      return this.$store.state.calendar?.events?.filter(e => e.isRevision) || [];
    },
    
    todayRevisions() {
      return this.allRevisions.filter(r => {
        const date = new Date(r.start);
        return isToday(date);
      }).sort((a, b) => new Date(a.start) - new Date(b.start));
    },
    
    todayTheoreticalRevisions() {
      return this.todayRevisions.filter(r => 
        r.revisionType === 'Teórica' || r.revisionType === 'Resumo' || !r.revisionType
      );
    },
    
    todayQuestionsRevisions() {
      return this.todayRevisions.filter(r => 
        r.revisionType === 'Prática' || r.revisionType === 'Questões'
      );
    },
    
    upcomingRevisions() {
      return this.allRevisions.filter(r => {
        const date = new Date(r.start);
        return isFuture(date) && !isToday(date);
      }).sort((a, b) => new Date(a.start) - new Date(b.start));
    },
    
    overdueRevisions() {
      return this.allRevisions.filter(r => {
        const date = new Date(r.start);
        return isPast(date) && !isToday(date) && !r.completed;
      }).sort((a, b) => new Date(b.start) - new Date(a.start));
    },
    
    pendingRevisions() {
      return this.allRevisions.filter(r => !r.completed);
    },
    
    completedToday() {
      return this.todayRevisions.filter(r => r.completed).length;
    },
    
    completionRate() {
      const total = this.allRevisions.length;
      if (total === 0) return 0;
      const completed = this.allRevisions.filter(r => r.completed).length;
      return Math.round((completed / total) * 100);
    },
    
    groupedUpcomingRevisions() {
      const grouped = {};
      this.upcomingRevisions.forEach(revision => {
        const date = format(new Date(revision.start), 'yyyy-MM-dd');
        if (!grouped[date]) {
          grouped[date] = [];
        }
        grouped[date].push(revision);
      });
      return grouped;
    },
    
    canGenerateSpacedRepetition() {
      return this.spacedRepetitionForm.subject && 
             this.spacedRepetitionForm.startDate &&
             this.spacedRepetitionForm.difficulty &&
             this.spacedRepetitionForm.revisionType;
    },
    
    today() {
      return new Date().toISOString().split('T')[0];
    },
    
    averageStudyTime() {
      // Calculate from completed revisions
      return '45 min';
    },
    
    averageRetention() {
      const completed = this.allRevisions.filter(r => r.completed && r.lastPerformance);
      if (completed.length === 0) return 0;
      const sum = completed.reduce((acc, r) => acc + (r.lastPerformance || 0), 0);
      return Math.round(sum / completed.length);
    },
    
    currentStreak() {
      // Calculate current streak
      return 7;
    },
    
    bestStreak() {
      // Calculate best streak
      return 14;
    },
    
    completionTrend() {
      // Calculate trend
      return 'trend-up';
    },
    
    completionTrendIcon() {
      return 'fas fa-arrow-up';
    },
    
    completionTrendText() {
      return '+5% esta semana';
    },
    
    canProceedToNextStep() {
      if (this.cycleStep === 1) {
        return this.cycleForm.name && this.cycleForm.subject && this.cycleForm.topicsCount > 0;
      }
      if (this.cycleStep === 2) {
        return this.cycleForm.topics.every(t => t.name && t.difficulty);
      }
      return true;
    },
    
    previewRevisions() {
      // Generate preview of revisions
      const revisions = [];
      const startDate = new Date(this.cycleForm.startDate);
      
      this.cycleForm.topics.forEach((topic, index) => {
        // Theoretical study
        const studyDate = new Date(startDate);
        studyDate.setDate(studyDate.getDate() + (index * 3));
        
        revisions.push({
          date: studyDate.toISOString(),
          title: `Estudo: ${topic.name}`,
          type: 'theoretical',
          typeLabel: 'Teórico'
        });
        
        // First contact
        const firstContactDate = new Date(studyDate);
        firstContactDate.setDate(firstContactDate.getDate() + (topic.difficulty === 'Fácil' ? 2 : 1));
        
        revisions.push({
          date: firstContactDate.toISOString(),
          title: `1º Contato: ${topic.name}`,
          type: 'practice',
          typeLabel: 'Prática'
        });
        
        // Additional revisions based on methodology
        const intervals = [7, 14, 30, 60];
        intervals.forEach((interval, i) => {
          const revisionDate = new Date(firstContactDate);
          revisionDate.setDate(revisionDate.getDate() + interval);
          
          revisions.push({
            date: revisionDate.toISOString(),
            title: `Revisão ${i + 2}: ${topic.name}`,
            type: 'practice',
            typeLabel: 'Questões'
          });
        });
      });
      
      return revisions.sort((a, b) => new Date(a.date) - new Date(b.date));
    }
  },
  methods: {
    getSubjectName(subjectId) {
      const subject = this.subjects.find(s => s.id === subjectId);
      return subject ? subject.name : 'Sem disciplina';
    },
    
    getSubjectColor(subjectId) {
      const subject = this.subjects.find(s => s.id === subjectId);
      return subject ? subject.color : '#6366f1';
    },
    
    formatTime(dateStr) {
      return format(new Date(dateStr), 'HH:mm');
    },
    
    formatDateLabel(dateStr) {
      const date = new Date(dateStr);
      return format(date, "EEEE, dd 'de' MMMM", { locale: ptBR });
    },
    
    getDaysOverdue(dateStr) {
      return differenceInDays(new Date(), new Date(dateStr));
    },
    
    getPriorityIcon(priority) {
      const icons = {
        'Alta': 'fas fa-fire',
        'Média': 'fas fa-bolt',
        'Baixa': 'fas fa-leaf'
      };
      return icons[priority] || 'fas fa-flag';
    },
    
    async startRevision(revision) {
      this.$router.push({
        path: '/calendar',
        query: { revisionId: revision.id }
      });
    },
    
    viewDetails(revision) {
      // TODO: Implement view details
      console.log('View details:', revision);
    },
    
    async rescheduleRevision(revision) {
      // TODO: Implement reschedule
      console.log('Reschedule:', revision);
    },
    
    async handleNewRevision(revision) {
      try {
        await this.$store.dispatch('calendar/addEvent', revision);
        this.showQuickAdd = false;
        await this.loadRevisions();
      } catch (error) {
        console.error('Erro ao criar revisão:', error);
      }
    },
    
    async generateSpacedRepetition() {
      if (!this.canGenerateSpacedRepetition) return;
      
      try {
        await this.$store.dispatch('calendar/generateSpacedRepetition', this.spacedRepetitionForm);
        this.showSpacedRepetition = false;
        
        // Reset form
        this.spacedRepetitionForm = {
          subject: '',
          difficulty: 'Médio',
          startDate: new Date().toISOString().split('T')[0],
          revisionType: 'Teórica'
        };
        
        await this.loadRevisions();
        
        this.$toast?.success('Revisões espaçadas criadas com sucesso!');
      } catch (error) {
        console.error('Erro ao gerar revisões espaçadas:', error);
        this.$toast?.error('Erro ao gerar revisões espaçadas');
      }
    },
    
    async scheduleFirstContact() {
      const { subject, title, studyDate, difficulty } = this.firstContactForm;
      
      if (!subject || !title || !studyDate) return;
      
      // Calculate first contact date based on difficulty
      const daysToAdd = difficulty === 'Fácil' ? 2 : 1;
      const firstContactDate = new Date(studyDate);
      firstContactDate.setDate(firstContactDate.getDate() + daysToAdd);
      
      const revision = {
        title: `Primeiro Contato: ${title}`,
        subject,
        start: firstContactDate.toISOString(),
        end: new Date(firstContactDate.getTime() + 60 * 60 * 1000).toISOString(), // 1 hour
        isRevision: true,
        revisionType: 'Prática',
        priority: difficulty === 'Difícil' ? 'Alta' : 'Média',
        description: `Primeiro contato com questões após estudo teórico. Dificuldade: ${difficulty}`
      };
      
      try {
        await this.$store.dispatch('calendar/addEvent', revision);
        this.showFirstContactModal = false;
        
        // Reset form
        this.firstContactForm = {
          subject: '',
          title: '',
          studyDate: new Date().toISOString().split('T')[0],
          difficulty: 'Médio'
        };
        
        await this.loadRevisions();
        this.$toast?.success('Primeiro contato agendado com sucesso!');
      } catch (error) {
        console.error('Erro ao agendar primeiro contato:', error);
        this.$toast?.error('Erro ao agendar primeiro contato');
      }
    },
    
    async schedulePerformanceBasedRevision() {
      const { subject, title, totalQuestions, correctAnswers, date } = this.questionsPerformanceForm;
      
      if (!subject || !title || !totalQuestions || totalQuestions <= 0) return;
      
      const percentage = (correctAnswers / totalQuestions) * 100;
      let daysToAdd;
      
      // Calculate days based on performance
      if (percentage <= 50) {
        daysToAdd = 2;
      } else if (percentage <= 55) {
        daysToAdd = 7;
      } else if (percentage <= 60) {
        daysToAdd = 14;
      } else if (percentage <= 65) {
        daysToAdd = 18;
      } else if (percentage <= 75) {
        daysToAdd = 24;
      } else if (percentage <= 80) {
        daysToAdd = 30;
      } else {
        daysToAdd = 35;
      }
      
      const nextRevisionDate = new Date(date);
      nextRevisionDate.setDate(nextRevisionDate.getDate() + daysToAdd);
      
      const revision = {
        title: `Revisão: ${title}`,
        subject,
        start: nextRevisionDate.toISOString(),
        end: new Date(nextRevisionDate.getTime() + 90 * 60 * 1000).toISOString(), // 1.5 hours
        isRevision: true,
        revisionType: 'Questões',
        priority: percentage <= 60 ? 'Alta' : percentage <= 75 ? 'Média' : 'Baixa',
        description: `Revisão baseada em desempenho. Última performance: ${percentage.toFixed(1)}% (${correctAnswers}/${totalQuestions})`,
        lastPerformance: percentage,
        questionsCount: totalQuestions
      };
      
      try {
        await this.$store.dispatch('calendar/addEvent', revision);
        this.showQuestionsSchedulerModal = false;
        
        // Reset form
        this.questionsPerformanceForm = {
          subject: '',
          title: '',
          totalQuestions: 30,
          correctAnswers: 0,
          date: new Date().toISOString().split('T')[0]
        };
        
        await this.loadRevisions();
        this.$toast?.success(`Revisão agendada para ${daysToAdd} dias!`);
      } catch (error) {
        console.error('Erro ao agendar revisão baseada em desempenho:', error);
        this.$toast?.error('Erro ao agendar revisão');
      }
    },
    
    startQuestionsRevision(revision) {
      // Navigate to questions page or open questions modal
      this.$router.push({
        path: '/provas-simulados',
        query: { revisionId: revision.id, subject: revision.subject }
      });
    },
    
    getPerformanceTrend(revision) {
      if (!revision.lastPerformance) return '';
      if (revision.lastPerformance >= 75) return 'trend-up';
      if (revision.lastPerformance >= 50) return 'trend-stable';
      return 'trend-down';
    },
    
    getPerformanceTrendIcon(revision) {
      if (!revision.lastPerformance) return '';
      if (revision.lastPerformance >= 75) return 'fas fa-arrow-up';
      if (revision.lastPerformance >= 50) return 'fas fa-minus';
      return 'fas fa-arrow-down';
    },
    
    getRecommendedDays() {
      const { totalQuestions, correctAnswers } = this.questionsPerformanceForm;
      if (!totalQuestions || totalQuestions <= 0) return 0;
      
      const percentage = (correctAnswers / totalQuestions) * 100;
      
      if (percentage <= 50) return 2;
      if (percentage <= 55) return 7;
      if (percentage <= 60) return 14;
      if (percentage <= 65) return 18;
      if (percentage <= 75) return 24;
      if (percentage <= 80) return 30;
      return 35;
    },
    
    nextCycleStep() {
      if (this.cycleStep === 1 && this.canProceedToNextStep) {
        // Initialize topics array
        this.cycleForm.topics = Array.from({ length: this.cycleForm.topicsCount }, () => ({
          name: '',
          difficulty: 'Médio'
        }));
        this.cycleStep++;
      } else if (this.cycleStep === 2 && this.canProceedToNextStep) {
        this.cycleStep++;
      }
    },
    
    calculateTotalRevisions() {
      // Each topic generates 6 revisions (1 theoretical + 1 first contact + 4 spaced revisions)
      return this.cycleForm.topicsCount * 6;
    },
    
    async generateCompleteCycle() {
      try {
        const revisions = this.previewRevisions;
        
        // Create all revisions
        for (const revision of revisions) {
          const event = {
            title: revision.title,
            subject: this.cycleForm.subject,
            start: revision.date,
            end: new Date(new Date(revision.date).getTime() + 60 * 60 * 1000).toISOString(),
            isRevision: true,
            revisionType: revision.type === 'theoretical' ? 'Teórica' : 'Questões',
            priority: 'Média',
            description: `Parte do ciclo: ${this.cycleForm.name}`
          };
          
          await this.$store.dispatch('calendar/addEvent', event);
        }
        
        this.showCycleGenerator = false;
        this.cycleStep = 1;
        this.cycleForm = {
          name: '',
          subject: '',
          topicsCount: 5,
          startDate: new Date().toISOString().split('T')[0],
          topics: []
        };
        
        await this.loadRevisions();
        this.$toast?.success(`Ciclo completo criado com ${revisions.length} revisões!`);
      } catch (error) {
        console.error('Erro ao gerar ciclo completo:', error);
        this.$toast?.error('Erro ao gerar ciclo completo');
      }
    },
    
    selectTemplate(template) {
      // Apply template to cycle form
      this.showTemplates = false;
      this.showCycleGenerator = true;
      
      this.cycleForm = {
        name: template.name,
        subject: '',
        topicsCount: Math.floor(template.revisions / 6),
        startDate: new Date().toISOString().split('T')[0],
        topics: []
      };
      
      this.$toast?.success(`Template "${template.name}" selecionado!`);
    },
    
    async generateBatchRevisions() {
      if (!this.selectedBatchType || !this.batchForm.startDate) return;
      
      try {
        const startDate = new Date(this.batchForm.startDate);
        const endDate = this.batchForm.endDate ? new Date(this.batchForm.endDate) : new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000);
        const daysBetween = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
        
        const revisions = [];
        
        if (this.selectedBatchType === 'weekly') {
          // Distribute evenly across weeks
          const interval = Math.floor(daysBetween / this.batchForm.count);
          
          for (let i = 0; i < this.batchForm.count; i++) {
            const revisionDate = new Date(startDate);
            revisionDate.setDate(revisionDate.getDate() + (i * interval));
            
            revisions.push({
              title: `Revisão Semanal ${i + 1}`,
              subject: this.batchForm.subject || this.subjects[0]?.id,
              start: revisionDate.toISOString(),
              end: new Date(revisionDate.getTime() + 60 * 60 * 1000).toISOString(),
              isRevision: true,
              revisionType: 'Mista',
              priority: 'Média'
            });
          }
        } else if (this.selectedBatchType === 'intensive') {
          // Concentrate near the end date
          const intensivePeriod = 7; // Last 7 days
          const startIntensive = new Date(endDate);
          startIntensive.setDate(startIntensive.getDate() - intensivePeriod);
          
          for (let i = 0; i < this.batchForm.count; i++) {
            const progress = i / (this.batchForm.count - 1);
            const daysFromStart = Math.floor(progress * intensivePeriod);
            
            const revisionDate = new Date(startIntensive);
            revisionDate.setDate(revisionDate.getDate() + daysFromStart);
            
            revisions.push({
              title: `Revisão Intensiva ${i + 1}`,
              subject: this.batchForm.subject || this.subjects[0]?.id,
              start: revisionDate.toISOString(),
              end: new Date(revisionDate.getTime() + 90 * 60 * 1000).toISOString(),
              isRevision: true,
              revisionType: 'Questões',
              priority: 'Alta'
            });
          }
        }
        
        // Create all revisions
        for (const revision of revisions) {
          await this.$store.dispatch('calendar/addEvent', revision);
        }
        
        this.showBatchScheduler = false;
        this.selectedBatchType = null;
        this.batchForm = {
          subject: '',
          startDate: new Date().toISOString().split('T')[0],
          endDate: '',
          count: 10
        };
        
        await this.loadRevisions();
        this.$toast?.success(`${revisions.length} revisões agendadas com sucesso!`);
      } catch (error) {
        console.error('Erro ao gerar revisões em lote:', error);
        this.$toast?.error('Erro ao gerar revisões em lote');
      }
    },
    
    async loadRevisions() {
      const today = new Date();
      const start = startOfDay(today);
      const end = new Date(today);
      end.setDate(end.getDate() + 30); // Load 30 days ahead
      
      await this.$store.dispatch('calendar/fetchEvents', {
        start: format(start, 'yyyy-MM-dd'),
        end: format(end, 'yyyy-MM-dd')
      });
    },
    
    applyOptimizations() {
      // Apply AI-suggested optimizations
      this.$toast?.success('Otimizações aplicadas com sucesso!');
      this.showPredictor = false;
      
      // Here would implement actual optimization logic
      // - Adjust revision frequencies
      // - Reschedule based on predicted performance
      // - Update priorities
    },
    
    exportAnalysis() {
      // Export analysis as PDF or CSV
      const analysisData = {
        date: new Date().toISOString(),
        predictions: {
          successRate: 85,
          attentionAreas: 3,
          timeToDomain: 45
        },
        recommendations: [
          'Continue o ritmo em Anatomia',
          'Aumentar frequência em Farmacologia',
          'Aplicar Active Recall em Fisiologia'
        ]
      };
      
      // Create downloadable file
      const dataStr = JSON.stringify(analysisData, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
      
      const exportFileDefaultName = `analise_revisoes_${format(new Date(), 'yyyy-MM-dd')}.json`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
      
      this.$toast?.success('Análise exportada com sucesso!');
    },
    
    getHeatmapIntensity(day) {
      // Calculate intensity for heatmap
      const intensities = ['0', '1', '2', '3', '4'];
      return intensities[Math.floor(Math.random() * intensities.length)];
    },
    
    getScoreClass(score) {
      if (score >= 80) return 'high';
      if (score >= 60) return 'medium';
      return 'low';
    },
    
    initializeChart() {
      // Initialize performance chart using Chart.js or similar
      if (this.$refs.performanceChart) {
        // Chart initialization would go here
        console.log('Chart initialized');
      }
    }
  },
  
  async mounted() {
    await this.$store.dispatch('calendar/fetchSubjects');
    await this.loadRevisions();
  }
};
</script>

<style scoped>
/* Base Styles */
.revision-scheduler {
  position: relative;
  min-height: 100vh;
  background: #0a0f1b;
  color: #e4e6eb;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Background Effects */
.background-effects {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

.gradient-orb.orb-1 {
  width: 600px;
  height: 600px;
  top: -200px;
  left: -200px;
  background: radial-gradient(circle, #6366f1, transparent);
}

.gradient-orb.orb-2 {
  width: 800px;
  height: 800px;
  bottom: -300px;
  right: -300px;
  background: radial-gradient(circle, #8b5cf6, transparent);
  animation-delay: -7s;
}

.gradient-orb.orb-3 {
  width: 500px;
  height: 500px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, #3b82f6, transparent);
  animation-delay: -14s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-30px, 30px) scale(0.9); }
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.05) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Header */
.revision-header {
  position: relative;
  z-index: 10;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.page-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.2);
}

.page-info h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #e4e6eb, #94a3b8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0.25rem 0 0 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

.action-button:hover {
  background: rgba(148, 163, 184, 0.2);
  transform: translateY(-1px);
}

.action-button.primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.action-button.primary:hover {
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Stats Overview */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s;
}

.stat-card:hover {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6366f1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #e4e6eb;
}

.stat-label {
  font-size: 0.75rem;
  color: #94a3b8;
}

/* Content */
.revision-content {
  position: relative;
  z-index: 10;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.revision-section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0;
}

.section-header h2 i {
  color: #6366f1;
}

.section-header.warning h2 i {
  color: #f59e0b;
}

.section-count {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 12px;
  border: 1px dashed rgba(148, 163, 184, 0.2);
}

.empty-state i {
  font-size: 3rem;
  color: #64748b;
  margin-bottom: 1rem;
}

.empty-state p {
  color: #94a3b8;
  margin-bottom: 1.5rem;
}

/* Revision Cards */
.revision-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
}

.revision-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s;
}

.revision-card:hover {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateY(-2px);
}

.revision-card.completed {
  opacity: 0.7;
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.card-title h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #e4e6eb;
}

.subject-tag {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
}

.subject-tag.mini {
  padding: 0.125rem 0.5rem;
  font-size: 0.7rem;
}

.card-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.revision-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.meta-item.priority[data-priority="alta"] {
  color: #ef4444;
}

.meta-item.priority[data-priority="média"] {
  color: #fb923c;
}

.meta-item.priority[data-priority="baixa"] {
  color: #22c55e;
}

.progress-section {
  margin-bottom: 1rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 6px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  transition: width 0.3s;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-start,
.btn-completed,
.btn-details {
  flex: 1;
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-start {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-start:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-completed {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  cursor: default;
}

.btn-details {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

.btn-details:hover {
  background: rgba(148, 163, 184, 0.2);
}

/* Timeline */
.revision-timeline {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.timeline-group {
  position: relative;
  padding-left: 2rem;
}

.timeline-date {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.date-label {
  font-weight: 600;
  color: #e4e6eb;
  text-transform: capitalize;
}

.date-count {
  font-size: 0.875rem;
  color: #64748b;
}

.timeline-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.timeline-item {
  position: relative;
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.timeline-dot {
  position: absolute;
  left: -25px;
  top: 0.5rem;
  width: 10px;
  height: 10px;
  background: #6366f1;
  border-radius: 50%;
}

.timeline-content {
  flex: 1;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  padding: 1rem;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.timeline-header h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
  color: #e4e6eb;
}

.timeline-time {
  font-size: 0.75rem;
  color: #94a3b8;
}

.timeline-meta {
  display: flex;
  gap: 0.5rem;
}

.type-tag {
  font-size: 0.7rem;
  color: #94a3b8;
  background: rgba(148, 163, 184, 0.1);
  padding: 0.125rem 0.5rem;
  border-radius: 999px;
}

/* Overdue */
.overdue-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.overdue-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
}

.overdue-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #e4e6eb;
}

.overdue-info p {
  font-size: 0.75rem;
  color: #ef4444;
  margin: 0;
}

.btn-reschedule {
  padding: 0.5rem 1rem;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-reschedule:hover {
  background: rgba(239, 68, 68, 0.3);
}

/* Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s;
}

.spaced-repetition-modal {
  background: #1e293b;
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.modal-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0;
}

.modal-header h2 i {
  color: #6366f1;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  color: #e4e6eb;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #6366f1;
  background: rgba(30, 41, 59, 0.8);
}

.difficulty-selector {
  display: flex;
  gap: 0.5rem;
}

.difficulty-btn {
  flex: 1;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.difficulty-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
}

.difficulty-btn.active {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
  color: #6366f1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.btn-secondary,
.btn-primary {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

.btn-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .revision-cards {
    grid-template-columns: 1fr;
  }
  
  .timeline-group {
    padding-left: 1.5rem;
  }
  
  .timeline-dot {
    left: -20px;
  }
}

/* Revision Tabs */
.revision-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 0.5rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 12px;
}

.tab-button {
  flex: 1;
  padding: 0.875rem 1.5rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tab-button:hover {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.tab-button.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.tab-content {
  animation: fadeIn 0.3s;
}

/* Action Button Mini */
.action-button.mini {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* Methodology Info */
.methodology-info {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.methodology-info i {
  color: #6366f1;
  font-size: 1.25rem;
  margin-top: 0.25rem;
}

.methodology-info p {
  margin: 0 0 0.75rem 0;
  color: #e4e6eb;
  line-height: 1.6;
}

.methodology-info ul {
  margin: 0;
  padding-left: 1.5rem;
  list-style: none;
}

.methodology-info li {
  position: relative;
  margin-bottom: 0.5rem;
  color: #cbd5e1;
  padding-left: 1rem;
}

.methodology-info li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #6366f1;
}

.methodology-info strong {
  color: #e4e6eb;
}

/* Questions Card */
.questions-card {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
}

.questions-card:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

/* Performance Indicators */
.performance-indicators {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.indicator {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 8px;
}

.indicator-label {
  font-size: 0.75rem;
  color: #94a3b8;
}

.trend-up {
  color: #22c55e;
}

.trend-stable {
  color: #fb923c;
}

.trend-down {
  color: #ef4444;
}

/* Form Row */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Performance Preview */
.performance-preview {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.25rem;
  margin-top: 1rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-weight: 600;
}

.preview-header .percentage {
  font-size: 1.5rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.preview-recommendation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.preview-recommendation i {
  color: #6366f1;
}

.preview-recommendation strong {
  color: #e4e6eb;
}

/* Action Button Special */
.action-button.special {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
}

.action-button.special:hover {
  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
}

/* Smart Tools Section */
.smart-tools-section {
  margin-bottom: 3rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: #e4e6eb;
  margin-bottom: 2rem;
}

.section-title i {
  color: #6366f1;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.tool-card {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.tool-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.5s;
  opacity: 0;
}

.tool-card:hover::before {
  opacity: 1;
}

.tool-card:hover {
  transform: translateY(-4px);
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 12px 32px rgba(99, 102, 241, 0.2);
}

.tool-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.tool-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 0.75rem;
}

.tool-card p {
  color: #94a3b8;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.tool-action {
  width: 100%;
  padding: 0.75rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 8px;
  color: #6366f1;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.tool-action:hover {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
}

/* Progress Overview */
.progress-overview-section {
  margin-bottom: 3rem;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.time-filter {
  display: flex;
  gap: 0.5rem;
  background: rgba(30, 41, 59, 0.5);
  padding: 0.25rem;
  border-radius: 8px;
}

.filter-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.filter-btn.active {
  background: rgba(99, 102, 241, 0.2);
  color: #6366f1;
}

.progress-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.progress-card {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  gap: 1.5rem;
  transition: all 0.2s;
}

.progress-card:hover {
  transform: translateY(-2px);
  border-color: rgba(99, 102, 241, 0.3);
}

.progress-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.progress-icon.success {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.progress-icon.warning {
  background: rgba(251, 146, 60, 0.1);
  color: #fb923c;
}

.progress-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.progress-icon.primary {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.progress-info h4 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #94a3b8;
  margin: 0 0 0.5rem 0;
}

.progress-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #e4e6eb;
}

.progress-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.progress-subtitle {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 0.25rem;
}

/* Cycle Generator Modal */
.cycle-generator-modal {
  background: #1e293b;
  border-radius: 16px;
  width: 90%;
  max-width: 700px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.cycle-steps {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.step {
  display: flex;
  gap: 1.5rem;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.step.active {
  opacity: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  background: rgba(99, 102, 241, 0.1);
  border: 2px solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: #6366f1;
  flex-shrink: 0;
}

.step.active .step-number {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-color: transparent;
  color: white;
}

.step-content {
  flex: 1;
}

.step-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 1.5rem;
}

.topics-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.topic-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 8px;
}

.topic-number {
  width: 32px;
  height: 32px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #6366f1;
  flex-shrink: 0;
}

.difficulty-mini {
  display: flex;
  gap: 0.25rem;
}

.diff-btn-mini {
  width: 32px;
  height: 32px;
  background: rgba(148, 163, 184, 0.1);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 6px;
  color: #94a3b8;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.diff-btn-mini:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
}

.diff-btn-mini.active {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
  color: #6366f1;
}

.cycle-preview h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #94a3b8;
  margin-bottom: 1rem;
}

.preview-info {
  background: rgba(30, 41, 59, 0.5);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.preview-info p {
  margin: 0.5rem 0;
  color: #cbd5e1;
}

.timeline-preview {
  margin-top: 1.5rem;
}

.timeline-scroll {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.timeline-scroll::-webkit-scrollbar {
  width: 6px;
}

.timeline-scroll::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
}

.timeline-scroll::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.preview-revision {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 6px;
  margin-bottom: 0.5rem;
}

.revision-date {
  font-size: 0.875rem;
  color: #94a3b8;
}

.revision-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.revision-title {
  font-weight: 500;
  color: #e4e6eb;
}

.revision-type {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.revision-type.theoretical {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.revision-type.practice {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.cycle-progress {
  margin-top: 2rem;
}

/* Templates Modal */
.templates-modal {
  background: #1e293b;
  border-radius: 16px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.template-card {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s;
}

.template-card:hover {
  transform: translateY(-2px);
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.2);
}

.template-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin-bottom: 1rem;
}

.template-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 0.5rem;
}

.template-card p {
  color: #94a3b8;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #64748b;
}

.template-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Batch Scheduler Modal */
.batch-scheduler-modal {
  background: #1e293b;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.batch-options h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 1.5rem;
}

.batch-type-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.batch-type-card {
  padding: 1.5rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.batch-type-card:hover {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(99, 102, 241, 0.3);
}

.batch-type-card.active {
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
}

.batch-type-card i {
  font-size: 2rem;
  color: #6366f1;
  margin-bottom: 0.75rem;
}

.batch-type-card h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 0.5rem;
}

.batch-type-card p {
  font-size: 0.75rem;
  color: #94a3b8;
  line-height: 1.4;
}

.batch-config {
  padding-top: 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.date-range {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.date-range span {
  color: #64748b;
  font-size: 0.875rem;
}

/* Predictor Modal */
.predictor-modal {
  background: #1e293b;
  border-radius: 16px;
  width: 90%;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.ai-analysis-section,
.performance-chart-section,
.ai-recommendations {
  margin-bottom: 2rem;
}

.ai-analysis-section h3,
.performance-chart-section h3,
.ai-recommendations h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 1.5rem;
}

.prediction-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.prediction-card {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
}

.prediction-card.success {
  border-color: rgba(34, 197, 94, 0.3);
  background: rgba(34, 197, 94, 0.05);
}

.prediction-card.warning {
  border-color: rgba(251, 146, 60, 0.3);
  background: rgba(251, 146, 60, 0.05);
}

.prediction-card.info {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.prediction-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #94a3b8;
  margin-bottom: 1rem;
}

.prediction-value {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.5rem;
}

.prediction-detail {
  font-size: 0.75rem;
  color: #64748b;
}

.chart-container {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.recommendation-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.recommendation-icon.success {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.recommendation-icon.warning {
  background: rgba(251, 146, 60, 0.1);
  color: #fb923c;
}

.recommendation-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.recommendation-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 0.5rem 0;
}

.recommendation-content p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0;
  line-height: 1.5;
}

.optimization-actions {
  display: flex;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.btn-optimize {
  flex: 1;
  padding: 0.875rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.btn-optimize:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-export {
  padding: 0.875rem 1.5rem;
  background: rgba(148, 163, 184, 0.1);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.btn-export:hover {
  background: rgba(148, 163, 184, 0.2);
  border-color: rgba(148, 163, 184, 0.3);
}

/* Analytics Modal */
.analytics-modal {
  background: #1e293b;
  border-radius: 16px;
  width: 90%;
  max-width: 1000px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.analytics-tabs {
  display: flex;
  gap: 0.5rem;
  background: rgba(30, 41, 59, 0.5);
  padding: 0.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.analytics-tab {
  flex: 1;
  padding: 0.75rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.analytics-tab:hover {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.analytics-tab.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.analytics-content {
  animation: fadeIn 0.3s;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: #94a3b8;
  margin-bottom: 1rem;
}

.metric-header i {
  color: #6366f1;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #e4e6eb;
  margin-bottom: 0.5rem;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.metric-change.positive {
  color: #22c55e;
}

.metric-change.negative {
  color: #ef4444;
}

.metric-progress {
  margin-top: 0.5rem;
}

.metric-subtitle {
  font-size: 0.875rem;
  color: #64748b;
}

/* Heatmap */
.heatmap-section {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
}

.heatmap-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 1.5rem;
}

.heatmap-placeholder {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.heatmap-months {
  display: flex;
  justify-content: space-between;
  padding: 0 1rem;
  font-size: 0.75rem;
  color: #94a3b8;
}

.heatmap-grid {
  display: grid;
  grid-template-columns: repeat(30, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 2px;
}

.heatmap-cell {
  width: 100%;
  aspect-ratio: 1;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 2px;
  transition: all 0.2s;
}

.heatmap-cell[data-intensity="0"] {
  background: rgba(148, 163, 184, 0.1);
}

.heatmap-cell[data-intensity="1"] {
  background: rgba(99, 102, 241, 0.2);
}

.heatmap-cell[data-intensity="2"] {
  background: rgba(99, 102, 241, 0.4);
}

.heatmap-cell[data-intensity="3"] {
  background: rgba(99, 102, 241, 0.6);
}

.heatmap-cell[data-intensity="4"] {
  background: rgba(99, 102, 241, 0.8);
}

.heatmap-cell:hover {
  transform: scale(1.5);
  border: 1px solid #6366f1;
}

/* Subject Performance */
.subject-performance-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.subject-performance-item {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
}

.subject-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.subject-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.subject-color {
  width: 24px;
  height: 24px;
  border-radius: 6px;
}

.subject-name {
  font-weight: 600;
  color: #e4e6eb;
}

.subject-score {
  font-size: 1.5rem;
  font-weight: 700;
}

.subject-score.high {
  color: #22c55e;
}

.subject-score.medium {
  color: #fb923c;
}

.subject-score.low {
  color: #ef4444;
}

.subject-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
}

.subject-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.subject-stats .stat i {
  color: #6366f1;
}

.subject-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.subject-progress .progress-label {
  font-size: 0.875rem;
  color: #64748b;
}

/* Time Analysis */
.time-analysis h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 1.5rem;
}

.time-distribution {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.time-period-card {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
}

.time-period-card h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #e4e6eb;
  margin-bottom: 1rem;
}

.time-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.circular-progress {
  position: relative;
  width: 100px;
  height: 100px;
}

.circular-progress svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circular-progress .circle-bg {
  fill: none;
  stroke: rgba(148, 163, 184, 0.2);
  stroke-width: 3;
}

.circular-progress .circle {
  fill: none;
  stroke: #6366f1;
  stroke-width: 3;
  stroke-linecap: round;
  transition: stroke-dasharray 0.5s;
}

.circular-progress .percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  font-weight: 700;
  color: #e4e6eb;
}

.time-stats p {
  font-size: 0.875rem;
  color: #94a3b8;
}

.best-time-recommendation {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.best-time-recommendation i {
  color: #6366f1;
  font-size: 1.25rem;
}

.best-time-recommendation p {
  color: #cbd5e1;
  margin: 0;
  line-height: 1.5;
}

.best-time-recommendation strong {
  color: #e4e6eb;
}
</style>