<template>
  <section class="modern-card sessions-section">
    <div class="section-header">
      <h2 class="section-heading">
        <span class="title-gradient">Sessões</span>
        <span class="title-accent">Hoje</span>
      </h2>
      <div class="header-actions">
        <div class="view-toggle">
          <button 
            v-for="view in views" 
            :key="view.id"
            @click="currentView = view.id"
            :class="['toggle-btn', { active: currentView === view.id }]"
            :title="view.label"
          >
            <i :class="view.icon"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Today's Focus Bar - Diferencial das Sessões -->
    <div class="today-focus-bar">
      <div class="focus-metric">
        <i class="fas fa-fire"></i>
        <div class="metric-info">
          <span class="metric-value">{{ totalHoursToday }}h</span>
          <span class="metric-label">estudadas hoje</span>
        </div>
      </div>
      <div class="focus-metric">
        <i class="fas fa-brain"></i>
        <div class="metric-info">
          <span class="metric-value">{{ focusScore }}%</span>
          <span class="metric-label">foco mantido</span>
        </div>
      </div>
      <div class="focus-metric">
        <i class="fas fa-bolt"></i>
        <div class="metric-info">
          <span class="metric-value">{{ sessionsCompleted }}/{{ totalSessionsToday }}</span>
          <span class="metric-label">sessões</span>
        </div>
      </div>
      <div class="focus-metric">
        <i class="fas fa-trophy"></i>
        <div class="metric-info">
          <span class="metric-value">{{ currentStreak }}</span>
          <span class="metric-label">dias seguidos</span>
        </div>
      </div>
    </div>

    <!-- Quick Session Actions - Específico para Sessões -->
    <div class="quick-session-bar">
      <button @click="startPomodoro" class="quick-btn pomodoro">
        <i class="fas fa-stopwatch"></i>
        <span>Pomodoro 25min</span>
      </button>
      <button @click="startFocusSession" class="quick-btn focus">
        <i class="fas fa-brain"></i>
        <span>Foco Profundo 50min</span>
      </button>
      <button @click="startReviewSession" class="quick-btn review">
        <i class="fas fa-redo"></i>
        <span>Revisão Rápida 15min</span>
      </button>
      <button @click="createCustomSession" class="quick-btn custom">
        <i class="fas fa-plus"></i>
        <span>Personalizada</span>
      </button>
    </div>

    <!-- Sessions List View -->
    <div v-if="currentView === 'list'" class="sessions-container">
      <div class="sessions-timeline">
        <!-- Current Time Indicator -->
        <div class="current-time-line" :style="{ top: currentTimePosition + 'px' }">
          <span class="time-now">{{ currentTime }}</span>
          <div class="time-line"></div>
        </div>

        <!-- Sessions -->
        <div 
          v-for="session in todaySessions" 
          :key="session.id"
          class="session-item"
          :class="{
            'is-active': session.status === 'in-progress',
            'is-completed': session.status === 'completed',
            'is-upcoming': session.status === 'upcoming'
          }"
        >
          <!-- Time Block -->
          <div class="time-block">
            <span class="start-time">{{ session.startTime }}</span>
            <div class="time-connector"></div>
            <span class="end-time">{{ session.endTime }}</span>
          </div>

          <!-- Session Card -->
          <div class="session-card-optimized">
            <div class="session-main">
              <div class="session-header">
                <h4>{{ session.subject }}</h4>
                <div class="session-status-indicator" :class="`status-${session.status}`"></div>
              </div>
              <p class="session-topic">{{ session.topic }}</p>
              
              <!-- Live Progress for Active Sessions -->
              <div v-if="session.status === 'in-progress'" class="live-progress">
                <div class="progress-info">
                  <span>{{ session.elapsedTime }} min</span>
                  <span>{{ session.remainingTime }} min restantes</span>
                </div>
                <div class="progress-bar-live">
                  <div class="progress-fill-live" :style="{ width: session.progressPercent + '%' }"></div>
                </div>
              </div>

              <!-- Session Meta -->
              <div class="session-meta-info">
                <span v-if="session.type" class="meta-badge" :class="`type-${session.type}`">
                  {{ getSessionTypeLabel(session.type) }}
                </span>
                <span v-if="session.difficulty" class="meta-badge difficulty">
                  {{ session.difficulty }}
                </span>
                <span v-if="session.focusRequired" class="meta-badge focus-level">
                  <i class="fas fa-brain"></i> {{ session.focusRequired }}% foco
                </span>
              </div>
            </div>

            <!-- Session Actions -->
            <div class="session-actions-optimized">
              <button 
                v-if="session.status === 'upcoming'"
                @click="startSession(session)" 
                class="action-btn start"
              >
                <i class="fas fa-play"></i>
              </button>
              <button 
                v-else-if="session.status === 'in-progress'"
                @click="togglePauseSession(session)" 
                class="action-btn pause"
              >
                <i :class="session.isPaused ? 'fas fa-play' : 'fas fa-pause'"></i>
              </button>
              <button 
                v-else-if="session.status === 'completed'"
                @click="viewSessionReport(session)" 
                class="action-btn report"
              >
                <i class="fas fa-chart-line"></i>
              </button>
              
              <div class="more-actions">
                <button @click="toggleSessionMenu(session.id)" class="action-btn more">
                  <i class="fas fa-ellipsis-v"></i>
                </button>
                <transition name="menu-slide">
                  <div v-if="openMenuId === session.id" class="session-menu">
                    <button @click="editSession(session)">
                      <i class="fas fa-edit"></i> Editar
                    </button>
                    <button @click="rescheduleSession(session)">
                      <i class="fas fa-clock"></i> Reagendar
                    </button>
                    <button @click="convertToTask(session)">
                      <i class="fas fa-tasks"></i> Criar Tarefa
                    </button>
                    <button @click="deleteSession(session)" class="danger">
                      <i class="fas fa-trash"></i> Excluir
                    </button>
                  </div>
                </transition>
              </div>
            </div>
          </div>
        </div>

        <!-- Add Session Button -->
        <div class="add-session-slot">
          <button @click="showNewSessionModal = true" class="add-session-btn">
            <i class="fas fa-plus"></i>
            <span>Adicionar Sessão</span>
          </button>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!todaySessions.length" class="empty-sessions">
        <div class="empty-illustration">
          <i class="fas fa-coffee"></i>
        </div>
        <h3>Dia livre de sessões</h3>
        <p>Que tal começar com uma sessão rápida de revisão?</p>
        <div class="empty-actions">
          <button @click="suggestOptimalSession" class="suggest-btn">
            <i class="fas fa-magic"></i>
            Sugerir Sessão Ideal
          </button>
          <button @click="showNewSessionModal = true" class="create-btn">
            <i class="fas fa-plus"></i>
            Criar Sessão
          </button>
        </div>
      </div>
    </div>

    <!-- Timeline View -->
    <div v-else-if="currentView === 'timeline'" class="timeline-container">
      <div class="timeline-hours">
        <div v-for="hour in displayHours" :key="hour" class="hour-mark">
          <span>{{ hour }}:00</span>
        </div>
      </div>
      <div class="timeline-track">
        <div class="current-time-indicator" :style="{ left: currentTimePercent + '%' }">
          <div class="indicator-dot"></div>
          <span class="indicator-time">{{ currentTime }}</span>
        </div>
        <div 
          v-for="session in todaySessions"
          :key="session.id"
          class="timeline-session"
          :style="getTimelinePosition(session)"
          :class="`status-${session.status}`"
          @click="selectSession(session)"
        >
          <span class="session-name">{{ session.subject }}</span>
          <span class="session-duration">{{ session.duration }}min</span>
        </div>
      </div>
    </div>

    <!-- Calendar View -->
    <div v-else-if="currentView === 'calendar'" class="week-calendar">
      <div class="week-header">
        <button @click="previousWeek" class="week-nav">
          <i class="fas fa-chevron-left"></i>
        </button>
        <span class="week-title">{{ weekRangeText }}</span>
        <button @click="nextWeek" class="week-nav">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
      <div class="week-grid">
        <div 
          v-for="day in weekDays" 
          :key="day.date"
          class="day-column"
          :class="{ 'is-today': isToday(day.date) }"
        >
          <div class="day-header">
            <span class="day-name">{{ day.dayName }}</span>
            <span class="day-date">{{ day.dayNumber }}</span>
          </div>
          <div class="day-sessions">
            <div 
              v-for="session in getSessionsForDay(day.date)"
              :key="session.id"
              class="mini-session-card"
              :class="`status-${session.status}`"
            >
              <span class="mini-time">{{ session.startTime }}</span>
              <span class="mini-subject">{{ session.subject }}</span>
            </div>
            <button 
              v-if="canAddSession(day.date)"
              @click="addSessionToDay(day.date)" 
              class="add-day-session"
            >
              <i class="fas fa-plus"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Session Stats Summary -->
    <div class="session-summary">
      <div class="summary-item">
        <span class="summary-label">Próxima sessão:</span>
        <span class="summary-value">{{ nextSessionInfo }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">Tempo até próxima:</span>
        <span class="summary-value">{{ timeUntilNext }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">Taxa de conclusão:</span>
        <span class="summary-value">{{ completionRate }}%</span>
      </div>
    </div>

    <!-- Modals -->
    <SessionModalOptimized
      v-if="showNewSessionModal"
      :session="selectedSession"
      :suggested-time="suggestedSessionTime"
      :available-subjects="availableSubjects"
      @save="saveSession"
      @close="closeModal"
    />
  </section>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import SessionModalOptimized from './SessionModalOptimized.vue'

export default {
  name: 'StudySessionsOptimized',
  components: {
    SessionModalOptimized
  },
  setup() {
    // View State
    const currentView = ref('list')
    const views = [
      { id: 'list', icon: 'fas fa-list', label: 'Lista' },
      { id: 'timeline', icon: 'fas fa-stream', label: 'Timeline' },
      { id: 'calendar', icon: 'fas fa-calendar-week', label: 'Semana' }
    ]
    
    // Session State
    const todaySessions = ref([])
    const selectedSession = ref(null)
    const showNewSessionModal = ref(false)
    const openMenuId = ref(null)
    
    // Time State
    const currentTime = ref('')
    const currentTimePosition = ref(0)
    const currentTimePercent = ref(0)
    let timeInterval = null
    
    // Week Navigation
    const weekOffset = ref(0)
    
    // Stats
    const totalHoursToday = ref(0)
    const focusScore = ref(0)
    const sessionsCompleted = ref(0)
    const totalSessionsToday = ref(0)
    const currentStreak = ref(0)
    
    // Load Sessions
    const loadTodaySessions = () => {
      // Simulated data - replace with actual API call
      todaySessions.value = [
        {
          id: 1,
          subject: 'Anatomia',
          topic: 'Sistema Cardiovascular',
          startTime: '08:00',
          endTime: '09:30',
          duration: 90,
          status: 'completed',
          type: 'review',
          difficulty: 'Médio',
          focusRequired: 85,
          progressPercent: 100
        },
        {
          id: 2,
          subject: 'Farmacologia',
          topic: 'Antibióticos',
          startTime: '10:00',
          endTime: '11:30',
          duration: 90,
          status: 'in-progress',
          type: 'new',
          difficulty: 'Difícil',
          focusRequired: 95,
          progressPercent: 45,
          elapsedTime: 40,
          remainingTime: 50,
          isPaused: false
        },
        {
          id: 3,
          subject: 'Neurociências',
          topic: 'Vias Sensoriais',
          startTime: '14:00',
          endTime: '15:00',
          duration: 60,
          status: 'upcoming',
          type: 'practice',
          difficulty: 'Fácil',
          focusRequired: 70
        }
      ]
      
      updateStats()
    }
    
    // Update Stats
    const updateStats = () => {
      const completed = todaySessions.value.filter(s => s.status === 'completed')
      sessionsCompleted.value = completed.length
      totalSessionsToday.value = todaySessions.value.length
      
      totalHoursToday.value = completed.reduce((acc, s) => acc + s.duration, 0) / 60
      focusScore.value = completed.length > 0 
        ? Math.round(completed.reduce((acc, s) => acc + (s.focusRequired || 80), 0) / completed.length)
        : 0
      
      // Get streak from localStorage
      currentStreak.value = parseInt(localStorage.getItem('studyStreak') || '0')
    }
    
    // Time Management
    const updateCurrentTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
      
      // Calculate position for timeline
      const hours = now.getHours()
      const minutes = now.getMinutes()
      const totalMinutes = hours * 60 + minutes
      const dayStart = 6 * 60 // 6:00 AM
      const dayEnd = 23 * 60 // 11:00 PM
      
      currentTimePosition.value = ((totalMinutes - dayStart) / (dayEnd - dayStart)) * 100
      currentTimePercent.value = ((hours - 6) / 17) * 100 // 6am to 11pm
    }
    
    // Session Actions
    const startPomodoro = () => {
      createQuickSession('pomodoro', 25)
    }
    
    const startFocusSession = () => {
      createQuickSession('focus', 50)
    }
    
    const startReviewSession = () => {
      createQuickSession('review', 15)
    }
    
    const createQuickSession = (type, duration) => {
      const now = new Date()
      const startTime = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
      const endTime = new Date(now.getTime() + duration * 60000).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
      
      const quickSession = {
        id: Date.now(),
        subject: 'Sessão Rápida',
        topic: getSessionTypeLabel(type),
        startTime,
        endTime,
        duration,
        status: 'in-progress',
        type,
        progressPercent: 0,
        elapsedTime: 0,
        remainingTime: duration
      }
      
      todaySessions.value.push(quickSession)
      startSession(quickSession)
    }
    
    const createCustomSession = () => {
      selectedSession.value = null
      showNewSessionModal.value = true
    }
    
    const startSession = (session) => {
      session.status = 'in-progress'
      session.isPaused = false
      // Start timer logic here
    }
    
    const togglePauseSession = (session) => {
      session.isPaused = !session.isPaused
    }
    
    const viewSessionReport = (session) => {
      console.log('View report for:', session)
    }
    
    const toggleSessionMenu = (sessionId) => {
      openMenuId.value = openMenuId.value === sessionId ? null : sessionId
    }
    
    const editSession = (session) => {
      selectedSession.value = session
      showNewSessionModal.value = true
      openMenuId.value = null
    }
    
    const rescheduleSession = (session) => {
      console.log('Reschedule:', session)
      openMenuId.value = null
    }
    
    const convertToTask = (session) => {
      console.log('Convert to task:', session)
      openMenuId.value = null
    }
    
    const deleteSession = (session) => {
      if (confirm(`Excluir sessão de ${session.subject}?`)) {
        const index = todaySessions.value.findIndex(s => s.id === session.id)
        if (index > -1) {
          todaySessions.value.splice(index, 1)
        }
      }
      openMenuId.value = null
    }
    
    const suggestOptimalSession = () => {
      // AI suggestion logic
      console.log('Suggesting optimal session...')
    }
    
    const saveSession = (sessionData) => {
      if (selectedSession.value) {
        // Update existing
        Object.assign(selectedSession.value, sessionData)
      } else {
        // Create new
        todaySessions.value.push({
          ...sessionData,
          id: Date.now(),
          status: 'upcoming'
        })
      }
      closeModal()
    }
    
    const closeModal = () => {
      showNewSessionModal.value = false
      selectedSession.value = null
    }
    
    // Helper Functions
    const getSessionTypeLabel = (type) => {
      const labels = {
        pomodoro: 'Pomodoro',
        focus: 'Foco Profundo',
        review: 'Revisão',
        practice: 'Prática',
        new: 'Nova Matéria'
      }
      return labels[type] || type
    }
    
    const displayHours = computed(() => {
      return Array.from({ length: 18 }, (_, i) => i + 6) // 6 AM to 11 PM
    })
    
    const getTimelinePosition = (session) => {
      const [hours, minutes] = session.startTime.split(':').map(Number)
      const [endHours, endMinutes] = session.endTime.split(':').map(Number)
      
      const startPercent = ((hours - 6 + minutes / 60) / 17) * 100
      const duration = ((endHours - hours) + (endMinutes - minutes) / 60)
      const widthPercent = (duration / 17) * 100
      
      return {
        left: `${startPercent}%`,
        width: `${widthPercent}%`
      }
    }
    
    // Week Calendar
    const weekDays = computed(() => {
      const days = []
      const today = new Date()
      const startOfWeek = new Date(today)
      startOfWeek.setDate(today.getDate() - today.getDay() + weekOffset.value * 7)
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek)
        date.setDate(startOfWeek.getDate() + i)
        days.push({
          date: date.toISOString().split('T')[0],
          dayName: date.toLocaleDateString('pt-BR', { weekday: 'short' }),
          dayNumber: date.getDate()
        })
      }
      
      return days
    })
    
    const weekRangeText = computed(() => {
      const start = weekDays.value[0]
      const end = weekDays.value[6]
      return `${start.dayNumber} - ${end.dayNumber} ${new Date(end.date).toLocaleDateString('pt-BR', { month: 'short' })}`
    })
    
    const isToday = (date) => {
      return date === new Date().toISOString().split('T')[0]
    }
    
    const getSessionsForDay = (date) => {
      // Filter sessions for specific date
      return todaySessions.value.filter(s => {
        // For now, return today's sessions
        return isToday(date)
      })
    }
    
    const canAddSession = (date) => {
      const today = new Date().toISOString().split('T')[0]
      return date >= today
    }
    
    const addSessionToDay = (date) => {
      console.log('Add session to:', date)
      showNewSessionModal.value = true
    }
    
    const previousWeek = () => {
      weekOffset.value--
    }
    
    const nextWeek = () => {
      weekOffset.value++
    }
    
    // Computed Stats
    const nextSessionInfo = computed(() => {
      const upcoming = todaySessions.value.find(s => s.status === 'upcoming')
      return upcoming ? `${upcoming.subject} às ${upcoming.startTime}` : 'Nenhuma sessão agendada'
    })
    
    const timeUntilNext = computed(() => {
      const upcoming = todaySessions.value.find(s => s.status === 'upcoming')
      if (!upcoming) return '--'
      
      const now = new Date()
      const [hours, minutes] = upcoming.startTime.split(':').map(Number)
      const sessionTime = new Date()
      sessionTime.setHours(hours, minutes, 0)
      
      const diff = sessionTime - now
      if (diff < 0) return 'Atrasado'
      
      const diffHours = Math.floor(diff / 3600000)
      const diffMinutes = Math.floor((diff % 3600000) / 60000)
      
      return diffHours > 0 ? `${diffHours}h ${diffMinutes}min` : `${diffMinutes}min`
    })
    
    const completionRate = computed(() => {
      if (totalSessionsToday.value === 0) return 0
      return Math.round((sessionsCompleted.value / totalSessionsToday.value) * 100)
    })
    
    const availableSubjects = computed(() => {
      // Get from store or API
      return ['Anatomia', 'Farmacologia', 'Neurociências', 'Fisiologia', 'Patologia']
    })
    
    const suggestedSessionTime = computed(() => {
      // Calculate next available slot
      const now = new Date()
      return now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
    })
    
    // Lifecycle
    onMounted(() => {
      loadTodaySessions()
      updateCurrentTime()
      timeInterval = setInterval(updateCurrentTime, 60000) // Update every minute
      
      // Close menu on outside click
      document.addEventListener('click', () => {
        openMenuId.value = null
      })
    })
    
    onBeforeUnmount(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })
    
    return {
      // State
      currentView,
      views,
      todaySessions,
      selectedSession,
      showNewSessionModal,
      openMenuId,
      currentTime,
      currentTimePosition,
      currentTimePercent,
      weekOffset,
      
      // Stats
      totalHoursToday,
      focusScore,
      sessionsCompleted,
      totalSessionsToday,
      currentStreak,
      
      // Methods
      startPomodoro,
      startFocusSession,
      startReviewSession,
      createCustomSession,
      startSession,
      togglePauseSession,
      viewSessionReport,
      toggleSessionMenu,
      editSession,
      rescheduleSession,
      convertToTask,
      deleteSession,
      suggestOptimalSession,
      saveSession,
      closeModal,
      getSessionTypeLabel,
      
      // Timeline
      displayHours,
      getTimelinePosition,
      
      // Calendar
      weekDays,
      weekRangeText,
      isToday,
      getSessionsForDay,
      canAddSession,
      addSessionToDay,
      previousWeek,
      nextWeek,
      
      // Computed
      nextSessionInfo,
      timeUntilNext,
      completionRate,
      availableSubjects,
      suggestedSessionTime
    }
  }
}
</script>

<style scoped>
.sessions-section {
  width: 100%;
  overflow: hidden;
}

/* Today's Focus Bar */
.today-focus-bar {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.focus-metric {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.focus-metric i {
  font-size: 1.5rem;
  color: #667eea;
}

.metric-info {
  display: flex;
  flex-direction: column;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
}

.metric-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Quick Session Bar */
.quick-session-bar {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.quick-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.quick-btn i {
  font-size: 1.5rem;
}

.quick-btn span {
  font-size: 0.813rem;
  font-weight: 500;
}

.quick-btn.pomodoro {
  border-color: rgba(245, 87, 108, 0.3);
}

.quick-btn.pomodoro:hover {
  background: rgba(245, 87, 108, 0.1);
}

.quick-btn.focus {
  border-color: rgba(102, 126, 234, 0.3);
}

.quick-btn.focus:hover {
  background: rgba(102, 126, 234, 0.1);
}

.quick-btn.review {
  border-color: rgba(74, 222, 128, 0.3);
}

.quick-btn.review:hover {
  background: rgba(74, 222, 128, 0.1);
}

/* Sessions Container */
.sessions-container {
  position: relative;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 0.5rem;
}

.sessions-container::-webkit-scrollbar {
  width: 6px;
}

.sessions-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.sessions-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

/* Sessions Timeline */
.sessions-timeline {
  position: relative;
  padding-left: 100px;
}

.current-time-line {
  position: absolute;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  z-index: 10;
}

.time-now {
  position: absolute;
  left: 10px;
  padding: 0.25rem 0.75rem;
  background: #f5576c;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 12px;
}

.time-line {
  position: absolute;
  left: 80px;
  right: 0;
  height: 2px;
  background: #f5576c;
  opacity: 0.5;
}

/* Session Item */
.session-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.time-block {
  position: absolute;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80px;
}

.start-time,
.end-time {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.time-connector {
  width: 2px;
  height: 30px;
  background: rgba(255, 255, 255, 0.2);
  margin: 0.25rem 0;
}

/* Session Card Optimized */
.session-card-optimized {
  flex: 1;
  display: flex;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.25rem;
  transition: all 0.3s ease;
}

.session-item.is-active .session-card-optimized {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.5);
}

.session-item.is-completed .session-card-optimized {
  opacity: 0.7;
}

.session-main {
  flex: 1;
}

.session-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.session-header h4 {
  margin: 0;
  font-size: 1.125rem;
  color: white;
}

.session-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-upcoming {
  background: rgba(255, 255, 255, 0.4);
}

.status-in-progress {
  background: #4ade80;
  animation: pulse 2s ease-in-out infinite;
}

.status-completed {
  background: rgba(255, 255, 255, 0.3);
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.2); }
}

.session-topic {
  margin: 0 0 0.75rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Live Progress */
.live-progress {
  margin: 1rem 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.5rem;
}

.progress-bar-live {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill-live {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

/* Session Meta */
.session-meta-info {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.meta-badge {
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
}

.meta-badge.type-review {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.meta-badge.type-new {
  background: rgba(240, 147, 251, 0.2);
  color: #f093fb;
}

.meta-badge.type-practice {
  background: rgba(74, 222, 128, 0.2);
  color: #4ade80;
}

/* Session Actions */
.session-actions-optimized {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.action-btn.start {
  background: rgba(74, 222, 128, 0.2);
  border-color: rgba(74, 222, 128, 0.5);
  color: #4ade80;
}

.action-btn.pause {
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.5);
  color: #fbbf24;
}

.action-btn.report {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  color: #667eea;
}

/* More Actions Menu */
.more-actions {
  position: relative;
}

.session-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: rgba(30, 30, 30, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.5rem;
  min-width: 160px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.session-menu button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.625rem 0.875rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.813rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
}

.session-menu button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.session-menu button.danger {
  color: #f87171;
}

/* Add Session */
.add-session-slot {
  margin-top: 1.5rem;
  padding-left: 0;
}

.add-session-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.add-session-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
  color: white;
}

/* Empty State */
.empty-sessions {
  text-align: center;
  padding: 3rem 2rem;
}

.empty-illustration {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.2);
  margin-bottom: 1.5rem;
}

.empty-sessions h3 {
  font-size: 1.5rem;
  margin: 0 0 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.empty-sessions p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 1.5rem;
}

.empty-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.suggest-btn,
.create-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggest-btn {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
}

.create-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

/* Timeline View */
.timeline-container {
  padding: 2rem 0;
  overflow-x: auto;
}

.timeline-hours {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0 1rem;
}

.hour-mark {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.timeline-track {
  position: relative;
  height: 100px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  margin: 0 1rem;
}

.current-time-indicator {
  position: absolute;
  top: -20px;
  bottom: -20px;
  width: 2px;
  background: #f5576c;
}

.indicator-dot {
  position: absolute;
  top: 50%;
  left: -4px;
  width: 10px;
  height: 10px;
  background: #f5576c;
  border-radius: 50%;
  transform: translateY(-50%);
}

.indicator-time {
  position: absolute;
  top: -30px;
  left: -20px;
  font-size: 0.75rem;
  color: #f5576c;
  font-weight: 600;
  white-space: nowrap;
}

.timeline-session {
  position: absolute;
  top: 20px;
  height: 60px;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.5);
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.timeline-session:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
}

.session-name {
  font-size: 0.813rem;
  font-weight: 600;
  color: white;
}

.session-duration {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Week Calendar */
.week-calendar {
  padding: 1rem 0;
}

.week-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.week-nav {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.week-nav:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.week-title {
  font-size: 1rem;
  font-weight: 600;
  color: white;
}

.week-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.day-column {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.75rem;
  min-height: 150px;
}

.day-column.is-today {
  background: rgba(102, 126, 234, 0.05);
  border-color: rgba(102, 126, 234, 0.3);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.day-name {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
}

.day-date {
  font-size: 1rem;
  font-weight: 600;
  color: white;
}

.day-sessions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mini-session-card {
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mini-session-card:hover {
  background: rgba(255, 255, 255, 0.1);
}

.mini-time {
  display: block;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.25rem;
}

.mini-subject {
  display: block;
  color: white;
  font-weight: 500;
}

.add-day-session {
  width: 100%;
  padding: 0.5rem;
  background: transparent;
  border: 1px dashed rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-day-session:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.4);
  color: white;
}

/* Session Summary */
.session-summary {
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  margin-top: 2rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

.summary-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

/* Transitions */
.menu-slide-enter-active,
.menu-slide-leave-active {
  transition: all 0.2s ease;
}

.menu-slide-enter-from,
.menu-slide-leave-to {
  opacity: 0;
  transform: translateY(-5px);
}

/* Responsive */
@media (max-width: 768px) {
  .sessions-timeline {
    padding-left: 70px;
  }
  
  .time-block {
    width: 60px;
  }
  
  .time-now {
    left: 5px;
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
  }
  
  .time-line {
    left: 60px;
  }
  
  .week-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .session-summary {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .today-focus-bar {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-session-bar {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>