<template>
  <footer class="app-footer">
    <div class="footer-wave-container">
      <div class="footer-wave"></div>
    </div>

    <div class="footer-content">
      <div class="footer-grid">
        <div class="footer-column">
          <h3 class="footer-title">Sophos Academy</h3>
          <p class="footer-about">
            Plataforma de estudo médico com recursos de IA avançada para maximizar seu aprendizado e otimizar sua preparação.
          </p>
          <div class="social-icons">
            <a href="#" class="social-icon" title="Twitter">
              <font-awesome-icon icon="fa-brands fa-twitter" />
            </a>
            <a href="#" class="social-icon" title="Instagram">
              <font-awesome-icon icon="fa-brands fa-instagram" />
            </a>
            <a href="#" class="social-icon" title="LinkedIn">
              <font-awesome-icon icon="fa-brands fa-linkedin-in" />
            </a>
            <a href="#" class="social-icon" title="YouTube">
              <font-awesome-icon icon="fa-brands fa-youtube" />
            </a>
          </div>
        </div>

        <div class="footer-column">
          <h3 class="footer-title">Navegação</h3>
          <ul class="footer-links">
            <li><router-link to="/">Início</router-link></li>
            <li><router-link to="/calendar">Calendário</router-link></li>
            <li><router-link to="/flashcards">Flashcards</router-link></li>
            <li><router-link to="/second-brain">Second Brain</router-link></li>
            <li><router-link to="/progress">Progresso</router-link></li>
          </ul>
        </div>

        <div class="footer-column">
          <h3 class="footer-title">Ferramentas IA</h3>
          <ul class="footer-links">
            <li><router-link to="/ai-tools/question-generator">Gerador de Questões</router-link></li>
            <li><router-link to="/round-ai">Round AI</router-link></li>
          </ul>
        </div>

        <div class="footer-column">
          <h3 class="footer-title">Suporte</h3>
          <ul class="footer-links">
            <li><router-link to="/faq">FAQ</router-link></li>
            <li><router-link to="/contact">Contato</router-link></li>
            <li><router-link to="/privacy">Privacidade</router-link></li>
            <li><router-link to="/terms">Termos de Uso</router-link></li>
            <li><router-link to="/help">Central de Ajuda</router-link></li>
          </ul>
        </div>
      </div>
    </div>

    <div class="footer-bottom">
      <div class="copyright">
        &copy; {{ currentYear }} Sophos Academy. Todos os direitos reservados.
      </div>
      <div class="footer-tech">
        <span class="tech-badge">
          <font-awesome-icon icon="fa-solid fa-brain" />
          <span>IA Avançada</span>
        </span>
        <span class="tech-badge">
          <font-awesome-icon icon="fa-solid fa-shield-alt" />
          <span>Dados Seguros</span>
        </span>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'AppFooter',
  computed: {
    currentYear() {
      return new Date().getFullYear();
    }
  }
};
</script>

<style scoped>
.app-footer {
  position: relative;
  margin-top: auto;
  color: var(--text-light);
  font-size: 0.95rem;
  overflow: hidden;
  background: linear-gradient(180deg, rgba(20, 20, 30, 0.9) 0%, rgba(30, 30, 50, 0.95) 100%);
  animation: footerFadeIn 0.8s ease-out;
}

@keyframes footerFadeIn {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Enhanced wave animation at the top of the footer */
.footer-wave-container {
  position: relative;
  height: 70px;
  width: 100%;
  overflow: hidden;
}

.footer-wave {
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%234DEEEA'/%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%234DEEEA'/%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' opacity='.25' fill='%23B65BFF'/%3E%3C/svg%3E");
  background-size: 1200px 100%;
  animation: waveAnimation 12s linear infinite;
}

@keyframes waveAnimation {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

.footer-content {
  position: relative;
  padding: 3rem 2rem;
  max-width: 1280px;
  margin: 0 auto;
}

.footer-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 2rem;
}

.footer-column {
  display: flex;
  flex-direction: column;
}

.footer-title {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
  position: relative;
  display: inline-block;
  padding-bottom: 0.5rem;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--neon-cyan);
  box-shadow: 0 0 10px var(--neon-cyan);
  animation: footerTitleLine 3s infinite alternate;
}

@keyframes footerTitleLine {
  0% { width: 40px; opacity: 0.7; }
  100% { width: 80px; opacity: 1; }
}

.footer-about {
  margin-bottom: 1.5rem;
  opacity: 0.8;
  line-height: 1.6;
}

.social-icons {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  font-size: 1.2rem;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.social-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--neon-cyan), var(--neon-blue));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.social-icon:hover {
  transform: translateY(-5px) scale(1.1);
  color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.social-icon:hover::before {
  opacity: 1;
}

.social-icon:nth-child(1):hover { 
  box-shadow: 0 5px 15px rgba(29, 161, 242, 0.5);
}

.social-icon:nth-child(2):hover { 
  box-shadow: 0 5px 15px rgba(225, 48, 108, 0.5); 
}

.social-icon:nth-child(3):hover { 
  box-shadow: 0 5px 15px rgba(0, 119, 181, 0.5); 
}

.social-icon:nth-child(4):hover { 
  box-shadow: 0 5px 15px rgba(255, 0, 0, 0.5); 
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.footer-links li a {
  color: var(--text-light);
  opacity: 0.8;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
}

.footer-links li a::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--neon-cyan);
  transition: width 0.3s ease;
}

.footer-links li a:hover {
  color: var(--neon-cyan);
  opacity: 1;
  transform: translateX(5px);
  text-shadow: 0 0 5px rgba(77, 238, 234, 0.5);
}

.footer-links li a:hover::before {
  width: 100%;
  box-shadow: 0 0 5px rgba(77, 238, 234, 0.5);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(15, 15, 25, 0.5);
}

.copyright {
  font-size: 0.9rem;
  opacity: 0.7;
}

.footer-tech {
  display: flex;
  gap: 1rem;
}

.tech-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.tech-badge:hover {
  background: rgba(77, 238, 234, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.tech-badge:hover svg {
  color: var(--neon-cyan);
  transform: scale(1.2);
}

.tech-badge svg {
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

/* Responsive styles */
@media (max-width: 992px) {
  .footer-grid {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-content {
    padding: 2rem 1rem;
  }
}
</style>
