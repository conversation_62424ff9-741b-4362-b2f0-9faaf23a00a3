<template>
  <div class="unified-platform">
    <!-- Background Effects -->
    <div class="platform-background">
      <div class="gradient-overlay"></div>
      <div class="particle-system"></div>
    </div>

    <!-- Header -->
    <header class="platform-header">
      <div class="header-container">
        <div class="logo-section">
          <div class="logo-icon">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <h1>Mentor AI Platform</h1>
        </div>
        
        <nav class="main-nav">
          <button 
            v-for="tab in tabs" 
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="['nav-tab', { active: activeTab === tab.id }]">
            <i :class="tab.icon"></i>
            <span>{{ tab.name }}</span>
          </button>
        </nav>

        <div class="user-section">
          <div class="notifications">
            <i class="fas fa-bell"></i>
            <span class="notification-badge">3</span>
          </div>
          <div class="user-avatar">
            <img src="https://ui-avatars.com/api/?name=User&background=6366f1&color=fff" alt="User">
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content Area -->
    <main class="platform-main">
      <div class="content-container">
        <!-- Dashboard View -->
        <div v-if="activeTab === 'dashboard'" class="tab-content dashboard-view">
          <h2 class="section-title">Dashboard Overview</h2>
          
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <i class="fas fa-question-circle"></i>
              </div>
              <div class="stat-content">
                <h3>{{ totalQuestions }}</h3>
                <p>Questions Generated</p>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <i class="fas fa-layer-group"></i>
              </div>
              <div class="stat-content">
                <h3>{{ totalFlashcards }}</h3>
                <p>Active Flashcards</p>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <i class="fas fa-file-alt"></i>
              </div>
              <div class="stat-content">
                <h3>{{ documentsProcessed }}</h3>
                <p>Documents Analyzed</p>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="stat-content">
                <h3>{{ studyStreak }} days</h3>
                <p>Study Streak</p>
              </div>
            </div>
          </div>

          <div class="recent-activity">
            <h3>Recent Activity</h3>
            <div class="activity-list">
              <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                <div class="activity-icon" :style="{ background: activity.color }">
                  <i :class="activity.icon"></i>
                </div>
                <div class="activity-content">
                  <p>{{ activity.description }}</p>
                  <span class="activity-time">{{ activity.time }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Assistant View -->
        <div v-if="activeTab === 'ai-assistant'" class="tab-content">
          <AIAssistantAdvanced />
        </div>

        <!-- Ultra Questions View -->
        <div v-if="activeTab === 'ultra-questions'" class="tab-content">
          <AIQuestionGeneratorUltra />
        </div>

        <!-- Flashcards View -->
        <div v-if="activeTab === 'flashcards'" class="tab-content">
          <FlashcardsPage />
        </div>

        <!-- Analytics View -->
        <div v-if="activeTab === 'analytics'" class="tab-content">
          <AdvancedAnalyticsDashboard />
        </div>

        <!-- Settings View -->
        <div v-if="activeTab === 'settings'" class="tab-content settings-view">
          <h2 class="section-title">Platform Settings</h2>
          
          <div class="settings-grid">
            <div class="settings-section">
              <h3><i class="fas fa-brain"></i> AI Configuration</h3>
              <div class="settings-content">
                <div class="setting-item">
                  <label>Default AI Model</label>
                  <select v-model="settings.aiModel" class="setting-select">
                    <option value="gpt-4-turbo">GPT-4 Turbo</option>
                    <option value="claude-3">Claude 3</option>
                    <option value="groq">Groq</option>
                  </select>
                </div>
                <div class="setting-item">
                  <label>Question Difficulty</label>
                  <input type="range" v-model="settings.defaultDifficulty" min="1" max="10" class="setting-range">
                  <span class="range-value">{{ settings.defaultDifficulty }}</span>
                </div>
              </div>
            </div>

            <div class="settings-section">
              <h3><i class="fas fa-language"></i> Language & Region</h3>
              <div class="settings-content">
                <div class="setting-item">
                  <label>Platform Language</label>
                  <select v-model="settings.language" class="setting-select">
                    <option value="pt">Português</option>
                    <option value="en">English</option>
                    <option value="es">Español</option>
                  </select>
                </div>
                <div class="setting-item">
                  <label>Medical Specialty</label>
                  <select v-model="settings.specialty" class="setting-select">
                    <option value="cardiologia">Cardiologia</option>
                    <option value="neurologia">Neurologia</option>
                    <option value="pediatria">Pediatria</option>
                    <option value="clinica_medica">Clínica Médica</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="settings-section">
              <h3><i class="fas fa-bell"></i> Notifications</h3>
              <div class="settings-content">
                <div class="setting-item">
                  <label class="toggle-label">
                    <input type="checkbox" v-model="settings.emailNotifications" class="toggle-input">
                    <span class="toggle-switch"></span>
                    Email Notifications
                  </label>
                </div>
                <div class="setting-item">
                  <label class="toggle-label">
                    <input type="checkbox" v-model="settings.studyReminders" class="toggle-input">
                    <span class="toggle-switch"></span>
                    Study Reminders
                  </label>
                </div>
              </div>
            </div>

            <div class="settings-section">
              <h3><i class="fas fa-shield-alt"></i> Privacy & Security</h3>
              <div class="settings-content">
                <div class="setting-item">
                  <label class="toggle-label">
                    <input type="checkbox" v-model="settings.dataAnalytics" class="toggle-input">
                    <span class="toggle-switch"></span>
                    Usage Analytics
                  </label>
                </div>
                <button class="danger-button">
                  <i class="fas fa-trash"></i>
                  Clear All Data
                </button>
              </div>
            </div>
          </div>

          <div class="settings-actions">
            <button @click="saveSettings" class="save-button">
              <i class="fas fa-save"></i>
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </main>

    <!-- Floating Action Button -->
    <div class="fab-container">
      <button @click="showQuickActions = !showQuickActions" class="fab-main">
        <i class="fas fa-plus"></i>
      </button>
      <transition name="fab-menu">
        <div v-if="showQuickActions" class="fab-menu">
          <button @click="quickAction('upload')" class="fab-option" title="Upload Document">
            <i class="fas fa-upload"></i>
          </button>
          <button @click="quickAction('generate')" class="fab-option" title="Generate Questions">
            <i class="fas fa-magic"></i>
          </button>
          <button @click="quickAction('flashcard')" class="fab-option" title="Create Flashcard">
            <i class="fas fa-layer-group"></i>
          </button>
        </div>
      </transition>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container">
      <transition-group name="toast">
        <div v-for="toast in toasts" :key="toast.id" :class="['toast', toast.type]">
          <i :class="toast.icon"></i>
          <span>{{ toast.message }}</span>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import AIAssistantAdvanced from './AIAssistantAdvanced.vue';
import AIQuestionGeneratorUltra from './AIQuestionGeneratorUltra.vue';
import FlashcardsPage from './FlashcardsPage.vue';
import AdvancedAnalyticsDashboard from './AdvancedAnalyticsDashboard.vue';

export default {
  name: 'UnifiedPlatform',
  
  components: {
    AIAssistantAdvanced,
    AIQuestionGeneratorUltra,
    FlashcardsPage,
    AdvancedAnalyticsDashboard
  },

  setup() {
    // State
    const activeTab = ref('dashboard');
    const showQuickActions = ref(false);
    const toasts = ref([]);
    
    // Stats
    const totalQuestions = ref(1247);
    const totalFlashcards = ref(523);
    const documentsProcessed = ref(89);
    const studyStreak = ref(15);
    
    // Navigation tabs
    const tabs = ref([
      { id: 'dashboard', name: 'Dashboard', icon: 'fas fa-home' },
      { id: 'ai-assistant', name: 'AI Assistant', icon: 'fas fa-robot' },
      { id: 'ultra-questions', name: 'Ultra Questions', icon: 'fas fa-brain' },
      { id: 'flashcards', name: 'Flashcards', icon: 'fas fa-layer-group' },
      { id: 'analytics', name: 'Analytics', icon: 'fas fa-chart-bar' },
      { id: 'settings', name: 'Settings', icon: 'fas fa-cog' }
    ]);
    
    // Recent activities
    const recentActivities = ref([
      {
        id: 1,
        icon: 'fas fa-question-circle',
        description: 'Generated 25 cardiology questions',
        time: '5 minutes ago',
        color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      },
      {
        id: 2,
        icon: 'fas fa-file-upload',
        description: 'Uploaded Harrison\'s Internal Medicine PDF',
        time: '1 hour ago',
        color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
      },
      {
        id: 3,
        icon: 'fas fa-layer-group',
        description: 'Reviewed 45 flashcards',
        time: '3 hours ago',
        color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
      },
      {
        id: 4,
        icon: 'fas fa-trophy',
        description: 'Achieved 15-day study streak!',
        time: 'Yesterday',
        color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
      }
    ]);
    
    // Settings
    const settings = ref({
      aiModel: 'gpt-4-turbo',
      defaultDifficulty: 5,
      language: 'pt',
      specialty: 'clinica_medica',
      emailNotifications: true,
      studyReminders: true,
      dataAnalytics: true
    });
    
    // Methods
    const showToast = (message, type = 'success') => {
      const toast = {
        id: Date.now(),
        message,
        type,
        icon: type === 'success' ? 'fas fa-check-circle' : 
              type === 'error' ? 'fas fa-exclamation-circle' : 
              'fas fa-info-circle'
      };
      
      toasts.value.push(toast);
      
      setTimeout(() => {
        const index = toasts.value.findIndex(t => t.id === toast.id);
        if (index > -1) {
          toasts.value.splice(index, 1);
        }
      }, 3000);
    };
    
    const quickAction = (action) => {
      showQuickActions.value = false;
      
      switch(action) {
        case 'upload':
          activeTab.value = 'ai-assistant';
          showToast('Navigate to AI Assistant for document upload');
          break;
        case 'generate':
          activeTab.value = 'ultra-questions';
          showToast('Navigate to Ultra Questions generator');
          break;
        case 'flashcard':
          activeTab.value = 'flashcards';
          showToast('Navigate to Flashcards creator');
          break;
      }
    };
    
    const saveSettings = () => {
      // In real app, this would save to backend
      localStorage.setItem('mentor-ai-settings', JSON.stringify(settings.value));
      showToast('Settings saved successfully!');
    };
    
    // Load settings on mount
    onMounted(() => {
      const savedSettings = localStorage.getItem('mentor-ai-settings');
      if (savedSettings) {
        settings.value = JSON.parse(savedSettings);
      }
      
      // Simulate real-time updates
      setInterval(() => {
        totalQuestions.value += Math.floor(Math.random() * 3);
        totalFlashcards.value += Math.floor(Math.random() * 2);
      }, 30000);
    });
    
    return {
      activeTab,
      showQuickActions,
      toasts,
      tabs,
      totalQuestions,
      totalFlashcards,
      documentsProcessed,
      studyStreak,
      recentActivities,
      settings,
      showToast,
      quickAction,
      saveSettings
    };
  }
};
</script>

<style scoped>
/* CSS Variables */
:root {
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --secondary: #8b5cf6;
  --success: #10b981;
  --danger: #ef4444;
  --warning: #f59e0b;
  --info: #3b82f6;
  --dark: #0f172a;
  --dark-lighter: #1e293b;
  --dark-card: #334155;
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --border: #475569;
}

/* Base Styles */
.unified-platform {
  min-height: 100vh;
  background: var(--dark);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Background Effects */
.platform-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.gradient-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at top, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
              radial-gradient(ellipse at bottom, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

.particle-system {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20% 30%, var(--primary), transparent),
    radial-gradient(2px 2px at 60% 70%, var(--secondary), transparent),
    radial-gradient(1px 1px at 90% 10%, white, transparent);
  background-size: 300px 300px;
  animation: particle-drift 60s linear infinite;
}

@keyframes particle-drift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-300px, -300px); }
}

/* Header */
.platform-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border);
}

.header-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Logo Section */
.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
}

.logo-section h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Main Navigation */
.main-nav {
  display: flex;
  gap: 0.5rem;
  background: var(--dark-lighter);
  padding: 0.5rem;
  border-radius: 12px;
}

.nav-tab {
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-tab:hover {
  color: var(--text-primary);
  background: rgba(99, 102, 241, 0.1);
}

.nav-tab.active {
  color: white;
  background: var(--primary);
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

/* User Section */
.user-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.notifications {
  position: relative;
  cursor: pointer;
  font-size: 1.25rem;
  color: var(--text-secondary);
  transition: color 0.3s;
}

.notifications:hover {
  color: var(--text-primary);
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--danger);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid var(--primary);
  transition: transform 0.3s;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Main Content */
.platform-main {
  position: relative;
  z-index: 1;
  min-height: calc(100vh - 82px);
}

.content-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem;
}

.tab-content {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dashboard View */
.section-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 2rem 0;
  color: var(--text-primary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  border-color: var(--primary);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  color: var(--text-primary);
}

.stat-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Recent Activity */
.recent-activity {
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  border-radius: 16px;
  padding: 2rem;
}

.recent-activity h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--dark-card);
  border-radius: 12px;
  transition: all 0.3s;
}

.activity-item:hover {
  background: rgba(99, 102, 241, 0.1);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-content p {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.activity-time {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* Settings View */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.settings-section {
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  border-radius: 16px;
  padding: 1.5rem;
}

.settings-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-primary);
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-item label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.setting-select {
  padding: 0.75rem 1rem;
  background: var(--dark-card);
  border: 1px solid var(--border);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.setting-select:hover,
.setting-select:focus {
  border-color: var(--primary);
  outline: none;
}

.setting-range {
  width: 100%;
  height: 6px;
  background: var(--dark-card);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.setting-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: var(--primary);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s;
}

.setting-range::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
}

.range-value {
  font-size: 0.875rem;
  color: var(--primary);
  font-weight: 600;
  text-align: center;
  margin-top: 0.5rem;
  display: block;
}

/* Toggle Switch */
.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.9rem;
}

.toggle-input {
  display: none;
}

.toggle-switch {
  position: relative;
  width: 48px;
  height: 24px;
  background: var(--dark-card);
  border: 1px solid var(--border);
  border-radius: 12px;
  transition: all 0.3s;
}

.toggle-switch::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background: var(--text-secondary);
  border-radius: 50%;
  transition: all 0.3s;
}

.toggle-input:checked + .toggle-switch {
  background: var(--primary);
  border-color: var(--primary);
}

.toggle-input:checked + .toggle-switch::after {
  transform: translateX(24px);
  background: white;
}

/* Buttons */
.save-button {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.save-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

.danger-button {
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: 1px solid var(--danger);
  border-radius: 8px;
  color: var(--danger);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.danger-button:hover {
  background: var(--danger);
  color: white;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 2rem;
  border-top: 1px solid var(--border);
}

/* Floating Action Button */
.fab-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.fab-main {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.4);
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-main:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 30px rgba(99, 102, 241, 0.5);
}

.fab-menu {
  position: absolute;
  bottom: 80px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.fab-option {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  color: var(--text-primary);
  font-size: 1.125rem;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-option:hover {
  background: var(--primary);
  color: white;
  transform: scale(1.1);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 100px;
  right: 2rem;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.toast {
  padding: 1rem 1.5rem;
  background: var(--dark-lighter);
  border: 1px solid var(--border);
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 300px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.toast.success {
  border-color: var(--success);
  background: rgba(16, 185, 129, 0.1);
}

.toast.error {
  border-color: var(--danger);
  background: rgba(239, 68, 68, 0.1);
}

.toast i {
  font-size: 1.25rem;
}

.toast.success i {
  color: var(--success);
}

.toast.error i {
  color: var(--danger);
}

/* Transitions */
.fab-menu-enter-active,
.fab-menu-leave-active {
  transition: all 0.3s ease;
}

.fab-menu-enter-from,
.fab-menu-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100px);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100px);
}

/* Responsive */
@media (max-width: 1024px) {
  .header-container {
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .main-nav {
    order: 3;
    width: 100%;
    justify-content: space-around;
  }
  
  .nav-tab {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  
  .nav-tab span {
    display: none;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .content-container {
    padding: 1rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .fab-container {
    bottom: 1rem;
    right: 1rem;
  }
  
  .toast-container {
    right: 1rem;
    left: 1rem;
  }
  
  .toast {
    min-width: auto;
  }
}
</style>