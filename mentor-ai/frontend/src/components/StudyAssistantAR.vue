<template>
  <div class="ar-container">
    <!-- AR Viewer -->
    <div class="ar-viewer" ref="arViewer">
      <canvas ref="arCanvas"></canvas>
      
      <!-- AR Overlay UI -->
      <div class="ar-overlay">
        <!-- AR Controls -->
        <div class="ar-controls">
          <button @click="toggleAR" class="ar-btn" :class="{ active: isARActive }">
            <i :class="isARActive ? 'fas fa-stop' : 'fas fa-cube'"></i>
            {{ isARActive ? 'Parar AR' : 'Iniciar AR' }}
          </button>
          
          <button @click="toggleCamera" class="ar-btn" :disabled="!isARActive">
            <i class="fas fa-camera"></i>
            Câmera
          </button>
          
          <button @click="resetView" class="ar-btn" :disabled="!isARActive">
            <i class="fas fa-sync-alt"></i>
            Resetar
          </button>
        </div>
        
        <!-- AR Mode Selector -->
        <div class="ar-modes">
          <div v-for="mode in arModes" :key="mode.id"
               @click="selectARMode(mode)"
               class="ar-mode-card"
               :class="{ active: selectedMode?.id === mode.id }">
            <div class="mode-icon">
              <i :class="mode.icon"></i>
            </div>
            <h4>{{ mode.name }}</h4>
            <p>{{ mode.description }}</p>
          </div>
        </div>
        
        <!-- AR Info Panel -->
        <transition name="slide">
          <div v-if="selectedObject" class="ar-info-panel">
            <div class="panel-header">
              <h3>{{ selectedObject.name }}</h3>
              <button @click="selectedObject = null" class="close-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            
            <div class="panel-content">
              <div class="info-section">
                <h4>Descrição</h4>
                <p>{{ selectedObject.description }}</p>
              </div>
              
              <div class="info-section">
                <h4>Propriedades</h4>
                <ul class="properties-list">
                  <li v-for="(value, key) in selectedObject.properties" :key="key">
                    <span class="property-key">{{ key }}:</span>
                    <span class="property-value">{{ value }}</span>
                  </li>
                </ul>
              </div>
              
              <div class="info-actions">
                <button @click="rotateObject" class="action-btn">
                  <i class="fas fa-redo"></i>
                  Girar
                </button>
                <button @click="explodeObject" class="action-btn">
                  <i class="fas fa-expand"></i>
                  Explodir
                </button>
                <button @click="annotateObject" class="action-btn">
                  <i class="fas fa-sticky-note"></i>
                  Anotar
                </button>
              </div>
            </div>
          </div>
        </transition>
      </div>
      
      <!-- AR Markers -->
      <div class="ar-markers" v-if="isARActive">
        <div v-for="marker in activeMarkers" :key="marker.id"
             class="ar-marker"
             :style="{ 
               left: marker.x + 'px', 
               top: marker.y + 'px',
               transform: `translate(-50%, -50%) scale(${marker.scale})`
             }">
          <div class="marker-pin">
            <i :class="marker.icon"></i>
          </div>
          <div class="marker-label">{{ marker.label }}</div>
        </div>
      </div>
    </div>
    
    <!-- 3D Model Library -->
    <div class="model-library">
      <h3 class="library-title">
        <i class="fas fa-shapes"></i>
        Biblioteca de Modelos 3D
      </h3>
      
      <div class="model-categories">
        <button v-for="category in modelCategories" :key="category"
                @click="selectedCategory = category"
                class="category-btn"
                :class="{ active: selectedCategory === category }">
          {{ category }}
        </button>
      </div>
      
      <div class="models-grid">
        <div v-for="model in filteredModels" :key="model.id"
             @click="loadModel(model)"
             class="model-card"
             :class="{ loading: model.loading }">
          <div class="model-preview">
            <div class="preview-3d">
              <i :class="model.icon"></i>
            </div>
            <div v-if="model.loading" class="loading-overlay">
              <div class="spinner"></div>
            </div>
          </div>
          <h4>{{ model.name }}</h4>
          <p>{{ model.complexity }}</p>
        </div>
      </div>
    </div>
    
    <!-- Holographic Display -->
    <div class="holographic-display">
      <h3 class="display-title">
        <i class="fas fa-atom"></i>
        Display Holográfico
      </h3>
      
      <div class="hologram-container" ref="hologramContainer">
        <div class="hologram-stage">
          <div class="hologram-object" ref="hologramObject">
            <!-- Dynamic 3D CSS Object -->
            <div class="cube-3d" v-if="hologramType === 'cube'">
              <div class="face front">Front</div>
              <div class="face back">Back</div>
              <div class="face left">Left</div>
              <div class="face right">Right</div>
              <div class="face top">Top</div>
              <div class="face bottom">Bottom</div>
            </div>
            
            <!-- DNA Helix -->
            <div class="dna-helix" v-if="hologramType === 'dna'">
              <div v-for="i in 20" :key="i" 
                   class="dna-pair"
                   :style="{ transform: `translateZ(${i * 20}px) rotateY(${i * 36}deg)` }">
                <div class="nucleotide a"></div>
                <div class="nucleotide t"></div>
              </div>
            </div>
            
            <!-- Molecule Structure -->
            <div class="molecule" v-if="hologramType === 'molecule'">
              <div class="atom carbon" style="transform: translateZ(50px)">C</div>
              <div class="atom hydrogen" style="transform: translateX(50px) translateZ(25px)">H</div>
              <div class="atom hydrogen" style="transform: translateX(-50px) translateZ(25px)">H</div>
              <div class="atom hydrogen" style="transform: translateY(50px) translateZ(25px)">H</div>
              <div class="atom hydrogen" style="transform: translateY(-50px) translateZ(25px)">H</div>
              <div class="bond" style="transform: rotateY(45deg)"></div>
              <div class="bond" style="transform: rotateY(-45deg)"></div>
              <div class="bond" style="transform: rotateX(45deg)"></div>
              <div class="bond" style="transform: rotateX(-45deg)"></div>
            </div>
          </div>
          
          <!-- Hologram Effects -->
          <div class="hologram-effects">
            <div class="scan-line"></div>
            <div class="glitch-effect"></div>
            <div class="particle-field">
              <div v-for="i in 50" :key="i" class="particle"
                   :style="{ 
                     left: Math.random() * 100 + '%',
                     top: Math.random() * 100 + '%',
                     animationDelay: Math.random() * 5 + 's'
                   }">
              </div>
            </div>
          </div>
        </div>
        
        <!-- Hologram Controls -->
        <div class="hologram-controls">
          <button v-for="type in hologramTypes" :key="type"
                  @click="hologramType = type"
                  class="holo-btn"
                  :class="{ active: hologramType === type }">
            {{ type }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- AR Anatomy Explorer -->
    <div class="anatomy-explorer">
      <h3 class="explorer-title">
        <i class="fas fa-user"></i>
        Explorador Anatômico AR
      </h3>
      
      <div class="anatomy-systems">
        <div v-for="system in anatomySystems" :key="system.id"
             @click="loadAnatomySystem(system)"
             class="system-card"
             :class="{ active: selectedSystem?.id === system.id }">
          <div class="system-icon">
            <i :class="system.icon"></i>
          </div>
          <h4>{{ system.name }}</h4>
          <div class="system-layers">
            <button v-for="layer in system.layers" :key="layer"
                    @click.stop="toggleLayer(layer)"
                    class="layer-btn"
                    :class="{ active: activeLayers.includes(layer) }">
              {{ layer }}
            </button>
          </div>
        </div>
      </div>
      
      <!-- Anatomy Viewer -->
      <div class="anatomy-viewer" v-if="selectedSystem">
        <div class="viewer-3d">
          <div class="anatomy-model" ref="anatomyModel">
            <!-- Cardiovascular System -->
            <div v-if="selectedSystem.id === 'cardiovascular'" class="heart-3d">
              <div class="heart-chamber atrium-left" v-show="activeLayers.includes('Átrios')">
                Átrio Esquerdo
              </div>
              <div class="heart-chamber atrium-right" v-show="activeLayers.includes('Átrios')">
                Átrio Direito
              </div>
              <div class="heart-chamber ventricle-left" v-show="activeLayers.includes('Ventrículos')">
                Ventrículo Esquerdo
              </div>
              <div class="heart-chamber ventricle-right" v-show="activeLayers.includes('Ventrículos')">
                Ventrículo Direito
              </div>
              <div class="heart-vessel aorta" v-show="activeLayers.includes('Vasos')">
                Aorta
              </div>
              <div class="heart-valve mitral" v-show="activeLayers.includes('Válvulas')">
                Válvula Mitral
              </div>
            </div>
            
            <!-- Nervous System -->
            <div v-if="selectedSystem.id === 'nervous'" class="brain-3d">
              <div class="brain-lobe frontal" v-show="activeLayers.includes('Lobos')">
                Lobo Frontal
              </div>
              <div class="brain-lobe parietal" v-show="activeLayers.includes('Lobos')">
                Lobo Parietal
              </div>
              <div class="brain-structure hippocampus" v-show="activeLayers.includes('Estruturas')">
                Hipocampo
              </div>
              <div class="neural-pathway" v-show="activeLayers.includes('Vias')">
                Via Neural
              </div>
            </div>
          </div>
          
          <!-- Anatomy Labels -->
          <div class="anatomy-labels">
            <div v-for="label in anatomyLabels" :key="label.id"
                 class="anatomy-label"
                 :style="{ left: label.x + '%', top: label.y + '%' }">
              <div class="label-line"></div>
              <div class="label-text">{{ label.text }}</div>
            </div>
          </div>
        </div>
        
        <!-- Anatomy Info -->
        <div class="anatomy-info">
          <h4>{{ selectedSystem.name }}</h4>
          <p>{{ selectedSystem.description }}</p>
          
          <div class="info-tabs">
            <button v-for="tab in ['Estrutura', 'Função', 'Patologias', 'Interações']" 
                    :key="tab"
                    @click="activeTab = tab"
                    class="tab-btn"
                    :class="{ active: activeTab === tab }">
              {{ tab }}
            </button>
          </div>
          
          <div class="tab-content">
            <div v-if="activeTab === 'Estrutura'" class="structure-info">
              <ul>
                <li v-for="item in selectedSystem.structure" :key="item">
                  {{ item }}
                </li>
              </ul>
            </div>
            
            <div v-if="activeTab === 'Função'" class="function-info">
              <p>{{ selectedSystem.function }}</p>
            </div>
            
            <div v-if="activeTab === 'Patologias'" class="pathology-info">
              <div v-for="pathology in selectedSystem.pathologies" :key="pathology.name"
                   class="pathology-card">
                <h5>{{ pathology.name }}</h5>
                <p>{{ pathology.description }}</p>
              </div>
            </div>
            
            <div v-if="activeTab === 'Interações'" class="interaction-info">
              <div class="interaction-diagram">
                <div class="system-node central">{{ selectedSystem.name }}</div>
                <div v-for="interaction in selectedSystem.interactions" :key="interaction"
                     class="system-node connected">
                  {{ interaction }}
                  <div class="connection-line"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- AR Simulation Lab -->
    <div class="simulation-lab">
      <h3 class="lab-title">
        <i class="fas fa-flask"></i>
        Laboratório de Simulação AR
      </h3>
      
      <div class="simulations-grid">
        <div v-for="sim in simulations" :key="sim.id"
             class="simulation-card">
          <div class="sim-preview">
            <i :class="sim.icon"></i>
          </div>
          <h4>{{ sim.name }}</h4>
          <p>{{ sim.description }}</p>
          <button @click="startSimulation(sim)" class="start-sim-btn">
            <i class="fas fa-play"></i>
            Iniciar Simulação
          </button>
        </div>
      </div>
      
      <!-- Active Simulation -->
      <transition name="fade">
        <div v-if="activeSimulation" class="active-simulation">
          <div class="sim-header">
            <h3>{{ activeSimulation.name }}</h3>
            <button @click="stopSimulation" class="stop-btn">
              <i class="fas fa-stop"></i>
              Parar
            </button>
          </div>
          
          <div class="sim-viewport">
            <canvas ref="simCanvas"></canvas>
            
            <!-- Simulation Controls -->
            <div class="sim-controls">
              <div class="control-group">
                <label>Velocidade</label>
                <input type="range" v-model="simSpeed" min="0.1" max="3" step="0.1">
                <span>{{ simSpeed }}x</span>
              </div>
              
              <div class="control-group">
                <label>Temperatura</label>
                <input type="range" v-model="simTemp" min="0" max="100" step="1">
                <span>{{ simTemp }}°C</span>
              </div>
              
              <div class="control-group">
                <label>Pressão</label>
                <input type="range" v-model="simPressure" min="0" max="200" step="10">
                <span>{{ simPressure }} kPa</span>
              </div>
            </div>
            
            <!-- Simulation Data -->
            <div class="sim-data">
              <div class="data-card">
                <h5>Resultados</h5>
                <canvas ref="simChart"></canvas>
              </div>
              
              <div class="data-card">
                <h5>Parâmetros</h5>
                <ul>
                  <li v-for="(value, param) in simParameters" :key="param">
                    <span>{{ param }}:</span>
                    <strong>{{ value }}</strong>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
    
    <!-- AR Collaboration Space -->
    <div class="collaboration-space">
      <h3 class="space-title">
        <i class="fas fa-users"></i>
        Espaço Colaborativo AR
      </h3>
      
      <div class="collaboration-tools">
        <button @click="startSharing" class="collab-btn" :class="{ active: isSharing }">
          <i class="fas fa-share-alt"></i>
          {{ isSharing ? 'Compartilhando' : 'Compartilhar' }}
        </button>
        
        <button @click="toggleAnnotations" class="collab-btn" :class="{ active: showAnnotations }">
          <i class="fas fa-comment-dots"></i>
          Anotações
        </button>
        
        <button @click="togglePointer" class="collab-btn" :class="{ active: pointerActive }">
          <i class="fas fa-mouse-pointer"></i>
          Ponteiro
        </button>
      </div>
      
      <!-- Connected Users -->
      <div class="connected-users" v-if="connectedUsers.length > 0">
        <h4>Usuários Conectados</h4>
        <div class="users-list">
          <div v-for="user in connectedUsers" :key="user.id"
               class="user-avatar"
               :style="{ backgroundColor: user.color }">
            {{ user.initials }}
            <div class="user-status" :class="user.status"></div>
          </div>
        </div>
      </div>
      
      <!-- Shared Annotations -->
      <div class="shared-annotations" v-if="showAnnotations">
        <div v-for="annotation in sharedAnnotations" :key="annotation.id"
             class="annotation-bubble"
             :style="{ left: annotation.x + '%', top: annotation.y + '%' }">
          <div class="annotation-author" :style="{ color: annotation.authorColor }">
            {{ annotation.author }}
          </div>
          <div class="annotation-text">{{ annotation.text }}</div>
          <div class="annotation-time">{{ formatTime(annotation.timestamp) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useStore } from 'vuex';
import Chart from 'chart.js/auto';

export default {
  name: 'StudyAssistantAR',
  setup() {
    const store = useStore();
    
    // Refs
    const arViewer = ref(null);
    const arCanvas = ref(null);
    const hologramContainer = ref(null);
    const hologramObject = ref(null);
    const anatomyModel = ref(null);
    const simCanvas = ref(null);
    const simChart = ref(null);
    
    // AR State
    const isARActive = ref(false);
    const selectedMode = ref(null);
    const selectedObject = ref(null);
    const activeMarkers = ref([]);
    
    // AR Modes
    const arModes = ref([
      {
        id: 'anatomy',
        name: 'Anatomia 3D',
        icon: 'fas fa-user',
        description: 'Explore modelos anatômicos em realidade aumentada'
      },
      {
        id: 'molecules',
        name: 'Moléculas',
        icon: 'fas fa-atom',
        description: 'Visualize estruturas moleculares em 3D'
      },
      {
        id: 'physics',
        name: 'Física',
        icon: 'fas fa-magnet',
        description: 'Simule fenômenos físicos interativos'
      },
      {
        id: 'biology',
        name: 'Biologia',
        icon: 'fas fa-dna',
        description: 'Observe processos biológicos em tempo real'
      }
    ]);
    
    // 3D Models
    const modelCategories = ref(['Anatomia', 'Química', 'Física', 'Biologia', 'Matemática']);
    const selectedCategory = ref('Anatomia');
    
    const models = ref([
      {
        id: 1,
        name: 'Coração Humano',
        category: 'Anatomia',
        icon: 'fas fa-heart',
        complexity: 'Alta complexidade',
        loading: false
      },
      {
        id: 2,
        name: 'Cérebro',
        category: 'Anatomia',
        icon: 'fas fa-brain',
        complexity: 'Muito alta',
        loading: false
      },
      {
        id: 3,
        name: 'DNA Dupla Hélice',
        category: 'Biologia',
        icon: 'fas fa-dna',
        complexity: 'Média',
        loading: false
      },
      {
        id: 4,
        name: 'Molécula de Água',
        category: 'Química',
        icon: 'fas fa-tint',
        complexity: 'Baixa',
        loading: false
      },
      {
        id: 5,
        name: 'Átomo de Carbono',
        category: 'Química',
        icon: 'fas fa-atom',
        complexity: 'Baixa',
        loading: false
      },
      {
        id: 6,
        name: 'Sistema Solar',
        category: 'Física',
        icon: 'fas fa-globe',
        complexity: 'Alta',
        loading: false
      }
    ]);
    
    // Hologram
    const hologramType = ref('molecule');
    const hologramTypes = ref(['cube', 'dna', 'molecule']);
    
    // Anatomy Explorer
    const anatomySystems = ref([
      {
        id: 'cardiovascular',
        name: 'Sistema Cardiovascular',
        icon: 'fas fa-heartbeat',
        layers: ['Átrios', 'Ventrículos', 'Válvulas', 'Vasos'],
        structure: ['Coração', 'Artérias', 'Veias', 'Capilares'],
        function: 'Bombear sangue e nutrientes por todo o corpo',
        pathologies: [
          { name: 'Hipertensão', description: 'Pressão arterial elevada' },
          { name: 'Infarto', description: 'Bloqueio do fluxo sanguíneo' }
        ],
        interactions: ['Sistema Respiratório', 'Sistema Nervoso', 'Sistema Endócrino']
      },
      {
        id: 'nervous',
        name: 'Sistema Nervoso',
        icon: 'fas fa-brain',
        layers: ['Lobos', 'Estruturas', 'Vias', 'Nervos'],
        structure: ['Cérebro', 'Medula Espinhal', 'Nervos Periféricos'],
        function: 'Controlar e coordenar as funções do corpo',
        pathologies: [
          { name: 'Alzheimer', description: 'Degeneração neuronal progressiva' },
          { name: 'Parkinson', description: 'Distúrbio do movimento' }
        ],
        interactions: ['Sistema Endócrino', 'Sistema Muscular', 'Todos os sistemas']
      },
      {
        id: 'respiratory',
        name: 'Sistema Respiratório',
        icon: 'fas fa-lungs',
        layers: ['Vias Aéreas', 'Pulmões', 'Alvéolos', 'Diafragma'],
        structure: ['Nariz', 'Faringe', 'Laringe', 'Traqueia', 'Pulmões'],
        function: 'Realizar trocas gasosas e oxigenar o sangue',
        pathologies: [
          { name: 'Asma', description: 'Inflamação das vias aéreas' },
          { name: 'DPOC', description: 'Doença pulmonar obstrutiva' }
        ],
        interactions: ['Sistema Cardiovascular', 'Sistema Nervoso']
      }
    ]);
    
    const selectedSystem = ref(null);
    const activeLayers = ref([]);
    const anatomyLabels = ref([]);
    const activeTab = ref('Estrutura');
    
    // Simulations
    const simulations = ref([
      {
        id: 1,
        name: 'Circulação Sanguínea',
        icon: 'fas fa-heartbeat',
        description: 'Simule o fluxo sanguíneo pelo sistema cardiovascular'
      },
      {
        id: 2,
        name: 'Reação Química',
        icon: 'fas fa-flask',
        description: 'Observe reações químicas em tempo real'
      },
      {
        id: 3,
        name: 'Ondas Eletromagnéticas',
        icon: 'fas fa-wave-square',
        description: 'Visualize propagação de ondas'
      },
      {
        id: 4,
        name: 'Divisão Celular',
        icon: 'fas fa-microscope',
        description: 'Acompanhe o processo de mitose'
      }
    ]);
    
    const activeSimulation = ref(null);
    const simSpeed = ref(1);
    const simTemp = ref(37);
    const simPressure = ref(101);
    const simParameters = ref({});
    const chartInstance = ref(null);
    
    // Collaboration
    const isSharing = ref(false);
    const showAnnotations = ref(false);
    const pointerActive = ref(false);
    const connectedUsers = ref([]);
    const sharedAnnotations = ref([]);
    
    // Animation
    const animationFrame = ref(null);
    const rotationAngle = ref(0);
    
    // Computed
    const filteredModels = computed(() => {
      return models.value.filter(model => model.category === selectedCategory.value);
    });
    
    // Methods
    const toggleAR = async () => {
      if (isARActive.value) {
        stopAR();
      } else {
        await startAR();
      }
    };
    
    const startAR = async () => {
      try {
        // Check for WebXR support
        if ('xr' in navigator) {
          // Initialize WebXR session
          const session = await navigator.xr.requestSession('immersive-ar', {
            requiredFeatures: ['local-floor'],
            optionalFeatures: ['dom-overlay', 'hit-test']
          });
          
          console.log('WebXR AR session started');
        }
        
        // Fallback to camera-based AR
        const stream = await navigator.mediaDevices.getUserMedia({ 
          video: { facingMode: 'environment' } 
        });
        
        if (arCanvas.value) {
          const video = document.createElement('video');
          video.srcObject = stream;
          video.play();
          
          // Draw video to canvas
          const ctx = arCanvas.value.getContext('2d');
          const drawFrame = () => {
            if (isARActive.value) {
              ctx.drawImage(video, 0, 0, arCanvas.value.width, arCanvas.value.height);
              requestAnimationFrame(drawFrame);
            }
          };
          drawFrame();
        }
        
        isARActive.value = true;
        
        // Start AR tracking
        startARTracking();
        
      } catch (error) {
        console.error('Erro ao iniciar AR:', error);
        // Fallback to simulated AR
        isARActive.value = true;
        simulateAR();
      }
    };
    
    const stopAR = () => {
      isARActive.value = false;
      
      // Stop camera stream
      if (arCanvas.value) {
        const stream = arCanvas.value.captureStream();
        stream.getTracks().forEach(track => track.stop());
      }
    };
    
    const simulateAR = () => {
      // Simulate AR environment
      const canvas = arCanvas.value;
      if (!canvas) return;
      
      const ctx = canvas.getContext('2d');
      
      const animate = () => {
        if (!isARActive.value) return;
        
        // Clear canvas
        ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Draw AR grid
        ctx.strokeStyle = 'rgba(102, 126, 234, 0.3)';
        ctx.lineWidth = 1;
        
        for (let i = 0; i < canvas.width; i += 50) {
          ctx.beginPath();
          ctx.moveTo(i, 0);
          ctx.lineTo(i, canvas.height);
          ctx.stroke();
        }
        
        for (let i = 0; i < canvas.height; i += 50) {
          ctx.beginPath();
          ctx.moveTo(0, i);
          ctx.lineTo(canvas.width, i);
          ctx.stroke();
        }
        
        // Draw AR objects
        if (selectedMode.value) {
          drawARObjects(ctx);
        }
        
        animationFrame.value = requestAnimationFrame(animate);
      };
      
      animate();
    };
    
    const drawARObjects = (ctx) => {
      // Example: Draw a rotating 3D cube projection
      const centerX = arCanvas.value.width / 2;
      const centerY = arCanvas.value.height / 2;
      const size = 100;
      
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate(rotationAngle.value);
      
      // Draw cube faces
      ctx.strokeStyle = '#667eea';
      ctx.lineWidth = 2;
      
      // Front face
      ctx.strokeRect(-size/2, -size/2, size, size);
      
      // 3D effect
      ctx.beginPath();
      ctx.moveTo(-size/2, -size/2);
      ctx.lineTo(-size/2 + 30, -size/2 - 30);
      ctx.lineTo(size/2 + 30, -size/2 - 30);
      ctx.lineTo(size/2, -size/2);
      ctx.stroke();
      
      ctx.restore();
      
      rotationAngle.value += 0.01;
    };
    
    const startARTracking = () => {
      // Simulate AR marker tracking
      setInterval(() => {
        if (!isARActive.value) return;
        
        activeMarkers.value = [
          {
            id: 1,
            x: 200 + Math.sin(Date.now() / 1000) * 50,
            y: 200,
            scale: 1 + Math.sin(Date.now() / 500) * 0.1,
            icon: 'fas fa-map-marker-alt',
            label: 'Ponto A'
          },
          {
            id: 2,
            x: 400,
            y: 300 + Math.cos(Date.now() / 1000) * 50,
            scale: 1 + Math.cos(Date.now() / 500) * 0.1,
            icon: 'fas fa-info-circle',
            label: 'Informação'
          }
        ];
      }, 100);
    };
    
    const toggleCamera = () => {
      // Toggle between front and back camera
      console.log('Toggle camera');
    };
    
    const resetView = () => {
      rotationAngle.value = 0;
      selectedObject.value = null;
      activeMarkers.value = [];
    };
    
    const selectARMode = (mode) => {
      selectedMode.value = mode;
      
      // Load mode-specific content
      switch (mode.id) {
        case 'anatomy':
          loadAnatomyModels();
          break;
        case 'molecules':
          loadMoleculeModels();
          break;
        case 'physics':
          loadPhysicsSimulations();
          break;
        case 'biology':
          loadBiologyModels();
          break;
      }
    };
    
    const loadModel = async (model) => {
      model.loading = true;
      
      // Simulate loading 3D model
      setTimeout(() => {
        model.loading = false;
        
        selectedObject.value = {
          name: model.name,
          description: `Modelo 3D interativo de ${model.name}`,
          properties: {
            'Polígonos': '12,450',
            'Texturas': '4K',
            'Animações': '8',
            'Tamanho': '2.4 MB'
          }
        };
        
        // Add to AR scene
        if (isARActive.value) {
          addModelToARScene(model);
        }
      }, 1500);
    };
    
    const addModelToARScene = (model) => {
      console.log('Adding model to AR scene:', model.name);
      // Implementation for adding 3D model to AR scene
    };
    
    const rotateObject = () => {
      if (hologramObject.value) {
        hologramObject.value.style.animation = 'rotate-3d 2s ease-in-out';
        setTimeout(() => {
          hologramObject.value.style.animation = 'hologram-spin 20s linear infinite';
        }, 2000);
      }
    };
    
    const explodeObject = () => {
      console.log('Explode object animation');
      // Implement exploded view
    };
    
    const annotateObject = () => {
      console.log('Add annotation to object');
      // Implement annotation feature
    };
    
    const loadAnatomySystem = (system) => {
      selectedSystem.value = system;
      activeLayers.value = [...system.layers]; // Show all layers by default
      
      // Generate anatomy labels
      anatomyLabels.value = [
        { id: 1, text: system.layers[0], x: 30, y: 20 },
        { id: 2, text: system.layers[1], x: 70, y: 30 },
        { id: 3, text: system.layers[2], x: 50, y: 60 },
        { id: 4, text: system.layers[3], x: 40, y: 80 }
      ];
    };
    
    const toggleLayer = (layer) => {
      const index = activeLayers.value.indexOf(layer);
      if (index > -1) {
        activeLayers.value.splice(index, 1);
      } else {
        activeLayers.value.push(layer);
      }
    };
    
    const startSimulation = (sim) => {
      activeSimulation.value = sim;
      
      // Initialize simulation parameters
      simParameters.value = {
        'Velocidade': simSpeed.value + 'x',
        'Temperatura': simTemp.value + '°C',
        'Pressão': simPressure.value + ' kPa',
        'Tempo': '0s'
      };
      
      // Start simulation animation
      animateSimulation();
      
      // Initialize chart
      nextTick(() => {
        initSimulationChart();
      });
    };
    
    const stopSimulation = () => {
      activeSimulation.value = null;
      
      if (chartInstance.value) {
        chartInstance.value.destroy();
        chartInstance.value = null;
      }
    };
    
    const animateSimulation = () => {
      if (!activeSimulation.value || !simCanvas.value) return;
      
      const canvas = simCanvas.value;
      const ctx = canvas.getContext('2d');
      
      // Example: Blood flow simulation
      if (activeSimulation.value.id === 1) {
        const particles = [];
        
        // Create blood cells
        for (let i = 0; i < 50; i++) {
          particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * simSpeed.value,
            vy: Math.random() * simSpeed.value,
            radius: 5 + Math.random() * 5
          });
        }
        
        const draw = () => {
          if (!activeSimulation.value) return;
          
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          // Draw blood vessel
          ctx.strokeStyle = '#dc2626';
          ctx.lineWidth = 40;
          ctx.beginPath();
          ctx.moveTo(0, canvas.height / 2);
          ctx.quadraticCurveTo(
            canvas.width / 2, 
            canvas.height / 2 + Math.sin(Date.now() / 1000) * 50,
            canvas.width, 
            canvas.height / 2
          );
          ctx.stroke();
          
          // Draw particles
          particles.forEach(particle => {
            ctx.fillStyle = '#ef4444';
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
            ctx.fill();
            
            // Update position
            particle.x += particle.vx * simSpeed.value;
            particle.y += particle.vy;
            
            // Wrap around
            if (particle.x > canvas.width) particle.x = 0;
            if (particle.y > canvas.height) particle.y = 0;
            if (particle.y < 0) particle.y = canvas.height;
          });
          
          requestAnimationFrame(draw);
        };
        
        draw();
      }
    };
    
    const initSimulationChart = () => {
      if (!simChart.value) return;
      
      const ctx = simChart.value.getContext('2d');
      
      chartInstance.value = new Chart(ctx, {
        type: 'line',
        data: {
          labels: Array(20).fill('').map((_, i) => i + 's'),
          datasets: [{
            label: 'Fluxo',
            data: Array(20).fill(0).map(() => Math.random() * 100),
            borderColor: '#667eea',
            tension: 0.4
          }, {
            label: 'Pressão',
            data: Array(20).fill(0).map(() => Math.random() * 100),
            borderColor: '#ef4444',
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              labels: {
                color: '#e5e7eb'
              }
            }
          },
          scales: {
            x: {
              ticks: { color: '#9ca3af' },
              grid: { color: 'rgba(255, 255, 255, 0.1)' }
            },
            y: {
              ticks: { color: '#9ca3af' },
              grid: { color: 'rgba(255, 255, 255, 0.1)' }
            }
          }
        }
      });
      
      // Update chart data periodically
      setInterval(() => {
        if (chartInstance.value && activeSimulation.value) {
          chartInstance.value.data.datasets[0].data.shift();
          chartInstance.value.data.datasets[0].data.push(Math.random() * 100);
          chartInstance.value.data.datasets[1].data.shift();
          chartInstance.value.data.datasets[1].data.push(Math.random() * 100);
          chartInstance.value.update('none');
        }
      }, 1000);
    };
    
    const startSharing = () => {
      isSharing.value = !isSharing.value;
      
      if (isSharing.value) {
        // Simulate connected users
        connectedUsers.value = [
          { id: 1, initials: 'JD', color: '#667eea', status: 'active' },
          { id: 2, initials: 'MS', color: '#10b981', status: 'active' },
          { id: 3, initials: 'AL', color: '#f59e0b', status: 'idle' }
        ];
      } else {
        connectedUsers.value = [];
      }
    };
    
    const toggleAnnotations = () => {
      showAnnotations.value = !showAnnotations.value;
      
      if (showAnnotations.value) {
        // Load sample annotations
        sharedAnnotations.value = [
          {
            id: 1,
            author: 'João',
            authorColor: '#667eea',
            text: 'Observe a estrutura do ventrículo esquerdo',
            x: 30,
            y: 40,
            timestamp: Date.now() - 300000
          },
          {
            id: 2,
            author: 'Maria',
            authorColor: '#10b981',
            text: 'A válvula mitral está aqui',
            x: 60,
            y: 55,
            timestamp: Date.now() - 120000
          }
        ];
      }
    };
    
    const togglePointer = () => {
      pointerActive.value = !pointerActive.value;
    };
    
    const formatTime = (timestamp) => {
      const diff = Date.now() - timestamp;
      const minutes = Math.floor(diff / 60000);
      
      if (minutes < 1) return 'Agora';
      if (minutes < 60) return `${minutes} min atrás`;
      
      const hours = Math.floor(minutes / 60);
      return `${hours} h atrás`;
    };
    
    // Helper methods
    const loadAnatomyModels = () => {
      console.log('Loading anatomy models');
    };
    
    const loadMoleculeModels = () => {
      console.log('Loading molecule models');
    };
    
    const loadPhysicsSimulations = () => {
      console.log('Loading physics simulations');
    };
    
    const loadBiologyModels = () => {
      console.log('Loading biology models');
    };
    
    // Lifecycle
    onMounted(() => {
      // Initialize hologram rotation
      if (hologramObject.value) {
        hologramObject.value.style.animation = 'hologram-spin 20s linear infinite';
      }
      
      // Set canvas sizes
      if (arCanvas.value) {
        arCanvas.value.width = arViewer.value?.clientWidth || 800;
        arCanvas.value.height = arViewer.value?.clientHeight || 600;
      }
      
      if (simCanvas.value) {
        simCanvas.value.width = 600;
        simCanvas.value.height = 400;
      }
    });
    
    onUnmounted(() => {
      if (animationFrame.value) {
        cancelAnimationFrame(animationFrame.value);
      }
      
      if (chartInstance.value) {
        chartInstance.value.destroy();
      }
      
      stopAR();
    });
    
    // Watchers
    watch([simSpeed, simTemp, simPressure], () => {
      if (activeSimulation.value) {
        simParameters.value = {
          'Velocidade': simSpeed.value + 'x',
          'Temperatura': simTemp.value + '°C',
          'Pressão': simPressure.value + ' kPa',
          'Tempo': Math.floor(Date.now() / 1000) + 's'
        };
      }
    });
    
    return {
      // Refs
      arViewer,
      arCanvas,
      hologramContainer,
      hologramObject,
      anatomyModel,
      simCanvas,
      simChart,
      
      // State
      isARActive,
      selectedMode,
      selectedObject,
      activeMarkers,
      arModes,
      modelCategories,
      selectedCategory,
      models,
      filteredModels,
      hologramType,
      hologramTypes,
      anatomySystems,
      selectedSystem,
      activeLayers,
      anatomyLabels,
      activeTab,
      simulations,
      activeSimulation,
      simSpeed,
      simTemp,
      simPressure,
      simParameters,
      isSharing,
      showAnnotations,
      pointerActive,
      connectedUsers,
      sharedAnnotations,
      
      // Methods
      toggleAR,
      toggleCamera,
      resetView,
      selectARMode,
      loadModel,
      rotateObject,
      explodeObject,
      annotateObject,
      loadAnatomySystem,
      toggleLayer,
      startSimulation,
      stopSimulation,
      startSharing,
      toggleAnnotations,
      togglePointer,
      formatTime
    };
  }
};
</script>

<style scoped>
.ar-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f1e 0%, #1a1a2e 100%);
  color: #fff;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

/* AR Viewer */
.ar-viewer {
  grid-column: span 2;
  height: 600px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.ar-viewer canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.ar-overlay {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.ar-overlay > * {
  pointer-events: auto;
}

/* AR Controls */
.ar-controls {
  position: absolute;
  top: 2rem;
  left: 2rem;
  display: flex;
  gap: 1rem;
}

.ar-btn {
  padding: 0.8rem 1.5rem;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #fff;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ar-btn:hover:not(:disabled) {
  background: rgba(102, 126, 234, 0.3);
  border-color: #667eea;
  transform: translateY(-2px);
}

.ar-btn.active {
  background: rgba(102, 126, 234, 0.5);
  border-color: #667eea;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.ar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* AR Modes */
.ar-modes {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  background: rgba(0, 0, 0, 0.6);
  padding: 1rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.ar-mode-card {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  min-width: 120px;
}

.ar-mode-card:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.ar-mode-card.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.mode-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #667eea;
}

.ar-mode-card h4 {
  font-size: 1rem;
  margin-bottom: 0.3rem;
}

.ar-mode-card p {
  font-size: 0.8rem;
  color: #9ca3af;
  line-height: 1.3;
}

/* AR Info Panel */
.ar-info-panel {
  position: absolute;
  right: 2rem;
  top: 2rem;
  width: 300px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h3 {
  font-size: 1.2rem;
}

.close-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: #fff;
}

.panel-content {
  padding: 1.5rem;
}

.info-section {
  margin-bottom: 1.5rem;
}

.info-section h4 {
  color: #9ca3af;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.info-section p {
  color: #e5e7eb;
  line-height: 1.5;
}

.properties-list {
  list-style: none;
  padding: 0;
}

.properties-list li {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.property-key {
  color: #9ca3af;
}

.property-value {
  color: #60a5fa;
  font-weight: 600;
}

.info-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  flex: 1;
  padding: 0.6rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  color: #fff;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
}

.action-btn:hover {
  background: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

/* AR Markers */
.ar-markers {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.ar-marker {
  position: absolute;
  transform-origin: center;
  animation: marker-pulse 2s infinite;
}

@keyframes marker-pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.marker-pin {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.marker-pin i {
  transform: rotate(45deg);
  color: #fff;
}

.marker-label {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  white-space: nowrap;
  backdrop-filter: blur(10px);
}

/* Model Library */
.model-library {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.library-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.model-categories {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.category-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: #e5e7eb;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.category-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.model-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
}

.model-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  border-color: rgba(102, 126, 234, 0.3);
}

.model-preview {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.8rem;
  position: relative;
}

.preview-3d {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: perspective(100px) rotateX(15deg) rotateY(-15deg);
  transition: transform 0.3s ease;
}

.model-card:hover .preview-3d {
  transform: perspective(100px) rotateX(0) rotateY(0) scale(1.1);
}

.preview-3d i {
  font-size: 2.5rem;
  color: #667eea;
}

.loading-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top-color: #667eea;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.model-card h4 {
  font-size: 1rem;
  margin-bottom: 0.3rem;
  color: #e5e7eb;
}

.model-card p {
  font-size: 0.8rem;
  color: #9ca3af;
}

/* Holographic Display */
.holographic-display {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.display-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: #e5e7eb;
}

.hologram-container {
  height: 400px;
  background: radial-gradient(ellipse at center, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  border-radius: 15px;
  position: relative;
  overflow: hidden;
}

.hologram-stage {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
}

.hologram-object {
  transform-style: preserve-3d;
  animation: hologram-spin 20s linear infinite;
}

@keyframes hologram-spin {
  from { transform: rotateY(0deg); }
  to { transform: rotateY(360deg); }
}

@keyframes rotate-3d {
  0% { transform: rotateX(0) rotateY(0); }
  100% { transform: rotateX(360deg) rotateY(360deg); }
}

/* 3D Cube */
.cube-3d {
  width: 150px;
  height: 150px;
  transform-style: preserve-3d;
}

.face {
  position: absolute;
  width: 150px;
  height: 150px;
  background: rgba(102, 126, 234, 0.2);
  border: 2px solid #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #667eea;
  backdrop-filter: blur(5px);
}

.face.front { transform: translateZ(75px); }
.face.back { transform: rotateY(180deg) translateZ(75px); }
.face.left { transform: rotateY(-90deg) translateZ(75px); }
.face.right { transform: rotateY(90deg) translateZ(75px); }
.face.top { transform: rotateX(90deg) translateZ(75px); }
.face.bottom { transform: rotateX(-90deg) translateZ(75px); }

/* DNA Helix */
.dna-helix {
  width: 200px;
  height: 300px;
  transform-style: preserve-3d;
  position: relative;
}

.dna-pair {
  position: absolute;
  width: 100%;
  height: 10px;
  transform-style: preserve-3d;
}

.nucleotide {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  top: -15px;
}

.nucleotide.a {
  left: 0;
  background: radial-gradient(circle, #667eea, #764ba2);
  box-shadow: 0 0 20px #667eea;
}

.nucleotide.t {
  right: 0;
  background: radial-gradient(circle, #10b981, #059669);
  box-shadow: 0 0 20px #10b981;
}

/* Molecule */
.molecule {
  width: 200px;
  height: 200px;
  transform-style: preserve-3d;
  position: relative;
}

.atom {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #fff;
  left: 50%;
  top: 50%;
  margin: -25px 0 0 -25px;
}

.atom.carbon {
  background: radial-gradient(circle, #374151, #1f2937);
  box-shadow: 0 0 30px rgba(55, 65, 81, 0.5);
  width: 60px;
  height: 60px;
  margin: -30px 0 0 -30px;
}

.atom.hydrogen {
  background: radial-gradient(circle, #ddd6fe, #a78bfa);
  box-shadow: 0 0 20px rgba(167, 139, 250, 0.5);
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
}

.bond {
  position: absolute;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, transparent, #667eea, transparent);
  left: 50%;
  top: 50%;
  margin: -2px 0 0 -40px;
  transform-origin: center;
}

/* Hologram Effects */
.hologram-effects {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.scan-line {
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #667eea, transparent);
  animation: scan 3s linear infinite;
}

@keyframes scan {
  0% { top: 0; }
  100% { top: 100%; }
}

.glitch-effect {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    transparent 50%,
    rgba(102, 126, 234, 0.03) 50%
  );
  background-size: 100% 4px;
  animation: glitch 0.5s infinite;
  opacity: 0.1;
}

@keyframes glitch {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(2px); }
}

.particle-field {
  position: absolute;
  inset: 0;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #667eea;
  border-radius: 50%;
  animation: float-particle 5s infinite;
}

@keyframes float-particle {
  0%, 100% { 
    transform: translateY(0) scale(1);
    opacity: 0;
  }
  50% {
    transform: translateY(-50px) scale(1.5);
    opacity: 1;
  }
}

.hologram-controls {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
}

.holo-btn {
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  color: #e5e7eb;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.holo-btn:hover {
  background: rgba(102, 126, 234, 0.3);
  border-color: #667eea;
}

.holo-btn.active {
  background: rgba(102, 126, 234, 0.5);
  border-color: #667eea;
}

/* Anatomy Explorer */
.anatomy-explorer {
  grid-column: span 2;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.explorer-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: #e5e7eb;
}

.anatomy-systems {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.system-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.system-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.system-card.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
}

.system-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.system-icon i {
  font-size: 1.8rem;
  color: #fff;
}

.system-card h4 {
  font-size: 1.1rem;
  margin-bottom: 0.8rem;
  color: #e5e7eb;
}

.system-layers {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.layer-btn {
  padding: 0.3rem 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  color: #9ca3af;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.layer-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #e5e7eb;
}

.layer-btn.active {
  background: rgba(102, 126, 234, 0.3);
  border-color: #667eea;
  color: #fff;
}

/* Anatomy Viewer */
.anatomy-viewer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.viewer-3d {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  padding: 2rem;
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.anatomy-model {
  transform-style: preserve-3d;
  animation: anatomy-rotate 30s linear infinite;
}

@keyframes anatomy-rotate {
  from { transform: rotateY(0deg); }
  to { transform: rotateY(360deg); }
}

/* Heart 3D Model */
.heart-3d {
  width: 200px;
  height: 250px;
  position: relative;
  transform-style: preserve-3d;
}

.heart-chamber {
  position: absolute;
  background: rgba(220, 38, 38, 0.3);
  border: 2px solid #dc2626;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 0.8rem;
  text-align: center;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.heart-chamber:hover {
  background: rgba(220, 38, 38, 0.5);
  transform: scale(1.1);
}

.atrium-left {
  width: 80px;
  height: 80px;
  top: 0;
  left: 20px;
  transform: translateZ(30px);
}

.atrium-right {
  width: 80px;
  height: 80px;
  top: 0;
  right: 20px;
  transform: translateZ(30px);
}

.ventricle-left {
  width: 100px;
  height: 120px;
  bottom: 0;
  left: 10px;
  transform: translateZ(40px);
}

.ventricle-right {
  width: 100px;
  height: 120px;
  bottom: 0;
  right: 10px;
  transform: translateZ(40px);
}

.heart-vessel {
  position: absolute;
  background: rgba(239, 68, 68, 0.3);
  border: 2px solid #ef4444;
  border-radius: 50px;
}

.aorta {
  width: 40px;
  height: 100px;
  top: -20px;
  left: 50%;
  transform: translateX(-50%) translateZ(50px);
}

.heart-valve {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(251, 191, 36, 0.3);
  border: 2px solid #fbbf24;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fbbf24;
  font-size: 0.7rem;
}

.mitral {
  top: 80px;
  left: 50%;
  transform: translateX(-50%) translateZ(60px);
}

/* Brain 3D Model */
.brain-3d {
  width: 250px;
  height: 200px;
  position: relative;
  transform-style: preserve-3d;
}

.brain-lobe {
  position: absolute;
  background: rgba(168, 85, 247, 0.3);
  border: 2px solid #a855f7;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 0.9rem;
  backdrop-filter: blur(5px);
}

.frontal {
  width: 120px;
  height: 100px;
  top: 0;
  left: 50%;
  transform: translateX(-50%) translateZ(40px);
}

.parietal {
  width: 100px;
  height: 80px;
  top: 50px;
  left: 50%;
  transform: translateX(-50%) translateZ(30px);
}

.brain-structure {
  position: absolute;
  background: rgba(236, 72, 153, 0.3);
  border: 2px solid #ec4899;
  border-radius: 20px;
  padding: 0.5rem;
  color: #fff;
  font-size: 0.8rem;
}

.hippocampus {
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%) translateZ(50px);
}

.neural-pathway {
  position: absolute;
  width: 150px;
  height: 3px;
  background: linear-gradient(90deg, #a855f7, #ec4899);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateZ(60px);
  box-shadow: 0 0 10px rgba(168, 85, 247, 0.5);
}

/* Anatomy Labels */
.anatomy-labels {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.anatomy-label {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.label-line {
  width: 50px;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  position: relative;
}

.label-line::before {
  content: '';
  position: absolute;
  left: 0;
  top: -3px;
  width: 8px;
  height: 8px;
  background: #667eea;
  border-radius: 50%;
}

.label-text {
  background: rgba(0, 0, 0, 0.8);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.85rem;
  white-space: nowrap;
  color: #e5e7eb;
}

/* Anatomy Info */
.anatomy-info {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 1.5rem;
}

.anatomy-info h4 {
  font-size: 1.3rem;
  margin-bottom: 0.8rem;
  color: #e5e7eb;
}

.anatomy-info > p {
  color: #9ca3af;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.info-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
  padding: 0.8rem 1.2rem;
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-btn:hover {
  color: #e5e7eb;
}

.tab-btn.active {
  color: #667eea;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #667eea;
}

.tab-content {
  min-height: 200px;
}

.structure-info ul {
  list-style: none;
  padding: 0;
}

.structure-info li {
  padding: 0.5rem 0;
  color: #e5e7eb;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.structure-info li::before {
  content: '→';
  color: #667eea;
  margin-right: 0.5rem;
}

.function-info p {
  color: #e5e7eb;
  line-height: 1.6;
}

.pathology-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.pathology-card h5 {
  color: #ef4444;
  margin-bottom: 0.5rem;
}

.pathology-card p {
  color: #9ca3af;
  font-size: 0.9rem;
  line-height: 1.4;
}

.interaction-diagram {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  padding: 2rem;
  position: relative;
}

.system-node {
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  font-size: 0.9rem;
  position: relative;
}

.system-node.central {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  font-weight: 600;
}

.connection-line {
  position: absolute;
  width: 100px;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  left: -100px;
  top: 50%;
  transform: translateY(-50%);
}

/* Simulation Lab */
.simulation-lab {
  grid-column: span 2;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.lab-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: #e5e7eb;
}

.simulations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.simulation-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.simulation-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.sim-preview {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(251, 191, 36, 0.2));
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.sim-preview i {
  font-size: 2.5rem;
  color: #ef4444;
}

.simulation-card h4 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: #e5e7eb;
}

.simulation-card p {
  color: #9ca3af;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
}

.start-sim-btn {
  width: 100%;
  padding: 0.8rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  border-radius: 10px;
  color: #fff;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.start-sim-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(239, 68, 68, 0.3);
}

/* Active Simulation */
.active-simulation {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  padding: 2rem;
  margin-top: 2rem;
}

.sim-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sim-header h3 {
  font-size: 1.3rem;
  color: #e5e7eb;
}

.stop-btn {
  padding: 0.8rem 1.5rem;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid #ef4444;
  border-radius: 10px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stop-btn:hover {
  background: rgba(239, 68, 68, 0.3);
  transform: translateY(-1px);
}

.sim-viewport canvas {
  width: 100%;
  height: 400px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  margin-bottom: 1.5rem;
}

.sim-controls {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-group label {
  color: #9ca3af;
  font-size: 0.9rem;
}

.control-group input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #ef4444;
  border-radius: 50%;
  cursor: pointer;
}

.control-group span {
  color: #ef4444;
  font-weight: 600;
}

.sim-data {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

.data-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  padding: 1.5rem;
}

.data-card h5 {
  color: #e5e7eb;
  margin-bottom: 1rem;
}

.data-card canvas {
  width: 100%;
  height: 200px;
}

.data-card ul {
  list-style: none;
  padding: 0;
}

.data-card li {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  color: #9ca3af;
}

.data-card li strong {
  color: #60a5fa;
}

/* Collaboration Space */
.collaboration-space {
  grid-column: span 2;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.space-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: #e5e7eb;
}

.collaboration-tools {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.collab-btn {
  padding: 0.8rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #e5e7eb;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.collab-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.collab-btn.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

.connected-users {
  margin-bottom: 2rem;
}

.connected-users h4 {
  color: #e5e7eb;
  margin-bottom: 1rem;
}

.users-list {
  display: flex;
  gap: 1rem;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 600;
  position: relative;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.user-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid #1a1a2e;
}

.user-status.active {
  background: #10b981;
}

.user-status.idle {
  background: #fbbf24;
}

.shared-annotations {
  position: relative;
  height: 300px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  overflow: hidden;
}

.annotation-bubble {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 1rem;
  max-width: 200px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.annotation-author {
  font-weight: 600;
  margin-bottom: 0.3rem;
}

.annotation-text {
  color: #e5e7eb;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.3rem;
}

.annotation-time {
  color: #9ca3af;
  font-size: 0.75rem;
}

/* Transitions */
.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .ar-container {
    grid-template-columns: 1fr;
  }
  
  .ar-viewer,
  .anatomy-explorer,
  .simulation-lab,
  .collaboration-space {
    grid-column: span 1;
  }
  
  .anatomy-viewer {
    grid-template-columns: 1fr;
  }
  
  .ar-modes {
    flex-wrap: wrap;
  }
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}
</style>