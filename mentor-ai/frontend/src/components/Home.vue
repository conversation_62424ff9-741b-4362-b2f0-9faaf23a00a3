<template>
  <div class="home-container">
    <header class="hero-creative">
      <div class="hero-wrapper">
        <div class="hero-background">
          <div class="grid-pattern"></div>
          <div class="floating-orbs">
            <span class="orb orb-1"></span>
            <span class="orb orb-2"></span>
            <span class="orb orb-3"></span>
          </div>
        </div>
        
        <div class="hero-content-creative">
          <div class="welcome-section">
            <div class="welcome-avatar">
              <div class="avatar-glow"></div>
              <div class="avatar-core">
                <span>{{ userInitials }}</span>
              </div>
              <div class="avatar-rings">
                <span class="ring ring-1"></span>
                <span class="ring ring-2"></span>
              </div>
            </div>
            
            <div class="welcome-text-creative">
              <h1 class="welcome-title">
                <span class="greeting">Be<PERSON>-vindo(a),</span>
                <span class="user-name-creative">{{ userName }}!</span>
              </h1>
              <p class="subtitle-creative">
                <span class="subtitle-icon"><font-awesome-icon icon="fa-brain" /></span>
                Seu espaço de estudo personalizado com IA.
              </p>
              <div class="last-login-creative" v-if="lastLogin">
                <span class="login-icon"><font-awesome-icon icon="fa-clock" /></span>
                <span>Último acesso: {{ formatDate(lastLogin) }}</span>
              </div>
            </div>
          </div>
          
          <div class="metrics-creative" v-if="user">
            <div class="metric-card">
              <div class="metric-icon">
                <font-awesome-icon icon="fa-calendar-days" />
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ completedDays }}</div>
                <div class="metric-label">Dias de estudo</div>
              </div>
              <div class="metric-glow"></div>
            </div>
            
            <div class="metric-card streak">
              <div class="metric-icon">
                <font-awesome-icon icon="fa-fire" />
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ streakDays }}</div>
                <div class="metric-label">Sequência atual</div>
              </div>
              <div class="metric-glow"></div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- NOVA SEÇÃO DE CONQUISTAS (Gamification) -->
    <section class="achievements-creative">
      <div class="section-header-creative">
        <div class="header-icon-wrapper">
          <font-awesome-icon icon="fa-solid fa-medal" class="header-icon" />
          <div class="icon-glow"></div>
        </div>
        <div class="header-text">
          <h2 class="section-title-creative">Conquistas</h2>
          <p class="section-subtitle-creative">Vá desbloqueando conquistas à medida que avança nos estudos!</p>
        </div>
      </div>
      
      <div class="achievements-grid-creative">
        <div 
          class="achievement-card-creative" 
          v-for="(achievement, index) in achievements" 
          :key="index"
          :class="{ 'completed': achievement.progress === 100 }"
        >
          <div class="achievement-backdrop">
            <div class="achievement-orb"></div>
          </div>
          
          <div class="achievement-icon-creative">
            <div class="icon-container">
              <font-awesome-icon :icon="achievement.icon" />
              <div class="icon-ring" v-if="achievement.progress === 100"></div>
            </div>
          </div>
          
          <div class="achievement-content-creative">
            <h4 class="achievement-title">{{ achievement.title }}</h4>
            <p class="achievement-description">{{ achievement.description }}</p>
            
            <div class="achievement-progress-creative">
              <div class="progress-track">
                <div 
                  class="progress-fill-creative" 
                  :style="{ width: achievement.progress + '%' }"
                >
                  <div class="progress-glow" v-if="achievement.progress > 0"></div>
                </div>
              </div>
              <div class="progress-label-creative">
                <span class="progress-value">{{ achievement.progress }}%</span>
                <span class="progress-text">completo</span>
              </div>
            </div>
          </div>
          
          <div class="achievement-badge" v-if="achievement.progress === 100">
            <font-awesome-icon icon="fa-check" />
          </div>
        </div>
      </div>
    </section>

    <section class="progress-overview-creative">
      <div class="progress-header-creative">
        <div class="progress-icon-wrapper">
          <font-awesome-icon icon="fa-solid fa-chart-line" class="progress-header-icon" />
          <div class="progress-icon-glow"></div>
        </div>
        <div class="progress-header-text">
          <h2 class="progress-title-creative">Visão Geral do Progresso</h2>
        </div>
      </div>
      
      <div class="progress-stats-creative">
        <div class="stat-card-creative" v-for="(stat, index) in progressStats" :key="index">
          <div class="stat-icon-wrapper">
            <font-awesome-icon :icon="stat.icon" />
          </div>
          <div class="stat-content">
            <div class="stat-value-wrapper">
              <span class="stat-value">{{ stat.value }}</span>
              <span class="stat-suffix" v-if="stat.suffix">{{ stat.suffix }}</span>
            </div>
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-description">{{ stat.description }}</div>
          </div>
          <div class="stat-trend" :class="stat.trendType">
            <font-awesome-icon :icon="stat.trendType === 'positive' ? 'fa-arrow-up' : 'fa-arrow-down'" />
            <span>{{ stat.trend }}</span>
          </div>
          <div class="stat-decoration"></div>
        </div>
      </div>
      
      <div class="weekly-detail-creative">
        <div class="weekly-chart-creative">
          <h3 class="chart-title-creative">Progresso Semanal</h3>
          <div class="chart-wrapper">
            <div class="chart-grid">
              <div class="grid-line" v-for="i in 5" :key="i"></div>
            </div>
            <div class="chart-bars-creative">
              <div 
                v-for="(day, index) in weeklyProgress" 
                :key="index"
                class="day-bar-creative" 
                :class="{ 'today': day.isToday }"
              >
                <div class="bar-container">
                  <div 
                    class="bar-fill-creative" 
                    :style="{ height: day.percentage + '%' }"
                  >
                    <div class="bar-glow" v-if="day.isToday"></div>
                  </div>
                  <div class="bar-particle" v-if="day.percentage > 0"></div>
                </div>
                <div class="bar-label-creative">{{ day.name }}</div>
                <div class="bar-value-creative">{{ day.hours }}h</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="stats-detail-creative">
          <div class="detail-card">
            <div class="detail-icon">
              <font-awesome-icon icon="fa-brain" />
            </div>
            <div class="detail-info">
              <div class="detail-label">Taxa de Retenção</div>
              <div class="detail-value-creative">87%</div>
            </div>
            <div class="detail-indicator retention"></div>
          </div>
          
          <div class="detail-card">
            <div class="detail-icon">
              <font-awesome-icon icon="fa-bullseye" />
            </div>
            <div class="detail-info">
              <div class="detail-label">Foco Médio</div>
              <div class="detail-value-creative">42 min</div>
            </div>
            <div class="detail-indicator focus"></div>
          </div>
          
          <div class="detail-card">
            <div class="detail-icon">
              <font-awesome-icon icon="fa-rocket" />
            </div>
            <div class="detail-info">
              <div class="detail-label">Produtividade</div>
              <div class="detail-value-creative">Alta</div>
            </div>
            <div class="detail-indicator productivity"></div>
          </div>
        </div>
      </div>
    </section>


    <section class="performance-analysis-creative">
      <div class="analysis-header-creative">
        <div class="analysis-icon-wrapper">
          <font-awesome-icon icon="fa-solid fa-chart-pie" class="analysis-header-icon" />
          <div class="analysis-icon-glow"></div>
        </div>
        <div class="analysis-header-text">
          <h2 class="analysis-title-creative">Análise de Desempenho</h2>
        </div>
      </div>
      
      <div class="analysis-grid-creative">
        <div class="performance-card-creative">
          <div class="card-header-creative">
            <h3 class="card-title">Desempenho por Área</h3>
            <div class="card-icon">
              <font-awesome-icon icon="fa-graduation-cap" />
            </div>
          </div>
          
          <div class="performance-table-creative">
            <div class="table-header">
              <span class="header-cell">Disciplina</span>
              <span class="header-cell">Acertos</span>
              <span class="header-cell">Progresso</span>
            </div>
            
            <div class="table-body">
              <div 
                v-for="(area, index) in performanceByArea" 
                :key="index" 
                class="table-row-creative"
                :class="{ 'high-performance': area.score >= 85, 'medium-performance': area.score >= 70 && area.score < 85 }"
              >
                <div class="row-cell discipline-name">
                  <div class="discipline-icon">
                    <font-awesome-icon :icon="getAreaIcon(area.name)" />
                  </div>
                  <span>{{ area.name }}</span>
                </div>
                
                <div class="row-cell score-value">
                  <span class="score-number">{{ area.score }}</span>
                  <span class="score-suffix">%</span>
                </div>
                
                <div class="row-cell progress-cell">
                  <div class="progress-bar-creative">
                    <div 
                      class="progress-fill-bar" 
                      :style="{ width: area.score + '%' }"
                    >
                      <div class="progress-glow-bar"></div>
                    </div>
                    <div class="progress-particle" v-if="area.score >= 85"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="distribution-card-creative">
          <div class="card-header-creative">
            <h3 class="card-title">Distribuição de Estudos</h3>
            <div class="card-icon">
              <font-awesome-icon icon="fa-clock" />
            </div>
          </div>
          
          <div class="distribution-chart-creative">
            <div class="chart-center">
              <div class="center-value">{{ totalStudyHours }}h</div>
              <div class="center-label">Total</div>
            </div>
            
            <div class="donut-segments">
              <svg class="donut-svg" viewBox="0 0 200 200">
                <defs>
                  <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#42b983;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#35a372;stop-opacity:1" />
                  </linearGradient>
                  <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
                  </linearGradient>
                  <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
                  </linearGradient>
                  <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#ec4899;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#db2777;stop-opacity:1" />
                  </linearGradient>
                  <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
                  </linearGradient>
                </defs>
                
                <circle 
                  v-for="(segment, index) in donutSegments" 
                  :key="index"
                  cx="100" 
                  cy="100" 
                  r="80" 
                  fill="none" 
                  :stroke="`url(#gradient${index + 1})`" 
                  stroke-width="20"
                  :stroke-dasharray="`${segment.length} ${segment.offset}`"
                  :transform="`rotate(${segment.rotation} 100 100)`"
                  class="donut-segment"
                />
              </svg>
            </div>
            
            <div class="distribution-legend">
              <div 
                v-for="(item, index) in studyDistribution" 
                :key="index" 
                class="legend-item-creative"
              >
                <div class="legend-color" :style="{ background: item.color }"></div>
                <div class="legend-info">
                  <div class="legend-name">{{ item.category }}</div>
                  <div class="legend-value">{{ item.hours }}h ({{ item.percentage }}%)</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="activities-timeline-creative">
      <div class="timeline-header-creative">
        <div class="timeline-icon-wrapper">
          <font-awesome-icon icon="fa-solid fa-clock-rotate-left" class="timeline-header-icon" />
          <div class="timeline-icon-glow"></div>
        </div>
        <div class="timeline-header-text">
          <h2 class="timeline-title-creative">Atividades Recentes</h2>
        </div>
      </div>
      
      <div class="timeline-creative">
        <div class="timeline-line"></div>
        <div 
          v-for="(activity, index) in recentActivities" 
          :key="index" 
          class="timeline-item-creative"
          :class="{ 'item-highlighted': index === 0 }"
        >
          <div class="timeline-connector">
            <div class="connector-dot">
              <div class="dot-pulse" v-if="index === 0"></div>
            </div>
          </div>
          
          <div class="timeline-card-creative">
            <div class="card-time">{{ formatTimeAgo(activity.timestamp) }}</div>
            
            <div class="card-content-wrapper">
              <div class="card-icon-creative" :style="{ background: getActivityGradient(activity.type) }">
                <font-awesome-icon :icon="getActivityIcon(activity.type)" />
              </div>
              
              <div class="card-content">
                <h4 class="card-title">{{ activity.title }}</h4>
                <p class="card-description">{{ activity.description }}</p>
                
                <router-link v-if="activity.link" :to="activity.link.url" class="card-action">
                  <span>{{ activity.link.text }}</span>
                  <font-awesome-icon icon="fa-arrow-right" />
                </router-link>
              </div>
            </div>
            
            <div class="card-decoration" :style="{ background: getActivityGradient(activity.type) }"></div>
          </div>
        </div>
      </div>
    </section>



    <section class="study-tips">
      <div class="section-header">
        <h2><font-awesome-icon icon="fa-solid fa-lightbulb" /> Dicas de Estudo</h2>
      </div>
      <div class="tips-container">
        <StudyTips />
      </div>
    </section>

    <section class="ai-resources-creative">
      <div class="ai-resources-header">
        <div class="header-left">
          <div class="ai-icon-container">
            <font-awesome-icon icon="fa-solid fa-robot" class="ai-icon" />
            <div class="ai-icon-glow"></div>
            <div class="ai-icon-pulse"></div>
          </div>
          <div class="header-text">
            <h2 class="section-title">Recursos Recomendados por IA</h2>
            <p class="section-subtitle">Baseado no seu perfil e histórico de estudo</p>
          </div>
        </div>
        <div class="ai-badge">
          <div class="badge-inner">
            <span class="badge-text">Personalizado</span>
            <div class="badge-glow"></div>
          </div>
        </div>
      </div>
      
      <div class="resources-grid-creative">
        <div 
          v-for="(resource, index) in recommendedResources" 
          :key="index" 
          class="resource-card-creative"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="card-header">
            <div class="resource-icon-creative" :style="{ background: getResourceGradient(resource.type) }">
              <font-awesome-icon :icon="resource.icon" />
              <div class="icon-particle" v-for="i in 3" :key="i"></div>
            </div>
            <div class="resource-type">{{ getResourceType(resource.icon) }}</div>
          </div>
          
          <div class="resource-content-creative">
            <h4 class="resource-title">{{ resource.title }}</h4>
            <p class="resource-description">{{ resource.description }}</p>
            
            <div class="resource-tags-creative">
              <span 
                v-for="(tag, tagIndex) in resource.tags" 
                :key="tagIndex" 
                class="resource-tag-creative"
                :style="{ animationDelay: `${tagIndex * 0.05}s` }"
              >
                <span class="tag-icon">•</span>
                {{ tag }}
              </span>
            </div>
            
            <div class="resource-footer">
              <div class="resource-stats">
                <div class="stat-item">
                  <font-awesome-icon icon="fa-solid fa-star" />
                  <span>{{ resource.rating || '4.8' }}</span>
                </div>
                <div class="stat-item">
                  <font-awesome-icon icon="fa-solid fa-clock" />
                  <span>{{ resource.duration || '15 min' }}</span>
                </div>
              </div>
              
              <a :href="resource.link" class="resource-link-creative">
                <span>Acessar recurso</span>
                <font-awesome-icon icon="fa-solid fa-arrow-right" />
                <div class="link-glow"></div>
              </a>
            </div>
          </div>
          
          <div class="card-glow" :style="{ background: getResourceGradient(resource.type) }"></div>
        </div>
      </div>
      
      <div class="ai-recommendation-footer">
        <button class="refresh-button">
          <font-awesome-icon icon="fa-solid fa-refresh" />
          <span>Atualizar recomendações</span>
        </button>
        <p class="ai-confidence">
          <font-awesome-icon icon="fa-solid fa-chart-line" />
          <span>95% de relevância com seu perfil</span>
        </p>
      </div>
    </section>

    <section class="getting-started-creative">
      <div class="getting-started-header">
        <div class="header-icon-container">
          <font-awesome-icon icon="fa-solid fa-road" class="header-icon" />
          <div class="icon-glow"></div>
          <div class="icon-particles">
            <div v-for="i in 4" :key="i" class="particle" :style="{ animationDelay: `${i * 0.2}s` }"></div>
          </div>
        </div>
        <div class="header-content">
          <h2 class="section-title">Guia de Estudos</h2>
          <p class="section-description">Siga estes passos para começar sua jornada de aprendizado</p>
        </div>
        <div class="progress-indicator">
          <div class="progress-ring">
            <svg width="60" height="60">
              <circle 
                cx="30" 
                cy="30" 
                r="25" 
                stroke="rgba(48, 54, 61, 0.5)" 
                stroke-width="5" 
                fill="none"
              />
              <circle 
                cx="30" 
                cy="30" 
                r="25" 
                stroke="#42b983" 
                stroke-width="5" 
                fill="none"
                :stroke-dasharray="157"
                :stroke-dashoffset="157 - (157 * completedPercentage / 100)"
                transform="rotate(-90 30 30)"
              />
            </svg>
            <div class="progress-text">{{ completedSteps }}/{{ totalSteps }}</div>
          </div>
        </div>
      </div>
      
      <div class="steps-container-creative">
        <div class="timeline-track"></div>
        
        <div 
          v-for="(step, index) in gettingStartedSteps" 
          :key="index"
          class="step-item-creative" 
          :class="{ 'completed': step.completed, 'active': index === activeStepIndex }"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="step-connector">
            <div class="connector-line" :class="{ 'completed': step.completed }"></div>
            <div class="step-number-creative" :class="{ 'completed': step.completed }">
              <span v-if="!step.completed">{{ index + 1 }}</span>
              <font-awesome-icon v-else icon="fa-solid fa-check" />
              <div class="number-glow"></div>
            </div>
          </div>
          
          <div class="step-content-creative">
            <div class="step-header">
              <h4 class="step-title">{{ step.title }}</h4>
              <div class="step-icon" :style="{ background: getStepGradient(index) }">
                <font-awesome-icon :icon="getStepIcon(step.link)" />
              </div>
            </div>
            
            <p class="step-description">{{ step.description }}</p>
            
            <div class="step-footer">
              <router-link 
                v-if="!step.completed" 
                :to="step.link" 
                class="step-action-creative"
              >
                <span>{{ step.actionText }}</span>
                <font-awesome-icon icon="fa-solid fa-arrow-right" />
                <div class="action-glow"></div>
              </router-link>
              
              <div v-else class="step-completed-creative">
                <font-awesome-icon icon="fa-solid fa-check-circle" />
                <span>Concluído</span>
                <div class="completed-badge">
                  <font-awesome-icon icon="fa-solid fa-star" />
                </div>
              </div>
              
              <div v-if="step.estimatedTime" class="step-time">
                <font-awesome-icon icon="fa-solid fa-clock" />
                <span>{{ step.estimatedTime }}</span>
              </div>
            </div>
          </div>
          
          <div 
            class="step-decoration" 
            :style="{ background: getStepGradient(index) }"
          ></div>
        </div>
      </div>
      
      <div class="getting-started-footer">
        <div class="motivation-quote">
          <font-awesome-icon icon="fa-solid fa-quote-left" class="quote-icon" />
          <p class="quote-text">{{ motivationalQuote }}</p>
        </div>
        
        <button class="skip-tutorial" v-if="completedSteps < totalSteps">
          <font-awesome-icon icon="fa-solid fa-forward" />
          <span>Pular tutorial</span>
        </button>
      </div>
    </section>

    <section class="ai-assistant-creative">
      <div class="ai-background-effects">
        <div class="neural-grid"></div>
        <div class="floating-particles">
          <span class="particle"></span>
          <span class="particle"></span>
          <span class="particle"></span>
          <span class="particle"></span>
        </div>
      </div>
      
      <div class="ai-container-creative">
        <div class="ai-header-creative">
          <div class="ai-icon-creative">
            <div class="icon-glow"></div>
            <div class="icon-core">
              <font-awesome-icon icon="fa-solid fa-robot" />
            </div>
            <div class="icon-rings">
              <span class="ring"></span>
              <span class="ring"></span>
            </div>
            <div class="status-dot"></div>
          </div>
          
          <div class="ai-title-creative">
            <h3>Assistente IA</h3>
            <span class="status-text">
              <span class="status-indicator"></span>
              {{ aiMessageLoading ? 'Analisando...' : 'Pronto para ajudar' }}
            </span>
          </div>
          
          <button class="options-button-creative" @click="toggleAIOptions">
            <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" />
            <div class="options-glow"></div>
          </button>
          
          <transition name="slide-down">
            <div class="options-menu-creative" v-if="showAIOptions">
              <div class="option-item" @click="resetAIAssistant">
                <font-awesome-icon icon="fa-solid fa-rotate" />
                <span>Reiniciar</span>
              </div>
              <div class="option-item" @click="customizeAIAssistant">
                <font-awesome-icon icon="fa-solid fa-sliders" />
                <span>Personalizar</span>
              </div>
              <div class="option-item" @click="expandAIAssistant">
                <font-awesome-icon icon="fa-solid fa-expand" />
                <span>Expandir</span>
              </div>
            </div>
          </transition>
        </div>

        <div class="ai-display-creative">
          <div class="message-area">
            <div class="message-content" v-if="!aiMessageLoading">
              <p class="ai-text">{{ currentAIMessage }}</p>
            </div>
            <div class="loading-animation" v-else>
              <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
          
          <div class="suggestions-area" v-if="aiSuggestions.length > 0 && !aiMessageLoading">
            <button
              v-for="(suggestion, index) in aiSuggestions"
              :key="index"
              class="suggestion-chip"
              @click="handleAISuggestion(suggestion)"
            >
              <span>{{ suggestion.text }}</span>
              <font-awesome-icon icon="fa-solid fa-chevron-right" />
            </button>
          </div>
        </div>

        <div class="ai-interaction-creative">
          <div class="input-wrapper">
            <input
              type="text"
              class="ai-input-creative"
              placeholder="Pergunte algo ao assistente..."
              v-model="aiInputText"
              @keyup.enter="sendAIMessage"
            />
            <button class="send-button-creative" @click="sendAIMessage">
              <font-awesome-icon icon="fa-solid fa-paper-plane" />
              <div class="send-glow"></div>
            </button>
          </div>
        </div>

        <div class="ai-actions-creative">
          <div class="action-card" @click="useAITool('studyPlan')">
            <div class="action-icon">
              <font-awesome-icon icon="fa-solid fa-book" />
            </div>
            <span>Plano de Estudos</span>
            <div class="action-glow"></div>
          </div>
          
          <div class="action-card" @click="useAITool('secondBrain')">
            <div class="action-icon">
              <font-awesome-icon icon="fa-solid fa-brain" />
            </div>
            <span>Second Brain</span>
            <div class="action-glow"></div>
          </div>
          
          <div class="action-card" @click="useAITool('progressAnalysis')">
            <div class="action-icon">
              <font-awesome-icon icon="fa-solid fa-chart-simple" />
            </div>
            <span>Análise de Progresso</span>
            <div class="action-glow"></div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { format, formatDistance } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import StudyStatusCard from './StudyStatusCard.vue';
import QuickActionCard from './QuickActionCard.vue';
import StudyTips from './StudyTips.vue';

export default {
  name: 'HomePage',
  components: {
    StudyTips,
  },
  setup() {
    const store = useStore();
    const router = useRouter();

    const userName = computed(() => {
      const user = store.getters['auth/user'];
      return user ? user.name : 'Estudante';
    });

    const userInitials = computed(() => {
      const user = store.getters['auth/user'];
      if (user && user.name) {
        return user.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
      }
      return 'M';
    });

    const user = computed(() => store.getters['auth/user']);
    const lastLogin = ref('2023-03-02T10:30:00');
    const completedDays = ref(42);
    const streakDays = ref(7);

    const totalStudyHours = computed(() => 125);
    const completedTasks = computed(() => 42);
    const averageScore = computed(() => 87);
    const taskCompletionRate = computed(() => 78);

    // Gamification: Achievements data
    const achievements = ref([
      {
        title: 'Iniciante Empolgado',
        description: 'Complete 5 dias seguidos de estudo.',
        progress: 100,
        icon: 'fa-solid fa-fire',
      },
      {
        title: 'Maratonista de Estudos',
        description: 'Estudar por 50 horas acumuladas.',
        progress: 80,
        icon: 'fa-solid fa-trophy',
      },
      {
        title: 'Memória de Elefante',
        description: 'Concluir 100 sessões de estudo bem-sucedidas.',
        progress: 40,
        icon: 'fa-solid fa-brain',
      },
    ]);

    // Progress Stats data
    const progressStats = ref([
      {
        value: totalStudyHours.value,
        suffix: 'h',
        title: 'Horas Estudadas',
        description: 'Total de tempo dedicado aos estudos',
        icon: 'fa-solid fa-clock',
        trend: '+12% esta semana',
        trendType: 'positive'
      },
      {
        value: completedTasks.value,
        suffix: '',
        title: 'Tarefas Concluídas',
        description: 'Atividades e revisões finalizadas',
        icon: 'fa-solid fa-list-check',
        trend: '+7 desde ontem',
        trendType: 'positive'
      },
      {
        value: averageScore.value,
        suffix: '%',
        title: 'Pontuação Média',
        description: 'Média de acertos nos seus testes',
        icon: 'fa-solid fa-star',
        trend: '+5% este mês',
        trendType: 'positive'
      },
      {
        value: taskCompletionRate.value,
        suffix: '%',
        title: 'Taxa de Conclusão',
        description: 'Tarefas concluídas vs. planejadas',
        icon: 'fa-solid fa-check-double',
        trend: '-3% esta semana',
        trendType: 'negative'
      }
    ]);

    // Dados do progresso semanal
    const weeklyProgress = ref([
      { name: 'Seg', hours: 2, percentage: 40, isToday: false },
      { name: 'Ter', hours: 3.5, percentage: 70, isToday: false },
      { name: 'Qua', hours: 1.5, percentage: 30, isToday: false },
      { name: 'Qui', hours: 4, percentage: 80, isToday: false },
      { name: 'Sex', hours: 2.5, percentage: 50, isToday: true },
      { name: 'Sáb', hours: 0, percentage: 0, isToday: false },
      { name: 'Dom', hours: 0, percentage: 0, isToday: false },
    ]);

    // AI Assistant Messages
    const aiMessages = [
      'Suas revisões de Cardiologia estão programadas para hoje às 15h.',
      'Seus tópicos mais fracos são Neuroanatomia e Endocrinologia. Quer revisar esses conteúdos?',
      'Você está com um ótimo ritmo de estudos! Continue assim.',
      'Notei que você tem se saído melhor em questões de múltipla escolha. Quer praticar mais?',
      'Recomendo revisar o material de Bioquímica hoje, está quase na hora de esquecê-lo.',
      'Você completou 70% das suas metas de estudo para esta semana!'
    ];
    const currentAIMessage = ref('');
    const currentAIMessageIndex = ref(0);
    const animationInterval = ref(null);

    // Função para animar digitação de mensagem
    const typeMessage = () => {
      const targetMessage = aiMessages[currentAIMessageIndex.value];
      let charIndex = 0;

      clearInterval(animationInterval.value);
      currentAIMessage.value = '';

      animationInterval.value = setInterval(() => {
        if (charIndex < targetMessage.length) {
          currentAIMessage.value += targetMessage[charIndex];
          charIndex++;
        } else {
          clearInterval(animationInterval.value);

          // Aguardar e trocar para próxima mensagem
          setTimeout(() => {
            currentAIMessageIndex.value = (currentAIMessageIndex.value + 1) % aiMessages.length;
            typeMessage();
          }, 5000);
        }
      }, 50);
    };

    // Iniciar animação do assistente IA
    onMounted(() => {
      typeMessage();

      if (!store.getters['auth/user']) {
        try {
          store.dispatch('auth/fetchUser');
        } catch (error) {
          console.error("Erro ao carregar dados do usuário:", error);
        }
      }

      // Inicializar os tooltips e efeitos 3D
      initializeTooltips();
      initialize3DEffects();
    });

    // Limpar intervalos na desmontagem
    onUnmounted(() => {
      clearInterval(animationInterval.value);
    });

    // Funções para efeitos visuais avançados
    const initializeTooltips = () => {
      // Inicialização de tooltips (simulado)
      console.log('Tooltips inicializados');
    };

    const initialize3DEffects = () => {
      // Inicialização de efeitos 3D (simulado)
      console.log('Efeitos 3D inicializados');

      // Implementação do efeito tilt para elementos
      const tiltElements = document.querySelectorAll('.status-card, .resource-card, .quick-action-card, .achievement-card');
      
      // Implementação do efeito tilt sem depender de bibliotecas externas
      tiltElements.forEach(element => {
        element.addEventListener('mousemove', (e) => {
          const rect = element.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;
          
          const centerX = rect.width / 2;
          const centerY = rect.height / 2;
          
          const tiltX = (y - centerY) / centerY * 15; // Enhanced tilt angle
          const tiltY = (centerX - x) / centerX * 15;
          
          element.style.transform = `perspective(1000px) rotateX(${tiltX}deg) rotateY(${tiltY}deg) scale3d(1.05, 1.05, 1.05)`;
          element.style.transition = 'transform 0.1s ease';
          
          // Spotlight effect on hover
          const glowX = (x / rect.width) * 100;
          const glowY = (y / rect.height) * 100;
          element.style.background = `
            radial-gradient(
              circle at ${glowX}% ${glowY}%, 
              rgba(77, 238, 234, 0.15) 0%, 
              rgba(20, 20, 30, 0.8) 50%
            )
          `;
        });
        
        element.addEventListener('mouseleave', () => {
          element.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';
          element.style.transition = 'transform 0.5s ease, background 0.5s ease';
          element.style.background = '';
        });
      });
    };

    const performanceByArea = ref([
      { name: 'Cardiologia', score: 92 },
      { name: 'Neurologia', score: 85 },
      { name: 'Endocrinologia', score: 78 },
      { name: 'Gastroenterologia', score: 65 },
      { name: 'Dermatologia', score: 89 }
    ]);

    const studyDistribution = ref([
      { category: 'Leitura', hours: 45, percentage: 36, color: '#42b983' },
      { category: 'Problemas Práticos', hours: 30, percentage: 24, color: '#ff7e67' },
      { category: 'Revisões', hours: 25, percentage: 20, color: '#4f8fda' },
      { category: 'Revisão', hours: 15, percentage: 12, color: '#9c27b0' },
      { category: 'Vídeo Aulas', hours: 10, percentage: 8, color: '#ffc107' }
    ]);

    const recentActivities = ref([
      {
        type: 'flashcard',
        title: 'Revisão de Flashcards - Cardiologia',
        description: 'Você completou 30 cartões com 85% de acertos',
        timestamp: new Date(Date.now() - 1000 * 60 * 30),
        link: { text: 'Continuar revisão', url: '/flashcards' }
      },
      {
        type: 'ai',
        title: 'AI Tools - Gerador de Questões',
        description: 'Você completou uma sessão com 12 questões geradas por IA',
        timestamp: new Date(Date.now() - 1000 * 60 * 30),
        link: { text: 'Continuar prática', url: '/ai-tools/question-generator' }
      },
      {
        type: 'quiz',
        title: 'Simulado Concluído',
        description: 'Você completou o simulado de Neurologia com 78% de acertos',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3),
        link: { text: 'Ver resultados', url: '/provas-simulados' }
      },
      {
        type: 'note',
        title: 'Nota Criada',
        description: 'Você criou uma nova nota sobre "Arritmias cardíacas"',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5),
        link: { text: 'Editar nota', url: '/resumos-notas' }
      },
      {
        type: 'calendar',
        title: 'Evento Agendado',
        description: 'Você agendou uma revisão de "Endocrinologia" para amanhã',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8),
        link: { text: 'Ver calendário', url: '/calendar' }
      },
      {
        type: 'ai',
        title: 'Consulta à IA',
        description: 'Você consultou o Second Brain sobre "Diabetes tipo 2"',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),
        link: { text: 'Continuar conversa', url: '/second-brain' }
      }
    ]);

    const recommendedResources = ref([
      {
        title: 'Artigo de Pesquisa: Avanços em Cardiologia',
        description: 'Um estudo recente sobre novos tratamentos para insuficiência cardíaca que complementa seu estudo atual.',
        icon: 'fa-solid fa-file-medical',
        tags: ['Cardiologia', 'Estudo Avançado', 'Pesquisa'],
        link: '#'
      },
      {
        title: 'Vídeo Aula: Neuroanatomia Aplicada',
        description: 'Esta aula visual ajudará a complementar suas anotações sobre o sistema nervoso central.',
        icon: 'fa-solid fa-video',
        tags: ['Neurologia', 'Visual', 'Material Complementar'],
        link: '#'
      },
      {
        title: 'Questões Comentadas: Endocrinologia',
        description: 'Com base no seu desempenho, estas questões ajudarão a reforçar conceitos importantes.',
        icon: 'fa-solid fa-clipboard-question',
        tags: ['Endocrinologia', 'Prática', 'Questões'],
        link: '#'
      },
      {
        title: 'Resumo Interativo: Sistema Digestivo',
        description: 'Um mapa conceitual interativo para ajudar na memorização da anatomia e fisiologia gastrointestinal.',
        icon: 'fa-solid fa-diagram-project',
        tags: ['Gastroenterologia', 'Mapa Mental', 'Revisão'],
        link: '#'
      }
    ]);

    const gettingStartedSteps = ref([
      {
        title: 'Configurar seu perfil de estudo',
        description: 'Defina suas metas, áreas de interesse e preferências para personalizar sua experiência.',
        completed: true,
        link: '/profile',
        actionText: 'Configurar Perfil'
      },
      {
        title: 'Criar seu primeiro plano de estudos',
        description: 'Use nosso gerador de planos para organizar seu tempo de maneira eficiente.',
        completed: true,
        link: '/plano-estudos',
        actionText: 'Criar Plano'
      },
      {
        title: 'Adicionar eventos ao calendário',
        description: 'Programe suas sessões de estudo e revisões no calendário para receber lembretes.',
        completed: false,
        link: '/calendar',
        actionText: 'Ir para Calendário'
      },
      {
        title: 'Criar seu primeiro deck de flashcards',
        description: 'Comece a construir seu banco de cartões para revisão espaçada.',
        completed: false,
        link: '/flashcards',
        actionText: 'Criar Flashcards'
      },
      {
        title: 'Fazer sua primeira pergunta ao Second Brain',
        description: 'Experimente nosso assistente de IA para tirar dúvidas e gerar conteúdo de estudo.',
        completed: false,
        link: '/second-brain',
        actionText: 'Acessar Second Brain'
      }
    ]);

    const formatDate = (date) => {
      return format(new Date(date), 'dd/MM/yyyy - HH:mm', { locale: ptBR });
    };

    const formatTimeAgo = (date) => {
      return formatDistance(new Date(date), new Date(), { addSuffix: true, locale: ptBR });
    };

    const getActivityIcon = (type) => {
      const icons = {
        flashcard: 'fa-solid fa-clone',
        quiz: 'fa-solid fa-clipboard-check',
        note: 'fa-solid fa-file-lines',
        calendar: 'fa-solid fa-calendar-day',
        ai: 'fa-solid fa-robot'
      };
      return icons[type] || 'fa-solid fa-circle';
    };

    const getColorByScore = (score) => {
      if (score >= 90) return '#42b983';
      if (score >= 75) return '#4f8fda';
      if (score >= 60) return '#ffc107';
      return '#ff7e67';
    };

    // AI Assistant methods
    const aiMessageLoading = ref(false);
    const aiInputText = ref('');
    const showAIOptions = ref(false);
    const aiSuggestions = ref([
      { text: 'Como organizar meus estudos?', action: 'studyPlan' },
      { text: 'Quais templates são recomendados para mim?', action: 'templateRecommender' },
      { text: 'Como está meu progresso?', action: 'progressAnalysis' },
      { text: 'Gere um resumo sobre Cardiologia', action: 'generateSummary' }
    ]);

    const toggleAIOptions = () => {
      showAIOptions.value = !showAIOptions.value;
    };

    const resetAIAssistant = () => {
      aiMessageLoading.value = true;
      showAIOptions.value = false;

      // Simulate AI reset
      setTimeout(() => {
        currentAIMessage.value = 'Assistente reiniciado. Como posso ajudar você hoje?';
        aiMessageLoading.value = false;
      }, 1500);
    };

    const customizeAIAssistant = () => {
      showAIOptions.value = false;
      router.push('/settings/ai-assistant');
    };

    const expandAIAssistant = () => {
      showAIOptions.value = false;
      router.push('/second-brain');
    };

    const sendAIMessage = () => {
      if (!aiInputText.value.trim()) return;

      aiMessageLoading.value = true;
      const userMessage = aiInputText.value;
      aiInputText.value = '';

      // Simulate AI response
      setTimeout(() => {
        if (userMessage.toLowerCase().includes('template')) {
          currentAIMessage.value = 'Posso ajudar você a encontrar o template ideal para seus estudos. Temos templates especializados para diferentes áreas da medicina. Qual área específica você está estudando?';
        } else if (userMessage.toLowerCase().includes('estud')) {
          currentAIMessage.value = 'Para otimizar seus estudos, recomendo utilizar a técnica de repetição espaçada e criar um cronograma consistente. Você gostaria que eu criasse um plano de estudos personalizado para você?';
        } else if (userMessage.toLowerCase().includes('progress')) {
          currentAIMessage.value = 'Seu progresso está excelente! Você completou 42 dias de estudo e tem uma sequência atual de 7 dias. Sua média de acertos é de 87%, o que é muito bom. Continue assim!';
        } else {
          currentAIMessage.value = 'Entendi sua pergunta sobre "' + userMessage + '". Posso ajudar com informações médicas, criar resumos, gerar questões para estudo ou recomendar recursos. O que você gostaria de saber especificamente?';
        }

        aiMessageLoading.value = false;
      }, 2000);
    };

    const handleAISuggestion = (suggestion) => {
      aiMessageLoading.value = true;

      // Simulate AI response based on suggestion
      setTimeout(() => {
        switch(suggestion.action) {
          case 'studyPlan':
            currentAIMessage.value = 'Posso criar um plano de estudos personalizado para você. Baseado no seu histórico, recomendo focar em Cardiologia (3h/semana), Neurologia (2h/semana) e Farmacologia (2h/semana). Gostaria que eu detalhasse este plano?';
            break;
          case 'templateRecommender':
            currentAIMessage.value = 'Baseado no seu perfil e histórico de estudo, recomendo os templates de Anatomia Interativa e Revisão Clínica. Eles são ideais para a fase atual do seu aprendizado. Gostaria de ver mais detalhes sobre esses templates?';
            break;
          case 'progressAnalysis':
            currentAIMessage.value = 'Sua performance tem melhorado consistentemente. Você teve um aumento de 12% nas horas de estudo e 8% nas tarefas concluídas. Sua área mais forte é Cardiologia (92%) e a que precisa de mais atenção é Neuroanatomia (76%).';
            break;
          case 'generateSummary':
            currentAIMessage.value = 'Gerando um resumo sobre Cardiologia... O coração é um órgão muscular responsável por bombear sangue para todo o corpo. Suas principais estruturas incluem átrios, ventrículos, válvulas e o sistema de condução elétrica. Gostaria de um resumo mais detalhado?';
            break;
          default:
            currentAIMessage.value = 'Posso ajudar você com isso. Por favor, me forneça mais detalhes sobre o que você precisa.';
        }

        aiMessageLoading.value = false;
      }, 2000);
    };

    const useAITool = (tool) => {
      switch(tool) {
        case 'studyPlan':
          router.push('/study-plan');
          break;
        case 'secondBrain':
          router.push('/second-brain');
          break;
        case 'progressAnalysis':
          router.push('/progress');
          break;
        case 'templateRecommender':
          router.push('/templates/recommender');
          break;
      }
    };

    // Stats details methods
    const selectedStat = ref(null);

    const showStatDetails = (stat) => {
      selectedStat.value = stat;
    };

    const getStatTitle = (stat) => {
      const titles = {
        studyHours: 'Horas de Estudo',
        completedTasks: 'Tarefas Concluídas',
        averageScore: 'Média de Acertos',
        taskCompletionRate: 'Taxa de Conclusão'
      };
      return titles[stat] || '';
    };

    const getStatChartData = () => {
      // Mock data for chart
      switch(selectedStat.value) {
        case 'studyHours':
          return [65, 80, 90, 75, 85, 95, 100];
        case 'completedTasks':
          return [40, 55, 70, 65, 75, 80, 85];
        case 'averageScore':
          return [75, 80, 85, 82, 87, 84, 90];
        case 'taskCompletionRate':
          return [85, 80, 75, 70, 72, 78, 75];
        default:
          return [50, 60, 70, 80, 90, 80, 70];
      }
    };

    const getStatChartLabels = () => {
      return ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'];
    };

    const getStatInsights = () => {
      switch(selectedStat.value) {
        case 'studyHours':
          return [
            'Você estudou 12% mais esta semana comparado à anterior',
            'Seu melhor dia de estudo é domingo',
            'Recomendação: Mantenha a consistência nos dias de semana'
          ];
        case 'completedTasks':
          return [
            'Você completou 8% mais tarefas esta semana',
            'Sua eficiência na conclusão de tarefas está melhorando',
            'Recomendação: Divida tarefas grandes em menores para melhor acompanhamento'
          ];
        case 'averageScore':
          return [
            'Sua média de acertos aumentou 5% este mês',
            'Melhor desempenho em Cardiologia (92%)',
            'Área para melhorar: Neuroanatomia (76%)'
          ];
        case 'taskCompletionRate':
          return [
            'Sua taxa de conclusão diminuiu 3% esta semana',
            'Possível causa: Aumento no número de tarefas planejadas',
            'Recomendação: Ajuste suas metas para serem mais realistas'
          ];
        default:
          return ['Clique em uma estatística para ver insights detalhados'];
      }
    };

    const getPlatformIcon = (platform) => {
      const icons = {
        'Web': 'fa-solid fa-globe',
        'Mobile': 'fa-solid fa-mobile-alt',
        'Notion': 'fa-solid fa-n',
        'PDF': 'fa-solid fa-file-pdf',
        'Excel': 'fa-solid fa-file-excel'
      };

      return icons[platform] || 'fa-solid fa-question';
    };

    const getAreaIcon = (area) => {
      const icons = {
        'Cardiologia': 'fa-heart',
        'Neurologia': 'fa-brain',
        'Endocrinologia': 'fa-vial',
        'Gastroenterologia': 'fa-stomach',
        'Dermatologia': 'fa-hand-dots',
        'Pediatria': 'fa-baby',
        'Ortopedia': 'fa-bone'
      };
      return icons[area] || 'fa-stethoscope';
    };

    const donutSegments = computed(() => {
      let cumulativePercentage = 0;
      return studyDistribution.value.map((item, index) => {
        const length = (item.percentage / 100) * (2 * Math.PI * 80);
        const offset = 2 * Math.PI * 80 - length;
        const rotation = -90 + (cumulativePercentage * 3.6);
        cumulativePercentage += item.percentage;
        return { length, offset, rotation };
      });
    });

    const getActivityGradient = (type) => {
      const gradients = {
        'flashcard': 'linear-gradient(135deg, #42b983, #35a372)',
        'ai': 'linear-gradient(135deg, #3b82f6, #2563eb)',
        'quiz': 'linear-gradient(135deg, #f59e0b, #d97706)',
        'note': 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
        'calendar': 'linear-gradient(135deg, #ec4899, #db2777)'
      };
      return gradients[type] || 'linear-gradient(135deg, #6b7280, #4b5563)';
    };

    const getResourceGradient = (type) => {
      const gradients = {
        'research': 'linear-gradient(135deg, #3b82f6, #2563eb)',
        'video': 'linear-gradient(135deg, #ef4444, #dc2626)',
        'practice': 'linear-gradient(135deg, #10b981, #059669)',
        'interactive': 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
        'default': 'linear-gradient(135deg, #6b7280, #4b5563)'
      };
      
      // Map icon to type
      if (type?.includes('file-medical')) return gradients.research;
      if (type?.includes('video')) return gradients.video;
      if (type?.includes('clipboard-question')) return gradients.practice;
      if (type?.includes('diagram-project')) return gradients.interactive;
      
      return gradients.default;
    };

    const getResourceType = (icon) => {
      const types = {
        'fa-solid fa-file-medical': 'Artigo',
        'fa-solid fa-video': 'Vídeo',
        'fa-solid fa-clipboard-question': 'Questões',
        'fa-solid fa-diagram-project': 'Interativo'
      };
      return types[icon] || 'Recurso';
    };

    const getStepGradient = (index) => {
      const gradients = [
        'linear-gradient(135deg, #3b82f6, #2563eb)', // Blue
        'linear-gradient(135deg, #8b5cf6, #7c3aed)', // Purple
        'linear-gradient(135deg, #f59e0b, #d97706)', // Orange
        'linear-gradient(135deg, #10b981, #059669)', // Green
        'linear-gradient(135deg, #ec4899, #db2777)', // Pink
        'linear-gradient(135deg, #42b983, #35a372)'  // Default green
      ];
      return gradients[index] || gradients[5];
    };

    const getStepIcon = (link) => {
      const icons = {
        '/profile': 'fa-solid fa-user-cog',
        '/plano-estudos': 'fa-solid fa-list-check',
        '/calendar': 'fa-solid fa-calendar-days',
        '/flashcards': 'fa-solid fa-clone',
        '/second-brain': 'fa-solid fa-brain'
      };
      return icons[link] || 'fa-solid fa-check';
    };

    const completedSteps = computed(() => {
      return gettingStartedSteps.value.filter(step => step.completed).length;
    });

    const totalSteps = computed(() => {
      return gettingStartedSteps.value.length;
    });

    const completedPercentage = computed(() => {
      return (completedSteps.value / totalSteps.value) * 100;
    });

    const activeStepIndex = computed(() => {
      return gettingStartedSteps.value.findIndex(step => !step.completed);
    });

    const motivationalQuotes = [
      "A jornada de mil milhas começa com um único passo.",
      "O sucesso é a soma de pequenos esforços repetidos dia após dia.",
      "A educação é a arma mais poderosa que você pode usar para mudar o mundo.",
      "O conhecimento é poder, mas o conhecimento aplicado é superpoder.",
      "Aprender é a única coisa que a mente nunca se cansa."
    ];

    const motivationalQuote = ref(motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)]);

    return {
      userName,
      userInitials,
      user,
      lastLogin,
      completedDays,
      streakDays,
      totalStudyHours,
      completedTasks,
      averageScore,
      taskCompletionRate,
      progressStats,
      performanceByArea,
      studyDistribution,
      recentActivities,
      recommendedResources,
      gettingStartedSteps,
      formatDate,
      formatTimeAgo,
      getActivityIcon,
      getColorByScore,
      getAreaIcon,
      getActivityGradient,
      getResourceGradient,
      getResourceType,
      getStepGradient,
      getStepIcon,
      completedSteps,
      totalSteps,
      completedPercentage,
      activeStepIndex,
      motivationalQuote,
      donutSegments,
      weeklyProgress,
      currentAIMessage,
      achievements,
      initialize3DEffects,
      // AI Assistant variables and methods
      aiMessageLoading,
      aiInputText,
      showAIOptions,
      aiSuggestions,
      toggleAIOptions,
      resetAIAssistant,
      customizeAIAssistant,
      expandAIAssistant,
      sendAIMessage,
      handleAISuggestion,
      useAITool
    };
  },
};
</script>

<style>
/* Neon color palette */
.home-container {
  --neon-green: #39ff14;
  --neon-blue: #4deeea;
  --neon-purple: #b65bff;
  --neon-pink: #ff36ab;
  --neon-orange: #ff9e1b;
  --neon-cyan: #18e6c8;
  --neon-yellow: #f5d300;

  /* Glow strengths */
  --glow-sm: 0 0 5px;
  --glow-md: 0 0 12px;
  --glow-lg: 0 0 20px;
  --glow-xl: 0 0 30px;
  
  /* Text colors */
  --text-light: #F9FAFB;
  --text-secondary: #D1D5DB;
  --color-text: #F9FAFB;
  --color-text-secondary: #D1D5DB;
  
  /* Background colors */
  --bg-dark: rgb(10, 12, 15);
  --bg-card: rgba(48, 54, 61, 0.3);
  --border-color: rgba(48, 54, 61, 0.5);
  --color-bg: #111827;
  --color-bg-secondary: #1F2937;
  --color-bg-tertiary: #374151;
  --color-card-bg: #1F2937;
  --color-border: #374151;
  
  /* Primary colors */
  --primary-color: #42b983;
  --secondary-color: #35a372;
  --color-primary: #42b983;
  
  /* Border radius */
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  
  /* Font sizes */
  --font-size-sm: 0.875rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  
  /* Font weights */
  --font-weight-bold: 600;
  
  /* Card background */
  --card-bg: rgba(48, 54, 61, 0.3);
  
  /* Elevations */
  --elevation-2: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  /* Primary scale */
  --primary-500: #42b983;
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-md: 1rem;
  --space-xl: 2rem;
  
  /* Success color */
  --success-500: #4cd964;
  
  /* Transitions */
  --transition-base: 0.3s ease;
  
  /* Dynamic value for animations */
  --raw-value: 70;
}

.home-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

/* Enhanced hero section with creative design */
.hero-creative {
  margin-bottom: 3rem;
  position: relative;
  overflow: hidden;
  background: rgb(10, 12, 15);
  border-radius: 16px;
  border: 1px solid rgba(48, 54, 61, 0.5);
}

@keyframes gradientAnimation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.hero:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='40' fill='none' stroke='rgba(255, 255, 255, 0.05)' stroke-width='1'/%3E%3C/svg%3E");
  opacity: 0.3;
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Enhanced welcome text with text shadow */
.welcome-text h1 {
  font-size: 2.8rem;
  margin-bottom: 1rem;
  color: var(--text-light);
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
  background: linear-gradient(90deg, #ffffff, #d1f1e4);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: textShimmer 3s infinite linear;
  background-size: 200% 100%;
}

@keyframes textShimmer {
  0% { background-position: 0% center; }
  100% { background-position: 200% center; }
}

/* Enhanced subtitle with neon glow effect */
.subtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  opacity: 0.9;
  margin-bottom: 0.5rem;
  position: relative;
  text-shadow: 0 0 8px rgba(77, 238, 234, 0.4);
  animation: subtlePulse 4s infinite alternate;
}

@keyframes subtlePulse {
  0% { text-shadow: 0 0 8px rgba(77, 238, 234, 0.3); }
  100% { text-shadow: 0 0 15px rgba(77, 238, 234, 0.7); }
}

.last-login {
  font-size: 0.9rem;
  color: var(--text-light);
  opacity: 0.8;
  background: linear-gradient(90deg, rgba(255,255,255,0.7), rgba(255,255,255,0.3));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.profile-summary {
  background: rgba(20, 20, 30, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: var(--border-radius-md);
  min-width: 200px;
  position: relative;
  animation: floating 6s ease-in-out infinite;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

@keyframes floating {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.profile-metrics {
  display: flex;
  gap: 1.5rem;
}

.metric {
  text-align: center;
}

.metric-value {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--neon-blue);
  text-shadow: 0 0 10px rgba(77, 238, 234, 0.3);
  animation: pulsatingValue 3s infinite alternate;
}

@keyframes pulsatingValue {
  0% { text-shadow: 0 0 5px rgba(77, 238, 234, 0.2); }
  100% { text-shadow: 0 0 15px rgba(77, 238, 234, 0.6); }
}

.metric-label {
  display: block;
  font-size: 0.9rem;
  color: var(--text-light);
  opacity: 0.9;
}

/* Add neon borders and glows to sections */
section {
  margin-bottom: 3rem;
  padding: 1.8rem;
  background: rgba(15, 15, 20, 0.6);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(8px);
}

section:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
}

/* Neon section accents - different color for each section */
section:nth-child(3n+1)::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--neon-green);
  box-shadow: var(--glow-md) var(--neon-green);
}

section:nth-child(3n+2)::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--neon-blue);
  box-shadow: var(--glow-md) var(--neon-blue);
}

section:nth-child(3n+3)::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--neon-purple);
  box-shadow: var(--glow-md) var(--neon-purple);
}

/* Enhanced section titles with neon glow */
section h2 {
  color: white;
  margin-bottom: 1.5rem;
  font-size: 1.7rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.6);
  position: relative;
  padding-left: 0.5rem;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-weight: 600;
}

section h2::before {
  content: '';
  position: absolute;
  left: -5px;
  top: 50%;
  width: 3px;
  height: 70%;
  background: var(--neon-cyan);
  transform: translateY(-50%);
  box-shadow: 0 0 10px var(--neon-cyan);
  animation: pulsingBar 2s infinite alternate;
}

@keyframes pulsingBar {
  0% { height: 50%; opacity: 0.7; }
  100% { height: 80%; opacity: 1; }
}

.section-description {
  color: var(--text-light);
  opacity: 0.8;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.view-all-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s;
}

.view-all-link:hover {
  color: var(--secondary-color);
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.8rem;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 1.5rem;
}

.status-card {
  background: rgba(20, 20, 30, 0.8);
  border-radius: var(--border-radius-md);
  padding: 1.8rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.status-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
  box-shadow: 0 0 15px var(--neon-cyan);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.status-card:hover::before {
  opacity: 1;
}

.chart-container {
  height: 300px;
  position: relative;
}

.performance-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  overflow: hidden;
  border-radius: 8px;
}

.performance-table th,
.performance-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.performance-table th {
  font-weight: 600;
  color: var(--neon-cyan);
  text-shadow: 0 0 5px var(--neon-cyan);
  background: rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.performance-table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--neon-cyan);
  box-shadow: 0 0 8px var(--neon-cyan);
}

.performance-table tr {
  transition: all 0.3s ease;
}

.performance-table tr:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: scale(1.01);
}

.performance-table tr:hover td {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.progress-bar {
  height: 8px;
  background: rgba(20, 20, 30, 0.5);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
              rgba(255,255,255,0) 0%,
              rgba(255,255,255,0.3) 50%,
              rgba(255,255,255,0) 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.distribution-data {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.distribution-item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
}

.distribution-label {
  width: 120px;
  font-size: 0.9rem;
}

.distribution-bar {
  flex: 1;
  height: 12px;
  background: rgba(20, 20, 30, 0.5);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.distribution-fill {
  height: 100%;
  border-radius: 6px;
  position: relative;
  transition: width 1s cubic-bezier(0.34, 1.56, 0.64, 1);
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 20px 20px;
  animation: stripesAnimation 1s linear infinite;
}

@keyframes stripesAnimation {
  0% { background-position: 0 0; }
  100% { background-position: 20px 0; }
}

.timeline {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
}

.timeline::after {
  content: '';
  position: absolute;
  width: 2px;
  background-color: rgba(255, 255, 255, 0.1);
  top: 0;
  bottom: 0;
  left: 20px;
  margin-left: -1px;
}

.timeline-item {
  padding: 12px 40px;
  position: relative;
  margin-bottom: 1.8rem;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.timeline-item:hover {
  transform: translateX(10px);
}

.timeline-icon {
  position: absolute;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  z-index: 1;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.timeline-icon:hover {
  transform: scale(1.1);
}

/* Neon timeline icons */
.timeline-icon.flashcard {
  background-color: var(--neon-green);
  box-shadow: 0 0 10px var(--neon-green);
}
.timeline-icon.quiz {
  background-color: var(--neon-orange);
  box-shadow: 0 0 10px var(--neon-orange);
}
.timeline-icon.note {
  background-color: var(--neon-blue);
  box-shadow: 0 0 10px var(--neon-blue);
}
.timeline-icon.calendar {
  background-color: var(--neon-purple);
  box-shadow: 0 0 10px var(--neon-purple);
}
.timeline-icon.ai {
  background-color: var(--neon-cyan);
  box-shadow: 0 0 10px var(--neon-cyan);
}

.timeline-content {
  background: rgba(20, 20, 30, 0.8);
  border-radius: var(--border-radius-md);
  padding: 1.8rem;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.timeline-content:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  background: rgba(25, 25, 35, 0.9);
}

.timeline-time {
  font-size: 0.8rem;
  color: var(--text-light);
  opacity: 0.7;
  margin-bottom: 0.5rem;
}

.timeline-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: var(--text-light);
}

.timeline-description {
  margin: 0 0 var(--space-md);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.timeline-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--neon-blue);
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.3rem 0;
}

.timeline-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--neon-blue);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.timeline-link:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

.timeline-link:hover, .resource-link:hover, .view-all-link:hover {
  color: var(--neon-cyan);
  transform: translateY(-2px);
  text-shadow: 0 0 5px rgba(77, 238, 234, 0.5);
}

.timeline-link:hover::after {
  background: var(--neon-cyan);
  box-shadow: 0 0 5px var(--neon-cyan);
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.resource-card {
  background: rgba(20, 20, 30, 0.8);
  border-radius: var(--border-radius-md);
  padding: 1.8rem;
  display: flex;
  gap: 1.2rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.08);
  overflow: hidden;
}

.resource-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--neon-cyan);
  box-shadow: 0 0 10px var(--neon-cyan);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.resource-card:hover::after {
  transform: scaleX(1);
}

.resource-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
}

.resource-icon {
  width: 55px;
  height: 55px;
  border-radius: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.6rem;
  color: white;
  background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
  box-shadow: 0 0 15px rgba(77, 238, 234, 0.5);
  transition: all 0.3s ease;
}

.resource-card:hover .resource-icon {
  transform: scale(1.1) rotate(10deg);
}

.resource-content {
  flex: 1;
}

.resource-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: var(--text-light);
}

.resource-content p {
  margin: 0 0 1rem 0;
  color: var(--text-light);
  opacity: 0.9;
  font-size: 0.9rem;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.resource-tag {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 0.3rem 0.9rem;
  font-size: 0.85rem;
  color: white;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  position: relative;
}

.resource-tag:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.resource-tag::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
              rgba(255,255,255,0) 0%,
              rgba(255,255,255,0.2) 50%,
              rgba(255,255,255,0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.resource-tag:hover::after {
  transform: translateX(100%);
}

.resource-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--neon-blue);
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.3rem 0;
}

.resource-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--neon-blue);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.resource-link:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

.resource-link:hover, .view-all-link:hover {
  color: var(--neon-cyan);
  transform: translateY(-2px);
  text-shadow: 0 0 5px rgba(77, 238, 234, 0.5);
}

.resource-link:hover::after {
  background: var(--neon-cyan);
  box-shadow: 0 0 5px var(--neon-cyan);
}

.steps-container {
  padding: 1rem 0;
}

.steps-timeline {
  position: relative;
}

.steps-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 2rem;
  height: 100%;
  width: 2px;
  background: rgba(255, 255, 255, 0.1);
}

.step-item {
  display: flex;
  gap: 1.8rem;
  position: relative;
  padding: 1.2rem 0;
  margin-bottom: 1.2rem;
  transition: transform 0.3s ease;
}

.step-item:hover {
  transform: translateX(10px);
}

.step-number {
  width: 4.5rem;
  height: 4.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.7rem;
  font-weight: bold;
  z-index: 1;
  box-shadow: 0 0 15px rgba(77, 238, 234, 0.5);
  transition: all 0.3s ease;
}

.step-item:hover .step-number {
  transform: scale(1.1);
  box-shadow: 0 0 25px rgba(77, 238, 234, 0.7);
}

.step-content {
  flex: 1;
  background: rgba(20, 20, 30, 0.8);
  border-radius: var(--border-radius-md);
  padding: 1.8rem;
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.step-content:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.step-action {
  display: inline-block;
  background: linear-gradient(90deg, var(--neon-blue), var(--neon-cyan));
  color: white;
  padding: 0.6rem 1.2rem;
  border-radius: var(--border-radius-sm);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: none;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  font-weight: 500;
}

.step-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
              rgba(255,255,255,0) 0%,
              rgba(255,255,255,0.2) 50%,
              rgba(255,255,255,0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.step-action:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3),
              0 0 10px rgba(77, 238, 234, 0.5);
}

.step-action:hover::before {
  transform: translateX(100%);
}

.step-completed {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--neon-green);
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  background: rgba(57, 255, 20, 0.1);
  border: 1px solid rgba(57, 255, 20, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.step-completed::before {
  content: '';
  position: absolute;
  width: 30px;
  height: 100%;
  top: 0;
  left: -30px;
  background: linear-gradient(90deg,
              rgba(57, 255, 20, 0) 0%,
              rgba(57, 255, 20, 0.3) 50%,
              rgba(57, 255, 20, 0) 100%);
  animation: shiningEffect 3s infinite;
}

@keyframes shiningEffect {
  0% { left: -30px; }
  100% { left: 100%; }
}

.step-completed:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(57, 255, 20, 0.3);
}

/* Enhanced scrollbar with neon pulsing */
.home-container::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--neon-blue), var(--neon-purple));
  border-radius: 5px;
  animation: scrollbarPulse 3s infinite alternate;
}

@keyframes scrollbarPulse {
  0% { box-shadow: 0 0 5px rgba(77, 238, 234, 0.3); }
  100% { box-shadow: 0 0 15px rgba(77, 238, 234, 0.7); }
}

/* Background effect for the whole page - enhanced */
.home-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(57, 255, 20, 0.03) 0%, transparent 20%),
    radial-gradient(circle at 90% 30%, rgba(77, 238, 234, 0.03) 0%, transparent 20%),
    radial-gradient(circle at 30% 70%, rgba(182, 91, 255, 0.03) 0%, transparent 20%),
    radial-gradient(circle at 70% 90%, rgba(255, 54, 171, 0.03) 0%, transparent 20%);
  z-index: -1;
  pointer-events: none;
  animation: ambientLighting 20s ease infinite alternate;
}

@keyframes ambientLighting {
  0% {
    background:
      radial-gradient(circle at 10% 20%, rgba(57, 255, 20, 0.03) 0%, transparent 20%),
      radial-gradient(circle at 90% 30%, rgba(77, 238, 234, 0.03) 0%, transparent 20%),
      radial-gradient(circle at 30% 70%, rgba(182, 91, 255, 0.03) 0%, transparent 20%),
      radial-gradient(circle at 70% 90%, rgba(255, 54, 171, 0.03) 0%, transparent 20%);
  }
  50% {
    background:
      radial-gradient(circle at 20% 10%, rgba(57, 255, 20, 0.03) 0%, transparent 25%),
      radial-gradient(circle at 85% 40%, rgba(77, 238, 234, 0.03) 0%, transparent 25%),
      radial-gradient(circle at 40% 60%, rgba(182, 91, 255, 0.03) 0%, transparent 25%),
      radial-gradient(circle at 60% 95%, rgba(255, 54, 171, 0.03) 0%, transparent 25%);
  }
  100% {
    background:
      radial-gradient(circle at 15% 25%, rgba(57, 255, 20, 0.03) 0%, transparent 30%),
      radial-gradient(circle at 80% 25%, rgba(77, 238, 234, 0.03) 0%, transparent 30%),
      radial-gradient(circle at 35% 65%, rgba(182, 91, 255, 0.03) 0%, transparent 30%),
      radial-gradient(circle at 65% 85%, rgba(255, 54, 171, 0.03) 0%, transparent 30%);
  }
}

@media (max-width: 992px) {
  .status-grid {
    grid-template-columns: 1fr;
  }

  .hero-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 2rem;
  }

  .profile-summary {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .home-container {
    padding: 1rem;
  }

  .hero {
    padding: 2rem 1rem;
  }

  .hero h1 {
    font-size: 1.8rem;
    text-align: center;
  }

  .subtitle, .last-login {
    text-align: center;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .resources-grid {
    grid-template-columns: 1fr;
  }

  .timeline-item {
    padding: 10px 20px 10px 60px;
  }
}

/* Creative Hero Section Styles - Matching Top Bar Design */
.hero-wrapper {
  position: relative;
  padding: 3rem 2rem;
  min-height: 200px;
}

.hero-background {
  position: absolute;
  inset: 0;
  overflow: hidden;
  opacity: 0.1;
}

.grid-pattern {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(66, 185, 131, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(66, 185, 131, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  from { transform: translate(0, 0); }
  to { transform: translate(50px, 50px); }
}

.floating-orbs {
  position: absolute;
  inset: 0;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.3;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: rgba(66, 185, 131, 0.3);
  top: -100px;
  left: -100px;
  animation: orbFloat1 15s ease-in-out infinite;
}

.orb-2 {
  width: 200px;
  height: 200px;
  background: rgba(139, 92, 246, 0.3);
  bottom: -50px;
  right: -50px;
  animation: orbFloat2 20s ease-in-out infinite;
}

.orb-3 {
  width: 150px;
  height: 150px;
  background: rgba(59, 130, 246, 0.3);
  top: 50%;
  left: 50%;
  animation: orbFloat3 18s ease-in-out infinite;
}

@keyframes orbFloat1 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.95); }
}

@keyframes orbFloat2 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(-40px, -20px) scale(1.05); }
  66% { transform: translate(30px, 30px) scale(0.9); }
}

@keyframes orbFloat3 {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  33% { transform: translate(-40%, -60%) scale(1.15); }
  66% { transform: translate(-60%, -40%) scale(0.85); }
}

/* Hero Content */
.hero-content-creative {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 3rem;
}

.welcome-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

.welcome-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.avatar-glow {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% { transform: scale(0.9); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.6; }
}

.avatar-core {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #42b983, #52d399);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  z-index: 3;
  box-shadow: 0 0 20px rgba(66, 185, 131, 0.5);
}

.avatar-rings {
  position: absolute;
  inset: 0;
  z-index: 2;
}

.ring {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 1px solid rgba(66, 185, 131, 0.3);
}

.ring-1 {
  animation: ringExpand 3s ease-out infinite;
}

.ring-2 {
  animation: ringExpand 3s ease-out infinite 1.5s;
}

@keyframes ringExpand {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(1.4); opacity: 0; }
}

/* Welcome Text */
.welcome-text-creative {
  flex: 1;
}

.welcome-title {
  margin: 0 0 0.5rem 0;
  font-size: 2.2rem;
  line-height: 1.2;
}

.greeting {
  display: block;
  font-size: 1.2rem;
  color: #8b949e;
  font-weight: 400;
  margin-bottom: 0.2rem;
}

.user-name-creative {
  display: block;
  font-size: 2.2rem;
  font-weight: 700;
  color: #e6edf3;
  animation: nameGlow 3s ease-in-out infinite;
}

@keyframes nameGlow {
  0%, 100% { text-shadow: 0 0 10px rgba(66, 185, 131, 0.3); }
  50% { text-shadow: 0 0 20px rgba(66, 185, 131, 0.6); }
}

.subtitle-creative {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  color: #8b949e;
  margin-bottom: 0.8rem;
}

.subtitle-icon {
  color: #42b983;
  font-size: 1rem;
}

.last-login-creative {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #8b949e;
  opacity: 0.8;
}

.login-icon {
  color: #42b983;
  font-size: 0.9rem;
}

/* Metrics Cards */
.metrics-creative {
  display: flex;
  gap: 1.5rem;
}

.metric-card {
  position: relative;
  background: rgba(13, 17, 23, 0.6);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 160px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-3px);
  border-color: #42b983;
  box-shadow: 0 8px 24px rgba(66, 185, 131, 0.15);
}

.metric-icon {
  width: 40px;
  height: 40px;
  background: rgba(66, 185, 131, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #42b983;
}

.metric-card.streak .metric-icon {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #e6edf3;
  line-height: 1;
  margin-bottom: 0.2rem;
}

.metric-label {
  font-size: 0.85rem;
  color: #8b949e;
}

.metric-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(20px, -20px);
  pointer-events: none;
}

.metric-card.streak .metric-glow {
  background: radial-gradient(circle, rgba(251, 191, 36, 0.2) 0%, transparent 70%);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .hero-content-creative {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .metrics-creative {
    width: 100%;
    justify-content: space-between;
  }
  
  .metric-card {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .hero-wrapper {
    padding: 2rem 1rem;
  }
  
  .welcome-section {
    flex-direction: column;
    text-align: center;
  }
  
  .welcome-avatar {
    margin: 0 auto;
  }
  
  .welcome-title {
    font-size: 1.8rem;
  }
  
  .user-name-creative {
    font-size: 1.8rem;
  }
  
  .metrics-creative {
    flex-direction: column;
  }
  
  .metric-card {
    width: 100%;
  }
}

/* Creative Achievements Section */
.achievements-creative {
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgb(10, 12, 15);
  border-radius: 16px;
  border: 1px solid rgba(48, 54, 61, 0.5);
  position: relative;
  overflow: hidden;
}

.section-header-creative {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.header-icon-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
}

.header-icon {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #fbbf24;
  z-index: 2;
}

.icon-glow {
  position: absolute;
  inset: -8px;
  background: radial-gradient(circle, rgba(251, 191, 36, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: iconGlow 3s ease-in-out infinite;
}

@keyframes iconGlow {
  0%, 100% { transform: scale(0.8); opacity: 0.3; }
  50% { transform: scale(1.2); opacity: 0.6; }
}

.header-text {
  flex: 1;
}

.section-title-creative {
  margin: 0 0 0.3rem 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #e6edf3;
}

.section-subtitle-creative {
  margin: 0;
  font-size: 1rem;
  color: #8b949e;
}

/* Achievements Grid */
.achievements-grid-creative {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.achievement-card-creative {
  position: relative;
  background: rgba(13, 17, 23, 0.6);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
  overflow: hidden;
}

.achievement-card-creative:hover {
  transform: translateY(-4px);
  border-color: rgba(66, 185, 131, 0.5);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
}

.achievement-card-creative.completed {
  border-color: rgba(251, 191, 36, 0.5);
  background: rgba(251, 191, 36, 0.05);
}

.achievement-backdrop {
  position: absolute;
  inset: 0;
  opacity: 0.05;
  pointer-events: none;
}

.achievement-orb {
  position: absolute;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  top: 50%;
  right: -100px;
  transform: translateY(-50%);
}

.achievement-card-creative.completed .achievement-orb {
  background: radial-gradient(circle, rgba(251, 191, 36, 0.3) 0%, transparent 70%);
}

/* Achievement Icon */
.achievement-icon-creative {
  position: relative;
  flex-shrink: 0;
}

.icon-container {
  position: relative;
  width: 64px;
  height: 64px;
  background: rgba(48, 54, 61, 0.4);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #8b949e;
  transition: all 0.3s ease;
}

.achievement-card-creative.completed .icon-container {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.icon-ring {
  position: absolute;
  inset: -8px;
  border: 2px solid #fbbf24;
  border-radius: 50%;
  animation: ringRotate 3s linear infinite;
}

@keyframes ringRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Achievement Content */
.achievement-content-creative {
  flex: 1;
}

.achievement-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #e6edf3;
}

.achievement-description {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #8b949e;
  line-height: 1.4;
}

/* Progress Bar */
.achievement-progress-creative {
  position: relative;
}

.progress-track {
  position: relative;
  height: 8px;
  background: rgba(48, 54, 61, 0.6);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill-creative {
  position: relative;
  height: 100%;
  background: linear-gradient(90deg, #42b983, #52d399);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.achievement-card-creative.completed .progress-fill-creative {
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
}

.progress-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressGlow 2s linear infinite;
}

@keyframes progressGlow {
  from { transform: translateX(-100%); }
  to { transform: translateX(100%); }
}

.progress-label-creative {
  display: flex;
  align-items: baseline;
  gap: 0.3rem;
  font-size: 0.85rem;
}

.progress-value {
  font-weight: 600;
  color: #e6edf3;
}

.progress-text {
  color: #8b949e;
}

/* Achievement Badge */
.achievement-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
  animation: badgeBounce 3s ease-in-out infinite;
}

@keyframes badgeBounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

/* Responsive */
@media (max-width: 768px) {
  .achievements-creative {
    padding: 1.5rem 1rem;
  }
  
  .section-header-creative {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
  
  .achievements-grid-creative {
    grid-template-columns: 1fr;
  }
  
  .achievement-card-creative {
    padding: 1.2rem;
  }
  
  .icon-container {
    width: 56px;
    height: 56px;
    font-size: 1.3rem;
  }
  
  .achievement-title {
    font-size: 1.1rem;
  }
  
  .achievement-description {
    font-size: 0.85rem;
  }
}

/* Creative Progress Overview Section */
.progress-overview-creative {
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgb(10, 12, 15);
  border-radius: 16px;
  border: 1px solid rgba(48, 54, 61, 0.5);
  position: relative;
  overflow: hidden;
}

.progress-header-creative {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.progress-icon-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
}

.progress-header-icon {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #42b983;
  z-index: 2;
}

.progress-icon-glow {
  position: absolute;
  inset: -8px;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: iconGlow 3s ease-in-out infinite;
}

.progress-header-text {
  flex: 1;
}

.progress-title-creative {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #e6edf3;
}

/* Progress Stats Cards */
.progress-stats-creative {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.stat-card-creative {
  position: relative;
  background: rgba(13, 17, 23, 0.6);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  transition: all 0.3s ease;
  overflow: hidden;
}

.stat-card-creative:hover {
  transform: translateY(-3px);
  border-color: rgba(66, 185, 131, 0.5);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.stat-icon-wrapper {
  width: 40px;
  height: 40px;
  background: rgba(66, 185, 131, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #42b983;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value-wrapper {
  display: flex;
  align-items: baseline;
  gap: 0.2rem;
  margin-bottom: 0.3rem;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #e6edf3;
  line-height: 1;
}

.stat-suffix {
  font-size: 1.1rem;
  color: #8b949e;
  font-weight: 500;
}

.stat-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #e6edf3;
  margin-bottom: 0.2rem;
}

.stat-description {
  font-size: 0.85rem;
  color: #8b949e;
  line-height: 1.3;
}

.stat-trend {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-weight: 500;
}

.stat-trend.positive {
  color: #42b983;
  background: rgba(66, 185, 131, 0.1);
}

.stat-trend.negative {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.stat-decoration {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(66, 185, 131, 0.1) 0%, transparent 70%);
  border-radius: 50% 0 12px 0;
}

/* Weekly Detail Section */
.weekly-detail-creative {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  align-items: stretch;
}

/* Weekly Chart */
.weekly-chart-creative {
  background: rgba(13, 17, 23, 0.4);
  border: 1px solid rgba(48, 54, 61, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
}

.chart-title-creative {
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #e6edf3;
}

.chart-wrapper {
  position: relative;
  height: 200px;
}

.chart-grid {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.grid-line {
  height: 1px;
  background: rgba(48, 54, 61, 0.3);
}

.chart-bars-creative {
  position: relative;
  height: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  gap: 8px;
  z-index: 2;
}

.day-bar-creative {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.bar-container {
  width: 100%;
  height: 160px;
  position: relative;
  display: flex;
  align-items: flex-end;
}

.bar-fill-creative {
  width: 100%;
  background: linear-gradient(180deg, #42b983, #35a372);
  border-radius: 6px 6px 0 0;
  position: relative;
  transition: height 0.3s ease;
  overflow: hidden;
}

.day-bar-creative.today .bar-fill-creative {
  background: linear-gradient(180deg, #3b82f6, #2563eb);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.bar-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2), transparent);
  animation: barPulse 2s ease-in-out infinite;
}

@keyframes barPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.bar-particle {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #42b983;
  border-radius: 50%;
  animation: particleFloat 3s ease-in-out infinite;
}

@keyframes particleFloat {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  50% { transform: translateX(-50%) translateY(-10px); }
}

.bar-label-creative {
  font-size: 0.8rem;
  color: #8b949e;
  font-weight: 500;
}

.bar-value-creative {
  font-size: 0.85rem;
  color: #e6edf3;
  font-weight: 600;
}

/* Stats Detail */
.stats-detail-creative {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-card {
  background: rgba(13, 17, 23, 0.4);
  border: 1px solid rgba(48, 54, 61, 0.3);
  border-radius: 12px;
  padding: 1.2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  transition: all 0.3s ease;
}

.detail-card:hover {
  transform: translateX(-4px);
  border-color: rgba(66, 185, 131, 0.5);
}

.detail-icon {
  width: 36px;
  height: 36px;
  background: rgba(66, 185, 131, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #42b983;
  font-size: 1rem;
}

.detail-card:nth-child(2) .detail-icon {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
}

.detail-card:nth-child(3) .detail-icon {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.detail-info {
  flex: 1;
}

.detail-label {
  font-size: 0.85rem;
  color: #8b949e;
  margin-bottom: 0.2rem;
}

.detail-value-creative {
  font-size: 1.3rem;
  font-weight: 700;
  color: #e6edf3;
}

.detail-indicator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.detail-card:hover .detail-indicator {
  opacity: 1;
}

.detail-indicator.retention {
  background: #42b983;
}

.detail-indicator.focus {
  background: #fbbf24;
}

.detail-indicator.productivity {
  background: #8b5cf6;
}

/* Responsive */
@media (max-width: 1024px) {
  .progress-stats-creative {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .weekly-detail-creative {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .progress-overview-creative {
    padding: 1.5rem 1rem;
  }
  
  .progress-stats-creative {
    grid-template-columns: 1fr;
  }
  
  .stat-card-creative {
    padding: 1.2rem;
  }
  
  .weekly-chart-creative {
    padding: 1rem;
  }
  
  .chart-bars-creative {
    gap: 4px;
  }
  
  .bar-label-creative {
    font-size: 0.7rem;
  }
  
  .detail-card {
    padding: 1rem;
  }
}

/* Creative Performance Analysis Section */
.performance-analysis-creative {
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgb(10, 12, 15);
  border-radius: 16px;
  border: 1px solid rgba(48, 54, 61, 0.5);
  position: relative;
  overflow: hidden;
}

.analysis-header-creative {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.analysis-icon-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
}

.analysis-header-icon {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #8b5cf6;
  z-index: 2;
}

.analysis-icon-glow {
  position: absolute;
  inset: -8px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: iconGlow 3s ease-in-out infinite;
}

.analysis-header-text {
  flex: 1;
}

.analysis-title-creative {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #e6edf3;
}

/* Analysis Grid */
.analysis-grid-creative {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: stretch;
}

/* Card Styling */
.performance-card-creative,
.distribution-card-creative {
  background: rgba(13, 17, 23, 0.6);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.performance-card-creative:hover,
.distribution-card-creative:hover {
  transform: translateY(-3px);
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.card-header-creative {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #e6edf3;
}

.card-icon {
  width: 36px;
  height: 36px;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8b5cf6;
  font-size: 1rem;
}

/* Performance Table */
.performance-table-creative {
  background: rgba(48, 54, 61, 0.2);
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr;
  padding: 0.8rem 1rem;
  background: rgba(48, 54, 61, 0.4);
  border-bottom: 1px solid rgba(48, 54, 61, 0.5);
}

.header-cell {
  font-size: 0.85rem;
  color: #8b949e;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-body {
  padding: 0.5rem;
}

.table-row-creative {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr;
  padding: 0.8rem 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.table-row-creative:hover {
  background: rgba(48, 54, 61, 0.3);
  transform: translateX(4px);
}

.table-row-creative.high-performance {
  border-left: 3px solid #42b983;
}

.table-row-creative.medium-performance {
  border-left: 3px solid #fbbf24;
}

.row-cell {
  display: flex;
  align-items: center;
}

.discipline-name {
  gap: 0.8rem;
}

.discipline-icon {
  width: 28px;
  height: 28px;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  color: #8b5cf6;
}

.score-value {
  gap: 0.2rem;
  font-weight: 700;
}

.score-number {
  font-size: 1.1rem;
  color: #e6edf3;
}

.score-suffix {
  font-size: 0.85rem;
  color: #8b949e;
}

.progress-cell {
  padding-right: 0.5rem;
}

.progress-bar-creative {
  width: 100%;
  height: 6px;
  background: rgba(48, 54, 61, 0.5);
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.progress-fill-bar {
  height: 100%;
  background: linear-gradient(90deg, #42b983, #52d399);
  border-radius: 3px;
  position: relative;
  transition: width 0.3s ease;
}

.table-row-creative.high-performance .progress-fill-bar {
  background: linear-gradient(90deg, #42b983, #35a372);
}

.table-row-creative.medium-performance .progress-fill-bar {
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
}

.table-row-creative:not(.high-performance):not(.medium-performance) .progress-fill-bar {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.progress-glow-bar {
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressGlow 2s linear infinite;
}

.progress-particle {
  position: absolute;
  top: -2px;
  right: 0;
  width: 3px;
  height: 3px;
  background: #42b983;
  border-radius: 50%;
  animation: particleMove 2s ease-in-out infinite;
}

@keyframes particleMove {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(-5px); }
}

/* Distribution Chart */
.distribution-chart-creative {
  position: relative;
  padding: 1rem;
}

.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 2;
}

.center-value {
  font-size: 2rem;
  font-weight: 700;
  color: #e6edf3;
  line-height: 1;
}

.center-label {
  font-size: 0.85rem;
  color: #8b949e;
  margin-top: 0.2rem;
}

.donut-segments {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
}

.donut-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.donut-segment {
  transition: all 0.3s ease;
  opacity: 0.9;
}

.donut-segment:hover {
  opacity: 1;
  filter: drop-shadow(0 0 12px currentColor);
}

.distribution-legend {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.legend-item-creative {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.legend-item-creative:hover {
  background: rgba(48, 54, 61, 0.3);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  flex-shrink: 0;
}

.legend-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.legend-name {
  font-size: 0.9rem;
  color: #e6edf3;
}

.legend-value {
  font-size: 0.85rem;
  color: #8b949e;
}

/* Responsive */
@media (max-width: 1024px) {
  .analysis-grid-creative {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .performance-analysis-creative {
    padding: 1.5rem 1rem;
  }
  
  .table-header,
  .table-row-creative {
    grid-template-columns: 2fr 1fr 1.5fr;
  }
  
  .discipline-name span {
    font-size: 0.85rem;
  }
  
  .score-number {
    font-size: 1rem;
  }
  
  .donut-segments {
    width: 160px;
    height: 160px;
  }
  
  .center-value {
    font-size: 1.5rem;
  }
}

/* Creative Activities Timeline Section */
.activities-timeline-creative {
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgb(10, 12, 15);
  border-radius: 16px;
  border: 1px solid rgba(48, 54, 61, 0.5);
  position: relative;
  overflow: hidden;
}

.timeline-header-creative {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.timeline-icon-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
}

.timeline-header-icon {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #3b82f6;
  z-index: 2;
}

.timeline-icon-glow {
  position: absolute;
  inset: -8px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: iconGlow 3s ease-in-out infinite;
}

.timeline-header-text {
  flex: 1;
}

.timeline-title-creative {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #e6edf3;
}

/* Timeline Structure */
.timeline-creative {
  position: relative;
  padding-left: 3rem;
}

.timeline-line {
  position: absolute;
  left: 24px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #3b82f6 0%, transparent 100%);
}

.timeline-item-creative {
  position: relative;
  margin-bottom: 2rem;
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

.timeline-item-creative:nth-child(2) { animation-delay: 0.1s; }
.timeline-item-creative:nth-child(3) { animation-delay: 0.2s; }
.timeline-item-creative:nth-child(4) { animation-delay: 0.3s; }
.timeline-item-creative:nth-child(5) { animation-delay: 0.4s; }
.timeline-item-creative:nth-child(6) { animation-delay: 0.5s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Timeline Connector */
.timeline-connector {
  position: absolute;
  left: -27px;
  top: 20px;
  z-index: 2;
}

.connector-dot {
  position: relative;
  width: 12px;
  height: 12px;
  background: #1e293b;
  border: 2px solid #3b82f6;
  border-radius: 50%;
}

.timeline-item-creative.item-highlighted .connector-dot {
  background: #3b82f6;
  box-shadow: 0 0 12px rgba(59, 130, 246, 0.6);
}

.dot-pulse {
  position: absolute;
  inset: -8px;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  animation: pulseDot 2s ease-out infinite;
}

@keyframes pulseDot {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

/* Timeline Card */
.timeline-card-creative {
  position: relative;
  background: rgba(13, 17, 23, 0.6);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  overflow: hidden;
}

.timeline-card-creative:hover {
  transform: translateX(4px);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.card-time {
  font-size: 0.85rem;
  color: #8b949e;
  margin-bottom: 1rem;
  font-weight: 500;
}

.card-content-wrapper {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.card-icon-creative {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.card-icon-creative::before {
  content: '';
  position: absolute;
  inset: 0;
  background: inherit;
  filter: blur(10px);
  opacity: 0.3;
  transform: scale(1.2);
}

.card-content {
  flex: 1;
}

.card-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #e6edf3;
}

.card-description {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #8b949e;
  line-height: 1.4;
}

.card-action {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.card-action:hover {
  color: #60a5fa;
  gap: 0.7rem;
}

.card-action .fa-arrow-right {
  font-size: 0.8rem;
  transition: transform 0.2s ease;
}

.card-action:hover .fa-arrow-right {
  transform: translateX(2px);
}

.card-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  opacity: 0.05;
  border-radius: 0 12px 0 50%;
  transform: translate(20px, -20px);
}

/* Item Highlighted State */
.timeline-item-creative.item-highlighted .timeline-card-creative {
  border-color: rgba(59, 130, 246, 0.3);
}

.timeline-item-creative.item-highlighted .card-time {
  color: #3b82f6;
}

/* Responsive */
@media (max-width: 768px) {
  .activities-timeline-creative {
    padding: 1.5rem 1rem;
  }
  
  .timeline-creative {
    padding-left: 2rem;
  }
  
  .timeline-line {
    left: 16px;
  }
  
  .timeline-connector {
    left: -19px;
  }
  
  .card-content-wrapper {
    flex-direction: column;
  }
  
  .card-icon-creative {
    width: 36px;
    height: 36px;
  }
  
  .card-title {
    font-size: 1rem;
  }
  
  .card-description {
    font-size: 0.85rem;
  }
}

/* Template Hub Styles */
.template-hub {
  position: relative;
  margin-bottom: 3rem;
  overflow: hidden;
}

.template-hub-content {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.template-hub-intro {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
}

.hub-intro-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.hub-intro-content h3 {
  font-size: 1.8rem;
  margin-bottom: 0.75rem;
  background: linear-gradient(90deg, #ffffff, var(--neon-cyan), #ffffff);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  animation: shineText 5s linear infinite;
}

@keyframes shineText {
  0% { background-position: 0% center; }
  100% { background-position: 200% center; }
}

.hub-features {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  margin: 1rem 0;
}

.hub-feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(20, 20, 30, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.hub-feature:hover {
  transform: translateX(10px);
  background: rgba(30, 30, 40, 0.7);
  border-color: rgba(77, 238, 234, 0.2);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: rgba(77, 238, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--neon-cyan);
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
}

.feature-text h4 {
  margin: 0 0 0.3rem 0;
  font-size: 1.1rem;
  color: var(--text-light);
}

.feature-text p {
  font-size: 0.9rem;
  opacity: 0.8;
  margin: 0;
}

.hub-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.hub-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  text-decoration: none;
}

.hub-action-btn.primary {
  background: linear-gradient(135deg, var(--neon-cyan), var(--primary-color));
  color: white;
  border: none;
  box-shadow: 0 5px 15px rgba(77, 238, 234, 0.3);
}

.hub-action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.hub-action-btn:hover {
  transform: translateY(-5px);
}

.hub-action-btn.primary:hover {
  box-shadow: 0 8px 25px rgba(77, 238, 234, 0.5);
}

.hub-action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(77, 238, 234, 0.3);
}

.hub-intro-image {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.template-preview {
  width: 100%;
  max-width: 450px;
  background: rgba(30, 30, 40, 0.8);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
  transition: all 0.5s ease;
}

.template-preview:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.template-preview-header {
  display: flex;
  align-items: center;
  padding: 0.8rem 1rem;
  background: rgba(77, 238, 234, 0.1);
  border-bottom: 1px solid rgba(77, 238, 234, 0.2);
}

.preview-dots {
  display: flex;
  gap: 5px;
  margin-right: 1rem;
}

.preview-dots span {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}

.preview-dots span:nth-child(1) {
  background: #ff5f57;
}

.preview-dots span:nth-child(2) {
  background: #febc2e;
}

.preview-dots span:nth-child(3) {
  background: #28c840;
}

.preview-title {
  flex: 1;
  text-align: center;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--neon-cyan);
}

.template-preview-body {
  padding: 1.5rem;
}

.preview-section {
  margin-bottom: 1.5rem;
}

.preview-section h5 {
  margin: 0 0 0.8rem 0;
  font-size: 1rem;
  color: var(--text-light);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding-left: 1rem;
}

.preview-item {
  font-size: 0.9rem;
  color: var(--text-secondary);
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.preview-item:hover {
  background: rgba(77, 238, 234, 0.1);
  transform: translateX(5px);
}

.popular-templates h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--text-light);
  position: relative;
  display: inline-block;
}

.popular-templates h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--neon-cyan);
  box-shadow: 0 0 10px var(--neon-cyan);
}

.templates-carousel {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  padding: 1rem 0.5rem;
  margin: 0 -0.5rem;
  scrollbar-width: thin;
  scrollbar-color: var(--neon-cyan) rgba(255, 255, 255, 0.1);
}

.templates-carousel::-webkit-scrollbar {
  height: 6px;
}

.templates-carousel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.templates-carousel::-webkit-scrollbar-thumb {
  background: var(--neon-cyan);
  border-radius: 3px;
}

.template-card {
  flex: 0 0 300px;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(20, 20, 30, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.template-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(77, 238, 234, 0.2);
}

.template-card-header {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  overflow: hidden;
}

.template-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  z-index: 1;
}

.template-card-header.bg-blue {
  background: linear-gradient(135deg, #4a6cf7, #2541b2);
}

.template-card-header.bg-green {
  background: linear-gradient(135deg, #42b983, #2f855a);
}

.template-card-header.bg-purple {
  background: linear-gradient(135deg, #9c27b0, #6a1b9a);
}

.template-card-header.bg-red {
  background: linear-gradient(135deg, #f44336, #c62828);
}

.template-card-header.bg-orange {
  background: linear-gradient(135deg, #ff9800, #ef6c00);
}

.template-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  position: relative;
  z-index: 2;
}

.template-card-header h4 {
  margin: 0;
  font-size: 1.2rem;
  color: white;
  position: relative;
  z-index: 2;
}

.template-card-body {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.template-card-body p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

.template-stats {
  display: flex;
  gap: 1rem;
}

.template-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-tertiary);
}

.template-stat svg {
  color: var(--neon-yellow);
}

.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.template-tag {
  font-size: 0.75rem;
  padding: 0.3rem 0.6rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-tertiary);
  transition: all 0.2s ease;
}

.template-tag:hover {
  background: rgba(77, 238, 234, 0.1);
  color: var(--neon-cyan);
}

.template-card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-btn {
  padding: 0.6rem 1.2rem;
  border-radius: 6px;
  background: rgba(77, 238, 234, 0.1);
  color: var(--neon-cyan);
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.template-btn:hover {
  background: rgba(77, 238, 234, 0.2);
  transform: translateY(-3px);
}

.template-btn-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-light);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-btn-icon:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.template-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.template-modal-content {
  width: 100%;
  max-width: 900px;
  background: var(--card-bg);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

.template-modal-header {
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.template-modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-light);
}

.modal-close {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 59, 48, 0.2);
  color: #ff3b30;
  transform: rotate(90deg);
}

.template-modal-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
}

.template-preview-large {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.template-preview-img {
  width: 100%;
  height: auto;
  display: block;
}

.template-modal-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.template-modal-info p {
  margin: 0;
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

.template-features-list h4, .template-compatibility h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: var(--text-light);
  position: relative;
  display: inline-block;
}

.template-features-list h4::after, .template-compatibility h4::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--neon-cyan);
  box-shadow: 0 0 10px var(--neon-cyan);
}

.template-features-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.template-features-list li {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 0.95rem;
  color: var(--text-secondary);
}

.template-features-list li svg {
  color: var(--neon-green);
}

.compatibility-icons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.platform-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: var(--text-light);
  transition: all 0.2s ease;
}

.platform-icon:hover {
  background: rgba(77, 238, 234, 0.1);
  color: var(--neon-cyan);
  transform: translateY(-3px);
}

.template-modal-footer {
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.template-btn.primary {
  background: var(--primary-color);
  color: white;
}

.template-btn.primary:hover {
  background: var(--secondary-color);
  box-shadow: 0 5px 15px rgba(66, 185, 131, 0.3);
}

.template-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
}

@media (max-width: 992px) {
  .template-hub-intro {
    grid-template-columns: 1fr;
  }

  .template-modal-body {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .hub-actions {
    flex-direction: column;
  }

  .template-card {
    flex: 0 0 260px;
  }
}

/* Modern scrollbar styling */
.home-container::-webkit-scrollbar {
  width: 10px;
}

.home-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}

.home-container::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--neon-blue), var(--neon-purple));
  border-radius: 5px;
}

.home-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--neon-cyan), var(--neon-blue));
}

/* Adding glass morphism to specific elements */
.profile-summary,
.stat-card,
.performance-chart {
  background: rgba(20, 20, 30, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Remaining styles (keep them) */
.welcome-card {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--elevation-2);
  padding: var(--space-xl);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-xl);
}

:root[data-theme="light"] .welcome-card {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
}

.welcome-content {
  display: flex;
  flex-direction: column;
}

.welcome-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--space-xs);
  line-height: 1.2;
}

.welcome-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin: 0 0 var(--space-xl);
}

.stats-row {
  display: flex;
  gap: var(--space-md);
  margin-top: auto;
}

.stat-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-md);
  padding: var(--space-md);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  transition: transform var(--transition-base);
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon-container {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(66, 185, 131, 0.2);
  color: var(--primary-500);
}

.stat-icon-container.success {
  background: rgba(40, 167, 69, 0.2);
  color: var(--success-500);
}

:root[data-theme="light"] .stat-icon-container {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.stat-icon {
  font-size: var(--font-size-xl);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

/* === VISÃO GERAL DO PROGRESSO ULTRA APRIMORADA === */
.progress-overview {
  position: relative;
  margin-bottom: 3rem;
  padding: 0;
  background: rgba(10, 12, 18, 0.85);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(77, 238, 234, 0.2);
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

/* Holographic border effect */
.progress-overview::before {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(130deg,
    var(--neon-blue) 0%,
    transparent 40%,
    transparent 60%,
    var(--neon-pink) 100%);
  border-radius: var(--border-radius-lg);
  z-index: -1;
  opacity: 0.7;
  animation: rotatingPrismaticBorder 8s linear infinite;
}

@keyframes rotatingPrismaticBorder {
  0% { filter: hue-rotate(0deg) brightness(1.5); }
  25% { filter: hue-rotate(90deg) brightness(1.2); }
  50% { filter: hue-rotate(180deg) brightness(1); }
  75% { filter: hue-rotate(270deg) brightness(1.2); }
  100% { filter: hue-rotate(360deg) brightness(1.5); }
}

/* Enhanced circuit pattern overlay */
.progress-overview::after {
  content: '';
  position: absolute;
  inset: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 10h80v80H10z' fill='none' stroke='rgba(77, 238, 234, 0.1)' stroke-width='1'/%3E%3Cpath d='M30 10v30h30' fill='none' stroke='rgba(77, 238, 234, 0.1)' stroke-width='1'/%3E%3Cpath d='M70 10v30H40' fill='none' stroke='rgba(77, 238, 234, 0.1)' stroke-width='1'/%3E%3Cpath d='M30 90v-30h30' fill='none' stroke='rgba(77, 238, 234, 0.1)' stroke-width='1'/%3E%3Cpath d='M70 90v-30H40' fill='none' stroke='rgba(77, 238, 234, 0.1)' stroke-width='1'/%3E%3Ccircle cx='10' cy='10' r='2' fill='rgba(77, 238, 234, 0.2)'/%3E%3Ccircle cx='90' cy='10' r='2' fill='rgba(77, 238, 234, 0.2)'/%3E%3Ccircle cx='10' cy='90' r='2' fill='rgba(77, 238, 234, 0.2)'/%3E%3Ccircle cx='90' cy='90' r='2' fill='rgba(77, 238, 234, 0.2)'/%3E%3C/svg%3E");
  opacity: 0.6;
  z-index: -1;
  animation: movingCircuit 100s linear infinite;
}

/* Hover effect for entire section */
.progress-overview:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.6),
              0 0 30px rgba(77, 238, 234, 0.3);
}

/* Enhanced heading with futuristic header */
.progress-overview h2 {
  margin: 0;
  padding: 1.5rem 2rem;
  background: rgba(5, 8, 16, 0.7);
  font-size: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-bottom: 1px solid rgba(77, 238, 234, 0.2);
  color: white;
  text-shadow: 0 0 10px var(--neon-cyan), 0 0 20px var(--neon-cyan);
  position: relative;
  clip-path: polygon(0 0, 100% 0, 100% 70%, 98% 100%, 0 100%);
}

/* Enhanced icon glow and animation */
.progress-overview h2 svg {
  filter: drop-shadow(0 0 8px var(--neon-cyan));
  animation: floatingIcon 3s ease-in-out infinite alternate;
}

@keyframes floatingIcon {
  0% { opacity: 0.7; transform: scale(0.95) rotate(-5deg); }
  100% { opacity: 1; transform: scale(1.1) rotate(5deg); }
}

/* Enhanced cyberpunk terminal UI style */
.progress-overview h2::before {
  position: absolute;
  right: 2rem;
  font-size: 0.8rem;
  font-family: monospace;
  color: var(--neon-cyan);
  opacity: 0.8;
  animation: typingEffect 4s steps(28) infinite;
  white-space: nowrap;
  overflow: hidden;
  width: 0;
}

@keyframes typingEffect {
  0% { width: 0; }
  50% { width: 28ch; }
  90% { width: 28ch; }
  100% { width: 0; }
}

/* Blinking indicator */
.progress-overview h2::after {
  content: '';
  position: absolute;
  right: 1rem;
  top: 50%;
  width: 12px;
  height: 12px;
  background-color: var(--neon-cyan);
  border-radius: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 10px var(--neon-cyan), 0 0 20px var(--neon-cyan);
  animation: pulsingIndicator 2s infinite;
}

@keyframes pulsingIndicator {
  0% { opacity: 1; box-shadow: 0 0 10px var(--neon-cyan), 0 0 20px var(--neon-cyan); }
  50% { opacity: 0.3; box-shadow: 0 0 5px var(--neon-cyan), 0 0 10px var(--neon-cyan); }
  100% { opacity: 1; box-shadow: 0 0 10px var(--neon-cyan), 0 0 20px var(--neon-cyan); }
}

/* Ultra-enhanced stats grid with holographic cards */
.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2px; /* Minimal gap to show divider glow */
  padding: 1rem;
  background: rgba(15, 18, 25, 0.7);
  position: relative;
}

/* Holographic grid background */
.progress-stats::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(to right, rgba(77, 238, 234, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(77, 238, 234, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  animation: gridPulse 4s infinite alternate;
}

@keyframes gridPulse {
  0% { opacity: 0.3; }
  100% { opacity: 0.8; }
}

/* Individual stat cards */
.progress-stats > * {
  position: relative;
  padding: 2rem;
  background: rgba(15, 20, 30, 0.6);
  border: 1px solid rgba(77, 238, 234, 0.15);
  border-radius: 8px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  backdrop-filter: blur(4px);
  z-index: 1;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Enhanced hover state for stat cards */
.progress-stats > *:hover {
  background: rgba(20, 25, 40, 0.8);
  transform: translateY(-8px) scale(1.05);
  z-index: 2;
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.3),
    0 0 20px var(--neon-cyan);
  border-color: var(--neon-cyan);
}

/* Advanced scanning line animation */
.progress-stats > *::before {
  content: '';
  position: absolute;
  top: -100%;
  left: 0;
  width: 100%;
  height: 200%;
  background: linear-gradient(to bottom,
              transparent,
              rgba(77, 238, 234, 0.15),
              transparent);
  z-index: 0;
  transform: translateY(0);
  animation: scanline 6s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes scanline {
  0% { transform: translateY(0%); }
  100% { transform: translateY(50%); }
}

/* Cyberpunk corner accents */
.progress-stats > *::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30px;
  height: 30px;
  border-right: 2px solid var(--neon-cyan);
  border-bottom: 2px solid var(--neon-cyan);
  opacity: 0.7;
  box-shadow: 5px 5px 10px rgba(77, 238, 234, 0.2);
  transition: all 0.3s ease;
}

.progress-stats > *:hover::after {
  width: 40px;
  height: 40px;
  opacity: 1;
  box-shadow: 5px 5px 15px rgba(77, 238, 234, 0.4);
}

/* Enhanced value display with holographic effect */
.progress-stats :deep(.value) {
  font-size: 2.8rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 0 15px var(--neon-cyan);
  display: block;
  margin-bottom: 0.75rem;
  background: linear-gradient(90deg, #ffffff, var(--neon-cyan), #ffffff);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  animation: shineText 5s linear infinite;
}

@keyframes shineText {
  0% { background-position: 0% center; }
  100% { background-position: 200% center; }
}

/* Data loading animation */
.progress-stats :deep(.value)::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -5px;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
              transparent,
              var(--neon-cyan),
              transparent);
  opacity: 0.7;
  animation: loadingBar 3s ease-in-out infinite;
}

/* Enhanced titles with icon */
.progress-stats :deep(.title) {
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  color: var(--neon-cyan);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  position: relative;
  text-shadow: 0 0 5px rgba(77, 238, 234, 0.5);
}

/* Enhanced icons with glow effect */
.progress-stats :deep(.icon) {
  color: var(--neon-cyan);
  filter: drop-shadow(0 0 5px var(--neon-cyan));
  font-size: 1.3rem;
  transition: all 0.3s ease;
}

.progress-stats > *:hover :deep(.icon) {
  transform: scale(1.2) rotate(10deg);
  filter: drop-shadow(0 0 10px var(--neon-cyan));
  animation: iconPulse 1.5s infinite alternate;
}

/* Enhanced descriptions */
.progress-stats :deep(.description) {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 0.75rem;
  line-height: 1.5;
  position: relative;
  padding-left: 1rem;
  border-left: 2px solid rgba(77, 238, 234, 0.3);
  transition: all 0.3s ease;
}

.progress-stats > *:hover :deep(.description) {
  opacity: 1;
  border-left: 2px solid var(--neon-cyan);
  box-shadow: -5px 0 10px rgba(77, 238, 234, 0.1);
}

/* Enhanced progress bar for percentage values */
.progress-stats :deep(.value[data-suffix="%"])::before {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  height: 4px;
  width: calc(1% * var(--raw-value));
  background: linear-gradient(90deg,
              var(--neon-cyan),
              var(--neon-blue));
  border-radius: 2px;
  box-shadow: 0 0 10px var(--neon-cyan);
  transition: width 1s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Data point indicator */
.progress-stats :deep(.value[data-suffix])::after {
  content: attr(data-suffix);
  position: absolute;
  right: -5px;
  top: 5px;
  font-size: 1rem;
  color: var(--neon-cyan);
  opacity: 0.8;
  text-shadow: 0 0 5px var(--neon-cyan);
}

/* Special vertical bar for 'hours' indicator */
.progress-stats :deep(.value[data-suffix="h"])::before {
  content: '';
  position: absolute;
  top: 10%;
  left: -15px;
  height: 80%;
  width: 4px;
  background: linear-gradient(to bottom,
              var(--neon-blue),
              var(--neon-cyan));
  border-radius: 2px;
  box-shadow: 0 0 10px var(--neon-cyan);
  opacity: 0.6;
  transition: all 0.3s ease;
}

.progress-stats > *:hover :deep(.value[data-suffix="h"])::before {
  opacity: 1;
  box-shadow: 0 0 15px var(--neon-cyan);
  height: 90%;
}

/* Mobile optimization */
@media (max-width: 768px) {
  .progress-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .progress-overview h2 {
    font-size: 1.3rem;
    padding: 1.2rem;
  }

  .progress-overview h2::before {
    content: '>> analytics';
    right: 1rem;
    animation: none;
    width: auto;
  }

  .progress-stats > * {
    padding: 1.5rem;
  }

  .progress-stats :deep(.value) {
    font-size: 2.2rem;
  }
}

/* ... existing code ... */

/* === DETALHES ADICIONAIS DO PROGRESSO SEMANAL === */
.weekly-detail {
  display: flex;
  padding: 1.5rem;
  margin-top: 1rem;
  gap: 2rem;
  background: rgba(15, 18, 25, 0.6);
  border-top: 1px solid rgba(77, 238, 234, 0.15);
  position: relative;
  overflow: hidden;
}

/* Efeito holográfico para essa seção também */
.weekly-detail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
  opacity: 0.7;
}

.weekly-chart {
  flex: 2;
  padding: 1rem;
  position: relative;
}

.chart-title {
  font-size: 1.1rem;
  color: var(--neon-cyan);
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  padding-left: 1.5rem;
}

.chart-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: var(--neon-cyan);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--neon-cyan);
  animation: pulsingDot 2s infinite;
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 180px;
  width: 100%;
  padding: 0 1rem;
}

.day-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(100% / 7 - 1rem);
  height: 100%;
  position: relative;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, var(--neon-blue), var(--neon-cyan));
  border-radius: 4px 4px 0 0;
  position: absolute;
  bottom: 30px;
  transition: height 1s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 0 10px rgba(77, 238, 234, 0.3);
}

.bar-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: white;
  opacity: 0.5;
  border-radius: 2px;
}

.bar-label {
  position: absolute;
  bottom: 0;
  font-size: 0.9rem;
  color: var(--text-light);
  opacity: 0.8;
}

.day-bar.today .bar-fill {
  background: linear-gradient(to top, var(--neon-pink), var(--neon-purple));
  box-shadow: 0 0 15px rgba(182, 91, 255, 0.4);
  animation: pulsingBar 2s infinite alternate;
}

.day-bar.today .bar-label {
  color: var(--neon-pink);
  text-shadow: 0 0 5px var(--neon-pink);
  font-weight: bold;
}

.stats-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 1rem;
  border-left: 1px solid rgba(77, 238, 234, 0.15);
}

.detail-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
  position: relative;
  padding-left: 1rem;
}

.detail-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--neon-cyan);
  opacity: 0.6;
}

.detail-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.detail-value {
  font-size: 1.4rem;
  color: white;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

/* Adicionando trend aos cards */
.progress-stats :deep(.trend) {
  font-size: 0.85rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.progress-stats :deep(.trend-positive) {
  color: var(--neon-green);
}

.progress-stats :deep(.trend-negative) {
  color: var(--neon-pink);
}

.progress-stats :deep(.trend)::before {
  content: '';
  width: 8px;
  height: 8px;
  background: currentColor;
  border-radius: 50%;
  display: inline-block;
  box-shadow: 0 0 8px currentColor;
}

/* Mobile responsiveness for new elements */
@media (max-width: 768px) {
  .weekly-detail {
    flex-direction: column;
    padding: 1rem;
  }

  .stats-detail {
    border-left: none;
    border-top: 1px solid rgba(77, 238, 234, 0.15);
    padding-top: 1rem;
    margin-top: 1rem;
  }

  .chart-bars {
    height: 150px;
  }
}

/* ... existing code ... */

/* AI Assistant Creative Styling */
.ai-assistant-creative {
  margin-bottom: 3rem;
  background: rgb(10, 12, 15);
  border-radius: 16px;
  border: 1px solid rgba(48, 54, 61, 0.5);
  position: relative;
  overflow: hidden;
}

.ai-background-effects {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.neural-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(66, 185, 131, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(66, 185, 131, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 10s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: #42b983;
  border-radius: 50%;
  opacity: 0.8;
}

.particle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation: floatParticle 6s infinite;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation: floatParticle 8s infinite;
  animation-delay: 1s;
}

.particle:nth-child(3) {
  top: 40%;
  left: 40%;
  animation: floatParticle 7s infinite;
  animation-delay: 2s;
}

.particle:nth-child(4) {
  top: 80%;
  left: 20%;
  animation: floatParticle 9s infinite;
  animation-delay: 3s;
}

@keyframes floatParticle {
  0%, 100% { transform: translate(0, 0) scale(0); opacity: 0; }
  20%, 80% { opacity: 0.8; }
  50% { transform: translate(30px, -30px) scale(1); }
}

.ai-container-creative {
  position: relative;
  padding: 2rem;
  z-index: 1;
}

.ai-header-creative {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
}

.ai-icon-creative {
  width: 80px;
  height: 80px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.3), transparent);
  border-radius: 50%;
  animation: glow 2s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.icon-core {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #42b983, #35a372);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  box-shadow: 0 8px 24px rgba(66, 185, 131, 0.4);
  z-index: 2;
  position: relative;
}

.icon-rings {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.ring {
  position: absolute;
  border: 2px solid rgba(66, 185, 131, 0.3);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring:nth-child(1) {
  width: 90px;
  height: 90px;
  animation: expandRing 3s ease-out infinite;
}

.ring:nth-child(2) {
  width: 100px;
  height: 100px;
  animation: expandRing 3s ease-out infinite;
  animation-delay: 1.5s;
}

@keyframes expandRing {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

.status-dot {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 16px;
  height: 16px;
  background: #42b983;
  border-radius: 50%;
  border: 3px solid rgb(10, 12, 15);
  animation: pulse 2s ease-in-out infinite;
}

.ai-title-creative {
  flex: 1;
}

.ai-title-creative h3 {
  margin: 0;
  font-size: 1.8rem;
  color: var(--color-text);
  font-weight: 600;
}

.status-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  background: #42b983;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.options-button-creative {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(48, 54, 61, 0.3);
  border: 1px solid rgba(48, 54, 61, 0.5);
  color: #8b949e;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.options-button-creative:hover {
  background: rgba(48, 54, 61, 0.5);
  color: #e6edf3;
  transform: scale(1.05);
}

.options-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background: rgba(66, 185, 131, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.options-button-creative:hover .options-glow {
  opacity: 1;
}

.options-menu-creative {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  background: rgb(10, 12, 15);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 8px;
  padding: 0.5rem;
  min-width: 160px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  z-index: 10;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  color: #e6edf3;
  cursor: pointer;
  transition: all 0.2s ease;
}

.option-item:hover {
  background: rgba(66, 185, 131, 0.1);
  color: #42b983;
}

.ai-display-creative {
  margin-bottom: 1.5rem;
}

.message-area {
  background: rgba(48, 54, 61, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  min-height: 80px;
  position: relative;
  overflow: hidden;
}

.message-area::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #42b983, transparent, #42b983);
  border-radius: 12px;
  opacity: 0.1;
  z-index: -1;
  animation: borderRotate 4s linear infinite;
}

@keyframes borderRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ai-text {
  margin: 0;
  color: #e6edf3;
  font-size: 1.05rem;
  line-height: 1.6;
}

.loading-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
}

.typing-dots {
  display: flex;
  gap: 8px;
}

.typing-dots span {
  width: 10px;
  height: 10px;
  background: #42b983;
  border-radius: 50%;
  animation: typingBounce 1.4s ease-in-out infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingBounce {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-15px); }
}

.suggestions-area {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.suggestion-chip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  background: rgba(66, 185, 131, 0.1);
  border: 1px solid rgba(66, 185, 131, 0.3);
  border-radius: 20px;
  color: #42b983;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.suggestion-chip:hover {
  background: rgba(66, 185, 131, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(66, 185, 131, 0.3);
}

.ai-interaction-creative {
  margin-bottom: 1.5rem;
}

.input-wrapper {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.ai-input-creative {
  flex: 1;
  background: rgba(48, 54, 61, 0.3);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 10px;
  padding: 0.875rem 1.25rem;
  color: #e6edf3;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.ai-input-creative::placeholder {
  color: #8b949e;
}

.ai-input-creative:focus {
  outline: none;
  border-color: #42b983;
  background: rgba(48, 54, 61, 0.4);
}

.send-button-creative {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  background: linear-gradient(135deg, #42b983, #35a372);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(66, 185, 131, 0.3);
}

.send-button-creative:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(66, 185, 131, 0.4);
}

.send-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  background: rgba(66, 185, 131, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.send-button-creative:hover .send-glow {
  opacity: 1;
}

.ai-actions-creative {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.action-card {
  background: rgba(48, 54, 61, 0.3);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 12px;
  padding: 1.25rem;
  text-align: center;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
}

.action-card:hover {
  transform: translateY(-2px);
  border-color: #42b983;
}

.action-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 0.75rem;
  background: rgba(66, 185, 131, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #42b983;
  transition: all 0.3s ease;
}

.action-card:hover .action-icon {
  background: rgba(66, 185, 131, 0.2);
  transform: scale(1.1);
}

.action-card span {
  display: block;
  color: #e6edf3;
  font-size: 0.95rem;
  font-weight: 500;
}

.action-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.action-card:hover .action-glow {
  opacity: 1;
}

/* Slide down transition */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Progress Bar Value Animation */
.progress-stats :deep(.value[data-suffix="%"])::before {
  animation: progressBarFill 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes progressBarFill {
  from { width: 0; }
  to { width: calc(1% * var(--raw-value)); }
}

/* Styling for weekly progress chart */
.weekly-detail {
  display: flex;
  padding: 1.5rem;
  margin-top: 1rem;
  gap: 2rem;
  background: rgba(15, 18, 25, 0.6);
  border-top: 1px solid rgba(77, 238, 234, 0.15);
  position: relative;
  overflow: hidden;
}

.weekly-detail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
  opacity: 0.7;
}

.weekly-chart {
  flex: 2;
  padding: 1rem;
  position: relative;
}

.chart-title {
  font-size: 1.1rem;
  color: var(--neon-cyan);
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  padding-left: 1.5rem;
}

.chart-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: var(--neon-cyan);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--neon-cyan);
  animation: pulsingDot 2s infinite;
}

@keyframes pulsingDot {
  0% { opacity: 0.5; transform: translateY(-50%) scale(0.8); }
  100% { opacity: 1; transform: translateY(-50%) scale(1.2); }
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 180px;
  width: 100%;
  padding: 0 1rem;
  position: relative;
}

.chart-bars::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 30px;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
}

.day-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(100% / 7 - 1rem);
  height: 100%;
  position: relative;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, var(--neon-blue), var(--neon-cyan));
  border-radius: 4px 4px 0 0;
  position: absolute;
  bottom: 30px;
  transition: height 1s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 0 10px rgba(77, 238, 234, 0.3);
  animation: barRise 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes barRise {
  from { height: 0; }
}

.bar-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: white;
  opacity: 0.5;
  border-radius: 2px;
}

.bar-label {
  position: absolute;
  bottom: 0;
  font-size: 0.9rem;
  color: var(--text-light);
  opacity: 0.8;
}

.bar-value {
  position: absolute;
  top: -25px;
  font-size: 0.85rem;
  color: var(--neon-cyan);
  opacity: 0;
  transition: opacity 0.3s;
}

.day-bar:hover .bar-value {
  opacity: 1;
}

.day-bar.today .bar-fill {
  background: linear-gradient(to top, var(--neon-pink), var(--neon-purple));
  box-shadow: 0 0 15px rgba(182, 91, 255, 0.4);
  animation: pulsingBar 2s infinite alternate;
}

@keyframes pulsingBar {
  0% { opacity: 0.8; box-shadow: 0 0 5px rgba(182, 91, 255, 0.4); }
  100% { opacity: 1; box-shadow: 0 0 15px rgba(182, 91, 255, 0.8); }
}

.day-bar.today .bar-label {
  color: var(--neon-pink);
  text-shadow: 0 0 5px var(--neon-pink);
  font-weight: bold;
}

.stats-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 1rem;
  border-left: 1px solid rgba(77, 238, 234, 0.15);
}

.detail-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
  position: relative;
  padding-left: 1rem;
}

.detail-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--neon-cyan);
  opacity: 0.6;
}

.detail-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.detail-value {
  font-size: 1.4rem;
  color: white;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

/* Responsive styling for new components */
@media (max-width: 768px) {
  .ai-card-container {
    flex-direction: column;
    text-align: center;
  }

  .ai-avatar {
    margin-right: 0;
    margin-bottom: 1.5rem;
  }

  .ai-buttons {
    flex-direction: column;
    width: 100%;
  }

  .weekly-detail {
    flex-direction: column;
  }

  .stats-detail {
    border-left: none;
    border-top: 1px solid rgba(77, 238, 234, 0.15);
    padding-top: 1.5rem;
    margin-top: 1rem;
  }

  .chart-bars {
    height: 150px;
  }
}

/* Trend indicator for stat cards */
.progress-stats :deep(.trend) {
  font-size: 0.85rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.progress-stats :deep(.trend-positive) {
  color: var(--neon-green);
}

.progress-stats :deep(.trend-negative) {
  color: var(--neon-pink);
}

.progress-stats :deep(.trend)::before {
  content: '';
  width: 8px;
  height: 8px;
  background: currentColor;
  border-radius: 50%;
  display: inline-block;
  box-shadow: 0 0 8px currentColor;
}


/* Stats Details Styles */
.stat-trend {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.8rem;
  padding: 0.3rem 0.6rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
}

.stat-trend.up {
  color: #4cd964;
}

.stat-trend.down {
  color: #ff3b30;
}

.stat-details {
  margin-top: 1.5rem;
  background: rgba(30, 30, 40, 0.7);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.05);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.stat-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(20, 20, 30, 0.7);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.stat-details-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-light);
}

.close-details-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-details-btn:hover {
  background: rgba(255, 59, 48, 0.2);
  color: #ff3b30;
  transform: rotate(90deg);
}

.stat-details-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  padding: 1.5rem;
}

.stat-chart {
  height: 200px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, rgba(77, 238, 234, 0.2), rgba(77, 238, 234, 0.8));
  border-radius: 4px 4px 0 0;
  position: relative;
  min-height: 10px;
  transition: height 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.bar-value {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
}

.chart-labels span {
  flex: 1;
  text-align: center;
  font-size: 0.8rem;
  color: var(--text-tertiary);
}

.stat-insights {
  display: flex;
  flex-direction: column;
}

.stat-insights h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: var(--text-light);
  position: relative;
  display: inline-block;
}

.stat-insights h4::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--neon-cyan);
  box-shadow: 0 0 10px var(--neon-cyan);
}

.stat-insights ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.stat-insights li {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.4;
  position: relative;
  padding-left: 1.2rem;
}

.stat-insights li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--neon-cyan);
  font-size: 1.2rem;
}

/* Achievement Section Styling */
.achievements-section {
  position: relative;
  margin-bottom: 3rem;
  padding: 1.8rem;
  background: rgba(10, 10, 20, 0.7);
  border: 1px solid rgba(77, 238, 234, 0.2);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.achievements-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at top left, rgba(255, 159, 28, 0.1), transparent 60%),
    radial-gradient(circle at bottom right, rgba(255, 54, 171, 0.1), transparent 60%);
  z-index: -1;
}

.achievements-section h2 {
  text-transform: uppercase;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--neon-orange);
  text-shadow: 0 0 10px var(--neon-orange);
  animation: pulsatingHeader 4s infinite alternate;
}

@keyframes pulsatingHeader {
  0% { text-shadow: 0 0 5px var(--neon-orange); }
  100% { text-shadow: 0 0 15px var(--neon-orange), 0 0 30px var(--neon-orange); }
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  perspective: 1000px;
}

.achievement-card {
  background: rgba(20, 20, 30, 0.8);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  transform-style: preserve-3d;
}

.achievement-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, 
    var(--neon-yellow),
    var(--neon-orange),
    var(--neon-pink),
    var(--neon-purple));
  z-index: -1;
  border-radius: 13px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.achievement-card:hover {
  transform: translateY(-10px) rotateX(5deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.achievement-card:hover::before {
  opacity: 1;
  animation: rotateGradient 3s linear infinite;
}

@keyframes rotateGradient {
  0% { background-position: 0% 0%; }
  100% { background-position: 100% 100%; }
}

.achievement-icon {
  font-size: 2.2rem;
  margin-bottom: 1rem;
  color: var(--neon-yellow);
  text-shadow: 0 0 8px var(--neon-yellow);
  transition: all 0.3s ease;
}

.achievement-card:hover .achievement-icon {
  transform: scale(1.2) translateZ(20px);
  text-shadow: 0 0 15px var(--neon-yellow);
}

.achievement-content h4 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #fff;
  transition: transform 0.3s ease;
}

.achievement-card:hover .achievement-content h4 {
  transform: translateZ(15px);
  color: var(--neon-orange);
  text-shadow: 0 0 5px var(--neon-orange);
}

.achievement-content p {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 1rem;
  transition: transform 0.3s ease;
}

.achievement-card:hover .achievement-content p {
  transform: translateZ(10px);
  opacity: 1;
}

.achievement-progress {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
  position: relative;
}

.achievement-progress .progress-fill {
  height: 100%;
  background: var(--neon-orange);
  transition: width 1s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 0 10px var(--neon-orange);
  position: relative;
  overflow: hidden;
}

.achievement-progress .progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%);
  animation: shimmerEffect 1.5s infinite;
}

@keyframes shimmerEffect {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.achievement-progress-label {
  font-size: 0.8rem;
  color: var(--text-light);
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.achievement-card:hover .achievement-progress-label {
  opacity: 1;
}

/* Enhanced section heading animations for all sections */
section h2 {
  position: relative;
  overflow: hidden;
  animation: textGlow 3s infinite alternate;
}

@keyframes textGlow {
  0% { text-shadow: 0 0 5px currentColor; }
  100% { text-shadow: 0 0 15px currentColor, 0 0 30px currentColor; }
}

section h2::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 50px;
  height: 2px;
  background: currentColor;
  box-shadow: 0 0 10px currentColor;
  animation: lineExpand 3s ease-in-out infinite alternate;
}

@keyframes lineExpand {
  0% { width: 30px; opacity: 0.5; }
  100% { width: 80px; opacity: 1; }
}

/* Enhanced 3D effects for status cards */
.status-card {
  transform-style: preserve-3d;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.status-card:hover {
  transform: translateY(-15px) rotateX(10deg) scale(1.03);
}

/* Enhanced quick action cards */
.quick-actions .actions-grid {
  perspective: 1000px;
}

/* Enhanced resource cards */
.resource-card {
  transform-style: preserve-3d;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.resource-card:hover {
  transform: translateY(-15px) scale(1.05);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.resource-card:hover .resource-icon {
  transform: translateZ(30px) scale(1.3) rotate(10deg);
  box-shadow: 0 0 30px rgba(77, 238, 234, 0.7);
}

/* Enhanced mobile responsiveness */
@media (max-width: 768px) {
  .achievements-grid {
    grid-template-columns: 1fr;
  }
  
  .achievement-card:hover {
    transform: translateY(-5px);
  }
}

/* Next Revisions Section */
.next-revisions {
  margin-bottom: 3rem;
  background: rgb(10, 12, 15);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 16px;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: rgba(10, 12, 15, 0.95);
  border-bottom: 1px solid rgba(48, 54, 61, 0.5);
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.4rem;
  color: #e6edf3;
  margin: 0;
  font-weight: 600;
}

.section-header h2 svg {
  font-size: 1.2rem;
  color: #42b983;
}

.view-all-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #8b949e;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.view-all-link:hover {
  color: #42b983;
}

.view-all-link svg {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.view-all-link:hover svg {
  transform: translateX(4px);
}

.revisions-container {
  padding: 1.5rem;
  min-height: 250px;
  position: relative;
}

/* Flashcard Preview Section */
.flashcard-preview {
  margin-bottom: 3rem;
  background: rgb(10, 12, 15);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 16px;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
}

.flashcard-container {
  padding: 1.5rem;
  min-height: 300px;
  position: relative;
}

/* Study Tips Section */
.study-tips {
  margin-bottom: 3rem;
  background: rgb(10, 12, 15);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 16px;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
}

.study-tips .section-header {
  padding: 1.5rem 2rem;
  background: rgba(10, 12, 15, 0.95);
  border-bottom: 1px solid rgba(48, 54, 61, 0.5);
}

.study-tips .section-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.4rem;
  color: #e6edf3;
  margin: 0;
  font-weight: 600;
}

.study-tips .section-header h2 svg {
  font-size: 1.2rem;
  color: #f59e0b;
}

.tips-container {
  padding: 1.5rem;
}

/* AI Resources Section - Creative Design */
.ai-resources-creative {
  margin-bottom: 3rem;
  background: rgb(10, 12, 15);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 16px;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
}

.ai-resources-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: rgba(10, 12, 15, 0.95);
  border-bottom: 1px solid rgba(48, 54, 61, 0.5);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.ai-icon-container {
  position: relative;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-icon {
  font-size: 1.5rem;
  color: #3b82f6;
  position: relative;
  z-index: 3;
}

.ai-icon-glow {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.4), transparent);
  filter: blur(15px);
  animation: pulse 3s ease-in-out infinite;
}

.ai-icon-pulse {
  position: absolute;
  inset: -15px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  animation: pulse 2s ease-out infinite;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.section-title {
  font-size: 1.4rem;
  color: #e6edf3;
  margin: 0;
  font-weight: 600;
}

.section-subtitle {
  font-size: 0.9rem;
  color: #8b949e;
  margin: 0;
}

.ai-badge {
  position: relative;
}

.badge-inner {
  position: relative;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 50px;
  overflow: hidden;
}

.badge-text {
  font-size: 0.85rem;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 2;
}

.badge-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 3s infinite;
}

/* Resources Grid */
.resources-grid-creative {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

.resource-card-creative {
  position: relative;
  background: rgba(30, 30, 30, 0.9);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
}

.resource-card-creative:hover {
  transform: translateY(-8px);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0;
}

.resource-icon-creative {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  overflow: hidden;
}

.icon-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: floatParticle 3s ease-in-out infinite;
}

.icon-particle:nth-child(2) {
  top: 10%;
  left: 20%;
  animation-delay: 0.5s;
}

.icon-particle:nth-child(3) {
  top: 70%;
  left: 80%;
  animation-delay: 1s;
}

.icon-particle:nth-child(4) {
  top: 30%;
  right: 10%;
  animation-delay: 1.5s;
}

.resource-type {
  padding: 0.25rem 0.75rem;
  background: rgba(139, 148, 158, 0.1);
  border: 1px solid rgba(139, 148, 158, 0.3);
  border-radius: 50px;
  font-size: 0.8rem;
  color: #8b949e;
  font-weight: 600;
}

.resource-content-creative {
  padding: 1.5rem;
}

.resource-title {
  font-size: 1.1rem;
  color: #e6edf3;
  margin: 0 0 0.75rem 0;
  font-weight: 600;
  line-height: 1.4;
}

.resource-description {
  font-size: 0.95rem;
  color: #8b949e;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.resource-tags-creative {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.resource-tag-creative {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.875rem;
  background: rgba(66, 185, 131, 0.1);
  border: 1px solid rgba(66, 185, 131, 0.3);
  border-radius: 50px;
  font-size: 0.85rem;
  color: #42b983;
  animation: fadeIn 0.4s ease-out forwards;
  opacity: 0;
}

.tag-icon {
  font-size: 0.7rem;
  opacity: 0.8;
}

.resource-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(48, 54, 61, 0.3);
}

.resource-stats {
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.85rem;
  color: #8b949e;
}

.stat-item svg {
  color: #f59e0b;
}

.resource-link-creative {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  background: linear-gradient(135deg, #42b983, #35a372);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  overflow: hidden;
}

.resource-link-creative:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(66, 185, 131, 0.3);
}

.resource-link-creative svg {
  font-size: 0.85rem;
  transition: transform 0.3s ease;
}

.resource-link-creative:hover svg {
  transform: translateX(3px);
}

.link-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.resource-link-creative:hover .link-glow {
  transform: translateX(100%);
}

.card-glow {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  opacity: 0.03;
  pointer-events: none;
  transform: rotate(45deg);
}

/* AI Recommendation Footer */
.ai-recommendation-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: rgba(10, 12, 15, 0.95);
  border-top: 1px solid rgba(48, 54, 61, 0.5);
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: #3b82f6;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-button:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.refresh-button svg {
  transition: transform 0.3s ease;
}

.refresh-button:hover svg {
  transform: rotate(180deg);
}

.ai-confidence {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #8b949e;
  font-size: 0.9rem;
  margin: 0;
}

.ai-confidence svg {
  color: #42b983;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes floatParticle {
  0%, 100% {
    transform: translate(0, 0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  50% {
    transform: translate(10px, -10px);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .resources-grid-creative {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .ai-resources-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .header-left {
    flex-direction: column;
  }
  
  .ai-recommendation-footer {
    flex-direction: column;
    gap: 1rem;
  }
}

/* Getting Started Section - Creative Design */
.getting-started-creative {
  margin-bottom: 3rem;
  background: rgb(10, 12, 15);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 16px;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
}

.getting-started-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: rgba(10, 12, 15, 0.95);
  border-bottom: 1px solid rgba(48, 54, 61, 0.5);
}

.header-icon-container {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-icon {
  font-size: 1.75rem;
  color: #f59e0b;
  position: relative;
  z-index: 3;
}

.icon-glow {
  position: absolute;
  inset: -12px;
  background: radial-gradient(circle at center, rgba(245, 158, 11, 0.4), transparent);
  filter: blur(15px);
  animation: pulse 3s ease-in-out infinite;
}

.icon-particles {
  position: absolute;
  inset: -20px;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #f59e0b;
  border-radius: 50%;
  animation: floatAround 6s ease-in-out infinite;
}

.particle:nth-child(1) { top: 0; left: 50%; }
.particle:nth-child(2) { top: 50%; right: 0; }
.particle:nth-child(3) { bottom: 0; left: 50%; }
.particle:nth-child(4) { top: 50%; left: 0; }

.header-content {
  flex: 1;
  margin-left: 1rem;
}

.section-title {
  font-size: 1.4rem;
  color: #e6edf3;
  margin: 0;
  font-weight: 600;
}

.section-description {
  font-size: 0.9rem;
  color: #8b949e;
  margin: 0.25rem 0 0 0;
}

/* Progress Indicator */
.progress-indicator {
  position: relative;
}

.progress-ring {
  position: relative;
  width: 60px;
  height: 60px;
}

.progress-ring svg {
  transform: rotate(-90deg);
}

.progress-ring circle:last-child {
  transition: stroke-dashoffset 0.6s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.9rem;
  font-weight: 600;
  color: #e6edf3;
}

/* Steps Container */
.steps-container-creative {
  padding: 2rem;
  position: relative;
}

.timeline-track {
  position: absolute;
  left: 2.5rem;
  top: 3rem;
  bottom: 3rem;
  width: 2px;
  background: rgba(48, 54, 61, 0.5);
}

.step-item-creative {
  position: relative;
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  animation: slideInRight 0.6s ease-out forwards;
  opacity: 0;
}

.step-item-creative:last-child {
  margin-bottom: 0;
}

/* Step Connector */
.step-connector {
  position: relative;
  width: 50px;
  flex-shrink: 0;
}

.connector-line {
  position: absolute;
  left: 24px;
  top: 40px;
  width: 2px;
  height: calc(100% + 32px);
  background: rgba(48, 54, 61, 0.5);
  transition: background 0.3s ease;
}

.step-item-creative:last-child .connector-line {
  display: none;
}

.connector-line.completed {
  background: #42b983;
}

.step-number-creative {
  position: relative;
  width: 50px;
  height: 50px;
  background: rgba(48, 54, 61, 0.8);
  border: 2px solid rgba(48, 54, 61, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
  color: #8b949e;
  z-index: 2;
  transition: all 0.3s ease;
}

.step-number-creative.completed {
  background: #42b983;
  border-color: #42b983;
  color: white;
  box-shadow: 0 0 20px rgba(66, 185, 131, 0.4);
}

.number-glow {
  position: absolute;
  inset: -8px;
  border-radius: 50%;
  background: radial-gradient(circle at center, rgba(66, 185, 131, 0.3), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.step-number-creative.completed .number-glow {
  opacity: 1;
  animation: pulse 2s ease-in-out infinite;
}

/* Step Content */
.step-content-creative {
  flex: 1;
  background: rgba(30, 30, 30, 0.6);
  border: 1px solid rgba(48, 54, 61, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.step-item-creative.completed .step-content-creative {
  background: rgba(30, 30, 30, 0.8);
  border-color: rgba(66, 185, 131, 0.3);
}

.step-item-creative.active .step-content-creative {
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.1);
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.step-title {
  font-size: 1.1rem;
  color: #e6edf3;
  margin: 0;
  font-weight: 600;
  line-height: 1.4;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.step-description {
  font-size: 0.95rem;
  color: #8b949e;
  line-height: 1.5;
  margin: 0 0 1rem 0;
}

.step-footer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.step-action-creative {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  background: linear-gradient(135deg, #42b983, #35a372);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  overflow: hidden;
}

.step-action-creative:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(66, 185, 131, 0.3);
}

.step-action-creative svg {
  font-size: 0.85rem;
  transition: transform 0.3s ease;
}

.step-action-creative:hover svg {
  transform: translateX(3px);
}

.action-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.step-action-creative:hover .action-glow {
  transform: translateX(100%);
}

.step-completed-creative {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #42b983;
  font-weight: 600;
  position: relative;
}

.completed-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #f59e0b;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  color: white;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.4);
}

.step-time {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #8b949e;
  font-size: 0.85rem;
  margin-left: auto;
}

.step-decoration {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  opacity: 0.03;
  pointer-events: none;
  transform: rotate(45deg);
}

/* Getting Started Footer */
.getting-started-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: rgba(10, 12, 15, 0.95);
  border-top: 1px solid rgba(48, 54, 61, 0.5);
}

.motivation-quote {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.quote-icon {
  font-size: 1.2rem;
  color: #f59e0b;
  opacity: 0.6;
}

.quote-text {
  font-size: 0.95rem;
  color: #8b949e;
  font-style: italic;
  margin: 0;
}

.skip-tutorial {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  background: rgba(139, 148, 158, 0.1);
  border: 1px solid rgba(139, 148, 158, 0.3);
  border-radius: 8px;
  color: #8b949e;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.skip-tutorial:hover {
  background: rgba(139, 148, 158, 0.2);
  transform: translateY(-2px);
  border-color: rgba(139, 148, 158, 0.5);
}

/* Animations */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes floatAround {
  0%, 100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10px, -10px);
  }
  50% {
    transform: translate(0, -15px);
  }
  75% {
    transform: translate(-10px, -10px);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .getting-started-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .header-content {
    margin-left: 0;
  }
  
  .getting-started-footer {
    flex-direction: column;
    gap: 1rem;
  }
  
  .motivation-quote {
    text-align: center;
  }
  
  .step-content-creative {
    padding: 1rem;
  }
  
  .step-footer {
    flex-wrap: wrap;
  }
}
</style>