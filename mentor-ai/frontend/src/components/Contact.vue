<template>
  <div class="contact-page">
    <div class="page-header">
      <h1>
        <font-awesome-icon icon="fa-envelope" />
        Fale Conosco
      </h1>
      <p>Estamos aqui para ajudar com suas dúvidas</p>
    </div>
    
    <div class="contact-container">
      <form @submit.prevent="handleSubmit" class="contact-form">
        <div class="form-group">
          <label for="name">Nome</label>
          <input 
            type="text" 
            id="name" 
            v-model="form.name" 
            required 
            placeholder="Seu nome completo"
          />
        </div>
        
        <div class="form-group">
          <label for="email">E-mail</label>
          <input 
            type="email" 
            id="email" 
            v-model="form.email" 
            required 
            placeholder="<EMAIL>"
          />
        </div>
        
        <div class="form-group">
          <label for="subject">Assunto</label>
          <select id="subject" v-model="form.subject" required>
            <option value="">Selecione um assunto</option>
            <option value="support">Suporte Técnico</option>
            <option value="feedback">Feedback</option>
            <option value="suggestion">Sugestão</option>
            <option value="other">Outro</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="message">Mensagem</label>
          <textarea 
            id="message" 
            v-model="form.message" 
            required 
            rows="5"
            placeholder="Descreva sua mensagem..."
          ></textarea>
        </div>
        
        <button type="submit" class="submit-btn">
          <font-awesome-icon icon="fa-paper-plane" />
          Enviar Mensagem
        </button>
      </form>
      
      <div class="contact-info">
        <h3>Outras formas de contato</h3>
        <div class="info-item">
          <font-awesome-icon icon="fa-envelope" />
          <span><EMAIL></span>
        </div>
        <div class="info-item">
          <font-awesome-icon icon="fa-clock" />
          <span>Resposta em até 24h</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Contact',
  data() {
    return {
      form: {
        name: '',
        email: '',
        subject: '',
        message: ''
      }
    }
  },
  methods: {
    handleSubmit() {
      console.log('Form submitted:', this.form)
      // Implementar envio do formulário
      alert('Mensagem enviada com sucesso!')
      this.resetForm()
    },
    resetForm() {
      this.form = {
        name: '',
        email: '',
        subject: '',
        message: ''
      }
    }
  }
}
</script>

<style scoped>
.contact-page {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.contact-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
}

.contact-form {
  background: var(--card-bg);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--input-bg);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--accent-color);
}

.submit-btn {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.contact-info {
  background: var(--card-bg);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
  height: fit-content;
}

.contact-info h3 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.info-item svg {
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .contact-container {
    grid-template-columns: 1fr;
  }
}
</style>