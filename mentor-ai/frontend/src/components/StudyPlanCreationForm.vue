<template>
  <div class="study-plan-creation-form">
    <form @submit.prevent="submitPlan" class="plan-form">
      <!-- Basic Information -->
      <div class="form-section">
        <h3>
          <i class="fas fa-info-circle"></i>
          Informações Básicas
        </h3>
        
        <div class="form-group">
          <label for="planName">Nome do Plano *</label>
          <input 
            id="planName"
            v-model="planData.name" 
            type="text" 
            placeholder="Ex: Anatomia Humana"
            required
            maxlength="100"
          />
        </div>
        
        <div class="form-group">
          <label for="planDescription">Descrição</label>
          <textarea 
            id="planDescription"
            v-model="planData.description" 
            placeholder="Descreva os objetivos e conteúdo do plano..."
            rows="3"
            maxlength="500"
          ></textarea>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="category">Categoria</label>
            <select id="category" v-model="planData.category">
              <option value="">Selecione uma categoria</option>
              <option value="medicina">Medicina</option>
              <option value="biologia">Biologia</option>
              <option value="quimica">Química</option>
              <option value="fisica">Física</option>
              <option value="matematica">Matemática</option>
              <option value="outras">Outras</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="difficulty">Dificuldade</label>
            <select id="difficulty" v-model="planData.difficulty">
              <option value="facil">Fácil</option>
              <option value="medio">Médio</option>
              <option value="dificil">Difícil</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Schedule Information -->
      <div class="form-section">
        <h3>
          <i class="fas fa-calendar-alt"></i>
          Cronograma
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="startDate">Data de Início</label>
            <input 
              id="startDate"
              v-model="planData.startDate" 
              type="date"
              :min="today"
            />
          </div>
          
          <div class="form-group">
            <label for="endDate">Data Final</label>
            <input 
              id="endDate"
              v-model="planData.endDate" 
              type="date"
              :min="planData.startDate || today"
            />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="dailyHours">Horas por Dia</label>
            <input 
              id="dailyHours"
              v-model.number="planData.dailyHours" 
              type="number" 
              min="0.5"
              max="12"
              step="0.5"
              placeholder="2"
            />
          </div>
          
          <div class="form-group">
            <label for="totalHours">Total de Horas</label>
            <input 
              id="totalHours"
              v-model.number="planData.totalHours" 
              type="number" 
              min="1"
              placeholder="40"
            />
          </div>
        </div>
      </div>

      <!-- Visual Customization -->
      <div class="form-section">
        <h3>
          <i class="fas fa-palette"></i>
          Personalização Visual
        </h3>
        
        <div class="form-group">
          <label>Ícone</label>
          <div class="icon-selector">
            <button 
              v-for="icon in iconOptions" 
              :key="icon.value"
              type="button"
              @click="planData.icon = icon.value"
              :class="['icon-option', { selected: planData.icon === icon.value }]"
              :title="icon.label"
            >
              <i :class="icon.value"></i>
            </button>
          </div>
        </div>
        
        <div class="form-group">
          <label>Cor do Tema</label>
          <div class="color-selector">
            <button 
              v-for="color in colorOptions" 
              :key="color.value"
              type="button"
              @click="planData.color = color.value"
              :class="['color-option', { selected: planData.color === color.value }]"
              :style="{ background: color.value }"
              :title="color.name"
            >
              <i v-if="planData.color === color.value" class="fas fa-check"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Objectives -->
      <div class="form-section">
        <h3>
          <i class="fas fa-bullseye"></i>
          Objetivos de Aprendizagem
        </h3>
        
        <div class="objectives-list">
          <div v-for="(objective, index) in planData.objectives" :key="index" class="objective-item">
            <input 
              v-model="objective.text" 
              type="text" 
              placeholder="Digite um objetivo..."
              class="objective-input"
            />
            <button 
              type="button" 
              @click="removeObjective(index)" 
              class="remove-objective"
              :disabled="planData.objectives.length === 1"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <button type="button" @click="addObjective" class="add-objective">
            <i class="fas fa-plus"></i>
            Adicionar Objetivo
          </button>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button type="button" @click="$emit('back')" class="btn-secondary">
          <i class="fas fa-arrow-left"></i>
          Cancelar
        </button>
        <button type="submit" class="btn-primary" :disabled="!isFormValid">
          <i class="fas fa-save"></i>
          Criar Plano
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import studyAssistantService from '@/services/studyAssistantService'

export default {
  name: 'StudyPlanCreationForm',
  emits: ['back', 'plan-created'],
  setup(props, { emit }) {
    const today = new Date().toISOString().split('T')[0]
    
    const planData = ref({
      name: '',
      description: '',
      category: '',
      difficulty: 'medio',
      startDate: today,
      endDate: '',
      dailyHours: 2,
      totalHours: 40,
      icon: 'fas fa-book',
      color: 'linear-gradient(135deg, #667eea, #764ba2)',
      objectives: [{ text: '' }]
    })

    const iconOptions = ref([
      { value: 'fas fa-book', label: 'Livro' },
      { value: 'fas fa-brain', label: 'Cérebro' },
      { value: 'fas fa-flask', label: 'Química' },
      { value: 'fas fa-heart', label: 'Medicina' },
      { value: 'fas fa-pills', label: 'Farmacologia' },
      { value: 'fas fa-dna', label: 'Genética' },
      { value: 'fas fa-bone', label: 'Anatomia' },
      { value: 'fas fa-microscope', label: 'Laboratório' }
    ])

    const colorOptions = ref([
      { name: 'Azul Roxo', value: 'linear-gradient(135deg, #667eea, #764ba2)' },
      { name: 'Rosa Vermelho', value: 'linear-gradient(135deg, #f093fb, #f5576c)' },
      { name: 'Azul Ciano', value: 'linear-gradient(135deg, #4facfe, #00f2fe)' },
      { name: 'Rosa Amarelo', value: 'linear-gradient(135deg, #fa709a, #fee140)' },
      { name: 'Verde Azul', value: 'linear-gradient(135deg, #30cfd0, #330867)' },
      { name: 'Laranja Vermelho', value: 'linear-gradient(135deg, #ff9a9e, #fecfef)' }
    ])

    const isFormValid = computed(() => {
      return planData.value.name.trim().length > 0 &&
             planData.value.startDate &&
             planData.value.dailyHours > 0 &&
             planData.value.totalHours > 0
    })

    const addObjective = () => {
      planData.value.objectives.push({ text: '' })
    }

    const removeObjective = (index) => {
      if (planData.value.objectives.length > 1) {
        planData.value.objectives.splice(index, 1)
      }
    }

    const submitPlan = async () => {
      if (!isFormValid.value) return

      try {
        // Prepare data for API
        const apiData = {
          name: planData.value.name,
          description: planData.value.description,
          category: planData.value.category,
          difficulty: planData.value.difficulty,
          start_date: planData.value.startDate,
          end_date: planData.value.endDate,
          daily_hours: planData.value.dailyHours,
          total_hours: planData.value.totalHours,
          icon: planData.value.icon,
          color: planData.value.color,
          objectives: planData.value.objectives.filter(obj => obj.text.trim()),
          notifications: {
            enabled: true,
            daily: true,
            deadline: true,
            progress: false
          }
        }

        // Try to save to backend API first
        let newPlan
        try {
          newPlan = await studyAssistantService.createStudyPlan(apiData)
          console.log('Plan saved to backend:', newPlan)
        } catch (apiError) {
          console.warn('Backend API not available, saving locally:', apiError)
          // Fallback to local storage
          newPlan = {
            id: Date.now().toString(),
            ...apiData,
            progress: 0,
            completed_tasks: 0,
            total_tasks: apiData.objectives.length,
            hours_completed: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            is_active: true
          }
        }

        // Always save to localStorage as backup
        const savedPlans = JSON.parse(localStorage.getItem('activePlans') || '[]')
        const localPlan = {
          id: newPlan.id,
          name: newPlan.name,
          description: newPlan.description,
          category: newPlan.category,
          difficulty: newPlan.difficulty,
          startDate: newPlan.start_date,
          endDate: newPlan.end_date,
          dailyHours: newPlan.daily_hours,
          estimatedTime: newPlan.total_hours,
          icon: newPlan.icon,
          color: newPlan.color,
          objectives: newPlan.objectives,
          progress: newPlan.progress || 0,
          completedTasks: newPlan.completed_tasks || 0,
          totalTasks: newPlan.total_tasks || newPlan.objectives.length,
          hoursCompleted: newPlan.hours_completed || 0,
          deadline: newPlan.end_date,
          createdAt: newPlan.created_at,
          isActive: newPlan.is_active !== false
        }

        savedPlans.push(localPlan)
        localStorage.setItem('activePlans', JSON.stringify(savedPlans))

        emit('plan-created', localPlan)
      } catch (error) {
        console.error('Error creating plan:', error)
        alert('Erro ao criar plano. Tente novamente.')
      }
    }

    return {
      today,
      planData,
      iconOptions,
      colorOptions,
      isFormValid,
      addObjective,
      removeObjective,
      submitPlan
    }
  }
}
</script>

<style scoped>
.study-plan-creation-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  background: rgba(255, 255, 255, 0.03);
  padding: 1.5rem;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.form-section h3 {
  color: #fff;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-section h3 i {
  color: #667eea;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: block;
  color: #e2e8f0;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.icon-selector,
.color-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.icon-option,
.color-option {
  width: 50px;
  height: 50px;
  border: 2px solid transparent;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.icon-option:hover,
.color-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.icon-option.selected,
.color-option.selected {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.objectives-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.objective-item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.objective-input {
  flex: 1;
}

.remove-objective {
  width: 40px;
  height: 40px;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-objective:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.3);
}

.remove-objective:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-objective {
  padding: 0.75rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 10px;
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-objective:hover {
  background: rgba(102, 126, 234, 0.3);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-secondary,
.btn-primary {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
