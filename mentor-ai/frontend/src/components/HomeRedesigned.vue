<template>
  <div class="home-ultra">
    <!-- Animated Background -->
    <div class="background-animation">
      <div class="neural-network"></div>
      <div class="floating-particles"></div>
    </div>

    <!-- Ultra Modern Header -->
    <header class="ultra-header">
      <div class="header-content">
        <div class="user-welcome">
          <div class="avatar-system">
            <div class="avatar-pulse"></div>
            <div class="avatar-main">
              <span>{{ userInitials }}</span>
            </div>
            <div class="status-indicator online"></div>
          </div>
          
          <div class="welcome-info">
            <h1 class="welcome-text">
              Olá, <span class="user-name">{{ userName }}</span>!
            </h1>
            <p class="welcome-subtitle">
              <span class="ai-indicator">
                <i class="fas fa-robot"></i>
                IA ativa
              </span>
              <span class="separator">•</span>
              <span class="study-time">{{ totalStudyHours }}h de estudo</span>
              <span class="separator">•</span>
              <span class="streak-info">
                <i class="fas fa-fire"></i>
                {{ streakDays }} dias
              </span>
            </p>
          </div>
        </div>

        <div class="quick-stats">
          <div class="stat-bubble" v-for="stat in quickStats" :key="stat.id">
            <i :class="stat.icon"></i>
            <div class="stat-info">
              <span class="stat-value">{{ stat.value }}</span>
              <span class="stat-label">{{ stat.label }}</span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- AI Command Center -->
    <section class="ai-command-center">
      <div class="section-header">
        <h2>
          <i class="fas fa-brain"></i>
          Central de Comando IA
        </h2>
        <p>Acesso rápido às suas ferramentas inteligentes</p>
      </div>

      <div class="command-grid">
        <!-- Study Area - NEW -->
        <div class="command-card study-area" @click="navigateTo('/pomodoro')">
          <div class="card-glow"></div>
          <div class="card-content">
            <div class="card-icon">
              <i class="fas fa-book-open"></i>
            </div>
            <h3>Área de Estudo</h3>
            <p>Foco total com Pomodoro e ferramentas</p>
            <div class="card-stats">
              <span><i class="fas fa-clock"></i> {{ todayStudyTime }}h hoje</span>
              <span><i class="fas fa-tasks"></i> {{ tasksCompleted }} tarefas</span>
            </div>
          </div>
          <div class="hover-effect"></div>
        </div>

        <!-- Performance Dashboard - NEW -->
        <div class="command-card performance" @click="navigateTo('/progress-dashboard')">
          <div class="card-glow"></div>
          <div class="card-content">
            <div class="card-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3>Dashboard Desempenho</h3>
            <p>Análises detalhadas do seu progresso</p>
            <div class="mini-chart">
              <canvas ref="miniPerformanceChart"></canvas>
            </div>
          </div>
          <div class="hover-effect"></div>
        </div>

        <!-- AI Engines Hub -->
        <div class="command-card engines" @click="navigateTo('/engines')">
          <div class="card-glow"></div>
          <div class="card-content">
            <div class="card-icon">
              <i class="fas fa-cogs"></i>
            </div>
            <h3>Engines IA</h3>
            <p>Todas as ferramentas de IA em um lugar</p>
            <div class="engines-preview">
              <span class="engine-badge">FlashcardsAI</span>
              <span class="engine-badge">+5</span>
            </div>
          </div>
          <div class="hover-effect"></div>
        </div>

        <!-- AI Tutor -->
        <div class="command-card tutor" @click="navigateTo('/ai-tutor')">
          <div class="card-glow"></div>
          <div class="card-content">
            <div class="card-icon">
              <i class="fas fa-robot"></i>
            </div>
            <h3>Tutor IA</h3>
            <p>Assistente personalizado 24/7</p>
            <div class="tutor-status">
              <div class="status-dot active"></div>
              <span>Online e pronto</span>
            </div>
          </div>
          <div class="hover-effect"></div>
        </div>

        <!-- Flashcards System -->
        <div class="command-card flashcards" @click="navigateTo('/ai-tools/flashcards')">
          <div class="card-glow"></div>
          <div class="card-content">
            <div class="card-icon">
              <i class="fas fa-layer-group"></i>
            </div>
            <h3>FlashcardsAI Pro</h3>
            <p>Sistema neural de memorização</p>
            <div class="flashcard-stats">
              <div class="stat">
                <span class="number">{{ totalCards }}</span>
                <span class="label">cards</span>
              </div>
              <div class="stat">
                <span class="number">{{ dueToday }}</span>
                <span class="label">hoje</span>
              </div>
            </div>
          </div>
          <div class="hover-effect"></div>
        </div>

        <!-- Study Plan -->
        <div class="command-card plan" @click="navigateTo('/plano-estudo')">
          <div class="card-glow"></div>
          <div class="card-content">
            <div class="card-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <h3>Plano de Estudo</h3>
            <p>Organização inteligente</p>
            <div class="plan-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: planProgress + '%' }"></div>
              </div>
              <span>{{ planProgress }}% concluído</span>
            </div>
          </div>
          <div class="hover-effect"></div>
        </div>
      </div>
    </section>

    <!-- Quick Actions Hub -->
    <section class="quick-actions-hub">
      <div class="section-header">
        <h2>
          <i class="fas fa-bolt"></i>
          Ações Rápidas
        </h2>
      </div>

      <div class="actions-carousel">
        <div class="action-item" v-for="action in quickActions" :key="action.id" @click="executeAction(action)">
          <i :class="action.icon"></i>
          <span>{{ action.label }}</span>
        </div>
      </div>
    </section>

    <!-- Live Analytics -->
    <section class="live-analytics">
      <div class="analytics-grid">
        <!-- Study Heatmap -->
        <div class="analytics-card heatmap">
          <h3>
            <i class="fas fa-fire-alt"></i>
            Mapa de Calor - Última Semana
          </h3>
          <div class="heatmap-container">
            <div class="heatmap-grid">
              <div v-for="(day, index) in weekHeatmap" :key="index" class="heatmap-day">
                <span class="day-label">{{ day.label }}</span>
                <div class="hour-blocks">
                  <div 
                    v-for="(hour, hourIndex) in day.hours" 
                    :key="hourIndex"
                    class="hour-block"
                    :style="{ backgroundColor: getHeatmapColor(hour) }"
                    :title="`${hour} minutos`"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Metrics -->
        <div class="analytics-card metrics">
          <h3>
            <i class="fas fa-chart-bar"></i>
            Métricas de Performance
          </h3>
          <div class="metrics-list">
            <div class="metric-item" v-for="metric in performanceMetrics" :key="metric.id">
              <div class="metric-header">
                <span class="metric-name">{{ metric.name }}</span>
                <span class="metric-value">{{ metric.value }}{{ metric.unit }}</span>
              </div>
              <div class="metric-bar">
                <div 
                  class="metric-fill" 
                  :style="{ 
                    width: metric.percentage + '%',
                    backgroundColor: metric.color 
                  }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="analytics-card activity">
          <h3>
            <i class="fas fa-history"></i>
            Atividade Recente
          </h3>
          <div class="activity-timeline">
            <div class="timeline-item" v-for="activity in recentActivities" :key="activity.id">
              <div class="timeline-marker" :style="{ backgroundColor: activity.color }">
                <i :class="activity.icon"></i>
              </div>
              <div class="timeline-content">
                <p class="activity-text">{{ activity.text }}</p>
                <span class="activity-time">{{ activity.time }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- AI Recommendations -->
    <section class="ai-recommendations">
      <div class="section-header">
        <h2>
          <i class="fas fa-magic"></i>
          Recomendações IA para Hoje
        </h2>
      </div>

      <div class="recommendations-grid">
        <div class="recommendation-card" v-for="rec in aiRecommendations" :key="rec.id">
          <div class="rec-icon" :style="{ backgroundColor: rec.color + '20' }">
            <i :class="rec.icon" :style="{ color: rec.color }"></i>
          </div>
          <div class="rec-content">
            <h4>{{ rec.title }}</h4>
            <p>{{ rec.description }}</p>
            <button class="rec-action" @click="executeRecommendation(rec)">
              {{ rec.actionText }}
              <i class="fas fa-arrow-right"></i>
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Floating Action Button -->
    <div class="fab-container">
      <button class="fab-main" @click="toggleFab">
        <i class="fas fa-plus" :class="{ 'rotate': fabOpen }"></i>
      </button>
      <transition-group name="fab" tag="div" class="fab-options">
        <button 
          v-if="fabOpen"
          v-for="(option, index) in fabOptions" 
          :key="option.id"
          class="fab-option"
          :style="{ '--index': index }"
          @click="executeFabAction(option)"
        >
          <i :class="option.icon"></i>
          <span class="fab-tooltip">{{ option.label }}</span>
        </button>
      </transition-group>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import Chart from 'chart.js/auto'

export default {
  name: 'HomeRedesigned',
  
  setup() {
    const store = useStore()
    const router = useRouter()
    
    // State
    const fabOpen = ref(false)
    const miniChart = ref(null)
    
    // User data
    const user = computed(() => store.state.auth.user)
    const userName = computed(() => user.value?.name || 'Estudante')
    const userInitials = computed(() => {
      const name = userName.value
      return name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase()
    })
    
    // Stats
    const totalStudyHours = ref(156)
    const streakDays = ref(28)
    const todayStudyTime = ref(3.5)
    const tasksCompleted = ref(12)
    const totalCards = ref(2847)
    const dueToday = ref(85)
    const planProgress = ref(68)
    
    // Quick stats
    const quickStats = ref([
      { id: 1, icon: 'fas fa-brain', value: '2.8K', label: 'Cards' },
      { id: 2, icon: 'fas fa-trophy', value: '15', label: 'Conquistas' },
      { id: 3, icon: 'fas fa-bullseye', value: '87%', label: 'Precisão' },
      { id: 4, icon: 'fas fa-graduation-cap', value: 'A+', label: 'Ranking' }
    ])
    
    // Quick actions
    const quickActions = ref([
      { id: 1, icon: 'fas fa-play', label: 'Iniciar Revisão', action: 'startReview' },
      { id: 2, icon: 'fas fa-plus', label: 'Novo Deck', action: 'createDeck' },
      { id: 3, icon: 'fas fa-clock', label: 'Pomodoro', action: 'startPomodoro' },
      { id: 4, icon: 'fas fa-robot', label: 'Chat IA', action: 'openAIChat' },
      { id: 5, icon: 'fas fa-chart-line', label: 'Ver Análises', action: 'viewAnalytics' }
    ])
    
    // Week heatmap data
    const weekHeatmap = ref([
      { label: 'Seg', hours: [0,0,0,0,0,0,20,45,60,55,30,45,60,45,30,40,50,60,45,30,20,0,0,0] },
      { label: 'Ter', hours: [0,0,0,0,0,0,15,30,50,60,45,55,50,40,35,45,55,50,40,25,15,0,0,0] },
      { label: 'Qua', hours: [0,0,0,0,0,0,25,40,55,50,40,50,60,50,40,50,60,55,35,20,10,0,0,0] },
      { label: 'Qui', hours: [0,0,0,0,0,0,30,50,60,60,50,60,55,45,45,55,60,50,30,15,5,0,0,0] },
      { label: 'Sex', hours: [0,0,0,0,0,0,20,35,45,55,45,40,45,35,30,40,45,40,25,10,0,0,0,0] },
      { label: 'Sáb', hours: [0,0,0,0,0,0,0,15,30,45,40,35,30,25,20,25,30,20,10,0,0,0,0,0] },
      { label: 'Dom', hours: [0,0,0,0,0,0,0,10,25,40,35,30,25,20,15,20,25,15,5,0,0,0,0,0] }
    ])
    
    // Performance metrics
    const performanceMetrics = ref([
      { id: 1, name: 'Taxa de Retenção', value: 89, unit: '%', percentage: 89, color: '#48bb78' },
      { id: 2, name: 'Velocidade Média', value: 15, unit: 's', percentage: 75, color: '#667eea' },
      { id: 3, name: 'Consistência', value: 92, unit: '%', percentage: 92, color: '#ed8936' },
      { id: 4, name: 'Eficiência', value: 85, unit: '%', percentage: 85, color: '#38b2ac' }
    ])
    
    // Recent activities
    const recentActivities = ref([
      { id: 1, icon: 'fas fa-check', text: 'Completou revisão de Anatomia', time: '5 min atrás', color: '#48bb78' },
      { id: 2, icon: 'fas fa-brain', text: 'Criou novo deck: Farmacologia', time: '1h atrás', color: '#667eea' },
      { id: 3, icon: 'fas fa-trophy', text: 'Conquistou: Mestre dos Flashcards', time: '2h atrás', color: '#ecc94b' },
      { id: 4, icon: 'fas fa-clock', text: 'Sessão Pomodoro: 2h de foco', time: '3h atrás', color: '#ed8936' }
    ])
    
    // AI Recommendations
    const aiRecommendations = ref([
      {
        id: 1,
        icon: 'fas fa-brain',
        title: 'Revisar Anatomia Cardiovascular',
        description: 'Baseado no seu desempenho, é hora de revisar estes cards para máxima retenção.',
        actionText: 'Começar Revisão',
        color: '#667eea'
      },
      {
        id: 2,
        icon: 'fas fa-clock',
        title: 'Sessão de Foco Profundo',
        description: 'Você está no seu horário de pico de produtividade. Que tal uma sessão Pomodoro?',
        actionText: 'Iniciar Pomodoro',
        color: '#ed8936'
      },
      {
        id: 3,
        icon: 'fas fa-chart-line',
        title: 'Análise Semanal Disponível',
        description: 'Veja insights detalhados sobre seu progresso esta semana.',
        actionText: 'Ver Análise',
        color: '#48bb78'
      }
    ])
    
    // FAB options
    const fabOptions = ref([
      { id: 1, icon: 'fas fa-plus', label: 'Novo Deck', action: 'createDeck' },
      { id: 2, icon: 'fas fa-brain', label: 'Gerar com IA', action: 'generateWithAI' },
      { id: 3, icon: 'fas fa-clock', label: 'Pomodoro', action: 'startPomodoro' },
      { id: 4, icon: 'fas fa-robot', label: 'Tutor IA', action: 'openTutor' }
    ])
    
    // Methods
    const navigateTo = (path) => {
      router.push(path)
    }
    
    const executeAction = (action) => {
      const actions = {
        startReview: () => navigateTo('/ai-tools/flashcards'),
        createDeck: () => navigateTo('/flashcards/create-deck'),
        startPomodoro: () => navigateTo('/pomodoro'),
        openAIChat: () => navigateTo('/ai-tutor'),
        viewAnalytics: () => navigateTo('/progress-dashboard')
      }
      
      if (actions[action.action]) {
        actions[action.action]()
      }
    }
    
    const executeRecommendation = (rec) => {
      // Navigate based on recommendation
      if (rec.id === 1) navigateTo('/ai-tools/flashcards')
      if (rec.id === 2) navigateTo('/pomodoro')
      if (rec.id === 3) navigateTo('/progress-dashboard')
    }
    
    const toggleFab = () => {
      fabOpen.value = !fabOpen.value
    }
    
    const executeFabAction = (option) => {
      fabOpen.value = false
      executeAction({ action: option.action })
    }
    
    const getHeatmapColor = (minutes) => {
      if (minutes === 0) return '#f0f0f0'
      if (minutes < 15) return '#c6e48b'
      if (minutes < 30) return '#7bc96f'
      if (minutes < 45) return '#239a3b'
      return '#196127'
    }
    
    const createMiniChart = () => {
      const canvas = document.querySelector('.mini-chart canvas')
      if (!canvas) return
      
      const ctx = canvas.getContext('2d')
      miniChart.value = new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['S', 'T', 'Q', 'Q', 'S', 'S', 'D'],
          datasets: [{
            data: [85, 88, 82, 90, 87, 92, 89],
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 2,
            tension: 0.4,
            pointRadius: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false }
          },
          scales: {
            x: { display: false },
            y: { display: false }
          }
        }
      })
    }
    
    // Lifecycle
    onMounted(() => {
      setTimeout(createMiniChart, 300)
    })
    
    onBeforeUnmount(() => {
      if (miniChart.value) {
        miniChart.value.destroy()
      }
    })
    
    return {
      // State
      fabOpen,
      
      // User
      userName,
      userInitials,
      
      // Stats
      totalStudyHours,
      streakDays,
      todayStudyTime,
      tasksCompleted,
      totalCards,
      dueToday,
      planProgress,
      
      // Data
      quickStats,
      quickActions,
      weekHeatmap,
      performanceMetrics,
      recentActivities,
      aiRecommendations,
      fabOptions,
      
      // Methods
      navigateTo,
      executeAction,
      executeRecommendation,
      toggleFab,
      executeFabAction,
      getHeatmapColor
    }
  }
}
</script>

<style scoped>
/* Reset and Base */
.home-ultra {
  min-height: 100vh;
  background: #0a0e27;
  color: #ffffff;
  overflow-x: hidden;
  position: relative;
}

/* Animated Background */
.background-animation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  pointer-events: none;
}

.neural-network {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(72, 187, 120, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, rgba(237, 137, 54, 0.1) 0%, transparent 50%);
}

.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

/* Ultra Header */
.ultra-header {
  position: relative;
  z-index: 10;
  padding: 2rem 0;
  background: rgba(13, 17, 39, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.user-welcome {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.avatar-system {
  position: relative;
  width: 80px;
  height: 80px;
}

.avatar-pulse {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.avatar-main {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
}

.status-indicator {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 3px solid #0a0e27;
}

.status-indicator.online {
  background: #48bb78;
  box-shadow: 0 0 10px #48bb78;
}

.welcome-text {
  font-size: 2rem;
  font-weight: 300;
  margin: 0;
}

.user-name {
  font-weight: 600;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.welcome-subtitle {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0.5rem 0 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.ai-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 20px;
  color: #667eea;
}

.separator {
  opacity: 0.3;
}

.streak-info {
  color: #ed8936;
}

/* Quick Stats */
.quick-stats {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.stat-bubble {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  transition: all 0.3s ease;
}

.stat-bubble:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.stat-bubble i {
  font-size: 1.5rem;
  color: #667eea;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Sections */
section {
  position: relative;
  z-index: 10;
  padding: 3rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.section-header {
  margin-bottom: 2rem;
}

.section-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-header p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

/* AI Command Center */
.command-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.command-card {
  position: relative;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.command-card:hover .card-glow {
  opacity: 1;
}

.command-card:hover {
  transform: translateY(-5px);
  border-color: rgba(102, 126, 234, 0.5);
}

.card-content {
  position: relative;
  z-index: 1;
}

.card-icon {
  width: 60px;
  height: 60px;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.command-card h3 {
  font-size: 1.3rem;
  margin: 0 0 0.5rem;
}

.command-card p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 1rem;
  font-size: 0.9rem;
}

.card-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.5);
}

.card-stats span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mini-chart {
  height: 60px;
  margin-top: 1rem;
}

.engines-preview {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.engine-badge {
  padding: 0.25rem 0.75rem;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 20px;
  font-size: 0.8rem;
  color: #667eea;
}

.tutor-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #48bb78;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #48bb78;
  animation: pulse 2s ease-in-out infinite;
}

.flashcard-stats {
  display: flex;
  gap: 2rem;
}

.flashcard-stats .stat {
  text-align: center;
}

.flashcard-stats .number {
  display: block;
  font-size: 1.5rem;
  font-weight: 600;
  color: #667eea;
}

.flashcard-stats .label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

.plan-progress {
  margin-top: 1rem;
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

/* Quick Actions */
.actions-carousel {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding-bottom: 1rem;
  -webkit-overflow-scrolling: touch;
}

.actions-carousel::-webkit-scrollbar {
  height: 4px;
}

.actions-carousel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.actions-carousel::-webkit-scrollbar-thumb {
  background: #667eea;
  border-radius: 2px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  transform: translateY(-2px);
}

/* Live Analytics */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.analytics-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 1.5rem;
}

.analytics-card h3 {
  font-size: 1.1rem;
  margin: 0 0 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Heatmap */
.heatmap-grid {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.heatmap-day {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.day-label {
  width: 40px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

.hour-blocks {
  display: flex;
  gap: 2px;
  flex: 1;
}

.hour-block {
  flex: 1;
  height: 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.hour-block:hover {
  transform: scale(1.1);
}

/* Metrics */
.metric-item {
  margin-bottom: 1.5rem;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.metric-name {
  color: rgba(255, 255, 255, 0.8);
}

.metric-value {
  font-weight: 600;
}

.metric-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  transition: width 0.5s ease;
}

/* Activity Timeline */
.activity-timeline {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.timeline-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.timeline-marker {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.timeline-content {
  flex: 1;
}

.activity-text {
  margin: 0 0 0.25rem;
  color: rgba(255, 255, 255, 0.9);
}

.activity-time {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

/* AI Recommendations */
.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.recommendation-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  transition: all 0.3s ease;
}

.recommendation-card:hover {
  transform: translateY(-3px);
  border-color: rgba(102, 126, 234, 0.3);
}

.rec-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.rec-content h4 {
  margin: 0 0 0.5rem;
  font-size: 1.1rem;
}

.rec-content p {
  margin: 0 0 1rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.rec-action {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid #667eea;
  border-radius: 8px;
  color: #667eea;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rec-action:hover {
  background: #667eea;
  color: white;
  transform: translateX(3px);
}

/* FAB */
.fab-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 100;
}

.fab-main {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-main:hover {
  transform: scale(1.1);
}

.fab-main i {
  transition: transform 0.3s ease;
}

.fab-main i.rotate {
  transform: rotate(45deg);
}

.fab-options {
  position: absolute;
  bottom: 80px;
  right: 0;
}

.fab-option {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #1a1f3a;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.fab-tooltip {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  background: #1a1f3a;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  white-space: nowrap;
  font-size: 0.9rem;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.fab-option:hover .fab-tooltip {
  opacity: 1;
}

/* FAB Animation */
.fab-enter-active {
  transition: all 0.3s ease;
  transition-delay: calc(var(--index) * 0.05s);
}

.fab-leave-active {
  transition: all 0.3s ease;
}

.fab-enter-from,
.fab-leave-to {
  opacity: 0;
  transform: scale(0) translateY(20px);
}

.fab-enter-to {
  transform: translateY(calc(var(--index) * -60px));
}

/* Animations */
@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .quick-stats {
    width: 100%;
  }
  
  .command-grid,
  .analytics-grid,
  .recommendations-grid {
    grid-template-columns: 1fr;
  }
  
  .fab-container {
    bottom: 1rem;
    right: 1rem;
  }
}
</style>