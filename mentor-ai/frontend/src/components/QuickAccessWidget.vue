<template>
  <div class="quick-access-widget">
    <h3 class="widget-title">
      <i class="fas fa-rocket"></i>
      Acesso Rápido
    </h3>
    
    <div class="quick-grid">
      <!-- AI Tools Dropdown -->
      <div class="quick-item dropdown" @mouseenter="showAITools = true" @mouseleave="showAITools = false">
        <div class="quick-button ai-tools">
          <i class="fas fa-robot"></i>
          <span>Ferramentas IA</span>
          <i class="fas fa-chevron-down"></i>
        </div>
        
        <transition name="dropdown">
          <div v-if="showAITools" class="dropdown-menu">
            <router-link to="/ia" class="dropdown-item">
              <i class="fas fa-brain"></i>
              <span>Acessar Ferramentas IA</span>
            </router-link>
          </div>
        </transition>
      </div>

      <!-- Study Tools -->
      <router-link to="/pomodoro" class="quick-item">
        <div class="quick-button study">
          <i class="fas fa-book-open"></i>
          <span><PERSON><PERSON> de Estudo</span>
        </div>
      </router-link>


      <!-- Performance -->
      <router-link to="/progress-dashboard" class="quick-item">
        <div class="quick-button performance">
          <i class="fas fa-chart-line"></i>
          <span>Desempenho</span>
        </div>
      </router-link>

      <!-- Calendar -->
      <router-link to="/calendar" class="quick-item">
        <div class="quick-button calendar">
          <i class="fas fa-calendar-alt"></i>
          <span>Calendário</span>
          <span class="badge" v-if="todayEvents > 0">{{ todayEvents }}</span>
        </div>
      </router-link>

      <!-- Resources -->
      <router-link to="/recursos" class="quick-item">
        <div class="quick-button resources">
          <i class="fas fa-book"></i>
          <span>Recursos</span>
        </div>
      </router-link>
    </div>

    <!-- Recent Items -->
    <div class="recent-section">
      <h4>
        <i class="fas fa-history"></i>
        Acessados Recentemente
      </h4>
      <div class="recent-items">
        <router-link 
          v-for="item in recentItems" 
          :key="item.id"
          :to="item.path"
          class="recent-item"
        >
          <i :class="item.icon"></i>
          <span>{{ item.name }}</span>
          <span class="time">{{ item.time }}</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'QuickAccessWidget',
  
  setup() {
    const store = useStore()
    const showAITools = ref(false)
    
    // Mock data - should come from store
    const dueCards = ref(15)
    const todayEvents = ref(3)
    
    const recentItems = ref([
      { id: 1, name: 'Pomodoro - Farmacologia', path: '/pomodoro', icon: 'fas fa-clock', time: '1h' },
      { id: 2, name: 'Dashboard Semanal', path: '/progress-dashboard', icon: 'fas fa-chart-line', time: '2h' }
    ])
    
    return {
      showAITools,
      dueCards,
      todayEvents,
      recentItems
    }
  }
}
</script>

<style scoped>
.quick-access-widget {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.widget-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: #1a202c;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.widget-title i {
  color: #667eea;
}

.quick-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.quick-item {
  text-decoration: none;
  position: relative;
}

.quick-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem 1rem;
  background: #f7fafc;
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.quick-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quick-button i {
  font-size: 1.5rem;
}

.quick-button span {
  font-size: 0.85rem;
  font-weight: 500;
  color: #4a5568;
  text-align: center;
}

/* Color themes for buttons */
.quick-button.ai-tools {
  background: linear-gradient(135deg, #667eea10, #764ba210);
  border-color: #667eea20;
}

.quick-button.ai-tools:hover {
  border-color: #667eea;
}

.quick-button.ai-tools i {
  color: #667eea;
}

.quick-button.study {
  background: linear-gradient(135deg, #ed893610, #f6ad5510);
  border-color: #ed893620;
}

.quick-button.study:hover {
  border-color: #ed8936;
}

.quick-button.study i {
  color: #ed8936;
}

.quick-button.flashcards {
  background: linear-gradient(135deg, #48bb7810, #68d39110);
  border-color: #48bb7820;
}

.quick-button.flashcards:hover {
  border-color: #48bb78;
}

.quick-button.flashcards i {
  color: #48bb78;
}

.quick-button.performance {
  background: linear-gradient(135deg, #4299e110, #63b3ed10);
  border-color: #4299e120;
}

.quick-button.performance:hover {
  border-color: #4299e1;
}

.quick-button.performance i {
  color: #4299e1;
}

.quick-button.calendar {
  background: linear-gradient(135deg, #9f7aea10, #b794f410);
  border-color: #9f7aea20;
}

.quick-button.calendar:hover {
  border-color: #9f7aea;
}

.quick-button.calendar i {
  color: #9f7aea;
}

.quick-button.resources {
  background: linear-gradient(135deg, #f687b310, #fc8a8a10);
  border-color: #f687b320;
}

.quick-button.resources:hover {
  border-color: #f687b3;
}

.quick-button.resources i {
  color: #f687b3;
}

/* Badge */
.badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #e53e3e;
  color: white;
  font-size: 0.7rem;
  padding: 0.15rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 100;
  overflow: hidden;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #4a5568;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.dropdown-item:hover {
  background: #f7fafc;
  color: #667eea;
}

.dropdown-item i {
  width: 20px;
  text-align: center;
}

/* Dropdown animation */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Recent Section */
.recent-section h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recent-section h4 i {
  color: #a0aec0;
}

.recent-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  text-decoration: none;
  color: #4a5568;
  transition: all 0.2s ease;
}

.recent-item:hover {
  background: #edf2f7;
  transform: translateX(4px);
}

.recent-item i {
  width: 20px;
  text-align: center;
  color: #667eea;
}

.recent-item span {
  flex: 1;
  font-size: 0.9rem;
}

.recent-item .time {
  font-size: 0.8rem;
  color: #a0aec0;
}

/* Responsive */
@media (max-width: 768px) {
  .quick-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-button {
    padding: 1rem 0.5rem;
  }
}
</style>