<template>
  <div class="progress-dashboard-page">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Modern Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <i class="fas fa-chart-line"></i>
            <div class="icon-glow"></div>
          </div>
          <div class="page-info">
            <h1 class="page-title">Dashboard de Progresso</h1>
            <p class="page-subtitle">Análise completa do seu desempenho acadêmico</p>
          </div>
        </div>
        <div class="header-right">
          <div class="time-selector">
            <button 
              v-for="period in timePeriods" 
              :key="period.value"
              :class="['period-btn', { active: selectedPeriod === period.value }]"
              @click="selectedPeriod = period.value"
            >
              <i :class="period.icon"></i>
              <span>{{ period.label }}</span>
            </button>
          </div>
          <button @click="exportDashboard" class="export-btn">
            <i class="fas fa-download"></i>
            <span>Exportar</span>
            <div class="btn-glow"></div>
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Gamification System (Ultra Minimal) -->
      <div class="gamification-minimal">
        <!-- Progress Overview -->
        <div class="progress-overview modern-card">
          <div class="level-section">
            <div class="level-badge">
              <span class="level-number">{{ currentLevel }}</span>
            </div>
            <div class="level-info">
              <h3>{{ levelTitle }}</h3>
              <div class="xp-progress">
                <div class="xp-bar">
                  <div class="xp-fill" :style="{ width: xpProgress + '%' }"></div>
                </div>
                <span class="xp-text">{{ formatXP(xpCurrent) }} / {{ formatXP(xpNextLevel) }} XP</span>
              </div>
            </div>
          </div>
          
          <div class="stats-row">
            <div class="stat-item">
              <i class="fas fa-fire"></i>
              <span class="stat-value">{{ streakDays }}</span>
              <span class="stat-label">dias</span>
            </div>
            <div class="stat-item">
              <i class="fas fa-bolt"></i>
              <span class="stat-value">+{{ xpToday }}</span>
              <span class="stat-label">hoje</span>
            </div>
            <div class="stat-item">
              <i class="fas fa-trophy"></i>
              <span class="stat-value">#{{ userRank }}</span>
              <span class="stat-label">posição</span>
            </div>
          </div>
        </div>

        <!-- Achievement Showcase -->
        <div class="achievements-minimal modern-card">
          <div class="section-title">
            <h3>Conquistas Recentes</h3>
            <span class="achievement-count">{{ unlockedAchievementsCount }}/{{ totalAchievements }}</span>
          </div>
          
          <div class="achievements-list">
            <div 
              v-for="achievement in recentAchievements.slice(0, 3)" 
              :key="achievement.id"
              class="achievement-item"
              :class="{ 
                'unlocked': achievement.unlocked,
                'locked': !achievement.unlocked
              }"
            >
              <div class="achievement-icon" :class="achievement.rarity">
                <i :class="achievement.icon"></i>
              </div>
              <div class="achievement-details">
                <h4>{{ achievement.name }}</h4>
                <p>{{ achievement.description }}</p>
              </div>
              <div class="achievement-status">
                <i :class="achievement.unlocked ? 'fas fa-check-circle' : 'fas fa-lock'"></i>
              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- AI Insights Section -->
      <div class="insights-section" v-if="showInsights">
        <div class="section-header">
          <div class="header-left">
            <div class="section-icon">
              <i class="fas fa-brain"></i>
            </div>
            <h2>Insights Inteligentes por IA</h2>
            <div class="ai-status" :class="{ active: aiActive }">
              <div class="status-dot"></div>
              <span>{{ aiActive ? 'Analisando' : 'Pronta' }}</span>
            </div>
          </div>
          <button @click="refreshInsights" class="refresh-btn" :class="{ spinning: refreshingInsights }">
            <i class="fas fa-sync-alt"></i>
          </button>
        </div>

        <div class="insights-grid">
          <div 
            v-for="(insight, index) in aiInsights" 
            :key="index"
            class="insight-card modern-card"
            :class="`insight-${insight.type}`"
          >
            <div class="card-background" :style="{ background: `linear-gradient(135deg, ${insight.color}20, transparent)` }"></div>
            <div class="card-content">
              <div class="insight-header">
                <div class="insight-icon" :style="{ backgroundColor: insight.color + '15' }">
                  <i :class="getInsightIcon(insight.type)" :style="{ color: insight.color }"></i>
                </div>
                <div class="insight-badge" :style="{ backgroundColor: insight.color + '20', color: insight.color }">
                  {{ insight.priority }}
                </div>
              </div>
              
              <h3>{{ insight.title }}</h3>
              <p>{{ insight.description }}</p>
              
              <div class="insight-metrics" v-if="insight.metrics">
                <div v-for="(metric, key) in insight.metrics" :key="key" class="metric-item">
                  <span class="metric-label">{{ key }}</span>
                  <span class="metric-value">{{ metric }}</span>
                </div>
              </div>
              
              <button @click="applyInsight(insight)" class="apply-btn">
                <i class="fas fa-magic"></i>
                <span>Aplicar Sugestão</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 3D View Toggle -->
      <div class="view-3d-toggle" v-if="supportsAR">
        <button @click="toggleARMode" class="view-3d-button" :class="{ active: arModeActive }">
          <i class="fas fa-cube"></i>
          <span>{{ arModeActive ? 'Sair' : '3D' }}</span>
        </button>
      </div>

      <!-- 3D Visualization Overlay Minimal -->
      <div v-if="arModeActive" class="ar-overlay minimal">
        <div class="ar-view-minimal">
          <button @click="toggleARMode" class="close-ar-btn">
            <i class="fas fa-times"></i>
          </button>
          
          <div class="ar-content-minimal">
            <div class="metrics-3d-container" :style="{ transform: `rotateY(${arRotation}deg)` }">
              <div 
                v-for="(metric, index) in performanceMetrics" 
                :key="metric.id"
                class="metric-3d-card"
                :style="{
                  '--index': index,
                  transform: `rotateY(${index * 90}deg) translateZ(150px)`
                }"
              >
                <div class="metric-3d-content">
                  <i :class="metric.icon" :style="{ color: metric.color }"></i>
                  <h3>{{ metric.value }}{{ metric.unit }}</h3>
                  <p>{{ metric.label }}</p>
                  <div class="metric-3d-progress">
                    <div class="progress-3d-fill" :style="{ width: metric.progress + '%', backgroundColor: metric.color }"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="ar-controls-minimal">
            <button @click="arRotation -= 90" class="control-3d">
              <i class="fas fa-chevron-left"></i>
            </button>
            <button @click="autoRotate = !autoRotate" class="control-3d" :class="{ active: autoRotate }">
              <i class="fas fa-sync"></i>
            </button>
            <button @click="arRotation += 90" class="control-3d">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="metrics-section">
        <div class="metrics-grid">
          <div 
            v-for="metric in performanceMetrics" 
            :key="metric.id"
            class="metric-card modern-card"
          >
            <div class="card-background"></div>
            <div class="metric-content">
              <div class="metric-header">
                <div class="metric-icon" :style="{ backgroundColor: metric.color + '15' }">
                  <i :class="metric.icon" :style="{ color: metric.color }"></i>
                </div>
                <div class="metric-menu">
                  <button class="menu-btn">
                    <i class="fas fa-ellipsis-h"></i>
                  </button>
                </div>
              </div>
              
              <div class="metric-body">
                <span class="metric-label">{{ metric.label }}</span>
                <div class="metric-value-row">
                  <h2 class="metric-value">{{ metric.value }}</h2>
                  <span class="metric-unit">{{ metric.unit }}</span>
                </div>
                
                <div class="metric-trend" :class="metric.trend > 0 ? 'positive' : 'negative'">
                  <i :class="metric.trend > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                  <span>{{ Math.abs(metric.trend) }}%</span>
                  <span class="trend-text">vs período anterior</span>
                </div>
                
                <div class="metric-chart">
                  <canvas :ref="`chart-${metric.id}`"></canvas>
                </div>
                
                <div class="metric-progress">
                  <div class="progress-header">
                    <span>Meta: {{ metric.goal }}{{ metric.unit }}</span>
                    <span>{{ metric.progress }}%</span>
                  </div>
                  <div class="progress-bar">
                    <div 
                      class="progress-fill" 
                      :style="{ width: metric.progress + '%', backgroundColor: metric.color }"
                    >
                      <div class="progress-glow"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Analytics Tabs -->
      <div class="analytics-section">
        <div class="section-tabs">
          <div class="tabs-header">
            <button 
              v-for="tab in analyticsTabs" 
              :key="tab.id"
              :class="['tab-btn', { active: activeTab === tab.id }]"
              @click="activeTab = tab.id"
            >
              <i :class="tab.icon"></i>
              <span>{{ tab.label }}</span>
            </button>
            <div class="tab-indicator" :style="tabIndicatorStyle"></div>
          </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
          <!-- Desempenho por Disciplina -->
          <div v-if="activeTab === 'subjects'" class="subjects-analytics">
            <div class="analytics-card modern-card">
              <div class="card-header">
                <h3>
                  <i class="fas fa-graduation-cap"></i>
                  Desempenho por Disciplina
                </h3>
                <div class="card-actions">
                  <select v-model="chartType" class="chart-selector">
                    <option value="bar">Barras</option>
                    <option value="radar">Radar</option>
                    <option value="line">Linha</option>
                    <option value="3d">3D Interativo</option>
                  </select>
                </div>
              </div>
              <div class="chart-container">
                <canvas ref="subjectsChart" v-show="chartType !== '3d'"></canvas>
                <div v-show="chartType === '3d'" class="chart-3d-clean">
                  <div class="bars-3d-container" :style="{ transform: `rotateY(${rotateY}deg)` }">
                    <div 
                      v-for="(subject, index) in subjectsData" 
                      :key="subject.id"
                      class="bar-3d-clean"
                      :style="{
                        '--height': subject.average + '%',
                        '--color': subject.color,
                        '--delay': index * 0.1 + 's',
                        transform: `translateX(${(index - 2) * 60}px)`
                      }"
                    >
                      <div class="bar-value">{{ subject.average }}%</div>
                      <div class="bar-body"></div>
                      <div class="bar-name">{{ subject.name }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="subjects-legend">
                <div v-for="subject in subjectsData" :key="subject.id" class="legend-item">
                  <div class="legend-color" :style="{ backgroundColor: subject.color }"></div>
                  <span class="legend-label">{{ subject.name }}</span>
                  <span class="legend-value">{{ subject.average }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Hábitos de Estudo -->
          <div v-if="activeTab === 'habits'" class="habits-analytics">
            <div class="habits-grid">
              <div class="analytics-card modern-card">
                <div class="card-header">
                  <h3>
                    <i class="fas fa-calendar-check"></i>
                    Frequência de Estudo
                  </h3>
                </div>
                <div class="heatmap-container">
                  <div class="heatmap-months">
                    <span v-for="month in months" :key="month">{{ month }}</span>
                  </div>
                  <div class="heatmap-grid">
                    <div 
                      v-for="(day, index) in heatmapData" 
                      :key="index"
                      class="heatmap-cell"
                      :style="{ backgroundColor: getHeatmapColor(day.value) }"
                      :title="`${day.date}: ${day.hours}h`"
                    ></div>
                  </div>
                  <div class="heatmap-legend">
                    <span>Menos</span>
                    <div class="legend-gradient"></div>
                    <span>Mais</span>
                  </div>
                </div>
              </div>

              <div class="analytics-card modern-card">
                <div class="card-header">
                  <h3>
                    <i class="fas fa-clock"></i>
                    Horários de Estudo
                  </h3>
                </div>
                <div class="time-distribution">
                  <canvas ref="timeChart"></canvas>
                </div>
                <div class="best-times">
                  <h4>Melhores Horários</h4>
                  <div class="time-slots">
                    <div v-for="slot in bestTimeSlots" :key="slot.time" class="time-slot">
                      <i class="fas fa-star"></i>
                      <span>{{ slot.time }}</span>
                      <div class="slot-bar" :style="{ width: slot.efficiency + '%' }"></div>
                      <span class="slot-value">{{ slot.efficiency }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Metas e Objetivos -->
          <div v-if="activeTab === 'goals'" class="goals-analytics">
            <div class="goals-overview">
              <div class="overview-stats">
                <div class="stat-item">
                  <div class="stat-icon completed">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="stat-info">
                    <h3>{{ completedGoals }}</h3>
                    <p>Metas Concluídas</p>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon in-progress">
                    <i class="fas fa-spinner"></i>
                  </div>
                  <div class="stat-info">
                    <h3>{{ inProgressGoals }}</h3>
                    <p>Em Andamento</p>
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-icon upcoming">
                    <i class="fas fa-calendar"></i>
                  </div>
                  <div class="stat-info">
                    <h3>{{ upcomingGoals }}</h3>
                    <p>Próximas</p>
                  </div>
                </div>
              </div>

              <div class="goals-list">
                <div v-for="goal in goals" :key="goal.id" class="goal-card modern-card">
                  <div class="goal-header">
                    <div class="goal-icon" :class="goal.status">
                      <i :class="getGoalIcon(goal.status)"></i>
                    </div>
                    <div class="goal-info">
                      <h4>{{ goal.title }}</h4>
                      <p>{{ goal.description }}</p>
                    </div>
                    <div class="goal-deadline">
                      <i class="fas fa-calendar-alt"></i>
                      <span>{{ formatDate(goal.deadline) }}</span>
                    </div>
                  </div>
                  <div class="goal-progress">
                    <div class="progress-info">
                      <span>Progresso</span>
                      <span>{{ goal.progress }}%</span>
                    </div>
                    <div class="progress-track">
                      <div 
                        class="progress-fill" 
                        :style="{ width: goal.progress + '%' }"
                      ></div>
                    </div>
                  </div>
                  <div class="goal-tasks">
                    <div v-for="task in goal.tasks" :key="task.id" class="task-item">
                      <i :class="task.completed ? 'fas fa-check-square' : 'far fa-square'"></i>
                      <span :class="{ completed: task.completed }">{{ task.title }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Study Plan Section (Minimal) -->
      <div class="study-plan-section">
        <div class="study-plan-card modern-card">
          <div class="plan-header">
            <div class="plan-title">
              <h3>Plano de Estudo</h3>
              <span class="plan-badge">Inteligente</span>
            </div>
            <p class="plan-subtitle">Otimizado por IA para máximo aprendizado</p>
          </div>
          
          <div class="plan-stats">
            <div class="stat">
              <span class="stat-value">142</span>
              <span class="stat-label">Horas Estudadas</span>
            </div>
            <div class="stat">
              <span class="stat-value">28</span>
              <span class="stat-label">Dias de Sequência</span>
            </div>
            <div class="stat">
              <span class="stat-value">94%</span>
              <span class="stat-label">Taxa de Conclusão</span>
            </div>
          </div>
          
          <div class="plan-suggestion">
            <i class="fas fa-lightbulb"></i>
            <p>Baseado no seu desempenho, recomendo aumentar o tempo de revisão de Matemática em 15 minutos por dia.</p>
          </div>
          
          <div class="plan-actions">
            <button class="btn-apply">
              <i class="fas fa-check"></i>
              Aplicar
            </button>
            <button class="btn-new" @click="generateNewSuggestion">
              <i class="fas fa-sync-alt"></i>
              Nova sugestão
            </button>
          </div>
          
          <div class="plan-metrics">
            <div class="metric">
              <span class="metric-label">Eficiência</span>
              <div class="metric-bar">
                <div class="metric-fill" style="width: 87%"></div>
              </div>
            </div>
            <div class="metric">
              <span class="metric-label">Cobertura</span>
              <div class="metric-bar">
                <div class="metric-fill" style="width: 92%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recommendations Section -->
      <div class="recommendations-section">
        <div class="section-header">
          <h2>
            <i class="fas fa-lightbulb"></i>
            Recomendações Personalizadas
          </h2>
          <button @click="generateRecommendations" class="generate-btn">
            <i class="fas fa-wand-magic-sparkles"></i>
            <span>Gerar Novas</span>
          </button>
        </div>

        <div class="recommendations-grid">
          <div v-for="rec in recommendations" :key="rec.id" class="recommendation-card modern-card">
            <div class="rec-icon" :style="{ backgroundColor: rec.color + '15' }">
              <i :class="rec.icon" :style="{ color: rec.color }"></i>
            </div>
            <div class="rec-content">
              <h4>{{ rec.title }}</h4>
              <p>{{ rec.description }}</p>
              <div class="rec-impact">
                <span>Impacto esperado:</span>
                <div class="impact-bar">
                  <div class="impact-fill" :style="{ width: rec.impact + '%', backgroundColor: rec.color }"></div>
                </div>
                <span class="impact-value">+{{ rec.impact }}%</span>
              </div>
            </div>
            <button @click="implementRecommendation(rec)" class="implement-btn">
              <i class="fas fa-play"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';

export default {
  name: 'ProgressDashboard',
  data() {
    return {
      selectedPeriod: 'month',
      activeTab: 'subjects',
      chartType: 'bar',
      aiActive: false,
      refreshingInsights: false,
      showInsights: true,
      
      timePeriods: [
        { value: 'week', label: 'Semana', icon: 'fas fa-calendar-week' },
        { value: 'month', label: 'Mês', icon: 'fas fa-calendar-alt' },
        { value: 'semester', label: 'Semestre', icon: 'fas fa-calendar' },
        { value: 'year', label: 'Ano', icon: 'fas fa-calendar-check' }
      ],
      
      aiInsights: [
        {
          type: 'success',
          title: 'Excelente Progresso em Matemática',
          description: 'Você melhorou 23% nas últimas 3 semanas. Continue com o ritmo atual!',
          priority: 'Alta',
          color: '#10b981',
          metrics: {
            'Acertos': '87%',
            'Tempo': '-15%',
            'Eficiência': '+18%'
          }
        },
        {
          type: 'warning',
          title: 'Atenção em Física',
          description: 'Detectamos dificuldade em mecânica quântica. Recomendamos revisão focada.',
          priority: 'Média',
          color: '#f59e0b',
          metrics: {
            'Acertos': '62%',
            'Revisões': '3',
            'Melhoria': '+5%'
          }
        },
        {
          type: 'tip',
          title: 'Otimize seu Horário de Estudo',
          description: 'Seus melhores resultados são entre 19h-22h. Considere ajustar sua rotina.',
          priority: 'Baixa',
          color: '#6366f1',
          metrics: {
            'Produtividade': '92%',
            'Retenção': '85%',
            'Foco': '88%'
          }
        }
      ],
      
      performanceMetrics: [
        {
          id: 1,
          label: 'Taxa de Acerto',
          value: 82,
          unit: '%',
          trend: 12,
          progress: 82,
          goal: 90,
          color: '#10b981',
          icon: 'fas fa-check-circle'
        },
        {
          id: 2,
          label: 'Tempo de Estudo',
          value: 156,
          unit: 'h',
          trend: -5,
          progress: 78,
          goal: 200,
          color: '#6366f1',
          icon: 'fas fa-clock'
        },
        {
          id: 3,
          label: 'Questões Resolvidas',
          value: 1234,
          unit: '',
          trend: 18,
          progress: 92,
          goal: 1500,
          color: '#8b5cf6',
          icon: 'fas fa-tasks'
        },
        {
          id: 4,
          label: 'Eficiência',
          value: 87,
          unit: '%',
          trend: 8,
          progress: 87,
          goal: 95,
          color: '#f59e0b',
          icon: 'fas fa-chart-line'
        }
      ],
      
      analyticsTabs: [
        { id: 'subjects', label: 'Disciplinas', icon: 'fas fa-book' },
        { id: 'habits', label: 'Hábitos', icon: 'fas fa-user-clock' },
        { id: 'goals', label: 'Metas', icon: 'fas fa-bullseye' }
      ],
      
      subjectsData: [
        { id: 1, name: 'Matemática', average: 85, color: '#6366f1' },
        { id: 2, name: 'Física', average: 72, color: '#8b5cf6' },
        { id: 3, name: 'Química', average: 78, color: '#ec4899' },
        { id: 4, name: 'Biologia', average: 90, color: '#10b981' },
        { id: 5, name: 'História', average: 88, color: '#f59e0b' }
      ],
      
      months: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
      heatmapData: this.generateHeatmapData(),
      
      bestTimeSlots: [
        { time: '19:00 - 21:00', efficiency: 95 },
        { time: '14:00 - 16:00', efficiency: 82 },
        { time: '09:00 - 11:00', efficiency: 78 }
      ],
      
      completedGoals: 12,
      inProgressGoals: 5,
      upcomingGoals: 3,
      
      goals: [
        {
          id: 1,
          title: 'Dominar Cálculo Diferencial',
          description: 'Completar todos os módulos e exercícios avançados',
          status: 'in-progress',
          progress: 75,
          deadline: new Date('2024-03-15'),
          tasks: [
            { id: 1, title: 'Limites e Continuidade', completed: true },
            { id: 2, title: 'Derivadas', completed: true },
            { id: 3, title: 'Aplicações de Derivadas', completed: false },
            { id: 4, title: 'Integrais', completed: false }
          ]
        },
        {
          id: 2,
          title: 'Certificação em Física Quântica',
          description: 'Preparação para exame de certificação',
          status: 'upcoming',
          progress: 30,
          deadline: new Date('2024-04-20'),
          tasks: [
            { id: 1, title: 'Mecânica Quântica Básica', completed: true },
            { id: 2, title: 'Equação de Schrödinger', completed: false },
            { id: 3, title: 'Princípio da Incerteza', completed: false }
          ]
        }
      ],
      
      recommendations: [
        {
          id: 1,
          title: 'Técnica Pomodoro Adaptativa',
          description: 'Baseado em seu padrão de foco, sugerimos sessões de 35 minutos',
          icon: 'fas fa-clock',
          color: '#6366f1',
          impact: 25
        },
        {
          id: 2,
          title: 'Revisão Espaçada Otimizada',
          description: 'Algoritmo personalizado detectou oportunidade de melhoria na retenção',
          icon: 'fas fa-brain',
          color: '#8b5cf6',
          impact: 40
        },
        {
          id: 3,
          title: 'Grupo de Estudo Virtual',
          description: 'Conecte-se com estudantes de desempenho similar para maximizar aprendizado',
          icon: 'fas fa-users',
          color: '#10b981',
          impact: 15
        }
      ],
      
      // Gamification Data
      currentLevel: 42,
      levelTitle: 'Mestre do Conhecimento',
      xpCurrent: 8750,
      xpNextLevel: 10000,
      xpToday: 350,
      streakDays: 15,
      levelColor: '#6366f1',
      levelColorLight: '#818cf8',
      userRank: 3,
      currentUserId: 'user123',
      
      recentAchievements: [
        {
          id: 1,
          name: 'Maratonista do Saber',
          description: 'Complete 100 horas de estudo',
          icon: 'fas fa-running',
          rarity: 'legendary',
          xpReward: 500,
          unlocked: true,
          justUnlocked: false
        },
        {
          id: 2,
          name: 'Mestre da Consistência',
          description: 'Mantenha uma sequência de 14 dias',
          icon: 'fas fa-fire',
          rarity: 'epic',
          xpReward: 300,
          unlocked: true,
          justUnlocked: true
        },
        {
          id: 3,
          name: 'Gênio da Matemática',
          description: 'Acerte 95% em um teste de matemática',
          icon: 'fas fa-calculator',
          rarity: 'rare',
          xpReward: 200,
          unlocked: false,
          justUnlocked: false
        },
        {
          id: 4,
          name: 'Explorador do Conhecimento',
          description: 'Estude 5 disciplinas diferentes em um dia',
          icon: 'fas fa-compass',
          rarity: 'common',
          xpReward: 100,
          unlocked: true,
          justUnlocked: false
        }
      ],
      
      topPlayers: [
        {
          id: 'user456',
          name: 'Ana Silva',
          avatar: 'https://ui-avatars.com/api/?name=Ana+Silva&background=6366f1&color=fff',
          level: 48,
          xp: 12500,
          change: 0
        },
        {
          id: 'user789',
          name: 'Carlos Oliveira',
          avatar: 'https://ui-avatars.com/api/?name=Carlos+Oliveira&background=8b5cf6&color=fff',
          level: 45,
          xp: 10200,
          change: 1
        },
        {
          id: 'user123',
          name: 'Você',
          avatar: 'https://ui-avatars.com/api/?name=Voce&background=10b981&color=fff',
          level: 42,
          xp: 8750,
          change: -1
        },
        {
          id: 'user321',
          name: 'Maria Santos',
          avatar: 'https://ui-avatars.com/api/?name=Maria+Santos&background=f59e0b&color=fff',
          level: 40,
          xp: 8200,
          change: 2
        },
        {
          id: 'user654',
          name: 'João Pedro',
          avatar: 'https://ui-avatars.com/api/?name=Joao+Pedro&background=ec4899&color=fff',
          level: 38,
          xp: 7500,
          change: -1
        }
      ],
      
      charts: {},
      
      // CSS 3D Chart properties
      rotateX: -20,
      rotateY: 30,
      isRotating: false,
      rotationAnimation: null,
      
      // AR Mode properties
      supportsAR: true, // Simulated AR support
      arModeActive: false,
      arRotation: 0,
      arAnimationFrame: null,
      autoRotate: false,
      
      // Simulation
      xpSimulationInterval: null
    };
  },
  
  computed: {
    tabIndicatorStyle() {
      const index = this.analyticsTabs.findIndex(tab => tab.id === this.activeTab);
      const width = 100 / this.analyticsTabs.length;
      return {
        width: `${width}%`,
        transform: `translateX(${index * 100}%)`
      };
    },
    
    xpProgress() {
      return Math.round((this.xpCurrent / this.xpNextLevel) * 100);
    },
    
    levelProgress() {
      return this.xpProgress;
    },
    
    unlockedAchievementsCount() {
      return this.recentAchievements.filter(a => a.unlocked).length;
    },
    
    totalAchievements() {
      return this.recentAchievements.length;
    }
  },
  
  watch: {
    chartType(newType) {
      if (newType !== '3d') {
        this.$nextTick(() => {
          this.createSubjectsChart();
        });
      } else {
        this.startRotationAnimation();
      }
    }
  },
  
  mounted() {
    this.initializeCharts();
    this.animateOnScroll();
    this.startAIAnalysis();
    this.checkAchievements();
    
    // Simula ganho de XP periódico para demonstração
    this.startXPSimulation();
  },
  
  beforeUnmount() {
    // Cleanup charts
    Object.values(this.charts).forEach(chart => {
      if (chart) chart.destroy();
    });
    this.stopRotationAnimation();
    this.stopARAnimation();
    document.body.style.overflow = '';
    
    // Para simulação de XP
    if (this.xpSimulationInterval) {
      clearInterval(this.xpSimulationInterval);
    }
  },
  
  methods: {
    initializeCharts() {
      // Initialize mini charts for metrics
      this.performanceMetrics.forEach(metric => {
        const canvas = this.$refs[`chart-${metric.id}`]?.[0];
        if (canvas) {
          this.createMiniChart(canvas, metric);
        }
      });
      
      // Initialize main charts
      this.$nextTick(() => {
        this.createSubjectsChart();
        this.createTimeChart();
      });
    },
    
    createMiniChart(canvas, metric) {
      const ctx = canvas.getContext('2d');
      
      this.charts[`mini-${metric.id}`] = new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['', '', '', '', '', ''],
          datasets: [{
            data: this.generateRandomData(6, 60, 100),
            borderColor: metric.color,
            backgroundColor: `${metric.color}20`,
            borderWidth: 2,
            tension: 0.4,
            pointRadius: 0,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false }
          },
          scales: {
            x: { display: false },
            y: { display: false }
          }
        }
      });
    },
    
    createSubjectsChart() {
      const canvas = this.$refs.subjectsChart;
      if (!canvas) return;
      
      const ctx = canvas.getContext('2d');
      
      if (this.charts.subjects) {
        this.charts.subjects.destroy();
      }
      
      const chartConfig = {
        type: this.chartType,
        data: {
          labels: this.subjectsData.map(s => s.name),
          datasets: [{
            label: 'Média',
            data: this.subjectsData.map(s => s.average),
            backgroundColor: this.subjectsData.map(s => `${s.color}40`),
            borderColor: this.subjectsData.map(s => s.color),
            borderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false }
          },
          scales: this.chartType === 'radar' ? {
            r: {
              beginAtZero: true,
              max: 100,
              ticks: { stepSize: 20 }
            }
          } : {
            y: {
              beginAtZero: true,
              max: 100,
              grid: {
                color: 'rgba(148, 163, 184, 0.1)'
              }
            },
            x: {
              grid: { display: false }
            }
          }
        }
      };
      
      this.charts.subjects = new Chart(ctx, chartConfig);
    },
    
    createTimeChart() {
      const canvas = this.$refs.timeChart;
      if (!canvas) return;
      
      const ctx = canvas.getContext('2d');
      
      this.charts.time = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['Manhã', 'Tarde', 'Noite', 'Madrugada'],
          datasets: [{
            data: [25, 35, 30, 10],
            backgroundColor: [
              '#f59e0b',
              '#6366f1',
              '#8b5cf6',
              '#ec4899'
            ],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 15,
                font: { size: 12 }
              }
            }
          }
        }
      });
    },
    
    generateHeatmapData() {
      const data = [];
      const today = new Date();
      
      for (let i = 180; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        
        data.push({
          date: date.toISOString().split('T')[0],
          value: Math.random(),
          hours: Math.floor(Math.random() * 8)
        });
      }
      
      return data;
    },
    
    getHeatmapColor(value) {
      const intensity = Math.floor(value * 5);
      const colors = [
        'rgba(148, 163, 184, 0.1)',
        'rgba(99, 102, 241, 0.3)',
        'rgba(99, 102, 241, 0.5)',
        'rgba(99, 102, 241, 0.7)',
        'rgba(99, 102, 241, 0.9)'
      ];
      return colors[intensity] || colors[0];
    },
    
    generateRandomData(count, min, max) {
      return Array.from({ length: count }, () => 
        Math.floor(Math.random() * (max - min + 1)) + min
      );
    },
    
    getInsightIcon(type) {
      const icons = {
        success: 'fas fa-check-circle',
        warning: 'fas fa-exclamation-triangle',
        tip: 'fas fa-lightbulb',
        error: 'fas fa-times-circle'
      };
      return icons[type] || 'fas fa-info-circle';
    },
    
    getGoalIcon(status) {
      const icons = {
        completed: 'fas fa-check-circle',
        'in-progress': 'fas fa-spinner',
        upcoming: 'fas fa-calendar'
      };
      return icons[status] || 'fas fa-circle';
    },
    
    formatDate(date) {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      }).format(date);
    },
    
    refreshInsights() {
      this.refreshingInsights = true;
      this.aiActive = true;
      
      setTimeout(() => {
        this.refreshingInsights = false;
        this.aiActive = false;
        // Update insights data
      }, 2000);
    },
    
    applyInsight(insight) {
      console.log('Applying insight:', insight);
      // Implement insight application
    },
    
    showMetricDetails(metric) {
      console.log('Showing details for:', metric);
      // Show detailed metric view
    },
    
    exportDashboard() {
      console.log('Exporting dashboard...');
      // Implement export functionality
    },
    
    generateRecommendations() {
      console.log('Generating new recommendations...');
      // Generate new recommendations
    },
    
    implementRecommendation(rec) {
      console.log('Implementing recommendation:', rec);
      // Implement recommendation
    },
    
    startAIAnalysis() {
      setInterval(() => {
        this.aiActive = !this.aiActive;
      }, 5000);
    },
    
    animateOnScroll() {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      }, { threshold: 0.1 });
      
      document.querySelectorAll('.modern-card').forEach(card => {
        observer.observe(card);
      });
    },
    
    // Gamification Methods
    showAllAchievements() {
      console.log('Showing all achievements');
      // Open achievements modal
    },
    
    showAchievementDetails(achievement) {
      console.log('Showing achievement details:', achievement);
      // Show achievement details modal
      if (achievement.justUnlocked) {
        this.playUnlockAnimation(achievement);
      }
    },
    
    playUnlockAnimation(achievement) {
      // Play unlock animation and sound
      console.log('Playing unlock animation for:', achievement.name);
      
      // Add XP to current total
      this.xpCurrent += achievement.xpReward;
      
      // Check for level up
      if (this.xpCurrent >= this.xpNextLevel) {
        this.levelUp();
      }
    },
    
    levelUp() {
      this.currentLevel++;
      this.xpCurrent = this.xpCurrent - this.xpNextLevel;
      this.xpNextLevel = Math.floor(this.xpNextLevel * 1.2); // 20% more XP needed for next level
      
      // Update level title based on new level
      const levelTitles = {
        10: 'Estudante Iniciante',
        20: 'Aprendiz Dedicado',
        30: 'Conhecedor',
        40: 'Mestre do Conhecimento',
        50: 'Sábio Acadêmico',
        60: 'Gênio',
        70: 'Lenda do Saber'
      };
      
      Object.entries(levelTitles).forEach(([level, title]) => {
        if (this.currentLevel >= parseInt(level)) {
          this.levelTitle = title;
        }
      });
      
      console.log('Level up! New level:', this.currentLevel);
    },
    
    // CSS 3D Chart Methods
    rotate3DChart(direction) {
      if (direction === 'left') {
        this.rotateY -= 30;
      } else if (direction === 'right') {
        this.rotateY += 30;
      }
    },
    
    reset3DChart() {
      this.rotateX = -20;
      this.rotateY = 30;
    },
    
    startRotationAnimation() {
      if (this.rotationAnimation) return;
      
      this.rotationAnimation = setInterval(() => {
        if (this.chartType === '3d' && this.isRotating) {
          this.rotateY += 0.5;
        }
      }, 16);
      
      // Auto-rotate for showcase
      setTimeout(() => {
        this.isRotating = true;
      }, 1000);
    },
    
    stopRotationAnimation() {
      if (this.rotationAnimation) {
        clearInterval(this.rotationAnimation);
        this.rotationAnimation = null;
      }
      this.isRotating = false;
    },
    
    handleChartMouseDown(event) {
      this.isRotating = false;
      const startX = event.clientX;
      const startY = event.clientY;
      const startRotateY = this.rotateY;
      const startRotateX = this.rotateX;
      
      const handleMouseMove = (e) => {
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;
        this.rotateY = startRotateY + deltaX * 0.5;
        this.rotateX = Math.max(-60, Math.min(60, startRotateX - deltaY * 0.5));
      };
      
      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        setTimeout(() => {
          this.isRotating = true;
        }, 3000);
      };
      
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    },
    
    // AR Mode Methods
    toggleARMode() {
      this.arModeActive = !this.arModeActive;
      
      if (this.arModeActive) {
        this.startARAnimation();
        document.body.style.overflow = 'hidden';
      } else {
        this.stopARAnimation();
        document.body.style.overflow = '';
      }
    },
    
    startARAnimation() {
      const animate = () => {
        if (!this.arModeActive) return;
        
        if (this.autoRotate) {
          this.arRotation += 0.5;
        }
        
        this.arAnimationFrame = requestAnimationFrame(animate);
      };
      
      animate();
    },
    
    stopARAnimation() {
      if (this.arAnimationFrame) {
        cancelAnimationFrame(this.arAnimationFrame);
        this.arAnimationFrame = null;
      }
    },
    
    resetARView() {
      this.arRotation = 0;
    },
    
    // Helper Methods for Gamification
    calculateDailyAverage() {
      // Calcula média de XP dos últimos 7 dias
      const averageXP = Math.round(this.xpCurrent / 7);
      return averageXP.toLocaleString();
    },
    
    getRarityColor(rarity) {
      const colors = {
        common: '#94a3b8',
        rare: '#6366f1',
        epic: '#8b5cf6',
        legendary: '#f59e0b'
      };
      return colors[rarity] || colors.common;
    },
    
    getRarityLabel(rarity) {
      const labels = {
        common: 'Comum',
        rare: 'Raro',
        epic: 'Épico',
        legendary: 'Lendário'
      };
      return labels[rarity] || labels.common;
    },
    
    formatXP(xp) {
      if (xp >= 1000) {
        return (xp / 1000).toFixed(1) + 'k';
      }
      return xp.toString();
    },
    
    // Achievement System
    checkAchievements() {
      // Verifica conquistas baseadas em condições
      this.recentAchievements.forEach(achievement => {
        if (!achievement.unlocked) {
          // Exemplo de condições para desbloquear
          switch(achievement.id) {
            case 1: // Maratonista do Saber
              if (this.getTotalStudyHours() >= 100) {
                this.unlockAchievement(achievement);
              }
              break;
            case 2: // Mestre da Consistência
              if (this.streakDays >= 14) {
                this.unlockAchievement(achievement);
              }
              break;
            case 3: // Gênio da Matemática
              const mathAverage = this.subjectsData.find(s => s.name === 'Matemática')?.average;
              if (mathAverage >= 95) {
                this.unlockAchievement(achievement);
              }
              break;
          }
        }
      });
    },
    
    unlockAchievement(achievement) {
      achievement.unlocked = true;
      achievement.justUnlocked = true;
      
      // Adiciona XP
      this.addXP(achievement.xpReward);
      
      // Remove animação após 3 segundos
      setTimeout(() => {
        achievement.justUnlocked = false;
      }, 3000);
      
      // Notificação (pode ser implementada)
      this.showNotification(`Conquista desbloqueada: ${achievement.name}!`);
    },
    
    addXP(amount) {
      this.xpCurrent += amount;
      this.xpToday += amount;
      
      // Verifica level up
      while (this.xpCurrent >= this.xpNextLevel) {
        this.levelUp();
      }
      
      // Atualiza ranking
      this.updatePlayerRanking();
    },
    
    updatePlayerRanking() {
      // Atualiza posição do jogador no ranking
      const userIndex = this.topPlayers.findIndex(p => p.id === this.currentUserId);
      if (userIndex !== -1) {
        this.topPlayers[userIndex].xp = this.xpCurrent;
        
        // Reordena o ranking
        this.topPlayers.sort((a, b) => b.xp - a.xp);
        
        // Atualiza posição do usuário
        const newRank = this.topPlayers.findIndex(p => p.id === this.currentUserId) + 1;
        if (newRank !== this.userRank) {
          const change = this.userRank - newRank;
          this.topPlayers[userIndex].change = change;
          this.userRank = newRank;
        }
      }
    },
    
    getTotalStudyHours() {
      // Simulação - em produção viria do backend
      return this.performanceMetrics.find(m => m.label === 'Tempo de Estudo')?.value || 0;
    },
    
    showNotification(message) {
      // Implementar sistema de notificações
      console.log('Notificação:', message);
    },
    
    startXPSimulation() {
      // Simula ganho de XP para demonstração
      this.xpSimulationInterval = setInterval(() => {
        // Simula ações que ganham XP
        const actions = [
          { xp: 10, chance: 0.3 }, // Ação comum
          { xp: 25, chance: 0.2 }, // Ação média
          { xp: 50, chance: 0.1 }, // Ação rara
        ];
        
        actions.forEach(action => {
          if (Math.random() < action.chance) {
            this.addXP(action.xp);
          }
        });
        
        // Verifica conquistas periodicamente
        this.checkAchievements();
      }, 5000); // A cada 5 segundos
    },
    
    generateNewSuggestion() {
      // Array de sugestões possíveis
      const suggestions = [
        'Baseado no seu desempenho, recomendo aumentar o tempo de revisão de Matemática em 15 minutos por dia.',
        'Análise detectou que você aprende melhor com flashcards. Considere criar 10 novos cards por dia.',
        'Seus melhores resultados são pela manhã. Que tal começar o dia com 30 minutos de revisão?',
        'Você está próximo de dominar Física Quântica. Adicione mais 2 exercícios diários para acelerar.',
        'Padrão identificado: pausas de 5 minutos a cada 25 minutos aumentam sua retenção em 23%.',
        'Sua taxa de acerto em Química aumentou 15%. Continue com sessões de 45 minutos para manter o ritmo.',
        'Detectamos fadiga após 2 horas de estudo. Considere dividir em blocos menores com intervalos.',
        'Flashcards de vocabulário médico mostram 92% de retenção. Aumente para 20 cards diários.',
        'Seu desempenho em simulados melhora 30% quando feitos às 14h. Ajuste sua agenda.',
        'Priorize Anatomia esta semana - está 20% abaixo da média necessária para seus objetivos.'
      ];
      
      // Seleciona uma sugestão aleatória
      const randomIndex = Math.floor(Math.random() * suggestions.length);
      const suggestionElement = document.querySelector('.plan-suggestion p');
      if (suggestionElement) {
        suggestionElement.textContent = suggestions[randomIndex];
        
        // Adiciona uma animação suave
        suggestionElement.style.opacity = '0';
        setTimeout(() => {
          suggestionElement.style.transition = 'opacity 0.3s ease';
          suggestionElement.style.opacity = '1';
        }, 100);
      }
    },
    
    showAchievementDetails(achievement) {
      // Mostra detalhes da conquista
      console.log('Achievement details:', achievement);
    }
  }
};
</script>

<style scoped>
/* Variables */
:root {
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --dark-bg: #0a0f1b;
  --card-bg: #1a2332;
  --text-primary: #e4e6eb;
  --text-secondary: #94a3b8;
  --border-color: rgba(148, 163, 184, 0.1);
}

/* Base */
.progress-dashboard-page {
  min-height: 100vh;
  background: var(--dark-bg);
  color: var(--text-primary);
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

.gradient-orb.orb-1 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, #6366f1, transparent);
  top: -200px;
  left: -200px;
}

.gradient-orb.orb-2 {
  width: 800px;
  height: 800px;
  background: radial-gradient(circle, #8b5cf6, transparent);
  bottom: -300px;
  right: -300px;
  animation-delay: -7s;
}

.gradient-orb.orb-3 {
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, #10b981, transparent);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -14s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-30px, 30px) scale(0.9); }
}

.grid-overlay {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 30s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Header */
.page-header {
  position: relative;
  z-index: 10;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: 2rem;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.page-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  color: white;
  position: relative;
}

.icon-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 16px;
  opacity: 0.5;
  filter: blur(10px);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.05); }
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-subtitle {
  margin: 0.25rem 0 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.time-selector {
  display: flex;
  background: rgba(30, 41, 59, 0.5);
  padding: 0.25rem;
  border-radius: 12px;
  gap: 0.25rem;
}

.period-btn {
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.period-btn:hover {
  color: var(--text-primary);
}

.period-btn.active {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
}

.export-btn {
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border: none;
  color: white;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
}

.btn-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  filter: blur(20px);
  opacity: 0;
  transition: opacity 0.3s;
}

.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 40px rgba(99, 102, 241, 0.4);
}

.export-btn:hover .btn-glow {
  opacity: 0.5;
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 5;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Modern Card */
.modern-card {
  background: rgba(26, 35, 50, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  transition: all 0.3s;
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(99, 102, 241, 0.3);
}

/* Insights Section */
.insights-section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header .header-left {
  gap: 1rem;
}

.section-icon {
  width: 40px;
  height: 40px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 1.25rem;
}

.section-header h2 {
  margin: 0;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.ai-status.active {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.status-dot {
  width: 8px;
  height: 8px;
  background: currentColor;
  border-radius: 50%;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

.refresh-btn {
  width: 40px;
  height: 40px;
  background: rgba(148, 163, 184, 0.1);
  border: none;
  border-radius: 10px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.refresh-btn.spinning i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  padding: 1.5rem;
  position: relative;
  cursor: pointer;
}

.card-background {
  position: absolute;
  inset: 0;
  opacity: 0.3;
  transition: opacity 0.3s;
}

.insight-card:hover .card-background {
  opacity: 0.5;
}

.card-content {
  position: relative;
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.insight-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.insight-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.insight-card h3 {
  margin: 0 0 0.75rem;
  font-size: 1.125rem;
}

.insight-card p {
  margin: 0 0 1rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.6;
}

.insight-metrics {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.metric-value {
  font-size: 0.875rem;
  font-weight: 600;
}

.apply-btn {
  padding: 0.625rem 1.25rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border: none;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.apply-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 20px rgba(99, 102, 241, 0.3);
}

/* Metrics Section */
.metrics-section {
  margin-bottom: 3rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  padding: 1.5rem;
  position: relative;
}

.metric-content {
  position: relative;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.menu-btn {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s;
}

.menu-btn:hover {
  background: rgba(148, 163, 184, 0.1);
}

.metric-body {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.metric-value-row {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.metric-unit {
  font-size: 1rem;
  color: var(--text-secondary);
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.metric-trend.positive {
  color: var(--success);
}

.metric-trend.negative {
  color: var(--danger);
}

.trend-text {
  color: var(--text-secondary);
}

.metric-chart {
  height: 60px;
  margin: 0.5rem 0;
}

.metric-progress {
  margin-top: auto;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  font-size: 0.813rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 6px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  position: relative;
  transition: width 0.5s ease;
}

.progress-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Analytics Section */
.analytics-section {
  margin-bottom: 3rem;
}

.section-tabs {
  margin-bottom: 2rem;
}

.tabs-header {
  display: flex;
  background: rgba(30, 41, 59, 0.5);
  padding: 0.25rem;
  border-radius: 12px;
  position: relative;
  gap: 0.25rem;
}

.tab-btn {
  padding: 0.875rem 1.5rem;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  border-radius: 10px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 1;
}

.tab-btn:hover {
  color: var(--text-primary);
}

.tab-btn.active {
  color: white;
}

.tab-indicator {
  position: absolute;
  top: 0.25rem;
  left: 0.25rem;
  height: calc(100% - 0.5rem);
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 10px;
  transition: all 0.3s ease;
  z-index: 0;
}

/* Tab Content */
.tab-content {
  margin-top: 2rem;
}

.analytics-card {
  padding: 2rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-header h3 {
  margin: 0;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.card-actions {
  display: flex;
  gap: 1rem;
}

.chart-selector {
  padding: 0.5rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  cursor: pointer;
}

.chart-container {
  height: 300px;
  margin-bottom: 1.5rem;
  position: relative;
}

/* Clean 3D Chart Minimal */
.chart-3d-clean {
  width: 100%;
  height: 250px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  perspective: 600px;
  padding: 1rem 0;
}

.bars-3d-container {
  display: flex;
  align-items: flex-end;
  gap: 16px;
  transform-style: preserve-3d;
  transition: transform 0.6s ease;
}

.bar-3d-clean {
  --bar-height: calc(var(--height) * 1.8);
  position: relative;
  width: 48px;
  height: var(--bar-height);
  animation: barFade 0.6s var(--delay) both;
}

@keyframes barFade {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bar-value {
  position: absolute;
  top: -22px;
  left: 50%;
  transform: translateX(-50%);
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.bar-body {
  width: 100%;
  height: 100%;
  background: var(--color);
  opacity: 0.85;
  border-radius: 4px 4px 0 0;
  transition: all 0.2s;
}

.bar-3d-clean:hover .bar-body {
  opacity: 1;
  transform: translateY(-2px);
}

.bar-name {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.688rem;
  color: var(--text-secondary);
  white-space: nowrap;
}

.css-3d-chart {
  width: 100%;
  height: 400px;
  background: radial-gradient(ellipse at center, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  perspective: 1000px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.chart-3d-wrapper {
  width: 100%;
  height: 350px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.1s ease-out;
  cursor: grab;
}

.chart-3d-wrapper:active {
  cursor: grabbing;
}

.chart-3d-base {
  position: absolute;
  width: 500px;
  height: 300px;
  left: 50%;
  bottom: 100px;
  margin-left: -250px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9));
  transform: rotateX(90deg) translateZ(-2px);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.chart-3d-base::before {
  content: '';
  position: absolute;
  inset: 0;
  background: 
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 40px,
      rgba(99, 102, 241, 0.1) 40px,
      rgba(99, 102, 241, 0.1) 41px
    ),
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 40px,
      rgba(99, 102, 241, 0.1) 40px,
      rgba(99, 102, 241, 0.1) 41px
    );
}

.chart-3d-bar {
  --bar-width: 60px;
  --bar-depth: 60px;
  --bar-height-value: calc(var(--bar-height) * 3);
  
  position: absolute;
  width: var(--bar-width);
  height: var(--bar-height-value);
  left: 50%;
  bottom: 100px;
  margin-left: calc(var(--bar-width) / -2);
  transform-style: preserve-3d;
  transition: all 0.3s ease;
}

.chart-3d-bar:hover {
  transform: translateX(calc(var(--bar-position) * 80px - 160px)) translateZ(70px) scale(1.05);
}

.chart-3d-bar:hover .bar-label {
  opacity: 1;
  transform: translateY(-20px);
}

.bar-front,
.bar-back,
.bar-left,
.bar-right,
.bar-top {
  position: absolute;
  background: var(--bar-color);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.bar-front {
  width: var(--bar-width);
  height: var(--bar-height-value);
  transform: translateZ(calc(var(--bar-depth) / 2));
  background: linear-gradient(135deg, var(--bar-color), color-mix(in srgb, var(--bar-color) 70%, black));
}

.bar-back {
  width: var(--bar-width);
  height: var(--bar-height-value);
  transform: translateZ(calc(var(--bar-depth) / -2));
  background: linear-gradient(135deg, color-mix(in srgb, var(--bar-color) 60%, black), color-mix(in srgb, var(--bar-color) 40%, black));
}

.bar-left {
  width: var(--bar-depth);
  height: var(--bar-height-value);
  left: 0;
  transform: rotateY(-90deg) translateZ(calc(var(--bar-width) / 2));
  background: linear-gradient(to right, color-mix(in srgb, var(--bar-color) 50%, black), color-mix(in srgb, var(--bar-color) 70%, black));
}

.bar-right {
  width: var(--bar-depth);
  height: var(--bar-height-value);
  right: 0;
  transform: rotateY(90deg) translateZ(calc(var(--bar-width) / 2));
  background: linear-gradient(to left, color-mix(in srgb, var(--bar-color) 50%, black), color-mix(in srgb, var(--bar-color) 70%, black));
}

.bar-top {
  width: var(--bar-width);
  height: var(--bar-depth);
  top: 0;
  transform: rotateX(90deg) translateZ(calc(var(--bar-depth) / 2));
  background: linear-gradient(135deg, var(--bar-color), color-mix(in srgb, var(--bar-color) 80%, white));
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.bar-label {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(26, 35, 50, 0.95);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--bar-color);
  opacity: 0.8;
  transition: all 0.3s ease;
  white-space: nowrap;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.label-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.label-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--bar-color);
}

.chart-3d-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  z-index: 10;
}

.control-btn {
  width: 40px;
  height: 40px;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 10px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(99, 102, 241, 0.3);
}

/* Add animation for floating bars */
@keyframes float3D {
  0%, 100% {
    transform: translateX(calc(var(--bar-position) * 80px - 160px)) translateZ(50px) translateY(0);
  }
  50% {
    transform: translateX(calc(var(--bar-position) * 80px - 160px)) translateZ(50px) translateY(-10px);
  }
}

.chart-3d-bar {
  animation: float3D 3s ease-in-out infinite;
  animation-delay: calc(var(--bar-position) * 0.2s);
}

.subjects-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}

.legend-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.legend-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* Habits Grid */
.habits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 2rem;
}

.heatmap-container {
  padding: 1rem;
}

.heatmap-months {
  display: flex;
  justify-content: space-around;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.heatmap-grid {
  display: grid;
  grid-template-columns: repeat(30, 1fr);
  grid-template-rows: repeat(6, 1fr);
  gap: 2px;
  margin-bottom: 1rem;
}

.heatmap-cell {
  aspect-ratio: 1;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s;
}

.heatmap-cell:hover {
  transform: scale(1.2);
  border: 1px solid var(--primary);
}

.heatmap-legend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.legend-gradient {
  width: 100px;
  height: 10px;
  background: linear-gradient(90deg, 
    rgba(148, 163, 184, 0.1),
    rgba(99, 102, 241, 0.9)
  );
  border-radius: 5px;
}

.time-distribution {
  height: 200px;
  margin-bottom: 1.5rem;
}

.best-times {
  background: rgba(30, 41, 59, 0.3);
  padding: 1.5rem;
  border-radius: 12px;
}

.best-times h4 {
  margin: 0 0 1rem;
  font-size: 1rem;
}

.time-slots {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.time-slot {
  display: grid;
  grid-template-columns: 20px 120px 1fr 50px;
  align-items: center;
  gap: 0.75rem;
}

.time-slot i {
  color: var(--warning);
}

.slot-bar {
  height: 6px;
  background: linear-gradient(90deg, var(--warning), var(--primary));
  border-radius: 3px;
}

.slot-value {
  text-align: right;
  font-weight: 600;
  color: var(--text-primary);
}

/* Goals Overview */
.goals-overview {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-icon.completed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.stat-icon.in-progress {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.stat-icon.upcoming {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.stat-info h3 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
}

.stat-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.goals-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.goal-card {
  padding: 1.5rem;
}

.goal-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.goal-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.goal-icon.completed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.goal-icon.in-progress {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.goal-icon.upcoming {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.goal-info {
  flex: 1;
}

.goal-info h4 {
  margin: 0 0 0.25rem;
  font-size: 1.125rem;
}

.goal-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.goal-deadline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.goal-progress {
  margin-bottom: 1rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.813rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.progress-track {
  height: 6px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.goal-tasks {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
}

.task-item i {
  color: var(--primary);
}

.task-item span.completed {
  text-decoration: line-through;
  color: var(--text-secondary);
}

/* Study Plan Section Minimal */
.study-plan-section {
  margin-bottom: 2rem;
}

.study-plan-card {
  padding: 1.75rem;
  max-width: 800px;
  margin: 0 auto;
}

.plan-header {
  margin-bottom: 1.5rem;
}

.plan-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.plan-title h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.plan-badge {
  padding: 0.25rem 0.625rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border-radius: 6px;
  font-size: 0.688rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-subtitle {
  margin: 0;
  font-size: 0.813rem;
  color: var(--text-secondary);
}

.plan-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  padding: 1.5rem 0;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.plan-stats .stat {
  text-align: center;
}

.plan-stats .stat-value {
  display: block;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary);
  line-height: 1;
  margin-bottom: 0.25rem;
}

.plan-stats .stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.plan-suggestion {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(99, 102, 241, 0.05);
  border-radius: 8px;
  margin: 1.5rem 0;
}

.plan-suggestion i {
  color: var(--primary);
  font-size: 1rem;
  margin-top: 0.125rem;
}

.plan-suggestion p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-primary);
  flex: 1;
  line-height: 1.5;
}

.plan-actions {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.btn-apply,
.btn-new {
  padding: 0.625rem 1.25rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.813rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-apply {
  background: var(--primary);
  color: white;
}

.btn-apply:hover {
  background: color-mix(in srgb, var(--primary) 85%, black);
  transform: translateY(-1px);
}

.btn-new {
  background: rgba(148, 163, 184, 0.1);
  color: var(--text-primary);
}

.btn-new:hover {
  background: rgba(148, 163, 184, 0.2);
}

.plan-metrics {
  display: flex;
  gap: 1.5rem;
}

.plan-metrics .metric {
  flex: 1;
}

.plan-metrics .metric-label {
  display: block;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.plan-metrics .metric-bar {
  height: 4px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.plan-metrics .metric-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
  transition: width 0.6s ease;
}

/* Recommendations Section */
.recommendations-section {
  margin-top: 3rem;
}

.generate-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  color: var(--primary);
  border-radius: 10px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.generate-btn:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.recommendation-card {
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.rec-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.rec-content {
  flex: 1;
}

.rec-content h4 {
  margin: 0 0 0.5rem;
  font-size: 1rem;
}

.rec-content p {
  margin: 0 0 1rem;
  color: var(--text-secondary);
  font-size: 0.813rem;
  line-height: 1.5;
}

.rec-impact {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.813rem;
}

.impact-bar {
  flex: 1;
  height: 4px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.impact-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.5s ease;
}

.impact-value {
  font-weight: 600;
  color: var(--success);
}

.implement-btn {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border: none;
  border-radius: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.implement-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 5px 20px rgba(99, 102, 241, 0.3);
}

/* Animations */
.animate-in {
  animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 1200px) {
  .habits-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-stats {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .time-selector {
    width: 100%;
    justify-content: space-between;
  }
  
  .period-btn span {
    display: none;
  }
  
  .insights-grid,
  .metrics-grid,
  .recommendations-grid {
    grid-template-columns: 1fr;
  }
  
  .heatmap-grid {
    grid-template-columns: repeat(15, 1fr);
  }
}

/* Gamification Ultra Minimal */
.gamification-minimal {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

/* Compact Progress Bar */
.progress-compact {
  padding: 1.25rem 1.5rem;
}

.progress-row {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.level-compact {
  display: flex;
  align-items: baseline;
  gap: 0.75rem;
  min-width: 140px;
}

.level-number-compact {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
  line-height: 1;
}

.level-label {
  font-size: 0.813rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.xp-bar-compact {
  flex: 1;
  height: 6px;
  background: rgba(148, 163, 184, 0.08);
  border-radius: 3px;
  position: relative;
  overflow: visible;
}

.xp-fill-compact {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 3px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.xp-label-compact {
  position: absolute;
  top: 12px;
  right: 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.stats-compact {
  display: flex;
  gap: 1.5rem;
}

.stat-compact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  opacity: 0.7;
  transition: all 0.2s;
}

.stat-compact.active {
  color: var(--warning);
  opacity: 1;
}

.stat-compact i {
  font-size: 0.875rem;
}

/* Achievement Badges */
.achievements-compact {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.achievements-header-compact {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.achievements-header-compact h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.progress-dots {
  display: flex;
  gap: 0.375rem;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(148, 163, 184, 0.2);
  transition: all 0.3s;
}

.dot.active {
  background: var(--primary);
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.5);
}

.achievement-badges {
  display: flex;
  gap: 0.75rem;
  flex: 1;
}

.badge-compact {
  width: 48px;
  height: 48px;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid transparent;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.badge-compact:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.badge-compact.common {
  border-color: rgba(148, 163, 184, 0.3);
  color: #94a3b8;
}

.badge-compact.rare {
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
  background: rgba(99, 102, 241, 0.05);
}

.badge-compact.epic {
  border-color: rgba(139, 92, 246, 0.3);
  color: #8b5cf6;
  background: rgba(139, 92, 246, 0.05);
}

.badge-compact.legendary {
  border-color: rgba(245, 158, 11, 0.3);
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
}

.badge-compact.just-unlocked {
  animation: badgeUnlock 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes badgeUnlock {
  0% {
    transform: scale(0) rotate(180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(10deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.badge-tooltip {
  position: absolute;
  bottom: 56px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s;
  z-index: 10;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.badge-compact:hover .badge-tooltip {
  opacity: 1;
  transform: translateX(-50%) translateY(-4px);
}

.badge-tooltip h4 {
  margin: 0 0 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.badge-tooltip p {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Leaderboard Compact */
.leaderboard-compact {
  padding: 1.25rem 1.5rem;
}

.leaderboard-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.leaderboard-header-compact h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.your-position {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary);
  padding: 0.25rem 0.75rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 20px;
}

.leaders-compact {
  display: flex;
  gap: 1rem;
}

.leader-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 10px;
  transition: all 0.2s;
}

.leader-item:hover {
  background: rgba(30, 41, 59, 0.5);
  transform: translateY(-2px);
}

.leader-item.first {
  background: rgba(245, 158, 11, 0.05);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.leader-item.is-user {
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.leader-rank {
  font-weight: 700;
  font-size: 0.875rem;
  color: var(--text-secondary);
  width: 20px;
  text-align: center;
}

.leader-item.first .leader-rank {
  color: #f59e0b;
}

.leader-item img {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  object-fit: cover;
}

.leader-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  min-width: 0;
}

.leader-name {
  font-size: 0.813rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.leader-xp {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Hide old gamification sections */
.gamification-section.minimal,
.gamification-section:not(.minimal) {
  display: none;
}

/* XP Progress Minimal */
.xp-progress-minimal {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.xp-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.level-info-minimal {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.level-badge-minimal {
  display: inline-flex;
  padding: 0.25rem 0.75rem;
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  align-self: flex-start;
}

.level-info-minimal h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.xp-text {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.xp-current {
  font-weight: 700;
  color: var(--primary);
}

.xp-separator {
  color: var(--text-secondary);
}

.xp-total {
  color: var(--text-secondary);
}

.xp-bar-minimal {
  height: 8px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.xp-fill-minimal {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 4px;
  transition: width 0.5s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 0.5rem;
}

.xp-percentage {
  position: absolute;
  right: 8px;
  font-size: 0.625rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.xp-stats-minimal {
  display: flex;
  gap: 1.5rem;
  font-size: 0.813rem;
}

.stat-item-minimal {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.stat-item-minimal i {
  font-size: 0.875rem;
  opacity: 0.7;
}

/* Achievements Minimal */
.achievements-minimal {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section-header-minimal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
}

.section-header-minimal h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.achievement-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.achievement-list-minimal {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.achievement-item-minimal {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.achievement-item-minimal:hover {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(99, 102, 241, 0.2);
  transform: translateX(4px);
}

.achievement-item-minimal.unlocked {
  background: rgba(99, 102, 241, 0.05);
  border-color: rgba(99, 102, 241, 0.2);
}

.achievement-item-minimal.just-unlocked {
  animation: achievementUnlock 0.5s ease;
}

@keyframes achievementUnlock {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.achievement-icon-minimal {
  width: 40px;
  height: 40px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--text-secondary);
  transition: all 0.2s;
}

.achievement-item-minimal.unlocked .achievement-icon-minimal {
  background: rgba(99, 102, 241, 0.1);
}

.achievement-content-minimal {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.achievement-content-minimal h4 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.achievement-bottom {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
}

.rarity-minimal {
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.xp-reward-minimal {
  color: var(--text-secondary);
}

.achievement-status {
  font-size: 1rem;
  color: var(--text-secondary);
}

.achievement-status .fa-check-circle {
  color: var(--success);
}

/* Ranking Minimal */
.ranking-minimal {
  padding: 1.5rem;
}

.ranking-header-minimal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.ranking-header-minimal h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.user-rank-minimal {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--primary);
}

.ranking-list-minimal {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.rank-item-minimal {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s;
}

.rank-item-minimal:hover {
  background: rgba(148, 163, 184, 0.05);
}

.rank-item-minimal.is-user {
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.rank-number {
  width: 24px;
  text-align: center;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.rank-item-minimal:nth-child(1) .rank-number {
  color: #f59e0b;
}

.rank-item-minimal:nth-child(2) .rank-number {
  color: #94a3b8;
}

.rank-item-minimal:nth-child(3) .rank-number {
  color: #dc2626;
}

.player-avatar-minimal {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  object-fit: cover;
}

.player-name-minimal {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
}

.player-xp-minimal {
  font-size: 0.813rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.rank-item-minimal i {
  font-size: 0.75rem;
  width: 16px;
  text-align: center;
}

/* Gamification Section Original (oculto) */
.gamification-section:not(.minimal) {
  display: none;
}

.xp-card {
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.xp-content {
  position: relative;
  z-index: 1;
}

.level-badge {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.level-ring {
  position: relative;
  width: 120px;
  height: 120px;
  flex-shrink: 0;
}

.level-ring svg {
  transform: rotate(-90deg);
  width: 100%;
  height: 100%;
}

.level-number {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.level-info h3 {
  margin: 0 0 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.level-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.xp-progress {
  margin-top: 1.5rem;
}

.xp-bar {
  height: 12px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.xp-fill {
  height: 100%;
  position: relative;
  border-radius: 6px;
  transition: width 0.5s ease;
}

.xp-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

.xp-stats {
  display: flex;
  gap: 2rem;
}

.xp-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.xp-stat i {
  color: var(--warning);
}

/* Achievements Showcase */
.achievements-showcase {
  position: relative;
}

.showcase-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.showcase-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
}

.showcase-header i {
  color: var(--warning);
}

.view-all-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  color: var(--primary);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.view-all-btn:hover {
  transform: translateX(2px);
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.achievement-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.achievement-card:hover {
  transform: translateY(-2px);
  border-color: rgba(99, 102, 241, 0.3);
}

.achievement-card.unlocked {
  background: rgba(99, 102, 241, 0.05);
  border-color: rgba(99, 102, 241, 0.3);
}

.achievement-card.legendary {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(239, 68, 68, 0.1));
  border-color: rgba(245, 158, 11, 0.5);
}

.achievement-glow {
  position: absolute;
  inset: -50%;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.3), transparent);
  animation: pulse 3s infinite;
}

.achievement-icon {
  width: 48px;
  height: 48px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--primary);
  margin-bottom: 0.75rem;
}

.achievement-card.unlocked .achievement-icon {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
}

.achievement-info h4 {
  margin: 0 0 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.achievement-info p {
  margin: 0 0 0.75rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.achievement-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.rarity {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.625rem;
}

.rarity.common {
  background: rgba(148, 163, 184, 0.1);
  color: var(--text-secondary);
}

.rarity.rare {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.rarity.epic {
  background: rgba(139, 92, 246, 0.1);
  color: var(--secondary);
}

.rarity.legendary {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.xp-reward {
  color: var(--success);
  font-weight: 600;
}

.unlock-animation {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  color: var(--warning);
  animation: sparkle 1s ease-out;
}

@keyframes sparkle {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: scale(0) rotate(360deg);
    opacity: 0;
  }
}

/* Leaderboard Mini */
.leaderboard-mini {
  padding: 1.5rem;
}

.leaderboard-mini .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.leaderboard-mini h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
}

.rank-badge {
  padding: 0.375rem 0.75rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.875rem;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 10px;
  transition: all 0.2s;
}

.leaderboard-item:hover {
  background: rgba(30, 41, 59, 0.5);
}

.leaderboard-item.current {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
}

.rank-position {
  width: 28px;
  height: 28px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.leaderboard-item:nth-child(1) .rank-position {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
}

.leaderboard-item:nth-child(2) .rank-position {
  background: linear-gradient(135deg, #e5e7eb, #9ca3af);
  color: white;
}

.leaderboard-item:nth-child(3) .rank-position {
  background: linear-gradient(135deg, #f87171, #dc2626);
  color: white;
}

.player-avatar {
  position: relative;
  width: 40px;
  height: 40px;
}

.player-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  object-fit: cover;
}

.level-indicator {
  position: absolute;
  bottom: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: var(--card-bg);
  border: 2px solid var(--primary);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  font-weight: 700;
  color: var(--primary);
}

.player-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.player-name {
  font-weight: 600;
  font-size: 0.875rem;
}

.player-xp {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.rank-change {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.rank-change.up {
  color: var(--success);
}

.rank-change.down {
  color: var(--danger);
}

@media (max-width: 1400px) {
  .gamification-section {
    grid-template-columns: 1fr 1fr;
  }
  
  .achievements-showcase {
    grid-column: span 2;
  }
}

@media (max-width: 1200px) {
  .gamification-section.minimal {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .achievements-minimal {
    order: 2;
  }
  
  .ranking-minimal {
    order: 3;
  }
}

@media (max-width: 768px) {
  .gamification-section {
    grid-template-columns: 1fr;
  }
  
  .achievements-grid {
    grid-template-columns: 1fr;
  }
  
  .level-badge {
    flex-direction: column;
    text-align: center;
  }
  
  /* Mobile adjustments for minimal version */
  .xp-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .xp-stats-minimal {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .achievement-item-minimal {
    padding: 0.75rem;
  }
  
  .ar-mode-toggle.minimal {
    bottom: 20px;
    right: 20px;
  }
  
  .ar-button-minimal {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  
  .metrics-3d-container {
    width: 250px;
    height: 250px;
  }
  
  .metric-3d-card {
    width: 230px;
    height: 230px;
    padding: 1.5rem;
  }
  
  .metric-3d-content h3 {
    font-size: 2rem;
  }
  
  .metric-3d-content i {
    font-size: 2.5rem;
  }
}

/* AR Mode Styles Minimalista */
/* Progress Overview */
.progress-overview {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.level-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.level-badge {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
}

.level-badge .level-number {
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.level-info {
  flex: 1;
}

.level-info h3 {
  margin: 0 0 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.xp-progress {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.xp-bar {
  height: 6px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.xp-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 3px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.xp-text {
  font-size: 0.813rem;
  color: var(--text-secondary);
}

.stats-row {
  display: flex;
  gap: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stat-item i {
  font-size: 1rem;
  opacity: 0.7;
}

.stat-item .fa-fire {
  color: var(--warning);
}

.stat-item .fa-bolt {
  color: var(--primary);
}

.stat-item .fa-trophy {
  color: var(--success);
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-left: 0.25rem;
}

/* Achievements Minimal */
.achievements-minimal {
  padding: 1.5rem;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-title h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.achievement-count {
  font-size: 0.813rem;
  color: var(--text-secondary);
  background: rgba(99, 102, 241, 0.1);
  padding: 0.25rem 0.625rem;
  border-radius: 12px;
}

.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.875rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 10px;
  transition: all 0.2s;
  cursor: pointer;
}

.achievement-item:hover {
  background: rgba(30, 41, 59, 0.5);
  transform: translateX(4px);
}

.achievement-item.unlocked {
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.achievement-item.locked {
  opacity: 0.6;
}

.achievement-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
  background: rgba(148, 163, 184, 0.1);
  color: var(--text-secondary);
}

.achievement-icon.common {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

.achievement-icon.rare {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.achievement-icon.epic {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.achievement-icon.legendary {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.achievement-details {
  flex: 1;
  min-width: 0;
}

.achievement-details h4 {
  margin: 0 0 0.125rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.achievement-details p {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.achievement-status {
  font-size: 1rem;
  color: var(--text-secondary);
}

.achievement-status .fa-check-circle {
  color: var(--success);
}

.ar-mode-toggle.minimal {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 100;
}

/* 3D View Toggle */
.view-3d-toggle {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 100;
}

.view-3d-button {
  padding: 0.75rem 1rem;
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 10px;
  color: var(--text-primary);
  cursor: pointer;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-3d-button:hover {
  background: rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
}

.view-3d-button.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.view-3d-button i {
  font-size: 0.875rem;
}

/* AR Overlay Minimal */
.ar-overlay.minimal {
  position: fixed;
  inset: 0;
  z-index: 1000;
  background: rgba(10, 15, 27, 0.95);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.ar-view-minimal {
  width: 90%;
  max-width: 800px;
  height: 600px;
  position: relative;
}

.close-ar-btn {
  position: absolute;
  top: -50px;
  right: 0;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.close-ar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.ar-content-minimal {
  width: 100%;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
}

.metrics-3d-container {
  width: 300px;
  height: 300px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s ease;
}

.metric-3d-card {
  position: absolute;
  width: 280px;
  height: 280px;
  background: rgba(26, 35, 50, 0.9);
  border: 2px solid rgba(99, 102, 241, 0.3);
  border-radius: 20px;
  padding: 2rem;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
}

.metric-3d-content {
  text-align: center;
  color: white;
}

.metric-3d-content i {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.metric-3d-content h3 {
  font-size: 2.5rem;
  margin: 0 0 0.5rem;
  font-weight: 700;
}

.metric-3d-content p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 1.5rem;
}

.metric-3d-progress {
  width: 200px;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin: 0 auto;
}

.progress-3d-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.ar-controls-minimal {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
}

.control-3d {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-3d:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.control-3d.active {
  background: var(--primary);
  border-color: var(--primary);
}

.control-3d i {
  font-size: 1.125rem;
}

/* AR Mode Original Styles (oculto) */
.ar-mode-toggle:not(.minimal) {
  display: none;
}

.ar-button {
  padding: 0.75rem 1.5rem;
  background: rgba(99, 102, 241, 0.1);
  border: 2px solid var(--primary);
  border-radius: 30px;
  color: var(--primary);
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
}

.ar-button:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

.ar-button.active {
  background: var(--primary);
  color: white;
  box-shadow: 0 10px 40px rgba(99, 102, 241, 0.5);
}

.ar-pulse {
  position: absolute;
  inset: -20px;
  background: radial-gradient(circle, var(--primary), transparent);
  animation: arPulse 2s infinite;
}

@keyframes arPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 0;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

.ar-overlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
  background: black;
}

.ar-camera-view {
  width: 100%;
  height: 100%;
  position: relative;
  background: 
    linear-gradient(135deg, #0a0f1b 0%, #1a2332 100%),
    radial-gradient(circle at 30% 40%, rgba(99, 102, 241, 0.1), transparent),
    radial-gradient(circle at 70% 60%, rgba(139, 92, 246, 0.1), transparent);
  overflow: hidden;
  perspective: 1200px;
}

.ar-grid {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
  background-size: 100px 100px;
  transform: rotateX(60deg) translateZ(-100px);
  animation: arGridMove 20s linear infinite;
}

@keyframes arGridMove {
  0% { transform: rotateX(60deg) translateZ(-100px) translateY(0); }
  100% { transform: rotateX(60deg) translateZ(-100px) translateY(100px); }
}

.ar-tracking-points {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.tracking-point {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(99, 102, 241, 0.5);
  border-radius: 50%;
  animation: trackingPulse 2s infinite;
}

.tracking-point:nth-child(1) { top: 20%; left: 15%; }
.tracking-point:nth-child(2) { top: 20%; left: 50%; }
.tracking-point:nth-child(3) { top: 20%; left: 85%; }
.tracking-point:nth-child(4) { top: 50%; left: 10%; }
.tracking-point:nth-child(5) { top: 50%; left: 50%; }
.tracking-point:nth-child(6) { top: 50%; left: 90%; }
.tracking-point:nth-child(7) { top: 80%; left: 20%; }
.tracking-point:nth-child(8) { top: 80%; left: 50%; }
.tracking-point:nth-child(9) { top: 80%; left: 80%; }

@keyframes trackingPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.8;
  }
}

.ar-content {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
}

.ar-metrics-3d {
  position: relative;
  width: 600px;
  height: 400px;
  transform-style: preserve-3d;
  animation: arFloat 6s ease-in-out infinite;
}

@keyframes arFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20px); }
}

.ar-metric-card {
  position: absolute;
  width: 200px;
  height: 250px;
  background: rgba(26, 35, 50, 0.8);
  backdrop-filter: blur(10px);
  border: 2px solid var(--card-color);
  border-radius: 20px;
  padding: 1.5rem;
  transform-style: preserve-3d;
  transition: all 0.5s ease;
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.3),
    inset 0 0 30px rgba(99, 102, 241, 0.1);
}

.ar-metric-card::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, var(--card-color), transparent);
  border-radius: 20px;
  opacity: 0.5;
  z-index: -1;
  animation: arGlow 3s ease-in-out infinite;
}

@keyframes arGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

.ar-card-content {
  text-align: center;
  color: white;
}

.ar-card-content i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--card-color);
}

.ar-card-content h3 {
  font-size: 2rem;
  margin: 0 0 0.5rem;
}

.ar-card-content p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 1rem;
}

.ar-progress-ring {
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.ar-insights-hologram {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateZ(200px);
  z-index: 10;
}

.hologram-container {
  position: relative;
  padding: 2rem;
  background: rgba(0, 20, 40, 0.8);
  border: 2px solid rgba(99, 102, 241, 0.5);
  border-radius: 20px;
  transform: rotateY(15deg);
  animation: hologramRotate 10s linear infinite;
}

@keyframes hologramRotate {
  0% { transform: rotateY(15deg); }
  100% { transform: rotateY(375deg); }
}

.hologram-content {
  position: relative;
  z-index: 2;
  color: rgba(255, 255, 255, 0.9);
}

.hologram-content h2 {
  text-align: center;
  margin: 0 0 1.5rem;
  font-size: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.insight-hologram {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 10px;
}

.hologram-icon {
  font-size: 2rem;
}

.hologram-text h3 {
  margin: 0 0 0.25rem;
  font-size: 1rem;
}

.hologram-text p {
  margin: 0;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.7);
}

.hologram-effect {
  position: absolute;
  inset: 0;
  background: 
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(99, 102, 241, 0.03) 2px,
      rgba(99, 102, 241, 0.03) 4px
    );
  border-radius: 20px;
  animation: hologramScan 2s linear infinite;
}

@keyframes hologramScan {
  0% { transform: translateY(0); }
  100% { transform: translateY(10px); }
}

.ar-controls {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  z-index: 20;
}

.ar-control-btn {
  width: 50px;
  height: 50px;
  background: rgba(30, 41, 59, 0.9);
  border: 2px solid var(--primary);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.ar-control-btn:hover {
  background: var(--primary);
  transform: scale(1.1);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.5);
}

.ar-info {
  position: absolute;
  top: 20px;
  left: 20px;
  color: white;
  font-family: monospace;
  z-index: 20;
}

.ar-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(16, 185, 129, 0.2);
  border: 1px solid var(--success);
  border-radius: 20px;
  font-size: 0.875rem;
}

.ar-status i {
  color: var(--success);
  animation: pulse 2s infinite;
}

.ar-coordinates {
  display: flex;
  gap: 1rem;
  font-size: 0.813rem;
  opacity: 0.7;
}

.ar-coordinates span {
  padding: 0.25rem 0.5rem;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}
</style>