<template>
  <div class="safe-component-wrapper">
    <div v-if="hasError" class="error-fallback">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Oops! Algo deu errado</h3>
        <p>{{ errorMessage }}</p>
        <button @click="retry" class="retry-button">
          <i class="fas fa-redo"></i>
          Tentar Novamente
        </button>
        <button @click="goHome" class="home-button">
          <i class="fas fa-home"></i>
          Ir para Home
        </button>
      </div>
    </div>
    
    <div v-else-if="isLoading" class="loading-fallback">
      <div class="loading-content">
        <i class="fas fa-spinner fa-spin"></i>
        <p>{{ loadingMessage }}</p>
      </div>
    </div>
    
    <template v-else>
      <slot />
    </template>
  </div>
</template>

<script>
import { ref, onMounted, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'SafeComponentWrapper',
  
  props: {
    loadingMessage: {
      type: String,
      default: 'Carregando...'
    },
    errorMessage: {
      type: String,
      default: 'Ocorreu um erro inesperado. Por favor, tente novamente.'
    },
    loadingDelay: {
      type: Number,
      default: 0
    },
    retryable: {
      type: Boolean,
      default: true
    }
  },
  
  setup(props, { emit }) {
    const router = useRouter()
    const hasError = ref(false)
    const isLoading = ref(false)
    const error = ref(null)
    const retryCount = ref(0)
    const maxRetries = 3
    
    onMounted(() => {
      if (props.loadingDelay > 0) {
        isLoading.value = true
        setTimeout(() => {
          isLoading.value = false
        }, props.loadingDelay)
      }
    })
    
    onErrorCaptured((err, instance, info) => {
      console.error('Component error caught:', err)
      console.error('Error info:', info)
      
      error.value = err
      hasError.value = true
      isLoading.value = false
      
      // Emit error event
      emit('error', { error: err, instance, info })
      
      // Log to error handler if available
      if (instance?.$errorHandler) {
        instance.$errorHandler.handleVueError(err, instance, info)
      }
      
      // Prevent error propagation
      return false
    })
    
    const retry = () => {
      if (retryCount.value < maxRetries) {
        retryCount.value++
        hasError.value = false
        error.value = null
        
        // Force re-render of slot content
        emit('retry', retryCount.value)
        
        // Add loading state for retry
        isLoading.value = true
        setTimeout(() => {
          isLoading.value = false
        }, 500)
      } else {
        alert('Número máximo de tentativas excedido. Por favor, recarregue a página.')
      }
    }
    
    const goHome = () => {
      router.push('/')
    }
    
    return {
      hasError,
      isLoading,
      error,
      retry,
      goHome
    }
  }
}
</script>

<style scoped>
.safe-component-wrapper {
  min-height: inherit;
  width: 100%;
}

/* Error Fallback */
.error-fallback {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.error-content {
  text-align: center;
  max-width: 400px;
}

.error-content i {
  font-size: 48px;
  color: #f56565;
  margin-bottom: 20px;
}

.error-content h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.error-content p {
  color: #718096;
  margin-bottom: 24px;
  line-height: 1.6;
}

.retry-button,
.home-button {
  margin: 0 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.retry-button {
  background: #667eea;
  color: white;
}

.retry-button:hover {
  background: #5a67d8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.home-button {
  background: #e2e8f0;
  color: #4a5568;
}

.home-button:hover {
  background: #cbd5e0;
  transform: translateY(-1px);
}

/* Loading Fallback */
.loading-fallback {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
}

.loading-content i {
  font-size: 32px;
  color: #667eea;
  margin-bottom: 16px;
}

.loading-content p {
  color: #718096;
  font-size: 16px;
}

/* Responsive */
@media (max-width: 640px) {
  .retry-button,
  .home-button {
    display: block;
    width: 100%;
    margin: 8px 0;
  }
}
</style>