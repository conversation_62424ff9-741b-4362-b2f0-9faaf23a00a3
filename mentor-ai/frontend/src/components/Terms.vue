<template>
  <div class="terms-page">
    <div class="container">
      <h1>Termos de Uso</h1>
      <p class="last-update">Última atualização: {{ currentDate }}</p>
      
      <section>
        <h2>1. Aceitação dos Termos</h2>
        <p>Ao usar a Sophos Academy, você concorda com estes termos de uso.</p>
      </section>
      
      <section>
        <h2>2. Uso da Plataforma</h2>
        <p>A plataforma é destinada exclusivamente para fins educacionais e de estudo.</p>
      </section>
      
      <section>
        <h2>3. Conta de Usuário</h2>
        <p>Você é responsável por manter a confidencialidade de sua conta e senha.</p>
      </section>
      
      <section>
        <h2>4. Conteúdo e Propriedade Intelectual</h2>
        <p>Todo o conteúdo da plataforma é protegido por direitos autorais.</p>
      </section>
      
      <section>
        <h2>5. Limitação de Responsabilidade</h2>
        <p>A Sophos Academy não se responsabiliza por danos indiretos ou consequenciais.</p>
      </section>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Terms',
  computed: {
    currentDate() {
      return new Date().toLocaleDateString('pt-BR', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    }
  }
}
</script>

<style scoped>
.terms-page {
  padding: 2rem;
  min-height: 100vh;
  background: var(--background-color);
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--card-bg);
  padding: 3rem;
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
}

h1 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.last-update {
  color: var(--text-secondary);
  font-style: italic;
  margin-bottom: 2rem;
}

section {
  margin-bottom: 2rem;
}

h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

p {
  color: var(--text-secondary);
  line-height: 1.6;
}
</style>