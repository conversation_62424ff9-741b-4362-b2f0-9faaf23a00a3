<template>
  <div class="event-detail-overlay" @click.self="$emit('close')">
    <div class="event-detail-modal">
      <div class="modal-header">
        <h2>
          <i class="fas fa-calendar-check"></i> 
          {{ event.title }}
        </h2>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <!-- Event Info -->
        <div class="event-info-section">
          <div class="info-row">
            <div class="info-item">
              <i class="fas fa-calendar"></i>
              <span>{{ formatDate(event.start) }}</span>
            </div>
            <div class="info-item">
              <i class="fas fa-clock"></i>
              <span>{{ formatTime(event.start) }} - {{ formatTime(event.end) }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <i class="fas fa-book"></i>
              <span :style="{ color: subjectColor }">{{ subjectName }}</span>
            </div>
            <div class="info-item" v-if="event.revisionType">
              <i class="fas fa-tag"></i>
              <span>{{ event.revisionType }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="priority-badge" :data-priority="event.priority?.toLowerCase()">
                <i :class="getPriorityIcon(event.priority)"></i>
                {{ event.priority }}
              </span>
            </div>
          </div>
        </div>

        <!-- Progress Section -->
        <div v-if="event.isRevision" class="progress-section">
          <h3>Progresso da Revisão</h3>
          <div class="progress-container">
            <div class="progress-header">
              <span>{{ event.progress || 0 }}%</span>
              <span v-if="event.completed" class="completed-badge">
                <i class="fas fa-check-circle"></i> Concluído
              </span>
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: (event.progress || 0) + '%' }"
              ></div>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div v-if="event.description" class="description-section">
          <h3>Descrição</h3>
          <p>{{ event.description }}</p>
        </div>

        <!-- Actions -->
        <div class="actions-section">
          <button 
            v-if="!event.completed" 
            @click="startRevision" 
            class="btn-primary"
          >
            <i class="fas fa-play"></i>
            Iniciar Revisão
          </button>
          <button @click="editEvent" class="btn-secondary">
            <i class="fas fa-edit"></i>
            Editar
          </button>
          <button @click="deleteEvent" class="btn-danger">
            <i class="fas fa-trash"></i>
            Excluir
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export default {
  name: 'EventDetail',
  props: {
    event: {
      type: Object,
      required: true
    }
  },
  computed: {
    subjectName() {
      const subjects = this.$store.state.calendar?.subjects || [];
      const subject = subjects.find(s => s.id === this.event.subject);
      return subject ? subject.name : 'Sem disciplina';
    },
    subjectColor() {
      const subjects = this.$store.state.calendar?.subjects || [];
      const subject = subjects.find(s => s.id === this.event.subject);
      return subject ? subject.color : '#6366f1';
    }
  },
  methods: {
    formatDate(dateStr) {
      return format(new Date(dateStr), "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
    },
    formatTime(dateStr) {
      return format(new Date(dateStr), 'HH:mm');
    },
    getPriorityIcon(priority) {
      const icons = {
        'Alta': 'fas fa-fire',
        'Média': 'fas fa-bolt',
        'Baixa': 'fas fa-leaf'
      };
      return icons[priority] || 'fas fa-flag';
    },
    startRevision() {
      this.$emit('start-revision', this.event);
      this.$emit('close');
    },
    editEvent() {
      this.$emit('edit', this.event);
      this.$emit('close');
    },
    deleteEvent() {
      if (confirm('Tem certeza que deseja excluir este evento?')) {
        this.$emit('delete', this.event);
        this.$emit('close');
      }
    }
  }
};
</script>

<style scoped>
.event-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.event-detail-modal {
  background: #1e293b;
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.modal-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0;
}

.modal-header h2 i {
  color: #6366f1;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-body {
  padding: 1.5rem;
}

.event-info-section {
  margin-bottom: 1.5rem;
}

.info-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
}

.info-item i {
  color: #6366f1;
  width: 20px;
}

.priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.priority-badge[data-priority="alta"] {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.priority-badge[data-priority="média"] {
  background: rgba(251, 146, 60, 0.1);
  color: #fb923c;
}

.priority-badge[data-priority="baixa"] {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.progress-section,
.description-section {
  margin-bottom: 1.5rem;
}

.progress-section h3,
.description-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #94a3b8;
  margin-bottom: 1rem;
}

.progress-container {
  background: rgba(30, 41, 59, 0.5);
  padding: 1rem;
  border-radius: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.completed-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #22c55e;
  font-size: 0.875rem;
}

.progress-bar {
  height: 8px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  transition: width 0.3s;
}

.description-section p {
  color: #cbd5e1;
  line-height: 1.6;
}

.actions-section {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.btn-primary,
.btn-secondary,
.btn-danger {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

.btn-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
}

.btn-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.btn-danger:hover {
  background: rgba(239, 68, 68, 0.2);
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}
</style>