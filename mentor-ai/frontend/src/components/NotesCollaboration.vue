<template>
  <div class="notes-collaboration-container">
    <canvas ref="backgroundCanvas" class="background-canvas"></canvas>
    
    <div class="collaboration-header">
      <button @click="$router.back()" class="back-btn">
        <i class="fas fa-arrow-left"></i>
      </button>
      
      <div class="header-content">
        <h1 class="page-title">
          <i class="fas fa-share-nodes"></i>
          Colaboração e Compartilhamento
        </h1>
        <p class="page-subtitle">
          Trabalhe em equipe e compartilhe conhecimento
        </p>
      </div>

      <div class="header-actions">
        <button class="action-btn create-space" @click="showCreateSpace = true">
          <i class="fas fa-plus-circle"></i>
          Criar Espaço
        </button>
      </div>
    </div>

    <div class="collaboration-content">
      <!-- Active Sessions Section -->
      <section class="active-sessions">
        <h2 class="section-title">
          <i class="fas fa-users"></i>
          Sess<PERSON>es Ativas
        </h2>
        
        <div class="sessions-grid">
          <div 
            v-for="session in activeSessions" 
            :key="session.id"
            class="session-card"
            @click="joinSession(session)"
          >
            <div class="session-header">
              <h3>{{ session.title }}</h3>
              <span class="session-status" :class="session.status">
                <i class="fas fa-circle"></i>
                {{ session.participants }} participantes
              </span>
            </div>
            
            <div class="session-preview">
              <p>{{ session.description }}</p>
              <div class="session-tags">
                <span v-for="tag in session.tags" :key="tag" class="tag">
                  {{ tag }}
                </span>
              </div>
            </div>
            
            <div class="session-footer">
              <div class="participants">
                <img 
                  v-for="(user, idx) in session.users.slice(0, 3)" 
                  :key="idx"
                  :src="user.avatar"
                  :alt="user.name"
                  class="participant-avatar"
                />
                <span v-if="session.users.length > 3" class="more-participants">
                  +{{ session.users.length - 3 }}
                </span>
              </div>
              
              <button class="join-btn">
                <i class="fas fa-sign-in-alt"></i>
                Entrar
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Shared Spaces Section -->
      <section class="shared-spaces">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-folder-open"></i>
            Espaços Compartilhados
          </h2>
          
          <div class="filter-controls">
            <input 
              v-model="searchQuery"
              type="text" 
              placeholder="Buscar espaços..."
              class="search-input"
            >
            
            <select v-model="filterType" class="filter-select">
              <option value="all">Todos</option>
              <option value="owned">Meus Espaços</option>
              <option value="shared">Compartilhados Comigo</option>
              <option value="public">Públicos</option>
            </select>
          </div>
        </div>
        
        <div class="spaces-grid">
          <div 
            v-for="space in filteredSpaces" 
            :key="space.id"
            class="space-card"
            @click="openSpace(space)"
          >
            <div class="space-icon">
              <i :class="space.icon"></i>
            </div>
            
            <div class="space-info">
              <h3>{{ space.name }}</h3>
              <p>{{ space.description }}</p>
              
              <div class="space-meta">
                <span class="meta-item">
                  <i class="fas fa-file"></i>
                  {{ space.documents }} documentos
                </span>
                <span class="meta-item">
                  <i class="fas fa-users"></i>
                  {{ space.members }} membros
                </span>
                <span class="meta-item">
                  <i class="fas fa-clock"></i>
                  {{ space.lastActivity }}
                </span>
              </div>
            </div>
            
            <div class="space-actions">
              <button class="action-icon" @click.stop="shareSpace(space)">
                <i class="fas fa-share"></i>
              </button>
              <button class="action-icon" @click.stop="editSpace(space)">
                <i class="fas fa-edit"></i>
              </button>
              <button class="action-icon danger" @click.stop="deleteSpace(space)">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Real-time Collaboration Tools -->
      <section class="collaboration-tools">
        <h2 class="section-title">
          <i class="fas fa-tools"></i>
          Ferramentas de Colaboração
        </h2>
        
        <div class="tools-grid">
          <div class="tool-card">
            <div class="tool-icon">
              <i class="fas fa-video"></i>
            </div>
            <h3>Chamadas de Vídeo</h3>
            <p>Discussões em tempo real com compartilhamento de tela</p>
            <button class="tool-btn">Iniciar Chamada</button>
          </div>
          
          <div class="tool-card">
            <div class="tool-icon">
              <i class="fas fa-comments"></i>
            </div>
            <h3>Chat em Tempo Real</h3>
            <p>Mensagens instantâneas com suporte a markdown</p>
            <button class="tool-btn">Abrir Chat</button>
          </div>
          
          <div class="tool-card">
            <div class="tool-icon">
              <i class="fas fa-chalkboard"></i>
            </div>
            <h3>Quadro Colaborativo</h3>
            <p>Whiteboard digital para brainstorming visual</p>
            <button class="tool-btn">Criar Quadro</button>
          </div>
          
          <div class="tool-card">
            <div class="tool-icon">
              <i class="fas fa-code-branch"></i>
            </div>
            <h3>Controle de Versão</h3>
            <p>Histórico completo de alterações e colaboradores</p>
            <button class="tool-btn">Ver Histórico</button>
          </div>
        </div>
      </section>

      <!-- Activity Feed -->
      <section class="activity-feed">
        <h2 class="section-title">
          <i class="fas fa-stream"></i>
          Atividade Recente
        </h2>
        
        <div class="activity-timeline">
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
            <div class="activity-icon" :class="activity.type">
              <i :class="activity.icon"></i>
            </div>
            
            <div class="activity-content">
              <h4>{{ activity.user }}</h4>
              <p>{{ activity.action }}</p>
              <span class="activity-time">{{ activity.time }}</span>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Create Space Modal -->
    <div v-if="showCreateSpace" class="modal-overlay" @click="showCreateSpace = false">
      <div class="modal" @click.stop>
        <h2>Criar Novo Espaço</h2>
        
        <form @submit.prevent="createNewSpace">
          <div class="form-group">
            <label>Nome do Espaço</label>
            <input 
              v-model="newSpace.name" 
              type="text" 
              placeholder="Ex: Projeto de Pesquisa"
              required
            >
          </div>
          
          <div class="form-group">
            <label>Descrição</label>
            <textarea 
              v-model="newSpace.description" 
              placeholder="Descreva o propósito deste espaço..."
              rows="3"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label>Tipo de Acesso</label>
            <select v-model="newSpace.accessType">
              <option value="private">Privado</option>
              <option value="team">Equipe</option>
              <option value="public">Público</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>Convidar Membros</label>
            <input 
              v-model="newSpace.inviteEmails" 
              type="text" 
              placeholder="emails separados por vírgula"
            >
          </div>
          
          <div class="modal-actions">
            <button type="button" @click="showCreateSpace = false" class="cancel-btn">
              Cancelar
            </button>
            <button type="submit" class="submit-btn">
              Criar Espaço
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'NotesCollaboration',
  setup() {
    const router = useRouter()
    const store = useStore()
    const backgroundCanvas = ref(null)
    
    // State
    const showCreateSpace = ref(false)
    const searchQuery = ref('')
    const filterType = ref('all')
    
    const newSpace = ref({
      name: '',
      description: '',
      accessType: 'private',
      inviteEmails: ''
    })
    
    // Mock data
    const activeSessions = ref([
      {
        id: 1,
        title: 'Revisão de Anatomia',
        description: 'Sessão colaborativa para revisar sistema cardiovascular',
        participants: 5,
        status: 'active',
        tags: ['Anatomia', 'Cardiovascular'],
        users: [
          { name: 'Ana', avatar: 'https://i.pravatar.cc/40?img=1' },
          { name: 'Bruno', avatar: 'https://i.pravatar.cc/40?img=2' },
          { name: 'Carlos', avatar: 'https://i.pravatar.cc/40?img=3' },
          { name: 'Diana', avatar: 'https://i.pravatar.cc/40?img=4' },
          { name: 'Eduardo', avatar: 'https://i.pravatar.cc/40?img=5' }
        ]
      },
      {
        id: 2,
        title: 'Casos Clínicos',
        description: 'Discussão de casos complexos de neurologia',
        participants: 3,
        status: 'active',
        tags: ['Neurologia', 'Casos'],
        users: [
          { name: 'Fernanda', avatar: 'https://i.pravatar.cc/40?img=6' },
          { name: 'Gabriel', avatar: 'https://i.pravatar.cc/40?img=7' },
          { name: 'Helena', avatar: 'https://i.pravatar.cc/40?img=8' }
        ]
      }
    ])
    
    const sharedSpaces = ref([
      {
        id: 1,
        name: 'Residência Médica 2024',
        description: 'Material compartilhado para preparação',
        icon: 'fas fa-graduation-cap',
        documents: 45,
        members: 12,
        lastActivity: 'há 2 horas',
        type: 'shared'
      },
      {
        id: 2,
        name: 'Grupo de Estudos - Cardiologia',
        description: 'Notas e resumos de cardiologia avançada',
        icon: 'fas fa-heartbeat',
        documents: 28,
        members: 8,
        lastActivity: 'há 1 dia',
        type: 'owned'
      },
      {
        id: 3,
        name: 'Banco de Questões USMLE',
        description: 'Questões comentadas e discussões',
        icon: 'fas fa-question-circle',
        documents: 150,
        members: 35,
        lastActivity: 'há 3 horas',
        type: 'public'
      }
    ])
    
    const recentActivities = ref([
      {
        id: 1,
        user: 'Dr. Silva',
        action: 'compartilhou novo resumo de Farmacologia',
        time: 'há 15 minutos',
        type: 'share',
        icon: 'fas fa-share'
      },
      {
        id: 2,
        user: 'Maria Santos',
        action: 'comentou em "Caso Clínico #23"',
        time: 'há 1 hora',
        type: 'comment',
        icon: 'fas fa-comment'
      },
      {
        id: 3,
        user: 'João Pedro',
        action: 'criou novo mapa mental de Neuroanatomia',
        time: 'há 2 horas',
        type: 'create',
        icon: 'fas fa-plus'
      }
    ])
    
    // Computed
    const filteredSpaces = computed(() => {
      let spaces = sharedSpaces.value
      
      if (filterType.value !== 'all') {
        spaces = spaces.filter(space => space.type === filterType.value)
      }
      
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        spaces = spaces.filter(space => 
          space.name.toLowerCase().includes(query) ||
          space.description.toLowerCase().includes(query)
        )
      }
      
      return spaces
    })
    
    // Methods
    const joinSession = (session) => {
      console.log('Joining session:', session)
      // Implement session joining logic
    }
    
    const openSpace = (space) => {
      console.log('Opening space:', space)
      // Navigate to space details
    }
    
    const shareSpace = (space) => {
      console.log('Sharing space:', space)
      // Implement share logic
    }
    
    const editSpace = (space) => {
      console.log('Editing space:', space)
      // Implement edit logic
    }
    
    const deleteSpace = (space) => {
      if (confirm(`Tem certeza que deseja excluir "${space.name}"?`)) {
        console.log('Deleting space:', space)
        // Implement delete logic
      }
    }
    
    const createNewSpace = () => {
      console.log('Creating space:', newSpace.value)
      // Implement space creation
      showCreateSpace.value = false
      newSpace.value = {
        name: '',
        description: '',
        accessType: 'private',
        inviteEmails: ''
      }
    }
    
    // Network background animation
    const initBackground = () => {
      const canvas = backgroundCanvas.value
      if (!canvas) return
      
      const ctx = canvas.getContext('2d')
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      
      const nodes = []
      const numNodes = 50
      
      // Create nodes
      for (let i = 0; i < numNodes; i++) {
        nodes.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          radius: Math.random() * 3 + 1
        })
      }
      
      const animate = () => {
        ctx.fillStyle = 'rgba(10, 10, 20, 0.05)'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        
        // Update and draw nodes
        nodes.forEach(node => {
          node.x += node.vx
          node.y += node.vy
          
          if (node.x < 0 || node.x > canvas.width) node.vx *= -1
          if (node.y < 0 || node.y > canvas.height) node.vy *= -1
          
          ctx.beginPath()
          ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2)
          ctx.fillStyle = '#00d4ff'
          ctx.fill()
        })
        
        // Draw connections
        nodes.forEach((node, i) => {
          nodes.slice(i + 1).forEach(otherNode => {
            const distance = Math.sqrt(
              Math.pow(node.x - otherNode.x, 2) + 
              Math.pow(node.y - otherNode.y, 2)
            )
            
            if (distance < 150) {
              ctx.beginPath()
              ctx.moveTo(node.x, node.y)
              ctx.lineTo(otherNode.x, otherNode.y)
              ctx.strokeStyle = `rgba(0, 212, 255, ${1 - distance / 150})`
              ctx.lineWidth = 0.5
              ctx.stroke()
            }
          })
        })
        
        requestAnimationFrame(animate)
      }
      
      animate()
    }
    
    onMounted(() => {
      initBackground()
      window.addEventListener('resize', initBackground)
    })
    
    onUnmounted(() => {
      window.removeEventListener('resize', initBackground)
    })
    
    return {
      backgroundCanvas,
      showCreateSpace,
      searchQuery,
      filterType,
      newSpace,
      activeSessions,
      sharedSpaces,
      recentActivities,
      filteredSpaces,
      joinSession,
      openSpace,
      shareSpace,
      editSpace,
      deleteSpace,
      createNewSpace
    }
  }
}
</script>

<style scoped>
.notes-collaboration-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a14 0%, #1a1a2e 100%);
  color: #fff;
  position: relative;
  overflow-x: hidden;
}

.background-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.collaboration-header {
  position: relative;
  z-index: 10;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 2rem;
  background: rgba(20, 20, 40, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.back-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: #00d4ff;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: translateX(-5px);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #00d4ff, #0099cc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-subtitle {
  color: #888;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.create-space {
  background: linear-gradient(45deg, #00d4ff, #0099cc);
  color: #fff;
}

.create-space:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.collaboration-content {
  position: relative;
  z-index: 10;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Sections */
section {
  margin-bottom: 3rem;
}

.section-title {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #00d4ff;
}

/* Active Sessions */
.sessions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.session-card {
  background: rgba(20, 20, 40, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.session-card:hover {
  transform: translateY(-5px);
  border-color: rgba(0, 212, 255, 0.5);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.session-header h3 {
  font-size: 1.3rem;
  color: #fff;
}

.session-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #4ade80;
}

.session-status i {
  font-size: 0.5rem;
}

.session-preview p {
  color: #888;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.session-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 20px;
  font-size: 0.85rem;
  color: #00d4ff;
}

.session-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.participants {
  display: flex;
  align-items: center;
}

.participant-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid #1a1a2e;
  margin-left: -10px;
}

.participant-avatar:first-child {
  margin-left: 0;
}

.more-participants {
  margin-left: 0.5rem;
  font-size: 0.9rem;
  color: #888;
}

.join-btn {
  padding: 0.5rem 1rem;
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 6px;
  color: #00d4ff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.join-btn:hover {
  background: rgba(0, 212, 255, 0.3);
  transform: translateX(5px);
}

/* Shared Spaces */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.filter-controls {
  display: flex;
  gap: 1rem;
}

.search-input {
  padding: 0.75rem 1rem;
  background: rgba(20, 20, 40, 0.6);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  color: #fff;
  width: 250px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #00d4ff;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.filter-select {
  padding: 0.75rem 1rem;
  background: rgba(20, 20, 40, 0.6);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
}

.spaces-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.space-card {
  background: rgba(20, 20, 40, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.space-card:hover {
  transform: translateY(-5px);
  border-color: rgba(0, 212, 255, 0.5);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
}

.space-icon {
  width: 50px;
  height: 50px;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #00d4ff;
  flex-shrink: 0;
}

.space-info {
  flex: 1;
}

.space-info h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #fff;
}

.space-info p {
  color: #888;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.space-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #666;
}

.meta-item i {
  color: #00d4ff;
}

.space-actions {
  display: flex;
  gap: 0.5rem;
}

.action-icon {
  width: 35px;
  height: 35px;
  border-radius: 8px;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: #00d4ff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: scale(1.1);
}

.action-icon.danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.action-icon.danger:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* Collaboration Tools */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.tool-card {
  background: rgba(20, 20, 40, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.tool-card:hover {
  transform: translateY(-5px);
  border-color: rgba(0, 212, 255, 0.5);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
}

.tool-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: #00d4ff;
}

.tool-card h3 {
  font-size: 1.3rem;
  margin-bottom: 0.75rem;
  color: #fff;
}

.tool-card p {
  color: #888;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.tool-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 8px;
  color: #00d4ff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-btn:hover {
  background: rgba(0, 212, 255, 0.3);
  transform: scale(1.05);
}

/* Activity Feed */
.activity-timeline {
  max-width: 800px;
}

.activity-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(20, 20, 40, 0.4);
  border-radius: 12px;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(20, 20, 40, 0.6);
  transform: translateX(10px);
}

.activity-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon.share {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.activity-icon.comment {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.activity-icon.create {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.activity-content h4 {
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
  color: #fff;
}

.activity-content p {
  color: #888;
  margin-bottom: 0.5rem;
}

.activity-time {
  font-size: 0.85rem;
  color: #666;
}

/* Modal */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: #1a1a2e;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 16px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal h2 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: #00d4ff;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #888;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  background: rgba(20, 20, 40, 0.6);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  color: #fff;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #00d4ff;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.cancel-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(107, 114, 128, 0.2);
  border: 1px solid rgba(107, 114, 128, 0.4);
  border-radius: 8px;
  color: #9ca3af;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: rgba(107, 114, 128, 0.3);
}

.submit-btn {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(45deg, #00d4ff, #0099cc);
  border: none;
  border-radius: 8px;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: light) {
  .notes-collaboration-container {
    background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
    color: #333;
  }
  
  .collaboration-header {
    background: rgba(255, 255, 255, 0.9);
    border-bottom-color: rgba(0, 123, 255, 0.3);
  }
  
  .page-title {
    background: linear-gradient(45deg, #007bff, #0056b3);
  }
  
  .page-subtitle {
    color: #666;
  }
  
  .session-card,
  .space-card,
  .tool-card,
  .activity-item {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(0, 123, 255, 0.2);
  }
  
  .session-header h3,
  .space-info h3,
  .tool-card h3,
  .activity-content h4 {
    color: #333;
  }
  
  .modal {
    background: #fff;
    color: #333;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .collaboration-header {
    flex-direction: column;
    text-align: center;
  }
  
  .section-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .filter-controls {
    width: 100%;
    flex-direction: column;
  }
  
  .search-input {
    width: 100%;
  }
  
  .sessions-grid,
  .spaces-grid {
    grid-template-columns: 1fr;
  }
  
  .space-card {
    flex-direction: column;
  }
  
  .space-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>