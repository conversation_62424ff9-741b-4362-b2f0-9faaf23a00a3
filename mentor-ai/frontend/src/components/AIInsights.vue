<template>
  <div class="ai-insights-modal" v-if="show">
    <div class="modal-backdrop" @click="$emit('close')"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-chart-line"></i>
          IA Insights - Análise Inteligente
        </h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <!-- Performance Overview -->
        <section class="insights-section">
          <h3 class="section-title">
            <i class="fas fa-tachometer-alt"></i>
            Visão Geral de Performance
          </h3>
          
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-brain"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ insights.totalGenerations || 0 }}</div>
                <div class="stat-label">Gerações Totais</div>
              </div>
              <div class="stat-trend" :class="{ positive: insights.trends?.improving }">
                <i :class="insights.trends?.improving ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ insights.trends?.percentage || 0 }}%
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-question-circle"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ insights.totalQuestions || 0 }}</div>
                <div class="stat-label">Questões Criadas</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-star"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ Math.round(insights.averageQuality || 0) }}%</div>
                <div class="stat-label">Qualidade Média</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-clock"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatTime(insights.averageGenerationTime) }}</div>
                <div class="stat-label">Tempo Médio</div>
              </div>
            </div>
          </div>
        </section>
        
        <!-- AI Model Performance -->
        <section class="insights-section">
          <h3 class="section-title">
            <i class="fas fa-robot"></i>
            Performance dos Modelos de IA
          </h3>
          
          <div class="models-comparison">
            <div v-for="model in modelPerformance" :key="model.id" class="model-stat">
              <div class="model-header">
                <span class="model-name">{{ model.name }}</span>
                <span class="model-usage">{{ model.usage }}% de uso</span>
              </div>
              <div class="performance-bars">
                <div class="bar-item">
                  <label>Velocidade</label>
                  <div class="bar-container">
                    <div class="bar-fill speed" :style="{ width: model.speed + '%' }"></div>
                  </div>
                  <span class="bar-value">{{ model.speed }}%</span>
                </div>
                <div class="bar-item">
                  <label>Precisão</label>
                  <div class="bar-container">
                    <div class="bar-fill accuracy" :style="{ width: model.accuracy + '%' }"></div>
                  </div>
                  <span class="bar-value">{{ model.accuracy }}%</span>
                </div>
                <div class="bar-item">
                  <label>Satisfação</label>
                  <div class="bar-container">
                    <div class="bar-fill satisfaction" :style="{ width: model.satisfaction + '%' }"></div>
                  </div>
                  <span class="bar-value">{{ model.satisfaction }}%</span>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        <!-- Specialty Analytics -->
        <section class="insights-section">
          <h3 class="section-title">
            <i class="fas fa-stethoscope"></i>
            Análise por Especialidade
          </h3>
          
          <div class="specialty-chart">
            <div v-for="spec in specialtyAnalytics" :key="spec.id" class="specialty-item">
              <div class="specialty-info">
                <i :class="spec.icon"></i>
                <span class="specialty-name">{{ spec.name }}</span>
                <span class="specialty-count">{{ spec.count }} questões</span>
              </div>
              <div class="specialty-bar">
                <div class="bar-fill" :style="{ width: spec.percentage + '%', background: spec.color }"></div>
              </div>
            </div>
          </div>
        </section>
        
        <!-- Quality Trends -->
        <section class="insights-section">
          <h3 class="section-title">
            <i class="fas fa-chart-area"></i>
            Tendências de Qualidade
          </h3>
          
          <div class="quality-trends">
            <div class="trend-chart">
              <canvas ref="qualityChart"></canvas>
            </div>
            <div class="trend-insights">
              <div class="insight-item" :class="{ positive: insights.trends?.improving }">
                <i :class="insights.trends?.improving ? 'fas fa-trending-up' : 'fas fa-trending-down'"></i>
                <div>
                  <strong>{{ insights.trends?.message }}</strong>
                  <p>Melhoria de {{ insights.trends?.percentage }}% nas últimas gerações</p>
                </div>
              </div>
              
              <div class="insight-item">
                <i class="fas fa-lightbulb"></i>
                <div>
                  <strong>Modelo Favorito</strong>
                  <p>{{ insights.favoriteModel || 'GPT-4 Turbo' }} é o mais utilizado</p>
                </div>
              </div>
              
              <div class="insight-item">
                <i class="fas fa-graduation-cap"></i>
                <div>
                  <strong>Especialidade Principal</strong>
                  <p>{{ getSpecialtyName(insights.favoriteSpecialty) }} com maior volume</p>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        <!-- Recommendations -->
        <section class="insights-section">
          <h3 class="section-title">
            <i class="fas fa-magic"></i>
            Recomendações Personalizadas
          </h3>
          
          <div class="recommendations">
            <div v-for="(rec, index) in recommendations" :key="index" class="recommendation-card">
              <div class="rec-icon" :style="{ background: rec.color }">
                <i :class="rec.icon"></i>
              </div>
              <div class="rec-content">
                <h4>{{ rec.title }}</h4>
                <p>{{ rec.description }}</p>
                <button class="rec-action" @click="applyRecommendation(rec)">
                  {{ rec.action }}
                  <i class="fas fa-arrow-right"></i>
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>
      
      <div class="modal-footer">
        <button class="btn export-btn" @click="exportInsights">
          <i class="fas fa-download"></i>
          Exportar Relatório
        </button>
        <button class="btn primary-btn" @click="$emit('close')">
          Fechar
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import UltraQuestionService from '@/services/UltraQuestionService'

export default {
  name: 'AIInsights',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  setup(props) {
    const service = new UltraQuestionService()
    const insights = ref({})
    const modelPerformance = ref([])
    const specialtyAnalytics = ref([])
    const recommendations = ref([])
    const qualityChart = ref(null)
    
    const loadInsights = async () => {
      insights.value = service.getGenerationInsights() || {
        totalGenerations: 0,
        totalQuestions: 0,
        averageQuality: 0,
        trends: { improving: true, percentage: 0, message: 'Começando...' }
      }
      
      // Mock model performance data
      modelPerformance.value = [
        {
          id: 'gpt-4-turbo',
          name: 'GPT-4 Turbo',
          usage: 45,
          speed: 85,
          accuracy: 98,
          satisfaction: 92
        },
        {
          id: 'claude-3',
          name: 'Claude 3',
          usage: 30,
          speed: 90,
          accuracy: 96,
          satisfaction: 88
        },
        {
          id: 'gemini-pro',
          name: 'Gemini Pro',
          usage: 20,
          speed: 95,
          accuracy: 94,
          satisfaction: 85
        },
        {
          id: 'medical-llm',
          name: 'MedicalLLM',
          usage: 5,
          speed: 80,
          accuracy: 99,
          satisfaction: 95
        }
      ]
      
      // Mock specialty analytics
      specialtyAnalytics.value = [
        {
          id: 'cardio',
          name: 'Cardiologia',
          icon: 'fas fa-heartbeat',
          count: 245,
          percentage: 35,
          color: '#ef4444'
        },
        {
          id: 'neuro',
          name: 'Neurologia',
          icon: 'fas fa-brain',
          count: 180,
          percentage: 25,
          color: '#8b5cf6'
        },
        {
          id: 'emergency',
          name: 'Emergência',
          icon: 'fas fa-ambulance',
          count: 150,
          percentage: 20,
          color: '#3b82f6'
        },
        {
          id: 'pediatrics',
          name: 'Pediatria',
          icon: 'fas fa-baby',
          count: 120,
          percentage: 15,
          color: '#10b981'
        },
        {
          id: 'others',
          name: 'Outras',
          icon: 'fas fa-ellipsis-h',
          count: 50,
          percentage: 5,
          color: '#6b7280'
        }
      ]
      
      // Generate recommendations
      generateRecommendations()
    }
    
    const generateRecommendations = () => {
      const recs = []
      
      if (insights.value.averageQuality < 80) {
        recs.push({
          icon: 'fas fa-chart-line',
          title: 'Melhore a Qualidade',
          description: 'Suas questões podem ter qualidade ainda maior. Tente usar modelos mais avançados.',
          action: 'Ver Modelos Premium',
          color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
        })
      }
      
      if (insights.value.totalGenerations < 10) {
        recs.push({
          icon: 'fas fa-rocket',
          title: 'Explore Mais',
          description: 'Você está apenas começando! Experimente diferentes especialidades e tipos de questões.',
          action: 'Explorar Especialidades',
          color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        })
      }
      
      if (!insights.value.favoriteModel?.includes('medical')) {
        recs.push({
          icon: 'fas fa-stethoscope',
          title: 'Teste o MedicalLLM',
          description: 'Modelo especializado em medicina com 99% de precisão em conceitos médicos.',
          action: 'Experimentar Agora',
          color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
        })
      }
      
      recommendations.value = recs
    }
    
    const formatTime = (ms) => {
      if (!ms) return '0s'
      const seconds = Math.round(ms / 1000)
      return seconds < 60 ? `${seconds}s` : `${Math.round(seconds / 60)}m`
    }
    
    const getSpecialtyName = (id) => {
      const names = {
        cardio: 'Cardiologia',
        neuro: 'Neurologia',
        emergency: 'Emergência',
        pediatrics: 'Pediatria'
      }
      return names[id] || 'Geral'
    }
    
    const applyRecommendation = (rec) => {
      console.log('Applying recommendation:', rec)
      // Implement recommendation actions
    }
    
    const exportInsights = async () => {
      const reportData = {
        insights: insights.value,
        modelPerformance: modelPerformance.value,
        specialtyAnalytics: specialtyAnalytics.value,
        generatedAt: new Date().toISOString()
      }
      
      await service.exportQuestions(reportData, 'json')
    }
    
    const drawQualityChart = () => {
      // Mock chart drawing - in production would use Chart.js
      console.log('Drawing quality chart')
    }
    
    watch(() => props.show, (newVal) => {
      if (newVal) {
        loadInsights()
        setTimeout(() => {
          if (qualityChart.value) {
            drawQualityChart()
          }
        }, 300)
      }
    })
    
    onMounted(() => {
      if (props.show) {
        loadInsights()
      }
    })
    
    return {
      insights,
      modelPerformance,
      specialtyAnalytics,
      recommendations,
      qualityChart,
      formatTime,
      getSpecialtyName,
      applyRecommendation,
      exportInsights
    }
  }
}
</script>

<style scoped>
.ai-insights-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal-content {
  position: relative;
  background: #0f172a;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #f1f5f9;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-title i {
  color: #6366f1;
}

.close-btn {
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.2s;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #f1f5f9;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.insights-section {
  margin-bottom: 3rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title i {
  color: #6366f1;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.08);
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #f1f5f9;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #ef4444;
}

.stat-trend.positive {
  color: #10b981;
}

/* Model Performance */
.models-comparison {
  display: grid;
  gap: 1.5rem;
}

.model-stat {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 1.5rem;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.model-name {
  font-weight: 600;
  color: #f1f5f9;
}

.model-usage {
  font-size: 0.875rem;
  color: #94a3b8;
}

.performance-bars {
  display: grid;
  gap: 0.75rem;
}

.bar-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.bar-item label {
  width: 80px;
  font-size: 0.875rem;
  color: #94a3b8;
}

.bar-container {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  transition: width 0.5s ease;
}

.bar-fill.speed {
  background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
}

.bar-fill.accuracy {
  background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
}

.bar-fill.satisfaction {
  background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
}

.bar-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f1f5f9;
  width: 40px;
  text-align: right;
}

/* Specialty Analytics */
.specialty-chart {
  display: grid;
  gap: 1rem;
}

.specialty-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.specialty-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 200px;
}

.specialty-info i {
  width: 2rem;
  height: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6366f1;
}

.specialty-name {
  flex: 1;
  font-weight: 500;
  color: #f1f5f9;
}

.specialty-count {
  font-size: 0.875rem;
  color: #94a3b8;
}

.specialty-bar {
  flex: 1;
  height: 24px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
}

.specialty-bar .bar-fill {
  height: 100%;
  transition: width 0.5s ease;
}

/* Quality Trends */
.quality-trends {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.trend-chart {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 1.5rem;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trend-insights {
  display: grid;
  gap: 1.5rem;
}

.insight-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.insight-item i {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6366f1;
  flex-shrink: 0;
}

.insight-item.positive i {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.insight-item strong {
  display: block;
  color: #f1f5f9;
  margin-bottom: 0.25rem;
}

.insight-item p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0;
}

/* Recommendations */
.recommendations {
  display: grid;
  gap: 1.5rem;
}

.recommendation-card {
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  transition: all 0.3s;
}

.recommendation-card:hover {
  transform: translateX(5px);
  background: rgba(255, 255, 255, 0.08);
}

.rec-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.rec-content {
  flex: 1;
}

.rec-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 0.5rem 0;
}

.rec-content p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0 0 1rem 0;
}

.rec-action {
  background: none;
  border: none;
  color: #6366f1;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.rec-action:hover {
  gap: 0.75rem;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.export-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #f1f5f9;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.export-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.primary-btn {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-height: 95vh;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quality-trends {
    grid-template-columns: 1fr;
  }
  
  .modal-header {
    padding: 1.5rem;
  }
  
  .modal-body {
    padding: 1.5rem;
  }
}
</style>