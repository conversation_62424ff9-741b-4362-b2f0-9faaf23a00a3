<template>
  <div class="flashcards-ai-container">
    <!-- Header Section -->
    <div class="flashcards-header">
      <div class="header-content">
        <h1 class="title">
          <i class="fas fa-layer-group"></i>
          Flashcards AI
        </h1>
        <p class="subtitle">Crie flashcards inteligentes com IA a partir de qualquer conteúdo</p>
      </div>
      <div class="stats-bar">
        <div class="stat-item">
          <i class="fas fa-clone"></i>
          <span>{{ totalFlashcards }} cards</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-folder"></i>
          <span>{{ decks.length }} decks</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-chart-line"></i>
          <span>{{ masteryPercentage }}% domínio</span>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flashcards-content">
      <!-- Left Panel - Creation -->
      <div class="creation-panel">
        <div class="panel-header">
          <h2><i class="fas fa-magic"></i> Criar Flashcards</h2>
        </div>

        <!-- Input Methods -->
        <div class="input-methods">
          <div class="method-tabs">
            <button 
              :class="['tab', { active: activeTab === 'text' }]"
              @click="activeTab = 'text'"
            >
              <i class="fas fa-keyboard"></i> Texto
            </button>
            <button 
              :class="['tab', { active: activeTab === 'file' }]"
              @click="activeTab = 'file'"
            >
              <i class="fas fa-file-upload"></i> Arquivo
            </button>
            <button 
              :class="['tab', { active: activeTab === 'url' }]"
              @click="activeTab = 'url'"
            >
              <i class="fas fa-link"></i> URL
            </button>
          </div>

          <!-- Text Input -->
          <div v-if="activeTab === 'text'" class="input-content">
            <textarea
              v-model="textInput"
              placeholder="Cole ou digite seu conteúdo aqui..."
              class="text-input"
              rows="8"
            ></textarea>
          </div>

          <!-- File Upload -->
          <div v-if="activeTab === 'file'" class="input-content">
            <div 
              class="file-upload-zone"
              @drop="handleDrop"
              @dragover.prevent
              @dragenter.prevent
            >
              <i class="fas fa-cloud-upload-alt"></i>
              <p>Arraste arquivos aqui ou clique para selecionar</p>
              <input 
                type="file" 
                ref="fileInput"
                @change="handleFileSelect"
                multiple
                accept=".pdf,.doc,.docx,.txt,.md"
                style="display: none"
              >
              <button @click="$refs.fileInput.click()" class="select-btn">
                Selecionar Arquivos
              </button>
            </div>
            <div v-if="uploadedFiles.length > 0" class="uploaded-files">
              <div v-for="file in uploadedFiles" :key="file.name" class="file-item">
                <i class="fas fa-file"></i>
                <span>{{ file.name }}</span>
                <button @click="removeFile(file)" class="remove-btn">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- URL Input -->
          <div v-if="activeTab === 'url'" class="input-content">
            <input
              v-model="urlInput"
              type="url"
              placeholder="https://exemplo.com/artigo"
              class="url-input"
            >
          </div>
        </div>

        <!-- Generation Options -->
        <div class="generation-options">
          <h3><i class="fas fa-cog"></i> Opções de Geração</h3>
          <div class="option-grid">
            <div class="option-item">
              <label>Quantidade</label>
              <input 
                v-model.number="options.quantity"
                type="number"
                min="1"
                max="50"
                class="option-input"
              >
            </div>
            <div class="option-item">
              <label>Dificuldade</label>
              <select v-model="options.difficulty" class="option-select">
                <option value="easy">Fácil</option>
                <option value="medium">Médio</option>
                <option value="hard">Difícil</option>
              </select>
            </div>
            <div class="option-item">
              <label>Tipo</label>
              <select v-model="options.type" class="option-select">
                <option value="qa">Pergunta/Resposta</option>
                <option value="cloze">Lacunas</option>
                <option value="definition">Definição</option>
                <option value="mixed">Misto</option>
              </select>
            </div>
            <div class="option-item">
              <label>Deck</label>
              <select v-model="options.deck" class="option-select">
                <option value="">Novo Deck</option>
                <option v-for="deck in decks" :key="deck.id" :value="deck.id">
                  {{ deck.name }}
                </option>
              </select>
            </div>
          </div>
          <div v-if="!options.deck" class="new-deck-input">
            <input 
              v-model="newDeckName"
              placeholder="Nome do novo deck"
              class="deck-name-input"
            >
          </div>
        </div>

        <!-- Generate Button -->
        <button 
          @click="generateFlashcards"
          :disabled="!canGenerate || isGenerating"
          class="generate-btn"
        >
          <i :class="['fas', isGenerating ? 'fa-spinner fa-spin' : 'fa-magic']"></i>
          {{ isGenerating ? 'Gerando...' : 'Gerar Flashcards' }}
        </button>
      </div>

      <!-- Right Panel - Decks & Preview -->
      <div class="decks-panel">
        <!-- Decks List -->
        <div class="decks-section">
          <div class="section-header">
            <h2><i class="fas fa-folder-open"></i> Meus Decks</h2>
            <button class="study-all-btn" @click="studyAll">
              <i class="fas fa-graduation-cap"></i> Estudar Todos
            </button>
          </div>
          <div class="decks-list">
            <div 
              v-for="deck in decks" 
              :key="deck.id"
              :class="['deck-item', { active: selectedDeck?.id === deck.id }]"
              @click="selectDeck(deck)"
            >
              <div class="deck-info">
                <h3>{{ deck.name }}</h3>
                <div class="deck-stats">
                  <span><i class="fas fa-clone"></i> {{ deck.cards.length }}</span>
                  <span><i class="fas fa-check-circle"></i> {{ deck.mastered }}</span>
                  <span><i class="fas fa-clock"></i> {{ deck.pending }}</span>
                </div>
              </div>
              <div class="deck-actions">
                <button @click.stop="studyDeck(deck)" class="action-btn study">
                  <i class="fas fa-play"></i>
                </button>
                <button @click.stop="editDeck(deck)" class="action-btn edit">
                  <i class="fas fa-edit"></i>
                </button>
                <button @click.stop="deleteDeck(deck)" class="action-btn delete">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Preview Section -->
        <div v-if="previewCards.length > 0" class="preview-section">
          <div class="section-header">
            <h2><i class="fas fa-eye"></i> Preview</h2>
            <button @click="savePreview" class="save-preview-btn">
              <i class="fas fa-save"></i> Salvar
            </button>
          </div>
          <div class="preview-cards">
            <div 
              v-for="(card, index) in previewCards" 
              :key="index"
              class="preview-card"
            >
              <div class="card-number">{{ index + 1 }}</div>
              <div class="card-content">
                <div class="card-front">
                  <label>Frente</label>
                  <textarea v-model="card.front" rows="2"></textarea>
                </div>
                <div class="card-back">
                  <label>Verso</label>
                  <textarea v-model="card.back" rows="2"></textarea>
                </div>
              </div>
              <button @click="removePreviewCard(index)" class="remove-card-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Study Modal -->
    <div v-if="isStudying" class="study-modal" @click.self="closeStudy">
      <div class="study-container">
        <div class="study-header">
          <h2>{{ currentStudyDeck?.name }}</h2>
          <div class="study-progress">
            <div class="progress-bar">
              <div 
                class="progress-fill"
                :style="{ width: studyProgress + '%' }"
              ></div>
            </div>
            <span>{{ currentCardIndex + 1 }} / {{ studyCards.length }}</span>
          </div>
          <button @click="closeStudy" class="close-study-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="study-card" :class="{ flipped: isFlipped }">
          <div class="card-face card-front-face" @click="flipCard">
            <div class="card-content-wrapper">
              <p>{{ currentCard?.front }}</p>
            </div>
            <div class="flip-hint">
              <i class="fas fa-sync-alt"></i> Clique para virar
            </div>
          </div>
          <div class="card-face card-back-face" @click="flipCard">
            <div class="card-content-wrapper">
              <p>{{ currentCard?.back }}</p>
            </div>
          </div>
        </div>

        <div v-if="isFlipped" class="study-actions">
          <button @click="rateCard(1)" class="rate-btn hard">
            <i class="fas fa-times"></i> Difícil
          </button>
          <button @click="rateCard(3)" class="rate-btn medium">
            <i class="fas fa-minus"></i> Médio
          </button>
          <button @click="rateCard(5)" class="rate-btn easy">
            <i class="fas fa-check"></i> Fácil
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import flashcardsService from '@/services/flashcardsService'

export default {
  name: 'FlashcardsAI',
  setup() {
    // State
    const activeTab = ref('text')
    const textInput = ref('')
    const urlInput = ref('')
    const uploadedFiles = ref([])
    const isGenerating = ref(false)
    const options = ref({
      quantity: 10,
      difficulty: 'medium',
      type: 'qa',
      deck: ''
    })
    const newDeckName = ref('')
    const decks = ref([])
    const selectedDeck = ref(null)
    const previewCards = ref([])
    const isStudying = ref(false)
    const currentStudyDeck = ref(null)
    const studyCards = ref([])
    const currentCardIndex = ref(0)
    const isFlipped = ref(false)

    // Computed
    const totalFlashcards = computed(() => {
      return decks.value.reduce((total, deck) => total + deck.cards.length, 0)
    })

    const masteryPercentage = computed(() => {
      if (totalFlashcards.value === 0) return 0
      const mastered = decks.value.reduce((total, deck) => total + deck.mastered, 0)
      return Math.round((mastered / totalFlashcards.value) * 100)
    })

    const canGenerate = computed(() => {
      return textInput.value.trim() || urlInput.value.trim() || uploadedFiles.value.length > 0
    })

    const currentCard = computed(() => {
      return studyCards.value[currentCardIndex.value]
    })

    const studyProgress = computed(() => {
      if (studyCards.value.length === 0) return 0
      return ((currentCardIndex.value + 1) / studyCards.value.length) * 100
    })

    // Methods
    const handleDrop = (e) => {
      e.preventDefault()
      const files = Array.from(e.dataTransfer.files)
      uploadedFiles.value.push(...files)
    }

    const handleFileSelect = (e) => {
      const files = Array.from(e.target.files)
      uploadedFiles.value.push(...files)
    }

    const removeFile = (file) => {
      const index = uploadedFiles.value.indexOf(file)
      if (index > -1) {
        uploadedFiles.value.splice(index, 1)
      }
    }

    const generateFlashcards = async () => {
      isGenerating.value = true
      try {
        let content = ''
        
        if (activeTab.value === 'text') {
          content = textInput.value
        } else if (activeTab.value === 'url') {
          content = await flashcardsService.fetchUrlContent(urlInput.value)
        } else if (activeTab.value === 'file') {
          content = await flashcardsService.processFiles(uploadedFiles.value)
        }

        const generated = await flashcardsService.generateFlashcards(content, options.value)
        previewCards.value = generated
      } catch (error) {
        console.error('Erro ao gerar flashcards:', error)
      } finally {
        isGenerating.value = false
      }
    }

    const savePreview = () => {
      const deckName = newDeckName.value || `Deck ${decks.value.length + 1}`
      const newDeck = {
        id: Date.now(),
        name: deckName,
        cards: previewCards.value.map((card, index) => ({
          ...card,
          id: Date.now() + index,
          mastery: 0,
          lastReview: null,
          nextReview: new Date()
        })),
        mastered: 0,
        pending: previewCards.value.length,
        createdAt: new Date()
      }
      
      decks.value.push(newDeck)
      previewCards.value = []
      newDeckName.value = ''
      
      // Save to localStorage
      localStorage.setItem('flashcards_decks', JSON.stringify(decks.value))
    }

    const selectDeck = (deck) => {
      selectedDeck.value = deck
    }

    const studyDeck = (deck) => {
      currentStudyDeck.value = deck
      studyCards.value = [...deck.cards].sort((a, b) => {
        return new Date(a.nextReview) - new Date(b.nextReview)
      })
      currentCardIndex.value = 0
      isFlipped.value = false
      isStudying.value = true
    }

    const studyAll = () => {
      const allCards = decks.value.flatMap(deck => 
        deck.cards.map(card => ({ ...card, deckName: deck.name }))
      )
      studyCards.value = allCards.sort((a, b) => {
        return new Date(a.nextReview) - new Date(b.nextReview)
      })
      currentStudyDeck.value = { name: 'Todos os Decks' }
      currentCardIndex.value = 0
      isFlipped.value = false
      isStudying.value = true
    }

    const flipCard = () => {
      isFlipped.value = !isFlipped.value
    }

    const rateCard = (rating) => {
      const card = currentCard.value
      
      // Update card mastery and next review based on rating
      if (rating === 5) {
        card.mastery = Math.min(card.mastery + 1, 5)
        card.nextReview = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      } else if (rating === 3) {
        card.nextReview = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days
      } else {
        card.mastery = Math.max(card.mastery - 1, 0)
        card.nextReview = new Date(Date.now() + 24 * 60 * 60 * 1000) // 1 day
      }
      
      card.lastReview = new Date()
      
      // Move to next card
      if (currentCardIndex.value < studyCards.value.length - 1) {
        currentCardIndex.value++
        isFlipped.value = false
      } else {
        closeStudy()
      }
      
      // Update deck stats
      updateDeckStats()
    }

    const updateDeckStats = () => {
      decks.value.forEach(deck => {
        deck.mastered = deck.cards.filter(card => card.mastery >= 4).length
        deck.pending = deck.cards.filter(card => 
          new Date(card.nextReview) <= new Date()
        ).length
      })
      
      // Save to localStorage
      localStorage.setItem('flashcards_decks', JSON.stringify(decks.value))
    }

    const closeStudy = () => {
      isStudying.value = false
      currentStudyDeck.value = null
      studyCards.value = []
      currentCardIndex.value = 0
      isFlipped.value = false
    }

    const editDeck = (deck) => {
      // TODO: Implement deck editing
      console.log('Edit deck:', deck)
    }

    const deleteDeck = (deck) => {
      const index = decks.value.indexOf(deck)
      if (index > -1 && confirm(`Deletar o deck "${deck.name}"?`)) {
        decks.value.splice(index, 1)
        localStorage.setItem('flashcards_decks', JSON.stringify(decks.value))
      }
    }

    const removePreviewCard = (index) => {
      previewCards.value.splice(index, 1)
    }

    // Load decks on mount
    onMounted(() => {
      const savedDecks = localStorage.getItem('flashcards_decks')
      if (savedDecks) {
        decks.value = JSON.parse(savedDecks)
      }
    })

    return {
      // State
      activeTab,
      textInput,
      urlInput,
      uploadedFiles,
      isGenerating,
      options,
      newDeckName,
      decks,
      selectedDeck,
      previewCards,
      isStudying,
      currentStudyDeck,
      studyCards,
      currentCardIndex,
      isFlipped,
      
      // Computed
      totalFlashcards,
      masteryPercentage,
      canGenerate,
      currentCard,
      studyProgress,
      
      // Methods
      handleDrop,
      handleFileSelect,
      removeFile,
      generateFlashcards,
      savePreview,
      selectDeck,
      studyDeck,
      studyAll,
      flipCard,
      rateCard,
      closeStudy,
      editDeck,
      deleteDeck,
      removePreviewCard
    }
  }
}
</script>

<style scoped>
.flashcards-ai-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0c29 0%, #302b63 50%, #24243e 100%);
  padding: 20px;
  color: #fff;
}

/* Header */
.flashcards-header {
  margin-bottom: 30px;
}

.header-content {
  text-align: center;
  margin-bottom: 20px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  background: linear-gradient(45deg, #00d4ff, #7b2ff7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.title i {
  margin-right: 10px;
}

.subtitle {
  font-size: 1.1rem;
  color: #a0a0a0;
}

.stats-bar {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.1rem;
}

.stat-item i {
  color: #00d4ff;
}

/* Main Content */
.flashcards-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Creation Panel */
.creation-panel {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: #00d4ff;
}

.panel-header i {
  margin-right: 10px;
}

/* Input Methods */
.method-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tab {
  flex: 1;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.tab:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tab.active {
  background: linear-gradient(45deg, #00d4ff, #7b2ff7);
  border: none;
}

.tab i {
  margin-right: 8px;
}

/* Input Content */
.input-content {
  margin-bottom: 30px;
}

.text-input {
  width: 100%;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 1rem;
  resize: vertical;
}

.text-input::placeholder {
  color: #666;
}

/* File Upload */
.file-upload-zone {
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload-zone:hover {
  border-color: #00d4ff;
  background: rgba(0, 212, 255, 0.05);
}

.file-upload-zone i {
  font-size: 3rem;
  color: #00d4ff;
  margin-bottom: 15px;
}

.select-btn {
  margin-top: 15px;
  padding: 10px 25px;
  background: linear-gradient(45deg, #00d4ff, #7b2ff7);
  border: none;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  font-size: 1rem;
}

.uploaded-files {
  margin-top: 15px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  margin-bottom: 8px;
}

.file-item i {
  color: #00d4ff;
}

.remove-btn {
  margin-left: auto;
  background: none;
  border: none;
  color: #ff4444;
  cursor: pointer;
  padding: 5px;
}

/* URL Input */
.url-input {
  width: 100%;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 1rem;
}

/* Generation Options */
.generation-options {
  margin-bottom: 30px;
}

.generation-options h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: #00d4ff;
}

.generation-options i {
  margin-right: 8px;
}

.option-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.option-item label {
  display: block;
  margin-bottom: 5px;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.option-input,
.option-select {
  width: 100%;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #fff;
  font-size: 1rem;
}

.option-select option {
  background: #1a1a2e;
}

.deck-name-input {
  width: 100%;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #fff;
  font-size: 1rem;
}

/* Generate Button */
.generate-btn {
  width: 100%;
  padding: 15px;
  background: linear-gradient(45deg, #00d4ff, #7b2ff7);
  border: none;
  border-radius: 10px;
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.generate-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.generate-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.generate-btn i {
  margin-right: 8px;
}

/* Decks Panel */
.decks-panel {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 1.5rem;
  color: #00d4ff;
}

.section-header i {
  margin-right: 10px;
}

.study-all-btn {
  padding: 10px 20px;
  background: linear-gradient(45deg, #00d4ff, #7b2ff7);
  border: none;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.study-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 212, 255, 0.3);
}

/* Decks List */
.decks-list {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 30px;
}

.deck-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.deck-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
}

.deck-item.active {
  background: rgba(0, 212, 255, 0.1);
  border-color: #00d4ff;
}

.deck-info h3 {
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.deck-stats {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.deck-stats i {
  margin-right: 5px;
}

.deck-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 35px;
  height: 35px;
  border: none;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.study {
  background: #00d4ff;
}

.action-btn.edit {
  background: #ffa500;
}

.action-btn.delete {
  background: #ff4444;
}

.action-btn:hover {
  transform: scale(1.1);
}

/* Preview Section */
.preview-section {
  margin-top: 30px;
}

.save-preview-btn {
  padding: 8px 20px;
  background: #4caf50;
  border: none;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  font-size: 0.9rem;
}

.preview-cards {
  max-height: 400px;
  overflow-y: auto;
}

.preview-card {
  position: relative;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  margin-bottom: 10px;
}

.card-number {
  position: absolute;
  top: 10px;
  right: 40px;
  background: #00d4ff;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 600;
}

.card-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.card-front,
.card-back {
  display: flex;
  flex-direction: column;
}

.card-front label,
.card-back label {
  font-size: 0.9rem;
  color: #a0a0a0;
  margin-bottom: 5px;
}

.card-front textarea,
.card-back textarea {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  color: #fff;
  padding: 8px;
  resize: vertical;
}

.remove-card-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #ff4444;
  border: none;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
}

/* Study Modal */
.study-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.study-container {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 20px;
  padding: 30px;
  max-width: 800px;
  width: 100%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.study-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.study-header h2 {
  font-size: 1.5rem;
  color: #00d4ff;
}

.study-progress {
  flex: 1;
  margin: 0 30px;
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(45deg, #00d4ff, #7b2ff7);
  transition: width 0.3s ease;
}

.study-progress span {
  font-size: 0.9rem;
  color: #a0a0a0;
}

.close-study-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-study-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Study Card */
.study-card {
  position: relative;
  height: 400px;
  margin-bottom: 30px;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  cursor: pointer;
}

.study-card.flipped {
  transform: rotateY(180deg);
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.card-back-face {
  transform: rotateY(180deg);
  background: rgba(123, 47, 247, 0.1);
}

.card-content-wrapper {
  text-align: center;
  font-size: 1.3rem;
  line-height: 1.8;
}

.flip-hint {
  position: absolute;
  bottom: 20px;
  font-size: 0.9rem;
  color: #666;
}

/* Study Actions */
.study-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.rate-btn {
  padding: 15px 30px;
  border: none;
  border-radius: 10px;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rate-btn.hard {
  background: #ff4444;
}

.rate-btn.medium {
  background: #ffa500;
}

.rate-btn.easy {
  background: #4caf50;
}

.rate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.rate-btn i {
  margin-right: 8px;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive */
@media (max-width: 1024px) {
  .flashcards-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }
  
  .stats-bar {
    flex-direction: column;
    gap: 15px;
  }
  
  .option-grid {
    grid-template-columns: 1fr;
  }
  
  .study-container {
    padding: 20px;
  }
  
  .study-card {
    height: 300px;
  }
  
  .card-content-wrapper {
    font-size: 1.1rem;
  }
}
</style>