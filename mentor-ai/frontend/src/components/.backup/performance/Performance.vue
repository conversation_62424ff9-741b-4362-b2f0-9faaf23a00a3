<template>
  <div class="performance-page">
    <div class="page-header">
      <h1>
        <font-awesome-icon icon="fa-chart-line" />
        Desempenho
      </h1>
      <p>An<PERSON>lise detalhada do seu progresso e performance</p>
    </div>
    
    <div class="coming-soon">
      <font-awesome-icon icon="fa-chart-pie" class="icon-large" />
      <h2>Em Breve!</h2>
      <p>Dashboard de performance em desenvolvimento.</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Performance'
}
</script>

<style scoped>
.performance-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.coming-soon {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--card-bg);
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
}

.icon-large {
  font-size: 4rem;
  color: var(--accent-color);
  margin-bottom: 1rem;
}
</style>