<template>
  <div class="performance-ultra">
    <!-- Animated Background -->
    <div class="animated-background">
      <div class="gradient-sphere sphere-1"></div>
      <div class="gradient-sphere sphere-2"></div>
      <div class="gradient-sphere sphere-3"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Ultra Modern Header -->
    <header class="ultra-header">
      <div class="header-container">
        <div class="header-content">
          <div class="title-section">
            <div class="title-animation">
              <h1 class="main-title">
                <span class="title-word" v-for="(word, index) in titleWords" :key="index" :style="{ animationDelay: index * 0.1 + 's' }">
                  {{ word }}
                </span>
              </h1>
              <div class="title-underline"></div>
            </div>
            <p class="subtitle">
              <span class="subtitle-part">Inteligência de dados</span>
              <span class="subtitle-divider">•</span>
              <span class="subtitle-part accent">Análise preditiva</span>
            </p>
          </div>

          <div class="header-stats">
            <div class="stat-bubble" v-for="stat in headerStats" :key="stat.id">
              <div class="bubble-icon">
                <i :class="stat.icon"></i>
              </div>
              <div class="bubble-content">
                <span class="bubble-value">{{ stat.value }}</span>
                <span class="bubble-label">{{ stat.label }}</span>
              </div>
              <div class="bubble-glow"></div>
            </div>
          </div>
        </div>

        <!-- Time Navigation -->
        <div class="time-navigation">
          <button 
            v-for="period in timePeriods" 
            :key="period.id"
            @click="selectPeriod(period)"
            :class="['time-btn', { active: selectedPeriod.id === period.id }]"
          >
            <div class="btn-background"></div>
            <i :class="period.icon"></i>
            <span>{{ period.label }}</span>
            <div class="btn-indicator"></div>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Dashboard -->
    <main class="dashboard-main">
      <!-- Performance Score Section -->
      <section class="score-section">
        <div class="score-container">
          <div class="score-card">
            <div class="score-header">
              <h2 class="section-title">Performance Score</h2>
              <div class="score-badge">
                <span>{{ getPerformanceGrade() }}</span>
              </div>
            </div>

            <div class="score-visual">
              <div class="score-circle">
                <svg class="score-svg" viewBox="0 0 250 250">
                  <defs>
                    <linearGradient id="scoreGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
                    </linearGradient>
                  </defs>
                  <circle cx="125" cy="125" r="110" fill="none" stroke="#1a1a1a" stroke-width="20"/>
                  <circle 
                    cx="125" 
                    cy="125" 
                    r="110" 
                    fill="none" 
                    stroke="url(#scoreGradient)" 
                    stroke-width="20"
                    :stroke-dasharray="`${scoreProgress} ${circumference}`"
                    stroke-linecap="round"
                    transform="rotate(-90 125 125)"
                  />
                </svg>
                <div class="score-center">
                  <div class="score-number">{{ performanceScore }}</div>
                  <div class="score-subtitle">de 100 pontos</div>
                  <div class="score-trend" :class="scoreTrend.type">
                    <i :class="scoreTrend.icon"></i>
                    <span>{{ scoreTrend.value }}%</span>
                  </div>
                </div>
              </div>

              <div class="score-factors">
                <div 
                  v-for="factor in performanceFactors" 
                  :key="factor.id"
                  class="factor-item"
                  @click="showFactorDetails(factor)"
                >
                  <div class="factor-icon" :style="{ background: factor.gradient }">
                    <i :class="factor.icon"></i>
                  </div>
                  <div class="factor-info">
                    <h4>{{ factor.name }}</h4>
                    <div class="factor-bar">
                      <div class="factor-progress" :style="{ width: factor.value + '%', background: factor.gradient }"></div>
                    </div>
                    <span class="factor-value">{{ factor.value }}%</span>
                  </div>
                  <div class="factor-arrow">
                    <i class="fas fa-chevron-right"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- AI Insights -->
          <div class="insights-card">
            <div class="insights-header">
              <div class="insights-title">
                <i class="fas fa-brain"></i>
                <h3>AI Insights</h3>
                <span class="insights-badge">Novo</span>
              </div>
              <button class="insights-refresh" @click="refreshInsights">
                <i class="fas fa-sync-alt" :class="{ spinning: refreshing }"></i>
              </button>
            </div>

            <div class="insights-content">
              <div 
                v-for="insight in aiInsights" 
                :key="insight.id"
                class="insight-item"
                :class="insight.type"
              >
                <div class="insight-icon">
                  <i :class="insight.icon"></i>
                </div>
                <div class="insight-text">
                  <h4>{{ insight.title }}</h4>
                  <p>{{ insight.message }}</p>
                </div>
                <button class="insight-action" v-if="insight.action" @click="executeAction(insight.action)">
                  {{ insight.actionText }}
                  <i class="fas fa-arrow-right"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Analytics Grid -->
      <section class="analytics-section">
        <div class="analytics-grid">
          <!-- Study Patterns -->
          <div class="analytics-card patterns-card">
            <div class="card-header">
              <h3>Padrões de Estudo</h3>
              <div class="card-actions">
                <button class="action-btn" @click="exportData('patterns')">
                  <i class="fas fa-download"></i>
                </button>
              </div>
            </div>

            <div class="patterns-visualization">
              <div class="pattern-graph">
                <canvas ref="patternsChart"></canvas>
              </div>
              <div class="pattern-stats">
                <div class="pattern-stat" v-for="stat in patternStats" :key="stat.id">
                  <div class="stat-icon" :style="{ color: stat.color }">
                    <i :class="stat.icon"></i>
                  </div>
                  <div class="stat-info">
                    <span class="stat-label">{{ stat.label }}</span>
                    <span class="stat-value">{{ stat.value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Progress Timeline -->
          <div class="analytics-card timeline-card">
            <div class="card-header">
              <h3>Timeline de Progresso</h3>
              <div class="timeline-controls">
                <button 
                  v-for="view in timelineViews" 
                  :key="view.id"
                  @click="setTimelineView(view.id)"
                  :class="['view-btn', { active: selectedTimelineView === view.id }]"
                >
                  {{ view.label }}
                </button>
              </div>
            </div>

            <div class="timeline-container">
              <div class="timeline-graph">
                <canvas ref="timelineChart"></canvas>
              </div>
              <div class="timeline-legend">
                <div v-for="metric in timelineMetrics" :key="metric.id" class="legend-item">
                  <div class="legend-color" :style="{ background: metric.color }"></div>
                  <span>{{ metric.name }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Heatmap Calendar -->
          <div class="analytics-card heatmap-card">
            <div class="card-header">
              <h3>Calendário de Atividades</h3>
              <div class="heatmap-info">
                <span class="info-text">{{ totalStudyDays }} dias ativos</span>
              </div>
            </div>

            <div class="heatmap-container">
              <div class="heatmap-months">
                <span v-for="month in calendarMonths" :key="month">{{ month }}</span>
              </div>
              <div class="heatmap-grid">
                <div class="weekday-labels">
                  <span v-for="day in weekdays" :key="day">{{ day }}</span>
                </div>
                <div class="days-grid">
                  <div 
                    v-for="(day, index) in calendarDays" 
                    :key="index"
                    class="day-cell"
                    :style="{ backgroundColor: getHeatmapColor(day.intensity) }"
                    :title="`${day.date}: ${day.hours}h de estudo`"
                    @click="showDayDetails(day)"
                  >
                    <div class="day-tooltip" v-if="hoveredDay === index">
                      <strong>{{ formatDate(day.date) }}</strong>
                      <span>{{ day.hours }}h estudadas</span>
                      <span>{{ day.cards }} cards revisados</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="heatmap-scale">
                <span>Menos</span>
                <div class="scale-colors">
                  <div v-for="level in 5" :key="level" class="scale-block" :style="{ backgroundColor: getHeatmapColor(level * 20) }"></div>
                </div>
                <span>Mais</span>
              </div>
            </div>
          </div>

          <!-- Subject Performance -->
          <div class="analytics-card subjects-card">
            <div class="card-header">
              <h3>Desempenho por Matéria</h3>
              <select v-model="selectedSubjectView" class="view-select">
                <option value="radar">Radar</option>
                <option value="bars">Barras</option>
                <option value="tree">Árvore</option>
              </select>
            </div>

            <div class="subjects-visualization">
              <div class="subjects-chart" v-show="selectedSubjectView === 'radar'">
                <canvas ref="radarChart"></canvas>
              </div>
              <div class="subjects-bars" v-show="selectedSubjectView === 'bars'">
                <div v-for="subject in subjects" :key="subject.id" class="subject-bar">
                  <div class="subject-header">
                    <div class="subject-info">
                      <i :class="subject.icon" :style="{ color: subject.color }"></i>
                      <span class="subject-name">{{ subject.name }}</span>
                    </div>
                    <span class="subject-score">{{ subject.score }}%</span>
                  </div>
                  <div class="subject-progress">
                    <div class="progress-bg">
                      <div 
                        class="progress-fill" 
                        :style="{ width: subject.score + '%', background: subject.color }"
                      >
                        <span class="progress-label">{{ subject.cards }} cards</span>
                      </div>
                    </div>
                  </div>
                  <div class="subject-stats">
                    <span><i class="fas fa-check"></i> {{ subject.mastered }}</span>
                    <span><i class="fas fa-clock"></i> {{ subject.learning }}</span>
                    <span><i class="fas fa-exclamation"></i> {{ subject.difficult }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Achievements & Goals -->
      <section class="achievements-section">
        <div class="achievements-container">
          <div class="achievements-header">
            <h2 class="section-title">Conquistas & Metas</h2>
            <div class="achievements-nav">
              <button 
                v-for="tab in achievementTabs" 
                :key="tab.id"
                @click="selectedAchievementTab = tab.id"
                :class="['tab-btn', { active: selectedAchievementTab === tab.id }]"
              >
                <i :class="tab.icon"></i>
                <span>{{ tab.label }}</span>
              </button>
            </div>
          </div>

          <div class="achievements-content">
            <!-- Recent Achievements -->
            <div v-show="selectedAchievementTab === 'achievements'" class="achievements-grid">
              <div 
                v-for="achievement in recentAchievements" 
                :key="achievement.id"
                class="achievement-card"
                :class="{ unlocked: achievement.unlocked }"
              >
                <div class="achievement-glow"></div>
                <div class="achievement-icon">
                  <i :class="achievement.icon"></i>
                </div>
                <h4>{{ achievement.title }}</h4>
                <p>{{ achievement.description }}</p>
                <div class="achievement-date" v-if="achievement.unlocked">
                  {{ formatDate(achievement.unlockedAt) }}
                </div>
                <div class="achievement-progress" v-else>
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: achievement.progress + '%' }"></div>
                  </div>
                  <span>{{ achievement.progress }}%</span>
                </div>
              </div>
            </div>

            <!-- Goals -->
            <div v-show="selectedAchievementTab === 'goals'" class="goals-list">
              <div v-for="goal in studyGoals" :key="goal.id" class="goal-item">
                <div class="goal-checkbox" @click="toggleGoal(goal.id)">
                  <i v-if="goal.completed" class="fas fa-check"></i>
                </div>
                <div class="goal-content">
                  <h4 :class="{ completed: goal.completed }">{{ goal.title }}</h4>
                  <p>{{ goal.description }}</p>
                  <div class="goal-meta">
                    <span class="goal-deadline">
                      <i class="fas fa-calendar"></i>
                      {{ formatDate(goal.deadline) }}
                    </span>
                    <span class="goal-priority" :class="goal.priority">
                      {{ goal.priority }}
                    </span>
                  </div>
                </div>
                <div class="goal-actions">
                  <button @click="editGoal(goal)" class="action-btn">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button @click="deleteGoal(goal.id)" class="action-btn danger">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
              <button class="add-goal-btn" @click="showAddGoal = true">
                <i class="fas fa-plus"></i>
                <span>Adicionar Nova Meta</span>
              </button>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Floating Action Button -->
    <div class="fab-container">
      <button class="fab" @click="toggleQuickActions">
        <i class="fas fa-bolt"></i>
      </button>
      <transition name="fab-menu">
        <div v-if="showQuickActions" class="fab-menu">
          <button class="fab-item" @click="exportReport" title="Exportar Relatório">
            <i class="fas fa-file-export"></i>
          </button>
          <button class="fab-item" @click="shareProgress" title="Compartilhar Progresso">
            <i class="fas fa-share-alt"></i>
          </button>
          <button class="fab-item" @click="printDashboard" title="Imprimir Dashboard">
            <i class="fas fa-print"></i>
          </button>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Chart from 'chart.js/auto'

export default {
  name: 'PerformanceDashboard',
  setup() {
    // Refs
    const patternsChart = ref(null)
    const timelineChart = ref(null)
    const radarChart = ref(null)
    
    // State
    const titleWords = ref(['Performance', 'Analytics'])
    const selectedPeriod = ref({ id: 'month', label: 'Mês' })
    const performanceScore = ref(87)
    const scoreTrend = ref({ type: 'up', icon: 'fas fa-arrow-up', value: 12 })
    const selectedTimelineView = ref('week')
    const selectedSubjectView = ref('radar')
    const selectedAchievementTab = ref('achievements')
    const showQuickActions = ref(false)
    const refreshing = ref(false)
    const hoveredDay = ref(null)
    const showAddGoal = ref(false)
    
    // Data
    const headerStats = ref([
      { id: 1, icon: 'fas fa-fire', value: '15', label: 'Dias seguidos' },
      { id: 2, icon: 'fas fa-clock', value: '42h', label: 'Este mês' },
      { id: 3, icon: 'fas fa-trophy', value: '8', label: 'Conquistas' }
    ])
    
    const timePeriods = ref([
      { id: 'day', label: 'Hoje', icon: 'fas fa-sun' },
      { id: 'week', label: 'Semana', icon: 'fas fa-calendar-week' },
      { id: 'month', label: 'Mês', icon: 'fas fa-calendar-alt' },
      { id: 'year', label: 'Ano', icon: 'fas fa-calendar' }
    ])
    
    const performanceFactors = ref([
      { 
        id: 1, 
        name: 'Consistência', 
        value: 92, 
        icon: 'fas fa-sync', 
        gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
      },
      { 
        id: 2, 
        name: 'Velocidade', 
        value: 78, 
        icon: 'fas fa-tachometer-alt', 
        gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' 
      },
      { 
        id: 3, 
        name: 'Precisão', 
        value: 85, 
        icon: 'fas fa-bullseye', 
        gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' 
      },
      { 
        id: 4, 
        name: 'Retenção', 
        value: 89, 
        icon: 'fas fa-brain', 
        gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' 
      }
    ])
    
    const aiInsights = ref([
      {
        id: 1,
        type: 'success',
        icon: 'fas fa-check-circle',
        title: 'Excelente Consistência!',
        message: 'Você manteve uma rotina de estudos por 15 dias consecutivos. Continue assim!',
        action: null
      },
      {
        id: 2,
        type: 'warning',
        icon: 'fas fa-exclamation-triangle',
        title: 'Atenção em Farmacologia',
        message: 'Sua taxa de acerto em cards de Farmacologia caiu 15% esta semana.',
        action: 'reviewPharmacology',
        actionText: 'Revisar agora'
      },
      {
        id: 3,
        type: 'info',
        icon: 'fas fa-lightbulb',
        title: 'Melhor horário detectado',
        message: 'Você tem melhor desempenho entre 19h e 21h. Que tal focar seus estudos neste período?',
        action: 'scheduleStudy',
        actionText: 'Agendar'
      }
    ])
    
    const patternStats = ref([
      { id: 1, label: 'Melhor dia', value: 'Quarta', icon: 'fas fa-calendar-day', color: '#667eea' },
      { id: 2, label: 'Horário pico', value: '20h', icon: 'fas fa-clock', color: '#a855f7' },
      { id: 3, label: 'Sessão média', value: '45 min', icon: 'fas fa-hourglass-half', color: '#ec4899' }
    ])
    
    const timelineViews = ref([
      { id: 'week', label: 'Semana' },
      { id: 'month', label: 'Mês' },
      { id: 'quarter', label: 'Trimestre' }
    ])
    
    const timelineMetrics = ref([
      { id: 1, name: 'Cards estudados', color: '#667eea' },
      { id: 2, name: 'Taxa de acerto', color: '#10b981' },
      { id: 3, name: 'Tempo de estudo', color: '#f59e0b' }
    ])
    
    const subjects = ref([
      { 
        id: 1, 
        name: 'Anatomia', 
        score: 92, 
        icon: 'fas fa-user', 
        color: '#667eea',
        cards: 245,
        mastered: 180,
        learning: 45,
        difficult: 20
      },
      { 
        id: 2, 
        name: 'Fisiologia', 
        score: 85, 
        icon: 'fas fa-heartbeat', 
        color: '#ec4899',
        cards: 189,
        mastered: 140,
        learning: 35,
        difficult: 14
      },
      { 
        id: 3, 
        name: 'Farmacologia', 
        score: 78, 
        icon: 'fas fa-pills', 
        color: '#f59e0b',
        cards: 156,
        mastered: 100,
        learning: 40,
        difficult: 16
      },
      { 
        id: 4, 
        name: 'Patologia', 
        score: 88, 
        icon: 'fas fa-virus', 
        color: '#10b981',
        cards: 210,
        mastered: 165,
        learning: 30,
        difficult: 15
      }
    ])
    
    const recentAchievements = ref([
      {
        id: 1,
        title: 'Maratonista',
        description: 'Estude por 5 horas em um único dia',
        icon: 'fas fa-running',
        unlocked: true,
        unlockedAt: new Date('2024-01-15')
      },
      {
        id: 2,
        title: 'Mestre da Consistência',
        description: 'Mantenha uma sequência de 30 dias',
        icon: 'fas fa-fire',
        unlocked: false,
        progress: 50
      },
      {
        id: 3,
        title: 'Perfeccionista',
        description: '100% de acerto em 50 cards seguidos',
        icon: 'fas fa-star',
        unlocked: true,
        unlockedAt: new Date('2024-01-10')
      }
    ])
    
    const studyGoals = ref([
      {
        id: 1,
        title: 'Completar módulo de Cardiologia',
        description: 'Revisar todos os 300 cards de cardiologia',
        deadline: new Date('2024-02-01'),
        priority: 'high',
        completed: false
      },
      {
        id: 2,
        title: 'Melhorar média em Farmacologia',
        description: 'Aumentar taxa de acerto para 85%',
        deadline: new Date('2024-01-31'),
        priority: 'medium',
        completed: false
      }
    ])
    
    const achievementTabs = ref([
      { id: 'achievements', label: 'Conquistas', icon: 'fas fa-trophy' },
      { id: 'goals', label: 'Metas', icon: 'fas fa-bullseye' }
    ])
    
    const weekdays = ref(['D', 'S', 'T', 'Q', 'Q', 'S', 'S'])
    const calendarMonths = ref(['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'])
    const totalStudyDays = ref(127)
    
    // Generate calendar days (simulated data)
    const calendarDays = ref(
      Array.from({ length: 182 }, (_, i) => ({
        date: new Date(2024, 0, i + 1),
        hours: Math.random() * 5,
        cards: Math.floor(Math.random() * 100),
        intensity: Math.floor(Math.random() * 100)
      }))
    )
    
    // Computed
    const circumference = computed(() => 2 * Math.PI * 110)
    const scoreProgress = computed(() => (performanceScore.value / 100) * circumference.value)
    
    // Methods
    const selectPeriod = (period) => {
      selectedPeriod.value = period
      updateCharts()
    }
    
    const getPerformanceGrade = () => {
      const score = performanceScore.value
      if (score >= 90) return 'A+'
      if (score >= 80) return 'A'
      if (score >= 70) return 'B'
      if (score >= 60) return 'C'
      return 'D'
    }
    
    const showFactorDetails = (factor) => {
      console.log('Show details for:', factor.name)
    }
    
    const refreshInsights = async () => {
      refreshing.value = true
      await new Promise(resolve => setTimeout(resolve, 2000))
      refreshing.value = false
    }
    
    const executeAction = (action) => {
      console.log('Execute action:', action)
    }
    
    const setTimelineView = (view) => {
      selectedTimelineView.value = view
      updateTimelineChart()
    }
    
    const getHeatmapColor = (intensity) => {
      if (intensity === 0) return '#0a0a0a'
      if (intensity < 20) return '#1a1a2e'
      if (intensity < 40) return '#2d2d5f'
      if (intensity < 60) return '#4040a0'
      if (intensity < 80) return '#5353d1'
      return '#667eea'
    }
    
    const showDayDetails = (day) => {
      console.log('Day details:', day)
    }
    
    const formatDate = (date) => {
      if (!date) return ''
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      }).format(new Date(date))
    }
    
    const toggleGoal = (goalId) => {
      const goal = studyGoals.value.find(g => g.id === goalId)
      if (goal) goal.completed = !goal.completed
    }
    
    const editGoal = (goal) => {
      console.log('Edit goal:', goal)
    }
    
    const deleteGoal = (goalId) => {
      studyGoals.value = studyGoals.value.filter(g => g.id !== goalId)
    }
    
    const toggleQuickActions = () => {
      showQuickActions.value = !showQuickActions.value
    }
    
    const exportReport = () => {
      console.log('Export report')
    }
    
    const shareProgress = () => {
      console.log('Share progress')
    }
    
    const printDashboard = () => {
      window.print()
    }
    
    const updateCharts = () => {
      updatePatternsChart()
      updateTimelineChart()
      updateRadarChart()
    }
    
    const updatePatternsChart = () => {
      if (!patternsChart.value) return
      
      // Update patterns chart based on selected period
    }
    
    const updateTimelineChart = () => {
      if (!timelineChart.value) return
      
      // Update timeline chart based on selected view
    }
    
    const updateRadarChart = () => {
      if (!radarChart.value) return
      
      // Update radar chart with subject data
    }
    
    const initCharts = () => {
      // Initialize Pattern Chart
      const patternsCtx = patternsChart.value?.getContext('2d')
      if (patternsCtx) {
        new Chart(patternsCtx, {
          type: 'line',
          data: {
            labels: ['00h', '04h', '08h', '12h', '16h', '20h', '24h'],
            datasets: [{
              label: 'Atividade',
              data: [10, 5, 15, 45, 30, 85, 20],
              borderColor: '#667eea',
              backgroundColor: 'rgba(102, 126, 234, 0.1)',
              tension: 0.4,
              fill: true
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: { display: false }
            },
            scales: {
              y: { 
                beginAtZero: true,
                grid: { color: 'rgba(255, 255, 255, 0.05)' },
                ticks: { color: '#666' }
              },
              x: {
                grid: { color: 'rgba(255, 255, 255, 0.05)' },
                ticks: { color: '#666' }
              }
            }
          }
        })
      }
      
      // Initialize Timeline Chart
      const timelineCtx = timelineChart.value?.getContext('2d')
      if (timelineCtx) {
        new Chart(timelineCtx, {
          type: 'line',
          data: {
            labels: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'],
            datasets: [
              {
                label: 'Cards',
                data: [65, 78, 90, 81, 56, 55, 40],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                yAxisID: 'y'
              },
              {
                label: 'Acerto %',
                data: [85, 88, 92, 87, 83, 89, 85],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                yAxisID: 'y1'
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
              mode: 'index',
              intersect: false
            },
            plugins: {
              legend: { display: false }
            },
            scales: {
              y: {
                type: 'linear',
                display: true,
                position: 'left',
                grid: { color: 'rgba(255, 255, 255, 0.05)' },
                ticks: { color: '#666' }
              },
              y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: { drawOnChartArea: false },
                ticks: { color: '#666' }
              },
              x: {
                grid: { color: 'rgba(255, 255, 255, 0.05)' },
                ticks: { color: '#666' }
              }
            }
          }
        })
      }
      
      // Initialize Radar Chart
      const radarCtx = radarChart.value?.getContext('2d')
      if (radarCtx) {
        new Chart(radarCtx, {
          type: 'radar',
          data: {
            labels: subjects.value.map(s => s.name),
            datasets: [{
              label: 'Desempenho',
              data: subjects.value.map(s => s.score),
              borderColor: '#667eea',
              backgroundColor: 'rgba(102, 126, 234, 0.2)',
              pointBackgroundColor: '#667eea',
              pointBorderColor: '#fff',
              pointHoverBackgroundColor: '#fff',
              pointHoverBorderColor: '#667eea'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: { display: false }
            },
            scales: {
              r: {
                angleLines: { color: 'rgba(255, 255, 255, 0.1)' },
                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                pointLabels: { color: '#aaa' },
                ticks: { 
                  color: '#666',
                  backdropColor: 'transparent'
                }
              }
            }
          }
        })
      }
    }
    
    // Lifecycle
    onMounted(() => {
      setTimeout(initCharts, 100)
    })
    
    onUnmounted(() => {
      // Cleanup charts if needed
    })
    
    return {
      // Refs
      patternsChart,
      timelineChart,
      radarChart,
      
      // State
      titleWords,
      selectedPeriod,
      performanceScore,
      scoreTrend,
      selectedTimelineView,
      selectedSubjectView,
      selectedAchievementTab,
      showQuickActions,
      refreshing,
      hoveredDay,
      showAddGoal,
      
      // Data
      headerStats,
      timePeriods,
      performanceFactors,
      aiInsights,
      patternStats,
      timelineViews,
      timelineMetrics,
      subjects,
      recentAchievements,
      studyGoals,
      achievementTabs,
      weekdays,
      calendarMonths,
      totalStudyDays,
      calendarDays,
      
      // Computed
      circumference,
      scoreProgress,
      
      // Methods
      selectPeriod,
      getPerformanceGrade,
      showFactorDetails,
      refreshInsights,
      executeAction,
      setTimelineView,
      getHeatmapColor,
      showDayDetails,
      formatDate,
      toggleGoal,
      editGoal,
      deleteGoal,
      toggleQuickActions,
      exportReport,
      shareProgress,
      printDashboard
    }
  }
}
</script>

<style scoped>
/* Base Styles */
.performance-ultra {
  min-height: calc(100vh - 64px);
  background: #0a0a0a;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Inter', sans-serif;
  position: relative;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
}

/* Animated Background */
.animated-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.gradient-sphere {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
}

.sphere-1 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, #667eea 0%, transparent 70%);
  top: -200px;
  left: -200px;
  animation: float 25s ease-in-out infinite;
}

.sphere-2 {
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, #a855f7 0%, transparent 70%);
  bottom: -150px;
  right: -150px;
  animation: float 30s ease-in-out infinite reverse;
}

.sphere-3 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #ec4899 0%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Ultra Header */
.ultra-header {
  position: relative;
  z-index: 10;
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.header-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.title-section {
  flex: 1;
}

.title-animation {
  position: relative;
}

.main-title {
  font-size: 3rem;
  font-weight: 900;
  margin: 0;
  display: flex;
  gap: 1rem;
}

.title-word {
  display: inline-block;
  animation: titleSlideUp 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) both;
  background: linear-gradient(135deg, #ffffff 0%, #888888 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes titleSlideUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.title-underline {
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #a855f7);
  border-radius: 2px;
  animation: underlineGrow 0.8s ease-out 0.5s both;
}

@keyframes underlineGrow {
  0% {
    width: 0;
  }
  100% {
    width: 100px;
  }
}

.subtitle {
  font-size: 1.125rem;
  color: #888;
  margin: 0.5rem 0 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.subtitle-part {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.8s forwards;
}

.subtitle-part.accent {
  color: #667eea;
  font-weight: 600;
}

.subtitle-divider {
  color: #444;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Header Stats */
.header-stats {
  display: flex;
  gap: 2rem;
}

.stat-bubble {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 2rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-bubble:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.bubble-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 50%;
  color: #667eea;
  font-size: 1.25rem;
}

.bubble-content {
  display: flex;
  flex-direction: column;
}

.bubble-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
}

.bubble-label {
  font-size: 0.875rem;
  color: #888;
}

.bubble-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
  border-radius: 2rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-bubble:hover .bubble-glow {
  opacity: 1;
}

/* Time Navigation */
.time-navigation {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  padding: 0.5rem;
  border-radius: 1rem;
}

.time-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: none;
  border-radius: 0.75rem;
  color: #888;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.time-btn:hover {
  color: #fff;
}

.time-btn.active {
  color: #fff;
}

.btn-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.time-btn.active .btn-background {
  opacity: 0.1;
}

.btn-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 80%;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #a855f7);
  border-radius: 3px;
  transition: transform 0.3s ease;
}

.time-btn.active .btn-indicator {
  transform: translateX(-50%) scaleX(1);
}

/* Dashboard Main */
.dashboard-main {
  position: relative;
  z-index: 1;
  padding: 2rem 0;
  max-width: 1400px;
  margin: 0 auto;
}

/* Score Section */
.score-section {
  margin-bottom: 4rem;
}

.score-container {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  padding: 0 2rem;
}

.score-card {
  background: rgba(26, 26, 26, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1.5rem;
  padding: 2rem;
}

.score-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.score-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 1rem;
  font-size: 1.5rem;
  font-weight: 900;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.score-visual {
  display: flex;
  gap: 3rem;
  align-items: center;
}

.score-circle {
  position: relative;
  width: 250px;
  height: 250px;
}

.score-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.score-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.score-number {
  font-size: 3.5rem;
  font-weight: 900;
  line-height: 1;
  background: linear-gradient(135deg, #667eea 0%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.score-subtitle {
  font-size: 0.875rem;
  color: #888;
  margin: 0.5rem 0;
}

.score-trend {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(16, 185, 129, 0.2);
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #10b981;
}

.score-trend.down {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* Score Factors */
.score-factors {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.factor-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.factor-item:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(5px);
}

.factor-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
  color: white;
  font-size: 1.25rem;
}

.factor-info {
  flex: 1;
}

.factor-info h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.factor-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.factor-progress {
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
}

.factor-value {
  font-size: 0.875rem;
  color: #888;
}

.factor-arrow {
  color: #444;
  transition: transform 0.3s ease;
}

.factor-item:hover .factor-arrow {
  transform: translateX(5px);
  color: #667eea;
}

/* AI Insights Card */
.insights-card {
  background: rgba(26, 26, 26, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1.5rem;
  padding: 2rem;
}

.insights-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.insights-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.insights-title i {
  font-size: 1.5rem;
  color: #667eea;
}

.insights-title h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
}

.insights-badge {
  padding: 0.25rem 0.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.insights-refresh {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #888;
  cursor: pointer;
  transition: all 0.3s ease;
}

.insights-refresh:hover {
  background: rgba(255, 255, 255, 0.08);
  color: white;
}

.insights-refresh i.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.insights-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.insight-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.insight-item.success {
  border-color: rgba(16, 185, 129, 0.3);
  background: rgba(16, 185, 129, 0.05);
}

.insight-item.warning {
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(245, 158, 11, 0.05);
}

.insight-item.info {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.insight-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  font-size: 1.25rem;
}

.insight-item.success .insight-icon {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.insight-item.warning .insight-icon {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.insight-item.info .insight-icon {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.insight-text {
  flex: 1;
}

.insight-text h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.insight-text p {
  margin: 0;
  font-size: 0.875rem;
  color: #aaa;
}

.insight-action {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 0.5rem;
  color: #a5b4fc;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.insight-action:hover {
  background: rgba(102, 126, 234, 0.3);
  color: white;
  transform: translateX(2px);
}

/* Analytics Section */
.analytics-section {
  padding: 0 2rem;
  margin-bottom: 4rem;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.analytics-card {
  background: rgba(26, 26, 26, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1.5rem;
  padding: 2rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.card-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  color: #888;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  color: white;
}

/* Patterns Visualization */
.patterns-visualization {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.pattern-graph {
  height: 200px;
}

.pattern-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.pattern-stat {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 0.75rem;
}

.stat-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  font-size: 1.25rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 0.75rem;
  color: #888;
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 600;
}

/* Timeline */
.timeline-controls {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  color: #888;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  color: white;
}

.view-btn.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
  color: #a5b4fc;
}

.timeline-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.timeline-graph {
  height: 250px;
}

.timeline-legend {
  display: flex;
  gap: 2rem;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #888;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Heatmap Calendar */
.heatmap-info {
  font-size: 0.875rem;
  color: #888;
}

.heatmap-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.heatmap-months {
  display: flex;
  gap: 2rem;
  padding-left: 2rem;
  font-size: 0.875rem;
  color: #888;
}

.heatmap-grid {
  display: flex;
  gap: 0.5rem;
}

.weekday-labels {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 0.75rem;
  color: #666;
  padding-right: 0.5rem;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(26, 1fr);
  grid-template-rows: repeat(7, 1fr);
  gap: 2px;
  grid-auto-flow: column;
}

.day-cell {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.day-cell:hover {
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.day-tooltip {
  position: absolute;
  bottom: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
  background: #1a1a1a;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  white-space: nowrap;
  font-size: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  z-index: 10;
  pointer-events: none;
}

.heatmap-scale {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #666;
  margin-top: 1rem;
}

.scale-colors {
  display: flex;
  gap: 2px;
}

.scale-block {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Subject Performance */
.view-select {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
}

.subjects-visualization {
  min-height: 300px;
}

.subjects-chart {
  height: 300px;
}

.subjects-bars {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.subject-bar {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.subject-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.subject-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.subject-info i {
  font-size: 1.25rem;
}

.subject-name {
  font-weight: 600;
}

.subject-score {
  font-size: 1.25rem;
  font-weight: 700;
}

.subject-progress {
  position: relative;
}

.progress-bg {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  position: relative;
  transition: width 0.5s ease;
}

.progress-label {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.625rem;
  color: white;
  font-weight: 600;
}

.subject-stats {
  display: flex;
  gap: 1.5rem;
  font-size: 0.875rem;
  color: #888;
}

.subject-stats span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Achievements Section */
.achievements-section {
  padding: 0 2rem;
  margin-bottom: 4rem;
}

.achievements-container {
  background: rgba(26, 26, 26, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1.5rem;
  padding: 2rem;
}

.achievements-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.achievements-nav {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  padding: 0.25rem;
  border-radius: 0.75rem;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  color: #888;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  color: white;
}

.tab-btn.active {
  background: rgba(102, 126, 234, 0.2);
  color: #a5b4fc;
}

/* Achievements Grid */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
}

.achievement-card {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 2rem 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  text-align: center;
  transition: all 0.3s ease;
  overflow: hidden;
}

.achievement-card:hover {
  transform: translateY(-5px);
  border-color: rgba(102, 126, 234, 0.3);
}

.achievement-card.unlocked {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
}

.achievement-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150%;
  height: 150%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.achievement-card.unlocked .achievement-glow {
  opacity: 1;
}

.achievement-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  font-size: 1.5rem;
  color: #888;
}

.achievement-card.unlocked .achievement-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.achievement-card h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.achievement-card p {
  margin: 0;
  font-size: 0.875rem;
  color: #888;
}

.achievement-date {
  font-size: 0.75rem;
  color: #666;
}

.achievement-progress {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.achievement-progress .progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.achievement-progress .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #a855f7);
  border-radius: 2px;
}

.achievement-progress span {
  font-size: 0.75rem;
  color: #888;
}

/* Goals List */
.goals-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.goal-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  transition: all 0.3s ease;
}

.goal-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.goal-checkbox {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.goal-checkbox:hover {
  border-color: #667eea;
}

.goal-checkbox i {
  color: #10b981;
  font-size: 0.875rem;
}

.goal-content {
  flex: 1;
}

.goal-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.goal-content h4.completed {
  color: #666;
  text-decoration: line-through;
}

.goal-content p {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  color: #888;
}

.goal-meta {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.goal-deadline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #888;
}

.goal-priority {
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.goal-priority.high {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.goal-priority.medium {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.goal-priority.low {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.goal-actions {
  display: flex;
  gap: 0.5rem;
}

.goal-actions .action-btn {
  background: transparent;
  border: none;
}

.goal-actions .action-btn.danger:hover {
  color: #ef4444;
}

.add-goal-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  padding: 1.5rem;
  background: rgba(102, 126, 234, 0.1);
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 1rem;
  color: #a5b4fc;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-goal-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  color: white;
}

/* Floating Action Button */
.fab-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 100;
}

.fab {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
}

.fab-menu {
  position: absolute;
  bottom: calc(100% + 1rem);
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.fab-item {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1a1a1a;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  font-size: 1.125rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fab-item:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
  transform: scale(1.1);
}

/* FAB Menu Animation */
.fab-menu-enter-active,
.fab-menu-leave-active {
  transition: all 0.3s ease;
}

.fab-menu-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fab-menu-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* Responsive */
@media (max-width: 1200px) {
  .score-container {
    grid-template-columns: 1fr;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 2rem;
  }
  
  .header-stats {
    width: 100%;
    justify-content: space-between;
  }
  
  .main-title {
    font-size: 2rem;
  }
  
  .score-visual {
    flex-direction: column;
  }
  
  .achievements-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .time-navigation {
    overflow-x: auto;
  }
}
</style>