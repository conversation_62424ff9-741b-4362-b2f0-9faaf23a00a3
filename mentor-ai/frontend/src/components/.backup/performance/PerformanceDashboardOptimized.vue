<template>
  <div class="performance-dashboard">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-bg"></div>
      <div class="grid-pattern"></div>
    </div>

    <!-- Header Section -->
    <header class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="dashboard-title">
            <span class="icon-wrapper">
              <font-awesome-icon icon="chart-line" />
            </span>
            Central de Performance
          </h1>
          <p class="dashboard-subtitle">Análise completa do seu progresso</p>
        </div>
        
        <div class="header-right">
          <div class="quick-stats-bar">
            <div class="quick-stat">
              <div class="stat-icon success">
                <font-awesome-icon icon="trophy" />
              </div>
              <div class="stat-info">
                <span class="stat-value">{{ overallScore }}%</span>
                <span class="stat-label">Score Geral</span>
              </div>
            </div>
            
            <div class="quick-stat">
              <div class="stat-icon warning">
                <font-awesome-icon icon="fire" />
              </div>
              <div class="stat-info">
                <span class="stat-value">{{ streakDays }}</span>
                <span class="stat-label">Dias Seguidos</span>
              </div>
            </div>
          </div>
          
          <div class="header-actions">
            <button class="action-button" @click="showExportModal = true">
              <font-awesome-icon icon="download" />
              <span>Exportar</span>
            </button>
            <button class="action-button primary" @click="showGoalsModal = true">
              <font-awesome-icon icon="bullseye" />
              <span>Metas</span>
            </button>
            <button class="action-button" @click="showInsights = true">
              <font-awesome-icon icon="lightbulb" />
              <span>Insights</span>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="dashboard-nav">
      <div class="nav-wrapper">
        <button
          v-for="tab in navigationTabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="['nav-tab', { active: activeTab === tab.id }]"
        >
          <font-awesome-icon :icon="tab.icon" />
          <span>{{ tab.label }}</span>
        </button>
      </div>
    </nav>

    <!-- Main Content Area -->
    <main class="dashboard-content">
      <!-- Time Period Selector -->
      <div class="period-selector-card">
        <h3 class="card-title">
          <font-awesome-icon icon="calendar" />
          Período de Análise
        </h3>
        
        <div class="period-controls">
          <div class="period-buttons">
            <button
              v-for="period in timePeriods"
              :key="period.value"
              @click="selectedPeriod = period.value"
              :class="['period-btn', { active: selectedPeriod === period.value }]"
            >
              {{ period.label }}
            </button>
          </div>
          
          <div class="date-inputs">
            <div class="date-field">
              <label>De</label>
              <input type="date" v-model="startDate" />
            </div>
            <div class="date-separator">
              <font-awesome-icon icon="arrow-right" />
            </div>
            <div class="date-field">
              <label>Até</label>
              <input type="date" v-model="endDate" />
            </div>
          </div>
        </div>
      </div>

      <!-- Overview Tab -->
      <div v-if="activeTab === 'overview'" class="tab-content overview-tab">
        <div class="content-grid">
          <!-- Main Performance Card -->
          <div class="performance-card main-card">
            <div class="card-header">
              <h3>Performance Geral</h3>
              <span class="performance-level">{{ getPerformanceLevel() }}</span>
            </div>
            
            <div class="performance-visual">
              <div class="circular-progress">
                <svg viewBox="0 0 200 200">
                  <circle cx="100" cy="100" r="90" class="progress-bg" />
                  <circle 
                    cx="100" 
                    cy="100" 
                    r="90" 
                    class="progress-fill"
                    :stroke-dasharray="`${scoreProgress} ${scoreCircumference}`"
                  />
                </svg>
                <div class="progress-content">
                  <span class="score-value">{{ overallScore }}</span>
                  <span class="score-unit">%</span>
                </div>
              </div>
              
              <div class="performance-details">
                <div class="detail-item" v-for="detail in performanceDetails" :key="detail.id">
                  <div class="detail-header">
                    <font-awesome-icon :icon="detail.icon" :style="{ color: detail.color }" />
                    <span>{{ detail.label }}</span>
                    <span class="detail-value">{{ detail.value }}</span>
                  </div>
                  <div class="detail-progress">
                    <div class="progress-bar">
                      <div 
                        class="progress-fill" 
                        :style="{ width: detail.percentage + '%', backgroundColor: detail.color }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- AI Insights Card -->
          <div class="insights-card">
            <div class="card-header">
              <h3>
                <font-awesome-icon icon="brain" />
                Insights IA
              </h3>
              <button class="refresh-btn" @click="refreshInsights">
                <font-awesome-icon icon="sync" :class="{ 'fa-spin': isRefreshing }" />
              </button>
            </div>
            
            <div class="insights-list">
              <div 
                v-for="insight in aiInsights.slice(0, 3)" 
                :key="insight.id" 
                :class="['insight-item', insight.type]"
              >
                <div class="insight-icon">
                  <font-awesome-icon :icon="insight.icon" />
                </div>
                <div class="insight-content">
                  <h4>{{ insight.title }}</h4>
                  <p>{{ insight.message }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Metrics Grid -->
          <div class="metrics-grid">
            <div v-for="metric in keyMetrics" :key="metric.id" class="metric-card">
              <div class="metric-header">
                <div class="metric-icon" :style="{ backgroundColor: metric.color + '20', color: metric.color }">
                  <font-awesome-icon :icon="metric.icon" />
                </div>
                <div class="metric-trend" :class="metric.trend">
                  <font-awesome-icon :icon="metric.trend === 'up' ? 'arrow-up' : 'arrow-down'" />
                  {{ Math.abs(metric.change) }}%
                </div>
              </div>
              
              <div class="metric-body">
                <h4>{{ metric.label }}</h4>
                <div class="metric-value">{{ metric.value }}</div>
                <p class="metric-description">{{ metric.description }}</p>
              </div>
              
              <div class="metric-sparkline">
                <svg viewBox="0 0 100 30">
                  <polyline
                    :points="metric.sparklineData"
                    fill="none"
                    :stroke="metric.color"
                    stroke-width="2"
                  />
                </svg>
              </div>
            </div>
          </div>

          <!-- Recent Activity Timeline -->
          <div class="timeline-card">
            <div class="card-header">
              <h3>Atividade Recente</h3>
              <select v-model="timelineFilter" class="filter-select">
                <option value="all">Todas</option>
                <option value="study">Estudos</option>
                <option value="exam">Provas</option>
                <option value="achievement">Conquistas</option>
              </select>
            </div>
            
            <div class="timeline">
              <div v-for="activity in recentActivities" :key="activity.id" class="timeline-item">
                <div class="timeline-marker" :class="activity.type">
                  <font-awesome-icon :icon="activity.icon" />
                </div>
                <div class="timeline-content">
                  <div class="timeline-header">
                    <h4>{{ activity.title }}</h4>
                    <span class="timeline-time">{{ formatTime(activity.date) }}</span>
                  </div>
                  <p>{{ activity.description }}</p>
                  <div class="timeline-meta" v-if="activity.metadata">
                    <span v-for="(value, key) in activity.metadata" :key="key" class="meta-tag">
                      {{ key }}: {{ value }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Analytics Tab -->
      <div v-if="activeTab === 'analytics'" class="tab-content analytics-tab">
        <div class="analytics-grid">
          <!-- Chart Controls -->
          <div class="chart-controls">
            <div class="control-group">
              <label>Tipo de Gráfico</label>
              <select v-model="chartType" class="control-select">
                <option value="line">Linha</option>
                <option value="bar">Barras</option>
                <option value="area">Área</option>
              </select>
            </div>
            
            <div class="control-group">
              <label>Métrica</label>
              <select v-model="selectedMetric" class="control-select">
                <option value="score">Score</option>
                <option value="hours">Horas Estudadas</option>
                <option value="exercises">Exercícios</option>
              </select>
            </div>
          </div>

          <!-- Main Chart -->
          <div class="chart-card">
            <canvas ref="mainChart"></canvas>
          </div>

          <!-- Statistics Cards -->
          <div class="stats-grid">
            <div v-for="stat in analyticsStats" :key="stat.id" class="stat-card">
              <h4>{{ stat.label }}</h4>
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-comparison">
                <span :class="['change', stat.change > 0 ? 'positive' : 'negative']">
                  {{ stat.change > 0 ? '+' : '' }}{{ stat.change }}%
                </span>
                <span class="comparison-label">vs período anterior</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Subjects Tab -->
      <div v-if="activeTab === 'subjects'" class="tab-content subjects-tab">
        <div class="subjects-grid">
          <div v-for="subject in subjects" :key="subject.id" class="subject-card">
            <div class="subject-header">
              <div class="subject-icon" :style="{ backgroundColor: subject.color + '20', color: subject.color }">
                <font-awesome-icon :icon="subject.icon" />
              </div>
              <h3>{{ subject.name }}</h3>
            </div>
            
            <div class="subject-stats">
              <div class="stat-row">
                <span>Score Médio</span>
                <span class="stat-value">{{ subject.avgScore }}%</span>
              </div>
              <div class="stat-row">
                <span>Horas Estudadas</span>
                <span class="stat-value">{{ subject.hours }}h</span>
              </div>
              <div class="stat-row">
                <span>Exercícios</span>
                <span class="stat-value">{{ subject.exercises }}</span>
              </div>
            </div>
            
            <div class="subject-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: subject.progress + '%', backgroundColor: subject.color }"
                ></div>
              </div>
              <span class="progress-label">{{ subject.progress }}% concluído</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Goals Tab -->
      <div v-if="activeTab === 'goals'" class="tab-content goals-tab">
        <div class="goals-header">
          <h3>Suas Metas</h3>
          <button class="add-goal-btn" @click="showAddGoalModal = true">
            <font-awesome-icon icon="plus" />
            Nova Meta
          </button>
        </div>
        
        <div class="goals-grid">
          <div v-for="goal in goals" :key="goal.id" class="goal-card" :class="goal.status">
            <div class="goal-header">
              <h4>{{ goal.title }}</h4>
              <span class="goal-deadline">{{ formatDate(goal.deadline) }}</span>
            </div>
            
            <p class="goal-description">{{ goal.description }}</p>
            
            <div class="goal-progress">
              <div class="progress-info">
                <span>Progresso</span>
                <span>{{ goal.progress }}%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: goal.progress + '%' }"></div>
              </div>
            </div>
            
            <div class="goal-actions">
              <button class="goal-action-btn" @click="editGoal(goal)">
                <font-awesome-icon icon="edit" />
              </button>
              <button class="goal-action-btn" @click="deleteGoal(goal)">
                <font-awesome-icon icon="trash" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity Tab -->
      <div v-if="activeTab === 'activity'" class="tab-content activity-tab">
        <div class="activity-calendar">
          <h3>Calendário de Atividades</h3>
          <div class="calendar-grid">
            <!-- Calendar implementation here -->
          </div>
        </div>
        
        <div class="activity-summary">
          <h3>Resumo de Atividades</h3>
          <div class="summary-stats">
            <div class="summary-stat">
              <span class="stat-label">Total de Horas</span>
              <span class="stat-value">{{ totalHours }}h</span>
            </div>
            <div class="summary-stat">
              <span class="stat-label">Dias Ativos</span>
              <span class="stat-value">{{ activeDays }}</span>
            </div>
            <div class="summary-stat">
              <span class="stat-label">Média Diária</span>
              <span class="stat-value">{{ avgDaily }}h</span>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Modals -->
    <transition name="modal">
      <div v-if="showExportModal" class="modal-overlay" @click="showExportModal = false">
        <div class="modal-content" @click.stop>
          <h3>Exportar Dados</h3>
          <div class="export-options">
            <button class="export-btn" @click="exportData('pdf')">
              <font-awesome-icon icon="file-pdf" />
              Exportar PDF
            </button>
            <button class="export-btn" @click="exportData('excel')">
              <font-awesome-icon icon="file-excel" />
              Exportar Excel
            </button>
            <button class="export-btn" @click="exportData('csv')">
              <font-awesome-icon icon="file-csv" />
              Exportar CSV
            </button>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import Chart from 'chart.js/auto'

export default {
  name: 'PerformanceDashboardOptimized',
  setup() {
    const store = useStore()
    
    // Reactive data
    const activeTab = ref('overview')
    const selectedPeriod = ref('month')
    const startDate = ref(new Date().toISOString().split('T')[0])
    const endDate = ref(new Date().toISOString().split('T')[0])
    const showExportModal = ref(false)
    const showGoalsModal = ref(false)
    const showInsights = ref(false)
    const isRefreshing = ref(false)
    const chartType = ref('line')
    const selectedMetric = ref('score')
    const timelineFilter = ref('all')
    
    // Navigation tabs
    const navigationTabs = [
      { id: 'overview', label: 'Visão Geral', icon: 'home' },
      { id: 'analytics', label: 'Análises', icon: 'chart-line' },
      { id: 'subjects', label: 'Matérias', icon: 'book' },
      { id: 'goals', label: 'Metas', icon: 'bullseye' },
      { id: 'activity', label: 'Atividade', icon: 'calendar-days' }
    ]
    
    // Time periods
    const timePeriods = [
      { value: 'week', label: 'Semana' },
      { value: 'month', label: 'Mês' },
      { value: 'quarter', label: 'Trimestre' },
      { value: 'year', label: 'Ano' }
    ]
    
    // Mock data
    const overallScore = ref(87)
    const streakDays = ref(23)
    const scoreCircumference = 565.5
    const scoreProgress = computed(() => (overallScore.value / 100) * scoreCircumference)
    
    const performanceDetails = ref([
      { id: 1, label: 'Frequência', value: '92%', percentage: 92, color: '#10b981', icon: 'calendar-check' },
      { id: 2, label: 'Conclusão', value: '85%', percentage: 85, color: '#3b82f6', icon: 'check-circle' },
      { id: 3, label: 'Eficiência', value: '78%', percentage: 78, color: '#8b5cf6', icon: 'bolt' },
      { id: 4, label: 'Retenção', value: '88%', percentage: 88, color: '#f59e0b', icon: 'brain' }
    ])
    
    const aiInsights = ref([
      {
        id: 1,
        type: 'success',
        icon: 'trophy',
        title: 'Excelente progresso!',
        message: 'Você manteve uma sequência de 23 dias.'
      },
      {
        id: 2,
        type: 'warning',
        icon: 'exclamation-triangle',
        title: 'Atenção em Física',
        message: 'Performance caiu 12% este mês.'
      },
      {
        id: 3,
        type: 'info',
        icon: 'chart-line',
        title: 'Novo recorde',
        message: '145.5 horas estudadas este mês!'
      }
    ])
    
    const keyMetrics = ref([
      {
        id: 1,
        label: 'Horas Estudadas',
        value: '145.5h',
        change: 15,
        trend: 'up',
        color: '#10b981',
        icon: 'clock',
        description: '+15% vs mês anterior',
        sparklineData: '0,15 20,10 40,25 60,20 80,35 100,30'
      },
      {
        id: 2,
        label: 'Exercícios Resolvidos',
        value: '1,234',
        change: 8,
        trend: 'up',
        color: '#3b82f6',
        icon: 'tasks',
        description: '+98 esta semana',
        sparklineData: '0,10 20,15 40,12 60,22 80,18 100,25'
      },
      {
        id: 3,
        label: 'Taxa de Acerto',
        value: '78%',
        change: -3,
        trend: 'down',
        color: '#f59e0b',
        icon: 'bullseye',
        description: 'Precisa de atenção',
        sparklineData: '0,25 20,22 40,20 60,18 80,16 100,15'
      },
      {
        id: 4,
        label: 'Matérias Estudadas',
        value: '8/12',
        change: 0,
        trend: 'up',
        color: '#8b5cf6',
        icon: 'book-open',
        description: '4 matérias pendentes',
        sparklineData: '0,8 20,8 40,8 60,8 80,8 100,8'
      }
    ])
    
    const recentActivities = ref([
      {
        id: 1,
        type: 'study',
        icon: 'book',
        title: 'Sessão de Estudo - Anatomia',
        description: 'Completou 2h de estudo com 85% de aproveitamento',
        date: new Date(),
        metadata: { duração: '2h', score: '85%' }
      },
      {
        id: 2,
        type: 'exam',
        icon: 'clipboard-check',
        title: 'Simulado de Fisiologia',
        description: 'Pontuação: 78/100 - Acima da média',
        date: new Date(Date.now() - 86400000),
        metadata: { pontuação: '78/100', posição: '12º' }
      },
      {
        id: 3,
        type: 'achievement',
        icon: 'trophy',
        title: 'Conquista Desbloqueada',
        description: '7 dias consecutivos de estudo!',
        date: new Date(Date.now() - 172800000)
      }
    ])
    
    const subjects = ref([
      { id: 1, name: 'Anatomia', avgScore: 85, hours: 45, exercises: 234, progress: 75, color: '#10b981', icon: 'user' },
      { id: 2, name: 'Fisiologia', avgScore: 78, hours: 38, exercises: 189, progress: 65, color: '#3b82f6', icon: 'heartbeat' },
      { id: 3, name: 'Farmacologia', avgScore: 82, hours: 32, exercises: 156, progress: 58, color: '#8b5cf6', icon: 'pills' },
      { id: 4, name: 'Patologia', avgScore: 79, hours: 28, exercises: 145, progress: 52, color: '#f59e0b', icon: 'virus' }
    ])
    
    const goals = ref([
      {
        id: 1,
        title: 'Completar módulo de Anatomia',
        description: 'Finalizar todos os capítulos e exercícios',
        deadline: new Date(Date.now() + 604800000),
        progress: 75,
        status: 'active'
      },
      {
        id: 2,
        title: 'Score médio de 85%',
        description: 'Manter score médio acima de 85% em todas as matérias',
        deadline: new Date(Date.now() + 2592000000),
        progress: 60,
        status: 'active'
      }
    ])
    
    const analyticsStats = ref([
      { id: 1, label: 'Média Geral', value: '82.5%', change: 5 },
      { id: 2, label: 'Total de Horas', value: '145.5h', change: 12 },
      { id: 3, label: 'Exercícios/Dia', value: '41.1', change: -2 },
      { id: 4, label: 'Taxa de Conclusão', value: '87%', change: 8 }
    ])
    
    // Computed properties
    const getPerformanceLevel = () => {
      if (overallScore.value >= 90) return 'Excelente'
      if (overallScore.value >= 80) return 'Muito Bom'
      if (overallScore.value >= 70) return 'Bom'
      if (overallScore.value >= 60) return 'Regular'
      return 'Precisa Melhorar'
    }
    
    const totalHours = computed(() => subjects.value.reduce((acc, s) => acc + s.hours, 0))
    const activeDays = ref(23)
    const avgDaily = computed(() => (totalHours.value / activeDays.value).toFixed(1))
    
    // Methods
    const refreshInsights = async () => {
      isRefreshing.value = true
      // Simulate API call
      setTimeout(() => {
        isRefreshing.value = false
      }, 2000)
    }
    
    const exportData = (format) => {
      console.log(`Exporting data as ${format}`)
      showExportModal.value = false
    }
    
    const formatTime = (date) => {
      return new Intl.RelativeTimeFormat('pt-BR', { numeric: 'auto' }).format(
        Math.round((date - new Date()) / (1000 * 60 * 60 * 24)),
        'day'
      )
    }
    
    const formatDate = (date) => {
      return new Intl.DateTimeFormat('pt-BR').format(date)
    }
    
    const editGoal = (goal) => {
      console.log('Editing goal:', goal)
    }
    
    const deleteGoal = (goal) => {
      console.log('Deleting goal:', goal)
    }
    
    // Chart setup
    let mainChart = null
    
    const initChart = () => {
      const ctx = document.querySelector('.chart-card canvas')
      if (ctx && ctx.getContext) {
        mainChart = new Chart(ctx.getContext('2d'), {
          type: chartType.value,
          data: {
            labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
            datasets: [{
              label: 'Performance',
              data: [65, 72, 78, 82, 85, 87],
              borderColor: '#8b5cf6',
              backgroundColor: 'rgba(139, 92, 246, 0.1)',
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                max: 100
              }
            }
          }
        })
      }
    }
    
    onMounted(() => {
      if (activeTab.value === 'analytics') {
        setTimeout(initChart, 100)
      }
    })
    
    watch(activeTab, (newVal) => {
      if (newVal === 'analytics' && !mainChart) {
        setTimeout(initChart, 100)
      }
    })
    
    watch([chartType, selectedMetric], () => {
      if (mainChart) {
        mainChart.destroy()
        initChart()
      }
    })
    
    return {
      activeTab,
      selectedPeriod,
      startDate,
      endDate,
      showExportModal,
      showGoalsModal,
      showInsights,
      isRefreshing,
      chartType,
      selectedMetric,
      timelineFilter,
      navigationTabs,
      timePeriods,
      overallScore,
      streakDays,
      scoreProgress,
      scoreCircumference,
      performanceDetails,
      aiInsights,
      keyMetrics,
      recentActivities,
      subjects,
      goals,
      analyticsStats,
      totalHours,
      activeDays,
      avgDaily,
      getPerformanceLevel,
      refreshInsights,
      exportData,
      formatTime,
      formatDate,
      editGoal,
      deleteGoal
    }
  }
}
</script>

<style scoped>
/* Global Styles */
.performance-dashboard {
  min-height: 100vh;
  background: #0f0f1e;
  color: #e2e8f0;
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  z-index: 0;
  pointer-events: none;
}

.gradient-bg {
  position: absolute;
  inset: 0;
  background: radial-gradient(ellipse at top left, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
              radial-gradient(ellipse at bottom right, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
}

.grid-pattern {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Header */
.dashboard-header {
  position: relative;
  z-index: 10;
  background: rgba(17, 17, 34, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem 2rem;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.header-left {
  flex: 1;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  border-radius: 12px;
  font-size: 1.5rem;
}

.dashboard-subtitle {
  color: #94a3b8;
  margin: 0.5rem 0 0 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.quick-stats-bar {
  display: flex;
  gap: 2rem;
  padding: 0 2rem;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.quick-stat {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.stat-icon.success {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.stat-icon.warning {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #e2e8f0;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.action-button.primary {
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  border: none;
}

.action-button.primary:hover {
  background: linear-gradient(135deg, #9333ea, #4f46e5);
}

/* Navigation */
.dashboard-nav {
  position: sticky;
  top: 0;
  z-index: 9;
  background: rgba(17, 17, 34, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  gap: 0.5rem;
  padding: 0 2rem;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.nav-tab:hover {
  color: #e2e8f0;
  background: rgba(255, 255, 255, 0.05);
}

.nav-tab.active {
  color: #8b5cf6;
  border-bottom-color: #8b5cf6;
}

/* Main Content */
.dashboard-content {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Period Selector Card */
.period-selector-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 1.5rem 0;
}

.period-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.period-buttons {
  display: flex;
  gap: 0.5rem;
}

.period-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: #94a3b8;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.period-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #e2e8f0;
}

.period-btn.active {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  color: #8b5cf6;
}

.date-inputs {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.date-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date-field label {
  font-size: 0.75rem;
  color: #94a3b8;
}

.date-field input {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 0.875rem;
}

.date-separator {
  color: #94a3b8;
  margin-top: 1.5rem;
}

/* Tab Content */
.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Overview Tab */
.content-grid {
  display: grid;
  gap: 2rem;
}

.performance-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
}

.main-card {
  grid-column: span 2;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.card-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.performance-level {
  padding: 0.375rem 1rem;
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.performance-visual {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  align-items: center;
}

.circular-progress {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
}

.circular-progress svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-bg {
  fill: none;
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 12;
}

.progress-fill {
  fill: none;
  stroke: url(#progressGradient);
  stroke-width: 12;
  stroke-linecap: round;
  transition: stroke-dasharray 0.5s ease;
}

.progress-content {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: 700;
}

.score-unit {
  font-size: 1.5rem;
  color: #94a3b8;
  margin-left: 0.25rem;
}

.performance-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
}

.detail-header svg {
  font-size: 1.125rem;
}

.detail-value {
  margin-left: auto;
  font-weight: 600;
}

.detail-progress {
  width: 100%;
}

.progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar .progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
}

/* Insights Card */
.insights-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
}

.refresh-btn {
  padding: 0.5rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #94a3b8;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #e2e8f0;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid transparent;
  transition: all 0.2s;
}

.insight-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.insight-item.success {
  border-left: 3px solid #10b981;
}

.insight-item.warning {
  border-left: 3px solid #f59e0b;
}

.insight-item.info {
  border-left: 3px solid #3b82f6;
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
}

.insight-content h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.insight-content p {
  font-size: 0.813rem;
  color: #94a3b8;
  margin: 0;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.metric-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.813rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.metric-trend.up {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.metric-trend.down {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.metric-body h4 {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0 0 0.5rem 0;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.metric-description {
  font-size: 0.813rem;
  color: #94a3b8;
  margin: 0 0 1rem 0;
}

.metric-sparkline {
  height: 40px;
  margin-top: 1rem;
}

.metric-sparkline svg {
  width: 100%;
  height: 100%;
}

/* Timeline */
.timeline-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin-top: 2rem;
}

.filter-select {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 0.875rem;
  cursor: pointer;
}

.timeline {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.timeline-item {
  display: flex;
  gap: 1rem;
  position: relative;
}

.timeline-marker {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.timeline-marker.study {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
  color: #3b82f6;
}

.timeline-marker.exam {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  color: #8b5cf6;
}

.timeline-marker.achievement {
  background: rgba(245, 158, 11, 0.2);
  border-color: #f59e0b;
  color: #f59e0b;
}

.timeline-content {
  flex: 1;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.timeline-header h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
}

.timeline-time {
  font-size: 0.75rem;
  color: #94a3b8;
}

.timeline-content p {
  font-size: 0.813rem;
  color: #94a3b8;
  margin: 0 0 0.75rem 0;
}

.timeline-meta {
  display: flex;
  gap: 1rem;
}

.meta-tag {
  font-size: 0.75rem;
  color: #64748b;
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

/* Analytics Tab */
.analytics-grid {
  display: grid;
  gap: 2rem;
}

.chart-controls {
  display: flex;
  gap: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-group label {
  font-size: 0.875rem;
  color: #94a3b8;
}

.control-select {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 0.875rem;
  cursor: pointer;
}

.chart-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  height: 400px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
}

.stat-card h4 {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0 0 0.5rem 0;
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-comparison {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.813rem;
}

.change {
  font-weight: 600;
}

.change.positive {
  color: #10b981;
}

.change.negative {
  color: #ef4444;
}

.comparison-label {
  color: #64748b;
}

/* Subjects Tab */
.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.subject-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s;
}

.subject-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
}

.subject-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.subject-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.subject-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.subject-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.stat-row .stat-value {
  font-weight: 600;
}

.subject-progress {
  margin-top: 1rem;
}

.progress-label {
  display: block;
  font-size: 0.813rem;
  color: #94a3b8;
  margin-top: 0.5rem;
}

/* Goals Tab */
.goals-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.goals-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.add-goal-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.add-goal-btn:hover {
  background: linear-gradient(135deg, #9333ea, #4f46e5);
  transform: translateY(-1px);
}

.goals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.goal-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s;
}

.goal-card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.goal-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.goal-deadline {
  font-size: 0.813rem;
  color: #94a3b8;
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.goal-description {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0 0 1.5rem 0;
}

.goal-progress {
  margin-bottom: 1rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.goal-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.goal-action-btn {
  padding: 0.5rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.2s;
}

.goal-action-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #e2e8f0;
}

/* Activity Tab */
.activity-calendar {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.activity-calendar h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

.calendar-grid {
  /* Calendar implementation styles */
}

.activity-summary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
}

.activity-summary h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
}

.summary-stat {
  text-align: center;
}

.summary-stat .stat-label {
  display: block;
  font-size: 0.875rem;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.summary-stat .stat-value {
  font-size: 2rem;
  font-weight: 700;
}

/* Modal */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1e1e2e;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
}

.modal-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 2rem 0;
}

.export-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.export-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #e2e8f0;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.export-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.export-btn svg {
  font-size: 2rem;
}

/* Modal Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-active .modal-content,
.modal-leave-active .modal-content {
  transition: transform 0.3s;
}

.modal-enter-from .modal-content {
  transform: scale(0.9);
}

.modal-leave-to .modal-content {
  transform: scale(0.9);
}

/* Responsive */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .quick-stats-bar {
    border-right: none;
    padding-right: 0;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem;
  }
  
  .dashboard-content {
    padding: 1rem;
  }
  
  .header-right {
    flex-direction: column;
    gap: 1rem;
  }
  
  .quick-stats-bar {
    width: 100%;
    justify-content: space-around;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .nav-wrapper {
    padding: 0 1rem;
    overflow-x: auto;
  }
  
  .period-controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .period-buttons {
    width: 100%;
    justify-content: space-between;
  }
  
  .date-inputs {
    width: 100%;
  }
  
  .performance-visual {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .export-options {
    grid-template-columns: 1fr;
  }
}

/* Utility Classes */
.fa-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>