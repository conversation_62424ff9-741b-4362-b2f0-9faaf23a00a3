<template>
  <ErrorBoundary>
    <div class="performance-dashboard" v-if="isInitialized">
      <!-- Clean Header -->
      <header class="dashboard-header">
        <div class="header-container">
          <div class="header-title">
            <div class="title-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h1>Dashboard de Desempenho</h1>
            <p class="subtitle">Análise completa do seu progresso acadêmico</p>
          </div>
          
          <!-- Time Period Selector -->
          <div class="period-selector">
            <button 
              v-for="period in timePeriods" 
              :key="period.value"
              @click="changePeriod(period.value)"
              :class="['period-btn', { active: selectedPeriod === period.value }]"
            >
              <i :class="period.icon"></i>
              {{ period.label }}
            </button>
          </div>
        </div>
      </header>

      <!-- Dynamic Period Info Bar -->
      <div class="period-info-bar">
        <div class="info-container">
          <div class="period-summary">
            <h3>{{ periodTitle }}</h3>
            <p>{{ periodDateRange }}</p>
          </div>
          <div class="period-highlights">
            <div class="highlight-item" v-for="highlight in periodHighlights" :key="highlight.id">
              <i :class="highlight.icon" :style="{ color: highlight.color }"></i>
              <div>
                <span class="highlight-value">{{ highlight.value }}</span>
                <span class="highlight-label">{{ highlight.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Overview Cards -->
      <section class="overview-section">
        <div class="overview-grid">
          <div class="overview-card" v-for="metric in overviewMetrics" :key="metric.id">
            <div class="metric-header">
              <div class="metric-icon" :style="{ backgroundColor: metric.color + '20' }">
                <i :class="metric.icon" :style="{ color: metric.color }"></i>
              </div>
              <div class="metric-trend" :class="metric.trend">
                <i :class="metric.trendIcon"></i>
                {{ metric.trendValue }}%
              </div>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ metric.value }}</div>
              <div class="metric-label">{{ metric.label }}</div>
              <div class="metric-subtitle">{{ metric.subtitle }}</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Performance Score Card -->
        <div class="card performance-score-card">
          <h2>Score de Performance</h2>
          <div class="score-display">
            <div class="score-ring">
              <svg viewBox="0 0 200 200" class="score-svg">
                <circle 
                  cx="100" 
                  cy="100" 
                  r="90" 
                  fill="none" 
                  stroke="#e9ecef" 
                  stroke-width="8"
                />
                <circle 
                  cx="100" 
                  cy="100" 
                  r="90" 
                  fill="none" 
                  stroke="#667eea" 
                  stroke-width="8"
                  :stroke-dasharray="circumference"
                  :stroke-dashoffset="scoreOffset"
                  stroke-linecap="round"
                  transform="rotate(-90 100 100)"
                />
              </svg>
              <div class="score-info">
                <div class="score-value">{{ performanceScore }}</div>
                <div class="score-label">Performance</div>
                <div class="score-rating">{{ getScoreRating() }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Period-Specific Analytics -->
        <div class="card period-analytics-card">
          <h2>Análise de {{ periodTitle }}</h2>
          
          <!-- Chart Container with Safe Rendering -->
          <div class="chart-container" v-if="chartsReady">
            <canvas ref="mainChart" v-show="selectedPeriod === 'month'"></canvas>
            <div v-show="selectedPeriod === 'week'" class="week-heatmap">
              <h3>Distribuição de Estudo</h3>
              <div class="heatmap-grid">
                <div v-for="day in weekDays" :key="day.name" class="day-row">
                  <span class="day-label">{{ day.short }}</span>
                  <div class="hour-cells">
                    <div 
                      v-for="hour in 24" 
                      :key="hour"
                      class="hour-cell"
                      :style="{ backgroundColor: getHeatmapColor(safeGet(day, `hours.${hour-1}`, 0)) }"
                      :title="`${day.name} ${hour-1}h: ${safeGet(day, `hours.${hour-1}`, 0)} min`"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Loading State -->
          <div v-else class="chart-loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Carregando análises...</p>
          </div>
        </div>
      </div>

      <!-- Charts Status (Debug) -->
      <div v-if="showDebug" class="debug-panel">
        <h4>Debug Info</h4>
        <p>Initialized: {{ isInitialized }}</p>
        <p>Charts Ready: {{ chartsReady }}</p>
        <p>Selected Period: {{ selectedPeriod }}</p>
        <p>Charts Count: {{ Object.keys(charts).length }}</p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-else class="dashboard-loading">
      <div class="loading-content">
        <i class="fas fa-chart-line fa-3x"></i>
        <h2>Carregando Dashboard...</h2>
        <p>Preparando suas análises de desempenho</p>
      </div>
    </div>
  </ErrorBoundary>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import Chart from 'chart.js/auto'
import SafeComponentMixin from '@/mixins/SafeComponentMixin'

export default {
  name: 'PerformanceDashboardSafe',
  
  mixins: [SafeComponentMixin],
  
  setup() {
    // Reactive state
    const isInitialized = ref(false)
    const chartsReady = ref(false)
    const selectedPeriod = ref('week')
    const charts = ref({})
    const showDebug = ref(false)
    
    // Data
    const periodTitle = ref('Esta Semana')
    const periodDateRange = ref('18 - 24 de Novembro, 2024')
    const performanceScore = ref(85)
    
    const timePeriods = [
      { value: 'week', label: 'Semana', icon: 'fas fa-calendar-week' },
      { value: 'month', label: 'Mês', icon: 'fas fa-calendar-alt' },
      { value: 'semester', label: 'Semestre', icon: 'fas fa-calendar' },
      { value: 'year', label: 'Ano', icon: 'fas fa-calendar-check' }
    ]
    
    const periodHighlights = ref([
      { id: 1, icon: 'fas fa-fire', value: '7', label: 'dias consecutivos', color: '#e53e3e' },
      { id: 2, icon: 'fas fa-clock', value: '28h', label: 'de estudo', color: '#667eea' },
      { id: 3, icon: 'fas fa-brain', value: '342', label: 'cards revisados', color: '#48bb78' }
    ])
    
    const overviewMetrics = ref([
      {
        id: 1,
        icon: 'fas fa-clock',
        color: '#667eea',
        value: '156h',
        label: 'Tempo Total',
        subtitle: 'Este período',
        trend: 'up',
        trendIcon: 'fas fa-arrow-up',
        trendValue: 12
      },
      {
        id: 2,
        icon: 'fas fa-brain',
        color: '#48bb78',
        value: '2,847',
        label: 'Cards Revisados',
        subtitle: '89% de retenção',
        trend: 'up',
        trendIcon: 'fas fa-arrow-up',
        trendValue: 8
      },
      {
        id: 3,
        icon: 'fas fa-bullseye',
        color: '#ed8936',
        value: '87%',
        label: 'Taxa de Acerto',
        subtitle: '+5% vs último período',
        trend: 'up',
        trendIcon: 'fas fa-arrow-up',
        trendValue: 5
      },
      {
        id: 4,
        icon: 'fas fa-fire',
        color: '#e53e3e',
        value: '28',
        label: 'Sequência',
        subtitle: 'Dias consecutivos',
        trend: 'stable',
        trendIcon: 'fas fa-minus',
        trendValue: 0
      }
    ])
    
    const weekDays = ref([
      { name: 'Segunda', short: 'Seg', hours: Array(24).fill(0).map(() => Math.floor(Math.random() * 60)) },
      { name: 'Terça', short: 'Ter', hours: Array(24).fill(0).map(() => Math.floor(Math.random() * 60)) },
      { name: 'Quarta', short: 'Qua', hours: Array(24).fill(0).map(() => Math.floor(Math.random() * 60)) },
      { name: 'Quinta', short: 'Qui', hours: Array(24).fill(0).map(() => Math.floor(Math.random() * 60)) },
      { name: 'Sexta', short: 'Sex', hours: Array(24).fill(0).map(() => Math.floor(Math.random() * 60)) },
      { name: 'Sábado', short: 'Sáb', hours: Array(24).fill(0).map(() => Math.floor(Math.random() * 60)) },
      { name: 'Domingo', short: 'Dom', hours: Array(24).fill(0).map(() => Math.floor(Math.random() * 60)) }
    ])
    
    // Computed
    const circumference = computed(() => 2 * Math.PI * 90)
    const scoreOffset = computed(() => {
      const percent = (100 - performanceScore.value) / 100
      return circumference.value * percent
    })
    
    // Methods
    const initializeData = async () => {
      try {
        // Simulate data loading
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // Initialize data here
        updatePeriodData()
        
        isInitialized.value = true
        
        // Wait for DOM to be ready
        await nextTick()
        
        // Initialize charts after another tick
        setTimeout(() => {
          chartsReady.value = true
          createCharts()
        }, 300)
        
      } catch (error) {
        console.error('Error initializing dashboard:', error)
      }
    }
    
    const updatePeriodData = () => {
      const periodTitles = {
        week: 'Esta Semana',
        month: 'Este Mês',
        semester: 'Este Semestre',
        year: 'Este Ano'
      }
      
      periodTitle.value = periodTitles[selectedPeriod.value] || 'Esta Semana'
      
      // Update date range based on period
      const now = new Date()
      if (selectedPeriod.value === 'week') {
        periodDateRange.value = '18 - 24 de Novembro, 2024'
      } else if (selectedPeriod.value === 'month') {
        periodDateRange.value = 'Novembro 2024'
      } else if (selectedPeriod.value === 'semester') {
        periodDateRange.value = 'Agosto - Dezembro 2024'
      } else {
        periodDateRange.value = '2024'
      }
    }
    
    const changePeriod = (period) => {
      selectedPeriod.value = period
      updatePeriodData()
      
      // Recreate charts for new period
      if (chartsReady.value) {
        destroyCharts()
        setTimeout(() => {
          createCharts()
        }, 100)
      }
    }
    
    const getScoreRating = () => {
      const score = performanceScore.value
      if (score >= 90) return 'Excelente!'
      if (score >= 80) return 'Muito Bom!'
      if (score >= 70) return 'Bom'
      if (score >= 60) return 'Regular'
      return 'Precisa Melhorar'
    }
    
    const getHeatmapColor = (minutes) => {
      if (minutes === 0) return '#f7fafc'
      if (minutes < 15) return '#bee3f8'
      if (minutes < 30) return '#90cdf4'
      if (minutes < 45) return '#63b3ed'
      return '#4299e1'
    }
    
    const createCharts = async () => {
      if (!chartsReady.value) return
      
      await nextTick()
      
      if (selectedPeriod.value === 'month') {
        createMonthChart()
      }
    }
    
    const createMonthChart = () => {
      const canvas = document.querySelector('.performance-dashboard canvas[ref="mainChart"]')
      if (!canvas || !canvas.getContext) {
        console.warn('Canvas not ready for month chart')
        return
      }
      
      try {
        const ctx = canvas.getContext('2d')
        if (!ctx) return
        
        // Destroy existing chart if any
        if (charts.value.main) {
          charts.value.main.destroy()
          charts.value.main = null
        }
        
        charts.value.main = new Chart(ctx, {
          type: 'line',
          data: {
            labels: Array.from({ length: 30 }, (_, i) => i + 1),
            datasets: [
              {
                label: 'Horas de Estudo',
                data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 6) + 2),
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        })
      } catch (error) {
        console.error('Error creating month chart:', error)
      }
    }
    
    const destroyCharts = () => {
      Object.keys(charts.value).forEach(key => {
        try {
          if (charts.value[key]) {
            charts.value[key].destroy()
            charts.value[key] = null
          }
        } catch (error) {
          console.error(`Error destroying chart ${key}:`, error)
        }
      })
      charts.value = {}
    }
    
    const safeGet = (obj, path, defaultValue = null) => {
      const keys = path.split('.')
      let result = obj
      
      for (const key of keys) {
        if (result && typeof result === 'object' && key in result) {
          result = result[key]
        } else {
          return defaultValue
        }
      }
      
      return result
    }
    
    // Lifecycle
    onMounted(() => {
      initializeData()
      
      // Enable debug in development
      if (process.env.NODE_ENV !== 'production') {
        showDebug.value = true
      }
    })
    
    onBeforeUnmount(() => {
      destroyCharts()
    })
    
    // Watchers
    watch(selectedPeriod, () => {
      updatePeriodData()
    })
    
    return {
      // State
      isInitialized,
      chartsReady,
      selectedPeriod,
      charts,
      showDebug,
      
      // Data
      periodTitle,
      periodDateRange,
      performanceScore,
      timePeriods,
      periodHighlights,
      overviewMetrics,
      weekDays,
      
      // Computed
      circumference,
      scoreOffset,
      
      // Methods
      changePeriod,
      getScoreRating,
      getHeatmapColor,
      safeGet
    }
  }
}
</script>

<style scoped>
/* Loading State */
.dashboard-loading {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafbfc;
}

.loading-content {
  text-align: center;
  color: #667eea;
}

.loading-content i {
  margin-bottom: 20px;
  opacity: 0.6;
}

.loading-content h2 {
  font-size: 24px;
  margin-bottom: 10px;
}

.loading-content p {
  color: #718096;
}

/* Dashboard Layout */
.performance-dashboard {
  min-height: 100vh;
  background: #fafbfc;
  padding-bottom: 40px;
}

/* Header */
.dashboard-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 24px 0;
  margin-bottom: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: #667eea;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.header-title h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

/* Period Selector */
.period-selector {
  display: flex;
  gap: 8px;
  background: #f7fafc;
  padding: 4px;
  border-radius: 12px;
}

.period-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: #718096;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.period-btn:hover {
  color: #4a5568;
}

.period-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Period Info Bar */
.period-info-bar {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin: 0 24px 32px;
  max-width: 1352px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.info-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.period-summary h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px;
  color: #1a202c;
}

.period-summary p {
  margin: 0;
  color: #718096;
}

.period-highlights {
  display: flex;
  gap: 32px;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.highlight-item i {
  font-size: 24px;
}

.highlight-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
}

.highlight-label {
  display: block;
  font-size: 14px;
  color: #718096;
}

/* Overview Section */
.overview-section {
  padding: 0 24px;
  margin-bottom: 32px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.overview-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.metric-trend {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
}

.metric-trend.up {
  color: #48bb78;
  background: rgba(72, 187, 120, 0.1);
}

.metric-trend.down {
  color: #f56565;
  background: rgba(245, 101, 101, 0.1);
}

.metric-trend.stable {
  color: #718096;
  background: rgba(113, 128, 150, 0.1);
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 16px;
  color: #4a5568;
  margin-bottom: 4px;
}

.metric-subtitle {
  font-size: 14px;
  color: #718096;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  padding: 0 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Cards */
.card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 24px;
  color: #1a202c;
}

/* Performance Score */
.score-display {
  display: flex;
  justify-content: center;
  margin: 32px 0;
}

.score-ring {
  position: relative;
  width: 200px;
  height: 200px;
}

.score-svg {
  width: 100%;
  height: 100%;
}

.score-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.score-value {
  font-size: 48px;
  font-weight: 700;
  color: #667eea;
}

.score-label {
  font-size: 14px;
  color: #718096;
  margin: 4px 0;
}

.score-rating {
  font-size: 16px;
  font-weight: 500;
  color: #48bb78;
}

/* Chart Container */
.chart-container {
  height: 400px;
  position: relative;
}

.chart-container canvas {
  max-height: 100%;
}

.chart-loading {
  height: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #718096;
}

.chart-loading i {
  font-size: 32px;
  margin-bottom: 16px;
  color: #667eea;
}

/* Week Heatmap */
.week-heatmap {
  height: 100%;
}

.week-heatmap h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px;
  color: #4a5568;
}

.heatmap-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.day-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.day-label {
  width: 40px;
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.hour-cells {
  display: flex;
  gap: 2px;
  flex: 1;
}

.hour-cell {
  flex: 1;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.hour-cell:hover {
  transform: scale(1.2);
}

/* Debug Panel */
.debug-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: white;
  border: 2px solid #667eea;
  border-radius: 8px;
  padding: 16px;
  font-size: 12px;
  font-family: monospace;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.debug-panel h4 {
  margin: 0 0 8px;
  color: #667eea;
}

.debug-panel p {
  margin: 4px 0;
  color: #4a5568;
}

/* Responsive */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .period-selector {
    width: 100%;
    justify-content: space-between;
  }
  
  .info-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .period-highlights {
    flex-wrap: wrap;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .debug-panel {
    display: none;
  }
}
</style>