<template>
  <div class="performance-dashboard-v2">
    <!-- Sidebar Navigation -->
    <aside class="sidebar" :class="{ 'collapsed': sidebarCollapsed }">
      <button class="sidebar-toggle" @click="sidebarCollapsed = !sidebarCollapsed">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path v-if="!sidebarCollapsed" d="M13 14L9 10L13 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          <path v-else d="M7 14L11 10L7 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </button>
      
      <nav class="sidebar-nav">
        <a 
          v-for="item in sidebarItems" 
          :key="item.id"
          :href="`#${item.id}`"
          @click="activeSection = item.id"
          :class="['nav-item', { active: activeSection === item.id }]"
        >
          <div class="nav-icon">
            <svg :width="20" :height="20" viewBox="0 0 20 20" fill="none">
              <path :d="item.icon" stroke="currentColor" stroke-width="1.5"/>
            </svg>
          </div>
          <span class="nav-label" v-if="!sidebarCollapsed">{{ item.label }}</span>
          <div class="nav-indicator"></div>
        </a>
      </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Compact Header -->
      <header class="dashboard-header">
        <div class="header-left">
          <h1 class="dashboard-title">Central de Performance</h1>
          <p class="dashboard-subtitle">Análise completa do seu progresso acadêmico</p>
        </div>
        
        <div class="header-actions">
          <!-- Date Range Picker -->
          <div class="date-range-picker">
            <button class="date-btn" @click="showDatePicker = !showDatePicker">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <rect x="2" y="3" width="12" height="11" rx="2" stroke="currentColor" stroke-width="1.5"/>
                <path d="M2 6H14M5 1V3M11 1V3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
              </svg>
              <span>{{ currentDateRange }}</span>
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
              </svg>
            </button>
          </div>

          <!-- Export Button -->
          <button class="export-btn" @click="showExportModal = true">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M8 1V10M8 10L11 7M8 10L5 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
              <path d="M2 11V13C2 14 3 15 4 15H12C13 15 14 14 14 13V11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
            <span>Exportar</span>
          </button>

          <!-- Settings -->
          <button class="settings-btn" @click="showSettings = !showSettings">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <circle cx="10" cy="10" r="2" stroke="currentColor" stroke-width="1.5"/>
              <path d="M10 1V3M10 17V19M19 10H17M3 10H1M16.5 3.5L15 5M5 15L3.5 16.5M16.5 16.5L15 15M5 5L3.5 3.5" 
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </header>

      <!-- Overview Cards -->
      <section id="overview" class="overview-section">
        <div class="overview-grid">
          <div class="overview-card primary">
            <div class="card-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L15 9L22 10L17 15L18 22L12 18L6 22L7 15L2 10L9 9L12 2Z" 
                      stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="card-content">
              <h3 class="card-value">{{ overallScore }}<span class="card-unit">%</span></h3>
              <p class="card-label">Score Geral</p>
              <div class="card-trend" :class="overallTrend.type">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path v-if="overallTrend.type === 'up'" d="M3 10L8 5L13 10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  <path v-else d="M3 6L8 11L13 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                <span>{{ overallTrend.value }}%</span>
              </div>
            </div>
          </div>

          <div class="overview-card">
            <div class="card-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
                <path d="M7 12L10 15L17 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="card-content">
              <h3 class="card-value">{{ completedTasks }}</h3>
              <p class="card-label">Tarefas Concluídas</p>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: taskProgress + '%' }"></div>
              </div>
            </div>
          </div>

          <div class="overview-card">
            <div class="card-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                <path d="M12 7V12L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <div class="card-content">
              <h3 class="card-value">{{ studyHours }}<span class="card-unit">h</span></h3>
              <p class="card-label">Horas de Estudo</p>
              <p class="card-subtitle">Esta semana</p>
            </div>
          </div>

          <div class="overview-card">
            <div class="card-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" 
                      stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="card-content">
              <h3 class="card-value">{{ streakDays }}</h3>
              <p class="card-label">Dias Consecutivos</p>
              <p class="card-subtitle">Sequência atual</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Performance Chart -->
      <section id="analytics" class="analytics-section">
        <div class="section-header">
          <h2 class="section-title">Análise de Performance</h2>
          <div class="chart-controls">
            <button 
              v-for="metric in chartMetrics" 
              :key="metric.id"
              @click="selectedMetric = metric.id"
              :class="['metric-btn', { active: selectedMetric === metric.id }]"
            >
              {{ metric.label }}
            </button>
          </div>
        </div>

        <div class="chart-container">
          <canvas ref="performanceChart"></canvas>
        </div>

        <!-- Insights Cards -->
        <div class="insights-grid">
          <div class="insight-card" v-for="insight in performanceInsights" :key="insight.id">
            <div class="insight-icon" :class="insight.type">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path :d="insight.icon" fill="currentColor"/>
              </svg>
            </div>
            <div class="insight-content">
              <h4 class="insight-title">{{ insight.title }}</h4>
              <p class="insight-text">{{ insight.description }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Subject Performance -->
      <section id="subjects" class="subjects-section">
        <h2 class="section-title">Performance por Matéria</h2>
        
        <div class="subjects-grid">
          <div 
            v-for="subject in subjects" 
            :key="subject.id"
            class="subject-card"
            @click="selectedSubject = subject"
          >
            <div class="subject-header">
              <div class="subject-icon" :style="{ backgroundColor: subject.color + '20', color: subject.color }">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path :d="subject.icon" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <h3 class="subject-name">{{ subject.name }}</h3>
            </div>

            <div class="subject-stats">
              <div class="stat-item">
                <span class="stat-label">Progresso</span>
                <span class="stat-value">{{ subject.progress }}%</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Nota Média</span>
                <span class="stat-value">{{ subject.average }}</span>
              </div>
            </div>

            <div class="subject-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: subject.progress + '%', backgroundColor: subject.color }"
                ></div>
              </div>
            </div>

            <div class="subject-topics">
              <span class="topic-count">{{ subject.completedTopics }}/{{ subject.totalTopics }} tópicos</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Goals Section -->
      <section id="goals" class="goals-section">
        <div class="section-header">
          <h2 class="section-title">Metas e Objetivos</h2>
          <button class="add-goal-btn" @click="showGoalModal = true">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M8 3V13M3 8H13" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
            Nova Meta
          </button>
        </div>

        <div class="goals-grid">
          <div v-for="goal in goals" :key="goal.id" class="goal-card" :class="goal.status">
            <div class="goal-header">
              <div class="goal-icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path :d="getGoalIcon(goal.type)" stroke="currentColor" stroke-width="1.5"/>
                </svg>
              </div>
              <div class="goal-badge" v-if="goal.status === 'completed'">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M5 8L7 10L11 6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
              </div>
            </div>

            <h3 class="goal-title">{{ goal.title }}</h3>
            <p class="goal-description">{{ goal.description }}</p>

            <div class="goal-progress">
              <div class="progress-info">
                <span class="progress-label">{{ goal.current }}/{{ goal.target }} {{ goal.unit }}</span>
                <span class="progress-percentage">{{ Math.round((goal.current / goal.target) * 100) }}%</span>
              </div>
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: Math.min((goal.current / goal.target) * 100, 100) + '%' }"
                ></div>
              </div>
            </div>

            <div class="goal-deadline">
              <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                <circle cx="7" cy="7" r="6" stroke="currentColor" stroke-width="1.5"/>
                <path d="M7 3V7L9 9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
              </svg>
              <span>{{ formatDeadline(goal.deadline) }}</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Activity Heatmap -->
      <section id="activity" class="activity-section">
        <h2 class="section-title">Mapa de Atividades</h2>
        
        <div class="heatmap-container">
          <div class="heatmap-months">
            <span v-for="month in heatmapMonths" :key="month">{{ month }}</span>
          </div>
          
          <div class="heatmap-grid">
            <div class="heatmap-weekdays">
              <span>Seg</span>
              <span>Ter</span>
              <span>Qua</span>
              <span>Qui</span>
              <span>Sex</span>
              <span>Sáb</span>
              <span>Dom</span>
            </div>
            
            <div class="heatmap-cells">
              <div 
                v-for="(day, index) in heatmapData" 
                :key="index"
                class="heatmap-cell"
                :class="`level-${day.level}`"
                :title="`${day.date}: ${day.hours} horas de estudo`"
              ></div>
            </div>
          </div>

          <div class="heatmap-legend">
            <span>Menos</span>
            <div class="legend-cells">
              <div class="heatmap-cell level-0"></div>
              <div class="heatmap-cell level-1"></div>
              <div class="heatmap-cell level-2"></div>
              <div class="heatmap-cell level-3"></div>
              <div class="heatmap-cell level-4"></div>
            </div>
            <span>Mais</span>
          </div>
        </div>
      </section>
    </main>

    <!-- Export Modal -->
    <transition name="modal">
      <div v-if="showExportModal" class="modal-overlay" @click.self="showExportModal = false">
        <div class="modal-content">
          <div class="modal-header">
            <h3>Exportar Relatório</h3>
            <button class="modal-close" @click="showExportModal = false">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>

          <div class="modal-body">
            <div class="export-options">
              <label class="export-option">
                <input type="radio" name="export-format" value="pdf" v-model="exportFormat">
                <div class="option-content">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6C5 2 4 3 4 4V20C4 21 5 22 6 22H18C19 22 20 21 20 20V8L14 2Z" 
                          stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                  </svg>
                  <span>PDF</span>
                </div>
              </label>

              <label class="export-option">
                <input type="radio" name="export-format" value="excel" v-model="exportFormat">
                <div class="option-content">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
                    <path d="M3 9H21M9 21V9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span>Excel</span>
                </div>
              </label>

              <label class="export-option">
                <input type="radio" name="export-format" value="csv" v-model="exportFormat">
                <div class="option-content">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M4 6H20M4 12H20M4 18H20" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                  <span>CSV</span>
                </div>
              </label>
            </div>

            <div class="export-sections">
              <h4>Seções a incluir:</h4>
              <label class="checkbox-option" v-for="section in exportSections" :key="section.id">
                <input type="checkbox" v-model="section.included">
                <span>{{ section.label }}</span>
              </label>
            </div>
          </div>

          <div class="modal-footer">
            <button class="btn-secondary" @click="showExportModal = false">Cancelar</button>
            <button class="btn-primary" @click="exportReport">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 1V10M8 10L11 7M8 10L5 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
              </svg>
              Exportar
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- Goal Modal -->
    <transition name="modal">
      <div v-if="showGoalModal" class="modal-overlay" @click.self="showGoalModal = false">
        <div class="modal-content">
          <div class="modal-header">
            <h3>Nova Meta</h3>
            <button class="modal-close" @click="showGoalModal = false">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>

          <div class="modal-body">
            <form @submit.prevent="createGoal">
              <div class="form-group">
                <label>Título da Meta</label>
                <input 
                  type="text" 
                  v-model="newGoal.title" 
                  placeholder="Ex: Completar todos os exercícios de Cálculo"
                  required
                >
              </div>

              <div class="form-group">
                <label>Tipo</label>
                <select v-model="newGoal.type" required>
                  <option value="study">Horas de Estudo</option>
                  <option value="tasks">Tarefas</option>
                  <option value="grade">Nota</option>
                  <option value="custom">Personalizado</option>
                </select>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>Meta</label>
                  <input 
                    type="number" 
                    v-model="newGoal.target" 
                    placeholder="100"
                    required
                  >
                </div>

                <div class="form-group">
                  <label>Unidade</label>
                  <input 
                    type="text" 
                    v-model="newGoal.unit" 
                    placeholder="horas"
                    required
                  >
                </div>
              </div>

              <div class="form-group">
                <label>Prazo</label>
                <input 
                  type="date" 
                  v-model="newGoal.deadline"
                  :min="today"
                  required
                >
              </div>

              <div class="form-actions">
                <button type="button" class="btn-secondary" @click="showGoalModal = false">
                  Cancelar
                </button>
                <button type="submit" class="btn-primary">
                  Criar Meta
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Chart from 'chart.js/auto'

export default {
  name: 'PerformanceDashboardV2',
  setup() {
    // Refs
    const performanceChart = ref(null)
    let chartInstance = null
    
    // State
    const sidebarCollapsed = ref(false)
    const activeSection = ref('overview')
    const showDatePicker = ref(false)
    const showExportModal = ref(false)
    const showGoalModal = ref(false)
    const showSettings = ref(false)
    const selectedMetric = ref('score')
    const selectedSubject = ref(null)
    const exportFormat = ref('pdf')
    
    // Data
    const currentDateRange = ref('Últimos 30 dias')
    const overallScore = ref(85)
    const overallTrend = ref({ type: 'up', value: 12 })
    const completedTasks = ref(127)
    const taskProgress = ref(78)
    const studyHours = ref(42.5)
    const streakDays = ref(15)
    
    const sidebarItems = ref([
      { id: 'overview', label: 'Visão Geral', icon: 'M3 3h18v6H3zM3 12h10v9H3zM16 12h5v9h-5z' },
      { id: 'analytics', label: 'Análises', icon: 'M3 19L12 10L16 14L21 9M21 9H17M21 9V13' },
      { id: 'subjects', label: 'Matérias', icon: 'M12 2L2 7V9C2 16 12 21 12 21S22 16 22 9V7L12 2Z' },
      { id: 'goals', label: 'Metas', icon: 'M12 2L15 9L22 10L17 15L18 22L12 18L6 22L7 15L2 10L9 9L12 2Z' },
      { id: 'activity', label: 'Atividade', icon: 'M3 3h18v18H3zM8 8h8v8H8z' }
    ])
    
    const chartMetrics = ref([
      { id: 'score', label: 'Score' },
      { id: 'hours', label: 'Horas' },
      { id: 'tasks', label: 'Tarefas' },
      { id: 'accuracy', label: 'Precisão' }
    ])
    
    const performanceInsights = ref([
      {
        id: 1,
        type: 'success',
        icon: 'M8 3.5C8 2.67 7.33 2 6.5 2S5 2.67 5 3.5 5.67 5 6.5 5 8 4.33 8 3.5M12 12L15 9V7L12 4L10 6L11 7L9 9L6 6L2 10L4 12L6 10L8 12L10 10L11 11L12 12Z',
        title: 'Excelente Progresso!',
        description: 'Você manteve uma sequência de estudos por 15 dias consecutivos.'
      },
      {
        id: 2,
        type: 'warning',
        icon: 'M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2M13 17H11V15H13V17M13 13H11V7H13V13Z',
        title: 'Atenção em Física',
        description: 'Sua performance em Física caiu 8% na última semana.'
      },
      {
        id: 3,
        type: 'info',
        icon: 'M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2M13 17H11V11H13V17M13 9H11V7H13V9Z',
        title: 'Nova Meta Disponível',
        description: 'Complete 5 simulados esta semana para ganhar bônus.'
      }
    ])
    
    const subjects = ref([
      {
        id: 1,
        name: 'Matemática',
        icon: 'M12 2L2 7V9C2 16 12 21 12 21S22 16 22 9V7L12 2Z',
        color: '#3b82f6',
        progress: 85,
        average: 8.5,
        completedTopics: 32,
        totalTopics: 40
      },
      {
        id: 2,
        name: 'Física',
        icon: 'M12 2L15 9L22 10L17 15L18 22L12 18L6 22L7 15L2 10L9 9L12 2Z',
        color: '#8b5cf6',
        progress: 72,
        average: 7.2,
        completedTopics: 28,
        totalTopics: 38
      },
      {
        id: 3,
        name: 'Química',
        icon: 'M7 2V8H3V10H7V22H9V10H13V8H9V2H7Z',
        color: '#10b981',
        progress: 90,
        average: 9.0,
        completedTopics: 45,
        totalTopics: 50
      },
      {
        id: 4,
        name: 'Biologia',
        icon: 'M21 16V8C21 6.89 20.1 6 19 6H11L9 4H3C1.89 4 1 4.89 1 6V16C1 17.1 1.89 18 3 18H19C20.1 18 21 17.1 21 16Z',
        color: '#f59e0b',
        progress: 68,
        average: 6.8,
        completedTopics: 20,
        totalTopics: 30
      }
    ])
    
    const goals = ref([
      {
        id: 1,
        type: 'study',
        title: 'Estudar 100 horas este mês',
        description: 'Manter uma média de 3.5 horas por dia',
        current: 78,
        target: 100,
        unit: 'horas',
        deadline: new Date('2024-02-29'),
        status: 'in-progress'
      },
      {
        id: 2,
        type: 'tasks',
        title: 'Completar todos os exercícios',
        description: 'Resolver todos os exercícios do capítulo 5',
        current: 45,
        target: 45,
        unit: 'exercícios',
        deadline: new Date('2024-02-15'),
        status: 'completed'
      },
      {
        id: 3,
        type: 'grade',
        title: 'Média 8.0 em Física',
        description: 'Melhorar a média de 7.2 para 8.0',
        current: 7.2,
        target: 8.0,
        unit: 'pontos',
        deadline: new Date('2024-03-15'),
        status: 'at-risk'
      }
    ])
    
    const exportSections = ref([
      { id: 'overview', label: 'Visão Geral', included: true },
      { id: 'analytics', label: 'Gráficos de Performance', included: true },
      { id: 'subjects', label: 'Performance por Matéria', included: true },
      { id: 'goals', label: 'Metas e Objetivos', included: true },
      { id: 'activity', label: 'Mapa de Atividades', included: false }
    ])
    
    const newGoal = ref({
      title: '',
      type: 'study',
      target: null,
      unit: '',
      deadline: ''
    })
    
    // Heatmap data
    const heatmapMonths = ref(['Out', 'Nov', 'Dez', 'Jan', 'Fev'])
    const heatmapData = ref(generateHeatmapData())
    
    // Computed
    const today = computed(() => new Date().toISOString().split('T')[0])
    
    // Methods
    function generateHeatmapData() {
      const data = []
      for (let i = 0; i < 365; i++) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        data.push({
          date: date.toISOString().split('T')[0],
          level: Math.floor(Math.random() * 5),
          hours: Math.floor(Math.random() * 8)
        })
      }
      return data.reverse()
    }
    
    function initChart() {
      const ctx = performanceChart.value?.getContext('2d')
      if (!ctx) return
      
      chartInstance = new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
          datasets: [{
            label: 'Performance Score',
            data: [65, 72, 78, 75, 82, 85],
            borderColor: '#8b5cf6',
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              grid: {
                color: 'rgba(255, 255, 255, 0.05)'
              },
              ticks: {
                color: '#8b949e'
              }
            },
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: '#8b949e'
              }
            }
          }
        }
      })
    }
    
    function formatDeadline(date) {
      const days = Math.ceil((date - new Date()) / (1000 * 60 * 60 * 24))
      if (days < 0) return 'Expirado'
      if (days === 0) return 'Hoje'
      if (days === 1) return 'Amanhã'
      return `${days} dias`
    }
    
    function getGoalIcon(type) {
      const icons = {
        study: 'M12 2L2 7V9C2 16 12 21 12 21S22 16 22 9V7L12 2Z',
        tasks: 'M9 11L12 14L22 4M20 12V18C20 19 19 20 18 20H4C3 20 2 19 2 18V6C2 5 3 4 4 4H14',
        grade: 'M12 2L15 9L22 10L17 15L18 22L12 18L6 22L7 15L2 10L9 9L12 2Z',
        custom: 'M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2Z'
      }
      return icons[type] || icons.custom
    }
    
    function exportReport() {
      console.log('Exporting report as', exportFormat.value)
      console.log('Sections:', exportSections.value.filter(s => s.included))
      showExportModal.value = false
      // Implement actual export logic
    }
    
    function createGoal() {
      goals.value.push({
        id: Date.now(),
        ...newGoal.value,
        current: 0,
        deadline: new Date(newGoal.value.deadline),
        status: 'in-progress'
      })
      showGoalModal.value = false
      // Reset form
      newGoal.value = {
        title: '',
        type: 'study',
        target: null,
        unit: '',
        deadline: ''
      }
    }
    
    // Lifecycle
    onMounted(() => {
      initChart()
      
      // Handle responsive sidebar
      const handleResize = () => {
        if (window.innerWidth < 768) {
          sidebarCollapsed.value = true
        }
      }
      
      handleResize()
      window.addEventListener('resize', handleResize)
      
      // Cleanup
      onUnmounted(() => {
        window.removeEventListener('resize', handleResize)
        if (chartInstance) {
          chartInstance.destroy()
        }
      })
    })
    
    return {
      // Refs
      performanceChart,
      
      // State
      sidebarCollapsed,
      activeSection,
      showDatePicker,
      showExportModal,
      showGoalModal,
      showSettings,
      selectedMetric,
      selectedSubject,
      exportFormat,
      
      // Data
      currentDateRange,
      overallScore,
      overallTrend,
      completedTasks,
      taskProgress,
      studyHours,
      streakDays,
      sidebarItems,
      chartMetrics,
      performanceInsights,
      subjects,
      goals,
      exportSections,
      newGoal,
      heatmapMonths,
      heatmapData,
      
      // Computed
      today,
      
      // Methods
      formatDeadline,
      getGoalIcon,
      exportReport,
      createGoal
    }
  }
}
</script>

<style scoped>
/* Variables */
:root {
  --bg-primary: #0a0d13;
  --bg-secondary: #0f1419;
  --bg-tertiary: #1a1f29;
  --text-primary: #ffffff;
  --text-secondary: #8b949e;
  --text-tertiary: #6e7681;
  --border-color: rgba(255, 255, 255, 0.1);
  --accent-primary: #8b5cf6;
  --accent-secondary: #3b82f6;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-danger: #ef4444;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.5);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);
}

/* Base */
.performance-dashboard-v2 {
  display: flex;
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Inter', sans-serif;
}

/* Sidebar */
.sidebar {
  width: 240px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  position: sticky;
  top: 64px;
  height: calc(100vh - 64px);
  transition: width 0.3s ease;
  z-index: 10;
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-toggle {
  position: absolute;
  right: -12px;
  top: 24px;
  width: 24px;
  height: 24px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
}

.sidebar-toggle:hover {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  transform: scale(1.1);
}

.sidebar-nav {
  padding: 24px 0;
  flex: 1;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.nav-item:hover {
  color: var(--text-primary);
  background: rgba(139, 92, 246, 0.1);
}

.nav-item.active {
  color: var(--accent-primary);
  background: rgba(139, 92, 246, 0.15);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-label {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 1;
  transition: opacity 0.2s ease;
}

.sidebar.collapsed .nav-label {
  opacity: 0;
  pointer-events: none;
}

.nav-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--accent-primary);
  transform: translateX(-100%);
  transition: transform 0.2s ease;
}

.nav-item.active .nav-indicator {
  transform: translateX(0);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
}

.header-left {
  flex: 1;
}

.dashboard-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
  background: linear-gradient(135deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dashboard-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Date Range Picker */
.date-range-picker {
  position: relative;
}

.date-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.date-btn:hover {
  background: rgba(139, 92, 246, 0.1);
  border-color: var(--accent-primary);
}

/* Buttons */
.export-btn,
.settings-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--accent-primary);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.export-btn:hover,
.settings-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.settings-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  justify-content: center;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
}

.settings-btn:hover {
  background: rgba(139, 92, 246, 0.1);
  border-color: var(--accent-primary);
}

/* Overview Section */
.overview-section {
  margin-bottom: 32px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 20px;
}

.overview-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.overview-card.primary {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.1));
  border-color: var(--accent-primary);
}

.card-icon {
  width: 48px;
  height: 48px;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  color: var(--accent-primary);
}

.card-content {
  position: relative;
  z-index: 1;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 4px;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.card-unit {
  font-size: 18px;
  font-weight: 400;
  color: var(--text-secondary);
}

.card-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.card-subtitle {
  font-size: 12px;
  color: var(--text-tertiary);
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  margin-top: 12px;
}

.card-trend.up {
  color: var(--accent-success);
}

.card-trend.down {
  color: var(--accent-danger);
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-top: 12px;
}

.progress-fill {
  height: 100%;
  background: var(--accent-primary);
  border-radius: 3px;
  transition: width 0.5s ease;
}

/* Analytics Section */
.analytics-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.metric-btn {
  padding: 6px 16px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-secondary);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.metric-btn:hover {
  background: rgba(139, 92, 246, 0.1);
  color: var(--text-primary);
}

.metric-btn.active {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.chart-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  height: 300px;
  margin-bottom: 24px;
}

/* Insights */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.insight-card {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.insight-card:hover {
  border-color: var(--accent-primary);
  transform: translateX(4px);
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.insight-icon.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--accent-success);
}

.insight-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--accent-warning);
}

.insight-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--accent-secondary);
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.insight-text {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Subjects Section */
.subjects-section {
  margin-bottom: 32px;
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.subject-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.subject-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.subject-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.subject-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.subject-name {
  font-size: 18px;
  font-weight: 600;
}

.subject-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-tertiary);
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
}

.subject-progress {
  margin-bottom: 12px;
}

.subject-topics {
  font-size: 13px;
  color: var(--text-secondary);
}

/* Goals Section */
.goals-section {
  margin-bottom: 32px;
}

.add-goal-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--accent-primary);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-goal-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.goals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 24px;
}

.goal-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  position: relative;
  transition: all 0.3s ease;
}

.goal-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.goal-card.completed {
  border-color: var(--accent-success);
  background: rgba(16, 185, 129, 0.05);
}

.goal-card.at-risk {
  border-color: var(--accent-danger);
  background: rgba(239, 68, 68, 0.05);
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.goal-icon {
  width: 40px;
  height: 40px;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
}

.goal-badge {
  width: 32px;
  height: 32px;
  background: var(--accent-success);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.goal-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.goal-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 16px;
  line-height: 1.4;
}

.goal-progress {
  margin-bottom: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 13px;
  color: var(--text-secondary);
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: var(--accent-primary);
}

.goal-deadline {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: var(--text-tertiary);
}

/* Activity Heatmap */
.activity-section {
  margin-bottom: 32px;
}

.heatmap-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
}

.heatmap-months {
  display: flex;
  gap: 40px;
  margin-bottom: 12px;
  margin-left: 40px;
  font-size: 12px;
  color: var(--text-tertiary);
}

.heatmap-grid {
  display: flex;
  gap: 12px;
}

.heatmap-weekdays {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 10px;
  color: var(--text-tertiary);
  width: 28px;
}

.heatmap-weekdays span {
  height: 12px;
  display: flex;
  align-items: center;
}

.heatmap-cells {
  display: grid;
  grid-template-columns: repeat(52, 1fr);
  grid-template-rows: repeat(7, 1fr);
  gap: 4px;
  grid-auto-flow: column;
}

.heatmap-cell {
  width: 12px;
  height: 12px;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.heatmap-cell:hover {
  transform: scale(1.5);
  border: 1px solid var(--accent-primary);
}

.heatmap-cell.level-0 {
  background: rgba(139, 92, 246, 0.1);
}

.heatmap-cell.level-1 {
  background: rgba(139, 92, 246, 0.25);
}

.heatmap-cell.level-2 {
  background: rgba(139, 92, 246, 0.5);
}

.heatmap-cell.level-3 {
  background: rgba(139, 92, 246, 0.75);
}

.heatmap-cell.level-4 {
  background: var(--accent-primary);
}

.heatmap-legend {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
  font-size: 12px;
  color: var(--text-tertiary);
}

.legend-cells {
  display: flex;
  gap: 4px;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  width: 100%;
  max-width: 480px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 20px;
  font-weight: 600;
}

.modal-close {
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 24px;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* Export Options */
.export-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.export-option {
  position: relative;
  cursor: pointer;
}

.export-option input {
  position: absolute;
  opacity: 0;
}

.option-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.export-option input:checked + .option-content {
  border-color: var(--accent-primary);
  background: rgba(139, 92, 246, 0.1);
}

.export-sections h4 {
  font-size: 14px;
  margin-bottom: 12px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  cursor: pointer;
  font-size: 14px;
}

.checkbox-option input {
  width: 18px;
  height: 18px;
  accent-color: var(--accent-primary);
}

/* Form */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-secondary);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 16px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

/* Buttons */
.btn-primary,
.btn-secondary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--accent-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--accent-primary);
}

/* Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Responsive */
@media (max-width: 1024px) {
  .sidebar {
    position: fixed;
    top: 0;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 100;
  }
  
  .sidebar:not(.collapsed) {
    transform: translateX(0);
  }
  
  .sidebar-toggle {
    position: fixed;
    left: 20px;
    right: auto;
    z-index: 101;
  }
  
  .main-content {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .subjects-grid {
    grid-template-columns: 1fr;
  }
  
  .goals-grid {
    grid-template-columns: 1fr;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
  }
  
  .heatmap-cells {
    grid-template-columns: repeat(26, 1fr);
  }
  
  .heatmap-months {
    gap: 20px;
  }
}
</style>