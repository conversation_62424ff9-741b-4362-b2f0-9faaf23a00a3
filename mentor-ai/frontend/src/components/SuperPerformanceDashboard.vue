<template>
  <div class="super-performance-dashboard">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-mesh"></div>
      <div class="floating-orbs">
        <div v-for="i in 5" :key="`orb-${i}`" class="orb" :style="getOrbStyle(i)"></div>
      </div>
      <canvas ref="particlesCanvas" class="particles-canvas"></canvas>
    </div>

    <!-- Main Header -->
    <header class="dashboard-header">
      <div class="header-container">
        <div class="header-left">
          <div class="header-icon-wrapper">
            <div class="icon-glow"></div>
            <font-awesome-icon icon="chart-line" class="header-icon" />
          </div>
          <div class="header-content">
            <h1 class="header-title">
              Central de <span class="highlight">Performance</span>
            </h1>
            <p class="header-subtitle">
              Análise completa e inteligente do seu progresso
            </p>
          </div>
        </div>

        <div class="header-right">
          <!-- Time Period Selector -->
          <div class="period-selector">
            <button
              v-for="period in timePeriods"
              :key="period.value"
              :class="['period-btn', { active: selectedPeriod === period.value }]"
              @click="changePeriod(period.value)"
            >
              <font-awesome-icon :icon="period.icon" />
              <span>{{ period.label }}</span>
            </button>
          </div>

          <!-- Quick Actions -->
          <div class="quick-actions">
            <button @click="refreshData" class="action-btn refresh" :class="{ spinning: isRefreshing }">
              <font-awesome-icon icon="sync-alt" />
            </button>
            <button @click="openExportModal" class="action-btn export">
              <font-awesome-icon icon="download" />
              <span>Exportar</span>
            </button>
            <button @click="openSettingsModal" class="action-btn settings">
              <font-awesome-icon icon="cog" />
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-content">
      <!-- Key Metrics Overview -->
      <section class="metrics-overview">
        <div class="metrics-grid">
          <div
            v-for="metric in keyMetrics"
            :key="metric.id"
            class="metric-card"
            :class="`metric-${metric.type}`"
            @click="metric.clickable && showMetricDetails(metric)"
          >
            <div class="metric-header">
              <div class="metric-icon">
                <font-awesome-icon :icon="metric.icon" />
              </div>
              <div class="metric-trend" v-if="metric.trend">
                <font-awesome-icon
                  :icon="metric.trend > 0 ? 'arrow-up' : 'arrow-down'"
                  :class="metric.trend > 0 ? 'trend-up' : 'trend-down'"
                />
                <span>{{ Math.abs(metric.trend) }}%</span>
              </div>
            </div>
            <div class="metric-body">
              <h3 class="metric-value">{{ formatMetricValue(metric) }}</h3>
              <p class="metric-label">{{ metric.label }}</p>
              <div class="metric-progress" v-if="metric.progress !== undefined">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{ width: `${metric.progress}%` }"
                  ></div>
                </div>
                <span class="progress-text">{{ metric.progress }}%</span>
              </div>
            </div>
            <div class="metric-sparkline" v-if="metric.sparkline">
              <svg viewBox="0 0 100 40" class="sparkline-svg">
                <polyline
                  :points="getSparklinePoints(metric.sparkline)"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                />
              </svg>
            </div>
          </div>
        </div>
      </section>

      <!-- Performance Charts -->
      <section class="charts-section">
        <div class="section-header">
          <h2 class="section-title">
            <font-awesome-icon icon="chart-area" />
            Análise Detalhada
          </h2>
          <div class="chart-controls">
            <select v-model="selectedChartType" class="chart-selector">
              <option value="progress">Progresso Geral</option>
              <option value="retention">Taxa de Retenção</option>
              <option value="categories">Por Categoria</option>
              <option value="heatmap">Mapa de Calor</option>
            </select>
          </div>
        </div>

        <div class="charts-container">
          <div class="main-chart-wrapper">
            <canvas ref="mainChart" class="main-chart"></canvas>
          </div>
          <div class="side-charts">
            <div class="mini-chart-card">
              <h4>Distribuição por Dificuldade</h4>
              <canvas ref="difficultyChart"></canvas>
            </div>
            <div class="mini-chart-card">
              <h4>Tempo de Resposta</h4>
              <canvas ref="responseTimeChart"></canvas>
            </div>
          </div>
        </div>
      </section>

      <!-- AI Insights -->
      <section class="insights-section">
        <div class="section-header">
          <h2 class="section-title">
            <font-awesome-icon icon="brain" />
            Insights Inteligentes
          </h2>
          <button @click="generateNewInsights" class="generate-btn">
            <font-awesome-icon icon="magic" />
            Gerar Novos Insights
          </button>
        </div>

        <div class="insights-grid">
          <div
            v-for="insight in aiInsights"
            :key="insight.id"
            class="insight-card"
            :class="`insight-${insight.type}`"
          >
            <div class="insight-icon">
              <font-awesome-icon :icon="insight.icon" />
            </div>
            <div class="insight-content">
              <h4 class="insight-title">{{ insight.title }}</h4>
              <p class="insight-description">{{ insight.description }}</p>
              <div class="insight-actions" v-if="insight.actions">
                <button
                  v-for="action in insight.actions"
                  :key="action.id"
                  @click="executeInsightAction(action)"
                  class="insight-action-btn"
                >
                  {{ action.label }}
                </button>
              </div>
            </div>
            <div class="insight-impact" v-if="insight.impact">
              <span class="impact-label">Impacto:</span>
              <div class="impact-stars">
                <font-awesome-icon
                  v-for="i in 5"
                  :key="`star-${i}`"
                  icon="star"
                  :class="{ filled: i <= insight.impact }"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Goals & Achievements -->
      <section class="goals-achievements">
        <div class="goals-section">
          <div class="section-header">
            <h2 class="section-title">
              <font-awesome-icon icon="bullseye" />
              Metas Ativas
            </h2>
            <button @click="openGoalsModal" class="add-goal-btn">
              <font-awesome-icon icon="plus" />
              Nova Meta
            </button>
          </div>

          <div class="goals-list">
            <div
              v-for="goal in activeGoals"
              :key="goal.id"
              class="goal-card"
              :class="{ 'near-deadline': isNearDeadline(goal) }"
            >
              <div class="goal-header">
                <div class="goal-icon" :style="{ backgroundColor: goal.color }">
                  <font-awesome-icon :icon="goal.icon" />
                </div>
                <div class="goal-info">
                  <h4 class="goal-title">{{ goal.title }}</h4>
                  <p class="goal-description">{{ goal.description }}</p>
                </div>
              </div>
              <div class="goal-progress">
                <div class="progress-info">
                  <span class="progress-current">{{ goal.current }}</span>
                  <span class="progress-separator">/</span>
                  <span class="progress-target">{{ goal.target }}</span>
                  <span class="progress-unit">{{ goal.unit }}</span>
                </div>
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{
                      width: `${(goal.current / goal.target) * 100}%`,
                      backgroundColor: goal.color
                    }"
                  ></div>
                </div>
                <div class="goal-deadline">
                  <font-awesome-icon icon="clock" />
                  {{ formatDeadline(goal.deadline) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="achievements-section">
          <div class="section-header">
            <h2 class="section-title">
              <font-awesome-icon icon="trophy" />
              Conquistas
            </h2>
            <div class="achievement-stats">
              <span class="total-points">{{ totalAchievementPoints }} pts</span>
            </div>
          </div>

          <div class="achievements-showcase">
            <div
              v-for="achievement in recentAchievements"
              :key="achievement.id"
              class="achievement-card"
              :class="`rarity-${achievement.rarity}`"
            >
              <div class="achievement-glow"></div>
              <div class="achievement-icon">
                <font-awesome-icon :icon="achievement.icon" />
              </div>
              <div class="achievement-details">
                <h5 class="achievement-name">{{ achievement.name }}</h5>
                <p class="achievement-description">{{ achievement.description }}</p>
                <div class="achievement-meta">
                  <span class="achievement-date">
                    {{ formatDate(achievement.unlockedDate) }}
                  </span>
                  <span class="achievement-points">+{{ achievement.points }}pts</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Category Performance -->
      <section class="category-performance">
        <div class="section-header">
          <h2 class="section-title">
            <font-awesome-icon icon="th-large" />
            Performance por Categoria
          </h2>
        </div>

        <div class="categories-grid">
          <div
            v-for="category in categoryPerformance"
            :key="category.id"
            class="category-card"
            @click="showCategoryDetails(category)"
          >
            <div class="category-header">
              <div class="category-icon">
                <font-awesome-icon :icon="category.icon" />
              </div>
              <h4 class="category-name">{{ category.name }}</h4>
            </div>
            <div class="category-stats">
              <div class="stat-item">
                <span class="stat-value">{{ category.accuracy }}%</span>
                <span class="stat-label">Precisão</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ category.totalCards }}</span>
                <span class="stat-label">Cards</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ category.avgTime }}s</span>
                <span class="stat-label">Tempo Médio</span>
              </div>
            </div>
            <div class="category-chart">
              <canvas :ref="`categoryChart${category.id}`"></canvas>
            </div>
            <div class="category-recommendation" v-if="category.recommendation">
              <font-awesome-icon icon="lightbulb" />
              <span>{{ category.recommendation }}</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Study Patterns -->
      <section class="study-patterns">
        <div class="section-header">
          <h2 class="section-title">
            <font-awesome-icon icon="fingerprint" />
            Padrões de Estudo
          </h2>
        </div>

        <div class="patterns-container">
          <div class="pattern-card time-distribution">
            <h4>Distribuição de Tempo</h4>
            <canvas ref="timeDistributionChart"></canvas>
          </div>
          <div class="pattern-card peak-hours">
            <h4>Horários de Pico</h4>
            <div class="peak-hours-grid">
              <div
                v-for="hour in peakHours"
                :key="hour.hour"
                class="hour-block"
                :style="{
                  backgroundColor: getHeatmapColor(hour.intensity),
                  height: `${hour.intensity}%`
                }"
              >
                <span class="hour-label">{{ hour.label }}</span>
              </div>
            </div>
          </div>
          <div class="pattern-card streak-calendar">
            <h4>Calendário de Sequências</h4>
            <div class="calendar-grid">
              <div
                v-for="day in streakCalendar"
                :key="day.date"
                class="calendar-day"
                :class="{
                  studied: day.studied,
                  'current-streak': day.inCurrentStreak,
                  today: day.isToday
                }"
              >
                <span class="day-number">{{ day.day }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Export Modal -->
    <Teleport to="body">
      <Transition name="modal">
        <div v-if="showExportModal" class="modal-overlay" @click="closeExportModal">
          <div class="modal-container" @click.stop>
            <div class="modal-header">
              <h3>Exportar Relatório de Performance</h3>
              <button @click="closeExportModal" class="modal-close">
                <font-awesome-icon icon="times" />
              </button>
            </div>
            <div class="modal-body">
              <div class="export-options">
                <label class="export-option">
                  <input type="radio" v-model="exportFormat" value="pdf" />
                  <div class="option-content">
                    <font-awesome-icon icon="file-pdf" />
                    <span>PDF Completo</span>
                  </div>
                </label>
                <label class="export-option">
                  <input type="radio" v-model="exportFormat" value="excel" />
                  <div class="option-content">
                    <font-awesome-icon icon="file-excel" />
                    <span>Excel Detalhado</span>
                  </div>
                </label>
                <label class="export-option">
                  <input type="radio" v-model="exportFormat" value="json" />
                  <div class="option-content">
                    <font-awesome-icon icon="file-code" />
                    <span>JSON (Dados Brutos)</span>
                  </div>
                </label>
              </div>
              <div class="export-period">
                <h4>Período do Relatório</h4>
                <div class="period-inputs">
                  <div class="date-input">
                    <label>Data Inicial</label>
                    <input type="date" v-model="exportStartDate" />
                  </div>
                  <div class="date-input">
                    <label>Data Final</label>
                    <input type="date" v-model="exportEndDate" />
                  </div>
                </div>
              </div>
              <div class="export-sections">
                <h4>Seções a Incluir</h4>
                <label class="checkbox-option">
                  <input type="checkbox" v-model="exportSections.metrics" />
                  <span>Métricas Gerais</span>
                </label>
                <label class="checkbox-option">
                  <input type="checkbox" v-model="exportSections.charts" />
                  <span>Gráficos e Visualizações</span>
                </label>
                <label class="checkbox-option">
                  <input type="checkbox" v-model="exportSections.insights" />
                  <span>Insights de IA</span>
                </label>
                <label class="checkbox-option">
                  <input type="checkbox" v-model="exportSections.goals" />
                  <span>Metas e Conquistas</span>
                </label>
              </div>
            </div>
            <div class="modal-footer">
              <button @click="closeExportModal" class="btn-secondary">Cancelar</button>
              <button @click="exportReport" class="btn-primary">
                <font-awesome-icon icon="download" />
                Exportar
              </button>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>

    <!-- Goals Modal -->
    <Teleport to="body">
      <Transition name="modal">
        <div v-if="showGoalsModal" class="modal-overlay" @click="closeGoalsModal">
          <div class="modal-container" @click.stop>
            <div class="modal-header">
              <h3>Criar Nova Meta</h3>
              <button @click="closeGoalsModal" class="modal-close">
                <font-awesome-icon icon="times" />
              </button>
            </div>
            <div class="modal-body">
              <form @submit.prevent="createGoal" class="goal-form">
                <div class="form-group">
                  <label>Título da Meta</label>
                  <input
                    type="text"
                    v-model="newGoal.title"
                    placeholder="Ex: Dominar Anatomia"
                    required
                  />
                </div>
                <div class="form-group">
                  <label>Descrição</label>
                  <textarea
                    v-model="newGoal.description"
                    placeholder="Descreva sua meta..."
                    rows="3"
                  ></textarea>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label>Tipo de Meta</label>
                    <select v-model="newGoal.type" required>
                      <option value="cards">Número de Cards</option>
                      <option value="accuracy">Taxa de Acerto</option>
                      <option value="time">Tempo de Estudo</option>
                      <option value="streak">Dias Consecutivos</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label>Valor Alvo</label>
                    <input
                      type="number"
                      v-model="newGoal.target"
                      placeholder="100"
                      required
                    />
                  </div>
                </div>
                <div class="form-group">
                  <label>Prazo</label>
                  <input
                    type="date"
                    v-model="newGoal.deadline"
                    :min="minDate"
                    required
                  />
                </div>
                <div class="form-group">
                  <label>Cor e Ícone</label>
                  <div class="icon-color-picker">
                    <div
                      v-for="option in goalOptions"
                      :key="option.id"
                      class="option-item"
                      :class="{ selected: newGoal.icon === option.icon }"
                      @click="selectGoalOption(option)"
                      :style="{ backgroundColor: option.color }"
                    >
                      <font-awesome-icon :icon="option.icon" />
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button @click="closeGoalsModal" class="btn-secondary">Cancelar</button>
              <button @click="createGoal" class="btn-primary">
                <font-awesome-icon icon="plus" />
                Criar Meta
              </button>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>

    <!-- Settings Modal -->
    <Teleport to="body">
      <Transition name="modal">
        <div v-if="showSettingsModal" class="modal-overlay" @click="closeSettingsModal">
          <div class="modal-container" @click.stop>
            <div class="modal-header">
              <h3>Configurações do Dashboard</h3>
              <button @click="closeSettingsModal" class="modal-close">
                <font-awesome-icon icon="times" />
              </button>
            </div>
            <div class="modal-body">
              <div class="settings-section">
                <h4>Visualização</h4>
                <label class="toggle-option">
                  <span>Mostrar Animações</span>
                  <input type="checkbox" v-model="settings.animations" />
                  <div class="toggle-slider"></div>
                </label>
                <label class="toggle-option">
                  <span>Modo Compacto</span>
                  <input type="checkbox" v-model="settings.compactMode" />
                  <div class="toggle-slider"></div>
                </label>
                <label class="toggle-option">
                  <span>Atualização Automática</span>
                  <input type="checkbox" v-model="settings.autoRefresh" />
                  <div class="toggle-slider"></div>
                </label>
              </div>
              <div class="settings-section">
                <h4>Notificações</h4>
                <label class="toggle-option">
                  <span>Alertas de Meta</span>
                  <input type="checkbox" v-model="settings.goalAlerts" />
                  <div class="toggle-slider"></div>
                </label>
                <label class="toggle-option">
                  <span>Insights Diários</span>
                  <input type="checkbox" v-model="settings.dailyInsights" />
                  <div class="toggle-slider"></div>
                </label>
              </div>
              <div class="settings-section">
                <h4>Dados</h4>
                <div class="data-actions">
                  <button @click="clearCache" class="btn-secondary">
                    <font-awesome-icon icon="trash" />
                    Limpar Cache
                  </button>
                  <button @click="resetDashboard" class="btn-danger">
                    <font-awesome-icon icon="redo" />
                    Resetar Dashboard
                  </button>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button @click="closeSettingsModal" class="btn-primary">
                Salvar Configurações
              </button>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>

    <!-- Loading Overlay -->
    <Transition name="fade">
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <font-awesome-icon icon="chart-line" class="spinner-icon" />
        </div>
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </Transition>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import Chart from 'chart.js/auto'
import { format, differenceInDays, addDays, startOfMonth, endOfMonth } from 'date-fns'
import { ptBR } from 'date-fns/locale'

export default {
  name: 'SuperPerformanceDashboard',

  data() {
    return {
      selectedPeriod: 'week',
      selectedChartType: 'progress',
      showExportModal: false,
      showGoalsModal: false,
      showSettingsModal: false,
      exportFormat: 'pdf',
      exportStartDate: '',
      exportEndDate: '',
      exportSections: {
        metrics: true,
        charts: true,
        insights: true,
        goals: true
      },
      newGoal: {
        title: '',
        description: '',
        type: 'cards',
        target: 100,
        deadline: '',
        icon: 'bullseye',
        color: '#6366f1'
      },
      settings: {
        animations: true,
        compactMode: false,
        autoRefresh: true,
        goalAlerts: true,
        dailyInsights: true
      },
      isRefreshing: false,
      loadingText: 'Carregando dados...',
      charts: {},
      timePeriods: [
        { value: 'today', label: 'Hoje', icon: 'calendar-day' },
        { value: 'week', label: 'Semana', icon: 'calendar-week' },
        { value: 'month', label: 'Mês', icon: 'calendar-alt' },
        { value: 'year', label: 'Ano', icon: 'calendar' }
      ],
      goalOptions: [
        { id: 1, icon: 'bullseye', color: '#6366f1' },
        { id: 2, icon: 'trophy', color: '#f59e0b' },
        { id: 3, icon: 'fire', color: '#ef4444' },
        { id: 4, icon: 'star', color: '#8b5cf6' },
        { id: 5, icon: 'rocket', color: '#10b981' },
        { id: 6, icon: 'brain', color: '#ec4899' }
      ],
      particlesAnimation: null
    }
  },

  computed: {
    ...mapState('performance', [
      'performanceMetrics',
      'retentionRates',
      'categoryAnalytics',
      'studySessions',
      'reviewHistory',
      'userRanking',
      'studyGoals',
      'achievements',
      'aiInsights',
      'recommendations',
      'chartData',
      'loading'
    ]),
    ...mapGetters('performance', [
      'masteryLevel',
      'studyTimeFormatted',
      'performanceTrend',
      'weakCategories',
      'activeGoals',
      'recentAchievements'
    ]),

    keyMetrics() {
      return [
        {
          id: 'mastery',
          type: 'primary',
          icon: 'graduation-cap',
          label: 'Nível de Domínio',
          value: this.masteryLevel,
          unit: '%',
          progress: this.masteryLevel,
          trend: this.calculateTrend('mastery'),
          sparkline: this.getSparklineData('mastery'),
          clickable: true
        },
        {
          id: 'studyTime',
          type: 'success',
          icon: 'clock',
          label: 'Tempo Total de Estudo',
          value: this.studyTimeFormatted,
          trend: this.calculateTrend('studyTime'),
          sparkline: this.getSparklineData('studyTime')
        },
        {
          id: 'streak',
          type: 'warning',
          icon: 'fire',
          label: 'Sequência Atual',
          value: this.performanceMetrics.currentStreak,
          unit: ' dias',
          trend: null,
          progress: (this.performanceMetrics.currentStreak / 30) * 100
        },
        {
          id: 'accuracy',
          type: 'info',
          icon: 'bullseye',
          label: 'Taxa de Acerto',
          value: this.performanceMetrics.averageAccuracy,
          unit: '%',
          trend: this.calculateTrend('accuracy'),
          sparkline: this.getSparklineData('accuracy')
        },
        {
          id: 'cards',
          type: 'secondary',
          icon: 'layer-group',
          label: 'Cards Dominados',
          value: this.performanceMetrics.masteredCards,
          subvalue: `de ${this.performanceMetrics.totalCards}`,
          progress: this.masteryLevel
        },
        {
          id: 'ranking',
          type: 'purple',
          icon: 'medal',
          label: 'Ranking',
          value: `#${this.userRanking.position}`,
          subvalue: `Top ${this.userRanking.percentile}%`,
          clickable: true
        }
      ]
    },

    categoryPerformance() {
      return this.categoryAnalytics.map(cat => ({
        ...cat,
        icon: this.getCategoryIcon(cat.name),
        recommendation: this.getCategoryRecommendation(cat)
      }))
    },

    peakHours() {
      const hours = []
      for (let i = 0; i < 24; i++) {
        const intensity = this.calculateHourIntensity(i)
        hours.push({
          hour: i,
          label: `${i}h`,
          intensity
        })
      }
      return hours
    },

    streakCalendar() {
      const days = []
      const today = new Date()
      const startDate = startOfMonth(today)
      const endDate = endOfMonth(today)
      
      let currentDate = startDate
      while (currentDate <= endDate) {
        days.push({
          date: currentDate.toISOString(),
          day: currentDate.getDate(),
          studied: this.wasStudiedOn(currentDate),
          inCurrentStreak: this.isInCurrentStreak(currentDate),
          isToday: this.isToday(currentDate)
        })
        currentDate = addDays(currentDate, 1)
      }
      
      return days
    },

    totalAchievementPoints() {
      return this.achievements
        .filter(a => a.unlocked)
        .reduce((total, a) => total + a.points, 0)
    },

    isLoading() {
      return Object.values(this.loading).some(v => v)
    },

    minDate() {
      return new Date().toISOString().split('T')[0]
    }
  },

  methods: {
    ...mapActions('performance', [
      'fetchAnalytics',
      'fetchCategoryAnalytics',
      'fetchReviewHistory',
      'fetchGoals',
      'createGoal',
      'fetchAchievements',
      'generateInsights',
      'exportReport'
    ]),

    async changePeriod(period) {
      this.selectedPeriod = period
      await this.refreshData()
    },

    async refreshData() {
      this.isRefreshing = true
      this.loadingText = 'Atualizando dados...'
      
      try {
        await Promise.all([
          this.fetchAnalytics(),
          this.fetchCategoryAnalytics(),
          this.fetchReviewHistory(),
          this.fetchGoals(),
          this.fetchAchievements()
        ])
        
        this.updateCharts()
        this.$nextTick(() => {
          this.initParticlesAnimation()
        })
      } catch (error) {
        console.error('Erro ao atualizar dados:', error)
      } finally {
        this.isRefreshing = false
      }
    },

    formatMetricValue(metric) {
      if (metric.type === 'time') {
        return metric.value
      }
      return metric.value + (metric.unit || '')
    },

    calculateTrend(metricType) {
      // Simulação de cálculo de tendência
      const trends = {
        mastery: 5.2,
        studyTime: -2.1,
        accuracy: 3.7
      }
      return trends[metricType] || 0
    },

    getSparklineData(metricType) {
      // Simulação de dados para sparkline
      return [20, 35, 40, 30, 45, 50, 40, 55, 60, 45, 65, 70]
    },

    getSparklinePoints(data) {
      const width = 100
      const height = 40
      const max = Math.max(...data)
      const min = Math.min(...data)
      const range = max - min || 1
      
      return data.map((value, index) => {
        const x = (index / (data.length - 1)) * width
        const y = height - ((value - min) / range) * height
        return `${x},${y}`
      }).join(' ')
    },

    getCategoryIcon(categoryName) {
      const icons = {
        'Anatomia': 'bone',
        'Fisiologia': 'heartbeat',
        'Farmacologia': 'pills',
        'Patologia': 'virus',
        'Clínica Médica': 'stethoscope',
        'Cirurgia': 'cut'
      }
      return icons[categoryName] || 'book-medical'
    },

    getCategoryRecommendation(category) {
      if (category.accuracy < 60) {
        return 'Reforce os conceitos básicos desta categoria'
      }
      if (category.accuracy < 80) {
        return 'Continue praticando para melhorar a precisão'
      }
      return 'Excelente desempenho! Mantenha o ritmo'
    },

    calculateHourIntensity(hour) {
      // Simulação de intensidade de estudo por hora
      const peakHours = [9, 14, 20]
      const distance = Math.min(...peakHours.map(peak => Math.abs(hour - peak)))
      return Math.max(0, 100 - distance * 15)
    },

    getHeatmapColor(intensity) {
      const hue = 200 - (intensity * 1.2)
      const lightness = 50 + (intensity * 0.3)
      return `hsl(${hue}, 70%, ${lightness}%)`
    },

    wasStudiedOn(date) {
      // Verificar se estudou em determinada data
      return Math.random() > 0.3 // Simulação
    },

    isInCurrentStreak(date) {
      // Verificar se a data faz parte da sequência atual
      const today = new Date()
      const daysDiff = differenceInDays(today, date)
      return daysDiff < this.performanceMetrics.currentStreak
    },

    isToday(date) {
      const today = new Date()
      return date.toDateString() === today.toDateString()
    },

    isNearDeadline(goal) {
      const daysUntilDeadline = differenceInDays(new Date(goal.deadline), new Date())
      return daysUntilDeadline <= 3
    },

    formatDeadline(deadline) {
      const days = differenceInDays(new Date(deadline), new Date())
      if (days === 0) return 'Hoje'
      if (days === 1) return 'Amanhã'
      if (days < 7) return `${days} dias`
      return format(new Date(deadline), 'dd/MM', { locale: ptBR })
    },

    formatDate(date) {
      return format(new Date(date), 'dd/MM/yyyy', { locale: ptBR })
    },

    showMetricDetails(metric) {
      console.log('Mostrando detalhes da métrica:', metric)
      // Implementar modal de detalhes
    },

    showCategoryDetails(category) {
      console.log('Mostrando detalhes da categoria:', category)
      // Implementar modal de detalhes da categoria
    },

    async generateNewInsights() {
      this.loadingText = 'Gerando insights com IA...'
      await this.generateInsights()
    },

    executeInsightAction(action) {
      console.log('Executando ação do insight:', action)
      // Implementar ações dos insights
    },

    selectGoalOption(option) {
      this.newGoal.icon = option.icon
      this.newGoal.color = option.color
    },

    async createGoal() {
      if (!this.newGoal.title || !this.newGoal.target || !this.newGoal.deadline) {
        return
      }
      
      await this.$store.dispatch('performance/createGoal', {
        ...this.newGoal,
        current: 0,
        unit: this.getUnitForGoalType(this.newGoal.type)
      })
      
      this.closeGoalsModal()
      this.resetGoalForm()
    },

    getUnitForGoalType(type) {
      const units = {
        cards: 'cards',
        accuracy: '%',
        time: 'horas',
        streak: 'dias'
      }
      return units[type] || ''
    },

    resetGoalForm() {
      this.newGoal = {
        title: '',
        description: '',
        type: 'cards',
        target: 100,
        deadline: '',
        icon: 'bullseye',
        color: '#6366f1'
      }
    },

    openExportModal() {
      this.showExportModal = true
      this.exportStartDate = startOfMonth(new Date()).toISOString().split('T')[0]
      this.exportEndDate = new Date().toISOString().split('T')[0]
    },

    closeExportModal() {
      this.showExportModal = false
    },

    openGoalsModal() {
      this.showGoalsModal = true
    },

    closeGoalsModal() {
      this.showGoalsModal = false
    },

    openSettingsModal() {
      this.showSettingsModal = true
    },

    closeSettingsModal() {
      this.showSettingsModal = false
      this.saveSettings()
    },

    saveSettings() {
      localStorage.setItem('dashboardSettings', JSON.stringify(this.settings))
      this.applySettings()
    },

    loadSettings() {
      const saved = localStorage.getItem('dashboardSettings')
      if (saved) {
        this.settings = { ...this.settings, ...JSON.parse(saved) }
        this.applySettings()
      }
    },

    applySettings() {
      if (this.settings.autoRefresh) {
        this.startAutoRefresh()
      } else {
        this.stopAutoRefresh()
      }
      
      document.documentElement.classList.toggle('no-animations', !this.settings.animations)
      document.documentElement.classList.toggle('compact-mode', this.settings.compactMode)
    },

    startAutoRefresh() {
      this.autoRefreshInterval = setInterval(() => {
        this.refreshData()
      }, 5 * 60 * 1000) // 5 minutos
    },

    stopAutoRefresh() {
      if (this.autoRefreshInterval) {
        clearInterval(this.autoRefreshInterval)
      }
    },

    clearCache() {
      localStorage.removeItem('performanceCache')
      this.refreshData()
    },

    resetDashboard() {
      if (confirm('Tem certeza que deseja resetar o dashboard? Isso removerá todas as configurações personalizadas.')) {
        localStorage.removeItem('dashboardSettings')
        localStorage.removeItem('performanceCache')
        this.loadSettings()
        this.refreshData()
      }
    },

    async exportReport() {
      this.loadingText = 'Gerando relatório...'
      
      const reportData = {
        format: this.exportFormat,
        startDate: this.exportStartDate,
        endDate: this.exportEndDate,
        sections: this.exportSections
      }
      
      await this.$store.dispatch('performance/exportReport', reportData)
      this.closeExportModal()
    },

    initCharts() {
      this.$nextTick(() => {
        this.initMainChart()
        this.initDifficultyChart()
        this.initResponseTimeChart()
        this.initTimeDistributionChart()
        this.initCategoryCharts()
      })
    },

    initMainChart() {
      const ctx = this.$refs.mainChart?.getContext('2d')
      if (!ctx) return

      this.charts.main = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.getChartLabels(),
          datasets: [{
            label: 'Taxa de Acerto',
            data: this.chartData.progress || [],
            borderColor: '#6366f1',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100
            }
          }
        }
      })
    },

    initDifficultyChart() {
      const ctx = this.$refs.difficultyChart?.getContext('2d')
      if (!ctx) return

      this.charts.difficulty = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['Fácil', 'Médio', 'Difícil'],
          datasets: [{
            data: [45, 35, 20],
            backgroundColor: ['#10b981', '#f59e0b', '#ef4444']
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      })
    },

    initResponseTimeChart() {
      const ctx = this.$refs.responseTimeChart?.getContext('2d')
      if (!ctx) return

      this.charts.responseTime = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['0-5s', '5-10s', '10-15s', '15-20s', '20s+'],
          datasets: [{
            label: 'Respostas',
            data: [150, 200, 100, 50, 20],
            backgroundColor: '#8b5cf6'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          }
        }
      })
    },

    initTimeDistributionChart() {
      const ctx = this.$refs.timeDistributionChart?.getContext('2d')
      if (!ctx) return

      this.charts.timeDistribution = new Chart(ctx, {
        type: 'radar',
        data: {
          labels: ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo'],
          datasets: [{
            label: 'Tempo de Estudo',
            data: [65, 75, 80, 70, 85, 60, 40],
            borderColor: '#ec4899',
            backgroundColor: 'rgba(236, 72, 153, 0.1)'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      })
    },

    initCategoryCharts() {
      this.categoryPerformance.forEach(category => {
        const canvas = this.$refs[`categoryChart${category.id}`]?.[0]
        if (!canvas) return

        const ctx = canvas.getContext('2d')
        new Chart(ctx, {
          type: 'line',
          data: {
            labels: ['S1', 'S2', 'S3', 'S4'],
            datasets: [{
              data: category.weeklyProgress || [70, 75, 72, 80],
              borderColor: '#6366f1',
              borderWidth: 2,
              pointRadius: 0,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: { display: false }
            },
            scales: {
              x: { display: false },
              y: { display: false }
            }
          }
        })
      })
    },

    updateCharts() {
      Object.values(this.charts).forEach(chart => {
        if (chart && chart.update) {
          chart.update()
        }
      })
    },

    getChartLabels() {
      // Gerar labels baseados no período selecionado
      switch (this.selectedPeriod) {
        case 'today':
          return Array.from({ length: 24 }, (_, i) => `${i}h`)
        case 'week':
          return ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom']
        case 'month':
          return Array.from({ length: 30 }, (_, i) => i + 1)
        case 'year':
          return ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
        default:
          return []
      }
    },

    initParticlesAnimation() {
      const canvas = this.$refs.particlesCanvas
      if (!canvas) return

      const ctx = canvas.getContext('2d')
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight

      const particles = []
      const particleCount = 50

      class Particle {
        constructor() {
          this.x = Math.random() * canvas.width
          this.y = Math.random() * canvas.height
          this.size = Math.random() * 2 + 1
          this.speedX = Math.random() * 2 - 1
          this.speedY = Math.random() * 2 - 1
          this.opacity = Math.random() * 0.5 + 0.2
        }

        update() {
          this.x += this.speedX
          this.y += this.speedY

          if (this.x > canvas.width) this.x = 0
          if (this.x < 0) this.x = canvas.width
          if (this.y > canvas.height) this.y = 0
          if (this.y < 0) this.y = canvas.height
        }

        draw() {
          ctx.fillStyle = `rgba(99, 102, 241, ${this.opacity})`
          ctx.beginPath()
          ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
          ctx.fill()
        }
      }

      for (let i = 0; i < particleCount; i++) {
        particles.push(new Particle())
      }

      const animate = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        particles.forEach(particle => {
          particle.update()
          particle.draw()
        })
        this.particlesAnimation = requestAnimationFrame(animate)
      }

      if (this.settings.animations) {
        animate()
      }
    },

    getOrbStyle(index) {
      const positions = [
        { top: '10%', left: '5%', size: '300px' },
        { top: '60%', left: '80%', size: '200px' },
        { top: '30%', right: '10%', size: '250px' },
        { bottom: '20%', left: '15%', size: '180px' },
        { top: '50%', left: '50%', size: '400px' }
      ]
      
      const pos = positions[index - 1]
      return {
        ...pos,
        width: pos.size,
        height: pos.size,
        animationDelay: `${index * 2}s`,
        animationDuration: `${20 + index * 5}s`
      }
    },

    handleResize() {
      if (this.$refs.particlesCanvas) {
        this.$refs.particlesCanvas.width = window.innerWidth
        this.$refs.particlesCanvas.height = window.innerHeight
      }
    }
  },

  mounted() {
    this.loadSettings()
    this.refreshData()
    this.initCharts()
    
    window.addEventListener('resize', this.handleResize)
  },

  beforeUnmount() {
    // Limpar intervalos e animações
    this.stopAutoRefresh()
    
    if (this.particlesAnimation) {
      cancelAnimationFrame(this.particlesAnimation)
    }
    
    // Destruir gráficos
    Object.values(this.charts).forEach(chart => {
      if (chart && chart.destroy) {
        chart.destroy()
      }
    })
    
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style scoped>
/* Reset e Variáveis CSS */
.super-performance-dashboard {
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #818cf8;
  --secondary-color: #ec4899;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #3b82f6;
  
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --text-inverse: #ffffff;
  
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-dark: #111827;
  
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  
  --transition-fast: 150ms ease;
  --transition-base: 300ms ease;
  --transition-slow: 500ms ease;
  
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: var(--bg-secondary);
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.gradient-mesh {
  position: absolute;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(at 20% 30%, rgba(99, 102, 241, 0.1) 0px, transparent 50%),
    radial-gradient(at 80% 50%, rgba(236, 72, 153, 0.08) 0px, transparent 50%),
    radial-gradient(at 40% 80%, rgba(16, 185, 129, 0.06) 0px, transparent 50%);
}

.floating-orbs {
  position: absolute;
  width: 100%;
  height: 100%;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: float-orb 20s ease-in-out infinite;
}

.orb:nth-child(1) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.orb:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.orb:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.orb:nth-child(4) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.orb:nth-child(5) {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

@keyframes float-orb {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
}

.particles-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
}

/* Header */
.dashboard-header {
  position: relative;
  z-index: 10;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon-wrapper {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, var(--primary-light) 0%, transparent 70%);
  border-radius: 50%;
  filter: blur(20px);
  opacity: 0.5;
  animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

.header-icon {
  font-size: 2rem;
  color: var(--primary-color);
  position: relative;
  z-index: 1;
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
}

.header-content .highlight {
  color: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-subtitle {
  color: var(--text-secondary);
  margin: 0.25rem 0 0;
  font-size: 1rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

/* Period Selector */
.period-selector {
  display: flex;
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: 0.25rem;
  gap: 0.25rem;
}

.period-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  background: transparent;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-base);
}

.period-btn:hover {
  color: var(--text-primary);
  background: rgba(0, 0, 0, 0.05);
}

.period-btn.active {
  background: var(--bg-primary);
  color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-base);
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.action-btn.refresh {
  padding: 0.75rem;
  border-radius: 50%;
}

.action-btn.refresh.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.action-btn.export {
  background: var(--primary-color);
  color: var(--text-inverse);
  border-color: var(--primary-color);
}

.action-btn.export:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
}

.action-btn.settings {
  padding: 0.75rem;
}

/* Main Content */
.dashboard-content {
  position: relative;
  z-index: 5;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Metrics Overview */
.metrics-overview {
  margin-bottom: 3rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
  cursor: default;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: currentColor;
  opacity: 0.8;
}

.metric-card.metric-primary { color: var(--primary-color); }
.metric-card.metric-success { color: var(--success-color); }
.metric-card.metric-warning { color: var(--warning-color); }
.metric-card.metric-info { color: var(--info-color); }
.metric-card.metric-secondary { color: var(--text-secondary); }
.metric-card.metric-purple { color: #8b5cf6; }

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: currentColor;
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.metric-icon {
  width: 48px;
  height: 48px;
  background: currentColor;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--text-inverse);
  opacity: 0.9;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.trend-up {
  color: var(--success-color);
}

.trend-down {
  color: var(--danger-color);
}

.metric-body {
  margin-bottom: 1rem;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: 1;
}

.metric-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0.5rem 0 0;
}

.metric-progress {
  margin-top: 1rem;
}

.progress-bar {
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: currentColor;
  border-radius: 4px;
  transition: width var(--transition-slow);
}

.progress-text {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.metric-sparkline {
  height: 40px;
  margin-top: 1rem;
  opacity: 0.8;
}

.sparkline-svg {
  width: 100%;
  height: 100%;
}

/* Charts Section */
.charts-section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 1rem;
}

.chart-selector {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-base);
}

.chart-selector:hover {
  border-color: var(--primary-color);
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 1.5rem;
}

.main-chart-wrapper {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  min-height: 400px;
}

.main-chart {
  width: 100%;
  height: 100%;
}

.side-charts {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.mini-chart-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.mini-chart-card h4 {
  margin: 0 0 1rem;
  font-size: 1rem;
  color: var(--text-primary);
}

/* Insights Section */
.insights-section {
  margin-bottom: 3rem;
}

.generate-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-base);
}

.generate-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.insight-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: currentColor;
}

.insight-card.insight-success { color: var(--success-color); }
.insight-card.insight-warning { color: var(--warning-color); }
.insight-card.insight-danger { color: var(--danger-color); }
.insight-card.insight-info { color: var(--info-color); }

.insight-card:hover {
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.insight-icon {
  width: 40px;
  height: 40px;
  background: currentColor;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--text-inverse);
  margin-bottom: 1rem;
  opacity: 0.9;
}

.insight-content {
  margin-bottom: 1rem;
}

.insight-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.insight-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.6;
  margin: 0;
}

.insight-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.insight-action-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid currentColor;
  border-radius: var(--radius-sm);
  color: currentColor;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-base);
}

.insight-action-btn:hover {
  background: currentColor;
  color: var(--text-inverse);
}

.insight-impact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.impact-label {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.impact-stars {
  display: flex;
  gap: 0.25rem;
  color: var(--warning-color);
}

.impact-stars .fa-star {
  font-size: 0.875rem;
  opacity: 0.3;
}

.impact-stars .fa-star.filled {
  opacity: 1;
}

/* Goals & Achievements */
.goals-achievements {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.goals-section,
.achievements-section {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.add-goal-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-base);
}

.add-goal-btn:hover {
  background: var(--primary-dark);
}

.goals-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.goal-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 1rem;
  transition: all var(--transition-base);
}

.goal-card:hover {
  background: var(--bg-tertiary);
}

.goal-card.near-deadline {
  border: 1px solid var(--warning-color);
}

.goal-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.goal-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  flex-shrink: 0;
}

.goal-info {
  flex: 1;
}

.goal-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem;
}

.goal-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.goal-progress {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progress-info {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.progress-current {
  font-weight: 600;
  color: var(--text-primary);
}

.progress-separator {
  color: var(--text-muted);
}

.progress-target {
  color: var(--text-secondary);
}

.progress-unit {
  color: var(--text-muted);
  font-size: 0.75rem;
}

.goal-deadline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* Achievements */
.achievement-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.total-points {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--warning-color);
}

.achievements-showcase {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.achievement-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-base);
}

.achievement-card:hover {
  transform: translateX(4px);
}

.achievement-glow {
  position: absolute;
  top: 50%;
  left: -50px;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, currentColor, transparent);
  opacity: 0.1;
  transform: translateY(-50%);
}

.achievement-card.rarity-common { color: var(--text-secondary); }
.achievement-card.rarity-rare { color: var(--info-color); }
.achievement-card.rarity-epic { color: var(--secondary-color); }
.achievement-card.rarity-legendary { color: var(--warning-color); }

.achievement-icon {
  width: 48px;
  height: 48px;
  background: currentColor;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--text-inverse);
  flex-shrink: 0;
}

.achievement-details {
  flex: 1;
}

.achievement-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem;
}

.achievement-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem;
}

.achievement-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.75rem;
}

.achievement-date {
  color: var(--text-muted);
}

.achievement-points {
  font-weight: 600;
  color: currentColor;
}

/* Category Performance */
.category-performance {
  margin-bottom: 3rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-base);
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.category-icon {
  width: 48px;
  height: 48px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--primary-color);
}

.category-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.category-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

.category-chart {
  height: 60px;
  margin-bottom: 1rem;
}

.category-recommendation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Study Patterns */
.study-patterns {
  margin-bottom: 3rem;
}

.patterns-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.pattern-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.pattern-card h4 {
  margin: 0 0 1rem;
  font-size: 1.125rem;
  color: var(--text-primary);
}

.peak-hours-grid {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 150px;
  gap: 0.5rem;
}

.hour-block {
  flex: 1;
  background: var(--primary-color);
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  position: relative;
  min-height: 20px;
  transition: all var(--transition-base);
}

.hour-block:hover {
  transform: scaleY(1.1);
}

.hour-label {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: var(--text-muted);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.calendar-day {
  aspect-ratio: 1;
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: var(--text-muted);
  transition: all var(--transition-base);
}

.calendar-day.studied {
  background: var(--success-color);
  color: var(--text-inverse);
}

.calendar-day.current-streak {
  background: var(--warning-color);
  color: var(--text-inverse);
}

.calendar-day.today {
  border: 2px solid var(--primary-color);
}

/* Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-xl);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-primary);
}

.modal-close {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-base);
}

.modal-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
}

/* Form Elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all var(--transition-base);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Export Options */
.export-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.export-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-base);
}

.export-option:hover {
  border-color: var(--primary-color);
  background: var(--bg-secondary);
}

.export-option input[type="radio"] {
  width: auto;
  margin-right: 1rem;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.export-period,
.export-sections {
  margin-bottom: 2rem;
}

.export-period h4,
.export-sections h4 {
  margin: 0 0 1rem;
  font-size: 1rem;
  color: var(--text-primary);
}

.period-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.date-input {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-input label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  cursor: pointer;
}

.checkbox-option input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Icon Color Picker */
.icon-color-picker {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.option-item {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--text-inverse);
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
}

.option-item:hover {
  transform: scale(1.1);
}

.option-item.selected {
  box-shadow: 0 0 0 3px var(--bg-primary), 0 0 0 5px currentColor;
}

/* Settings */
.settings-section {
  margin-bottom: 2rem;
}

.settings-section h4 {
  margin: 0 0 1rem;
  font-size: 1rem;
  color: var(--text-primary);
}

.toggle-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  cursor: pointer;
}

.toggle-option span {
  font-size: 0.875rem;
  color: var(--text-primary);
}

.toggle-option input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  width: 48px;
  height: 24px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  position: relative;
  transition: all var(--transition-base);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background: var(--bg-primary);
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.toggle-option input[type="checkbox"]:checked + .toggle-slider {
  background: var(--primary-color);
}

.toggle-option input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(24px);
}

.data-actions {
  display: flex;
  gap: 1rem;
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-danger {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-base);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--text-inverse);
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background: var(--bg-secondary);
}

.btn-danger {
  background: var(--danger-color);
  color: var(--text-inverse);
}

.btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 80%;
  height: 80%;
  border-top-color: var(--secondary-color);
  animation-duration: 0.8s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 60%;
  height: 60%;
  border-top-color: var(--warning-color);
  animation-duration: 0.6s;
}

.spinner-icon {
  font-size: 2rem;
  color: var(--text-inverse);
}

.loading-text {
  margin-top: 1.5rem;
  font-size: 1rem;
  color: var(--text-inverse);
}

/* Animations */
.modal-enter-active,
.modal-leave-active {
  transition: opacity var(--transition-base);
}

.modal-enter-active .modal-container,
.modal-leave-active .modal-container {
  transition: transform var(--transition-base);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-container,
.modal-leave-to .modal-container {
  transform: scale(0.9);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-base);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .header-container,
  .dashboard-content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  
  .charts-container {
    grid-template-columns: 1fr;
  }
  
  .side-charts {
    flex-direction: row;
  }
  
  .goals-achievements {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .period-selector {
    flex-wrap: wrap;
  }
  
  .metrics-grid,
  .insights-grid,
  .categories-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .patterns-container {
    grid-template-columns: 1fr;
  }
  
  .achievement-card {
    flex-direction: column;
    text-align: center;
  }
  
  .modal-container {
    width: 95%;
    margin: 1rem;
  }
}

@media (max-width: 480px) {
  .header-content h1 {
    font-size: 1.5rem;
  }
  
  .quick-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .action-btn span {
    display: none;
  }
  
  .metric-value {
    font-size: 2rem;
  }
  
  .calendar-grid {
    gap: 0.25rem;
  }
  
  .calendar-day {
    font-size: 0.75rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .super-performance-dashboard {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --text-inverse: #111827;
    
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --bg-tertiary: #374151;
    
    --border-color: #374151;
  }
  
  .gradient-mesh {
    opacity: 0.5;
  }
  
  .orb {
    opacity: 0.3;
  }
}

/* Performance Optimizations */
.no-animations * {
  animation: none !important;
  transition: none !important;
}

.compact-mode .metric-card {
  padding: 1rem;
}

.compact-mode .metric-value {
  font-size: 2rem;
}

.compact-mode .section-header {
  margin-bottom: 1rem;
}

.compact-mode .charts-container,
.compact-mode .insights-grid,
.compact-mode .categories-grid {
  gap: 1rem;
}
</style>