<template>
  <footer class="app-footer-enhanced">
    <!-- Newsletter Section -->
    <section class="newsletter-section">
      <div class="newsletter-content">
        <div class="newsletter-text">
          <h3>
            <font-awesome-icon icon="fa-envelope" />
            Fique por dentro das novidades
          </h3>
          <p>Receba dicas de estudo e atualizações sobre nossas ferramentas de IA</p>
        </div>
        <form @submit.prevent="subscribeNewsletter" class="newsletter-form">
          <input 
            type="email" 
            v-model="newsletterEmail"
            placeholder="Seu melhor e-mail"
            class="newsletter-input"
            required
          />
          <button type="submit" class="newsletter-button">
            Inscrever
            <font-awesome-icon icon="fa-arrow-right" />
          </button>
        </form>
      </div>
    </section>

    <!-- Wave Animation -->
    <div class="footer-wave-container">
      <div class="footer-wave"></div>
    </div>

    <!-- Main Footer Content -->
    <div class="footer-content">
      <div class="footer-grid">
        <!-- Brand Column -->
        <div class="footer-column brand-column">
          <div class="footer-logo">
            <font-awesome-icon icon="fa-brain" class="logo-icon" />
            <h3>Sophos Academy</h3>
          </div>
          <p class="footer-about">
            Revolucionando o estudo médico com IA avançada. 
            Aprenda de forma mais inteligente, não mais difícil.
          </p>
          
          <!-- Stats -->
          <div class="platform-stats">
            <div class="stat">
              <span class="stat-number">15+</span>
              <span class="stat-label">Ferramentas IA</span>
            </div>
            <div class="stat">
              <span class="stat-number">2K+</span>
              <span class="stat-label">Estudantes</span>
            </div>
            <div class="stat">
              <span class="stat-number">98%</span>
              <span class="stat-label">Aprovação</span>
            </div>
          </div>

          <!-- Social Links -->
          <div class="social-section">
            <h4>Conecte-se</h4>
            <div class="social-icons">
              <a href="#" class="social-icon twitter" title="Twitter">
                <font-awesome-icon icon="fa-brands fa-twitter" />
              </a>
              <a href="#" class="social-icon instagram" title="Instagram">
                <font-awesome-icon icon="fa-brands fa-instagram" />
              </a>
              <a href="#" class="social-icon linkedin" title="LinkedIn">
                <font-awesome-icon icon="fa-brands fa-linkedin-in" />
              </a>
              <a href="#" class="social-icon youtube" title="YouTube">
                <font-awesome-icon icon="fa-brands fa-youtube" />
              </a>
              <a href="#" class="social-icon discord" title="Discord">
                <font-awesome-icon icon="fa-brands fa-discord" />
              </a>
            </div>
          </div>
        </div>

        <!-- Study Tools Column -->
        <div class="footer-column">
          <h3 class="footer-title">
            <font-awesome-icon icon="fa-graduation-cap" />
            Ferramentas de Estudo
          </h3>
          <ul class="footer-links">
            <li>
              <router-link to="/flashcards">
                <font-awesome-icon icon="fa-layer-group" />
                Flashcards Inteligentes
              </router-link>
            </li>
            <li>
              <router-link to="/revision-scheduler">
                <font-awesome-icon icon="fa-calendar-alt" />
                Agendador de Revisões
              </router-link>
            </li>
            <li>
              <router-link to="/plano-estudo">
                <font-awesome-icon icon="fa-tasks" />
                Plano de Estudo
              </router-link>
            </li>
            <li>
              <router-link to="/pomodoro">
                <font-awesome-icon icon="fa-clock" />
                Pomodoro Timer
              </router-link>
            </li>
            <li>
              <router-link to="/calendar">
                <font-awesome-icon icon="fa-calendar" />
                Calendário
              </router-link>
            </li>
          </ul>
        </div>

        <!-- AI Tools Column -->
        <div class="footer-column">
          <h3 class="footer-title">
            <font-awesome-icon icon="fa-robot" />
            Central de IA
          </h3>
          <ul class="footer-links">
            <li>
              <router-link to="/engines">
                <font-awesome-icon icon="fa-rocket" />
                Engines Hub
                <span class="link-badge">Novo</span>
              </router-link>
            </li>
            <li>
              <router-link to="/ai-tools/second-brain">
                <font-awesome-icon icon="fa-brain" />
                Second Brain
              </router-link>
            </li>
            <li>
              <router-link to="/ai-tools/study-assistant">
                <font-awesome-icon icon="fa-robot" />
                Assistente de Estudo
              </router-link>
            </li>
            <li>
              <router-link to="/exames">
                <font-awesome-icon icon="fa-microscope" />
                Análise de Exames
              </router-link>
            </li>
          </ul>
        </div>

        <!-- Resources Column -->
        <div class="footer-column">
          <h3 class="footer-title">
            <font-awesome-icon icon="fa-book-open" />
            Recursos
          </h3>
          <ul class="footer-links">
            <li>
              <router-link to="/videos">
                <font-awesome-icon icon="fa-video" />
                Videoaulas
              </router-link>
            </li>
            <li>
              <router-link to="/questoes">
                <font-awesome-icon icon="fa-question-circle" />
                Banco de Questões
              </router-link>
            </li>
            <li>
              <router-link to="/notas">
                <font-awesome-icon icon="fa-sticky-note" />
                Notas & Resumos
              </router-link>
            </li>
            <li>
              <router-link to="/resources">
                <font-awesome-icon icon="fa-book" />
                Material de Estudo
              </router-link>
            </li>
          </ul>
        </div>

        <!-- Support Column -->
        <div class="footer-column">
          <h3 class="footer-title">
            <font-awesome-icon icon="fa-life-ring" />
            Suporte
          </h3>
          <ul class="footer-links">
            <li>
              <router-link to="/help">
                <font-awesome-icon icon="fa-question-circle" />
                Central de Ajuda
              </router-link>
            </li>
            <li>
              <router-link to="/faq">
                <font-awesome-icon icon="fa-comments" />
                Perguntas Frequentes
              </router-link>
            </li>
            <li>
              <router-link to="/contact">
                <font-awesome-icon icon="fa-envelope" />
                Fale Conosco
              </router-link>
            </li>
            <li>
              <router-link to="/tutorials">
                <font-awesome-icon icon="fa-play-circle" />
                Tutoriais
              </router-link>
            </li>
            <li>
              <router-link to="/community">
                <font-awesome-icon icon="fa-users" />
                Comunidade
                <span class="link-badge">Beta</span>
              </router-link>
            </li>
          </ul>
        </div>
      </div>

      <!-- Features Showcase -->
      <div class="features-showcase">
        <div class="feature-card">
          <font-awesome-icon icon="fa-shield-alt" />
          <div>
            <h4>Dados Seguros</h4>
            <p>Criptografia de ponta</p>
          </div>
        </div>
        <div class="feature-card">
          <font-awesome-icon icon="fa-sync" />
          <div>
            <h4>Sincronização</h4>
            <p>Acesse de qualquer lugar</p>
          </div>
        </div>
        <div class="feature-card">
          <font-awesome-icon icon="fa-chart-line" />
          <div>
            <h4>Analytics</h4>
            <p>Acompanhe seu progresso</p>
          </div>
        </div>
        <div class="feature-card">
          <font-awesome-icon icon="fa-brain" />
          <div>
            <h4>IA Avançada</h4>
            <p>Aprendizado personalizado</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Bottom -->
    <div class="footer-bottom">
      <div class="footer-bottom-content">
        <div class="copyright">
          <p>&copy; {{ currentYear }} Sophos Academy. Todos os direitos reservados.</p>
          <div class="legal-links">
            <router-link to="/privacy">Privacidade</router-link>
            <span class="separator">•</span>
            <router-link to="/terms">Termos de Uso</router-link>
            <span class="separator">•</span>
            <router-link to="/cookies">Cookies</router-link>
          </div>
        </div>
        
        <div class="footer-badges">
          <div class="tech-stack">
            <span class="tech-badge vue">
              <font-awesome-icon icon="fa-brands fa-vuejs" />
              Vue 3
            </span>
            <span class="tech-badge python">
              <font-awesome-icon icon="fa-brands fa-python" />
              FastAPI
            </span>
            <span class="tech-badge ai">
              <font-awesome-icon icon="fa-brain" />
              GPT-4
            </span>
          </div>
          
          <div class="awards">
            <img src="/badge-innovation.png" alt="Innovation Award" class="award-badge" />
            <img src="/badge-education.png" alt="Education Excellence" class="award-badge" />
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'AppFooterEnhanced',
  setup() {
    const store = useStore()
    const newsletterEmail = ref('')
    
    const currentYear = computed(() => new Date().getFullYear())
    
    const subscribeNewsletter = async () => {
      try {
        // Implementar lógica de inscrição
        console.log('Newsletter subscription:', newsletterEmail.value)
        // Mostrar notificação de sucesso
        store.dispatch('showNotification', {
          type: 'success',
          message: 'Inscrição realizada com sucesso!'
        })
        newsletterEmail.value = ''
      } catch (error) {
        store.dispatch('showNotification', {
          type: 'error',
          message: 'Erro ao realizar inscrição. Tente novamente.'
        })
      }
    }
    
    return {
      currentYear,
      newsletterEmail,
      subscribeNewsletter
    }
  }
}
</script>

<style scoped>
.app-footer-enhanced {
  position: relative;
  margin-top: auto;
  overflow: hidden;
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
  color: #e0e0e0;
}

/* Newsletter Section */
.newsletter-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem 2rem;
}

.newsletter-content {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 3rem;
  flex-wrap: wrap;
}

.newsletter-text h3 {
  font-size: 1.5rem;
  color: white;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.newsletter-text p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
}

.newsletter-form {
  display: flex;
  gap: 1rem;
  flex: 1;
  max-width: 500px;
}

.newsletter-input {
  flex: 1;
  padding: 0.875rem 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 2rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.newsletter-input:focus {
  outline: none;
  border-color: white;
  background: rgba(255, 255, 255, 0.2);
}

.newsletter-button {
  padding: 0.875rem 2rem;
  background: white;
  color: #667eea;
  border: none;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.newsletter-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Wave Animation */
.footer-wave-container {
  position: relative;
  height: 100px;
  width: 100%;
  overflow: hidden;
}

.footer-wave {
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23667eea'/%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23764ba2'/%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%231a1a2e'/%3E%3C/svg%3E");
  background-size: 1200px 100%;
  animation: waveAnimation 20s linear infinite;
}

@keyframes waveAnimation {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

/* Main Content */
.footer-content {
  padding: 4rem 2rem 3rem;
  max-width: 1400px;
  margin: 0 auto;
}

.footer-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

/* Brand Column */
.brand-column {
  padding-right: 2rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.logo-icon {
  font-size: 2rem;
  color: #667eea;
}

.footer-logo h3 {
  font-size: 1.75rem;
  color: white;
  font-weight: 700;
}

.footer-about {
  color: #b0b0b0;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.platform-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.75rem;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  font-size: 0.85rem;
  color: #888;
}

/* Social Section */
.social-section h4 {
  font-size: 1rem;
  margin-bottom: 1rem;
  color: #ccc;
}

.social-icons {
  display: flex;
  gap: 0.75rem;
}

.social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-icon:hover {
  transform: translateY(-3px);
}

.social-icon.twitter:hover {
  background: #1da1f2;
}

.social-icon.instagram:hover {
  background: linear-gradient(45deg, #f58529, #dd2a7b, #8134af);
}

.social-icon.linkedin:hover {
  background: #0077b5;
}

.social-icon.youtube:hover {
  background: #ff0000;
}

.social-icon.discord:hover {
  background: #5865f2;
}

/* Column Styles */
.footer-column h3 {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-title svg {
  font-size: 1rem;
  color: #667eea;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-links a {
  color: #b0b0b0;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
}

.footer-links a:hover {
  color: #667eea;
  transform: translateX(5px);
}

.footer-links svg {
  font-size: 0.85rem;
  opacity: 0.7;
}

.link-badge {
  background: #667eea;
  color: white;
  font-size: 0.7rem;
  padding: 0.15rem 0.5rem;
  border-radius: 0.75rem;
  margin-left: 0.5rem;
  font-weight: 600;
}

/* Features Showcase */
.features-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 1rem;
  margin-bottom: 2rem;
}

.feature-card {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.feature-card svg {
  font-size: 2rem;
  color: #667eea;
}

.feature-card h4 {
  font-size: 1rem;
  color: white;
  margin-bottom: 0.25rem;
}

.feature-card p {
  font-size: 0.85rem;
  color: #888;
}

/* Footer Bottom */
.footer-bottom {
  background: rgba(0, 0, 0, 0.3);
  padding: 2rem;
}

.footer-bottom-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.copyright p {
  margin-bottom: 0.5rem;
  color: #888;
}

.legal-links {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.legal-links a {
  color: #888;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.legal-links a:hover {
  color: #667eea;
}

.separator {
  color: #555;
}

/* Footer Badges */
.footer-badges {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.tech-stack {
  display: flex;
  gap: 0.75rem;
}

.tech-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  font-size: 0.85rem;
  color: white;
}

.tech-badge svg {
  font-size: 1rem;
}

.tech-badge.vue {
  background: rgba(66, 184, 131, 0.2);
  color: #42b883;
}

.tech-badge.python {
  background: rgba(55, 118, 171, 0.2);
  color: #3776ab;
}

.tech-badge.ai {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.awards {
  display: flex;
  gap: 1rem;
}

.award-badge {
  height: 40px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.award-badge:hover {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .footer-grid {
    grid-template-columns: 2fr 1fr 1fr 1fr;
  }
  
  .footer-column:last-child {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .newsletter-content {
    flex-direction: column;
    text-align: center;
  }
  
  .newsletter-form {
    width: 100%;
    flex-direction: column;
  }
  
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .brand-column {
    padding-right: 0;
    text-align: center;
  }
  
  .platform-stats {
    justify-content: center;
  }
  
  .social-icons {
    justify-content: center;
  }
  
  .features-showcase {
    grid-template-columns: 1fr;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-badges {
    flex-direction: column;
  }
}

/* Dark Mode Adjustments */
:root[data-theme="dark"] .app-footer-enhanced {
  background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 100%);
}

:root[data-theme="dark"] .footer-wave {
  opacity: 0.5;
}

:root[data-theme="dark"] .feature-card {
  background: rgba(255, 255, 255, 0.05);
}
</style>