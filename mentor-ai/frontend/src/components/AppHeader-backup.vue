<template>
  <header class="app-header">
    <div class="container">
      <!-- Logo Section - Redesigned -->
      <div class="logo-section">
        <router-link to="/" class="logo-link" @click="closeMenus">
          <div class="logo-content">
            <!-- Enhanced Brain Icon with Creative Animations -->
            <div class="creative-brain-container">
              <div class="brain-core">
                <font-awesome-icon icon="brain" class="brain-main" />
                <div class="brain-aura"></div>
              </div>
              <div class="brain-orbit">
                <div class="orbit-particle"></div>
                <div class="orbit-particle"></div>
                <div class="orbit-particle"></div>
              </div>
              <div class="neural-network">
                <span class="neural-line"></span>
                <span class="neural-line"></span>
                <span class="neural-line"></span>
                <span class="neural-line"></span>
              </div>
            </div>
            
            <!-- Text Container -->
            <div class="text-container">
              <div class="text-primary">Sophos</div>
              <div class="text-divider"></div>
              <div class="text-secondary">Academy</div>
            </div>
          </div>
        </router-link>
        
        <!-- Navigation Menu Trigger -->
        <button class="menu-trigger" @click="toggleLogoMenu" :class="{ 'active': logoMenuOpen }">
          <div class="menu-trigger-creative">
            <div class="trigger-logo">
              <div class="logo-grid">
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
                <div class="grid-dot center-dot">
                  <div class="pulse-ring"></div>
                </div>
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
                <div class="grid-dot"></div>
              </div>
            </div>
          </div>
        </button>

        <!-- Enhanced Navigation Dropdown -->
        <transition name="slide-fade">
          <div v-if="logoMenuOpen" class="navigation-dropdown" @click.stop>
            <div class="dropdown-inner">
              <div class="dropdown-header">
                <font-awesome-icon icon="compass" />
                <h3>Menu Principal</h3>
              </div>

              <!-- Engines Highlight -->
              <div class="engines-highlight">
                <div class="highlight-badge">
                  <font-awesome-icon icon="star" />
                  <span>NOVO</span>
                </div>
                <router-link to="/engines" class="engines-card" @click="closeLogoMenu">
                  <div class="engines-icon">
                    <font-awesome-icon icon="rocket" />
                  </div>
                  <div class="engines-info">
                    <h4>Engines - Central de IA</h4>
                    <p>Ferramentas de inteligência artificial avançadas</p>
                  </div>
                  <font-awesome-icon icon="arrow-right" class="engines-arrow" />
                </router-link>
              </div>

              <!-- Navigation Grid -->
              <div class="nav-grid">
                <div class="nav-column">
                  <h5>Estudo</h5>
                  <router-link v-for="item in dailyStudyItems" :key="item.path"
                              :to="item.path" class="nav-link" @click="closeLogoMenu">
                    <font-awesome-icon :icon="item.icon" />
                    <span>{{ item.title }}</span>
                  </router-link>
                </div>

                <div class="nav-column">
                  <h5>IA Tools</h5>
                  <router-link v-for="item in aiToolsItems" :key="item.path"
                              :to="item.path" class="nav-link" @click="closeLogoMenu">
                    <font-awesome-icon :icon="item.icon" />
                    <span>{{ item.title }}</span>
                  </router-link>
                </div>

                <div class="nav-column">
                  <h5>Recursos</h5>
                  <router-link v-for="item in resourceItems" :key="item.path"
                              :to="item.path" class="nav-link" @click="closeLogoMenu">
                    <font-awesome-icon :icon="item.icon" />
                    <span>{{ item.title }}</span>
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>

      <!-- Search Bar - Enhanced -->
      <div class="search-container">
        <div class="search-wrapper" :class="{ 'focused': isSearchFocused }">
          <font-awesome-icon icon="search" class="search-icon" />
          <input 
            type="text" 
            class="search-input" 
            placeholder="Pesquisar..."
            v-model="searchQuery"
            @focus="isSearchFocused = true"
            @blur="isSearchFocused = false"
          />
          <div class="search-actions">
            <transition name="fade">
              <div v-if="isSearchFocused" class="search-hints">
                <kbd>ESC</kbd>
                <kbd>Enter</kbd>
              </div>
            </transition>
          </div>
        </div>
      </div>


      <!-- Actions -->
      <div class="actions-container">
        <!-- Theme Toggle -->
        <div class="theme-switch">
          <input 
            type="checkbox" 
            id="theme-toggle"
            :checked="isDarkMode"
            @change="toggleTheme"
            class="theme-checkbox"
          >
          <label for="theme-toggle" class="theme-label">
            <span class="theme-slider">
              <font-awesome-icon :icon="isDarkMode ? 'fa-moon' : 'fa-sun'" />
            </span>
          </label>
        </div>

        <!-- User Menu -->
        <div class="user-menu" v-if="currentUser">
          <button class="user-button" @click="toggleUserMenu" :class="{ 'active': userMenuOpen }">
            <div class="user-avatar-creative">
              <div class="avatar-core">
                <span class="avatar-text">{{ userInitials }}</span>
                <div class="avatar-glow"></div>
              </div>
              <div class="avatar-particles">
                <span class="particle"></span>
                <span class="particle"></span>
                <span class="particle"></span>
              </div>
              <div class="neural-connections">
                <span class="connection"></span>
                <span class="connection"></span>
                <span class="connection"></span>
                <span class="connection"></span>
              </div>
            </div>
            <span class="user-name">{{ currentUser.name || currentUser.email.split('@')[0] }}</span>
            <font-awesome-icon icon="fa-chevron-down" class="user-arrow" />
          </button>

          <transition name="dropdown">
            <div v-if="userMenuOpen" class="user-dropdown">
              <div class="dropdown-user-info">
                <div class="info-avatar">
                  <span>{{ userInitials }}</span>
                </div>
                <div class="info-details">
                  <div class="info-name">{{ currentUser.name || 'Usuário' }}</div>
                  <div class="info-email">{{ currentUser.email }}</div>
                </div>
              </div>

              <div class="dropdown-divider"></div>

              <router-link to="/profile" @click="closeUserMenu" class="dropdown-link">
                <font-awesome-icon icon="fa-user" />
                <span>Perfil</span>
              </router-link>

              <router-link to="/settings" @click="closeUserMenu" class="dropdown-link">
                <font-awesome-icon icon="fa-cog" />
                <span>Configurações</span>
              </router-link>

              <router-link to="/help" @click="closeUserMenu" class="dropdown-link">
                <font-awesome-icon icon="fa-question-circle" />
                <span>Ajuda</span>
              </router-link>

              <div class="dropdown-divider"></div>

              <button @click="logout" class="dropdown-link logout-btn">
                <font-awesome-icon icon="fa-sign-out-alt" />
                <span>Sair</span>
              </button>
            </div>
          </transition>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <button class="mobile-menu-btn" @click="toggleMobileMenu">
        <span></span>
        <span></span>
        <span></span>
      </button>
    </div>
  </header>
</template>

<script>
export default {
  name: 'AppHeader',
  data() {
    return {
      isSearchFocused: false,
      searchQuery: '',
      logoMenuOpen: false,
      userMenuOpen: false,
      mobileMenuOpen: false,
      isDarkMode: true,
      currentTheme: 'dark',
      dailyStudyItems: [
        { path: '/calendar', title: 'Calendário', icon: 'calendar-days' },
        { path: '/revision-scheduler', title: 'Revisões', icon: 'clock-rotate-left' },
        { path: '/plano-estudo', title: 'Plano de Estudo', icon: 'clipboard-list' },
        { path: '/pomodoro', title: 'Pomodoro', icon: 'clock' },
        { path: '/progress-dashboard', title: 'Desempenho', icon: 'chart-line' }
      ],
      aiToolsItems: [
        { path: '/ai-tools/flashcards', title: 'Flashcards', icon: 'layer-group' },
        { path: '/second-brain', title: 'Second Brain', icon: 'brain' },
        { path: '/round-ai', title: 'Round AI', icon: 'user-md' },
        { path: '/ai-tools/study-assistant', title: 'Assistente de Estudo', icon: 'user-graduate' }
      ],
      resourceItems: [
        { path: '/recursos', title: 'Recursos', icon: 'book-open' },
        { path: '/videos', title: 'Videoaulas', icon: 'video' },
        { path: '/resumos-notas', title: 'Notas & Resumos', icon: 'file-alt' },
        { path: '/provas-simulados', title: 'Provas & Simulados', icon: 'file-lines' }
      ]
    };
  },
  computed: {
    currentUser() {
      return this.$store.state.auth.user;
    },
    userInitials() {
      if (this.currentUser) {
        if (this.currentUser.name) {
          return this.currentUser.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
        }
        return this.currentUser.email.charAt(0).toUpperCase();
      }
      return 'U';
    }
  },
  mounted() {
    this.isDarkMode = this.currentTheme === 'dark';
    document.addEventListener('click', this.handleOutsideClick);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleOutsideClick);
  },
  methods: {
    toggleLogoMenu() {
      this.logoMenuOpen = !this.logoMenuOpen;
      this.userMenuOpen = false;
    },
    closeLogoMenu() {
      this.logoMenuOpen = false;
    },
    toggleUserMenu() {
      this.userMenuOpen = !this.userMenuOpen;
      this.logoMenuOpen = false;
    },
    closeUserMenu() {
      this.userMenuOpen = false;
    },
    closeMenus() {
      this.logoMenuOpen = false;
      this.userMenuOpen = false;
    },
    toggleMobileMenu() {
      this.mobileMenuOpen = !this.mobileMenuOpen;
    },
    async logout() {
      try {
        await this.$store.dispatch('auth/logout');
        this.$router.push('/login');
      } catch (error) {
        console.error('Logout error:', error);
      }
    },
    handleOutsideClick(event) {
      const logoSection = this.$el.querySelector('.logo-section');
      const userMenu = this.$el.querySelector('.user-menu');
      
      if (logoSection && !logoSection.contains(event.target)) {
        this.logoMenuOpen = false;
      }
      
      if (userMenu && !userMenu.contains(event.target)) {
        this.userMenuOpen = false;
      }
    },
    toggleTheme() {
      this.isDarkMode = !this.isDarkMode;
      const newTheme = this.isDarkMode ? 'dark' : 'light';
      document.documentElement.setAttribute('data-theme', newTheme);
      this.currentTheme = newTheme;
      localStorage.setItem('theme', newTheme);
      this.$emit('theme-changed', newTheme);
    }
  }
};
</script>

<style scoped>
/* Base Styles */
.app-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--header-bg);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  height: 72px;
}

/* Variables */
:root {
  --header-bg: rgba(22, 27, 34, 0.95);
  --border-color: rgba(48, 54, 61, 0.5);
  --primary-color: #42b983;
  --primary-hover: #52d399;
  --text-primary: #e6edf3;
  --text-secondary: #8b949e;
  --button-bg: rgba(48, 54, 61, 0.4);
  --button-hover: rgba(48, 54, 61, 0.6);
  --dropdown-bg: rgba(10, 12, 15, 1);
  --input-bg: rgba(13, 17, 23, 0.4);
  --accent-purple: #8b5cf6;
  --accent-blue: #3b82f6;
  --accent-pink: #ec4899;
}

:root[data-theme="light"] {
  --header-bg: rgba(22, 27, 34, 0.95);
  --border-color: rgba(48, 54, 61, 0.5);
  --text-primary: #e6edf3;
  --text-secondary: #8b949e;
  --button-bg: rgba(48, 54, 61, 0.4);
  --button-hover: rgba(48, 54, 61, 0.6);
  --dropdown-bg: rgba(10, 12, 15, 1);
  --input-bg: rgba(13, 17, 23, 0.4);
}

.container {
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 24px;
}

/* Logo Section - Enhanced Design */
.logo-section {
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 10;
}

.logo-link {
  text-decoration: none;
  display: block;
}

.logo-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 8px 0;
  transition: all 0.3s ease;
}

/* Creative Brain Container */
.creative-brain-container {
  position: relative;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

/* Brain Core */
.brain-core {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brain-main {
  font-size: 32px;
  color: var(--primary-color);
  z-index: 3;
  animation: brain-breathe 4s ease-in-out infinite;
  filter: drop-shadow(0 0 8px rgba(66, 185, 131, 0.5));
}

@keyframes brain-breathe {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 8px rgba(66, 185, 131, 0.5));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 16px rgba(66, 185, 131, 0.8));
  }
}

/* Brain Aura */
.brain-aura {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: aura-pulse 3s ease-in-out infinite;
  z-index: 1;
}

@keyframes aura-pulse {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.6;
  }
}


/* Orbital Particles */
.brain-orbit {
  position: absolute;
  inset: -12px;
  animation: orbit-rotate 10s linear infinite;
  z-index: 4;
}

@keyframes orbit-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.orbit-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: 0 0 4px var(--primary-color);
}

.orbit-particle:nth-child(1) {
  top: 2px;
  left: 50%;
  transform: translateX(-50%);
  animation: particle-glow 2s ease-in-out infinite;
}

.orbit-particle:nth-child(2) {
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  animation: particle-glow 2s ease-in-out infinite 0.66s;
}

.orbit-particle:nth-child(3) {
  top: 50%;
  right: 2px;
  transform: translateY(-50%);
  animation: particle-glow 2s ease-in-out infinite 1.33s;
}

@keyframes particle-glow {
  0%, 100% {
    background: var(--primary-color);
    box-shadow: 0 0 4px var(--primary-color);
  }
  50% {
    background: var(--primary-hover);
    box-shadow: 0 0 8px var(--primary-hover);
  }
}

/* Neural Network Lines */
.neural-network {
  position: absolute;
  inset: 0;
  z-index: 0;
}

.neural-line {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.3;
  transform-origin: center;
}

.neural-line:nth-child(1) {
  width: 100%;
  top: 50%;
  animation: neural-flash 3s ease-in-out infinite;
}

.neural-line:nth-child(2) {
  width: 100%;
  left: 50%;
  transform: rotate(90deg);
  animation: neural-flash 3s ease-in-out infinite 0.5s;
}

.neural-line:nth-child(3) {
  width: 100%;
  top: 50%;
  transform: rotate(45deg);
  animation: neural-flash 3s ease-in-out infinite 1s;
}

.neural-line:nth-child(4) {
  width: 100%;
  top: 50%;
  transform: rotate(-45deg);
  animation: neural-flash 3s ease-in-out infinite 1.5s;
}

@keyframes neural-flash {
  0%, 100% {
    opacity: 0.1;
    transform: scaleX(0.5) rotate(var(--rotation, 0deg));
  }
  50% {
    opacity: 0.6;
    transform: scaleX(1) rotate(var(--rotation, 0deg));
  }
}

/* Text Container */
.text-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.text-primary {
  font-size: 22px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.5px;
  transition: all 0.3s ease;
}

.text-divider {
  width: 2px;
  height: 24px;
  background: var(--border-color);
  transition: all 0.3s ease;
}

.text-secondary {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-secondary);
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

/* Logo Hover Effects */
.logo-link:hover .brain-bg {
  opacity: 0.2;
  transform: rotate(45deg);
}

.logo-link:hover .brain-icon {
  transform: scale(1.1);
}

.logo-link:hover .text-primary {
  color: var(--primary-color);
}

.logo-link:hover .text-divider {
  background: var(--primary-color);
  height: 28px;
}

.logo-link:hover .brain-particles span {
  animation-duration: 2s;
}

/* Menu Trigger - Creative Design */
.menu-trigger {
  width: 48px;
  height: 48px;
  background: var(--button-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.menu-trigger:hover {
  background: var(--button-hover);
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.menu-trigger.active {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-color: var(--primary-color);
  box-shadow: 0 0 20px rgba(66, 185, 131, 0.4);
}

/* Creative Menu Logo */
.menu-trigger-creative {
  width: 24px;
  height: 24px;
  position: relative;
}

.trigger-logo {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Grid Pattern */
.logo-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 3px;
  width: 100%;
  height: 100%;
  position: relative;
}

.grid-dot {
  width: 4px;
  height: 4px;
  background: var(--text-secondary);
  border-radius: 50%;
  transition: all 0.3s ease;
  position: relative;
  margin: auto;
}

.center-dot {
  background: var(--primary-color);
  transform: scale(1.5);
}

.center-dot .pulse-ring {
  position: absolute;
  inset: -4px;
  border: 1px solid var(--primary-color);
  border-radius: 50%;
  animation: menu-pulse 2s ease-out infinite;
}

@keyframes menu-pulse {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}


/* Hover Effects */
.menu-trigger:hover .grid-dot {
  background: var(--primary-color);
  animation: dot-dance 0.6s ease-in-out infinite;
}

.menu-trigger:hover .grid-dot:nth-child(1) { animation-delay: 0s; }
.menu-trigger:hover .grid-dot:nth-child(2) { animation-delay: 0.1s; }
.menu-trigger:hover .grid-dot:nth-child(3) { animation-delay: 0.2s; }
.menu-trigger:hover .grid-dot:nth-child(4) { animation-delay: 0.3s; }
.menu-trigger:hover .grid-dot:nth-child(5) { animation-delay: 0.4s; }
.menu-trigger:hover .grid-dot:nth-child(6) { animation-delay: 0.5s; }
.menu-trigger:hover .grid-dot:nth-child(7) { animation-delay: 0.6s; }
.menu-trigger:hover .grid-dot:nth-child(8) { animation-delay: 0.7s; }
.menu-trigger:hover .grid-dot:nth-child(9) { animation-delay: 0.8s; }

@keyframes dot-dance {
  0%, 100% {
    transform: scale(1) translateY(0);
  }
  50% {
    transform: scale(1.5) translateY(-3px);
  }
}

/* Active State Animation */
.menu-trigger.active .grid-dot {
  background: white;
  animation: dot-spin 0.6s ease-in-out;
}

.menu-trigger.active .center-dot {
  background: white;
  animation: center-expand 0.4s ease-out;
}

@keyframes dot-spin {
  0% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.3) rotate(180deg);
  }
  100% {
    transform: scale(1) rotate(360deg);
  }
}

@keyframes center-expand {
  0% {
    transform: scale(1.5);
  }
  50% {
    transform: scale(2);
  }
  100% {
    transform: scale(1.5);
  }
}


/* Navigation Dropdown */
.navigation-dropdown {
  position: absolute;
  top: calc(100% + 12px);
  left: 0;
  width: 520px;
  background: rgb(10, 12, 15);
  backdrop-filter: none;
  border: 1px solid var(--border-color);
  border-radius: 16px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.dropdown-inner {
  padding: 0;
  background: rgb(10, 12, 15);
}

.dropdown-header {
  padding: 20px 24px;
  background: var(--button-bg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 12px;
}

.dropdown-header svg {
  font-size: 20px;
  color: var(--primary-color);
}

.dropdown-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

/* Engines Highlight */
.engines-highlight {
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.highlight-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.highlight-badge svg {
  font-size: 12px;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.2) rotate(180deg);
  }
}

.engines-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--button-bg);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.engines-card:hover {
  background: var(--button-hover);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(66, 185, 131, 0.15);
}

.engines-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
}

.engines-info {
  flex: 1;
}

.engines-info h4 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.engines-info p {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
}

.engines-arrow {
  font-size: 16px;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.engines-card:hover .engines-arrow {
  color: var(--primary-color);
  transform: translateX(4px);
}

/* Navigation Grid */
.nav-grid {
  padding: 24px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  background: rgb(10, 12, 15);
}

.nav-column h5 {
  margin: 0 0 16px 0;
  font-size: 13px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--text-secondary);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 16px;
  border-radius: 8px;
  text-decoration: none;
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
  margin-bottom: 4px;
}

.nav-link:hover {
  background: var(--button-bg);
  color: var(--primary-color);
  transform: translateX(4px);
}

.nav-link svg {
  font-size: 16px;
  opacity: 0.7;
}

/* Search Container */
.search-container {
  flex: 1;
  margin-right: 24px;
}

.search-wrapper {
  position: relative;
  width: 100%;
  height: 44px;
}

.search-input {
  width: 100%;
  height: 100%;
  padding: 0 110px 0 44px;
  background: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  color: var(--text-primary);
  font-size: 15px;
  transition: all 0.3s ease;
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.search-input:focus {
  outline: none;
  background: var(--button-bg);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(66, 185, 131, 0.1);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.search-wrapper.focused .search-icon {
  color: var(--primary-color);
}

.search-actions {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}

.search-hints {
  display: flex;
  gap: 8px;
}

kbd {
  padding: 2px 8px;
  background: var(--button-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  color: var(--text-secondary);
}


/* Actions Container */
.actions-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Theme Switch */
.theme-switch {
  position: relative;
}

.theme-checkbox {
  display: none;
}

.theme-label {
  display: block;
  width: 44px;
  height: 24px;
  background: var(--button-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.theme-slider {
  position: absolute;
  width: 18px;
  height: 18px;
  background: var(--text-primary);
  border-radius: 50%;
  top: 2px;
  left: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: var(--header-bg);
  transition: all 0.3s ease;
}

.theme-checkbox:checked + .theme-label {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.theme-checkbox:checked + .theme-label .theme-slider {
  transform: translateX(20px);
  background: white;
  color: var(--primary-color);
}

/* User Menu */
.user-menu {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 2px 16px 2px 2px;
  background: var(--button-bg);
  border: 1px solid var(--border-color);
  border-radius: 22px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 44px;
}

.user-button:hover {
  background: var(--button-hover);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

/* Creative User Avatar Design */
.user-avatar-creative {
  width: 40px;
  height: 40px;
  position: relative;
  flex-shrink: 0;
}

.avatar-core {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 3;
}

.avatar-text {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  color: white;
  animation: avatar-breathe 3s ease-in-out infinite;
  box-shadow: 0 0 15px rgba(66, 185, 131, 0.4);
}

@keyframes avatar-breathe {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 15px rgba(66, 185, 131, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 25px rgba(66, 185, 131, 0.6);
  }
}

.avatar-glow {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, rgba(66, 185, 131, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: aura-pulse 3s ease-in-out infinite;
  z-index: 1;
}

@keyframes aura-pulse {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.6;
  }
}


/* Orbital Particles */
.avatar-particles {
  position: absolute;
  inset: -6px;
  animation: orbit-rotate 10s linear infinite;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: 0 0 3px var(--primary-color);
  opacity: 0.8;
}

.particle:nth-child(1) {
  top: 1px;
  left: 50%;
  transform: translateX(-50%);
  animation: particle-glow 2s ease-in-out infinite;
}

.particle:nth-child(2) {
  bottom: 1px;
  left: 50%;
  transform: translateX(-50%);
  animation: particle-glow 2s ease-in-out infinite 0.66s;
}

.particle:nth-child(3) {
  top: 50%;
  right: 1px;
  transform: translateY(-50%);
  animation: particle-glow 2s ease-in-out infinite 1.33s;
}

@keyframes orbit-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes particle-glow {
  0%, 100% {
    background: var(--primary-color);
    box-shadow: 0 0 4px var(--primary-color);
  }
  50% {
    background: var(--primary-hover);
    box-shadow: 0 0 8px var(--primary-hover);
  }
}


/* Neural Network Lines */
.neural-connections {
  position: absolute;
  inset: 0;
  z-index: 0;
}

.connection {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.3;
  transform-origin: center;
}

.connection:nth-child(1) {
  width: 100%;
  top: 50%;
  animation: neural-flash 3s ease-in-out infinite;
}

.connection:nth-child(2) {
  width: 100%;
  left: 50%;
  transform: rotate(90deg);
  animation: neural-flash 3s ease-in-out infinite 0.5s;
}

.connection:nth-child(3) {
  width: 100%;
  top: 50%;
  transform: rotate(45deg);
  animation: neural-flash 3s ease-in-out infinite 1s;
}

.connection:nth-child(4) {
  width: 100%;
  top: 50%;
  transform: rotate(-45deg);
  animation: neural-flash 3s ease-in-out infinite 1.5s;
}

@keyframes neural-flash {
  0%, 100% {
    opacity: 0.1;
    transform: scaleX(0.5) rotate(var(--rotation, 0deg));
  }
  50% {
    opacity: 0.6;
    transform: scaleX(1) rotate(var(--rotation, 0deg));
  }
}

/* Hover Effects */
.user-button:hover .avatar-text {
  animation-duration: 1.5s;
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
}

.user-button:hover .ring {
  border-color: var(--primary-hover);
}

.user-button:hover .particle {
  background: var(--primary-hover);
  box-shadow: 0 0 5px var(--primary-hover);
}

/* Active State */
.user-button.active .avatar-text {
  background: white;
  color: var(--primary-color);
}

.user-button.active .ring {
  border-color: white;
}

.user-button.active .particle {
  background: white;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-arrow {
  font-size: 10px;
  color: var(--text-secondary);
  transition: transform 0.3s ease;
}

.user-button.active .user-arrow {
  transform: rotate(180deg);
}

/* User Dropdown */
.user-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 280px;
  background: rgb(10, 12, 15);
  backdrop-filter: none;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.dropdown-user-info {
  padding: 20px;
  background: rgb(15, 17, 20);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 16px;
}

.info-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  color: white;
}

.info-details {
  flex: 1;
}

.info-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.info-email {
  font-size: 13px;
  color: var(--text-secondary);
}

.dropdown-divider {
  height: 1px;
  background: var(--border-color);
  margin: 0;
}

.dropdown-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  text-decoration: none;
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
}

.dropdown-link:hover {
  background: rgb(20, 22, 25);
  color: var(--primary-color);
}

.dropdown-link svg {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.logout-btn {
  color: #ef4444;
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  background: var(--button-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-menu-btn span {
  width: 20px;
  height: 2px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* Transitions */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive */
@media (max-width: 1200px) {
  .container {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .app-header {
    height: 64px;
  }
  
  .container {
    padding: 0 16px;
    gap: 12px;
  }
  
  .search-container {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .text-primary {
    font-size: 18px;
  }
  
  .text-secondary {
    font-size: 14px;
  }
  
  .creative-brain-container {
    width: 40px;
    height: 40px;
  }
  
  .brain-main {
    font-size: 20px;
  }
  
  .text-divider {
    height: 20px;
  }
  
  .navigation-dropdown {
    width: 100vw;
    left: -16px;
    right: -16px;
    border-radius: 0 0 16px 16px;
  }
  
  .nav-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>