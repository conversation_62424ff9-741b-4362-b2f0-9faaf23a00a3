<template>
  <div class="simple-calendar">
    <div class="calendar-header">
      <button @click="previousMonth" class="nav-btn">
        <i class="fas fa-chevron-left"></i>
      </button>
      <h3 class="month-title">{{ monthYear }}</h3>
      <button @click="nextMonth" class="nav-btn">
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
    
    <div class="weekdays">
      <div v-for="day in weekDays" :key="day" class="weekday">
        {{ day }}
      </div>
    </div>
    
    <div class="calendar-grid">
      <div 
        v-for="day in calendarDays" 
        :key="day.date"
        class="calendar-day"
        :class="{
          'other-month': !day.isCurrentMonth,
          'today': day.isToday,
          'selected': day.isSelected,
          'has-event': day.hasEvent
        }"
        @click="selectDate(day)"
      >
        <span class="day-number">{{ day.day }}</span>
        <div v-if="day.hasEvent" class="event-indicator"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'SimpleCalendar',
  props: {
    events: {
      type: Array,
      default: () => []
    },
    selectedDate: {
      type: Date,
      default: null
    }
  },
  emits: ['date-selected', 'month-changed'],
  setup(props, { emit }) {
    const currentDate = ref(new Date())
    const selectedDateRef = ref(props.selectedDate || null)
    
    const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
    
    const monthYear = computed(() => {
      const month = currentDate.value.toLocaleDateString('pt-BR', { month: 'long' })
      const year = currentDate.value.getFullYear()
      return `${month.charAt(0).toUpperCase() + month.slice(1)} ${year}`
    })
    
    const calendarDays = computed(() => {
      const year = currentDate.value.getFullYear()
      const month = currentDate.value.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())
      
      const days = []
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate)
        date.setDate(startDate.getDate() + i)
        
        const dayObj = {
          date: date.toISOString(),
          day: date.getDate(),
          isCurrentMonth: date.getMonth() === month,
          isToday: date.getTime() === today.getTime(),
          isSelected: selectedDateRef.value && 
                      date.toDateString() === new Date(selectedDateRef.value).toDateString(),
          hasEvent: checkHasEvent(date)
        }
        
        days.push(dayObj)
      }
      
      return days
    })
    
    const checkHasEvent = (date) => {
      return props.events.some(event => {
        const eventDate = new Date(event.date)
        return eventDate.toDateString() === date.toDateString()
      })
    }
    
    const previousMonth = () => {
      currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1)
      emit('month-changed', currentDate.value)
    }
    
    const nextMonth = () => {
      currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1)
      emit('month-changed', currentDate.value)
    }
    
    const selectDate = (day) => {
      if (!day.isCurrentMonth) return
      selectedDateRef.value = new Date(day.date)
      emit('date-selected', selectedDateRef.value)
    }
    
    return {
      weekDays,
      monthYear,
      calendarDays,
      previousMonth,
      nextMonth,
      selectDate
    }
  }
}
</script>

<style scoped>
.simple-calendar {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.nav-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.month-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.weekday {
  text-align: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.5);
  padding: 0.5rem 0;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.25rem;
}

.calendar-day:hover:not(.other-month) {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.calendar-day.other-month {
  opacity: 0.3;
  cursor: default;
}

.calendar-day.today {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
}

.calendar-day.selected {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: white;
}

.day-number {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.event-indicator {
  position: absolute;
  bottom: 4px;
  width: 4px;
  height: 4px;
  background: #667eea;
  border-radius: 50%;
}

.calendar-day.has-event .event-indicator {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .simple-calendar {
    padding: 1rem;
  }
  
  .calendar-day {
    font-size: 0.75rem;
  }
  
  .weekday {
    font-size: 0.625rem;
  }
}
</style> 