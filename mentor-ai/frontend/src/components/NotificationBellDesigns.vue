<template>
  <div class="bell-designs-showcase">
    <h2>Designs de Sino para Notificações</h2>
    
    <!-- Design 1: Gradiente Moderno -->
    <div class="design-item">
      <h3>Design 1: Gradiente Moderno</h3>
      <div class="bell-preview">
        <svg class="bell-design-1" viewBox="0 0 24 24" width="40" height="40">
          <defs>
            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
            </linearGradient>
          </defs>
          <path d="M12 2C10.9 2 10 2.9 10 4C10 4.11 10.01 4.22 10.03 4.33C7.75 5.13 6 7.35 6 10V16L4 18V19H20V18L18 16V10C18 7.35 16.25 5.13 13.97 4.33C13.99 4.22 14 4.11 14 4C14 2.9 13.1 2 12 2Z" fill="url(#gradient1)"/>
          <path d="M12 22C13.1 22 14 21.1 14 20H10C10 21.1 10.9 22 12 22Z" fill="url(#gradient1)"/>
          <circle cx="17" cy="6" r="3" fill="#f44336"/>
        </svg>
      </div>
    </div>

    <!-- Design 2: Minimalista com Linha -->
    <div class="design-item">
      <h3>Design 2: Minimalista com Linha</h3>
      <div class="bell-preview">
        <svg class="bell-design-2" viewBox="0 0 24 24" width="40" height="40">
          <path d="M12 2.5C11 2.5 10.2 3.3 10.2 4.3C10.2 4.4 10.2 4.5 10.2 4.6C8.1 5.3 6.5 7.3 6.5 9.7V15.5C6.5 16 6.3 16.5 5.9 16.9L4.5 18.3C4.2 18.6 4 19 4 19.5C4 19.8 4.2 20 4.5 20H19.5C19.8 20 20 19.8 20 19.5C20 19 19.8 18.6 19.5 18.3L18.1 16.9C17.7 16.5 17.5 16 17.5 15.5V9.7C17.5 7.3 15.9 5.3 13.8 4.6C13.8 4.5 13.8 4.4 13.8 4.3C13.8 3.3 13 2.5 12 2.5Z" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <path d="M10 20C10 21.1 10.9 22 12 22C13.1 22 14 21.1 14 20" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <circle cx="17" cy="6" r="2.5" fill="#ff5252" stroke="#fff" stroke-width="1"/>
        </svg>
      </div>
    </div>

    <!-- Design 3: Glassmorphism -->
    <div class="design-item">
      <h3>Design 3: Glassmorphism</h3>
      <div class="bell-preview dark-bg">
        <svg class="bell-design-3" viewBox="0 0 32 32" width="40" height="40">
          <defs>
            <filter id="glass">
              <feGaussianBlur in="SourceGraphic" stdDeviation="0.5"/>
            </filter>
            <linearGradient id="glassGrad" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" style="stop-color:rgba(255,255,255,0.3);stop-opacity:1" />
              <stop offset="100%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
            </linearGradient>
          </defs>
          <rect x="4" y="4" width="24" height="24" rx="12" fill="rgba(255,255,255,0.1)" filter="url(#glass)"/>
          <path d="M16 8C15 8 14.2 8.8 14.2 9.8C14.2 9.9 14.2 10 14.2 10.1C12.5 10.7 11.2 12.3 11.2 14.2V18L10 19.2V19.8H22V19.2L20.8 18V14.2C20.8 12.3 19.5 10.7 17.8 10.1C17.8 10 17.8 9.9 17.8 9.8C17.8 8.8 17 8 16 8Z" fill="url(#glassGrad)"/>
          <path d="M16 22C16.8 22 17.5 21.3 17.5 20.5H14.5C14.5 21.3 15.2 22 16 22Z" fill="url(#glassGrad)"/>
          <circle cx="22" cy="10" r="3" fill="#ff4444" opacity="0.9"/>
        </svg>
      </div>
    </div>

    <!-- Design 4: Outline Moderno -->
    <div class="design-item">
      <h3>Design 4: Outline Moderno</h3>
      <div class="bell-preview">
        <svg class="bell-design-4" viewBox="0 0 24 24" width="40" height="40">
          <defs>
            <linearGradient id="outlineGrad" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#667eea" />
              <stop offset="100%" style="stop-color:#764ba2" />
            </linearGradient>
          </defs>
          <g fill="none" stroke="url(#outlineGrad)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
            <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
            <circle cx="12" cy="2" r="1"/>
          </g>
          <circle cx="18" cy="8" r="3" fill="#f44336"/>
          <text x="18" y="11" text-anchor="middle" fill="white" font-size="4" font-weight="bold">3</text>
        </svg>
      </div>
    </div>

    <!-- Design 5: Neumorphism -->
    <div class="design-item">
      <h3>Design 5: Neumorphism</h3>
      <div class="bell-preview light-bg">
        <svg class="bell-design-5" viewBox="0 0 40 40" width="40" height="40">
          <defs>
            <filter id="neumorph">
              <feDropShadow dx="2" dy="2" stdDeviation="2" flood-opacity="0.2"/>
              <feDropShadow dx="-2" dy="-2" stdDeviation="2" flood-color="white" flood-opacity="0.7"/>
            </filter>
          </defs>
          <rect x="5" y="5" width="30" height="30" rx="15" fill="#e0e5ec" filter="url(#neumorph)"/>
          <path d="M20 10C19 10 18.2 10.8 18.2 11.8C18.2 11.9 18.2 12 18.2 12.1C16.5 12.7 15.2 14.3 15.2 16.2V20L14 21.2V21.8H26V21.2L24.8 20V16.2C24.8 14.3 23.5 12.7 21.8 12.1C21.8 12 21.8 11.9 21.8 11.8C21.8 10.8 21 10 20 10Z" fill="#9ca3af"/>
          <path d="M20 24C20.8 24 21.5 23.3 21.5 22.5H18.5C18.5 23.3 19.2 24 20 24Z" fill="#9ca3af"/>
          <circle cx="26" cy="14" r="4" fill="#ef4444"/>
        </svg>
      </div>
    </div>

    <!-- Design 6: Futurista -->
    <div class="design-item">
      <h3>Design 6: Futurista</h3>
      <div class="bell-preview dark-bg">
        <svg class="bell-design-6" viewBox="0 0 24 24" width="40" height="40">
          <defs>
            <linearGradient id="futureGrad" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#00f2fe;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#4facfe;stop-opacity:1" />
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          <path d="M12 2L11 3V4C8.5 4.5 6.5 6.5 6 9L5 15H3V17H21V15H19L18 9C17.5 6.5 15.5 4.5 13 4V3L12 2Z" fill="url(#futureGrad)" filter="url(#glow)" opacity="0.8"/>
          <path d="M12 20C13 20 14 19 14 18H10C10 19 11 20 12 20Z" fill="url(#futureGrad)" filter="url(#glow)"/>
          <circle cx="17" cy="7" r="2" fill="#ff0080" filter="url(#glow)"/>
        </svg>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bell-designs-showcase {
  padding: 2rem;
  background: #1a1625;
  color: #fff;
}

h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.design-item {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.design-item h3 {
  margin-bottom: 1rem;
  color: #667eea;
}

.bell-preview {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  min-width: 80px;
  min-height: 80px;
}

.bell-preview.dark-bg {
  background: #0a0a0a;
}

.bell-preview.light-bg {
  background: #f3f4f6;
}

/* Animações para demonstração */
.bell-design-1 {
  animation: subtle-bounce 2s ease-in-out infinite;
}

.bell-design-2 {
  animation: gentle-shake 3s ease-in-out infinite;
}

.bell-design-3 {
  animation: pulse 2s ease-in-out infinite;
}

.bell-design-4 {
  animation: rotate-slight 4s linear infinite;
}

.bell-design-5 {
  animation: float 3s ease-in-out infinite;
}

.bell-design-6 {
  animation: glow-pulse 2s ease-in-out infinite;
}

@keyframes subtle-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

@keyframes gentle-shake {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-5deg); }
  75% { transform: rotate(5deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.05); opacity: 1; }
}

@keyframes rotate-slight {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0) translateX(0); }
  33% { transform: translateY(-5px) translateX(2px); }
  66% { transform: translateY(3px) translateX(-2px); }
}

@keyframes glow-pulse {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.3); }
}
</style>