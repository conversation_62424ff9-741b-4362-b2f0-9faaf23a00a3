<template>
  <div class="ultra-provas-container">
    <!-- Animated Background -->
    <div class="cosmic-background">
      <div class="stars-field">
        <div v-for="i in 100" :key="`star-${i}`" class="star"></div>
      </div>
      <div class="nebula-effect"></div>
      <div class="cosmic-dust">
        <div v-for="i in 50" :key="`dust-${i}`" class="dust-particle"></div>
      </div>
      
      <!-- Animated Grid -->
      <svg class="grid-background" viewBox="0 0 1000 1000">
        <defs>
          <linearGradient id="grid-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#6366f1;stop-opacity:0.1" />
            <stop offset="100%" style="stop-color:#ec4899;stop-opacity:0.05" />
          </linearGradient>
        </defs>
        <g opacity="0.3">
          <line v-for="i in 20" :key="`h-${i}`" 
                :x1="0" :y1="i * 50" 
                :x2="1000" :y2="i * 50" 
                stroke="url(#grid-gradient)" stroke-width="1"/>
          <line v-for="i in 20" :key="`v-${i}`" 
                :x1="i * 50" :y1="0" 
                :x2="i * 50" :y2="1000" 
                stroke="url(#grid-gradient)" stroke-width="1"/>
        </g>
      </svg>
    </div>

    <!-- Ultra Header -->
    <header class="ultra-header">
      <div class="header-glass">
        <div class="header-content">
          <div class="title-section">
            <div class="holographic-icon">
              <div class="icon-layers">
                <font-awesome-icon icon="graduation-cap" class="icon-layer layer-1" />
                <font-awesome-icon icon="graduation-cap" class="icon-layer layer-2" />
                <font-awesome-icon icon="graduation-cap" class="icon-layer layer-3" />
              </div>
              <div class="icon-particles">
                <span v-for="i in 6" :key="i" class="particle"></span>
              </div>
            </div>
            
            <div class="title-wrapper">
              <h1 class="ultra-title">
                <span class="title-word">PROVAS</span>
                <span class="title-accent">&</span>
                <span class="title-word">SIMULADOS</span>
              </h1>
              <p class="ultra-subtitle">Sistema Inteligente de Avaliação e Performance</p>
            </div>
          </div>
          
          <!-- AI Assistant Button -->
          <button class="ai-assistant-btn" @click="toggleAIAssistant">
            <div class="ai-icon-wrapper">
              <font-awesome-icon icon="robot" class="ai-icon" />
              <div class="ai-pulse"></div>
            </div>
            <span>AI Coach</span>
          </button>
        </div>
        
        <!-- Live Stats Bar -->
        <div class="live-stats-bar">
          <div class="stat-item" v-for="stat in liveStats" :key="stat.id">
            <div class="stat-icon-wrapper">
              <font-awesome-icon :icon="stat.icon" class="stat-icon" />
            </div>
            <div class="stat-info">
              <span class="stat-value">{{ stat.value }}</span>
              <span class="stat-label">{{ stat.label }}</span>
            </div>
            <div class="stat-trend" :class="stat.trend">
              <font-awesome-icon :icon="stat.trend === 'up' ? 'arrow-up' : 'arrow-down'" />
              {{ stat.change }}%
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- AI Assistant Panel -->
    <transition name="slide-fade">
      <div v-if="showAIAssistant" class="ai-assistant-panel">
        <div class="assistant-header">
          <h3>AI Performance Coach</h3>
          <button @click="showAIAssistant = false" class="close-btn">
            <font-awesome-icon icon="times" />
          </button>
        </div>
        
        <div class="ai-features">
          <div class="ai-feature-card" @click="generateSmartTest">
            <div class="feature-icon">
              <font-awesome-icon icon="brain" />
            </div>
            <h4>Teste Adaptativo</h4>
            <p>Gera provas personalizadas baseadas no seu desempenho</p>
          </div>
          
          <div class="ai-feature-card" @click="analyzeWeakPoints">
            <div class="feature-icon">
              <font-awesome-icon icon="chart-line" />
            </div>
            <h4>Análise de Pontos Fracos</h4>
            <p>Identifica áreas que precisam de mais estudo</p>
          </div>
          
          <div class="ai-feature-card" @click="predictPerformance">
            <div class="feature-icon">
              <font-awesome-icon icon="crystal-ball" />
            </div>
            <h4>Previsão de Performance</h4>
            <p>Estima seu desempenho em provas futuras</p>
          </div>
          
          <div class="ai-feature-card" @click="generateStudyPlan">
            <div class="feature-icon">
              <font-awesome-icon icon="calendar-alt" />
            </div>
            <h4>Plano de Estudo IA</h4>
            <p>Cria um plano otimizado baseado em suas metas</p>
          </div>
        </div>
      </div>
    </transition>

    <!-- Futuristic Navigation -->
    <nav class="ultra-nav">
      <div class="nav-glass">
        <button 
          v-for="tab in navigationTabs" 
          :key="tab.id"
          @click="activeTab = tab.id"
          class="nav-tab"
          :class="{ active: activeTab === tab.id }"
        >
          <div class="tab-content">
            <div class="tab-icon-wrapper">
              <font-awesome-icon :icon="tab.icon" class="tab-icon" />
              <div class="tab-glow"></div>
            </div>
            <span class="tab-label">{{ tab.label }}</span>
            <div class="tab-indicator"></div>
          </div>
        </button>
      </div>
    </nav>

    <!-- Dynamic Content Area -->
    <main class="ultra-main">
      <!-- Quick Test Section -->
      <section v-if="activeTab === 'quick-test'" class="quick-test-section">
        <div class="section-glass">
          <div class="test-launcher">
            <h2 class="section-title">Teste Rápido Inteligente</h2>
            
            <div class="test-options">
              <div class="option-card" v-for="option in testOptions" :key="option.id">
                <div class="option-header" :style="{ background: option.gradient }">
                  <font-awesome-icon :icon="option.icon" class="option-icon" />
                  <h3>{{ option.name }}</h3>
                </div>
                <p class="option-description">{{ option.description }}</p>
                <div class="option-stats">
                  <span><font-awesome-icon icon="clock" /> {{ option.duration }}</span>
                  <span><font-awesome-icon icon="tasks" /> {{ option.questions }} questões</span>
                </div>
                <button @click="startTest(option)" class="start-btn">
                  <span>Iniciar</span>
                  <font-awesome-icon icon="play" />
                </button>
              </div>
            </div>
            
            <!-- Custom Test Builder -->
            <div class="custom-test-builder">
              <h3>Criar Teste Personalizado</h3>
              <div class="builder-controls">
                <div class="control-group">
                  <label>Disciplinas</label>
                  <div class="multi-select">
                    <div 
                      v-for="subject in availableSubjects" 
                      :key="subject"
                      @click="toggleSubject(subject)"
                      class="select-chip"
                      :class="{ selected: selectedSubjects.includes(subject) }"
                    >
                      {{ subject }}
                    </div>
                  </div>
                </div>
                
                <div class="control-group">
                  <label>Número de Questões</label>
                  <div class="range-slider">
                    <input 
                      type="range" 
                      v-model="customTest.questions" 
                      min="5" 
                      max="100" 
                      step="5"
                    />
                    <span class="range-value">{{ customTest.questions }}</span>
                  </div>
                </div>
                
                <div class="control-group">
                  <label>Dificuldade</label>
                  <div class="difficulty-selector">
                    <button 
                      v-for="level in difficultyLevels" 
                      :key="level.id"
                      @click="customTest.difficulty = level.id"
                      class="difficulty-btn"
                      :class="{ active: customTest.difficulty === level.id }"
                      :style="{ '--color': level.color }"
                    >
                      <font-awesome-icon :icon="level.icon" />
                      {{ level.name }}
                    </button>
                  </div>
                </div>
                
                <button @click="createCustomTest" class="create-test-btn">
                  <font-awesome-icon icon="magic" />
                  Gerar Teste Personalizado
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Performance Dashboard -->
      <section v-if="activeTab === 'dashboard'" class="dashboard-section">
        <div class="dashboard-grid">
          <!-- Performance Overview -->
          <div class="dashboard-card overview-card">
            <h3 class="card-title">Visão Geral de Performance</h3>
            <div class="performance-chart">
              <svg viewBox="0 0 400 200" class="chart-svg">
                <!-- Animated Line Chart -->
                <defs>
                  <linearGradient id="chart-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:#6366f1;stop-opacity:0.8" />
                    <stop offset="100%" style="stop-color:#6366f1;stop-opacity:0.1" />
                  </linearGradient>
                </defs>
                <path :d="performancePath" fill="url(#chart-gradient)" />
                <path :d="performanceLine" fill="none" stroke="#6366f1" stroke-width="3" />
                
                <!-- Data Points -->
                <circle 
                  v-for="(point, index) in performanceData" 
                  :key="index"
                  :cx="point.x" 
                  :cy="point.y" 
                  r="5" 
                  fill="#6366f1"
                  class="data-point"
                >
                  <animate attributeName="r" values="5;8;5" dur="2s" repeatCount="indefinite" />
                </circle>
              </svg>
              
              <div class="chart-labels">
                <span v-for="label in chartLabels" :key="label">{{ label }}</span>
              </div>
            </div>
            
            <div class="overview-stats">
              <div class="overview-stat">
                <span class="stat-label">Média Geral</span>
                <span class="stat-value">{{ overallAverage }}%</span>
              </div>
              <div class="overview-stat">
                <span class="stat-label">Tendência</span>
                <span class="stat-value trend" :class="performanceTrend">
                  <font-awesome-icon :icon="performanceTrend === 'up' ? 'arrow-up' : 'arrow-down'" />
                  {{ trendValue }}%
                </span>
              </div>
            </div>
          </div>
          
          <!-- Subject Radar Chart -->
          <div class="dashboard-card radar-card">
            <h3 class="card-title">Análise por Disciplina</h3>
            <div class="radar-chart">
              <svg viewBox="0 0 300 300" class="radar-svg">
                <!-- Radar Grid -->
                <g class="radar-grid">
                  <circle 
                    v-for="i in 5" 
                    :key="i"
                    cx="150" 
                    cy="150" 
                    :r="i * 25" 
                    fill="none" 
                    stroke="rgba(255,255,255,0.1)"
                  />
                  <line 
                    v-for="(subject, index) in radarSubjects" 
                    :key="subject"
                    :x1="150" 
                    :y1="150" 
                    :x2="150 + 125 * Math.cos((index * 2 * Math.PI) / radarSubjects.length - Math.PI/2)" 
                    :y2="150 + 125 * Math.sin((index * 2 * Math.PI) / radarSubjects.length - Math.PI/2)"
                    stroke="rgba(255,255,255,0.1)"
                  />
                </g>
                
                <!-- Radar Data -->
                <polygon 
                  :points="radarPoints" 
                  fill="rgba(99, 102, 241, 0.3)" 
                  stroke="#6366f1" 
                  stroke-width="2"
                />
                
                <!-- Subject Labels -->
                <text 
                  v-for="(subject, index) in radarSubjects" 
                  :key="subject"
                  :x="150 + 140 * Math.cos((index * 2 * Math.PI) / radarSubjects.length - Math.PI/2)" 
                  :y="150 + 140 * Math.sin((index * 2 * Math.PI) / radarSubjects.length - Math.PI/2)"
                  text-anchor="middle"
                  fill="white"
                  font-size="12"
                >
                  {{ subject }}
                </text>
              </svg>
            </div>
          </div>
          
          <!-- Achievement Badges -->
          <div class="dashboard-card badges-card">
            <h3 class="card-title">Conquistas Recentes</h3>
            <div class="badges-grid">
              <div 
                v-for="badge in recentBadges" 
                :key="badge.id"
                class="achievement-badge"
                :class="{ unlocked: badge.unlocked }"
              >
                <div class="badge-icon" :style="{ background: badge.color }">
                  <font-awesome-icon :icon="badge.icon" />
                </div>
                <h4>{{ badge.name }}</h4>
                <p>{{ badge.description }}</p>
                <div class="badge-progress" v-if="!badge.unlocked">
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: badge.progress + '%' }"></div>
                  </div>
                  <span>{{ badge.progress }}%</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Study Streak -->
          <div class="dashboard-card streak-card">
            <h3 class="card-title">Sequência de Estudos</h3>
            <div class="streak-display">
              <div class="streak-number">
                <span class="number">{{ currentStreak }}</span>
                <span class="label">dias</span>
              </div>
              <div class="streak-calendar">
                <div 
                  v-for="day in streakCalendar" 
                  :key="day.date"
                  class="calendar-day"
                  :class="{ active: day.studied, today: day.isToday }"
                >
                  <span class="day-number">{{ day.day }}</span>
                </div>
              </div>
              <div class="streak-motivation">
                <font-awesome-icon icon="fire" class="fire-icon" />
                <p>{{ streakMessage }}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Test History -->
      <section v-if="activeTab === 'history'" class="history-section">
        <div class="section-glass">
          <div class="history-header">
            <h2 class="section-title">Histórico de Avaliações</h2>
            
            <div class="history-filters">
              <button 
                v-for="filter in historyFilters" 
                :key="filter.id"
                @click="activeHistoryFilter = filter.id"
                class="filter-btn"
                :class="{ active: activeHistoryFilter === filter.id }"
              >
                <font-awesome-icon :icon="filter.icon" />
                {{ filter.label }}
              </button>
            </div>
          </div>
          
          <div class="history-timeline">
            <div 
              v-for="(test, index) in filteredHistory" 
              :key="test.id"
              class="timeline-item"
              :class="{ 'left': index % 2 === 0, 'right': index % 2 === 1 }"
            >
              <div class="timeline-marker"></div>
              <div class="timeline-content">
                <div class="test-header">
                  <h3>{{ test.subject }}</h3>
                  <span class="test-date">{{ formatDate(test.date) }}</span>
                </div>
                
                <div class="test-stats">
                  <div class="stat">
                    <font-awesome-icon icon="percentage" />
                    <span>{{ test.score }}%</span>
                  </div>
                  <div class="stat">
                    <font-awesome-icon icon="clock" />
                    <span>{{ test.duration }}</span>
                  </div>
                  <div class="stat">
                    <font-awesome-icon icon="tasks" />
                    <span>{{ test.questions }} questões</span>
                  </div>
                </div>
                
                <div class="test-performance">
                  <div class="performance-bar">
                    <div 
                      class="performance-fill" 
                      :style="{ 
                        width: test.score + '%', 
                        background: getScoreColor(test.score) 
                      }"
                    ></div>
                  </div>
                </div>
                
                <button @click="viewTestDetails(test)" class="details-btn">
                  Ver Detalhes
                  <font-awesome-icon icon="arrow-right" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Analytics Section -->
      <section v-if="activeTab === 'analytics'" class="analytics-section">
        <div class="analytics-grid">
          <!-- Heatmap Calendar -->
          <div class="analytics-card heatmap-card">
            <h3 class="card-title">Mapa de Atividades</h3>
            <div class="heatmap-container">
              <div class="heatmap-months">
                <span v-for="month in heatmapMonths" :key="month">{{ month }}</span>
              </div>
              <div class="heatmap-grid">
                <div 
                  v-for="day in heatmapData" 
                  :key="day.date"
                  class="heatmap-cell"
                  :style="{ background: getHeatmapColor(day.activity) }"
                  :title="`${day.date}: ${day.activity} atividades`"
                ></div>
              </div>
              <div class="heatmap-legend">
                <span>Menos</span>
                <div class="legend-cells">
                  <div v-for="i in 5" :key="i" class="legend-cell" :style="{ background: getHeatmapColor(i * 2) }"></div>
                </div>
                <span>Mais</span>
              </div>
            </div>
          </div>
          
          <!-- Subject Distribution -->
          <div class="analytics-card distribution-card">
            <h3 class="card-title">Distribuição por Disciplina</h3>
            <div class="donut-chart">
              <svg viewBox="0 0 200 200" class="donut-svg">
                <g transform="translate(100, 100)">
                  <circle 
                    v-for="(segment, index) in donutSegments" 
                    :key="index"
                    r="70" 
                    fill="none" 
                    :stroke="segment.color" 
                    stroke-width="30"
                    :stroke-dasharray="`${segment.value} ${300 - segment.value}`"
                    :stroke-dashoffset="-segment.offset"
                    class="donut-segment"
                  />
                </g>
                <text x="100" y="100" text-anchor="middle" fill="white" font-size="24" font-weight="bold">
                  {{ totalTests }}
                </text>
                <text x="100" y="120" text-anchor="middle" fill="white" font-size="12" opacity="0.7">
                  Total de Testes
                </text>
              </svg>
              
              <div class="donut-legend">
                <div v-for="subject in subjectDistribution" :key="subject.name" class="legend-item">
                  <div class="color-box" :style="{ background: subject.color }"></div>
                  <span class="subject-name">{{ subject.name }}</span>
                  <span class="subject-percentage">{{ subject.percentage }}%</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Predictive Analytics -->
          <div class="analytics-card prediction-card">
            <h3 class="card-title">Previsão de Performance</h3>
            <div class="prediction-content">
              <div class="prediction-chart">
                <svg viewBox="0 0 400 200" class="prediction-svg">
                  <!-- Historical Data -->
                  <path :d="historicalPath" fill="none" stroke="#6366f1" stroke-width="2" />
                  
                  <!-- Prediction -->
                  <path :d="predictionPath" fill="none" stroke="#ec4899" stroke-width="2" stroke-dasharray="5,5" />
                  
                  <!-- Confidence Interval -->
                  <path :d="confidenceInterval" fill="rgba(236, 72, 153, 0.1)" />
                </svg>
              </div>
              
              <div class="prediction-insights">
                <h4>Insights da IA</h4>
                <ul>
                  <li v-for="insight in aiInsights" :key="insight">{{ insight }}</li>
                </ul>
              </div>
              
              <div class="prediction-actions">
                <button @click="updatePrediction" class="action-btn">
                  <font-awesome-icon icon="sync" />
                  Atualizar Previsão
                </button>
                <button @click="exportAnalytics" class="action-btn">
                  <font-awesome-icon icon="download" />
                  Exportar Relatório
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Floating Action Button -->
    <div class="fab-container">
      <button @click="toggleQuickActions" class="fab-main">
        <font-awesome-icon :icon="fabOpen ? 'times' : 'plus'" />
      </button>
      
      <transition-group name="fab-items">
        <button 
          v-if="fabOpen"
          v-for="(action, index) in quickActions" 
          :key="action.id"
          @click="executeQuickAction(action)"
          class="fab-item"
          :style="{ transform: `translateY(-${(index + 1) * 60}px)` }"
        >
          <font-awesome-icon :icon="action.icon" />
          <span class="fab-tooltip">{{ action.label }}</span>
        </button>
      </transition-group>
    </div>

    <!-- Test Modal -->
    <transition name="modal-fade">
      <div v-if="showTestModal" class="test-modal-overlay" @click="closeTestModal">
        <div class="test-modal" @click.stop>
          <div class="modal-header">
            <h2>{{ currentTest.subject }}</h2>
            <button @click="closeTestModal" class="close-btn">
              <font-awesome-icon icon="times" />
            </button>
          </div>
          
          <div class="test-progress">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: testProgress + '%' }"></div>
            </div>
            <div class="progress-info">
              <span>Questão {{ currentQuestion + 1 }} de {{ currentTest.questions.length }}</span>
              <span class="timer">{{ formatTime(timeRemaining) }}</span>
            </div>
          </div>
          
          <div class="question-container">
            <h3 class="question-text">{{ currentTest.questions[currentQuestion].text }}</h3>
            
            <div class="answer-options">
              <button 
                v-for="(option, index) in currentTest.questions[currentQuestion].options" 
                :key="index"
                @click="selectAnswer(index)"
                class="answer-option"
                :class="{ selected: selectedAnswer === index }"
              >
                <span class="option-letter">{{ String.fromCharCode(65 + index) }}</span>
                <span class="option-text">{{ option }}</span>
              </button>
            </div>
          </div>
          
          <div class="test-actions">
            <button @click="previousQuestion" :disabled="currentQuestion === 0" class="nav-btn">
              <font-awesome-icon icon="chevron-left" />
              Anterior
            </button>
            <button @click="flagQuestion" class="flag-btn" :class="{ flagged: currentTest.questions[currentQuestion].flagged }">
              <font-awesome-icon icon="flag" />
              Marcar
            </button>
            <button @click="nextQuestion" class="nav-btn primary">
              {{ currentQuestion === currentTest.questions.length - 1 ? 'Finalizar' : 'Próxima' }}
              <font-awesome-icon icon="chevron-right" />
            </button>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useToast } from 'vue-toastification'

export default {
  name: 'UltraProvasSimulados',
  setup() {
    const store = useStore()
    const toast = useToast()
    
    // State
    const activeTab = ref('dashboard')
    const showAIAssistant = ref(false)
    const showTestModal = ref(false)
    const activeHistoryFilter = ref('all')
    const fabOpen = ref(false)
    const selectedSubjects = ref([])
    const currentQuestion = ref(0)
    const selectedAnswer = ref(null)
    const timeRemaining = ref(3600) // 1 hour in seconds
    const currentStreak = ref(15)
    
    // Custom Test
    const customTest = reactive({
      questions: 20,
      difficulty: 'medium'
    })
    
    // Current Test
    const currentTest = ref({
      subject: '',
      questions: []
    })
    
    // Navigation Tabs
    const navigationTabs = ref([
      { id: 'dashboard', label: 'Dashboard', icon: 'chart-line' },
      { id: 'quick-test', label: 'Teste Rápido', icon: 'bolt' },
      { id: 'history', label: 'Histórico', icon: 'history' },
      { id: 'analytics', label: 'Analytics', icon: 'chart-bar' }
    ])
    
    // Live Stats
    const liveStats = ref([
      { id: 1, icon: 'trophy', label: 'Ranking', value: '#12', trend: 'up', change: 3 },
      { id: 2, icon: 'fire', label: 'Sequência', value: '15 dias', trend: 'up', change: 7 },
      { id: 3, icon: 'graduation-cap', label: 'XP Total', value: '2,450', trend: 'up', change: 12 },
      { id: 4, icon: 'medal', label: 'Conquistas', value: '28', trend: 'up', change: 2 }
    ])
    
    // Test Options
    const testOptions = ref([
      {
        id: 1,
        name: 'Teste Rápido',
        description: 'Questões aleatórias para prática rápida',
        icon: 'bolt',
        duration: '15 min',
        questions: 10,
        gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      },
      {
        id: 2,
        name: 'Simulado Completo',
        description: 'Experiência completa de prova',
        icon: 'clipboard-list',
        duration: '3 horas',
        questions: 100,
        gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
      },
      {
        id: 3,
        name: 'Revisão Inteligente',
        description: 'Foco em suas áreas fracas',
        icon: 'brain',
        duration: '30 min',
        questions: 20,
        gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
      },
      {
        id: 4,
        name: 'Desafio Diário',
        description: 'Novas questões todos os dias',
        icon: 'calendar-day',
        duration: '20 min',
        questions: 15,
        gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
      }
    ])
    
    // Available Subjects
    const availableSubjects = ref([
      'Matemática', 'Física', 'Química', 'Biologia', 
      'História', 'Geografia', 'Português', 'Literatura',
      'Inglês', 'Filosofia', 'Sociologia', 'Artes'
    ])
    
    // Difficulty Levels
    const difficultyLevels = ref([
      { id: 'easy', name: 'Fácil', icon: 'smile', color: '#10b981' },
      { id: 'medium', name: 'Médio', icon: 'meh', color: '#f59e0b' },
      { id: 'hard', name: 'Difícil', icon: 'frown', color: '#ef4444' },
      { id: 'expert', name: 'Expert', icon: 'skull', color: '#dc2626' }
    ])
    
    // Performance Data
    const performanceData = ref([
      { x: 50, y: 150 },
      { x: 100, y: 120 },
      { x: 150, y: 100 },
      { x: 200, y: 80 },
      { x: 250, y: 60 },
      { x: 300, y: 40 },
      { x: 350, y: 30 }
    ])
    
    // Radar Subjects
    const radarSubjects = ref(['Mat', 'Fís', 'Quí', 'Bio', 'His', 'Geo'])
    const radarData = ref([85, 72, 90, 68, 95, 80])
    
    // Recent Badges
    const recentBadges = ref([
      {
        id: 1,
        name: 'Mestre da Velocidade',
        description: 'Complete 10 testes rápidos',
        icon: 'bolt',
        color: '#6366f1',
        unlocked: true,
        progress: 100
      },
      {
        id: 2,
        name: 'Estudante Dedicado',
        description: '30 dias de sequência',
        icon: 'fire',
        color: '#ef4444',
        unlocked: false,
        progress: 50
      },
      {
        id: 3,
        name: 'Gênio da Matemática',
        description: '95% em 5 testes de matemática',
        icon: 'calculator',
        color: '#10b981',
        unlocked: false,
        progress: 80
      }
    ])
    
    // History Filters
    const historyFilters = ref([
      { id: 'all', label: 'Todos', icon: 'list' },
      { id: 'week', label: 'Esta Semana', icon: 'calendar-week' },
      { id: 'month', label: 'Este Mês', icon: 'calendar-alt' },
      { id: 'best', label: 'Melhores', icon: 'star' }
    ])
    
    // Test History
    const testHistory = ref([
      {
        id: 1,
        subject: 'Matemática - Cálculo Diferencial',
        date: new Date(),
        score: 92,
        duration: '45 min',
        questions: 30
      },
      {
        id: 2,
        subject: 'Física - Mecânica Quântica',
        date: new Date(Date.now() - 86400000),
        score: 78,
        duration: '60 min',
        questions: 40
      },
      {
        id: 3,
        subject: 'Química - Orgânica',
        date: new Date(Date.now() - 172800000),
        score: 85,
        duration: '50 min',
        questions: 35
      }
    ])
    
    // Heatmap Data
    const heatmapData = ref(
      Array.from({ length: 365 }, (_, i) => ({
        date: new Date(Date.now() - i * 86400000).toISOString().split('T')[0],
        activity: Math.floor(Math.random() * 10)
      }))
    )
    
    // Subject Distribution
    const subjectDistribution = ref([
      { name: 'Matemática', percentage: 30, color: '#6366f1' },
      { name: 'Física', percentage: 25, color: '#ec4899' },
      { name: 'Química', percentage: 20, color: '#10b981' },
      { name: 'Biologia', percentage: 15, color: '#f59e0b' },
      { name: 'História', percentage: 10, color: '#ef4444' }
    ])
    
    // AI Insights
    const aiInsights = ref([
      'Seu desempenho em Matemática melhorou 15% no último mês',
      'Recomendamos focar em Física Quântica esta semana',
      'Você está no top 10% dos estudantes em Química'
    ])
    
    // Quick Actions
    const quickActions = ref([
      { id: 1, icon: 'rocket', label: 'Iniciar Teste Rápido' },
      { id: 2, icon: 'book', label: 'Revisar Erros' },
      { id: 3, icon: 'chart-line', label: 'Ver Relatório' },
      { id: 4, icon: 'share', label: 'Compartilhar' }
    ])
    
    // Computed
    const overallAverage = computed(() => {
      if (testHistory.value.length === 0) return 0
      const sum = testHistory.value.reduce((acc, test) => acc + test.score, 0)
      return Math.round(sum / testHistory.value.length)
    })
    
    const performanceTrend = computed(() => {
      if (testHistory.value.length < 2) return 'neutral'
      const recent = testHistory.value.slice(0, 5).reduce((acc, t) => acc + t.score, 0) / 5
      const older = testHistory.value.slice(5, 10).reduce((acc, t) => acc + t.score, 0) / 5
      return recent > older ? 'up' : 'down'
    })
    
    const trendValue = computed(() => {
      return performanceTrend.value === 'up' ? 12 : -8
    })
    
    const chartLabels = computed(() => {
      return ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul']
    })
    
    const performancePath = computed(() => {
      const points = performanceData.value.map(p => `${p.x},${p.y}`).join(' ')
      return `M ${points} L 350,200 L 50,200 Z`
    })
    
    const performanceLine = computed(() => {
      const points = performanceData.value.map(p => `${p.x},${p.y}`).join(' ')
      return `M ${points}`
    })
    
    const radarPoints = computed(() => {
      return radarData.value.map((value, index) => {
        const angle = (index * 2 * Math.PI) / radarSubjects.value.length - Math.PI/2
        const x = 150 + (value / 100) * 125 * Math.cos(angle)
        const y = 150 + (value / 100) * 125 * Math.sin(angle)
        return `${x},${y}`
      }).join(' ')
    })
    
    const streakCalendar = computed(() => {
      const days = []
      for (let i = 29; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        days.push({
          date: date.toISOString().split('T')[0],
          day: date.getDate(),
          studied: i < currentStreak.value,
          isToday: i === 0
        })
      }
      return days
    })
    
    const streakMessage = computed(() => {
      if (currentStreak.value >= 30) return 'Incrível! Um mês completo!'
      if (currentStreak.value >= 7) return 'Ótimo trabalho! Continue assim!'
      return 'Construa seu hábito de estudo!'
    })
    
    const filteredHistory = computed(() => {
      let filtered = [...testHistory.value]
      
      switch (activeHistoryFilter.value) {
        case 'week':
          const weekAgo = new Date(Date.now() - 7 * 86400000)
          filtered = filtered.filter(test => test.date >= weekAgo)
          break
        case 'month':
          const monthAgo = new Date(Date.now() - 30 * 86400000)
          filtered = filtered.filter(test => test.date >= monthAgo)
          break
        case 'best':
          filtered = filtered.sort((a, b) => b.score - a.score).slice(0, 10)
          break
      }
      
      return filtered
    })
    
    const totalTests = computed(() => {
      return testHistory.value.length
    })
    
    const donutSegments = computed(() => {
      let offset = 0
      return subjectDistribution.value.map(subject => {
        const value = (subject.percentage / 100) * 300
        const segment = {
          value,
          offset,
          color: subject.color
        }
        offset += value
        return segment
      })
    })
    
    const heatmapMonths = computed(() => {
      return ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
    })
    
    const historicalPath = computed(() => {
      // Mock historical data path
      return 'M 50,150 Q 150,100 250,80'
    })
    
    const predictionPath = computed(() => {
      // Mock prediction path
      return 'M 250,80 Q 300,60 350,40'
    })
    
    const confidenceInterval = computed(() => {
      // Mock confidence interval
      return 'M 250,70 Q 300,50 350,30 L 350,50 Q 300,70 250,90 Z'
    })
    
    const testProgress = computed(() => {
      if (!currentTest.value.questions.length) return 0
      return ((currentQuestion.value + 1) / currentTest.value.questions.length) * 100
    })
    
    // Methods
    const toggleAIAssistant = () => {
      showAIAssistant.value = !showAIAssistant.value
    }
    
    const generateSmartTest = () => {
      toast.info('Gerando teste adaptativo...')
      // AI test generation logic
    }
    
    const analyzeWeakPoints = () => {
      toast.info('Analisando pontos fracos...')
      // Weak points analysis logic
    }
    
    const predictPerformance = () => {
      toast.info('Calculando previsão de performance...')
      // Performance prediction logic
    }
    
    const generateStudyPlan = () => {
      toast.info('Criando plano de estudo personalizado...')
      // Study plan generation logic
    }
    
    const startTest = (option) => {
      currentTest.value = {
        subject: option.name,
        questions: generateQuestions(option.questions)
      }
      currentQuestion.value = 0
      selectedAnswer.value = null
      showTestModal.value = true
      startTimer()
    }
    
    const toggleSubject = (subject) => {
      const index = selectedSubjects.value.indexOf(subject)
      if (index > -1) {
        selectedSubjects.value.splice(index, 1)
      } else {
        selectedSubjects.value.push(subject)
      }
    }
    
    const createCustomTest = () => {
      if (selectedSubjects.value.length === 0) {
        toast.warning('Selecione pelo menos uma disciplina')
        return
      }
      
      toast.success('Teste personalizado criado!')
      // Custom test creation logic
    }
    
    const formatDate = (date) => {
      return new Intl.DateTimeFormat('pt-BR', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      }).format(date)
    }
    
    const getScoreColor = (score) => {
      if (score >= 90) return 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
      if (score >= 70) return 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)'
      if (score >= 50) return 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
      return 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
    }
    
    const getHeatmapColor = (activity) => {
      const intensity = activity / 10
      return `rgba(99, 102, 241, ${intensity})`
    }
    
    const viewTestDetails = (test) => {
      toast.info(`Visualizando detalhes: ${test.subject}`)
    }
    
    const updatePrediction = () => {
      toast.info('Atualizando previsões...')
    }
    
    const exportAnalytics = () => {
      toast.success('Relatório exportado!')
    }
    
    const toggleQuickActions = () => {
      fabOpen.value = !fabOpen.value
    }
    
    const executeQuickAction = (action) => {
      toast.info(`Executando: ${action.label}`)
      fabOpen.value = false
    }
    
    const closeTestModal = () => {
      showTestModal.value = false
      stopTimer()
    }
    
    const selectAnswer = (index) => {
      selectedAnswer.value = index
    }
    
    const previousQuestion = () => {
      if (currentQuestion.value > 0) {
        currentQuestion.value--
        selectedAnswer.value = null
      }
    }
    
    const nextQuestion = () => {
      if (currentQuestion.value < currentTest.value.questions.length - 1) {
        currentQuestion.value++
        selectedAnswer.value = null
      } else {
        finishTest()
      }
    }
    
    const flagQuestion = () => {
      currentTest.value.questions[currentQuestion.value].flagged = 
        !currentTest.value.questions[currentQuestion.value].flagged
    }
    
    const finishTest = () => {
      toast.success('Teste finalizado!')
      closeTestModal()
    }
    
    const generateQuestions = (count) => {
      // Mock question generation
      return Array.from({ length: count }, (_, i) => ({
        id: i + 1,
        text: `Questão ${i + 1}: Esta é uma questão de exemplo sobre o conteúdo estudado.`,
        options: [
          'Opção A - Resposta possível',
          'Opção B - Outra resposta',
          'Opção C - Mais uma alternativa',
          'Opção D - Última opção'
        ],
        correct: Math.floor(Math.random() * 4),
        flagged: false
      }))
    }
    
    const formatTime = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    
    let timerInterval = null
    
    const startTimer = () => {
      timerInterval = setInterval(() => {
        if (timeRemaining.value > 0) {
          timeRemaining.value--
        } else {
          finishTest()
        }
      }, 1000)
    }
    
    const stopTimer = () => {
      if (timerInterval) {
        clearInterval(timerInterval)
        timerInterval = null
      }
    }
    
    // Lifecycle
    onMounted(() => {
      // Initialize data
    })
    
    onUnmounted(() => {
      stopTimer()
    })
    
    return {
      activeTab,
      showAIAssistant,
      showTestModal,
      activeHistoryFilter,
      fabOpen,
      selectedSubjects,
      currentQuestion,
      selectedAnswer,
      timeRemaining,
      currentStreak,
      customTest,
      currentTest,
      navigationTabs,
      liveStats,
      testOptions,
      availableSubjects,
      difficultyLevels,
      performanceData,
      radarSubjects,
      radarData,
      recentBadges,
      historyFilters,
      testHistory,
      heatmapData,
      subjectDistribution,
      aiInsights,
      quickActions,
      overallAverage,
      performanceTrend,
      trendValue,
      chartLabels,
      performancePath,
      performanceLine,
      radarPoints,
      streakCalendar,
      streakMessage,
      filteredHistory,
      totalTests,
      donutSegments,
      heatmapMonths,
      historicalPath,
      predictionPath,
      confidenceInterval,
      testProgress,
      toggleAIAssistant,
      generateSmartTest,
      analyzeWeakPoints,
      predictPerformance,
      generateStudyPlan,
      startTest,
      toggleSubject,
      createCustomTest,
      formatDate,
      getScoreColor,
      getHeatmapColor,
      viewTestDetails,
      updatePrediction,
      exportAnalytics,
      toggleQuickActions,
      executeQuickAction,
      closeTestModal,
      selectAnswer,
      previousQuestion,
      nextQuestion,
      flagQuestion,
      formatTime,
      Math // For template calculations
    }
  }
}
</script>

<style scoped>
/* Container */
.ultra-provas-container {
  position: relative;
  min-height: 100vh;
  background: #0a0a0a;
  color: white;
  overflow-x: hidden;
}

/* Cosmic Background */
.cosmic-background {
  position: fixed;
  inset: 0;
  z-index: 0;
  pointer-events: none;
}

.stars-field {
  position: absolute;
  width: 100%;
  height: 100%;
}

.star {
  position: absolute;
  width: 2px;
  height: 2px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  animation: twinkle 3s infinite;
}

.star:nth-child(odd) {
  width: 1px;
  height: 1px;
  animation-duration: 4s;
}

.star:nth-child(even) {
  animation-delay: 1s;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.nebula-effect {
  position: absolute;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(ellipse at 20% 30%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 70%, rgba(236, 72, 153, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 50% 50%, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
  filter: blur(60px);
}

.cosmic-dust {
  position: absolute;
  width: 100%;
  height: 100%;
}

.dust-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  filter: blur(1px);
  animation: float-dust 20s infinite linear;
}

@keyframes float-dust {
  from {
    transform: translateY(100vh) translateX(0) rotate(0deg);
  }
  to {
    transform: translateY(-100vh) translateX(100px) rotate(360deg);
  }
}

.grid-background {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.1;
}

/* Ultra Header */
.ultra-header {
  position: relative;
  z-index: 10;
  padding: 2rem;
}

.header-glass {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.holographic-icon {
  position: relative;
  width: 80px;
  height: 80px;
}

.icon-layers {
  position: relative;
  width: 100%;
  height: 100%;
}

.icon-layer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 3rem;
}

.layer-1 {
  color: #6366f1;
  animation: float-icon 3s ease-in-out infinite;
}

.layer-2 {
  color: #ec4899;
  animation: float-icon 3s ease-in-out infinite 0.1s;
  opacity: 0.7;
}

.layer-3 {
  color: #10b981;
  animation: float-icon 3s ease-in-out infinite 0.2s;
  opacity: 0.5;
}

@keyframes float-icon {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -55%) scale(1.1); }
}

.icon-particles {
  position: absolute;
  inset: -20px;
}

.icon-particles span {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #6366f1;
  border-radius: 50%;
  animation: orbit-particle 4s linear infinite;
}

@keyframes orbit-particle {
  from {
    transform: rotate(0deg) translateX(40px) rotate(0deg);
  }
  to {
    transform: rotate(360deg) translateX(40px) rotate(-360deg);
  }
}

.title-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ultra-title {
  font-size: 3.5rem;
  font-weight: 900;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #6366f1 0%, #ec4899 50%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(99, 102, 241, 0.5);
}

.title-accent {
  color: white;
  -webkit-text-fill-color: white;
}

.ultra-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.ai-assistant-btn {
  position: relative;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  overflow: hidden;
}

.ai-assistant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4);
}

.ai-icon-wrapper {
  position: relative;
  width: 30px;
  height: 30px;
}

.ai-icon {
  position: relative;
  z-index: 2;
  font-size: 1.5rem;
}

.ai-pulse {
  position: absolute;
  inset: -5px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 0;
  }
}

/* Live Stats Bar */
.live-stats-bar {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.stat-icon-wrapper {
  width: 40px;
  height: 40px;
  background: rgba(99, 102, 241, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon {
  color: #6366f1;
  font-size: 1.2rem;
}

.stat-info {
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 1.3rem;
  font-weight: 700;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.9rem;
  font-weight: 600;
}

.stat-trend.up {
  color: #10b981;
}

.stat-trend.down {
  color: #ef4444;
}

/* AI Assistant Panel */
.ai-assistant-panel {
  position: relative;
  z-index: 10;
  margin: 2rem;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem;
  animation: slideDown 0.3s ease-out;
}

.assistant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.assistant-header h3 {
  font-size: 1.5rem;
  margin: 0;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

.ai-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.ai-feature-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.ai-feature-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.ai-feature-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
}

.ai-feature-card p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
}

/* Ultra Navigation */
.ultra-nav {
  position: relative;
  z-index: 10;
  padding: 0 2rem 2rem;
}

.nav-glass {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem;
  display: flex;
  gap: 0.5rem;
}

.nav-tab {
  flex: 1;
  background: transparent;
  border: none;
  padding: 1rem;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 16px;
}

.nav-tab:hover {
  background: rgba(255, 255, 255, 0.05);
}

.nav-tab.active {
  background: rgba(99, 102, 241, 0.2);
  color: white;
}

.tab-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.tab-icon-wrapper {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-icon {
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.nav-tab.active .tab-icon {
  color: #6366f1;
}

.tab-glow {
  position: absolute;
  inset: -10px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-tab.active .tab-glow {
  opacity: 1;
  animation: glow 2s infinite;
}

.tab-label {
  font-size: 0.9rem;
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: -0.5rem;
  width: 40px;
  height: 3px;
  background: #6366f1;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-tab.active .tab-indicator {
  opacity: 1;
}

/* Main Content */
.ultra-main {
  position: relative;
  z-index: 10;
  padding: 0 2rem 2rem;
}

/* Section Glass */
.section-glass {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem;
}

.section-title {
  font-size: 2rem;
  margin: 0 0 2rem 0;
  background: linear-gradient(135deg, #6366f1 0%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Quick Test Section */
.test-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.option-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.option-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.option-header {
  padding: 2rem;
  text-align: center;
}

.option-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.option-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.option-description {
  padding: 0 2rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.option-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  padding: 1rem 2rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.start-btn {
  width: 100%;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.start-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Custom Test Builder */
.custom-test-builder {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-test-builder h3 {
  margin: 0 0 2rem 0;
  font-size: 1.5rem;
}

.builder-controls {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.control-group label {
  display: block;
  margin-bottom: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.multi-select {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.select-chip {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-chip:hover {
  background: rgba(255, 255, 255, 0.1);
}

.select-chip.selected {
  background: #6366f1;
  border-color: #6366f1;
}

.range-slider {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.range-slider input {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.range-slider input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: #6366f1;
  border-radius: 50%;
  cursor: pointer;
}

.range-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #6366f1;
  min-width: 60px;
  text-align: center;
}

.difficulty-selector {
  display: flex;
  gap: 1rem;
}

.difficulty-btn {
  flex: 1;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.difficulty-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.difficulty-btn.active {
  background: var(--color);
  border-color: var(--color);
}

.create-test-btn {
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.create-test-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4);
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.dashboard-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem;
}

.card-title {
  font-size: 1.3rem;
  margin: 0 0 1.5rem 0;
  color: rgba(255, 255, 255, 0.9);
}

/* Performance Chart */
.performance-chart {
  position: relative;
  margin-bottom: 2rem;
}

.chart-svg {
  width: 100%;
  height: auto;
}

.data-point {
  cursor: pointer;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  margin-top: 1rem;
}

.overview-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.overview-stat {
  text-align: center;
}

.overview-stat .stat-label {
  display: block;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.overview-stat .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #6366f1;
}

.trend.up {
  color: #10b981;
}

.trend.down {
  color: #ef4444;
}

/* Radar Chart */
.radar-svg {
  width: 100%;
  height: auto;
}

/* Achievement Badges */
.badges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1.5rem;
}

.achievement-badge {
  text-align: center;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.achievement-badge.unlocked {
  opacity: 1;
}

.badge-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
}

.achievement-badge h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.achievement-badge p {
  margin: 0;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.badge-progress {
  margin-top: 1rem;
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: #6366f1;
  transition: width 0.3s ease;
}

/* Study Streak */
.streak-display {
  text-align: center;
}

.streak-number {
  margin-bottom: 2rem;
}

.streak-number .number {
  display: block;
  font-size: 4rem;
  font-weight: 900;
  color: #f59e0b;
}

.streak-number .label {
  display: block;
  color: rgba(255, 255, 255, 0.6);
}

.streak-calendar {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.calendar-day {
  aspect-ratio: 1;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.calendar-day.active {
  background: rgba(245, 158, 11, 0.3);
  color: #f59e0b;
}

.calendar-day.today {
  border: 2px solid #f59e0b;
}

.streak-motivation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.fire-icon {
  font-size: 2rem;
  color: #f59e0b;
}

/* History Timeline */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
}

.history-filters {
  display: flex;
  gap: 1rem;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.filter-btn.active {
  background: #6366f1;
  border-color: #6366f1;
  color: white;
}

.history-timeline {
  position: relative;
  padding: 2rem 0;
}

.history-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 3rem;
  width: 45%;
}

.timeline-item.left {
  margin-left: 0;
  text-align: right;
}

.timeline-item.right {
  margin-left: 55%;
}

.timeline-marker {
  position: absolute;
  width: 20px;
  height: 20px;
  background: #6366f1;
  border-radius: 50%;
  top: 20px;
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
}

.timeline-item.left .timeline-marker {
  right: -10px;
}

.timeline-item.right .timeline-marker {
  left: -10px;
}

.timeline-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.timeline-content:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: scale(1.02);
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.test-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.test-date {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.test-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.test-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.test-performance {
  margin-bottom: 1rem;
}

.performance-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.performance-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.details-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.details-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #6366f1;
}

/* Analytics Grid */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.analytics-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem;
}

/* Heatmap */
.heatmap-container {
  position: relative;
}

.heatmap-months {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

.heatmap-grid {
  display: grid;
  grid-template-columns: repeat(52, 1fr);
  grid-template-rows: repeat(7, 1fr);
  gap: 3px;
  margin-bottom: 1rem;
}

.heatmap-cell {
  aspect-ratio: 1;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.heatmap-cell:hover {
  transform: scale(1.2);
  z-index: 10;
}

.heatmap-legend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

.legend-cells {
  display: flex;
  gap: 3px;
}

.legend-cell {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Donut Chart */
.donut-chart {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.donut-svg {
  width: 200px;
  height: 200px;
}

.donut-segment {
  transition: all 0.3s ease;
  cursor: pointer;
}

.donut-segment:hover {
  opacity: 0.8;
}

.donut-legend {
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.color-box {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.subject-name {
  flex: 1;
}

.subject-percentage {
  font-weight: 600;
}

/* Prediction */
.prediction-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.prediction-svg {
  width: 100%;
  height: auto;
}

.prediction-insights h4 {
  margin: 0 0 1rem 0;
  color: rgba(255, 255, 255, 0.8);
}

.prediction-insights ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.prediction-insights li {
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

.prediction-insights li:before {
  content: '→';
  margin-right: 0.5rem;
  color: #6366f1;
}

.prediction-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  flex: 1;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* FAB */
.fab-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 100;
}

.fab-main {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4);
}

.fab-main:hover {
  transform: scale(1.1);
}

.fab-item {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fab-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px) !important;
}

.fab-tooltip {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  white-space: nowrap;
  font-size: 0.9rem;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.fab-item:hover .fab-tooltip {
  opacity: 1;
}

/* Test Modal */
.test-modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.test-modal {
  width: 100%;
  max-width: 800px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.modal-header {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.03);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.test-progress {
  padding: 1.5rem 2rem;
  background: rgba(255, 255, 255, 0.02);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.timer {
  color: #f59e0b;
  font-weight: 600;
}

.question-container {
  padding: 3rem 2rem;
}

.question-text {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.answer-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.answer-option {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  text-align: left;
  color: white;
}

.answer-option:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.answer-option.selected {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
}

.option-letter {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.option-text {
  flex: 1;
}

.test-actions {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.02);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-btn.primary {
  background: #6366f1;
  border-color: #6366f1;
}

.nav-btn.primary:hover {
  background: #4f46e5;
}

.flag-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.flag-btn.flagged {
  background: rgba(245, 158, 11, 0.2);
  border-color: #f59e0b;
  color: #f59e0b;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glow {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.fab-items-enter-active,
.fab-items-leave-active {
  transition: all 0.3s ease;
}

.fab-items-enter-from,
.fab-items-leave-to {
  opacity: 0;
  transform: translateY(0) !important;
}

/* Responsive */
@media (max-width: 1024px) {
  .dashboard-grid,
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .test-options {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .ultra-title {
    font-size: 2rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .live-stats-bar {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .history-timeline::before {
    left: 20px;
  }
  
  .timeline-item {
    width: calc(100% - 40px);
    margin-left: 40px !important;
  }
  
  .timeline-item.left .timeline-marker,
  .timeline-item.right .timeline-marker {
    left: -30px;
  }
  
  .donut-chart {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .nav-glass {
    flex-direction: column;
  }
  
  .nav-tab {
    width: 100%;
  }
  
  .fab-container {
    bottom: 1rem;
    right: 1rem;
  }
}
</style>