<template>
  <div class="community-page">
    <div class="page-header">
      <h1>
        <font-awesome-icon icon="fa-users" />
        Comunidade
      </h1>
      <p>Conecte-se com outros estudantes e compartilhe conhecimento</p>
    </div>
    
    <div class="coming-soon">
      <font-awesome-icon icon="fa-comments" class="icon-large" />
      <h2>Em Breve!</h2>
      <p>Funcionalidade de comunidade em desenvolvimento.</p>
      <span class="beta-badge">BETA</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Community'
}
</script>

<style scoped>
.community-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.coming-soon {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--card-bg);
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
  position: relative;
}

.icon-large {
  font-size: 4rem;
  color: var(--accent-color);
  margin-bottom: 1rem;
}

.beta-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--accent-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
}
</style>