<template>
  <button 
    class="theme-toggle"
    @click="toggleTheme"
    :aria-label="isDarkMode ? 'Ativar tema claro' : 'Ativar tema escuro'"
    :title="isDarkMode ? 'Tema Claro' : 'Te<PERSON> Escuro'">
    
    <div class="toggle-container">
      <transition name="theme-icon" mode="out-in">
        <!-- Sun Icon (Light Mode) -->
        <svg v-if="!isDarkMode" 
             key="sun"
             class="theme-icon sun-icon" 
             viewBox="0 0 24 24" 
             fill="none" 
             xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="4" stroke="currentColor" stroke-width="2"/>
          <path d="M12 2V6M12 18V22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          <path d="M22 12H18M6 12H2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          <path d="M19.07 4.93L16.24 7.76M7.76 16.24L4.93 19.07" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          <path d="M19.07 19.07L16.24 16.24M7.76 7.76L4.93 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          
          <!-- Sun rays animation -->
          <g class="sun-rays">
            <circle cx="12" cy="12" r="4" fill="currentColor" opacity="0.3" class="sun-glow"/>
          </g>
        </svg>
        
        <!-- Moon Icon (Dark Mode) -->
        <svg v-else 
             key="moon"
             class="theme-icon moon-icon" 
             viewBox="0 0 24 24" 
             fill="none" 
             xmlns="http://www.w3.org/2000/svg">
          <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z" 
                stroke="currentColor" 
                stroke-width="2" 
                stroke-linecap="round" 
                stroke-linejoin="round"
                fill="currentColor"
                fill-opacity="0.1"/>
          
          <!-- Stars -->
          <g class="stars">
            <circle cx="5" cy="5" r="1" fill="currentColor" class="star star-1"/>
            <circle cx="8" cy="3" r="0.5" fill="currentColor" class="star star-2"/>
            <circle cx="3" cy="8" r="0.5" fill="currentColor" class="star star-3"/>
          </g>
        </svg>
      </transition>
      
      <!-- Ripple effect -->
      <transition name="ripple">
        <span v-if="showRipple" class="ripple-effect"></span>
      </transition>
    </div>
  </button>
</template>

<script>
import { ref } from 'vue'
import { useTheme } from '@/composables/useTheme'

export default {
  name: 'ThemeToggle',
  
  setup() {
    const { isDarkMode, toggleTheme: toggleThemeBase } = useTheme()
    const showRipple = ref(false)
    
    const toggleTheme = () => {
      // Show ripple effect
      showRipple.value = true
      setTimeout(() => {
        showRipple.value = false
      }, 600)
      
      // Toggle theme
      toggleThemeBase()
    }
    
    return {
      isDarkMode,
      toggleTheme,
      showRipple
    }
  }
}
</script>

<style scoped>
.theme-toggle {
  position: relative;
  width: 50px;
  height: 50px;
  border: none;
  background: var(--theme-glass);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 4px 20px var(--theme-shadow);
  border: 1px solid var(--theme-border);
}

.theme-toggle:hover {
  background: var(--theme-glassHover);
  transform: scale(1.1);
  box-shadow: 0 6px 30px var(--theme-shadow);
  border-color: var(--theme-borderHover);
}

.theme-toggle:active {
  transform: scale(0.95);
}

.toggle-container {
  position: relative;
  width: 24px;
  height: 24px;
}

.theme-icon {
  width: 24px;
  height: 24px;
  color: var(--theme-primary);
}

/* Sun specific styles */
.sun-icon {
  animation: rotate-sun 20s linear infinite;
}

@keyframes rotate-sun {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.sun-glow {
  animation: sun-pulse 2s ease-in-out infinite;
}

@keyframes sun-pulse {
  0%, 100% { 
    r: 4;
    opacity: 0.3;
  }
  50% { 
    r: 8;
    opacity: 0.1;
  }
}

/* Moon specific styles */
.moon-icon {
  animation: moon-glow 3s ease-in-out infinite;
}

@keyframes moon-glow {
  0%, 100% { 
    filter: drop-shadow(0 0 5px currentColor);
  }
  50% { 
    filter: drop-shadow(0 0 15px currentColor);
  }
}

/* Stars animation */
.stars .star {
  animation: twinkle 3s ease-in-out infinite;
}

.star-1 { animation-delay: 0s; }
.star-2 { animation-delay: 1s; }
.star-3 { animation-delay: 2s; }

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* Ripple effect */
.ripple-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: var(--theme-primary);
  opacity: 0.3;
  transform: translate(-50%, -50%);
  animation: ripple-expand 0.6s ease-out;
  pointer-events: none;
}

@keyframes ripple-expand {
  to {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

/* Transitions */
.theme-icon-enter-active,
.theme-icon-leave-active {
  transition: all 0.3s ease;
}

.theme-icon-enter-from {
  opacity: 0;
  transform: scale(0.5) rotate(-90deg);
}

.theme-icon-leave-to {
  opacity: 0;
  transform: scale(0.5) rotate(90deg);
}

.ripple-enter-active,
.ripple-leave-active {
  transition: all 0.6s ease-out;
}

.ripple-enter-from,
.ripple-leave-to {
  opacity: 0;
}

/* Dark mode specific adjustments */
[data-theme="dark"] .theme-toggle {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .theme-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Light mode specific adjustments */
[data-theme="light"] .theme-toggle {
  background: rgba(0, 0, 0, 0.05);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .theme-toggle:hover {
  background: rgba(0, 0, 0, 0.1);
  box-shadow: 0 6px 30px rgba(0, 0, 0, 0.15);
}

/* Responsive */
@media (max-width: 768px) {
  .theme-toggle {
    width: 40px;
    height: 40px;
  }
  
  .toggle-container,
  .theme-icon {
    width: 20px;
    height: 20px;
  }
}
</style>