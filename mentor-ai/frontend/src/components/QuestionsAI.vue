<template>
  <div class="questions-ai-container">
    <!-- Particle Background -->
    <div class="particle-container">
      <div v-for="i in 30" :key="i" class="particle"></div>
    </div>
    
    <!-- Gradient Orbs -->
    <div class="gradient-orb gradient-orb-1"></div>
    <div class="gradient-orb gradient-orb-2"></div>
    <div class="gradient-orb gradient-orb-3"></div>
    
    <!-- Header Section -->
    <div class="questions-header">
      <div class="header-content">
        <h1 class="title">
          <i class="fas fa-tasks"></i>
          <span class="gradient-text">Questions AI</span>
        </h1>
        <p class="subtitle">Gerador inteligente de questões de concurso com IA ultra-avançada</p>
      </div>
      <div class="stats-bar glass-effect">
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-question-circle"></i>
          </div>
          <div class="stat-info">
            <span class="stat-value">{{ totalQuestions }}</span>
            <span class="stat-label">questões</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="stat-info">
            <span class="stat-value">{{ exams.length }}</span>
            <span class="stat-label">simulados</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-percentage"></i>
          </div>
          <div class="stat-info">
            <span class="stat-value">{{ averageScore }}%</span>
            <span class="stat-label">média</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <div class="stat-info">
            <span class="stat-value">{{ bestScore }}%</span>
            <span class="stat-label">melhor</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="questions-content">
      <!-- Left Panel - Generation -->
      <div class="generation-panel glass-effect">
        <div class="panel-header">
          <h2><i class="fas fa-magic"></i> Gerar Questões</h2>
          <div class="panel-glow"></div>
        </div>

        <!-- Input Methods -->
        <div class="input-methods">
          <div class="method-tabs">
            <button 
              :class="['tab', { active: activeTab === 'text' }]"
              @click="activeTab = 'text'"
            >
              <i class="fas fa-keyboard"></i> Texto
            </button>
            <button 
              :class="['tab', { active: activeTab === 'file' }]"
              @click="activeTab = 'file'"
            >
              <i class="fas fa-file-upload"></i> Arquivo
            </button>
            <button 
              :class="['tab', { active: activeTab === 'topic' }]"
              @click="activeTab = 'topic'"
            >
              <i class="fas fa-book"></i> Tópico
            </button>
          </div>

          <!-- Text Input -->
          <div v-if="activeTab === 'text'" class="input-content">
            <textarea
              v-model="textInput"
              placeholder="Cole ou digite o conteúdo base para gerar questões..."
              class="text-input"
              rows="10"
            ></textarea>
          </div>

          <!-- File Upload -->
          <div v-if="activeTab === 'file'" class="input-content">
            <div 
              class="file-upload-zone"
              @drop="handleDrop"
              @dragover.prevent
              @dragenter.prevent
            >
              <i class="fas fa-cloud-upload-alt"></i>
              <p>Arraste arquivos aqui ou clique para selecionar</p>
              <input 
                type="file" 
                ref="fileInput"
                @change="handleFileSelect"
                multiple
                accept=".pdf,.doc,.docx,.txt,.md"
                style="display: none"
              >
              <button @click="$refs.fileInput.click()" class="select-btn">
                Selecionar Arquivos
              </button>
            </div>
            <div v-if="uploadedFiles.length > 0" class="uploaded-files">
              <div v-for="file in uploadedFiles" :key="file.name" class="file-item">
                <i class="fas fa-file"></i>
                <span>{{ file.name }}</span>
                <button @click="removeFile(file)" class="remove-btn">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Topic Selection -->
          <div v-if="activeTab === 'topic'" class="input-content">
            <div class="topic-selection">
              <select v-model="selectedTopic" class="topic-select">
                <option value="">Selecione um tópico</option>
                <optgroup label="Ciências Básicas">
                  <option value="anatomia">Anatomia</option>
                  <option value="fisiologia">Fisiologia</option>
                  <option value="bioquimica">Bioquímica</option>
                  <option value="farmacologia">Farmacologia</option>
                  <option value="patologia">Patologia</option>
                </optgroup>
                <optgroup label="Clínica Médica">
                  <option value="cardiologia">Cardiologia</option>
                  <option value="pneumologia">Pneumologia</option>
                  <option value="gastroenterologia">Gastroenterologia</option>
                  <option value="nefrologia">Nefrologia</option>
                  <option value="endocrinologia">Endocrinologia</option>
                </optgroup>
                <optgroup label="Especialidades">
                  <option value="pediatria">Pediatria</option>
                  <option value="ginecologia">Ginecologia e Obstetrícia</option>
                  <option value="cirurgia">Cirurgia Geral</option>
                  <option value="psiquiatria">Psiquiatria</option>
                  <option value="emergencia">Medicina de Emergência</option>
                </optgroup>
              </select>
              <div v-if="selectedTopic" class="subtopic-input">
                <input 
                  v-model="subtopic"
                  placeholder="Especifique o subtópico (opcional)"
                  class="subtopic-field"
                >
              </div>
            </div>
          </div>
        </div>

        <!-- Generation Options -->
        <div class="generation-options">
          <h3><i class="fas fa-cog"></i> Configurações</h3>
          <div class="options-grid">
            <div class="option-item">
              <label>Quantidade</label>
              <input 
                v-model.number="options.quantity"
                type="number"
                min="5"
                max="100"
                step="5"
                class="option-input"
              >
            </div>
            <div class="option-item">
              <label>Dificuldade</label>
              <select v-model="options.difficulty" class="option-select">
                <option value="facil">Fácil</option>
                <option value="medio">Médio</option>
                <option value="dificil">Difícil</option>
                <option value="expert">Expert</option>
              </select>
            </div>
            <div class="option-item">
              <label>Tipo de Prova</label>
              <select v-model="options.examType" class="option-select">
                <option value="residencia">Residência Médica</option>
                <option value="revalida">Revalida</option>
                <option value="enem">ENEM Medicina</option>
                <option value="concurso">Concurso Público</option>
                <option value="board">Board Exam</option>
              </select>
            </div>
            <div class="option-item">
              <label>Estilo</label>
              <select v-model="options.style" class="option-select">
                <option value="direto">Direto</option>
                <option value="caso_clinico">Caso Clínico</option>
                <option value="interpretacao">Interpretação</option>
                <option value="associacao">Associação</option>
              </select>
            </div>
          </div>

          <!-- Advanced Options -->
          <div class="advanced-options">
            <button @click="showAdvanced = !showAdvanced" class="advanced-toggle">
              <i :class="['fas', showAdvanced ? 'fa-chevron-up' : 'fa-chevron-down']"></i>
              Opções Avançadas
            </button>
            <div v-if="showAdvanced" class="advanced-content">
              <label class="checkbox-label">
                <input type="checkbox" v-model="options.includeImages">
                <span>Incluir questões com imagens</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" v-model="options.includeExplanation">
                <span>Gerar explicações detalhadas</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" v-model="options.includeReferences">
                <span>Incluir referências bibliográficas</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Generate Button -->
        <button 
          @click="generateQuestions"
          :disabled="!canGenerate || isGenerating"
          class="generate-btn"
        >
          <i :class="['fas', isGenerating ? 'fa-spinner fa-spin' : 'fa-magic']"></i>
          {{ isGenerating ? 'Gerando...' : 'Gerar Questões' }}
        </button>

        <!-- AI Status Indicator -->
        <div v-if="isGenerating" class="ai-status">
          <div class="ai-status-header">
            <i class="fas fa-brain"></i>
            <span>IA Ultra-Thinking em Ação</span>
          </div>
          <div class="ai-process-steps">
            <div :class="['process-step', { active: currentStep >= 1, completed: currentStep > 1 }]">
              <i class="fas fa-search"></i>
              <span>Analisando conteúdo</span>
            </div>
            <div :class="['process-step', { active: currentStep >= 2, completed: currentStep > 2 }]">
              <i class="fas fa-project-diagram"></i>
              <span>Extraindo conceitos</span>
            </div>
            <div :class="['process-step', { active: currentStep >= 3, completed: currentStep > 3 }]">
              <i class="fas fa-lightbulb"></i>
              <span>Identificando tópicos</span>
            </div>
            <div :class="['process-step', { active: currentStep >= 4, completed: currentStep > 4 }]">
              <i class="fas fa-cogs"></i>
              <span>Construindo questões</span>
            </div>
            <div :class="['process-step', { active: currentStep >= 5 }]">
              <i class="fas fa-check-double"></i>
              <span>Validando qualidade</span>
            </div>
          </div>
          <div class="ai-insight" v-if="currentInsight">
            <i class="fas fa-info-circle"></i>
            {{ currentInsight }}
          </div>
        </div>
      </div>

      <!-- Right Panel - Questions & Exams -->
      <div class="questions-panel glass-effect">
        <!-- Tab Navigation -->
        <div class="panel-tabs">
          <button 
            :class="['panel-tab', { active: rightPanelTab === 'preview' }]"
            @click="rightPanelTab = 'preview'"
          >
            <i class="fas fa-eye"></i> Preview
          </button>
          <button 
            :class="['panel-tab', { active: rightPanelTab === 'exams' }]"
            @click="rightPanelTab = 'exams'"
          >
            <i class="fas fa-file-alt"></i> Simulados
          </button>
          <button 
            :class="['panel-tab', { active: rightPanelTab === 'stats' }]"
            @click="rightPanelTab = 'stats'"
          >
            <i class="fas fa-chart-bar"></i> Estatísticas
          </button>
        </div>

        <!-- Preview Tab -->
        <div v-if="rightPanelTab === 'preview'" class="tab-content">
          <div v-if="generatedQuestions.length > 0" class="preview-section">
            <div class="preview-header">
              <h3>Questões Geradas</h3>
              <div class="preview-actions">
                <button @click="saveAsExam" class="save-exam-btn">
                  <i class="fas fa-save"></i> Salvar como Simulado
                </button>
                <button @click="exportQuestions" class="export-btn">
                  <i class="fas fa-download"></i> Exportar PDF
                </button>
              </div>
            </div>

            <div class="questions-list">
              <div 
                v-for="(question, index) in generatedQuestions" 
                :key="index"
                class="question-card"
              >
                <div class="question-header">
                  <span class="question-number">Questão {{ index + 1 }}</span>
                  <span :class="['difficulty-badge', question.difficulty]">
                    {{ question.difficulty }}
                  </span>
                </div>

                <div class="question-content">
                  <p class="question-text" v-html="question.text"></p>
                  
                  <div v-if="question.image" class="question-image">
                    <img :src="question.image" :alt="`Imagem da questão ${index + 1}`">
                  </div>

                  <div class="alternatives">
                    <div 
                      v-for="(alt, altIndex) in question.alternatives" 
                      :key="altIndex"
                      :class="[
                        'alternative',
                        { 
                          selected: question.userAnswer === altIndex,
                          correct: showAnswers && altIndex === question.correctAnswer,
                          incorrect: showAnswers && question.userAnswer === altIndex && altIndex !== question.correctAnswer
                        }
                      ]"
                      @click="selectAnswer(index, altIndex)"
                    >
                      <span class="alt-letter">{{ String.fromCharCode(65 + altIndex) }})</span>
                      <span class="alt-text">{{ alt }}</span>
                    </div>
                  </div>

                  <div v-if="showAnswers && question.explanation" class="explanation">
                    <h4><i class="fas fa-lightbulb"></i> Explicação</h4>
                    <p>{{ question.explanation }}</p>
                    <div v-if="question.references" class="references">
                      <strong>Referências:</strong>
                      <ul>
                        <li v-for="(ref, refIndex) in question.references" :key="refIndex">
                          {{ ref }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div class="question-actions">
                  <button @click="editQuestion(index)" class="action-btn edit">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button @click="removeQuestion(index)" class="action-btn remove">
                    <i class="fas fa-trash"></i>
                  </button>
                  <button @click="reportQuestion(index)" class="action-btn report">
                    <i class="fas fa-flag"></i>
                  </button>
                </div>
              </div>
            </div>

            <div class="preview-footer">
              <button @click="showAnswers = !showAnswers" class="toggle-answers-btn">
                <i :class="['fas', showAnswers ? 'fa-eye-slash' : 'fa-eye']"></i>
                {{ showAnswers ? 'Ocultar' : 'Mostrar' }} Gabarito
              </button>
            </div>
          </div>

          <div v-else class="empty-state">
            <i class="fas fa-clipboard-list"></i>
            <p>Nenhuma questão gerada ainda</p>
            <span>Use o painel à esquerda para gerar questões</span>
          </div>
        </div>

        <!-- Exams Tab -->
        <div v-if="rightPanelTab === 'exams'" class="tab-content">
          <div class="exams-section">
            <div class="exams-header">
              <h3>Meus Simulados</h3>
              <button @click="createNewExam" class="create-exam-btn">
                <i class="fas fa-plus"></i> Novo Simulado
              </button>
            </div>

            <div v-if="exams.length > 0" class="exams-list">
              <div 
                v-for="exam in exams" 
                :key="exam.id"
                class="exam-card"
              >
                <div class="exam-info">
                  <h4>{{ exam.name }}</h4>
                  <div class="exam-meta">
                    <span><i class="fas fa-question-circle"></i> {{ exam.questions.length }} questões</span>
                    <span><i class="fas fa-clock"></i> {{ exam.duration }} min</span>
                    <span><i class="fas fa-calendar"></i> {{ formatDate(exam.createdAt) }}</span>
                  </div>
                  <div v-if="exam.completed" class="exam-score">
                    <span class="score">{{ exam.score }}%</span>
                    <span class="status">{{ getScoreStatus(exam.score) }}</span>
                  </div>
                </div>
                <div class="exam-actions">
                  <button 
                    @click="startExam(exam)" 
                    class="action-btn start"
                    :disabled="exam.completed"
                  >
                    <i class="fas fa-play"></i>
                    {{ exam.completed ? 'Revisar' : 'Iniciar' }}
                  </button>
                  <button @click="viewExamStats(exam)" class="action-btn stats">
                    <i class="fas fa-chart-pie"></i>
                  </button>
                  <button @click="deleteExam(exam)" class="action-btn delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>

            <div v-else class="empty-state">
              <i class="fas fa-file-medical-alt"></i>
              <p>Nenhum simulado criado</p>
              <span>Gere questões e salve como simulado</span>
            </div>
          </div>
        </div>

        <!-- Statistics Tab -->
        <div v-if="rightPanelTab === 'stats'" class="tab-content">
          <div class="stats-section">
            <h3>Estatísticas de Desempenho</h3>

            <!-- Performance Overview -->
            <div class="performance-overview">
              <div class="stat-card">
                <i class="fas fa-graduation-cap"></i>
                <h4>Taxa de Acerto</h4>
                <p class="stat-value">{{ overallAccuracy }}%</p>
                <span class="stat-trend" :class="accuracyTrend > 0 ? 'up' : 'down'">
                  <i :class="['fas', accuracyTrend > 0 ? 'fa-arrow-up' : 'fa-arrow-down']"></i>
                  {{ Math.abs(accuracyTrend) }}%
                </span>
              </div>
              <div class="stat-card">
                <i class="fas fa-fire"></i>
                <h4>Sequência Atual</h4>
                <p class="stat-value">{{ currentStreak }} dias</p>
                <span class="stat-label">Melhor: {{ bestStreak }} dias</span>
              </div>
              <div class="stat-card">
                <i class="fas fa-brain"></i>
                <h4>Questões Respondidas</h4>
                <p class="stat-value">{{ totalAnswered }}</p>
                <span class="stat-label">Esta semana: {{ weeklyAnswered }}</span>
              </div>
            </div>

            <!-- Performance by Topic -->
            <div class="topic-performance">
              <h4>Desempenho por Tópico</h4>
              <div class="topic-bars">
                <div 
                  v-for="topic in topicStats" 
                  :key="topic.name"
                  class="topic-bar"
                >
                  <div class="topic-info">
                    <span class="topic-name">{{ topic.name }}</span>
                    <span class="topic-percentage">{{ topic.accuracy }}%</span>
                  </div>
                  <div class="progress-bar">
                    <div 
                      class="progress-fill"
                      :style="{ 
                        width: topic.accuracy + '%',
                        backgroundColor: getColorForAccuracy(topic.accuracy)
                      }"
                    ></div>
                  </div>
                  <span class="topic-count">{{ topic.total }} questões</span>
                </div>
              </div>
            </div>

            <!-- Recent Activity -->
            <div class="recent-activity">
              <h4>Atividade Recente</h4>
              <div class="activity-list">
                <div 
                  v-for="activity in recentActivities" 
                  :key="activity.id"
                  class="activity-item"
                >
                  <i :class="['fas', getActivityIcon(activity.type)]"></i>
                  <div class="activity-details">
                    <p>{{ activity.description }}</p>
                    <span class="activity-time">{{ formatTimeAgo(activity.timestamp) }}</span>
                  </div>
                  <span :class="['activity-result', activity.result]">
                    {{ activity.score }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Exam Modal -->
    <div v-if="isExamMode" class="exam-modal" @click.self="closeExam">
      <div class="exam-container">
        <div class="exam-header">
          <h2>{{ currentExam.name }}</h2>
          <div class="exam-progress">
            <div class="progress-info">
              <span>Questão {{ currentQuestionIndex + 1 }} de {{ currentExam.questions.length }}</span>
              <span class="timer">
                <i class="fas fa-clock"></i> {{ formatTime(remainingTime) }}
              </span>
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill"
                :style="{ width: examProgress + '%' }"
              ></div>
            </div>
          </div>
          <button @click="closeExam" class="close-exam-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="exam-content">
          <div class="exam-question">
            <h3>Questão {{ currentQuestionIndex + 1 }}</h3>
            <p v-html="currentExamQuestion.text"></p>
            
            <div v-if="currentExamQuestion.image" class="question-image">
              <img :src="currentExamQuestion.image" alt="Imagem da questão">
            </div>

            <div class="exam-alternatives">
              <div 
                v-for="(alt, index) in currentExamQuestion.alternatives" 
                :key="index"
                :class="[
                  'exam-alternative',
                  { selected: examAnswers[currentQuestionIndex] === index }
                ]"
                @click="selectExamAnswer(index)"
              >
                <span class="alt-letter">{{ String.fromCharCode(65 + index) }})</span>
                <span class="alt-text">{{ alt }}</span>
              </div>
            </div>
          </div>

          <div class="exam-navigation">
            <button 
              @click="previousQuestion"
              :disabled="currentQuestionIndex === 0"
              class="nav-btn prev"
            >
              <i class="fas fa-chevron-left"></i> Anterior
            </button>

            <div class="question-map">
              <button 
                v-for="(_, index) in currentExam.questions"
                :key="index"
                :class="[
                  'map-btn',
                  { 
                    answered: examAnswers[index] !== undefined,
                    current: index === currentQuestionIndex
                  }
                ]"
                @click="goToQuestion(index)"
              >
                {{ index + 1 }}
              </button>
            </div>

            <button 
              v-if="currentQuestionIndex < currentExam.questions.length - 1"
              @click="nextQuestion"
              class="nav-btn next"
            >
              Próxima <i class="fas fa-chevron-right"></i>
            </button>
            <button 
              v-else
              @click="finishExam"
              class="nav-btn finish"
            >
              Finalizar <i class="fas fa-check"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import questionsService from '@/services/questionsService'

export default {
  name: 'QuestionsAI',
  setup() {
    // State
    const activeTab = ref('text')
    const rightPanelTab = ref('preview')
    const textInput = ref('')
    const uploadedFiles = ref([])
    const selectedTopic = ref('')
    const subtopic = ref('')
    const isGenerating = ref(false)
    const showAdvanced = ref(false)
    const showAnswers = ref(false)
    const isExamMode = ref(false)
    
    // AI Process State
    const currentStep = ref(0)
    const currentInsight = ref('')
    
    const options = ref({
      quantity: 10,
      difficulty: 'medio',
      examType: 'residencia',
      style: 'direto',
      includeImages: false,
      includeExplanation: true,
      includeReferences: false
    })

    const generatedQuestions = ref([])
    const exams = ref([])
    const currentExam = ref(null)
    const currentQuestionIndex = ref(0)
    const examAnswers = ref([])
    const remainingTime = ref(0)
    const timerInterval = ref(null)

    // Statistics
    const topicStats = ref([])
    const recentActivities = ref([])
    const overallAccuracy = ref(78)
    const accuracyTrend = ref(5)
    const currentStreak = ref(7)
    const bestStreak = ref(15)
    const totalAnswered = ref(342)
    const weeklyAnswered = ref(48)

    // Computed
    const totalQuestions = computed(() => {
      return exams.value.reduce((total, exam) => total + exam.questions.length, 0)
    })

    const averageScore = computed(() => {
      const completedExams = exams.value.filter(exam => exam.completed)
      if (completedExams.length === 0) return 0
      const total = completedExams.reduce((sum, exam) => sum + exam.score, 0)
      return Math.round(total / completedExams.length)
    })

    const bestScore = computed(() => {
      const completedExams = exams.value.filter(exam => exam.completed)
      if (completedExams.length === 0) return 0
      return Math.max(...completedExams.map(exam => exam.score))
    })

    const canGenerate = computed(() => {
      if (activeTab.value === 'text') return textInput.value.trim()
      if (activeTab.value === 'file') return uploadedFiles.value.length > 0
      if (activeTab.value === 'topic') return selectedTopic.value
      return false
    })

    const currentExamQuestion = computed(() => {
      if (!currentExam.value) return null
      return currentExam.value.questions[currentQuestionIndex.value]
    })

    const examProgress = computed(() => {
      if (!currentExam.value) return 0
      return ((currentQuestionIndex.value + 1) / currentExam.value.questions.length) * 100
    })

    // Methods
    const handleDrop = (e) => {
      e.preventDefault()
      const files = Array.from(e.dataTransfer.files)
      uploadedFiles.value.push(...files)
    }

    const handleFileSelect = (e) => {
      const files = Array.from(e.target.files)
      uploadedFiles.value.push(...files)
    }

    const removeFile = (file) => {
      const index = uploadedFiles.value.indexOf(file)
      if (index > -1) {
        uploadedFiles.value.splice(index, 1)
      }
    }

    const generateQuestions = async () => {
      isGenerating.value = true
      currentStep.value = 0
      currentInsight.value = ''
      
      try {
        let content = ''
        
        // Step 1: Analyzing content
        currentStep.value = 1
        currentInsight.value = 'Processando e preparando conteúdo...'
        await new Promise(resolve => setTimeout(resolve, 500))
        
        if (activeTab.value === 'text') {
          content = textInput.value
        } else if (activeTab.value === 'file') {
          content = await questionsService.processFiles(uploadedFiles.value)
        } else if (activeTab.value === 'topic') {
          content = { topic: selectedTopic.value, subtopic: subtopic.value }
        }
        
        // Step 2: Extracting concepts
        currentStep.value = 2
        currentInsight.value = 'Identificando conceitos-chave e relações...'
        await new Promise(resolve => setTimeout(resolve, 800))
        
        // Step 3: Identifying topics
        currentStep.value = 3
        currentInsight.value = 'Mapeando tópicos médicos relevantes...'
        await new Promise(resolve => setTimeout(resolve, 600))
        
        // Step 4: Building questions
        currentStep.value = 4
        currentInsight.value = 'Construindo questões contextualizadas...'
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const questions = await questionsService.generateQuestions(content, options.value)
        
        // Step 5: Validating quality
        currentStep.value = 5
        currentInsight.value = 'Validando qualidade e relevância...'
        await new Promise(resolve => setTimeout(resolve, 500))
        
        generatedQuestions.value = questions.map(q => ({
          ...q,
          userAnswer: null
        }))
        
        rightPanelTab.value = 'preview'
        currentInsight.value = `✓ ${questions.length} questões geradas com sucesso!`
        
        // Clear status after 3 seconds
        setTimeout(() => {
          currentStep.value = 0
          currentInsight.value = ''
        }, 3000)
        
      } catch (error) {
        console.error('Erro ao gerar questões:', error)
        currentInsight.value = '❌ Erro ao gerar questões. Tente novamente.'
      } finally {
        isGenerating.value = false
      }
    }

    const selectAnswer = (questionIndex, answerIndex) => {
      if (!showAnswers.value) {
        generatedQuestions.value[questionIndex].userAnswer = answerIndex
      }
    }

    const editQuestion = (index) => {
      // TODO: Implement question editing
      console.log('Edit question:', index)
    }

    const removeQuestion = (index) => {
      generatedQuestions.value.splice(index, 1)
    }

    const reportQuestion = (index) => {
      // TODO: Implement question reporting
      console.log('Report question:', index)
    }

    const saveAsExam = () => {
      const examName = prompt('Nome do simulado:')
      if (!examName) return

      const newExam = {
        id: Date.now(),
        name: examName,
        questions: [...generatedQuestions.value],
        duration: generatedQuestions.value.length * 3, // 3 min per question
        createdAt: new Date(),
        completed: false,
        score: null
      }

      exams.value.push(newExam)
      localStorage.setItem('questions_exams', JSON.stringify(exams.value))
      
      rightPanelTab.value = 'exams'
    }

    const exportQuestions = () => {
      // TODO: Implement PDF export
      console.log('Export questions to PDF')
    }

    const createNewExam = () => {
      // Clear generated questions and switch to generation panel
      generatedQuestions.value = []
      rightPanelTab.value = 'preview'
    }

    const startExam = (exam) => {
      currentExam.value = exam
      currentQuestionIndex.value = 0
      examAnswers.value = new Array(exam.questions.length).fill(undefined)
      remainingTime.value = exam.duration * 60 // Convert to seconds
      isExamMode.value = true
      
      // Start timer
      timerInterval.value = setInterval(() => {
        remainingTime.value--
        if (remainingTime.value <= 0) {
          finishExam()
        }
      }, 1000)
    }

    const viewExamStats = (exam) => {
      // TODO: Implement detailed exam statistics view
      console.log('View exam stats:', exam)
    }

    const deleteExam = (exam) => {
      if (confirm(`Deletar o simulado "${exam.name}"?`)) {
        const index = exams.value.indexOf(exam)
        if (index > -1) {
          exams.value.splice(index, 1)
          localStorage.setItem('questions_exams', JSON.stringify(exams.value))
        }
      }
    }

    const selectExamAnswer = (answerIndex) => {
      examAnswers.value[currentQuestionIndex.value] = answerIndex
    }

    const previousQuestion = () => {
      if (currentQuestionIndex.value > 0) {
        currentQuestionIndex.value--
      }
    }

    const nextQuestion = () => {
      if (currentQuestionIndex.value < currentExam.value.questions.length - 1) {
        currentQuestionIndex.value++
      }
    }

    const goToQuestion = (index) => {
      currentQuestionIndex.value = index
    }

    const finishExam = () => {
      clearInterval(timerInterval.value)
      
      // Calculate score
      let correct = 0
      currentExam.value.questions.forEach((question, index) => {
        if (examAnswers.value[index] === question.correctAnswer) {
          correct++
        }
      })
      
      const score = Math.round((correct / currentExam.value.questions.length) * 100)
      
      // Update exam
      const examIndex = exams.value.findIndex(e => e.id === currentExam.value.id)
      if (examIndex > -1) {
        exams.value[examIndex].completed = true
        exams.value[examIndex].score = score
        localStorage.setItem('questions_exams', JSON.stringify(exams.value))
      }
      
      // Add to recent activities
      recentActivities.value.unshift({
        id: Date.now(),
        type: 'exam',
        description: `Simulado "${currentExam.value.name}" concluído`,
        score: score,
        result: score >= 70 ? 'success' : 'danger',
        timestamp: new Date()
      })
      
      // Update statistics
      updateStatistics()
      
      // Show results
      alert(`Simulado finalizado!\n\nAcertos: ${correct}/${currentExam.value.questions.length}\nNota: ${score}%`)
      
      closeExam()
    }

    const closeExam = () => {
      if (timerInterval.value) {
        clearInterval(timerInterval.value)
      }
      isExamMode.value = false
      currentExam.value = null
      currentQuestionIndex.value = 0
      examAnswers.value = []
    }

    const updateStatistics = () => {
      // Mock topic statistics update
      topicStats.value = [
        { name: 'Cardiologia', accuracy: 82, total: 45 },
        { name: 'Pneumologia', accuracy: 75, total: 38 },
        { name: 'Neurologia', accuracy: 68, total: 32 },
        { name: 'Endocrinologia', accuracy: 90, total: 28 },
        { name: 'Gastroenterologia', accuracy: 77, total: 25 }
      ]
    }

    // Utility Functions
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      })
    }

    const formatTime = (seconds) => {
      const minutes = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${minutes}:${secs.toString().padStart(2, '0')}`
    }

    const formatTimeAgo = (date) => {
      const diff = Date.now() - new Date(date).getTime()
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (days > 0) return `${days} dia${days > 1 ? 's' : ''} atrás`
      if (hours > 0) return `${hours} hora${hours > 1 ? 's' : ''} atrás`
      if (minutes > 0) return `${minutes} minuto${minutes > 1 ? 's' : ''} atrás`
      return 'Agora'
    }

    const getScoreStatus = (score) => {
      if (score >= 90) return 'Excelente!'
      if (score >= 80) return 'Muito Bom!'
      if (score >= 70) return 'Bom'
      if (score >= 60) return 'Regular'
      return 'Precisa Melhorar'
    }

    const getColorForAccuracy = (accuracy) => {
      if (accuracy >= 80) return '#4caf50'
      if (accuracy >= 60) return '#ff9800'
      return '#f44336'
    }

    const getActivityIcon = (type) => {
      const icons = {
        exam: 'fa-file-alt',
        practice: 'fa-dumbbell',
        review: 'fa-redo',
        achievement: 'fa-trophy'
      }
      return icons[type] || 'fa-circle'
    }

    // Lifecycle
    onMounted(() => {
      // Load saved exams
      const savedExams = localStorage.getItem('questions_exams')
      if (savedExams) {
        exams.value = JSON.parse(savedExams)
      }
      
      // Load mock statistics
      updateStatistics()
      
      // Load mock recent activities
      recentActivities.value = [
        {
          id: 1,
          type: 'exam',
          description: 'Simulado "Cardiologia Básica" concluído',
          score: 85,
          result: 'success',
          timestamp: new Date(Date.now() - 3600000)
        },
        {
          id: 2,
          type: 'practice',
          description: 'Prática rápida - Farmacologia',
          score: 72,
          result: 'success',
          timestamp: new Date(Date.now() - 7200000)
        }
      ]
    })

    onUnmounted(() => {
      if (timerInterval.value) {
        clearInterval(timerInterval.value)
      }
    })

    return {
      // State
      activeTab,
      rightPanelTab,
      textInput,
      uploadedFiles,
      selectedTopic,
      subtopic,
      isGenerating,
      showAdvanced,
      showAnswers,
      isExamMode,
      options,
      generatedQuestions,
      exams,
      currentExam,
      currentQuestionIndex,
      examAnswers,
      remainingTime,
      currentStep,
      currentInsight,
      
      // Statistics
      topicStats,
      recentActivities,
      overallAccuracy,
      accuracyTrend,
      currentStreak,
      bestStreak,
      totalAnswered,
      weeklyAnswered,
      
      // Computed
      totalQuestions,
      averageScore,
      bestScore,
      canGenerate,
      currentExamQuestion,
      examProgress,
      
      // Methods
      handleDrop,
      handleFileSelect,
      removeFile,
      generateQuestions,
      selectAnswer,
      editQuestion,
      removeQuestion,
      reportQuestion,
      saveAsExam,
      exportQuestions,
      createNewExam,
      startExam,
      viewExamStats,
      deleteExam,
      selectExamAnswer,
      previousQuestion,
      nextQuestion,
      goToQuestion,
      finishExam,
      closeExam,
      
      // Utilities
      formatDate,
      formatTime,
      formatTimeAgo,
      getScoreStatus,
      getColorForAccuracy,
      getActivityIcon
    }
  }
}
</script>

<style scoped>
.questions-ai-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0A0E27 0%, #1A0F2E 25%, #2A1A4E 50%, #1A0F2E 75%, #0A0E27 100%);
  padding: 20px;
  color: #fff;
  position: relative;
  overflow: hidden;
}

/* Particle Background */
.particle-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  border-radius: 50%;
  opacity: 0.6;
  animation: float 15s infinite ease-in-out;
}

.particle:nth-child(odd) {
  animation-duration: 20s;
  animation-delay: 2s;
}

.particle:nth-child(even) {
  animation-duration: 25s;
  animation-delay: 1s;
}

.particle:nth-child(3n) {
  animation-duration: 30s;
  animation-delay: 3s;
}

.particle:nth-child(1) { left: 10%; top: 20%; }
.particle:nth-child(2) { left: 20%; top: 80%; }
.particle:nth-child(3) { left: 30%; top: 10%; }
.particle:nth-child(4) { left: 40%; top: 40%; }
.particle:nth-child(5) { left: 50%; top: 60%; }
.particle:nth-child(6) { left: 60%; top: 30%; }
.particle:nth-child(7) { left: 70%; top: 70%; }
.particle:nth-child(8) { left: 80%; top: 20%; }
.particle:nth-child(9) { left: 90%; top: 50%; }
.particle:nth-child(10) { left: 15%; top: 90%; }
.particle:nth-child(11) { left: 25%; top: 15%; }
.particle:nth-child(12) { left: 35%; top: 65%; }
.particle:nth-child(13) { left: 45%; top: 25%; }
.particle:nth-child(14) { left: 55%; top: 85%; }
.particle:nth-child(15) { left: 65%; top: 45%; }
.particle:nth-child(16) { left: 75%; top: 5%; }
.particle:nth-child(17) { left: 85%; top: 75%; }
.particle:nth-child(18) { left: 95%; top: 35%; }
.particle:nth-child(19) { left: 5%; top: 55%; }
.particle:nth-child(20) { left: 12%; top: 42%; }
.particle:nth-child(21) { left: 22%; top: 72%; }
.particle:nth-child(22) { left: 32%; top: 32%; }
.particle:nth-child(23) { left: 42%; top: 82%; }
.particle:nth-child(24) { left: 52%; top: 12%; }
.particle:nth-child(25) { left: 62%; top: 92%; }
.particle:nth-child(26) { left: 72%; top: 52%; }
.particle:nth-child(27) { left: 82%; top: 62%; }
.particle:nth-child(28) { left: 92%; top: 22%; }
.particle:nth-child(29) { left: 8%; top: 88%; }
.particle:nth-child(30) { left: 18%; top: 38%; }

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-30px) translateX(20px) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(20px) translateX(-30px) scale(0.9);
    opacity: 0.4;
  }
  75% {
    transform: translateY(-20px) translateX(-20px) scale(1.2);
    opacity: 0.7;
  }
}

/* Gradient Orbs */
.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.6;
  animation: orb-float 20s infinite ease-in-out;
  z-index: 0;
}

.gradient-orb-1 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.4) 0%, transparent 70%);
  top: -300px;
  left: -300px;
  animation-duration: 25s;
}

.gradient-orb-2 {
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, rgba(255, 142, 83, 0.4) 0%, transparent 70%);
  bottom: -250px;
  right: -250px;
  animation-duration: 30s;
  animation-delay: 5s;
}

.gradient-orb-3 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.3) 0%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-duration: 35s;
  animation-delay: 10s;
}

@keyframes orb-float {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(100px, -100px) scale(1.1);
  }
  66% {
    transform: translate(-100px, 100px) scale(0.9);
  }
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px 0 rgba(255, 107, 107, 0.1),
    inset 0 0 20px rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 2;
}

/* Header */
.questions-header {
  margin-bottom: 40px;
  position: relative;
  z-index: 3;
  animation: fadeInDown 1s ease-out;
}

.header-content {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.title i {
  font-size: 3rem;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.5));
  animation: iconPulse 2s ease-in-out infinite;
}

.gradient-text {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ff6b6b 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease infinite;
  filter: drop-shadow(0 0 30px rgba(255, 107, 107, 0.3));
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(5deg); }
}

.subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 1px;
  animation: fadeIn 1.5s ease-out 0.5s both;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-30px); }
  to { opacity: 1; transform: translateY(0); }
}

.stats-bar {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 30px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px 0 rgba(255, 107, 107, 0.1),
    inset 0 0 20px rgba(255, 255, 255, 0.05);
  animation: slideInUp 1s ease-out 0.3s both;
}

@keyframes slideInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 142, 83, 0.2));
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.5s ease;
}

.stat-item:hover .stat-icon::before {
  transform: translate(-50%, -50%) scale(2);
}

.stat-icon i {
  font-size: 1.5rem;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  z-index: 1;
  position: relative;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 700;
  color: #fff;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Main Content */
.questions-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 30px;
  max-width: 1600px;
  margin: 0 auto;
}

/* Generation Panel */
.generation-panel {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 25px;
  padding: 35px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px 0 rgba(255, 107, 107, 0.1),
    inset 0 0 20px rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  animation: fadeInLeft 1s ease-out 0.5s both;
}

@keyframes fadeInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

.generation-panel::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.1) 0%, transparent 70%);
  animation: rotate 30s linear infinite;
  pointer-events: none;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.panel-header {
  position: relative;
  margin-bottom: 30px;
}

.panel-header h2 {
  font-size: 1.8rem;
  margin-bottom: 0;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 15px;
}

.panel-header i {
  font-size: 1.6rem;
  filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.5));
}

.panel-glow {
  position: absolute;
  bottom: -10px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #ff6b6b, #ff8e53, transparent);
  filter: blur(4px);
  animation: glowSlide 3s ease-in-out infinite;
}

@keyframes glowSlide {
  0%, 100% { transform: translateX(-100%); opacity: 0; }
  50% { transform: translateX(0); opacity: 1; }
}

/* Input Methods */
.method-tabs {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  padding: 5px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
}

.tab {
  flex: 1;
  padding: 15px 25px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(255, 142, 83, 0.1));
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.tab:hover {
  color: #fff;
  border-color: rgba(255, 255, 255, 0.1);
}

.tab:hover::before {
  transform: translateY(0);
}

.tab.active {
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: #fff;
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3);
  border: none;
}

.tab.active::before {
  display: none;
}

.tab i {
  margin-right: 10px;
  font-size: 1.1rem;
}

/* Input Content */
.input-content {
  margin-bottom: 30px;
}

.text-input {
  width: 100%;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 1rem;
  resize: vertical;
}

.text-input::placeholder {
  color: #666;
}

/* File Upload */
.file-upload-zone {
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload-zone:hover {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.05);
}

.file-upload-zone i {
  font-size: 3rem;
  color: #ff6b6b;
  margin-bottom: 15px;
}

.select-btn {
  margin-top: 15px;
  padding: 10px 25px;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  border: none;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  font-size: 1rem;
}

/* Topic Selection */
.topic-select {
  width: 100%;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 1rem;
}

.topic-select option {
  background: #1a0f2e;
}

.subtopic-field {
  width: 100%;
  margin-top: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  font-size: 1rem;
}

/* Generation Options */
.generation-options {
  margin-bottom: 30px;
}

.generation-options h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: #ff6b6b;
}

.options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.option-item label {
  display: block;
  margin-bottom: 5px;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.option-input,
.option-select {
  width: 100%;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #fff;
  font-size: 1rem;
}

/* Advanced Options */
.advanced-toggle {
  width: 100%;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.advanced-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.advanced-content {
  margin-top: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  cursor: pointer;
}

.checkbox-label input {
  margin-right: 10px;
}

/* Generate Button */
.generate-btn {
  width: 100%;
  padding: 18px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  border: none;
  border-radius: 15px;
  color: #fff;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.generate-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.generate-btn:hover::before {
  width: 300%;
  height: 300%;
}

.generate-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 
    0 10px 30px rgba(255, 107, 107, 0.4),
    0 0 60px rgba(255, 107, 107, 0.2);
}

.generate-btn:active:not(:disabled) {
  transform: translateY(-1px);
}

.generate-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: linear-gradient(135deg, #666, #888);
}

.generate-btn i {
  margin-right: 10px;
  font-size: 1.1rem;
}

/* AI Status Indicator */
.ai-status {
  margin-top: 25px;
  padding: 25px;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 142, 83, 0.05));
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 1px solid rgba(255, 107, 107, 0.3);
  box-shadow: 
    0 8px 32px rgba(255, 107, 107, 0.2),
    inset 0 0 20px rgba(255, 255, 255, 0.05);
  animation: statusFadeIn 0.5s ease;
  position: relative;
  overflow: hidden;
}

.ai-status::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53, #ff6b6b);
  background-size: 200% 200%;
  border-radius: 20px;
  opacity: 0.5;
  z-index: -1;
  animation: borderGlow 3s linear infinite;
}

@keyframes borderGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes statusFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.ai-status-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
  font-size: 1.3rem;
  color: #fff;
}

.ai-status-header i {
  font-size: 1.8rem;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.5));
  animation: brainPulse 2s ease-in-out infinite;
}

@keyframes brainPulse {
  0%, 100% { 
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.5));
  }
  50% { 
    transform: scale(1.2) rotate(5deg);
    filter: drop-shadow(0 0 30px rgba(255, 107, 107, 0.8));
  }
}

.ai-process-steps {
  display: grid;
  gap: 15px;
  margin-bottom: 25px;
}

.process-step {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  opacity: 0.4;
  position: relative;
  overflow: hidden;
}

.process-step::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, rgba(255, 107, 107, 0.2), rgba(255, 142, 83, 0.1));
  transition: width 0.5s ease;
}

.process-step.active {
  opacity: 1;
  background: rgba(255, 107, 107, 0.08);
  border-color: rgba(255, 107, 107, 0.4);
  transform: translateX(10px);
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.2);
}

.process-step.active::before {
  width: 100%;
  animation: progressWave 2s ease-in-out infinite;
}

@keyframes progressWave {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.process-step.completed {
  opacity: 1;
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.4);
}

.process-step.completed::before {
  width: 100%;
  background: rgba(76, 175, 80, 0.2);
}

.process-step i {
  font-size: 1.3rem;
  color: #ff6b6b;
  position: relative;
  z-index: 1;
}

.process-step.active i {
  animation: iconSpin 1s ease-in-out infinite;
}

@keyframes iconSpin {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.process-step.completed i {
  color: #4caf50;
  animation: none;
}

.process-step span {
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
}

.ai-insight {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  font-size: 0.9rem;
  color: #a0a0a0;
  animation: slideIn 0.3s ease;
}

.ai-insight i {
  color: #ff6b6b;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Questions Panel */
.questions-panel {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 25px;
  padding: 35px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px 0 rgba(255, 107, 107, 0.1),
    inset 0 0 20px rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  animation: fadeInRight 1s ease-out 0.5s both;
}

@keyframes fadeInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

.questions-panel::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 142, 83, 0.1) 0%, transparent 70%);
  animation: rotate 35s linear infinite reverse;
  pointer-events: none;
}

/* Panel Tabs */
.panel-tabs {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  padding: 5px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
}

.panel-tab {
  flex: 1;
  padding: 15px 25px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.panel-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(255, 142, 83, 0.1));
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.panel-tab:hover {
  color: #fff;
  border-color: rgba(255, 255, 255, 0.1);
}

.panel-tab:hover::before {
  transform: translateY(0);
}

.panel-tab.active {
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: #fff;
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3);
  border: none;
}

.panel-tab.active::before {
  display: none;
}

.panel-tab i {
  margin-right: 10px;
  font-size: 1.1rem;
}

/* Preview Section */
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.preview-header h3 {
  font-size: 1.3rem;
  color: #ff6b6b;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.save-exam-btn,
.export-btn {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-exam-btn:hover,
.export-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Questions List */
.questions-list {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 10px;
}

.question-card {
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 25px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.question-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.5), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.question-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 107, 107, 0.2);
  transform: translateY(-3px);
  box-shadow: 
    0 10px 30px rgba(255, 107, 107, 0.1),
    0 0 20px rgba(255, 107, 107, 0.05);
}

.question-card:hover::before {
  transform: translateX(100%);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-number {
  font-weight: 600;
  color: #ff6b6b;
}

.difficulty-badge {
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
}

.difficulty-badge.facil {
  background: #4caf50;
}

.difficulty-badge.medio {
  background: #ff9800;
}

.difficulty-badge.dificil {
  background: #f44336;
}

.difficulty-badge.expert {
  background: #9c27b0;
}

.question-text {
  font-size: 1.05rem;
  line-height: 1.6;
  margin-bottom: 20px;
}

.question-image {
  margin-bottom: 20px;
  text-align: center;
}

.question-image img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 10px;
}

/* Alternatives */
.alternatives {
  margin-bottom: 20px;
}

.alternative {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  margin-bottom: 10px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.alternative:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
}

.alternative.selected {
  background: rgba(255, 107, 107, 0.2);
  border-color: #ff6b6b;
}

.alternative.correct {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4caf50;
}

.alternative.incorrect {
  background: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
}

.alt-letter {
  font-weight: 600;
  margin-right: 15px;
  color: #ff6b6b;
}

.alt-text {
  flex: 1;
  line-height: 1.5;
}

/* Explanation */
.explanation {
  background: rgba(255, 255, 255, 0.05);
  border-left: 4px solid #ff6b6b;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.explanation h4 {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: #ff6b6b;
}

.explanation i {
  margin-right: 8px;
}

.references {
  margin-top: 15px;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.references ul {
  list-style: none;
  padding-left: 0;
}

.references li {
  padding-left: 20px;
  position: relative;
}

.references li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #ff6b6b;
}

/* Question Actions */
.question-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.action-btn {
  width: 35px;
  height: 35px;
  border: none;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.edit {
  background: #ff9800;
}

.action-btn.remove {
  background: #f44336;
}

.action-btn.report {
  background: #9c27b0;
}

.action-btn:hover {
  transform: scale(1.1);
}

/* Toggle Answers Button */
.toggle-answers-btn {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-answers-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Exams Section */
.exams-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.exams-header h3 {
  font-size: 1.3rem;
  color: #ff6b6b;
}

.create-exam-btn {
  padding: 10px 20px;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  border: none;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.create-exam-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(255, 107, 107, 0.3);
}

.exams-list {
  max-height: 600px;
  overflow-y: auto;
}

.exam-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.exam-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
}

.exam-info h4 {
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.exam-meta {
  display: flex;
  gap: 20px;
  font-size: 0.9rem;
  color: #a0a0a0;
  margin-bottom: 10px;
}

.exam-meta i {
  margin-right: 5px;
}

.exam-score {
  display: flex;
  align-items: center;
  gap: 15px;
}

.score {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4caf50;
}

.status {
  font-size: 0.9rem;
  color: #a0a0a0;
}

.exam-actions {
  display: flex;
  gap: 10px;
}

.exam-actions .action-btn {
  width: auto;
  padding: 8px 16px;
}

.action-btn.start {
  background: #4caf50;
}

.action-btn.stats {
  background: #2196f3;
}

.action-btn.delete {
  background: #f44336;
}

/* Statistics Section */
.stats-section h3 {
  font-size: 1.3rem;
  color: #ff6b6b;
  margin-bottom: 20px;
}

.performance-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 25px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.1) 0%, transparent 60%);
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.5s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 107, 107, 0.3);
  box-shadow: 0 10px 30px rgba(255, 107, 107, 0.15);
}

.stat-card:hover::before {
  transform: translate(-50%, -50%) scale(1);
}

.stat-card i {
  font-size: 2.5rem;
  color: #ff6b6b;
  margin-bottom: 15px;
}

.stat-card h4 {
  font-size: 1rem;
  color: #a0a0a0;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-trend {
  font-size: 0.9rem;
  font-weight: 600;
}

.stat-trend.up {
  color: #4caf50;
}

.stat-trend.down {
  color: #f44336;
}

.stat-label {
  font-size: 0.85rem;
  color: #666;
}

/* Topic Performance */
.topic-performance {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 30px;
}

.topic-performance h4 {
  font-size: 1.1rem;
  margin-bottom: 20px;
  color: #ff6b6b;
}

.topic-bar {
  margin-bottom: 20px;
}

.topic-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.topic-name {
  font-weight: 500;
}

.topic-percentage {
  font-weight: 700;
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.topic-count {
  font-size: 0.85rem;
  color: #666;
}

/* Recent Activity */
.recent-activity {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 20px;
}

.recent-activity h4 {
  font-size: 1.1rem;
  margin-bottom: 20px;
  color: #ff6b6b;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  margin-bottom: 10px;
}

.activity-item i {
  font-size: 1.5rem;
  color: #ff6b6b;
}

.activity-details {
  flex: 1;
}

.activity-details p {
  margin: 0 0 5px;
}

.activity-time {
  font-size: 0.85rem;
  color: #666;
}

.activity-result {
  font-size: 1.2rem;
  font-weight: 700;
}

.activity-result.success {
  color: #4caf50;
}

.activity-result.danger {
  color: #f44336;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-state i {
  font-size: 4rem;
  color: #666;
  margin-bottom: 20px;
}

.empty-state p {
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.empty-state span {
  color: #666;
}

/* Exam Modal */
.exam-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.exam-container {
  background: rgba(26, 15, 46, 0.9);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 35px;
  max-width: 1000px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 
    0 25px 60px rgba(0, 0, 0, 0.7),
    0 0 100px rgba(255, 107, 107, 0.1),
    inset 0 0 30px rgba(255, 255, 255, 0.05);
  position: relative;
}

.exam-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53, #ff6b6b);
  background-size: 300% 300%;
  border-radius: 25px;
  opacity: 0.3;
  z-index: -1;
  animation: examGlow 5s ease-in-out infinite;
}

@keyframes examGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.exam-header h2 {
  font-size: 1.5rem;
  color: #ff6b6b;
}

.exam-progress {
  flex: 1;
  margin: 0 30px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.timer {
  color: #ff6b6b;
  font-weight: 600;
}

.close-exam-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-exam-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Exam Content */
.exam-question h3 {
  font-size: 1.3rem;
  color: #ff6b6b;
  margin-bottom: 20px;
}

.exam-alternatives {
  margin: 30px 0;
}

.exam-alternative {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  margin-bottom: 15px;
  background: rgba(255, 255, 255, 0.03);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.exam-alternative:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
}

.exam-alternative.selected {
  background: rgba(255, 107, 107, 0.2);
  border-color: #ff6b6b;
}

/* Exam Navigation */
.exam-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-btn.finish {
  background: linear-gradient(45deg, #4caf50, #45a049);
  border: none;
}

.nav-btn.finish:hover {
  box-shadow: 0 5px 20px rgba(76, 175, 80, 0.3);
}

.question-map {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.map-btn {
  width: 35px;
  height: 35px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.map-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.map-btn.answered {
  background: rgba(255, 107, 107, 0.3);
  border-color: #ff6b6b;
}

.map-btn.current {
  background: #ff6b6b;
  border-color: #ff6b6b;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive */
@media (max-width: 1200px) {
  .questions-content {
    grid-template-columns: 1fr;
  }
  
  .performance-overview {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }
  
  .stats-bar {
    flex-direction: column;
    gap: 15px;
  }
  
  .options-grid {
    grid-template-columns: 1fr;
  }
  
  .method-tabs {
    flex-direction: column;
  }
  
  .panel-tabs {
    flex-direction: column;
  }
  
  .exam-navigation {
    flex-direction: column;
    gap: 20px;
  }
  
  .question-map {
    order: -1;
    margin-bottom: 20px;
  }
}
</style>