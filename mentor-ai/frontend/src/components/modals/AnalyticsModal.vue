<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="analytics-modal">
      <div class="modal-header">
        <div class="modal-title">
          <i class="fas fa-chart-line"></i>
          <h2>An<PERSON><PERSON><PERSON></h2>
        </div>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <!-- Overview Cards -->
        <div class="overview-section">
          <div class="overview-card total">
            <div class="card-icon">
              <i class="fas fa-book"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ totalStudies }}</div>
              <div class="card-label">Estudos Realizados</div>
            </div>
          </div>

          <div class="overview-card average">
            <div class="card-icon">
              <i class="fas fa-percentage"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ averageScore }}%</div>
              <div class="card-label">Média Geral</div>
            </div>
          </div>

          <div class="overview-card streak">
            <div class="card-icon">
              <i class="fas fa-fire"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ currentStreak }}</div>
              <div class="card-label">Dias Consecutivos</div>
            </div>
          </div>

          <div class="overview-card completed">
            <div class="card-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="card-content">
              <div class="card-value">{{ completedRevisions }}</div>
              <div class="card-label">Revisões Completas</div>
            </div>
          </div>
        </div>

        <!-- Performance Chart -->
        <div class="chart-section">
          <h3>Evolução do Desempenho</h3>
          <div class="chart-container">
            <canvas ref="performanceChart"></canvas>
          </div>
        </div>

        <!-- Subject Breakdown -->
        <div class="subjects-section">
          <h3>Desempenho por Matéria</h3>
          <div class="subjects-grid">
            <div v-for="subject in subjectStats" :key="subject.name" class="subject-card">
              <div class="subject-header">
                <span class="subject-name">{{ subject.name }}</span>
                <span class="subject-count">{{ subject.count }} revisões</span>
              </div>
              <div class="subject-stats">
                <div class="stat-item">
                  <span class="stat-label">Média</span>
                  <span class="stat-value">{{ subject.average }}%</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Melhor</span>
                  <span class="stat-value best">{{ subject.best }}%</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Última</span>
                  <span class="stat-value">{{ subject.last }}%</span>
                </div>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: subject.average + '%' }"></div>
              </div>
              <div class="trend" :class="subject.trend">
                <i :class="getTrendIcon(subject.trend)"></i>
                {{ getTrendText(subject.trend) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Study Patterns -->
        <div class="patterns-section">
          <h3>Padrões de Estudo</h3>
          <div class="patterns-grid">
            <div class="pattern-card">
              <h4>Melhor Horário</h4>
              <div class="pattern-value">
                <i class="fas fa-clock"></i>
                {{ bestStudyTime }}
              </div>
              <p class="pattern-description">
                Você tem melhor desempenho estudando {{ bestStudyTimeDescription }}
              </p>
            </div>

            <div class="pattern-card">
              <h4>Dia Mais Produtivo</h4>
              <div class="pattern-value">
                <i class="fas fa-calendar-day"></i>
                {{ mostProductiveDay }}
              </div>
              <p class="pattern-description">
                {{ mostProductiveDayCount }} revisões realizadas
              </p>
            </div>

            <div class="pattern-card">
              <h4>Tempo Médio</h4>
              <div class="pattern-value">
                <i class="fas fa-stopwatch"></i>
                {{ averageStudyTime }} min
              </div>
              <p class="pattern-description">
                Por sessão de questões
              </p>
            </div>
          </div>
        </div>

        <!-- Recommendations -->
        <div class="recommendations-section">
          <h3>
            <i class="fas fa-lightbulb"></i>
            Recomendações Personalizadas
          </h3>
          <div class="recommendations-list">
            <div v-for="(rec, index) in recommendations" :key="index" class="recommendation-item">
              <div class="rec-icon" :class="rec.type">
                <i :class="rec.icon"></i>
              </div>
              <div class="rec-content">
                <h4>{{ rec.title }}</h4>
                <p>{{ rec.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import Chart from 'chart.js/auto';

export default {
  name: 'AnalyticsModal',
  props: {
    data: {
      type: Object,
      required: true
    }
  },
  emits: ['close'],
  setup(props) {
    const performanceChart = ref(null);

    // Computed stats
    const totalStudies = computed(() => props.data.studies?.length || 0);
    
    const averageScore = computed(() => {
      const performances = props.data.performances || [];
      if (performances.length === 0) return 0;
      const sum = performances.reduce((acc, p) => acc + p.percentage, 0);
      return Math.round(sum / performances.length);
    });

    const currentStreak = computed(() => {
      // Calculate consecutive days of study
      const dates = (props.data.performances || [])
        .map(p => new Date(p.date).toDateString())
        .filter((date, index, self) => self.indexOf(date) === index)
        .sort((a, b) => new Date(b) - new Date(a));

      let streak = 0;
      const today = new Date().toDateString();
      
      for (let i = 0; i < dates.length; i++) {
        const checkDate = new Date();
        checkDate.setDate(checkDate.getDate() - i);
        if (dates[i] === checkDate.toDateString()) {
          streak++;
        } else {
          break;
        }
      }
      
      return streak;
    });

    const completedRevisions = computed(() => {
      return (props.data.revisions || []).filter(r => r.completed).length;
    });

    const subjectStats = computed(() => {
      const performances = props.data.performances || [];
      const subjectMap = {};

      performances.forEach(p => {
        if (!subjectMap[p.subject]) {
          subjectMap[p.subject] = {
            name: p.subject,
            scores: [],
            count: 0
          };
        }
        subjectMap[p.subject].scores.push(p.percentage);
        subjectMap[p.subject].count++;
      });

      return Object.values(subjectMap).map(subject => {
        const scores = subject.scores;
        const average = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
        const best = Math.max(...scores);
        const last = scores[scores.length - 1];
        
        // Calculate trend
        const recentScores = scores.slice(-3);
        let trend = 'stable';
        if (recentScores.length >= 2) {
          const avgRecent = recentScores.reduce((a, b) => a + b, 0) / recentScores.length;
          const avgPrevious = scores.slice(0, -3).reduce((a, b) => a + b, 0) / (scores.length - 3);
          if (avgRecent > avgPrevious + 5) trend = 'up';
          else if (avgRecent < avgPrevious - 5) trend = 'down';
        }

        return {
          ...subject,
          average,
          best,
          last,
          trend
        };
      });
    });

    const bestStudyTime = computed(() => {
      const performances = props.data.performances || [];
      const timeMap = {};

      performances.forEach(p => {
        const hour = new Date(p.date).getHours();
        const period = hour < 12 ? 'Manhã' : hour < 18 ? 'Tarde' : 'Noite';
        
        if (!timeMap[period]) {
          timeMap[period] = { total: 0, count: 0 };
        }
        timeMap[period].total += p.percentage;
        timeMap[period].count++;
      });

      let bestPeriod = 'Manhã';
      let bestAvg = 0;

      Object.entries(timeMap).forEach(([period, data]) => {
        const avg = data.total / data.count;
        if (avg > bestAvg) {
          bestAvg = avg;
          bestPeriod = period;
        }
      });

      return bestPeriod;
    });

    const bestStudyTimeDescription = computed(() => {
      const time = bestStudyTime.value;
      if (time === 'Manhã') return 'no período da manhã';
      if (time === 'Tarde') return 'no período da tarde';
      return 'no período da noite';
    });

    const mostProductiveDay = computed(() => {
      const performances = props.data.performances || [];
      const dayMap = {};

      performances.forEach(p => {
        const day = new Date(p.date).toLocaleDateString('pt-BR', { weekday: 'long' });
        dayMap[day] = (dayMap[day] || 0) + 1;
      });

      return Object.entries(dayMap).sort((a, b) => b[1] - a[1])[0]?.[0] || 'Segunda-feira';
    });

    const mostProductiveDayCount = computed(() => {
      const performances = props.data.performances || [];
      const dayMap = {};

      performances.forEach(p => {
        const day = new Date(p.date).toLocaleDateString('pt-BR', { weekday: 'long' });
        dayMap[day] = (dayMap[day] || 0) + 1;
      });

      return Object.entries(dayMap).sort((a, b) => b[1] - a[1])[0]?.[1] || 0;
    });

    const averageStudyTime = computed(() => {
      const performances = props.data.performances || [];
      if (performances.length === 0) return 0;
      
      // Assuming 1.5 minutes per question on average
      return Math.round(30 * 1.5);
    });

    const recommendations = computed(() => {
      const recs = [];
      const avg = averageScore.value;
      const subjects = subjectStats.value;

      // Performance-based recommendations
      if (avg < 60) {
        recs.push({
          type: 'warning',
          icon: 'fas fa-exclamation-triangle',
          title: 'Aumente a Frequência de Revisões',
          description: 'Seu desempenho está abaixo do ideal. Considere revisar os conteúdos com mais frequência e fazer anotações durante o estudo teórico.'
        });
      } else if (avg > 80) {
        recs.push({
          type: 'success',
          icon: 'fas fa-trophy',
          title: 'Excelente Desempenho!',
          description: 'Continue mantendo este ritmo de estudos. Você pode experimentar intervalos maiores entre revisões para otimizar seu tempo.'
        });
      }

      // Subject-specific recommendations
      const weakSubjects = subjects.filter(s => s.average < 60);
      if (weakSubjects.length > 0) {
        recs.push({
          type: 'info',
          icon: 'fas fa-book-medical',
          title: 'Foque nas Matérias Difíceis',
          description: `Dedique mais tempo para: ${weakSubjects.map(s => s.name).join(', ')}. Considere fazer resumos e mapas mentais.`
        });
      }

      // Streak recommendations
      if (currentStreak.value === 0) {
        recs.push({
          type: 'warning',
          icon: 'fas fa-calendar-times',
          title: 'Retome seus Estudos',
          description: 'Você não estuda há alguns dias. Mantenha a consistência para melhores resultados!'
        });
      } else if (currentStreak.value > 7) {
        recs.push({
          type: 'success',
          icon: 'fas fa-fire',
          title: 'Ótima Consistência!',
          description: `${currentStreak.value} dias consecutivos! Continue assim para consolidar seu conhecimento.`
        });
      }

      return recs;
    });

    // Methods
    const getTrendIcon = (trend) => {
      if (trend === 'up') return 'fas fa-arrow-trend-up';
      if (trend === 'down') return 'fas fa-arrow-trend-down';
      return 'fas fa-minus';
    };

    const getTrendText = (trend) => {
      if (trend === 'up') return 'Melhorando';
      if (trend === 'down') return 'Piorando';
      return 'Estável';
    };

    const initChart = () => {
      const ctx = performanceChart.value?.getContext('2d');
      if (!ctx) return;

      const performances = props.data.performances || [];
      const last30 = performances.slice(-30);

      new Chart(ctx, {
        type: 'line',
        data: {
          labels: last30.map(p => new Date(p.date).toLocaleDateString('pt-BR', { 
            day: 'numeric', 
            month: 'short' 
          })),
          datasets: [{
            label: 'Desempenho (%)',
            data: last30.map(p => p.percentage),
            borderColor: '#6366f1',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 6
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: 'rgba(30, 41, 59, 0.9)',
              padding: 12,
              borderColor: 'rgba(99, 102, 241, 0.3)',
              borderWidth: 1
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              ticks: {
                callback: (value) => value + '%',
                color: '#94a3b8'
              },
              grid: {
                color: 'rgba(148, 163, 184, 0.1)'
              }
            },
            x: {
              ticks: {
                color: '#94a3b8'
              },
              grid: {
                color: 'rgba(148, 163, 184, 0.1)'
              }
            }
          }
        }
      });
    };

    onMounted(() => {
      setTimeout(initChart, 100);
    });

    return {
      performanceChart,
      totalStudies,
      averageScore,
      currentStreak,
      completedRevisions,
      subjectStats,
      bestStudyTime,
      bestStudyTimeDescription,
      mostProductiveDay,
      mostProductiveDayCount,
      averageStudyTime,
      recommendations,
      getTrendIcon,
      getTrendText
    };
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s;
}

.analytics-modal {
  background: #1e293b;
  border-radius: 20px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modal-title i {
  font-size: 1.5rem;
  color: #6366f1;
}

.modal-title h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #e4e6eb;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

/* Overview Section */
.overview-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.overview-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.card-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.overview-card.total .card-icon {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.overview-card.average .card-icon {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.overview-card.streak .card-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.overview-card.completed .card-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.card-value {
  font-size: 2rem;
  font-weight: 700;
  color: #e4e6eb;
}

.card-label {
  font-size: 0.875rem;
  color: #94a3b8;
}

/* Chart Section */
.chart-section {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 3rem;
}

.chart-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 1.5rem;
}

.chart-container {
  height: 300px;
}

/* Subjects Section */
.subjects-section {
  margin-bottom: 3rem;
}

.subjects-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 1.5rem;
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.subject-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s;
}

.subject-card:hover {
  transform: translateY(-2px);
  border-color: rgba(99, 102, 241, 0.2);
}

.subject-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.subject-name {
  font-weight: 600;
  color: #e4e6eb;
}

.subject-count {
  font-size: 0.75rem;
  color: #64748b;
}

.subject-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: #64748b;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #94a3b8;
}

.stat-value.best {
  color: #10b981;
}

.progress-bar {
  height: 6px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.trend.up {
  color: #10b981;
}

.trend.down {
  color: #ef4444;
}

.trend.stable {
  color: #94a3b8;
}

/* Patterns Section */
.patterns-section {
  margin-bottom: 3rem;
}

.patterns-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 1.5rem;
}

.patterns-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.pattern-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
}

.pattern-card h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #94a3b8;
  margin: 0 0 1rem;
  text-transform: uppercase;
}

.pattern-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.pattern-value i {
  font-size: 1.25rem;
}

.pattern-description {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

/* Recommendations Section */
.recommendations-section {
  background: rgba(30, 41, 59, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 2rem;
}

.recommendations-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.rec-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.rec-icon.success {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.rec-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.rec-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.rec-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 0.5rem;
}

.rec-content p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0;
  line-height: 1.5;
}

/* Scrollbar */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.modal-body::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}
</style>