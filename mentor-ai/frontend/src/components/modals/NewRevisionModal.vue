<template>
  <div class="modal-overlay" @click.self="$emit('close')">
    <div class="revision-modal">
      <div class="modal-header">
        <div class="modal-title">
          <i class="fas fa-calendar-plus"></i>
          <h2>Nova Revisão</h2>
        </div>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="modal-body">
        <div class="form-group">
          <label><PERSON><PERSON><PERSON><PERSON> da <PERSON>ão</label>
          <input 
            v-model="formData.title" 
            type="text" 
            class="form-control" 
            placeholder="Ex: Revisão de Anatomia Cardiovascular"
            required
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Data</label>
            <input 
              v-model="formData.date" 
              type="date" 
              class="form-control"
              :min="today"
              required
            />
          </div>

          <div class="form-group">
            <label><PERSON><PERSON><PERSON><PERSON></label>
            <div class="time-inputs">
              <input 
                v-model="formData.startTime" 
                type="time" 
                class="form-control"
                required
              />
              <span class="time-separator">até</span>
              <input 
                v-model="formData.endTime" 
                type="time" 
                class="form-control"
                required
              />
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Disciplina</label>
            <select v-model="formData.subject" class="form-control" required>
              <option value="">Selecione...</option>
              <option value="Anatomia">Anatomia</option>
              <option value="Fisiologia">Fisiologia</option>
              <option value="Patologia">Patologia</option>
              <option value="Farmacologia">Farmacologia</option>
              <option value="Microbiologia">Microbiologia</option>
              <option value="Bioquímica">Bioquímica</option>
              <option value="Clínica Médica">Clínica Médica</option>
              <option value="Cirurgia">Cirurgia</option>
              <option value="Pediatria">Pediatria</option>
              <option value="Ginecologia">Ginecologia</option>
            </select>
          </div>

          <div class="form-group">
            <label>Tipo de Revisão</label>
            <select v-model="formData.revisionType" class="form-control" required>
              <option value="Teórica">Teórica</option>
              <option value="Prática">Prática (Questões)</option>
              <option value="Mista">Mista</option>
              <option value="Flashcards">Flashcards</option>
              <option value="Resumo">Resumo</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>Prioridade</label>
          <div class="priority-selector">
            <button 
              v-for="priority in ['Baixa', 'Média', 'Alta']" 
              :key="priority"
              type="button"
              @click="formData.priority = priority"
              :class="{ active: formData.priority === priority }"
              class="priority-btn"
            >
              <i :class="getPriorityIcon(priority)"></i>
              {{ priority }}
            </button>
          </div>
        </div>

        <div class="form-group">
          <label>Descrição (opcional)</label>
          <textarea 
            v-model="formData.description" 
            class="form-control" 
            rows="3"
            placeholder="Adicione detalhes sobre o conteúdo a revisar..."
          ></textarea>
        </div>

        <div class="modal-footer">
          <button type="button" @click="$emit('close')" class="btn-secondary">
            Cancelar
          </button>
          <button type="submit" class="btn-primary">
            <i class="fas fa-save"></i>
            Criar Revisão
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';

export default {
  name: 'NewRevisionModal',
  emits: ['close', 'save'],
  setup(props, { emit }) {
    const formData = ref({
      title: '',
      date: new Date().toISOString().split('T')[0],
      startTime: '09:00',
      endTime: '10:00',
      subject: '',
      revisionType: 'Teórica',
      priority: 'Média',
      description: ''
    });

    const today = computed(() => new Date().toISOString().split('T')[0]);

    const getPriorityIcon = (priority) => {
      const icons = {
        'Baixa': 'fas fa-flag',
        'Média': 'fas fa-flag',
        'Alta': 'fas fa-fire'
      };
      return icons[priority];
    };

    const handleSubmit = () => {
      const startDateTime = new Date(`${formData.value.date}T${formData.value.startTime}`);
      const endDateTime = new Date(`${formData.value.date}T${formData.value.endTime}`);
      
      const revision = {
        id: Date.now(),
        title: formData.value.title,
        date: startDateTime,
        startTime: formData.value.startTime,
        endTime: formData.value.endTime,
        subject: formData.value.subject,
        revisionType: formData.value.revisionType,
        priority: formData.value.priority,
        description: formData.value.description,
        completed: false,
        isManual: true
      };
      
      emit('save', revision);
    };

    return {
      formData,
      today,
      getPriorityIcon,
      handleSubmit
    };
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s;
}

.revision-modal {
  background: #1e293b;
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modal-title i {
  font-size: 1.5rem;
  color: #6366f1;
}

.modal-title h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #e4e6eb;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-body {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  color: #e4e6eb;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #6366f1;
  background: rgba(30, 41, 59, 0.8);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-separator {
  color: #64748b;
  font-size: 0.875rem;
}

.priority-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.priority-btn {
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.priority-btn i {
  font-size: 1.25rem;
}

.priority-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
}

.priority-btn.active {
  background: rgba(99, 102, 241, 0.2);
  border-color: #6366f1;
  color: #6366f1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  background: rgba(30, 41, 59, 0.3);
}

.btn-secondary,
.btn-primary {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.btn-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

.btn-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar */
.revision-modal::-webkit-scrollbar {
  width: 8px;
}

.revision-modal::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.revision-modal::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

.revision-modal::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}
</style>