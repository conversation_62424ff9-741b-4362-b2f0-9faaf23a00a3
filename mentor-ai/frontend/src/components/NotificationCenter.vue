<template>
  <div class="notification-center" ref="notificationCenter">
    <!-- Notification Bell Icon -->
    <button 
      class="notification-trigger" 
      @click="toggleNotifications"
      :class="{ 'has-unread': unreadCount > 0, 'active': isOpen }"
    >
      <div class="bell-wrapper">
        <!-- Custom Bell SVG Icon -->
        <svg class="bell-icon-svg" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="bellGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
            </linearGradient>
          </defs>
          <!-- Bell Body -->
          <path 
            class="bell-body"
            d="M12 2C10.9 2 10 2.9 10 4C10 4.11 10.01 4.22 10.03 4.33C7.75 5.13 6 7.35 6 10V16L4 18V19H20V18L18 16V10C18 7.35 16.25 5.13 13.97 4.33C13.99 4.22 14 4.11 14 4C14 2.9 13.1 2 12 2Z"
            :fill="hasUnread ? 'url(#bellGradient)' : 'currentColor'"
          />
          <!-- Bell Clapper (bottom part) -->
          <path 
            class="bell-clapper"
            d="M12 22C13.1 22 14 21.1 14 20H10C10 21.1 10.9 22 12 22Z"
            :fill="hasUnread ? 'url(#bellGradient)' : 'currentColor'"
          />
          <!-- Notification Dot (animated) -->
          <circle 
            v-if="hasNewNotification"
            class="notification-pulse"
            cx="17" 
            cy="6" 
            r="2"
            fill="#f44336"
          />
        </svg>
        
        <transition name="bounce">
          <span v-if="unreadCount > 0" class="notification-badge">
            {{ unreadCount > 99 ? '99+' : unreadCount }}
          </span>
        </transition>
        <div class="bell-ring" :class="{ 'ringing': hasNewNotification }"></div>
      </div>
    </button>

    <!-- Notification Panel -->
    <transition name="notification-panel">
      <div v-if="isOpen" class="notification-panel" @click.stop>
        <!-- Panel Header -->
        <div class="panel-header">
          <h3>
            <font-awesome-icon icon="bell" />
            Notificações
          </h3>
          <div class="header-actions">
            <button 
              v-if="notifications.length > 0" 
              @click="markAllAsRead" 
              class="mark-all-read"
              :disabled="unreadCount === 0"
            >
              <font-awesome-icon icon="check-double" />
              Marcar todas como lidas
            </button>
            <button @click="toggleNotifications" class="close-btn">
              <font-awesome-icon icon="times" />
            </button>
          </div>
        </div>

        <!-- Filter Tabs -->
        <div class="notification-tabs">
          <button 
            v-for="tab in tabs" 
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="{ active: activeTab === tab.id }"
            class="tab-btn"
          >
            <font-awesome-icon :icon="tab.icon" />
            {{ tab.label }}
            <span v-if="tab.count" class="tab-count">{{ tab.count }}</span>
          </button>
        </div>

        <!-- Notifications List -->
        <div class="notifications-container">
          <transition-group name="notification-list" tag="div" class="notifications-list">
            <div 
              v-for="notification in filteredNotifications" 
              :key="notification.id"
              class="notification-item"
              :class="{ 
                unread: !notification.read,
                [notification.type]: true
              }"
              @click="handleNotificationClick(notification)"
            >
              <!-- Notification Icon -->
              <div class="notification-icon" :style="{ background: getIconColor(notification.type) }">
                <font-awesome-icon :icon="getIcon(notification.type)" />
              </div>

              <!-- Notification Content -->
              <div class="notification-content">
                <h4>{{ notification.title }}</h4>
                <p class="notification-message">{{ notification.message }}</p>
                
                <!-- Quick Actions -->
                <div v-if="notification.actions && notification.actions.length > 0" class="notification-quick-actions">
                  <button 
                    v-for="action in notification.actions" 
                    :key="action.label"
                    @click.stop="executeAction(notification, action)"
                    class="quick-action-btn"
                  >
                    {{ action.label }}
                  </button>
                </div>
                
                <!-- Meta Information -->
                <div class="notification-meta">
                  <span class="notification-time">
                    <font-awesome-icon icon="clock" />
                    {{ formatTime(notification.timestamp) }}
                  </span>
                  <span v-if="notification.category" class="notification-category">
                    {{ notification.category }}
                  </span>
                  <span v-if="notification.priority === 'urgent'" class="notification-urgent">
                    <font-awesome-icon icon="exclamation-triangle" />
                    Urgente
                  </span>
                </div>
              </div>

              <!-- Actions -->
              <div class="notification-actions">
                <button 
                  v-if="!notification.read" 
                  @click.stop="markAsRead(notification.id)"
                  class="action-btn read-btn"
                  title="Marcar como lida"
                >
                  <font-awesome-icon icon="check" />
                </button>
                <button 
                  @click.stop="deleteNotification(notification.id)"
                  class="action-btn delete-btn"
                  title="Excluir"
                >
                  <font-awesome-icon icon="trash" />
                </button>
              </div>
            </div>
          </transition-group>

          <!-- Empty State -->
          <div v-if="filteredNotifications.length === 0" class="empty-state">
            <div class="empty-icon">
              <font-awesome-icon :icon="activeTab === 'all' ? 'bell-slash' : 'inbox'" />
            </div>
            <h4>Nenhuma notificação</h4>
            <p>{{ getEmptyMessage() }}</p>
          </div>
        </div>

        <!-- Panel Footer -->
        <div class="panel-footer">
          <router-link to="/notifications" @click="toggleNotifications" class="view-all-link">
            Ver todas as notificações
            <font-awesome-icon icon="arrow-right" />
          </router-link>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { formatDistanceToNow } from 'date-fns'
import { ptBR } from 'date-fns/locale'

export default {
  name: 'NotificationCenter',
  setup() {
    const store = useStore()
    const router = useRouter()
    const isOpen = ref(false)
    const activeTab = ref('all')
    const hasNewNotification = ref(false)
    const notificationCenter = ref(null)

    // Tabs configuration
    const tabs = computed(() => [
      {
        id: 'all',
        label: 'Todas',
        icon: 'list',
        count: notifications.value.length
      },
      {
        id: 'unread',
        label: 'Não lidas',
        icon: 'envelope',
        count: unreadCount.value
      },
      {
        id: 'important',
        label: 'Importantes',
        icon: 'star',
        count: notifications.value.filter(n => n.important).length
      }
    ])

    // Get notifications from store
    const notifications = computed(() => store.state.notifications?.items || [])
    const unreadCount = computed(() => notifications.value.filter(n => !n.read).length)

    // Filter notifications based on active tab
    const filteredNotifications = computed(() => {
      switch (activeTab.value) {
        case 'unread':
          return notifications.value.filter(n => !n.read)
        case 'important':
          return notifications.value.filter(n => n.important)
        default:
          return notifications.value
      }
    })

    // Methods
    const toggleNotifications = () => {
      isOpen.value = !isOpen.value
      if (isOpen.value) {
        // Reset new notification animation
        hasNewNotification.value = false
      }
    }

    const markAsRead = (id) => {
      store.dispatch('notifications/markAsRead', id)
    }

    const markAllAsRead = () => {
      store.dispatch('notifications/markAllAsRead')
    }

    const deleteNotification = (id) => {
      store.dispatch('notifications/deleteNotification', id)
    }

    const handleNotificationClick = (notification) => {
      if (!notification.read) {
        markAsRead(notification.id)
      }
      if (notification.link) {
        window.location.href = notification.link
        toggleNotifications()
      }
    }

    const getIcon = (type) => {
      const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle',
        study: 'book',
        achievement: 'trophy',
        reminder: 'clock',
        system: 'cog'
      }
      return icons[type] || 'bell'
    }

    const getIconColor = (type) => {
      const colors = {
        success: 'linear-gradient(135deg, #4caf50, #45a049)',
        error: 'linear-gradient(135deg, #f44336, #e53935)',
        warning: 'linear-gradient(135deg, #ff9800, #fb8c00)',
        info: 'linear-gradient(135deg, #2196f3, #1e88e5)',
        study: 'linear-gradient(135deg, #673ab7, #5e35b1)',
        achievement: 'linear-gradient(135deg, #ffc107, #ffb300)',
        reminder: 'linear-gradient(135deg, #00bcd4, #00acc1)',
        system: 'linear-gradient(135deg, #607d8b, #546e7a)'
      }
      return colors[type] || 'linear-gradient(135deg, #667eea, #764ba2)'
    }

    const formatTime = (timestamp) => {
      return formatDistanceToNow(new Date(timestamp), { 
        addSuffix: true, 
        locale: ptBR 
      })
    }

    const getEmptyMessage = () => {
      switch (activeTab.value) {
        case 'unread':
          return 'Você está em dia com todas as notificações!'
        case 'important':
          return 'Nenhuma notificação importante no momento.'
        default:
          return 'Você não tem notificações ainda.'
      }
    }
    
    // Execute notification action
    const executeAction = async (notification, action) => {
      try {
        if (!notification.read) {
          markAsRead(notification.id)
        }
        
        // Handle different action types
        if (action.action === 'router/push') {
          router.push(action.params.path)
          toggleNotifications()
        } else if (action.action.includes('/')) {
          // Vuex action
          await store.dispatch(action.action, action.params)
          toggleNotifications()
        } else {
          console.warn('Unknown action type:', action.action)
        }
      } catch (error) {
        console.error('Error executing action:', error)
      }
    }

    // Handle click outside
    const handleClickOutside = (event) => {
      if (notificationCenter.value && !notificationCenter.value.contains(event.target)) {
        isOpen.value = false
      }
    }

    // Watch for new notifications
    const unsubscribe = store.subscribe((mutation) => {
      if (mutation.type === 'notifications/ADD_NOTIFICATION') {
        hasNewNotification.value = true
        setTimeout(() => {
          hasNewNotification.value = false
        }, 3000)
      }
    })

    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
      // Load notifications from store
      store.dispatch('notifications/loadNotifications')
    })

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
      unsubscribe()
    })

    // Computed property for hasUnread
    const hasUnread = computed(() => unreadCount.value > 0)
    
    return {
      isOpen,
      activeTab,
      hasNewNotification,
      notificationCenter,
      tabs,
      notifications,
      unreadCount,
      hasUnread,
      filteredNotifications,
      toggleNotifications,
      markAsRead,
      markAllAsRead,
      deleteNotification,
      handleNotificationClick,
      getIcon,
      getIconColor,
      formatTime,
      getEmptyMessage,
      executeAction
    }
  }
}
</script>

<style scoped>
.notification-center {
  position: relative;
}

/* Notification Trigger Button */
.notification-trigger {
  position: relative;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
}

.notification-trigger:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.notification-trigger.active {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  color: #667eea;
}

.bell-wrapper {
  position: relative;
}

.bell-icon {
  font-size: 1.125rem;
  transition: transform 0.3s ease;
}

/* Custom SVG Bell Icon */
.bell-icon-svg {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
}

.notification-trigger:hover .bell-icon-svg {
  transform: scale(1.1);
}

.notification-trigger.has-unread .bell-icon-svg {
  animation: bell-shake 0.5s ease-in-out;
}

.notification-trigger.has-unread .bell-icon-svg .bell-body,
.notification-trigger.has-unread .bell-icon-svg .bell-clapper {
  filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.5));
}

/* Animated notification pulse */
.notification-pulse {
  animation: pulse-dot 2s ease-in-out infinite;
}

@keyframes pulse-dot {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Bell animation for active state */
.notification-trigger.active .bell-icon-svg {
  animation: bell-rotate 0.3s ease-in-out;
}

@keyframes bell-rotate {
  0% { transform: rotate(0deg); }
  25% { transform: rotate(10deg); }
  75% { transform: rotate(-10deg); }
  100% { transform: rotate(0deg); }
}

.notification-trigger.has-unread .bell-icon {
  animation: bell-shake 0.5s ease-in-out;
}

@keyframes bell-shake {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-15deg); }
  75% { transform: rotate(15deg); }
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #f44336, #e53935);
  color: white;
  font-size: 0.625rem;
  font-weight: 600;
  padding: 2px 5px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.bell-ring {
  position: absolute;
  inset: -10px;
  border: 2px solid rgba(102, 126, 234, 0.5);
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
}

.bell-ring.ringing {
  animation: ring-pulse 1s ease-out;
}

@keyframes ring-pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Notification Panel */
.notification-panel {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  width: 400px;
  max-width: 90vw;
  background: #1a1625;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  z-index: 1000;
}

.panel-header {
  padding: 1.25rem;
  background: rgba(255, 255, 255, 0.03);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.mark-all-read {
  padding: 0.375rem 0.75rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  color: #667eea;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.mark-all-read:hover:not(:disabled) {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

.mark-all-read:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

/* Notification Tabs */
.notification-tabs {
  display: flex;
  padding: 0 1.25rem;
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
  flex: 1;
  padding: 0.875rem 0.5rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.813rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  position: relative;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  color: rgba(255, 255, 255, 0.8);
}

.tab-btn.active {
  color: #667eea;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #667eea;
}

.tab-count {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.688rem;
  font-weight: 600;
}

/* Notifications Container */
.notifications-container {
  max-height: 400px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.01);
}

.notifications-list {
  padding: 0.5rem;
}

.notification-item {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(-4px);
}

.notification-item.unread {
  background: rgba(102, 126, 234, 0.05);
  border-color: rgba(102, 126, 234, 0.1);
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-content h4 {
  margin: 0 0 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #fff;
}

.notification-content p {
  margin: 0 0 0.5rem;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  white-space: pre-line;
}

/* Quick Actions */
.notification-quick-actions {
  display: flex;
  gap: 8px;
  margin: 0.75rem 0 0.5rem;
}

.quick-action-btn {
  padding: 6px 12px;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 6px;
  color: #667eea;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

/* Urgent Indicator */
.notification-urgent {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border-radius: 10px;
  font-size: 0.688rem;
  font-weight: 600;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.notification-time {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.notification-category {
  padding: 2px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.notification-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.action-btn {
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.read-btn:hover {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.3);
  color: #4caf50;
}

.delete-btn:hover {
  background: rgba(244, 67, 54, 0.2);
  border-color: rgba(244, 67, 54, 0.3);
  color: #f44336;
}

/* Empty State */
.empty-state {
  padding: 3rem 2rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  color: rgba(255, 255, 255, 0.2);
  margin-bottom: 1rem;
}

.empty-state h4 {
  margin: 0 0 0.5rem;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
}

.empty-state p {
  margin: 0;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Panel Footer */
.panel-footer {
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.03);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.view-all-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #667eea;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.view-all-link:hover {
  color: #764ba2;
  transform: translateX(4px);
}

/* Animations */
.notification-panel-enter-active,
.notification-panel-leave-active {
  transition: all 0.3s ease;
}

.notification-panel-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.notification-panel-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.notification-list-enter-active,
.notification-list-leave-active {
  transition: all 0.3s ease;
}

.notification-list-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.notification-list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.bounce-enter-active {
  animation: bounce-in 0.5s;
}

@keyframes bounce-in {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Responsive */
@media (max-width: 768px) {
  .notification-panel {
    width: calc(100vw - 2rem);
    right: -1rem;
  }
}
</style>