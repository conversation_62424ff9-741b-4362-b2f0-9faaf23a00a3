<template>
  <div class="privacy-page">
    <div class="container">
      <h1>Política de Privacidade</h1>
      <p class="last-update">Última atualização: {{ currentDate }}</p>
      
      <section>
        <h2>1. Informações que Coletamos</h2>
        <p>A Sophos Academy coleta informações para fornecer melhores serviços aos nossos usuários.</p>
      </section>
      
      <section>
        <h2>2. Como Usamos suas Informações</h2>
        <p>Utilizamos as informações coletadas para melhorar a experiência de aprendizado e personalizar o conteúdo.</p>
      </section>
      
      <section>
        <h2>3. Segurança dos Dados</h2>
        <p>Implementamos medidas de segurança rigorosas para proteger suas informações pessoais.</p>
      </section>
      
      <section>
        <h2>4. Seus Direitos</h2>
        <p>Você tem direito de acessar, corrigir ou excluir suas informações pessoais a qualquer momento.</p>
      </section>
      
      <section>
        <h2>5. Contato</h2>
        <p>Para questões sobre privacidade, entre em contato: <EMAIL></p>
      </section>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Privacy',
  computed: {
    currentDate() {
      return new Date().toLocaleDateString('pt-BR', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    }
  }
}
</script>

<style scoped>
.privacy-page {
  padding: 2rem;
  min-height: 100vh;
  background: var(--background-color);
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--card-bg);
  padding: 3rem;
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
}

h1 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.last-update {
  color: var(--text-secondary);
  font-style: italic;
  margin-bottom: 2rem;
}

section {
  margin-bottom: 2rem;
}

h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

p {
  color: var(--text-secondary);
  line-height: 1.6;
}
</style>