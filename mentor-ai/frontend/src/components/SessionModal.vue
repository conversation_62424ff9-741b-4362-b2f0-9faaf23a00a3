<template>
  <div class="creation-modal-overlay" @click.self="$emit('close')">
    <!-- Left Side Button - Same pattern as StudyPlanPage -->
    <button class="modal-side-button left" @click="$emit('close')" title="Voltar">
      <i class="fas fa-arrow-left"></i>
    </button>
    
    <div class="creation-modal-container">
      <div class="modal-content">
        <div class="modal-header">
          <button class="back-button" @click="$emit('close')" title="Voltar">
            <i class="fas fa-arrow-left"></i>
          </button>
          <h3>{{ editMode ? 'Editar' : 'Nova' }} Sessão de Estudo</h3>
          <div class="header-spacer"></div>
        </div>
        
        <div class="modal-body">
          <form @submit.prevent="handleSubmit">
            <!-- Subject Selection -->
            <div class="form-group">
              <label>
                <i class="fas fa-book"></i>
                Matéria *
              </label>
              <select v-model="formData.subject" class="form-input" required>
                <option value="">Selecione uma matéria</option>
                <option value="Anatomia">Anatomia</option>
                <option value="Farmacologia">Farmacologia</option>
                <option value="Neurociências">Neurociências</option>
                <option value="Fisiologia">Fisiologia</option>
                <option value="Patologia">Patologia</option>
                <option value="Microbiologia">Microbiologia</option>
                <option value="Bioquímica">Bioquímica</option>
                <option value="Imunologia">Imunologia</option>
              </select>
            </div>
            
            <!-- Topic -->
            <div class="form-group">
              <label>
                <i class="fas fa-bookmark"></i>
                Tópico *
              </label>
              <input 
                v-model="formData.topic" 
                type="text" 
                placeholder="Ex: Sistema Cardiovascular"
                maxlength="100"
                required
              />
              <span class="char-count">{{ formData.topic.length }}/100</span>
            </div>
            
            <!-- Date & Time Row -->
            <div class="form-row">
              <div class="form-group">
                <label>
                  <i class="fas fa-calendar"></i>
                  Data
                </label>
                <input 
                  v-model="formData.date" 
                  type="date" 
                  :min="minDate"
                  required
                />
              </div>
              
              <div class="form-group">
                <label>
                  <i class="fas fa-clock"></i>
                  Início
                </label>
                <input 
                  v-model="formData.startTime" 
                  type="time" 
                  required
                />
              </div>
              
              <div class="form-group">
                <label>
                  <i class="fas fa-hourglass-end"></i>
                  Término
                </label>
                <input 
                  v-model="formData.endTime" 
                  type="time" 
                  required
                />
              </div>
            </div>
            
            <!-- Duration Display -->
            <div v-if="duration > 0" class="duration-display">
              <i class="fas fa-stopwatch"></i>
              <span>Duração: {{ duration }} minutos</span>
            </div>
            
            <!-- Session Type -->
            <div class="form-group">
              <label>
                <i class="fas fa-graduation-cap"></i>
                Tipo de Sessão
              </label>
              <div class="session-types">
                <button 
                  v-for="type in sessionTypes" 
                  :key="type.value"
                  type="button"
                  @click="formData.sessionType = type.value"
                  :class="['type-option', { selected: formData.sessionType === type.value }]"
                >
                  <i :class="type.icon"></i>
                  <span>{{ type.label }}</span>
                </button>
              </div>
            </div>
            
            <!-- Tags -->
            <div class="form-group">
              <label>
                <i class="fas fa-tags"></i>
                Tags
              </label>
              <div class="tags-selector">
                <button 
                  v-for="tag in availableTags" 
                  :key="tag"
                  type="button"
                  @click="toggleTag(tag)"
                  :class="['tag-option', { selected: formData.tags.includes(tag) }]"
                >
                  {{ tag }}
                </button>
              </div>
            </div>
            
            <!-- Priority -->
            <div class="form-group">
              <label>
                <i class="fas fa-exclamation-circle"></i>
                Prioridade
              </label>
              <div class="priority-selector">
                <button 
                  v-for="priority in priorities" 
                  :key="priority.value"
                  type="button"
                  @click="formData.priority = priority.value"
                  :class="['priority-option', priority.value, { selected: formData.priority === priority.value }]"
                >
                  <i :class="priority.icon"></i>
                  {{ priority.label }}
                </button>
              </div>
            </div>
            
            <!-- Notes -->
            <div class="form-group">
              <label>
                <i class="fas fa-sticky-note"></i>
                Notas (opcional)
              </label>
              <textarea 
                v-model="formData.notes" 
                placeholder="Adicione observações, links úteis ou lembretes..."
                rows="3"
                maxlength="300"
              ></textarea>
              <span class="char-count">{{ formData.notes.length }}/300</span>
            </div>
            
            <!-- Reminders -->
            <div class="form-group">
              <label>
                <i class="fas fa-bell"></i>
                Lembretes
              </label>
              <div class="reminder-options">
                <label class="checkbox-option">
                  <div class="checkbox-wrapper">
                    <input type="checkbox" v-model="formData.reminders.beforeStart" id="remind-start">
                    <label for="remind-start" class="custom-checkbox"></label>
                  </div>
                  <div class="reminder-content">
                    <span class="reminder-title">15 minutos antes</span>
                    <span class="reminder-desc">Notificação antes do início da sessão</span>
                  </div>
                </label>
                
                <label class="checkbox-option">
                  <div class="checkbox-wrapper">
                    <input type="checkbox" v-model="formData.reminders.dayBefore" id="remind-day">
                    <label for="remind-day" class="custom-checkbox"></label>
                  </div>
                  <div class="reminder-content">
                    <span class="reminder-title">1 dia antes</span>
                    <span class="reminder-desc">Lembrete no dia anterior</span>
                  </div>
                </label>
              </div>
            </div>
            
            <!-- Form Actions -->
            <div class="modal-actions">
              <button type="button" @click="$emit('close')" class="btn-cancel">
                Cancelar
              </button>
              <button type="submit" class="btn-submit">
                <i :class="editMode ? 'fas fa-save' : 'fas fa-plus'"></i>
                {{ editMode ? 'Salvar Alterações' : 'Criar Sessão' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'SessionModal',
  props: {
    session: {
      type: Object,
      default: null
    }
  },
  emits: ['save', 'close'],
  setup(props, { emit }) {
    // Form data
    const formData = ref({
      subject: '',
      topic: '',
      date: new Date().toISOString().split('T')[0],
      startTime: '',
      endTime: '',
      sessionType: 'study',
      tags: [],
      priority: 'medium',
      notes: '',
      reminders: {
        beforeStart: true,
        dayBefore: false
      }
    })
    
    // Options
    const sessionTypes = [
      { value: 'study', label: 'Estudo', icon: 'fas fa-book-reader' },
      { value: 'review', label: 'Revisão', icon: 'fas fa-redo' },
      { value: 'practice', label: 'Prática', icon: 'fas fa-dumbbell' },
      { value: 'exam', label: 'Simulado', icon: 'fas fa-file-alt' }
    ]
    
    const availableTags = [
      'Revisão',
      'Nova matéria',
      'Prática',
      'Importante',
      'Urgente',
      'Exercícios',
      'Resumo',
      'Videoaula'
    ]
    
    const priorities = [
      { value: 'low', label: 'Baixa', icon: 'fas fa-arrow-down' },
      { value: 'medium', label: 'Média', icon: 'fas fa-minus' },
      { value: 'high', label: 'Alta', icon: 'fas fa-arrow-up' }
    ]
    
    // Computed
    const minDate = new Date().toISOString().split('T')[0]
    
    const editMode = computed(() => !!props.session)
    
    const duration = computed(() => {
      if (formData.value.startTime && formData.value.endTime) {
        const [startHour, startMin] = formData.value.startTime.split(':').map(Number)
        const [endHour, endMin] = formData.value.endTime.split(':').map(Number)
        const totalMin = (endHour * 60 + endMin) - (startHour * 60 + startMin)
        return totalMin > 0 ? totalMin : 0
      }
      return 0
    })
    
    // Watch for session prop changes
    watch(() => props.session, (newSession) => {
      if (newSession) {
        formData.value = {
          ...formData.value,
          ...newSession,
          date: newSession.date || formData.value.date,
          tags: newSession.tags || [],
          reminders: newSession.reminders || formData.value.reminders
        }
      }
    }, { immediate: true })
    
    // Methods
    const toggleTag = (tag) => {
      const index = formData.value.tags.indexOf(tag)
      if (index > -1) {
        formData.value.tags.splice(index, 1)
      } else {
        formData.value.tags.push(tag)
      }
    }
    
    const handleSubmit = () => {
      if (duration.value <= 0) {
        alert('O horário de término deve ser posterior ao horário de início')
        return
      }
      
      const sessionData = {
        ...formData.value,
        duration: duration.value,
        status: 'pending'
      }
      
      emit('save', sessionData)
    }
    
    return {
      formData,
      sessionTypes,
      availableTags,
      priorities,
      minDate,
      editMode,
      duration,
      toggleTag,
      handleSubmit
    }
  }
}
</script>

<style scoped>
/* Modal Overlay */
.creation-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 2rem;
}

/* Modal Side Button - Same pattern as StudyPlanPage */
.modal-side-button {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10000;
}

.modal-side-button.left {
  left: 2rem;
}

.modal-side-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-side-button i {
  font-size: 1.25rem;
}

.creation-modal-container {
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

.modal-content {
  background: #1a1a2e;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.back-button {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateX(-2px);
}

.modal-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-spacer {
  width: 40px;
}

/* Modal Body */
.modal-body {
  padding: 2rem;
}

/* Form Groups */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.form-group label i {
  font-size: 1rem;
  color: #667eea;
  opacity: 0.7;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group select option {
  background: #1a1a2e;
  color: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.08);
  border-color: #667eea;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
  margin-top: 0.25rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

/* Duration Display */
.duration-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  color: #667eea;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

/* Session Types */
.session-types {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
}

.type-option:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.type-option.selected {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

.type-option i {
  font-size: 1.5rem;
}

/* Tags Selector */
.tags-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-option {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tag-option:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.tag-option.selected {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  color: #667eea;
}

/* Priority Selector */
.priority-selector {
  display: flex;
  gap: 0.75rem;
}

.priority-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.priority-option.low:hover,
.priority-option.low.selected {
  background: rgba(74, 222, 128, 0.2);
  border-color: #4ade80;
  color: #4ade80;
}

.priority-option.medium:hover,
.priority-option.medium.selected {
  background: rgba(251, 191, 36, 0.2);
  border-color: #fbbf24;
  color: #fbbf24;
}

.priority-option.high:hover,
.priority-option.high.selected {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #ef4444;
}

/* Reminder Options */
.reminder-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 1rem;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.checkbox-option:hover {
  background: rgba(255, 255, 255, 0.05);
}

.checkbox-wrapper {
  position: relative;
}

.checkbox-wrapper input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.custom-checkbox {
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  display: block;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.custom-checkbox::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.checkbox-wrapper input[type="checkbox"]:checked + .custom-checkbox {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-wrapper input[type="checkbox"]:checked + .custom-checkbox::after {
  opacity: 1;
}

.reminder-content {
  flex: 1;
}

.reminder-title {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.25rem;
}

.reminder-desc {
  display: block;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Form Actions */
.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel,
.btn-submit {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-cancel {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.btn-submit {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* Animations */
@keyframes modalSlideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .creation-modal-container {
    max-width: 100%;
    padding: 0;
  }
  
  .modal-content {
    border-radius: 24px 24px 0 0;
    max-height: 100vh;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .session-types {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .btn-cancel,
  .btn-submit {
    width: 100%;
    justify-content: center;
  }
}
</style>