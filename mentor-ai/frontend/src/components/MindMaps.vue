<template>
  <div class="mind-maps-container">
    <div class="mind-maps-header">
      <h1 class="gradient-text">
        <i class="fas fa-brain"></i>
        Mapas Mentais 3D
      </h1>
      <p class="subtitle">Visualização avançada de conhecimento com realidade virtual</p>
    </div>

    <div class="controls-panel">
      <div class="control-section">
        <h3>Modo de Visualização</h3>
        <div class="mode-selector">
          <button 
            v-for="mode in visualizationModes" 
            :key="mode.id"
            @click="currentMode = mode.id"
            :class="['mode-btn', { active: currentMode === mode.id }]"
          >
            <i :class="mode.icon"></i>
            {{ mode.name }}
          </button>
        </div>
      </div>

      <div class="control-section">
        <h3>Ferramentas</h3>
        <div class="tools-grid">
          <button @click="addNode" class="tool-btn">
            <i class="fas fa-plus-circle"></i>
            <PERSON><PERSON><PERSON><PERSON>
          </button>
          <button @click="toggleVR" class="tool-btn" :class="{ active: vrEnabled }">
            <i class="fas fa-vr-cardboard"></i>
            Modo VR
          </button>
          <button @click="toggleCollaboration" class="tool-btn" :class="{ active: collaborationEnabled }">
            <i class="fas fa-users"></i>
            Colaboração
          </button>
          <button @click="generateAIMap" class="tool-btn">
            <i class="fas fa-magic"></i>
            Gerar com IA
          </button>
        </div>
      </div>

      <div class="control-section">
        <h3>Estilos e Temas</h3>
        <div class="theme-selector">
          <div 
            v-for="theme in themes" 
            :key="theme.id"
            @click="currentTheme = theme.id"
            :class="['theme-option', { active: currentTheme === theme.id }]"
            :style="{ background: theme.gradient }"
          >
            {{ theme.name }}
          </div>
        </div>
      </div>
    </div>

    <div class="visualization-area" ref="visualizationContainer">
      <div v-if="loading" class="loading-overlay">
        <div class="neural-loader">
          <div class="loader-brain">
            <i class="fas fa-brain"></i>
          </div>
          <p>Construindo visualização neural...</p>
        </div>
      </div>

      <canvas ref="canvas3d" v-show="!loading"></canvas>

      <div v-if="vrEnabled" class="vr-controls">
        <button ref="vrButton" class="vr-enter-btn">
          <i class="fas fa-vr-cardboard"></i>
          Entrar em VR
        </button>
      </div>
    </div>

    <div v-if="selectedNode" class="node-editor">
      <h3>Editar Nó</h3>
      <input 
        v-model="selectedNode.text" 
        @input="updateNode"
        placeholder="Texto do nó"
        class="node-input"
      >
      <textarea 
        v-model="selectedNode.description" 
        @input="updateNode"
        placeholder="Descrição detalhada"
        class="node-textarea"
      ></textarea>
      <div class="node-actions">
        <button @click="deleteNode" class="delete-btn">
          <i class="fas fa-trash"></i>
          Excluir
        </button>
        <button @click="selectedNode = null" class="close-btn">
          <i class="fas fa-times"></i>
          Fechar
        </button>
      </div>
    </div>

    <div class="export-panel">
      <h3>Exportar Mapa Mental</h3>
      <div class="export-options">
        <button @click="exportAs('png')" class="export-btn">
          <i class="fas fa-image"></i>
          PNG
        </button>
        <button @click="exportAs('svg')" class="export-btn">
          <i class="fas fa-vector-square"></i>
          SVG
        </button>
        <button @click="exportAs('pdf')" class="export-btn">
          <i class="fas fa-file-pdf"></i>
          PDF
        </button>
        <button @click="exportAs('json')" class="export-btn">
          <i class="fas fa-code"></i>
          JSON
        </button>
      </div>
    </div>

    <div v-if="collaborationEnabled" class="collaboration-panel">
      <h3>Colaboradores Online</h3>
      <div class="collaborators-list">
        <div v-for="user in collaborators" :key="user.id" class="collaborator">
          <div class="avatar" :style="{ backgroundColor: user.color }">
            {{ user.name.charAt(0) }}
          </div>
          <span>{{ user.name }}</span>
          <span class="status-indicator" :class="user.status"></span>
        </div>
      </div>
    </div>

    <div v-if="showAIDialog" class="ai-dialog-overlay" @click.self="showAIDialog = false">
      <div class="ai-dialog">
        <h3>Gerar Mapa Mental com IA</h3>
        <textarea 
          v-model="aiPrompt" 
          placeholder="Descreva o tópico para gerar o mapa mental..."
          class="ai-input"
        ></textarea>
        <div class="ai-options">
          <label>
            <input type="checkbox" v-model="aiOptions.includeSubtopics">
            Incluir subtópicos detalhados
          </label>
          <label>
            <input type="checkbox" v-model="aiOptions.addDescriptions">
            Adicionar descrições
          </label>
          <label>
            <input type="checkbox" v-model="aiOptions.suggestConnections">
            Sugerir conexões
          </label>
        </div>
        <div class="dialog-actions">
          <button @click="showAIDialog = false" class="cancel-btn">Cancelar</button>
          <button @click="generateWithAI" class="generate-btn">
            <i class="fas fa-magic"></i>
            Gerar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { VRButton } from 'three/examples/jsm/webxr/VRButton'

export default {
  name: 'MindMaps',
  setup() {
    const visualizationContainer = ref(null)
    const canvas3d = ref(null)
    const vrButton = ref(null)
    const loading = ref(true)
    const currentMode = ref('3d')
    const currentTheme = ref('neural')
    const vrEnabled = ref(false)
    const collaborationEnabled = ref(false)
    const selectedNode = ref(null)
    const showAIDialog = ref(false)
    const aiPrompt = ref('')
    
    const visualizationModes = [
      { id: '3d', name: '3D', icon: 'fas fa-cube' },
      { id: '2d', name: '2D', icon: 'fas fa-layer-group' },
      { id: 'radial', name: 'Radial', icon: 'fas fa-sun' },
      { id: 'tree', name: 'Árvore', icon: 'fas fa-sitemap' },
      { id: 'force', name: 'Força', icon: 'fas fa-atom' }
    ]

    const themes = [
      { id: 'neural', name: 'Neural', gradient: 'linear-gradient(135deg, #667eea, #764ba2)' },
      { id: 'ocean', name: 'Oceano', gradient: 'linear-gradient(135deg, #2193b0, #6dd5ed)' },
      { id: 'sunset', name: 'Pôr do Sol', gradient: 'linear-gradient(135deg, #f093fb, #f5576c)' },
      { id: 'forest', name: 'Floresta', gradient: 'linear-gradient(135deg, #11998e, #38ef7d)' }
    ]

    const collaborators = ref([
      { id: 1, name: 'Dr. Silva', color: '#667eea', status: 'active' },
      { id: 2, name: 'Ana Costa', color: '#f093fb', status: 'editing' }
    ])

    const aiOptions = ref({
      includeSubtopics: true,
      addDescriptions: true,
      suggestConnections: false
    })

    let scene, camera, renderer, controls
    let nodes = []
    let connections = []

    const initThreeJS = () => {
      scene = new THREE.Scene()
      
      const aspect = visualizationContainer.value.clientWidth / visualizationContainer.value.clientHeight
      camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000)
      camera.position.set(0, 0, 50)

      renderer = new THREE.WebGLRenderer({ 
        canvas: canvas3d.value,
        antialias: true,
        alpha: true 
      })
      renderer.setSize(visualizationContainer.value.clientWidth, visualizationContainer.value.clientHeight)
      renderer.setPixelRatio(window.devicePixelRatio)
      renderer.xr.enabled = true

      controls = new OrbitControls(camera, renderer.domElement)
      controls.enableDamping = true
      controls.dampingFactor = 0.05

      const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
      scene.add(ambientLight)

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.4)
      directionalLight.position.set(50, 50, 50)
      scene.add(directionalLight)

      createInitialNodes()
      animate()
      loading.value = false
    }

    const createInitialNodes = () => {
      const centralNode = createNode('Conceito Central', { x: 0, y: 0, z: 0 })
      
      const childNodes = [
        { text: 'Subtópico 1', pos: { x: 20, y: 0, z: 0 } },
        { text: 'Subtópico 2', pos: { x: -20, y: 0, z: 0 } },
        { text: 'Subtópico 3', pos: { x: 0, y: 20, z: 0 } },
        { text: 'Subtópico 4', pos: { x: 0, y: -20, z: 0 } }
      ]

      childNodes.forEach(child => {
        const node = createNode(child.text, child.pos)
        createConnection(centralNode, node)
      })
    }

    const createNode = (text, position) => {
      const geometry = new THREE.SphereGeometry(3, 32, 32)
      const material = new THREE.MeshPhongMaterial({ 
        color: 0x667eea,
        emissive: 0x667eea,
        emissiveIntensity: 0.2
      })
      const sphere = new THREE.Mesh(geometry, material)
      
      sphere.position.set(position.x, position.y, position.z)
      scene.add(sphere)

      const node = {
        id: Date.now() + Math.random(),
        text,
        description: '',
        mesh: sphere,
        position
      }

      nodes.push(node)
      return node
    }

    const createConnection = (node1, node2) => {
      const points = []
      points.push(new THREE.Vector3(node1.position.x, node1.position.y, node1.position.z))
      points.push(new THREE.Vector3(node2.position.x, node2.position.y, node2.position.z))

      const geometry = new THREE.BufferGeometry().setFromPoints(points)
      const material = new THREE.LineBasicMaterial({ color: 0x667eea, opacity: 0.6, transparent: true })
      const line = new THREE.Line(geometry, material)
      
      scene.add(line)
      connections.push({ node1, node2, line })
    }

    const animate = () => {
      requestAnimationFrame(animate)
      
      controls.update()
      
      nodes.forEach(node => {
        node.mesh.rotation.y += 0.01
      })

      renderer.render(scene, camera)
    }

    const addNode = () => {
      const randomPos = {
        x: (Math.random() - 0.5) * 40,
        y: (Math.random() - 0.5) * 40,
        z: (Math.random() - 0.5) * 40
      }
      
      const newNode = createNode('Novo Conceito', randomPos)
      
      if (selectedNode.value) {
        createConnection(selectedNode.value, newNode)
      }
    }

    const updateNode = () => {
      // Update node visually
    }

    const deleteNode = () => {
      if (!selectedNode.value) return
      
      scene.remove(selectedNode.value.mesh)
      nodes = nodes.filter(n => n.id !== selectedNode.value.id)
      
      connections = connections.filter(conn => {
        if (conn.node1.id === selectedNode.value.id || conn.node2.id === selectedNode.value.id) {
          scene.remove(conn.line)
          return false
        }
        return true
      })
      
      selectedNode.value = null
    }

    const toggleVR = () => {
      vrEnabled.value = !vrEnabled.value
      if (vrEnabled.value) {
        document.body.appendChild(VRButton.createButton(renderer))
      }
    }

    const toggleCollaboration = () => {
      collaborationEnabled.value = !collaborationEnabled.value
    }

    const generateAIMap = () => {
      showAIDialog.value = true
    }

    const generateWithAI = async () => {
      loading.value = true
      showAIDialog.value = false
      
      // Simulate AI generation
      setTimeout(() => {
        // Clear existing nodes
        nodes.forEach(node => scene.remove(node.mesh))
        connections.forEach(conn => scene.remove(conn.line))
        nodes = []
        connections = []
        
        // Generate new structure
        const central = createNode(aiPrompt.value, { x: 0, y: 0, z: 0 })
        
        const numTopics = aiOptions.value.includeSubtopics ? 8 : 4
        for (let i = 0; i < numTopics; i++) {
          const angle = (i / numTopics) * Math.PI * 2
          const radius = 30
          const pos = {
            x: Math.cos(angle) * radius,
            y: Math.sin(angle) * radius,
            z: (Math.random() - 0.5) * 20
          }
          
          const topicNode = createNode(`Tópico ${i + 1}`, pos)
          createConnection(central, topicNode)
          
          if (aiOptions.value.includeSubtopics) {
            for (let j = 0; j < 3; j++) {
              const subAngle = angle + (j - 1) * 0.3
              const subRadius = radius + 20
              const subPos = {
                x: Math.cos(subAngle) * subRadius,
                y: Math.sin(subAngle) * subRadius,
                z: pos.z + (Math.random() - 0.5) * 10
              }
              
              const subNode = createNode(`Subtópico ${i + 1}.${j + 1}`, subPos)
              createConnection(topicNode, subNode)
            }
          }
        }
        
        loading.value = false
      }, 2000)
    }

    const exportAs = (format) => {
      console.log(`Exportando como ${format}`)
      // Implement export functionality
    }

    const handleResize = () => {
      if (!renderer) return
      
      const width = visualizationContainer.value.clientWidth
      const height = visualizationContainer.value.clientHeight
      
      camera.aspect = width / height
      camera.updateProjectionMatrix()
      renderer.setSize(width, height)
    }

    onMounted(() => {
      initThreeJS()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      if (renderer) {
        renderer.dispose()
      }
    })

    return {
      visualizationContainer,
      canvas3d,
      vrButton,
      loading,
      currentMode,
      currentTheme,
      vrEnabled,
      collaborationEnabled,
      selectedNode,
      showAIDialog,
      aiPrompt,
      visualizationModes,
      themes,
      collaborators,
      aiOptions,
      addNode,
      updateNode,
      deleteNode,
      toggleVR,
      toggleCollaboration,
      generateAIMap,
      generateWithAI,
      exportAs
    }
  }
}
</script>

<style scoped>
.mind-maps-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f1e 0%, #1a1a2e 50%, #16213e 100%);
  padding: 2rem;
  color: #fff;
}

.mind-maps-header {
  text-align: center;
  margin-bottom: 3rem;
}

.gradient-text {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #a0a0a0;
}

.controls-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.control-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.control-section h3 {
  margin-bottom: 1rem;
  color: #667eea;
}

.mode-selector {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.mode-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mode-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.mode-btn.active {
  background: #667eea;
  border-color: #667eea;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.tool-btn {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.tool-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  transform: translateY(-2px);
}

.tool-btn.active {
  background: #667eea;
  border-color: #667eea;
}

.tool-btn i {
  font-size: 1.5rem;
}

.theme-selector {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.theme-option {
  padding: 1rem;
  border-radius: 10px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.theme-option:hover {
  transform: scale(1.05);
}

.theme-option.active {
  border-color: #fff;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.visualization-area {
  position: relative;
  width: 100%;
  height: 70vh;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 2rem;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10;
}

.neural-loader {
  text-align: center;
}

.loader-brain {
  font-size: 4rem;
  color: #667eea;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

canvas {
  width: 100% !important;
  height: 100% !important;
}

.vr-controls {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
}

.vr-enter-btn {
  padding: 1rem 2rem;
  background: #667eea;
  border: none;
  border-radius: 30px;
  color: #fff;
  font-size: 1.1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.vr-enter-btn:hover {
  background: #764ba2;
  transform: scale(1.05);
}

.node-editor {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 2rem;
  width: 300px;
  z-index: 100;
}

.node-editor h3 {
  margin-bottom: 1rem;
  color: #667eea;
}

.node-input,
.node-textarea {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  margin-bottom: 1rem;
}

.node-textarea {
  min-height: 100px;
  resize: vertical;
}

.node-actions {
  display: flex;
  gap: 1rem;
}

.delete-btn,
.close-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.delete-btn {
  background: #e53e3e;
  color: #fff;
}

.delete-btn:hover {
  background: #c53030;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.export-panel {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.export-panel h3 {
  margin-bottom: 1rem;
  color: #667eea;
}

.export-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.export-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.export-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: #667eea;
  transform: translateY(-2px);
}

.collaboration-panel {
  position: fixed;
  left: 2rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 1.5rem;
  width: 250px;
  z-index: 100;
}

.collaboration-panel h3 {
  margin-bottom: 1rem;
  color: #667eea;
}

.collaborators-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.collaborator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #fff;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: auto;
}

.status-indicator.active {
  background: #48bb78;
}

.status-indicator.editing {
  background: #f6ad55;
  animation: blink 1s ease-in-out infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.ai-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.ai-dialog {
  background: #1a1a2e;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
}

.ai-dialog h3 {
  margin-bottom: 1.5rem;
  color: #667eea;
  text-align: center;
}

.ai-input {
  width: 100%;
  min-height: 120px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #fff;
  resize: vertical;
  margin-bottom: 1rem;
}

.ai-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.ai-options label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.dialog-actions {
  display: flex;
  gap: 1rem;
}

.cancel-btn,
.generate-btn {
  flex: 1;
  padding: 1rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.generate-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

@media (max-width: 768px) {
  .controls-panel {
    grid-template-columns: 1fr;
  }
  
  .tools-grid {
    grid-template-columns: 1fr;
  }
  
  .visualization-area {
    height: 50vh;
  }
  
  .node-editor,
  .collaboration-panel {
    position: static;
    transform: none;
    width: 100%;
    margin-bottom: 2rem;
  }
}
</style>