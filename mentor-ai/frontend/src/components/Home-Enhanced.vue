<template>
  <div class="home-container">
    <!-- Hero Section Aprimorada -->
    <header class="hero-creative">
      <div class="hero-wrapper">
        <div class="hero-background">
          <div class="grid-pattern"></div>
          <div class="floating-orbs">
            <span class="orb orb-1"></span>
            <span class="orb orb-2"></span>
            <span class="orb orb-3"></span>
          </div>
        </div>
        
        <div class="hero-content-creative">
          <div class="welcome-section">
            <div class="welcome-avatar">
              <div class="avatar-glow"></div>
              <div class="avatar-core">
                <span>{{ userInitials }}</span>
              </div>
              <div class="avatar-rings">
                <span class="ring ring-1"></span>
                <span class="ring ring-2"></span>
              </div>
            </div>
            
            <div class="welcome-text-creative">
              <h1 class="welcome-title">
                <span class="greeting">Be<PERSON>-vindo(a),</span>
                <span class="user-name-creative">{{ userName }}!</span>
              </h1>
              <p class="subtitle-creative">
                <span class="subtitle-icon"><font-awesome-icon icon="fa-brain" /></span>
                Seu espaço de estudo personalizado com IA.
              </p>
              <div class="quick-actions">
                <button @click="navigateTo('/engines')" class="cta-button primary">
                  <font-awesome-icon icon="fa-rocket" />
                  Explorar IA Tools
                </button>
                <button @click="navigateTo('/plano-estudo')" class="cta-button secondary">
                  <font-awesome-icon icon="fa-calendar-check" />
                  Meu Plano
                </button>
              </div>
            </div>
          </div>
          
          <div class="metrics-creative" v-if="user">
            <div class="metric-card">
              <div class="metric-icon">
                <font-awesome-icon icon="fa-calendar-days" />
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ completedDays }}</div>
                <div class="metric-label">Dias de estudo</div>
              </div>
              <div class="metric-glow"></div>
            </div>
            
            <div class="metric-card streak">
              <div class="metric-icon">
                <font-awesome-icon icon="fa-fire" />
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ streakDays }}</div>
                <div class="metric-label">Sequência atual</div>
              </div>
              <div class="metric-glow"></div>
            </div>
            
            <div class="metric-card today">
              <div class="metric-icon">
                <font-awesome-icon icon="fa-brain" />
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ todayStudyHours }}h</div>
                <div class="metric-label">Estudo hoje</div>
              </div>
              <div class="metric-glow"></div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- NOVA SEÇÃO: Quick Access Tools -->
    <section class="quick-tools-section">
      <div class="section-header-creative">
        <div class="header-icon-wrapper">
          <font-awesome-icon icon="fa-solid fa-bolt" class="header-icon" />
          <div class="icon-glow"></div>
        </div>
        <div class="header-text">
          <h2 class="section-title-creative">Acesso Rápido</h2>
          <p class="section-subtitle-creative">Suas ferramentas favoritas em um clique</p>
        </div>
      </div>
      
      <div class="quick-tools-grid">
        <div class="quick-tool-card" @click="navigateTo('/flashcards')">
          <div class="tool-icon-wrapper flashcards">
            <font-awesome-icon icon="fa-layer-group" />
          </div>
          <h4>Flashcards</h4>
          <p>{{ flashcardsToReview }} cards para revisar</p>
          <div class="tool-action">
            <font-awesome-icon icon="fa-arrow-right" />
          </div>
        </div>
        
        <div class="quick-tool-card" @click="navigateTo('/revision-scheduler')">
          <div class="tool-icon-wrapper revision">
            <font-awesome-icon icon="fa-calendar-alt" />
          </div>
          <h4>Revisões</h4>
          <p>{{ todayRevisions }} revisões hoje</p>
          <div class="tool-action">
            <font-awesome-icon icon="fa-arrow-right" />
          </div>
        </div>
        
        <div class="quick-tool-card" @click="navigateTo('/pomodoro')">
          <div class="tool-icon-wrapper pomodoro">
            <font-awesome-icon icon="fa-clock" />
          </div>
          <h4>Pomodoro</h4>
          <p>Iniciar sessão focada</p>
          <div class="tool-action">
            <font-awesome-icon icon="fa-arrow-right" />
          </div>
        </div>
        
        <div class="quick-tool-card" @click="navigateTo('/ai-tools/second-brain')">
          <div class="tool-icon-wrapper secondbrain">
            <font-awesome-icon icon="fa-brain" />
          </div>
          <h4>Second Brain</h4>
          <p>Assistente IA online</p>
          <div class="tool-action">
            <font-awesome-icon icon="fa-arrow-right" />
          </div>
        </div>
      </div>
    </section>

    <!-- NOVA SEÇÃO: AI Engines Hub -->
    <section class="ai-engines-section">
      <div class="section-header-creative">
        <div class="header-icon-wrapper">
          <font-awesome-icon icon="fa-solid fa-microchip" class="header-icon" />
          <div class="icon-glow"></div>
        </div>
        <div class="header-text">
          <h2 class="section-title-creative">Central de IA</h2>
          <p class="section-subtitle-creative">Ferramentas inteligentes para potencializar seus estudos</p>
        </div>
        <button @click="navigateTo('/engines')" class="view-all-btn">
          Ver todas <font-awesome-icon icon="fa-arrow-right" />
        </button>
      </div>
      
      <div class="ai-engines-showcase">
        
        <div class="engine-card" @click="navigateTo('/ai-tools/study-assistant')">
          <div class="engine-icon">
            <font-awesome-icon icon="fa-robot" />
          </div>
          <h3>Assistente de Estudo</h3>
          <p>Recomendações personalizadas de IA</p>
          <div class="engine-stats">
            <span class="stat-item">
              <font-awesome-icon icon="fa-chart-line" />
              Performance
            </span>
          </div>
        </div>
        
        <div class="engine-card" @click="navigateTo('/exames')">
          <div class="engine-icon">
            <font-awesome-icon icon="fa-microscope" />
          </div>
          <h3>Análise de Exames</h3>
          <p>IA para análise de exames médicos</p>
          <div class="engine-stats">
            <span class="stat-item">
              <font-awesome-icon icon="fa-file-medical" />
              Multi-formato
            </span>
          </div>
        </div>
      </div>
    </section>

    <!-- SEÇÃO EXISTENTE: Conquistas (Gamification) -->
    <section class="achievements-creative">
      <div class="section-header-creative">
        <div class="header-icon-wrapper">
          <font-awesome-icon icon="fa-solid fa-medal" class="header-icon" />
          <div class="icon-glow"></div>
        </div>
        <div class="header-text">
          <h2 class="section-title-creative">Conquistas</h2>
          <p class="section-subtitle-creative">Vá desbloqueando conquistas à medida que avança nos estudos!</p>
        </div>
      </div>
      
      <div class="achievements-grid-creative">
        <div 
          class="achievement-card-creative" 
          v-for="(achievement, index) in achievements" 
          :key="index"
          :class="{ 'completed': achievement.progress === 100 }"
        >
          <div class="achievement-backdrop">
            <div class="achievement-orb"></div>
          </div>
          
          <div class="achievement-icon-creative">
            <div class="icon-container">
              <font-awesome-icon :icon="achievement.icon" />
              <div class="icon-ring" v-if="achievement.progress === 100"></div>
            </div>
          </div>
          
          <div class="achievement-content-creative">
            <h4 class="achievement-title">{{ achievement.title }}</h4>
            <p class="achievement-description">{{ achievement.description }}</p>
            
            <div class="achievement-progress-creative">
              <div class="progress-track">
                <div 
                  class="progress-fill-creative" 
                  :style="{ width: achievement.progress + '%' }"
                >
                  <div class="progress-glow" v-if="achievement.progress > 0"></div>
                </div>
              </div>
              <div class="progress-label-creative">
                <span class="progress-value">{{ achievement.progress }}%</span>
                <span class="progress-text">completo</span>
              </div>
            </div>
          </div>
          
          <div class="achievement-badge" v-if="achievement.progress === 100">
            <font-awesome-icon icon="fa-check" />
          </div>
        </div>
      </div>
    </section>

    <!-- NOVA SEÇÃO: Learning Path -->
    <section class="learning-path-section">
      <div class="section-header-creative">
        <div class="header-icon-wrapper">
          <font-awesome-icon icon="fa-solid fa-route" class="header-icon" />
          <div class="icon-glow"></div>
        </div>
        <div class="header-text">
          <h2 class="section-title-creative">Sua Jornada de Aprendizado</h2>
          <p class="section-subtitle-creative">Próximos passos recomendados pela IA</p>
        </div>
      </div>
      
      <div class="learning-path-timeline">
        <div class="path-item completed">
          <div class="path-marker">
            <font-awesome-icon icon="fa-check" />
          </div>
          <div class="path-content">
            <h4>Fundamentos Concluídos</h4>
            <p>Parabéns! Você dominou os conceitos básicos</p>
          </div>
        </div>
        
        <div class="path-item current">
          <div class="path-marker pulse">
            <font-awesome-icon icon="fa-book" />
          </div>
          <div class="path-content">
            <h4>Revisão de Anatomia</h4>
            <p>15 flashcards pendentes • Tempo estimado: 30min</p>
            <button @click="navigateTo('/flashcards')" class="path-action">
              Continuar <font-awesome-icon icon="fa-arrow-right" />
            </button>
          </div>
        </div>
        
        <div class="path-item upcoming">
          <div class="path-marker">
            <font-awesome-icon icon="fa-lock" />
          </div>
          <div class="path-content">
            <h4>Simulado Semanal</h4>
            <p>Desbloqueie completando a revisão atual</p>
          </div>
        </div>
      </div>
    </section>

    <!-- NOVA SEÇÃO: Study Resources -->
    <section class="study-resources-section">
      <div class="section-header-creative">
        <div class="header-icon-wrapper">
          <font-awesome-icon icon="fa-solid fa-book-open" class="header-icon" />
          <div class="icon-glow"></div>
        </div>
        <div class="header-text">
          <h2 class="section-title-creative">Recursos de Estudo</h2>
          <p class="section-subtitle-creative">Material organizado para seu aprendizado</p>
        </div>
      </div>
      
      <div class="resources-grid">
        <div class="resource-card videos" @click="navigateTo('/videos')">
          <div class="resource-header">
            <font-awesome-icon icon="fa-video" class="resource-icon" />
            <span class="resource-badge">{{ totalVideos }} vídeos</span>
          </div>
          <h3>Videoaulas</h3>
          <p>Aulas gravadas e ao vivo</p>
          <div class="resource-stats">
            <span><font-awesome-icon icon="fa-eye" /> {{ watchedVideos }} assistidas</span>
            <span><font-awesome-icon icon="fa-bookmark" /> {{ savedVideos }} salvas</span>
          </div>
        </div>
        
        <div class="resource-card notes" @click="navigateTo('/notas')">
          <div class="resource-header">
            <font-awesome-icon icon="fa-sticky-note" class="resource-icon" />
            <span class="resource-badge">Novo</span>
          </div>
          <h3>Notas & Resumos</h3>
          <p>Anotações inteligentes com IA</p>
          <div class="resource-action">
            Explorar <font-awesome-icon icon="fa-arrow-right" />
          </div>
        </div>
        
        <div class="resource-card exams" @click="navigateTo('/questoes')">
          <div class="resource-header">
            <font-awesome-icon icon="fa-file-medical-alt" class="resource-icon" />
            <span class="resource-badge">{{ totalQuestions }}+ questões</span>
          </div>
          <h3>Banco de Questões</h3>
          <p>Questões de provas anteriores</p>
          <div class="resource-action">
            Praticar <font-awesome-icon icon="fa-arrow-right" />
          </div>
        </div>
      </div>
    </section>

    <!-- SEÇÃO EXISTENTE: Progresso (com melhorias) -->
    <section class="progress-overview-creative">
      <div class="progress-header-creative">
        <div class="progress-icon-wrapper">
          <font-awesome-icon icon="fa-solid fa-chart-line" class="progress-header-icon" />
          <div class="progress-icon-glow"></div>
        </div>
        <div class="progress-header-text">
          <h2 class="progress-title-creative">Visão Geral do Progresso</h2>
        </div>
        <button @click="navigateTo('/performance')" class="view-details-btn">
          Ver detalhes <font-awesome-icon icon="fa-arrow-right" />
        </button>
      </div>
      
      <div class="progress-stats-creative">
        <div class="stat-card-creative" v-for="(stat, index) in progressStats" :key="index">
          <div class="stat-icon-wrapper">
            <font-awesome-icon :icon="stat.icon" />
          </div>
          <div class="stat-content">
            <div class="stat-value-wrapper">
              <span class="stat-value">{{ stat.value }}</span>
              <span class="stat-suffix" v-if="stat.suffix">{{ stat.suffix }}</span>
            </div>
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-description">{{ stat.description }}</div>
          </div>
          <div class="stat-trend" :class="stat.trendType">
            <font-awesome-icon :icon="stat.trendType === 'positive' ? 'fa-arrow-up' : 'fa-arrow-down'" />
            <span>{{ stat.trend }}</span>
          </div>
          <div class="stat-decoration"></div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'HomeEnhanced',
  setup() {
    const router = useRouter()
    
    // Estado reativo para dados dinâmicos
    const flashcardsToReview = ref(23)
    const todayRevisions = ref(5)
    const todayStudyHours = ref(2.5)
    const totalVideos = ref(147)
    const watchedVideos = ref(89)
    const savedVideos = ref(42)
    const totalQuestions = ref(2500)
    
    // Navegação
    const navigateTo = (path) => {
      router.push(path)
    }
    
    // Dados mockados para demonstração
    const achievements = ref([
      {
        icon: 'fa-fire',
        title: 'Primeira Semana',
        description: 'Complete 7 dias consecutivos de estudo',
        progress: 100
      },
      {
        icon: 'fa-brain',
        title: 'Mestre dos Flashcards',
        description: 'Domine 100 flashcards',
        progress: 67
      },
      {
        icon: 'fa-trophy',
        title: 'Nota Máxima',
        description: 'Acerte 95% em um simulado',
        progress: 45
      },
      {
        icon: 'fa-clock',
        title: 'Maratonista',
        description: 'Estude por 5 horas em um dia',
        progress: 80
      }
    ])
    
    const progressStats = ref([
      {
        icon: 'fa-graduation-cap',
        title: 'Taxa de Acerto',
        value: '85',
        suffix: '%',
        description: 'Nas últimas questões',
        trend: '+12%',
        trendType: 'positive'
      },
      {
        icon: 'fa-clock',
        title: 'Tempo de Estudo',
        value: '24.5',
        suffix: 'h',
        description: 'Esta semana',
        trend: '+3.2h',
        trendType: 'positive'
      },
      {
        icon: 'fa-layer-group',
        title: 'Cards Dominados',
        value: '67',
        suffix: '',
        description: 'De 100 totais',
        trend: '+15',
        trendType: 'positive'
      }
    ])
    
    return {
      flashcardsToReview,
      todayRevisions,
      todayStudyHours,
      totalVideos,
      watchedVideos,
      savedVideos,
      totalQuestions,
      achievements,
      progressStats,
      navigateTo
    }
  },
  computed: {
    ...mapState(['user']),
    ...mapGetters(['isAuthenticated']),
    userName() {
      return this.user?.name || 'Estudante'
    },
    userInitials() {
      if (!this.user?.name) return 'E'
      return this.user.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
    },
    completedDays() {
      return this.user?.stats?.completedDays || 0
    },
    streakDays() {
      return this.user?.stats?.streakDays || 0
    },
    lastLogin() {
      return this.user?.lastLogin
    }
  },
  methods: {
    formatDate(date) {
      if (!date) return ''
      const options = { 
        day: 'numeric', 
        month: 'short', 
        hour: '2-digit', 
        minute: '2-digit' 
      }
      return new Date(date).toLocaleDateString('pt-BR', options)
    },
    getAreaIcon(areaName) {
      const icons = {
        'Anatomia': 'fa-user',
        'Fisiologia': 'fa-heartbeat',
        'Farmacologia': 'fa-pills',
        'Patologia': 'fa-virus',
        'Clínica Médica': 'fa-stethoscope'
      }
      return icons[areaName] || 'fa-book'
    }
  }
}
</script>

<style scoped>
/* Mantém todos os estilos existentes e adiciona novos */

/* Quick Access Tools Section */
.quick-tools-section {
  padding: 4rem 2rem;
  background: var(--section-bg);
}

.quick-tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.quick-tool-card {
  background: var(--card-bg);
  border-radius: 1rem;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.quick-tool-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.tool-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  position: relative;
}

.tool-icon-wrapper.flashcards {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tool-icon-wrapper.revision {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.tool-icon-wrapper.pomodoro {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.tool-icon-wrapper.secondbrain {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.quick-tool-card h4 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.quick-tool-card p {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.tool-action {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  color: var(--accent-color);
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
}

.quick-tool-card:hover .tool-action {
  opacity: 1;
  transform: translateX(0);
}

/* AI Engines Section */
.ai-engines-section {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.view-all-btn {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-all-btn:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.ai-engines-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.engine-card {
  background: var(--card-bg);
  border-radius: 1.5rem;
  padding: 2.5rem;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.engine-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.engine-card.featured {
  background: linear-gradient(135deg, var(--card-bg) 0%, rgba(102, 126, 234, 0.1) 100%);
  border: 2px solid var(--accent-color);
}

.engine-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--accent-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.engine-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
  border-radius: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--accent-color);
  margin-bottom: 1.5rem;
}

.engine-card h3 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.engine-card p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.engine-features,
.engine-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.engine-features span,
.stat-item {
  background: var(--bg-secondary);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

/* Learning Path Section */
.learning-path-section {
  padding: 4rem 2rem;
  background: var(--section-bg);
}

.learning-path-timeline {
  margin-top: 3rem;
  position: relative;
  padding-left: 3rem;
}

.learning-path-timeline::before {
  content: '';
  position: absolute;
  left: 1.5rem;
  top: 2rem;
  bottom: 2rem;
  width: 2px;
  background: var(--border-color);
}

.path-item {
  position: relative;
  margin-bottom: 3rem;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.path-item.completed,
.path-item.current {
  opacity: 1;
}

.path-marker {
  position: absolute;
  left: -1.5rem;
  width: 3rem;
  height: 3rem;
  background: var(--card-bg);
  border: 2px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.path-item.completed .path-marker {
  background: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.path-item.current .path-marker {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

.path-marker.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

.path-content {
  background: var(--card-bg);
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid var(--border-color);
}

.path-content h4 {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.path-content p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.path-action {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.path-action:hover {
  transform: translateX(5px);
}

/* Study Resources Section */
.study-resources-section {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, rgba(240, 147, 251, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%);
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.resource-card {
  background: var(--card-bg);
  border-radius: 1.5rem;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.resource-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.resource-icon {
  font-size: 2rem;
  color: var(--accent-color);
}

.resource-badge {
  background: var(--bg-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.resource-card h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.resource-card p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.resource-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.resource-stats span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.resource-action {
  color: var(--accent-color);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.resource-card:hover .resource-action {
  transform: translateX(5px);
}

/* Quick Actions in Hero */
.quick-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cta-button {
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  border: none;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cta-button.primary {
  background: var(--accent-color);
  color: white;
}

.cta-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.cta-button.secondary {
  background: transparent;
  color: var(--accent-color);
  border: 2px solid var(--accent-color);
}

.cta-button.secondary:hover {
  background: var(--accent-color);
  color: white;
}

/* Enhanced Metrics */
.metric-card.today {
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
}

/* View Details Button */
.view-details-btn {
  background: transparent;
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-details-btn:hover {
  background: var(--accent-color);
  color: white;
}

/* Dark mode adjustments */
:root[data-theme="dark"] {
  --section-bg: #1a1a1a;
  --card-bg: #242424;
  --border-color: #333;
  --text-primary: #fff;
  --text-secondary: #aaa;
  --bg-secondary: #2a2a2a;
  --success-color: #4caf50;
  --accent-color: #667eea;
}
</style>