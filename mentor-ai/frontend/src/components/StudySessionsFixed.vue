<template>
  <section :class="showHeader ? 'modern-card sessions-section' : 'sessions-section'">
    <div v-if="showHeader" class="section-header">
      <div class="section-title">
        <h2 class="section-heading">
          <span class="title-gradient">Sessões</span>
          <span class="title-accent">Hoje</span>
        </h2>
        <span class="today-date">{{ formatTodayDate() }}</span>
      </div>
      <div class="header-actions">
        <div class="view-toggle">
          <button 
            v-for="view in ['list', 'timeline', 'cards']" 
            :key="view"
            @click="currentView = view"
            :class="['toggle-btn', { active: currentView === view }]"
          >
            <i :class="getViewIcon(view)"></i>
          </button>
        </div>
        <button @click="showNewSessionModal = true" class="create-plan-btn">
          <i class="fas fa-plus"></i>
          <span>Nova Sessão</span>
        </button>
      </div>
    </div>


    <!-- Quick Actions -->
    <div v-if="showHeader" class="quick-actions">
      <button @click="createQuickSession('pomodoro')" class="quick-btn">
        <i class="fas fa-stopwatch"></i>
        <span>Pomodoro</span>
        <span class="duration">25 min</span>
      </button>
      
      <button @click="createQuickSession('focus')" class="quick-btn">
        <i class="fas fa-brain"></i>
        <span>Foco</span>
        <span class="duration">50 min</span>
      </button>
      
      <button @click="createQuickSession('review')" class="quick-btn">
        <i class="fas fa-redo"></i>
        <span>Revisão</span>
        <span class="duration">15 min</span>
      </button>
      
      <button @click="showNewSessionModal = true" class="quick-btn custom">
        <i class="fas fa-plus"></i>
        <span>Nova Sessão</span>
        <span class="duration">Personalizada</span>
      </button>
    </div>

    <!-- List View -->
    <div v-if="currentView === 'list'" class="sessions-list-view">
      <!-- Header for embedded version -->
      <div v-if="!showHeader" class="embedded-header">
        <button @click="showNewSessionModal = true" class="create-plan-btn compact">
          <i class="fas fa-plus"></i>
          <span>Nova Sessão</span>
        </button>
      </div>
      
      <div v-if="sortedSessions.length > 0" class="sessions-container">
        <div 
          v-for="session in sortedSessions" 
          :key="session.id"
          class="session-item"
          :class="{ 'active': session.status === 'in-progress', 'completed': session.status === 'completed' }"
          @click="selectSession(session)"
        >
          <!-- Time Display -->
          <div class="time-block">
            <div class="time-start">{{ session.startTime }}</div>
            <div class="time-duration">{{ session.duration }} min</div>
          </div>
          
          <!-- Visual Time Bar -->
          <div class="time-visual">
            <div class="time-line"></div>
            <div class="time-dot" :class="`status-${session.status}`"></div>
          </div>
          
          <!-- Session Content -->
          <div class="session-content">
            <div class="content-header">
              <h3 class="session-title">{{ session.subject }}</h3>
              <div class="session-actions">
                <button 
                  v-if="session.status === 'pending'"
                  @click.stop="startSession(session)" 
                  class="action-btn play"
                  title="Iniciar"
                >
                  <i class="fas fa-play"></i>
                </button>
                <button 
                  v-else-if="session.status === 'in-progress'"
                  @click.stop="pauseSession(session)" 
                  class="action-btn pause"
                  title="Pausar"
                >
                  <i class="fas fa-pause"></i>
                </button>
                <button 
                  v-else-if="session.status === 'completed'"
                  class="action-btn completed"
                  title="Concluída"
                >
                  <i class="fas fa-check"></i>
                </button>
              </div>
            </div>
            
            <p class="session-topic">{{ session.topic }}</p>
            
            <!-- Progress Bar for Active Sessions -->
            <div v-if="session.status === 'in-progress'" class="progress-wrapper">
              <div class="progress-bar-minimal">
                <div 
                  class="progress-fill-minimal" 
                  :style="{ width: getProgressPercent(session) + '%' }"
                ></div>
              </div>
              <span class="progress-text">{{ getProgressPercent(session) }}% concluído</span>
            </div>
            
            <!-- Tags -->
            <div v-if="session.tags && session.tags.length" class="session-tags">
              <span v-for="tag in session.tags" :key="tag" class="tag-pill">
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="empty-state-modern">
        <div class="empty-illustration">
          <i class="fas fa-calendar-plus"></i>
        </div>
        <h3>Seu dia está livre!</h3>
        <p>Que tal planejar uma sessão de estudos?</p>
        <button @click="showNewSessionModal = true" class="create-session-btn">
          <i class="fas fa-plus-circle"></i>
          Criar Sessão
        </button>
      </div>
    </div>

    <!-- Timeline View -->
    <div v-else-if="currentView === 'timeline'" class="timeline-view">
      <div class="timeline-container">
        <div class="timeline-hours">
          <div v-for="hour in hours" :key="hour" class="hour-mark">
            {{ hour }}:00
          </div>
        </div>
        
        <div class="timeline-track">
          <div 
            class="current-time-marker" 
            :style="{ left: currentTimePosition + '%' }"
          >
            <div class="marker-dot"></div>
            <span class="marker-time">{{ currentTime }}</span>
          </div>

          <div 
            v-for="session in sessions"
            :key="session.id"
            class="timeline-item"
            :class="`status-${session.status}`"
            :style="getTimelineStyle(session)"
            @click="selectSession(session)"
          >
            <span class="timeline-subject">{{ session.subject }}</span>
            <span class="timeline-time">{{ session.startTime }} - {{ session.endTime }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Cards View -->
    <div v-else-if="currentView === 'cards'" class="cards-view">
      <div class="session-cards-grid">
        <div 
          v-for="session in sessions"
          :key="session.id"
          class="session-card"
          :class="`status-${session.status}`"
        >
          <div class="card-header">
            <div class="card-time">
              <i class="fas fa-clock"></i>
              {{ session.startTime }} - {{ session.endTime }}
            </div>
            <div class="card-status" :class="`status-${session.status}`"></div>
          </div>

          <div class="card-body">
            <h3>{{ session.subject }}</h3>
            <p>{{ session.topic }}</p>
          </div>

          <div class="card-footer">
            <div class="card-tags">
              <span v-for="tag in session.tags" :key="tag" class="tag">
                {{ tag }}
              </span>
            </div>
            <button 
              v-if="session.status === 'pending'"
              @click="startSession(session)"
              class="card-action"
            >
              <i class="fas fa-play"></i>
            </button>
          </div>
        </div>
      </div>
    </div>


    <!-- New Session Modal -->
    <Teleport to="body">
      <SessionModal
        v-if="showNewSessionModal"
        :session="selectedSession"
        @save="saveSession"
        @close="closeModal"
      />
    </Teleport>
    
    <!-- Timer Modal -->
    <SessionTimerModal
      :show="showTimerModal"
      :session="activeSessionForTimer"
      :plan-id="activePlanId"
      @close="closeTimerModal"
      @session-complete="handleSessionComplete"
      @update-progress="handleProgressUpdate"
    />
  </section>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import SessionModal from './SessionModal.vue'
import SessionTimerModal from './SessionTimerModal.vue'

export default {
  name: 'StudySessionsFixed',
  components: {
    SessionModal,
    SessionTimerModal
  },
  props: {
    showHeader: {
      type: Boolean,
      default: true
    }
  },
  setup() {
    // State
    const currentView = ref('list')
    const sessions = ref([])
    const showNewSessionModal = ref(false)
    const selectedSession = ref(null)
    const activeMenuId = ref(null)
    const currentTime = ref('')
    const currentTimePosition = ref(0)
    
    // Timer modal state
    const showTimerModal = ref(false)
    const activeSessionForTimer = ref(null)
    const activePlanId = ref(null)
    
    // Stats
    const totalHoursToday = ref(0)
    const completedSessions = ref(0)
    const totalSessions = ref(0)
    const focusScore = ref(0)
    const currentStreak = ref(0)
    
    // Time update interval
    let timeInterval = null
    
    // Load sessions from localStorage
    const loadSessions = () => {
      const savedSessions = localStorage.getItem('todaySessions')
      if (savedSessions) {
        sessions.value = JSON.parse(savedSessions)
      } else {
        // Default sessions for demo
        sessions.value = [
          {
            id: 1,
            subject: 'Anatomia',
            topic: 'Sistema Cardiovascular',
            startTime: '08:00',
            endTime: '09:30',
            duration: 90,
            status: 'completed',
            tags: ['Revisão', 'Importante']
          },
          {
            id: 2,
            subject: 'Farmacologia',
            topic: 'Antibióticos Beta-lactâmicos',
            startTime: '10:00',
            endTime: '11:00',
            duration: 60,
            status: 'completed',
            tags: ['Nova matéria']
          },
          {
            id: 3,
            subject: 'Neurociências',
            topic: 'Vias Sensoriais',
            startTime: '14:00',
            endTime: '15:30',
            duration: 90,
            status: 'in-progress',
            tags: ['Prática'],
            elapsed: 45,
            remaining: 45
          },
          {
            id: 4,
            subject: 'Fisiologia',
            topic: 'Sistema Endócrino',
            startTime: '16:00',
            endTime: '17:00',
            duration: 60,
            status: 'pending',
            tags: ['Revisão']
          }
        ]
      }
      updateStats()
    }
    
    // Save sessions to localStorage
    const saveSessions = () => {
      localStorage.setItem('todaySessions', JSON.stringify(sessions.value))
    }
    
    // Update stats
    const updateStats = () => {
      const completed = sessions.value.filter(s => s.status === 'completed')
      completedSessions.value = completed.length
      totalSessions.value = sessions.value.length
      
      // Calculate total hours
      totalHoursToday.value = completed.reduce((acc, session) => {
        return acc + (session.duration / 60)
      }, 0).toFixed(1)
      
      // Calculate focus score (mock)
      focusScore.value = completed.length > 0 ? 85 : 0
      
      // Get streak (mock)
      currentStreak.value = 7
    }
    
    // Update current time
    const updateCurrentTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
      
      // Calculate position for timeline
      const hours = now.getHours()
      const minutes = now.getMinutes()
      const dayStart = 6 // 6 AM
      const dayEnd = 22 // 10 PM
      const totalMinutes = (hours - dayStart) * 60 + minutes
      const totalDayMinutes = (dayEnd - dayStart) * 60
      
      currentTimePosition.value = Math.max(0, Math.min(100, (totalMinutes / totalDayMinutes) * 100))
    }
    
    // Computed
    const sortedSessions = computed(() => {
      return [...sessions.value].sort((a, b) => {
        const timeA = a.startTime.split(':').map(Number)
        const timeB = b.startTime.split(':').map(Number)
        return (timeA[0] * 60 + timeA[1]) - (timeB[0] * 60 + timeB[1])
      })
    })
    
    const hours = computed(() => {
      return Array.from({ length: 17 }, (_, i) => i + 6) // 6 AM to 10 PM
    })
    
    // Methods
    const formatTodayDate = () => {
      return new Date().toLocaleDateString('pt-BR', { 
        weekday: 'long', 
        day: 'numeric', 
        month: 'long' 
      })
    }
    
    const getViewIcon = (view) => {
      const icons = {
        list: 'fas fa-list',
        timeline: 'fas fa-stream',
        cards: 'fas fa-th-large'
      }
      return icons[view]
    }
    
    const getProgressPercent = (session) => {
      if (!session.elapsed || !session.duration) return 0
      return Math.min(100, (session.elapsed / session.duration) * 100)
    }
    
    const getTimelineStyle = (session) => {
      const [startHour, startMin] = session.startTime.split(':').map(Number)
      const [endHour, endMin] = session.endTime.split(':').map(Number)
      
      const dayStart = 6 // 6 AM
      const dayEnd = 22 // 10 PM
      const dayDuration = dayEnd - dayStart
      
      const startOffset = ((startHour - dayStart) + (startMin / 60)) / dayDuration * 100
      const duration = ((endHour - startHour) + ((endMin - startMin) / 60)) / dayDuration * 100
      
      return {
        left: `${startOffset}%`,
        width: `${duration}%`
      }
    }
    
    const createQuickSession = (type) => {
      const durations = {
        pomodoro: 25,
        focus: 50,
        review: 15
      }
      
      const now = new Date()
      const startTime = now.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
      
      const endDate = new Date(now.getTime() + durations[type] * 60000)
      const endTime = endDate.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
      
      const newSession = {
        id: Date.now(),
        subject: type === 'pomodoro' ? 'Pomodoro' : type === 'focus' ? 'Foco Profundo' : 'Revisão Rápida',
        topic: 'Sessão de ' + durations[type] + ' minutos',
        startTime,
        endTime,
        duration: durations[type],
        status: 'pending',
        tags: [type]
      }
      
      sessions.value.push(newSession)
      saveSessions()
      updateStats()
      
      // Auto start the session
      startSession(newSession)
    }
    
    const startSession = (session) => {
      // Find the plan ID associated with this session
      const plans = JSON.parse(localStorage.getItem('activePlans') || '[]')
      const matchingPlan = plans.find(plan => 
        plan.name === session.subject || 
        plan.category === session.subject
      )
      
      activePlanId.value = matchingPlan?.id || null
      activeSessionForTimer.value = session
      showTimerModal.value = true
      
      session.status = 'in-progress'
      session.startedAt = new Date()
      saveSessions()
    }
    
    const pauseSession = (session) => {
      session.status = 'paused'
      if (session.interval) {
        clearInterval(session.interval)
      }
      saveSessions()
    }
    
    const resumeSession = (session) => {
      session.status = 'in-progress'
      startSession(session)
    }
    
    const completeSession = (session) => {
      session.status = 'completed'
      if (session.interval) {
        clearInterval(session.interval)
      }
      saveSessions()
      updateStats()
    }
    
    const toggleMenu = (session) => {
      activeMenuId.value = activeMenuId.value === session.id ? null : session.id
    }
    
    const editSession = (session) => {
      selectedSession.value = session
      showNewSessionModal.value = true
      activeMenuId.value = null
    }
    
    const duplicateSession = (session) => {
      const duplicate = {
        ...session,
        id: Date.now(),
        status: 'pending',
        elapsed: 0,
        remaining: session.duration
      }
      delete duplicate.interval
      delete duplicate.startedAt
      
      sessions.value.push(duplicate)
      saveSessions()
      activeMenuId.value = null
    }
    
    const deleteSession = (session) => {
      if (confirm('Deseja excluir esta sessão?')) {
        const index = sessions.value.findIndex(s => s.id === session.id)
        if (index > -1) {
          if (session.interval) {
            clearInterval(session.interval)
          }
          sessions.value.splice(index, 1)
          saveSessions()
          updateStats()
        }
      }
      activeMenuId.value = null
    }
    
    const viewDetails = (session) => {
      console.log('View details:', session)
      // Implementar modal de detalhes
    }
    
    const moveSession = (session) => {
      console.log('Move session:', session)
      // Implementar reagendamento
    }
    
    const getTagClass = (tag) => {
      const tagClasses = {
        'Revisão': 'tag-review',
        'Importante': 'tag-important',
        'Nova matéria': 'tag-new',
        'Prática': 'tag-practice',
        'pomodoro': 'tag-pomodoro',
        'focus': 'tag-focus',
        'review': 'tag-review'
      }
      return tagClasses[tag] || 'tag-default'
    }
    
    const getTagIcon = (tag) => {
      const tagIcons = {
        'Revisão': 'fas fa-redo',
        'Importante': 'fas fa-exclamation',
        'Nova matéria': 'fas fa-plus-circle',
        'Prática': 'fas fa-dumbbell',
        'pomodoro': 'fas fa-stopwatch',
        'focus': 'fas fa-brain',
        'review': 'fas fa-redo'
      }
      return tagIcons[tag] || 'fas fa-tag'
    }
    
    const getStatusText = (status) => {
      const statusTexts = {
        'pending': 'Agendado',
        'in-progress': 'Em andamento',
        'paused': 'Pausado',
        'completed': 'Concluído'
      }
      return statusTexts[status] || status
    }
    
    const selectSession = (session) => {
      selectedSession.value = session
    }
    
    const saveSession = (sessionData) => {
      if (selectedSession.value) {
        // Update existing
        Object.assign(selectedSession.value, sessionData)
      } else {
        // Create new
        sessions.value.push({
          ...sessionData,
          id: Date.now(),
          status: 'pending'
        })
      }
      saveSessions()
      updateStats()
      closeModal()
    }
    
    const closeModal = () => {
      showNewSessionModal.value = false
      selectedSession.value = null
    }
    
    const handleSessionComplete = (data) => {
      const session = sessions.value.find(s => s.id === data.sessionId)
      if (session) {
        session.status = 'completed'
        session.completedMinutes = data.minutesStudied
        saveSessions()
        updateStats()
      }
      showTimerModal.value = false
      activeSessionForTimer.value = null
    }
    
    const handleProgressUpdate = (data) => {
      // Update plan progress in localStorage
      const plans = JSON.parse(localStorage.getItem('activePlans') || '[]')
      const planIndex = plans.findIndex(p => p.id === data.planId)
      
      if (planIndex > -1) {
        const plan = plans[planIndex]
        plan.hoursCompleted = (plan.hoursCompleted || 0) + data.hoursStudied
        
        // Update progress percentage
        if (plan.estimatedTime > 0) {
          plan.progress = Math.min(100, Math.round((plan.hoursCompleted / plan.estimatedTime) * 100))
        }
        
        localStorage.setItem('activePlans', JSON.stringify(plans))
      }
    }
    
    const closeTimerModal = () => {
      showTimerModal.value = false
      activeSessionForTimer.value = null
    }
    
    // Lifecycle
    onMounted(() => {
      loadSessions()
      updateCurrentTime()
      timeInterval = setInterval(updateCurrentTime, 60000) // Update every minute
      
      // Close menu on outside click
      document.addEventListener('click', (e) => {
        if (!e.target.closest('.action-btn') && !e.target.closest('.dropdown-menu')) {
          activeMenuId.value = null
        }
      })
    })
    
    onBeforeUnmount(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
      
      // Clear all session intervals
      sessions.value.forEach(session => {
        if (session.interval) {
          clearInterval(session.interval)
        }
      })
    })
    
    return {
      // State
      currentView,
      sessions,
      sortedSessions,
      showNewSessionModal,
      selectedSession,
      activeMenuId,
      currentTime,
      currentTimePosition,
      hours,
      showTimerModal,
      activeSessionForTimer,
      activePlanId,
      
      // Stats
      totalHoursToday,
      completedSessions,
      totalSessions,
      focusScore,
      currentStreak,
      
      // Methods
      formatTodayDate,
      getViewIcon,
      getProgressPercent,
      getTimelineStyle,
      createQuickSession,
      startSession,
      pauseSession,
      resumeSession,
      toggleMenu,
      editSession,
      duplicateSession,
      deleteSession,
      viewDetails,
      moveSession,
      getTagClass,
      getTagIcon,
      getStatusText,
      selectSession,
      saveSession,
      closeModal,
      handleSessionComplete,
      handleProgressUpdate,
      closeTimerModal
    }
  }
}
</script>

<style scoped>
/* Base Styles */
.sessions-section {
  width: 100%;
}

/* Section Header */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.section-title {
  display: flex;
  align-items: baseline;
  gap: 1rem;
}

.today-date {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* View Toggle */
.view-toggle {
  display: flex;
  gap: 0.25rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem;
  border-radius: 10px;
}

.toggle-btn {
  width: 36px;
  height: 36px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.toggle-btn.active {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

/* Create Plan Button (Same style for New Session) */
.create-plan-btn {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.75rem 1.5rem !important;
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  color: white !important;
  border: none !important;
  border-radius: 12px !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
  text-decoration: none !important;
}

.create-plan-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4) !important;
}

.create-plan-btn i {
  font-size: 1rem !important;
}

/* Embedded Header */
.embedded-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1.5rem;
}

.create-plan-btn.compact {
  padding: 0.5rem 1rem;
  font-size: 0.8125rem;
}

.create-plan-btn.compact i {
  font-size: 0.875rem;
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.quick-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
}

.quick-btn i {
  font-size: 1.5rem;
  color: #667eea;
}

.quick-btn span {
  font-size: 0.875rem;
  font-weight: 500;
}

.quick-btn .duration {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

.quick-btn.custom {
  border-style: dashed;
}

/* Sessions Grid - Clean Style matching Plans */
.sessions-grid {
  display: grid;
  gap: 1.5rem;
}

.session-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.session-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.session-card.is-active {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.5);
}

.session-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.session-time-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
}

.session-time-info i {
  color: rgba(255, 255, 255, 0.5);
}

.session-info {
  flex: 1;
}

.session-info h3 {
  margin: 0 0 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.session-info p {
  margin: 0;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.session-actions {
  display: flex;
  gap: 0.5rem;
}

.icon-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.session-stats {
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.session-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.session-progress {
  margin: 1rem 0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.progress-percentage {
  color: #667eea;
  font-weight: 600;
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.session-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.status-info i {
  font-size: 1rem;
}

.status-info i.fa-check-circle {
  color: #4ade80;
}

.status-info i.fa-play-circle {
  color: #667eea;
}

.status-info i.fa-pause-circle {
  color: #fbbf24;
}

.start-btn,
.pause-btn,
.resume-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.5);
  border-radius: 8px;
  color: #667eea;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.start-btn:hover,
.resume-btn:hover {
  background: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

.pause-btn {
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.5);
  color: #fbbf24;
}

.pause-btn:hover {
  background: rgba(251, 191, 36, 0.3);
}

/* Timeline View */
.timeline-view {
  padding: 2rem 0;
}

.timeline-container {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  padding: 2rem;
  overflow-x: auto;
}

.timeline-hours {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 0 1rem;
}

.hour-mark {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.timeline-track {
  position: relative;
  height: 120px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  margin: 0 1rem;
}

.current-time-marker {
  position: absolute;
  top: -20px;
  bottom: -20px;
  width: 2px;
  background: #f5576c;
  z-index: 5;
}

.marker-dot {
  position: absolute;
  top: 50%;
  left: -4px;
  width: 10px;
  height: 10px;
  background: #f5576c;
  border-radius: 50%;
  transform: translateY(-50%);
}

.marker-time {
  position: absolute;
  top: -30px;
  left: -30px;
  font-size: 0.75rem;
  color: #f5576c;
  font-weight: 600;
  white-space: nowrap;
}

.timeline-item {
  position: absolute;
  top: 20px;
  height: 80px;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.5);
  border-radius: 8px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.timeline-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
  z-index: 10;
}

.timeline-item.status-completed {
  background: rgba(74, 222, 128, 0.2);
  border-color: rgba(74, 222, 128, 0.5);
}

.timeline-item.status-in-progress {
  background: rgba(102, 126, 234, 0.3);
  border-color: rgba(102, 126, 234, 0.7);
}

.timeline-subject {
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

.timeline-time {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Cards View */
.cards-view {
  padding: 1rem 0;
}

.session-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.session-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.session-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.6);
}

.card-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
}

.card-status.status-completed {
  background: #4ade80;
}

.card-status.status-in-progress {
  background: #667eea;
  animation: pulse 2s ease-in-out infinite;
}

.card-body h3 {
  margin: 0 0 0.5rem;
  font-size: 1.125rem;
  color: white;
}

.card-body p {
  margin: 0;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.card-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.card-action {
  width: 36px;
  height: 36px;
  background: rgba(74, 222, 128, 0.2);
  border: 1px solid rgba(74, 222, 128, 0.5);
  border-radius: 8px;
  color: #4ade80;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.card-action:hover {
  background: rgba(74, 222, 128, 0.3);
  transform: scale(1.1);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-icon {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.2);
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin: 0 0 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.empty-state p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 2rem;
}

.btn-primary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* New Session Item Design */
.sessions-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.session-item {
  display: grid;
  grid-template-columns: 120px 40px 1fr;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.session-item:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.15);
  transform: translateX(4px);
}

.session-item.active {
  background: rgba(102, 126, 234, 0.08);
  border-color: rgba(102, 126, 234, 0.3);
}

.session-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #667eea;
}

.session-item.completed {
  opacity: 0.7;
}

/* Time Block */
.time-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
}

.time-start {
  font-size: 1.5rem;
  font-weight: 600;
  color: #667eea;
  line-height: 1;
}

.time-duration {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 0.25rem;
}

/* Visual Time Bar */
.time-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.time-line {
  width: 2px;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  position: absolute;
}

.time-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.time-dot.status-pending {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.time-dot.status-in-progress {
  background: #667eea;
  border-color: rgba(102, 126, 234, 0.3);
  animation: pulse 2s infinite;
}

.time-dot.status-completed {
  background: #4ade80;
  border-color: rgba(74, 222, 128, 0.3);
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

/* Session Content */
.session-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.session-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.session-topic {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

/* Action Buttons */
.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.action-btn.play {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
}

.action-btn.play:hover {
  background: rgba(102, 126, 234, 0.3);
  transform: scale(1.1);
}

.action-btn.pause {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.action-btn.pause:hover {
  background: rgba(251, 191, 36, 0.3);
  transform: scale(1.1);
}

.action-btn.completed {
  background: rgba(74, 222, 128, 0.2);
  color: #4ade80;
  cursor: default;
}

/* Progress Bar */
.progress-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-bar-minimal {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill-minimal {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  white-space: nowrap;
}

/* Tags */
.session-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag-pill {
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Empty State Modern */
.empty-state-modern {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-illustration {
  font-size: 4rem;
  color: rgba(102, 126, 234, 0.3);
  margin-bottom: 1.5rem;
}

.empty-state-modern h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin: 0 0 0.5rem;
}

.empty-state-modern p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 2rem;
}

.create-session-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(102, 126, 234, 0.5);
  border-radius: 12px;
  color: #667eea;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.create-session-btn:hover {
  background: rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .session-item {
    grid-template-columns: 100px 30px 1fr;
    gap: 0.75rem;
    padding: 1rem;
  }
  
  .time-block {
    padding: 0.75rem;
  }
  
  .time-start {
    font-size: 1.25rem;
  }
  
  .session-title {
    font-size: 1rem;
  }
  
  .action-btn {
    width: 32px;
    height: 32px;
  }
}

/* Floating Add Button */
.floating-add-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-add-btn:hover {
  transform: translateY(-2px) scale(1.1);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Responsive */
@media (max-width: 768px) {
  .daily-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .session-item {
    flex-direction: column;
    gap: 1rem;
  }
  
  .session-time {
    flex-direction: row;
    gap: 1rem;
    width: 100%;
    justify-content: flex-start;
  }
  
  .time-divider {
    width: 20px;
    height: 2px;
  }
  
  .session-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .timeline-container {
    padding: 1rem;
  }
  
  .session-cards-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .view-toggle {
    width: 100%;
    justify-content: center;
  }
}
</style>