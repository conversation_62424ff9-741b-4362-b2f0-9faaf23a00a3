<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Estatísticas de Estudo</h2>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-body">
        <!-- Overview Stats -->
        <div class="stats-overview">
          <div class="stat-card primary">
            <div class="stat-icon">
              <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.totalSessions }}</h3>
              <p>Sessões Totais</p>
            </div>
          </div>
          
          <div class="stat-card success">
            <div class="stat-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.completedSessions }}</h3>
              <p>Concluídas</p>
            </div>
          </div>
          
          <div class="stat-card info">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.totalHours }}h</h3>
              <p>Horas Estudadas</p>
            </div>
          </div>
          
          <div class="stat-card warning">
            <div class="stat-icon">
              <i class="fas fa-fire"></i>
            </div>
            <div class="stat-content">
              <h3>{{ stats.streak }} dias</h3>
              <p>Sequência</p>
            </div>
          </div>
        </div>
        
        <!-- Performance Metrics -->
        <div class="performance-section">
          <h3>Métricas de Performance</h3>
          
          <div class="metric-item">
            <div class="metric-header">
              <span>Taxa de Conclusão</span>
              <span class="metric-value">{{ completionRate }}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: completionRate + '%' }"></div>
            </div>
          </div>
          
          <div class="metric-item">
            <div class="metric-header">
              <span>Score de Foco</span>
              <span class="metric-value">{{ stats.focusScore }}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill focus" :style="{ width: stats.focusScore + '%' }"></div>
            </div>
          </div>
          
          <div class="metric-item">
            <div class="metric-header">
              <span>Produtividade</span>
              <span class="metric-value">{{ stats.productivityScore }}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill productivity" :style="{ width: stats.productivityScore + '%' }"></div>
            </div>
          </div>
          
          <div class="metric-item">
            <div class="metric-header">
              <span>Média de Pontuação</span>
              <span class="metric-value">{{ stats.averageScore }}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill average" :style="{ width: stats.averageScore + '%' }"></div>
            </div>
          </div>
        </div>
        
        <!-- Study Distribution -->
        <div class="distribution-section">
          <h3>Distribuição por Matéria</h3>
          <div class="distribution-chart">
            <div 
              v-for="subject in subjectDistribution" 
              :key="subject.name"
              class="distribution-item"
            >
              <div class="subject-info">
                <span class="subject-name">{{ subject.name }}</span>
                <span class="subject-hours">{{ subject.hours }}h</span>
              </div>
              <div class="distribution-bar">
                <div 
                  class="distribution-fill" 
                  :style="{ 
                    width: subject.percentage + '%',
                    background: subject.color 
                  }"
                ></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Recent Achievements -->
        <div class="achievements-section">
          <h3>Conquistas Recentes</h3>
          <div class="achievements-grid">
            <div v-for="achievement in achievements" :key="achievement.id" class="achievement-item">
              <div class="achievement-icon" :style="{ background: achievement.color }">
                <i :class="achievement.icon"></i>
              </div>
              <div class="achievement-info">
                <h4>{{ achievement.title }}</h4>
                <p>{{ achievement.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'StatsModal',
  props: {
    stats: {
      type: Object,
      required: true
    }
  },
  emits: ['close'],
  setup(props) {
    const completionRate = computed(() => {
      if (props.stats.totalSessions === 0) return 0
      return Math.round((props.stats.completedSessions / props.stats.totalSessions) * 100)
    })
    
    const subjectDistribution = computed(() => [
      { name: 'Anatomia', hours: 12.5, percentage: 35, color: 'linear-gradient(135deg, #667eea, #764ba2)' },
      { name: 'Farmacologia', hours: 8.3, percentage: 23, color: 'linear-gradient(135deg, #f093fb, #f5576c)' },
      { name: 'Neurociências', hours: 7.2, percentage: 20, color: 'linear-gradient(135deg, #4facfe, #00f2fe)' },
      { name: 'Fisiologia', hours: 4.5, percentage: 13, color: 'linear-gradient(135deg, #fa709a, #fee140)' },
      { name: 'Outros', hours: 3.5, percentage: 9, color: 'linear-gradient(135deg, #84fab0, #8fd3f4)' }
    ])
    
    const achievements = computed(() => [
      {
        id: 1,
        icon: 'fas fa-fire',
        title: 'Sequência de 7 dias',
        description: 'Estudou todos os dias da semana',
        color: 'linear-gradient(135deg, #f093fb, #f5576c)'
      },
      {
        id: 2,
        icon: 'fas fa-brain',
        title: 'Foco Intenso',
        description: 'Manteve foco acima de 80% por 5 sessões',
        color: 'linear-gradient(135deg, #667eea, #764ba2)'
      },
      {
        id: 3,
        icon: 'fas fa-trophy',
        title: '100 Sessões',
        description: 'Completou 100 sessões de estudo',
        color: 'linear-gradient(135deg, #fa709a, #fee140)'
      }
    ])
    
    return {
      completionRate,
      subjectDistribution,
      achievements
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: #1a1a2e;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: white;
}

.close-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.modal-body {
  padding: 2rem;
}

/* Stats Overview */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stat-card.primary {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
}

.stat-card.success {
  background: rgba(74, 222, 128, 0.1);
  border-color: rgba(74, 222, 128, 0.3);
}

.stat-card.info {
  background: rgba(79, 172, 254, 0.1);
  border-color: rgba(79, 172, 254, 0.3);
}

.stat-card.warning {
  background: rgba(251, 191, 36, 0.1);
  border-color: rgba(251, 191, 36, 0.3);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #667eea;
}

.stat-content h3 {
  margin: 0 0 0.5rem;
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.stat-content p {
  margin: 0;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Performance Section */
.performance-section,
.distribution-section,
.achievements-section {
  margin-bottom: 2.5rem;
}

.performance-section h3,
.distribution-section h3,
.achievements-section h3 {
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  color: white;
}

.metric-item {
  margin-bottom: 1.5rem;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.metric-header span {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.metric-value {
  font-weight: 600;
  color: white;
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.5s ease;
}

.progress-fill.focus {
  background: linear-gradient(90deg, #f093fb, #f5576c);
}

.progress-fill.productivity {
  background: linear-gradient(90deg, #4facfe, #00f2fe);
}

.progress-fill.average {
  background: linear-gradient(90deg, #fa709a, #fee140);
}

/* Distribution Section */
.distribution-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.distribution-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.subject-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.subject-name {
  color: rgba(255, 255, 255, 0.8);
}

.subject-hours {
  color: rgba(255, 255, 255, 0.6);
}

.distribution-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.distribution-fill {
  height: 100%;
  transition: width 0.5s ease;
}

/* Achievements Section */
.achievements-grid {
  display: grid;
  gap: 1rem;
}

.achievement-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.achievement-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.achievement-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.achievement-info h4 {
  margin: 0 0 0.25rem;
  font-size: 1rem;
  color: white;
}

.achievement-info p {
  margin: 0;
  font-size: 0.813rem;
  color: rgba(255, 255, 255, 0.6);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>