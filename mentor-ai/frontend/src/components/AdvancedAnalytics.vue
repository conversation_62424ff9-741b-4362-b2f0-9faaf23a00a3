<template>
  <div class="analytics-container">
    <div class="analytics-header">
      <h1>ULTRA Analytics</h1>
      <p>Análise avançada de desempenho de flashcards</p>
    </div>
    
    <div class="analytics-content">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <font-awesome-icon icon="fa-solid fa-chart-line" />
          </div>
          <h3>Taxa de Retenção</h3>
          <p class="stat-value">{{ retentionRate }}%</p>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <font-awesome-icon icon="fa-solid fa-brain" />
          </div>
          <h3>Força da Memória</h3>
          <p class="stat-value">{{ memoryStrength }}/10</p>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <font-awesome-icon icon="fa-solid fa-clock" />
          </div>
          <h3>Tempo de Estudo</h3>
          <p class="stat-value">{{ studyTime }} min</p>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <font-awesome-icon icon="fa-solid fa-trophy" />
          </div>
          <h3>Cards Dominados</h3>
          <p class="stat-value">{{ masteredCards }}</p>
        </div>
      </div>
      
      <div class="placeholder-message">
        <font-awesome-icon icon="fa-solid fa-chart-area" class="placeholder-icon" />
        <h2>Visualizações Avançadas em Desenvolvimento</h2>
        <p>Gráficos interativos, análise de conhecimento e insights personalizados estarão disponíveis em breve!</p>
      </div>
      
      <button @click="goBack" class="back-button">
        <font-awesome-icon icon="fa-solid fa-arrow-left" />
        Voltar aos Flashcards
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';

export default {
  name: 'AdvancedAnalytics',
  props: {
    analyticsData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const router = useRouter();
    const store = useStore();
    
    // Mock data for now
    const retentionRate = ref(85);
    const memoryStrength = ref(7.5);
    const studyTime = ref(120);
    const masteredCards = ref(45);
    
    const goBack = () => {
      router.push('/ai-tools/flashcards');
    };
    
    return {
      retentionRate,
      memoryStrength,
      studyTime,
      masteredCards,
      goBack
    };
  }
};
</script>

<style scoped>
.analytics-container {
  min-height: 100vh;
  background: #0f172a;
  color: #e2e8f0;
  padding: 2rem;
}

.analytics-header {
  text-align: center;
  margin-bottom: 3rem;
}

.analytics-header h1 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.analytics-header p {
  color: #94a3b8;
  font-size: 1.25rem;
}

.analytics-content {
  max-width: 1200px;
  margin: 0 auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
}

.stat-icon {
  font-size: 2.5rem;
  color: #6366f1;
  margin-bottom: 1rem;
}

.stat-card h3 {
  font-size: 1.125rem;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #e2e8f0;
}

.placeholder-message {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(99, 102, 241, 0.05);
  border: 2px dashed rgba(99, 102, 241, 0.2);
  border-radius: 1rem;
  margin-bottom: 2rem;
}

.placeholder-icon {
  font-size: 4rem;
  color: #6366f1;
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.placeholder-message h2 {
  color: #e2e8f0;
  margin-bottom: 1rem;
}

.placeholder-message p {
  color: #94a3b8;
  font-size: 1.125rem;
}

.back-button {
  display: block;
  margin: 0 auto;
  padding: 0.75rem 2rem;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #4f46e5;
  transform: translateY(-2px);
}
</style>