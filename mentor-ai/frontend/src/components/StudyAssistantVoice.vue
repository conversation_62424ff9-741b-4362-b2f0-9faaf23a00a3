<template>
  <div class="voice-assistant-container">
    <!-- Voice Control Panel -->
    <div class="voice-control-panel">
      <h3 class="panel-title">
        <i class="fas fa-microphone-alt"></i>
        Assistente de Voz Neural
      </h3>
      
      <!-- Voice Recognition Status -->
      <div class="voice-status-indicator">
        <div class="status-light" :class="{ 
          active: isListening, 
          processing: isProcessing,
          speaking: isSpeaking 
        }"></div>
        <span class="status-text">{{ voiceStatusText }}</span>
      </div>
      
      <!-- Main Voice Controls -->
      <div class="voice-controls">
        <button 
          @click="toggleListening" 
          class="voice-btn main-voice-btn"
          :class="{ active: isListening, disabled: !speechRecognitionSupported }"
          :disabled="!speechRecognitionSupported"
        >
          <div class="btn-glow"></div>
          <i :class="isListening ? 'fas fa-stop' : 'fas fa-microphone'"></i>
          <span>{{ isListening ? 'Parar' : 'Iniciar' }} Reconhecimento</span>
        </button>
        
        <button 
          @click="toggleSpeaking" 
          class="voice-btn speak-btn"
          :class="{ active: isSpeaking }"
        >
          <i :class="isSpeaking ? 'fas fa-volume-mute' : 'fas fa-volume-up'"></i>
          <span>{{ isSpeaking ? 'Silenciar' : 'Falar' }}</span>
        </button>
      </div>
      
      <!-- Voice Settings -->
      <div class="voice-settings">
        <div class="setting-group">
          <label>
            <i class="fas fa-language"></i>
            Idioma
          </label>
          <select v-model="selectedLanguage" @change="updateVoiceSettings">
            <option value="pt-BR">Português (Brasil)</option>
            <option value="en-US">English (US)</option>
            <option value="es-ES">Español</option>
            <option value="fr-FR">Français</option>
            <option value="de-DE">Deutsch</option>
          </select>
        </div>
        
        <div class="setting-group">
          <label>
            <i class="fas fa-user-voice"></i>
            Voz
          </label>
          <select v-model="selectedVoice" @change="updateVoiceSettings">
            <option v-for="voice in availableVoices" :key="voice.name" :value="voice.name">
              {{ voice.name }} ({{ voice.lang }})
            </option>
          </select>
        </div>
        
        <div class="setting-group">
          <label>
            <i class="fas fa-tachometer-alt"></i>
            Velocidade
          </label>
          <input 
            type="range" 
            v-model="speechRate" 
            min="0.5" 
            max="2" 
            step="0.1"
            @change="updateVoiceSettings"
            class="speed-slider"
          >
          <span class="speed-value">{{ speechRate }}x</span>
        </div>
        
        <div class="setting-group">
          <label>
            <i class="fas fa-music"></i>
            Tom
          </label>
          <input 
            type="range" 
            v-model="speechPitch" 
            min="0.5" 
            max="2" 
            step="0.1"
            @change="updateVoiceSettings"
            class="pitch-slider"
          >
          <span class="pitch-value">{{ speechPitch }}</span>
        </div>
      </div>
    </div>
    
    <!-- Real-time Transcription -->
    <div class="transcription-panel">
      <h4 class="panel-subtitle">
        <i class="fas fa-closed-captioning"></i>
        Transcrição em Tempo Real
      </h4>
      
      <div class="transcription-display" ref="transcriptionDisplay">
        <div v-if="!currentTranscript && !finalTranscripts.length" class="transcription-placeholder">
          <i class="fas fa-comment-dots"></i>
          <p>Aguardando entrada de voz...</p>
        </div>
        
        <!-- Final Transcripts -->
        <div v-for="(transcript, index) in finalTranscripts" :key="index" class="transcript-item final">
          <span class="timestamp">{{ formatTimestamp(transcript.timestamp) }}</span>
          <span class="text">{{ transcript.text }}</span>
          <button @click="copyTranscript(transcript.text)" class="copy-btn" title="Copiar">
            <i class="fas fa-copy"></i>
          </button>
        </div>
        
        <!-- Current Transcript -->
        <div v-if="currentTranscript" class="transcript-item current">
          <span class="timestamp">{{ formatTimestamp(Date.now()) }}</span>
          <span class="text typing">{{ currentTranscript }}</span>
          <span class="cursor"></span>
        </div>
      </div>
      
      <!-- Transcription Actions -->
      <div class="transcription-actions">
        <button @click="clearTranscripts" class="action-btn" :disabled="!finalTranscripts.length">
          <i class="fas fa-trash"></i>
          Limpar
        </button>
        <button @click="saveTranscripts" class="action-btn" :disabled="!finalTranscripts.length">
          <i class="fas fa-save"></i>
          Salvar
        </button>
        <button @click="exportTranscripts" class="action-btn" :disabled="!finalTranscripts.length">
          <i class="fas fa-download"></i>
          Exportar
        </button>
      </div>
    </div>
    
    <!-- Voice Commands -->
    <div class="voice-commands-panel">
      <h4 class="panel-subtitle">
        <i class="fas fa-terminal"></i>
        Comandos de Voz Disponíveis
      </h4>
      
      <div class="commands-grid">
        <div v-for="command in voiceCommands" :key="command.id" 
             class="command-card"
             :class="{ active: command.id === lastExecutedCommand }">
          <div class="command-icon">
            <i :class="command.icon"></i>
          </div>
          <div class="command-info">
            <h5>{{ command.trigger }}</h5>
            <p>{{ command.description }}</p>
          </div>
          <div v-if="command.variations" class="command-variations">
            <span v-for="(variation, idx) in command.variations" :key="idx" class="variation">
              "{{ variation }}"
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Voice Notes -->
    <div class="voice-notes-panel">
      <h4 class="panel-subtitle">
        <i class="fas fa-sticky-note"></i>
        Notas por Voz
      </h4>
      
      <div class="voice-notes-container">
        <div class="note-recorder">
          <button 
            @click="startNoteRecording" 
            class="record-note-btn"
            :class="{ recording: isRecordingNote }"
            :disabled="isListening"
          >
            <i :class="isRecordingNote ? 'fas fa-stop-circle' : 'fas fa-microphone'"></i>
            {{ isRecordingNote ? 'Parar Gravação' : 'Gravar Nota' }}
          </button>
          
          <div v-if="isRecordingNote" class="recording-indicator">
            <div class="pulse"></div>
            <span>Gravando... {{ recordingDuration }}s</span>
          </div>
        </div>
        
        <div class="notes-list">
          <TransitionGroup name="note-list">
            <div v-for="note in voiceNotes" :key="note.id" class="voice-note">
              <div class="note-header">
                <span class="note-time">{{ formatNoteTime(note.timestamp) }}</span>
                <div class="note-actions">
                  <button @click="playNote(note)" class="mini-btn" title="Reproduzir">
                    <i class="fas fa-play"></i>
                  </button>
                  <button @click="editNote(note)" class="mini-btn" title="Editar">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button @click="deleteNote(note)" class="mini-btn danger" title="Excluir">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
              <p class="note-content">{{ note.text }}</p>
              <div v-if="note.tags && note.tags.length" class="note-tags">
                <span v-for="tag in note.tags" :key="tag" class="tag">
                  #{{ tag }}
                </span>
              </div>
            </div>
          </TransitionGroup>
          
          <div v-if="!voiceNotes.length" class="empty-notes">
            <i class="fas fa-microphone-slash"></i>
            <p>Nenhuma nota gravada ainda</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- AI Voice Chat -->
    <div class="ai-voice-chat">
      <h4 class="panel-subtitle">
        <i class="fas fa-robot"></i>
        Chat por Voz com IA
      </h4>
      
      <div class="chat-messages" ref="chatContainer">
        <div v-for="message in chatHistory" :key="message.id" 
             class="chat-message"
             :class="message.type">
          <div class="message-avatar">
            <i :class="message.type === 'user' ? 'fas fa-user' : 'fas fa-robot'"></i>
          </div>
          <div class="message-content">
            <p>{{ message.text }}</p>
            <span class="message-time">{{ formatMessageTime(message.timestamp) }}</span>
          </div>
          <button v-if="message.type === 'ai'" @click="speakMessage(message.text)" class="speak-msg-btn">
            <i class="fas fa-volume-up"></i>
          </button>
        </div>
        
        <div v-if="isAIThinking" class="ai-thinking">
          <div class="thinking-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
      
      <div class="chat-input-area">
        <button 
          @click="startVoiceChat" 
          class="voice-chat-btn"
          :class="{ active: isVoiceChatActive }"
        >
          <i class="fas fa-comments"></i>
          {{ isVoiceChatActive ? 'Conversa Ativa' : 'Iniciar Conversa' }}
        </button>
      </div>
    </div>
    
    <!-- Audio Visualizer -->
    <div class="audio-visualizer">
      <canvas ref="audioCanvas"></canvas>
      <div class="visualizer-overlay">
        <div class="frequency-bars" v-if="isListening || isSpeaking">
          <div v-for="i in 20" :key="i" 
               class="frequency-bar" 
               :style="{ height: frequencyData[i-1] + '%' }">
          </div>
        </div>
      </div>
    </div>
    
    <!-- Voice Training -->
    <div class="voice-training-panel">
      <h4 class="panel-subtitle">
        <i class="fas fa-graduation-cap"></i>
        Treinamento de Pronúncia
      </h4>
      
      <div class="training-exercises">
        <div v-for="exercise in pronunciationExercises" :key="exercise.id" 
             class="exercise-card"
             :class="{ completed: exercise.completed }">
          <div class="exercise-header">
            <h5>{{ exercise.title }}</h5>
            <div class="exercise-difficulty">
              <i v-for="i in exercise.difficulty" :key="i" class="fas fa-star"></i>
            </div>
          </div>
          
          <div class="exercise-content">
            <p class="target-phrase">{{ exercise.phrase }}</p>
            <button @click="startPronunciationExercise(exercise)" class="practice-btn">
              <i class="fas fa-microphone"></i>
              Praticar
            </button>
          </div>
          
          <div v-if="exercise.lastScore" class="exercise-score">
            <div class="score-bar">
              <div class="score-fill" :style="{ width: exercise.lastScore + '%' }"></div>
            </div>
            <span class="score-text">{{ exercise.lastScore }}% de precisão</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue';
import { useStore } from 'vuex';
import Chart from 'chart.js/auto';

export default {
  name: 'StudyAssistantVoice',
  setup() {
    const store = useStore();
    
    // Refs
    const transcriptionDisplay = ref(null);
    const chatContainer = ref(null);
    const audioCanvas = ref(null);
    
    // Speech Recognition
    const recognition = ref(null);
    const speechRecognitionSupported = ref(false);
    const isListening = ref(false);
    const isProcessing = ref(false);
    const currentTranscript = ref('');
    const finalTranscripts = ref([]);
    
    // Speech Synthesis
    const synthesis = window.speechSynthesis;
    const isSpeaking = ref(false);
    const availableVoices = ref([]);
    const selectedVoice = ref('');
    const selectedLanguage = ref('pt-BR');
    const speechRate = ref(1.0);
    const speechPitch = ref(1.0);
    
    // Voice Commands
    const lastExecutedCommand = ref('');
    const voiceCommands = ref([
      {
        id: 'study',
        trigger: 'Estudar',
        icon: 'fas fa-book',
        description: 'Iniciar sessão de estudo',
        variations: ['começar a estudar', 'iniciar estudo', 'vamos estudar'],
        action: () => startStudySession()
      },
      {
        id: 'review',
        trigger: 'Revisar',
        icon: 'fas fa-redo',
        description: 'Iniciar revisão de flashcards',
        variations: ['fazer revisão', 'revisar cartões', 'flashcards'],
        action: () => startReview()
      },
      {
        id: 'explain',
        trigger: 'Explicar',
        icon: 'fas fa-chalkboard-teacher',
        description: 'Solicitar explicação sobre um tópico',
        variations: ['me explique', 'o que é', 'como funciona'],
        action: (topic) => explainTopic(topic)
      },
      {
        id: 'summarize',
        trigger: 'Resumir',
        icon: 'fas fa-compress-alt',
        description: 'Criar resumo do conteúdo',
        variations: ['fazer resumo', 'resumo do', 'sintetizar'],
        action: () => summarizeContent()
      },
      {
        id: 'quiz',
        trigger: 'Quiz',
        icon: 'fas fa-question-circle',
        description: 'Iniciar quiz rápido',
        variations: ['fazer quiz', 'me teste', 'perguntas'],
        action: () => startQuiz()
      },
      {
        id: 'note',
        trigger: 'Anotar',
        icon: 'fas fa-sticky-note',
        description: 'Criar nota por voz',
        variations: ['criar nota', 'anote', 'lembrete'],
        action: (content) => createVoiceNote(content)
      },
      {
        id: 'search',
        trigger: 'Buscar',
        icon: 'fas fa-search',
        description: 'Buscar no conteúdo',
        variations: ['procurar', 'encontrar', 'onde está'],
        action: (query) => searchContent(query)
      },
      {
        id: 'help',
        trigger: 'Ajuda',
        icon: 'fas fa-life-ring',
        description: 'Obter ajuda',
        variations: ['preciso de ajuda', 'me ajude', 'o que posso fazer'],
        action: () => showHelp()
      }
    ]);
    
    // Voice Notes
    const voiceNotes = ref([]);
    const isRecordingNote = ref(false);
    const recordingDuration = ref(0);
    const recordingTimer = ref(null);
    
    // AI Chat
    const chatHistory = ref([]);
    const isVoiceChatActive = ref(false);
    const isAIThinking = ref(false);
    
    // Audio Visualization
    const audioContext = ref(null);
    const analyser = ref(null);
    const frequencyData = ref(Array(20).fill(0));
    const animationFrame = ref(null);
    
    // Pronunciation Training
    const pronunciationExercises = ref([
      {
        id: 1,
        title: 'Termos Médicos Básicos',
        phrase: 'A hipertensão arterial sistêmica é uma condição crônica',
        difficulty: 1,
        completed: false,
        lastScore: null
      },
      {
        id: 2,
        title: 'Anatomia Cardiovascular',
        phrase: 'O ventrículo esquerdo bombeia sangue oxigenado para a circulação sistêmica',
        difficulty: 2,
        completed: false,
        lastScore: null
      },
      {
        id: 3,
        title: 'Farmacologia',
        phrase: 'Os inibidores da enzima conversora de angiotensina reduzem a pressão arterial',
        difficulty: 3,
        completed: false,
        lastScore: null
      },
      {
        id: 4,
        title: 'Fisiologia Respiratória',
        phrase: 'A ventilação alveolar é essencial para a oxigenação adequada do sangue',
        difficulty: 2,
        completed: false,
        lastScore: null
      }
    ]);
    
    // Computed
    const voiceStatusText = computed(() => {
      if (isProcessing.value) return 'Processando...';
      if (isListening.value) return 'Ouvindo...';
      if (isSpeaking.value) return 'Falando...';
      return 'Inativo';
    });
    
    // Methods
    const initializeSpeechRecognition = () => {
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition.value = new SpeechRecognition();
        speechRecognitionSupported.value = true;
        
        // Configure recognition
        recognition.value.continuous = true;
        recognition.value.interimResults = true;
        recognition.value.lang = selectedLanguage.value;
        
        // Event handlers
        recognition.value.onstart = () => {
          isListening.value = true;
          console.log('Reconhecimento de voz iniciado');
        };
        
        recognition.value.onend = () => {
          isListening.value = false;
          console.log('Reconhecimento de voz finalizado');
        };
        
        recognition.value.onresult = (event) => {
          let interimTranscript = '';
          let finalTranscript = '';
          
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            
            if (event.results[i].isFinal) {
              finalTranscript += transcript;
            } else {
              interimTranscript += transcript;
            }
          }
          
          if (finalTranscript) {
            finalTranscripts.value.push({
              text: finalTranscript,
              timestamp: Date.now()
            });
            currentTranscript.value = '';
            
            // Check for voice commands
            checkVoiceCommands(finalTranscript);
            
            // Auto scroll
            nextTick(() => {
              if (transcriptionDisplay.value) {
                transcriptionDisplay.value.scrollTop = transcriptionDisplay.value.scrollHeight;
              }
            });
          } else {
            currentTranscript.value = interimTranscript;
          }
        };
        
        recognition.value.onerror = (event) => {
          console.error('Erro no reconhecimento de voz:', event.error);
          if (event.error === 'no-speech') {
            // Continue listening
            if (isListening.value) {
              recognition.value.start();
            }
          }
        };
      }
    };
    
    const toggleListening = () => {
      if (!recognition.value) return;
      
      if (isListening.value) {
        recognition.value.stop();
      } else {
        recognition.value.start();
      }
    };
    
    const loadVoices = () => {
      availableVoices.value = synthesis.getVoices().filter(voice => 
        voice.lang.startsWith('pt') || 
        voice.lang.startsWith('en') || 
        voice.lang.startsWith('es') ||
        voice.lang.startsWith('fr') ||
        voice.lang.startsWith('de')
      );
      
      if (availableVoices.value.length > 0 && !selectedVoice.value) {
        selectedVoice.value = availableVoices.value[0].name;
      }
    };
    
    const speak = (text) => {
      if (isSpeaking.value) {
        synthesis.cancel();
        isSpeaking.value = false;
        return;
      }
      
      const utterance = new SpeechSynthesisUtterance(text);
      
      // Set voice
      const voice = availableVoices.value.find(v => v.name === selectedVoice.value);
      if (voice) {
        utterance.voice = voice;
      }
      
      // Set parameters
      utterance.rate = speechRate.value;
      utterance.pitch = speechPitch.value;
      utterance.lang = selectedLanguage.value;
      
      // Event handlers
      utterance.onstart = () => {
        isSpeaking.value = true;
      };
      
      utterance.onend = () => {
        isSpeaking.value = false;
      };
      
      synthesis.speak(utterance);
    };
    
    const toggleSpeaking = () => {
      if (isSpeaking.value) {
        synthesis.cancel();
        isSpeaking.value = false;
      } else {
        const sampleText = 'Olá! Eu sou seu assistente de voz neural. Como posso ajudá-lo hoje?';
        speak(sampleText);
      }
    };
    
    const updateVoiceSettings = () => {
      if (recognition.value) {
        recognition.value.lang = selectedLanguage.value;
      }
    };
    
    const checkVoiceCommands = (transcript) => {
      const lowerTranscript = transcript.toLowerCase();
      
      for (const command of voiceCommands.value) {
        const triggers = [command.trigger.toLowerCase(), ...command.variations.map(v => v.toLowerCase())];
        
        for (const trigger of triggers) {
          if (lowerTranscript.includes(trigger)) {
            lastExecutedCommand.value = command.id;
            isProcessing.value = true;
            
            // Extract parameters if needed
            const params = lowerTranscript.replace(trigger, '').trim();
            
            // Execute command
            setTimeout(() => {
              command.action(params);
              isProcessing.value = false;
              
              // Voice feedback
              speak(`Executando comando: ${command.description}`);
              
              // Reset highlight after 2 seconds
              setTimeout(() => {
                lastExecutedCommand.value = '';
              }, 2000);
            }, 500);
            
            return;
          }
        }
      }
    };
    
    // Voice command actions
    const startStudySession = () => {
      store.dispatch('studyAssistant/startStudySession', {
        topic: 'Sessão por Voz',
        mode: 'focused'
      });
      speak('Sessão de estudo iniciada. Boa sorte!');
    };
    
    const startReview = () => {
      speak('Iniciando revisão de flashcards');
      // Navigate to flashcards or trigger review
    };
    
    const explainTopic = (topic) => {
      if (!topic) {
        speak('Por favor, diga o que você gostaria que eu explicasse');
        return;
      }
      
      isAIThinking.value = true;
      
      // Simulate AI explanation
      setTimeout(() => {
        const explanation = `Aqui está uma explicação sobre ${topic}. Este é um conceito importante que envolve...`;
        
        chatHistory.value.push({
          id: Date.now(),
          type: 'user',
          text: `Explique ${topic}`,
          timestamp: Date.now()
        });
        
        chatHistory.value.push({
          id: Date.now() + 1,
          type: 'ai',
          text: explanation,
          timestamp: Date.now()
        });
        
        speak(explanation);
        isAIThinking.value = false;
        
        // Auto scroll chat
        nextTick(() => {
          if (chatContainer.value) {
            chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
          }
        });
      }, 1500);
    };
    
    const summarizeContent = () => {
      speak('Criando resumo do conteúdo atual');
      // Implement summarization logic
    };
    
    const startQuiz = () => {
      speak('Preparando quiz. Responda as perguntas em voz alta');
      // Implement quiz logic
    };
    
    const createVoiceNote = (content) => {
      if (!content) {
        speak('Por favor, diga o conteúdo da nota após o comando');
        return;
      }
      
      const note = {
        id: Date.now(),
        text: content,
        timestamp: Date.now(),
        tags: extractTags(content)
      };
      
      voiceNotes.value.unshift(note);
      speak('Nota criada com sucesso');
    };
    
    const searchContent = (query) => {
      if (!query) {
        speak('O que você gostaria de buscar?');
        return;
      }
      
      speak(`Buscando por ${query}`);
      // Implement search logic
    };
    
    const showHelp = () => {
      speak('Você pode usar comandos como: estudar, revisar, explicar, resumir, quiz, anotar, buscar. Basta falar naturalmente!');
    };
    
    // Transcription methods
    const copyTranscript = (text) => {
      navigator.clipboard.writeText(text);
      // Show toast notification
    };
    
    const clearTranscripts = () => {
      finalTranscripts.value = [];
      currentTranscript.value = '';
    };
    
    const saveTranscripts = () => {
      const transcriptsText = finalTranscripts.value
        .map(t => `[${formatTimestamp(t.timestamp)}] ${t.text}`)
        .join('\n');
      
      localStorage.setItem('voiceTranscripts', JSON.stringify({
        transcripts: finalTranscripts.value,
        savedAt: Date.now()
      }));
      
      speak('Transcrições salvas com sucesso');
    };
    
    const exportTranscripts = () => {
      const transcriptsText = finalTranscripts.value
        .map(t => `[${formatTimestamp(t.timestamp)}] ${t.text}`)
        .join('\n');
      
      const blob = new Blob([transcriptsText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transcricao_${new Date().toISOString().split('T')[0]}.txt`;
      a.click();
      URL.revokeObjectURL(url);
      
      speak('Transcrições exportadas');
    };
    
    // Voice notes methods
    const startNoteRecording = () => {
      if (isRecordingNote.value) {
        stopNoteRecording();
        return;
      }
      
      isRecordingNote.value = true;
      recordingDuration.value = 0;
      
      // Start recording timer
      recordingTimer.value = setInterval(() => {
        recordingDuration.value++;
      }, 1000);
      
      // Start speech recognition for note
      if (recognition.value && !isListening.value) {
        recognition.value.start();
      }
    };
    
    const stopNoteRecording = () => {
      isRecordingNote.value = false;
      
      if (recordingTimer.value) {
        clearInterval(recordingTimer.value);
        recordingTimer.value = null;
      }
      
      // Get the last transcript as note
      if (finalTranscripts.value.length > 0) {
        const lastTranscript = finalTranscripts.value[finalTranscripts.value.length - 1];
        createVoiceNote(lastTranscript.text);
      }
    };
    
    const playNote = (note) => {
      speak(note.text);
    };
    
    const editNote = (note) => {
      // Implement edit functionality
      console.log('Edit note:', note);
    };
    
    const deleteNote = (note) => {
      const index = voiceNotes.value.findIndex(n => n.id === note.id);
      if (index > -1) {
        voiceNotes.value.splice(index, 1);
        speak('Nota excluída');
      }
    };
    
    const extractTags = (text) => {
      const words = text.toLowerCase().split(' ');
      const tags = [];
      
      // Extract hashtags
      const hashtagMatches = text.match(/#\w+/g);
      if (hashtagMatches) {
        tags.push(...hashtagMatches.map(tag => tag.substring(1)));
      }
      
      // Auto-tag based on keywords
      if (words.some(w => ['medicina', 'médico', 'médica'].includes(w))) {
        tags.push('medicina');
      }
      if (words.some(w => ['anatomia', 'órgão', 'corpo'].includes(w))) {
        tags.push('anatomia');
      }
      if (words.some(w => ['farmaco', 'remédio', 'medicamento'].includes(w))) {
        tags.push('farmacologia');
      }
      
      return [...new Set(tags)]; // Remove duplicates
    };
    
    // AI Chat methods
    const startVoiceChat = () => {
      isVoiceChatActive.value = !isVoiceChatActive.value;
      
      if (isVoiceChatActive.value) {
        speak('Chat por voz ativado. Pode fazer suas perguntas!');
        if (!isListening.value) {
          toggleListening();
        }
      } else {
        speak('Chat por voz desativado');
        if (isListening.value) {
          toggleListening();
        }
      }
    };
    
    const speakMessage = (text) => {
      speak(text);
    };
    
    // Audio visualization
    const initializeAudioVisualization = () => {
      audioContext.value = new (window.AudioContext || window.webkitAudioContext)();
      analyser.value = audioContext.value.createAnalyser();
      analyser.value.fftSize = 64;
      
      // Get user media for visualization
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
          const source = audioContext.value.createMediaStreamSource(stream);
          source.connect(analyser.value);
          
          visualizeAudio();
        })
        .catch(err => {
          console.error('Erro ao acessar microfone:', err);
        });
    };
    
    const visualizeAudio = () => {
      if (!analyser.value) return;
      
      const bufferLength = analyser.value.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      
      const draw = () => {
        animationFrame.value = requestAnimationFrame(draw);
        
        analyser.value.getByteFrequencyData(dataArray);
        
        // Update frequency bars
        for (let i = 0; i < 20; i++) {
          const index = Math.floor(i * bufferLength / 20);
          frequencyData.value[i] = (dataArray[index] / 255) * 100;
        }
      };
      
      draw();
    };
    
    // Pronunciation training
    const startPronunciationExercise = async (exercise) => {
      // Show the target phrase
      speak(`Repita após mim: ${exercise.phrase}`);
      
      // Wait for speech to finish
      await new Promise(resolve => {
        const checkSpeaking = setInterval(() => {
          if (!isSpeaking.value) {
            clearInterval(checkSpeaking);
            resolve();
          }
        }, 100);
      });
      
      // Start listening for user's pronunciation
      currentTranscript.value = '';
      finalTranscripts.value = [];
      
      if (!isListening.value) {
        toggleListening();
      }
      
      // Simulate pronunciation scoring after 5 seconds
      setTimeout(() => {
        if (isListening.value) {
          toggleListening();
        }
        
        // Calculate score based on similarity
        const userText = finalTranscripts.value.map(t => t.text).join(' ').toLowerCase();
        const targetText = exercise.phrase.toLowerCase();
        const score = calculateSimilarity(userText, targetText);
        
        exercise.lastScore = Math.round(score * 100);
        
        if (score > 0.8) {
          exercise.completed = true;
          speak(`Excelente! Você acertou ${exercise.lastScore}% da pronúncia`);
        } else if (score > 0.6) {
          speak(`Bom trabalho! ${exercise.lastScore}% de precisão. Tente novamente para melhorar`);
        } else {
          speak(`Continue praticando. ${exercise.lastScore}% de precisão. Vamos tentar mais uma vez?`);
        }
      }, 5000);
    };
    
    const calculateSimilarity = (str1, str2) => {
      // Simple similarity calculation (Levenshtein distance based)
      const longer = str1.length > str2.length ? str1 : str2;
      const shorter = str1.length > str2.length ? str2 : str1;
      
      if (longer.length === 0) return 1.0;
      
      const editDistance = getEditDistance(longer, shorter);
      return (longer.length - editDistance) / longer.length;
    };
    
    const getEditDistance = (str1, str2) => {
      const matrix = [];
      
      for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
      }
      
      for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
      }
      
      for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
          if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
            matrix[i][j] = matrix[i - 1][j - 1];
          } else {
            matrix[i][j] = Math.min(
              matrix[i - 1][j - 1] + 1,
              matrix[i][j - 1] + 1,
              matrix[i - 1][j] + 1
            );
          }
        }
      }
      
      return matrix[str2.length][str1.length];
    };
    
    // Formatting helpers
    const formatTimestamp = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      });
    };
    
    const formatNoteTime = (timestamp) => {
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date;
      
      if (diff < 60000) return 'Agora';
      if (diff < 3600000) return `${Math.floor(diff / 60000)} min atrás`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)} h atrás`;
      
      return date.toLocaleDateString('pt-BR');
    };
    
    const formatMessageTime = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    };
    
    // Lifecycle
    onMounted(() => {
      initializeSpeechRecognition();
      loadVoices();
      
      // Load voices when ready
      if (speechSynthesis.onvoiceschanged !== undefined) {
        speechSynthesis.onvoiceschanged = loadVoices;
      }
      
      // Initialize audio visualization
      initializeAudioVisualization();
      
      // Load saved data
      const savedTranscripts = localStorage.getItem('voiceTranscripts');
      if (savedTranscripts) {
        const data = JSON.parse(savedTranscripts);
        finalTranscripts.value = data.transcripts || [];
      }
    });
    
    onUnmounted(() => {
      if (recognition.value) {
        recognition.value.stop();
      }
      
      if (synthesis.speaking) {
        synthesis.cancel();
      }
      
      if (animationFrame.value) {
        cancelAnimationFrame(animationFrame.value);
      }
      
      if (audioContext.value) {
        audioContext.value.close();
      }
      
      if (recordingTimer.value) {
        clearInterval(recordingTimer.value);
      }
    });
    
    return {
      // Refs
      transcriptionDisplay,
      chatContainer,
      audioCanvas,
      
      // State
      speechRecognitionSupported,
      isListening,
      isProcessing,
      isSpeaking,
      currentTranscript,
      finalTranscripts,
      availableVoices,
      selectedVoice,
      selectedLanguage,
      speechRate,
      speechPitch,
      voiceStatusText,
      lastExecutedCommand,
      voiceCommands,
      voiceNotes,
      isRecordingNote,
      recordingDuration,
      chatHistory,
      isVoiceChatActive,
      isAIThinking,
      frequencyData,
      pronunciationExercises,
      
      // Methods
      toggleListening,
      toggleSpeaking,
      updateVoiceSettings,
      copyTranscript,
      clearTranscripts,
      saveTranscripts,
      exportTranscripts,
      startNoteRecording,
      playNote,
      editNote,
      deleteNote,
      startVoiceChat,
      speakMessage,
      startPronunciationExercise,
      formatTimestamp,
      formatNoteTime,
      formatMessageTime
    };
  }
};
</script>

<style scoped>
.voice-assistant-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #1a1a2e 0%, #0f0f1e 100%);
  color: #fff;
  min-height: 100vh;
}

/* Voice Control Panel */
.voice-control-panel {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-title {
  font-size: 1.8rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.voice-status-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.status-light {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #444;
  position: relative;
  transition: all 0.3s ease;
}

.status-light.active {
  background: #4ade80;
  box-shadow: 0 0 20px #4ade80;
}

.status-light.processing {
  background: #fbbf24;
  box-shadow: 0 0 20px #fbbf24;
  animation: pulse 1s infinite;
}

.status-light.speaking {
  background: #60a5fa;
  box-shadow: 0 0 20px #60a5fa;
  animation: pulse 0.8s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.status-text {
  font-size: 1.1rem;
  color: #e5e7eb;
}

/* Voice Controls */
.voice-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.voice-btn {
  flex: 1;
  padding: 1.2rem 2rem;
  border: none;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
}

.voice-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.voice-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.voice-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.main-voice-btn {
  position: relative;
}

.btn-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  opacity: 0;
  filter: blur(10px);
  transition: opacity 0.3s ease;
  z-index: -1;
}

.voice-btn.active .btn-glow {
  opacity: 0.5;
}

/* Voice Settings */
.voice-settings {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.9rem;
}

.setting-group select,
.setting-group input[type="range"] {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.8rem;
  color: #fff;
  font-size: 0.95rem;
}

.setting-group select option {
  background: #1a1a2e;
}

.speed-slider,
.pitch-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  outline: none;
  padding: 0 !important;
}

.speed-slider::-webkit-slider-thumb,
.pitch-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  cursor: pointer;
}

.speed-value,
.pitch-value {
  color: #60a5fa;
  font-weight: 600;
}

/* Transcription Panel */
.transcription-panel {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.panel-subtitle {
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: #e5e7eb;
}

.transcription-display {
  flex: 1;
  max-height: 400px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.transcription-placeholder {
  text-align: center;
  color: #6b7280;
  padding: 3rem;
}

.transcription-placeholder i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.transcript-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.transcript-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.transcript-item.current {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.timestamp {
  color: #9ca3af;
  font-size: 0.85rem;
  white-space: nowrap;
}

.transcript-item .text {
  flex: 1;
  line-height: 1.5;
}

.text.typing::after {
  content: '';
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background: #667eea;
  animation: blink 1s infinite;
  vertical-align: text-bottom;
  margin-left: 4px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.copy-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.4rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.copy-btn:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.transcription-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  flex: 1;
  padding: 0.8rem 1.2rem;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.action-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Voice Commands Panel */
.voice-commands-panel {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  grid-column: span 2;
}

.commands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.command-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.command-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
}

.command-card.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.command-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.command-icon i {
  font-size: 1.5rem;
}

.command-info h5 {
  color: #fff;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.command-info p {
  color: #9ca3af;
  font-size: 0.9rem;
  line-height: 1.4;
}

.command-variations {
  margin-top: 0.8rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.4rem;
}

.variation {
  font-size: 0.8rem;
  color: #60a5fa;
  background: rgba(96, 165, 250, 0.1);
  padding: 0.2rem 0.6rem;
  border-radius: 4px;
}

/* Voice Notes Panel */
.voice-notes-panel {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.note-recorder {
  margin-bottom: 1.5rem;
}

.record-note-btn {
  width: 100%;
  padding: 1rem 2rem;
  border: none;
  border-radius: 10px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
}

.record-note-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(239, 68, 68, 0.4);
}

.record-note-btn.recording {
  background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
  animation: recording-pulse 1.5s infinite;
}

@keyframes recording-pulse {
  0%, 100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }
  50% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
}

.record-note-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.recording-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
  color: #ef4444;
}

.recording-indicator .pulse {
  width: 10px;
  height: 10px;
  background: #ef4444;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.notes-list {
  max-height: 400px;
  overflow-y: auto;
}

.voice-note {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  padding: 1.2rem;
  margin-bottom: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.voice-note:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(102, 126, 234, 0.2);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}

.note-time {
  color: #9ca3af;
  font-size: 0.85rem;
}

.note-actions {
  display: flex;
  gap: 0.5rem;
}

.mini-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #9ca3af;
  padding: 0.4rem 0.6rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
}

.mini-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
}

.mini-btn.danger:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.note-content {
  color: #e5e7eb;
  line-height: 1.5;
  margin-bottom: 0.8rem;
}

.note-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  font-size: 0.8rem;
  color: #60a5fa;
  background: rgba(96, 165, 250, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
}

.empty-notes {
  text-align: center;
  color: #6b7280;
  padding: 3rem;
}

.empty-notes i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Note list transitions */
.note-list-enter-active,
.note-list-leave-active {
  transition: all 0.3s ease;
}

.note-list-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.note-list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

/* AI Voice Chat */
.ai-voice-chat {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.chat-message {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  animation: messageSlide 0.3s ease;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.chat-message.user .message-avatar {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.message-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.chat-message.user .message-content {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}

.message-content p {
  color: #e5e7eb;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.message-time {
  color: #9ca3af;
  font-size: 0.75rem;
}

.speak-msg-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #9ca3af;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.speak-msg-btn:hover {
  background: rgba(96, 165, 250, 0.2);
  color: #60a5fa;
}

.ai-thinking {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.thinking-dots {
  display: flex;
  gap: 0.5rem;
}

.thinking-dots span {
  width: 10px;
  height: 10px;
  background: #667eea;
  border-radius: 50%;
  animation: thinking 1.4s infinite;
}

.thinking-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes thinking {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.3);
    opacity: 1;
  }
}

.chat-input-area {
  display: flex;
  gap: 1rem;
}

.voice-chat-btn {
  flex: 1;
  padding: 1rem;
  border: none;
  border-radius: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
}

.voice-chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.voice-chat-btn.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.4);
}

/* Audio Visualizer */
.audio-visualizer {
  grid-column: span 2;
  height: 150px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.audio-visualizer canvas {
  width: 100%;
  height: 100%;
  opacity: 0.3;
}

.visualizer-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.frequency-bars {
  display: flex;
  align-items: flex-end;
  gap: 4px;
  height: 80%;
}

.frequency-bar {
  width: 20px;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 4px 4px 0 0;
  transition: height 0.1s ease;
  min-height: 4px;
}

/* Voice Training Panel */
.voice-training-panel {
  grid-column: span 2;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.training-exercises {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.exercise-card {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.exercise-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
}

.exercise-card.completed {
  border-color: rgba(16, 185, 129, 0.3);
  background: rgba(16, 185, 129, 0.05);
}

.exercise-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.exercise-header h5 {
  color: #fff;
  font-size: 1.1rem;
}

.exercise-difficulty {
  display: flex;
  gap: 0.2rem;
}

.exercise-difficulty i {
  color: #fbbf24;
  font-size: 0.8rem;
}

.exercise-content {
  margin-bottom: 1rem;
}

.target-phrase {
  color: #e5e7eb;
  font-style: italic;
  line-height: 1.5;
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.practice-btn {
  width: 100%;
  padding: 0.8rem;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.practice-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.exercise-score {
  margin-top: 1rem;
}

.score-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, #ef4444 0%, #fbbf24 50%, #10b981 100%);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.score-text {
  color: #9ca3af;
  font-size: 0.85rem;
}

/* Transitions */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateX(20px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .voice-assistant-container {
    grid-template-columns: 1fr;
  }
  
  .voice-commands-panel,
  .audio-visualizer,
  .voice-training-panel {
    grid-column: span 1;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}
</style>