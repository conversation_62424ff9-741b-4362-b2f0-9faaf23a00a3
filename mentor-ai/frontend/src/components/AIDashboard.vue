<template>
  <div class="ai-dashboard-ultra">
    <!-- Holographic Background -->
    <div class="holo-background">
      <canvas ref="holoCanvas" class="holo-canvas"></canvas>
      <div class="holo-grid"></div>
    </div>

    <!-- Neural Particles -->
    <div class="neural-particles">
      <div v-for="i in 200" :key="`np-${i}`" 
           class="neural-particle"
           :style="{
             '--delay': Math.random() * 20 + 's',
             '--duration': 20 + Math.random() * 30 + 's',
             '--size': 2 + Math.random() * 4 + 'px'
           }">
      </div>
    </div>

    <!-- Main Dashboard -->
    <div class="dashboard-container">
      <!-- Header -->
      <header class="dashboard-header">
        <div class="header-left">
          <h1 class="dashboard-title">
            <span class="title-icon">🧠</span>
            AI Performance Dashboard
          </h1>
          <div class="sync-status" :class="{ active: isSyncing }">
            <span class="sync-icon"></span>
            <span>{{ syncStatus }}</span>
          </div>
        </div>
        
        <div class="header-right">
          <div class="quantum-status">
            <div class="quantum-indicator" :style="{ '--coherence': systemMetrics.overallCoherence + '%' }"></div>
            <span>Quantum Coherence: {{ Math.floor(systemMetrics.overallCoherence) }}%</span>
          </div>
        </div>
      </header>

      <!-- AI Engines Grid -->
      <div class="engines-grid">
        <div v-for="engine in engines" 
             :key="engine.id"
             class="engine-card"
             :class="{ active: engine.status === 'active' }"
             @click="selectEngine(engine)">
          
          <!-- Engine Header -->
          <div class="engine-header">
            <div class="engine-icon" :style="{ background: engine.color }">
              <font-awesome-icon :icon="engine.icon" />
            </div>
            <div class="engine-info">
              <h3>{{ engine.name }}</h3>
              <span class="engine-status" :class="engine.status">{{ engine.status }}</span>
            </div>
          </div>

          <!-- Real-time Metrics -->
          <div class="engine-metrics">
            <div class="metric-row">
              <span class="metric-label">Accuracy</span>
              <div class="metric-bar">
                <div class="metric-fill" :style="{ width: engine.metrics.accuracy + '%', background: 'linear-gradient(90deg, #00ff88, #00ffff)' }"></div>
              </div>
              <span class="metric-value">{{ engine.metrics.accuracy }}%</span>
            </div>
            
            <div class="metric-row">
              <span class="metric-label">Speed</span>
              <div class="metric-bar">
                <div class="metric-fill" :style="{ width: engine.metrics.speed + '%', background: 'linear-gradient(90deg, #ff00ff, #ff88ff)' }"></div>
              </div>
              <span class="metric-value">{{ engine.metrics.speed }}%</span>
            </div>
            
            <div class="metric-row">
              <span class="metric-label">Efficiency</span>
              <div class="metric-bar">
                <div class="metric-fill" :style="{ width: engine.metrics.efficiency + '%', background: 'linear-gradient(90deg, #ffff00, #ffaa00)' }"></div>
              </div>
              <span class="metric-value">{{ engine.metrics.efficiency }}%</span>
            </div>
          </div>

          <!-- Neural Connections -->
          <div class="engine-connections">
            <div class="connection-item" v-for="connection in engine.connections" :key="connection">
              <span class="connection-dot" :class="{ active: isConnected(engine.id, connection) }"></span>
              <span class="connection-name">{{ getEngineName(connection) }}</span>
            </div>
          </div>

          <!-- Live Activity Sparkline -->
          <canvas :ref="`sparkline-${engine.id}`" class="activity-sparkline"></canvas>
        </div>
      </div>

      <!-- System Overview -->
      <div class="system-overview">
        <div class="overview-section">
          <h2>System Performance</h2>
          <div class="performance-grid">
            <div class="perf-card">
              <div class="perf-icon">⚡</div>
              <div class="perf-info">
                <div class="perf-value">{{ totalRequests }}</div>
                <div class="perf-label">Total Requests</div>
              </div>
            </div>
            
            <div class="perf-card">
              <div class="perf-icon">🚀</div>
              <div class="perf-info">
                <div class="perf-value">{{ avgResponseTime }}ms</div>
                <div class="perf-label">Avg Response</div>
              </div>
            </div>
            
            <div class="perf-card">
              <div class="perf-icon">💎</div>
              <div class="perf-info">
                <div class="perf-value">{{ quantumOperations }}</div>
                <div class="perf-label">Quantum Ops</div>
              </div>
            </div>
            
            <div class="perf-card">
              <div class="perf-icon">🔗</div>
              <div class="perf-info">
                <div class="perf-value">{{ activeConnections }}</div>
                <div class="perf-label">Active Links</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Neural Network Visualization -->
        <div class="overview-section">
          <h2>Neural Network Status</h2>
          <div class="neural-viz">
            <svg ref="neuralSvg" class="neural-svg" viewBox="0 0 800 400"></svg>
          </div>
        </div>

        <!-- Quantum Entanglement Map -->
        <div class="overview-section">
          <h2>Quantum Entanglement Map</h2>
          <div class="entanglement-map">
            <canvas ref="entanglementCanvas" class="entanglement-canvas"></canvas>
          </div>
        </div>
      </div>

      <!-- Live Workflow Monitor -->
      <div class="workflow-monitor">
        <h2>Active AI Workflows</h2>
        <div class="workflow-list">
          <div v-for="workflow in activeWorkflows" 
               :key="workflow.id"
               class="workflow-item"
               :class="workflow.status">
            
            <div class="workflow-header">
              <span class="workflow-name">{{ workflow.name }}</span>
              <span class="workflow-status">{{ workflow.status }}</span>
            </div>
            
            <div class="workflow-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: workflow.progress + '%' }"></div>
              </div>
              <span class="progress-text">{{ workflow.currentStep }}/{{ workflow.totalSteps }}</span>
            </div>
            
            <div class="workflow-engines">
              <span v-for="engine in workflow.engines" 
                    :key="engine"
                    class="workflow-engine"
                    :style="{ background: getEngineColor(engine) }">
                {{ engine }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- AI Communication Log -->
      <div class="communication-log">
        <h2>Inter-AI Communication</h2>
        <div class="log-container" ref="logContainer">
          <div v-for="message in communicationLog" 
               :key="message.id"
               class="log-message"
               :class="message.type">
            
            <div class="message-header">
              <span class="message-from">{{ message.from }}</span>
              <span class="message-arrow">→</span>
              <span class="message-to">{{ message.to }}</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>
            
            <div class="message-content">{{ message.content }}</div>
            
            <div class="message-meta">
              <span class="meta-item" v-if="message.quantum">
                <span class="quantum-badge">Q</span>
                Quantum
              </span>
              <span class="meta-item">{{ message.latency }}ms</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Engine Detail Modal -->
    <transition name="modal">
      <div v-if="selectedEngine" class="engine-modal" @click.self="selectedEngine = null">
        <div class="modal-content">
          <div class="modal-header">
            <h2>{{ selectedEngine.name }} - Detailed Analytics</h2>
            <button @click="selectedEngine = null" class="close-modal">×</button>
          </div>
          
          <div class="modal-body">
            <!-- Detailed metrics charts -->
            <div class="detail-charts">
              <canvas ref="detailChart" class="detail-chart"></canvas>
            </div>
            
            <!-- Performance history -->
            <div class="performance-history">
              <h3>Performance History (24h)</h3>
              <canvas ref="historyChart" class="history-chart"></canvas>
            </div>
            
            <!-- Operations log -->
            <div class="operations-log">
              <h3>Recent Operations</h3>
              <div class="ops-list">
                <div v-for="op in selectedEngine.operations" :key="op.id" class="op-item">
                  <span class="op-time">{{ formatTime(op.timestamp) }}</span>
                  <span class="op-type">{{ op.type }}</span>
                  <span class="op-status" :class="op.status">{{ op.status }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import aiIntegrationService from '@/services/aiIntegrationService'

export default {
  name: 'AIDashboard',
  
  setup() {
    // State
    const engines = ref([])
    const selectedEngine = ref(null)
    const systemMetrics = ref({ overallCoherence: 0 })
    const activeWorkflows = ref([])
    const communicationLog = ref([])
    const isSyncing = ref(false)
    const syncStatus = ref('Synchronized')
    
    // Performance metrics
    const totalRequests = ref(0)
    const avgResponseTime = ref(0)
    const quantumOperations = ref(0)
    const activeConnections = ref(0)
    
    // Canvas refs
    const holoCanvas = ref(null)
    const neuralSvg = ref(null)
    const entanglementCanvas = ref(null)
    const detailChart = ref(null)
    const historyChart = ref(null)
    const logContainer = ref(null)
    
    // Engine configurations
    const engineConfigs = [
      { id: 'second-brain', name: 'Second Brain', icon: 'brain', color: '#00ffff' },
      { id: 'round-ai', name: 'Round AI', icon: 'microphone', color: '#ff00ff' },
      { id: 'flashcards-ai', name: 'Flashcards AI', icon: 'layer-group', color: '#ffff00' },
      { id: 'questions-ai', name: 'Questions AI', icon: 'tasks', color: '#00ff00' }
    ]
    
    // Initialize engines
    const initializeEngines = () => {
      engines.value = engineConfigs.map(config => ({
        ...config,
        status: 'initializing',
        metrics: {
          accuracy: 0,
          speed: 0,
          efficiency: 0,
          quantumCoherence: 0
        },
        connections: [],
        operations: [],
        sparklineData: Array(50).fill(0)
      }))
    }
    
    // Update engine metrics
    const updateEngineMetrics = () => {
      engines.value.forEach(engine => {
        const metrics = aiIntegrationService.getPerformanceMetrics(engine.id)
        if (metrics) {
          engine.status = metrics.status
          engine.metrics = metrics.performance
          engine.connections = Array.from(metrics.connections || [])
          
          // Update sparkline data
          engine.sparklineData.shift()
          engine.sparklineData.push(metrics.health || 0)
          
          // Draw sparkline
          drawSparkline(engine.id, engine.sparklineData)
        }
      })
      
      // Update system metrics
      const sysMetrics = aiIntegrationService.getPerformanceMetrics()
      if (sysMetrics && sysMetrics.system) {
        systemMetrics.value = sysMetrics.system
        activeConnections.value = sysMetrics.system.entanglements
      }
    }
    
    // Draw sparkline
    const drawSparkline = (engineId, data) => {
      const canvas = document.querySelector(`canvas[ref="sparkline-${engineId}"]`)
      if (!canvas) return
      
      const ctx = canvas.getContext('2d')
      const width = canvas.width = 120
      const height = canvas.height = 30
      
      ctx.clearRect(0, 0, width, height)
      ctx.strokeStyle = '#00ffff'
      ctx.lineWidth = 1
      ctx.beginPath()
      
      data.forEach((value, index) => {
        const x = (index / (data.length - 1)) * width
        const y = height - (value / 100) * height
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      
      ctx.stroke()
    }
    
    // Simulate workflows
    const simulateWorkflows = () => {
      const workflows = [
        { name: 'Multi-AI Content Analysis', engines: ['second-brain', 'questions-ai'], steps: 5 },
        { name: 'Audio Processing Pipeline', engines: ['round-ai', 'second-brain'], steps: 3 },
        { name: 'Learning Optimization', engines: ['flashcards-ai', 'questions-ai'], steps: 4 }
      ]
      
      activeWorkflows.value = workflows.map((w, i) => ({
        id: `workflow-${i}`,
        name: w.name,
        engines: w.engines,
        status: Math.random() > 0.7 ? 'completed' : 'running',
        progress: Math.floor(Math.random() * 100),
        currentStep: Math.floor(Math.random() * w.steps) + 1,
        totalSteps: w.steps
      }))
    }
    
    // Simulate communication
    const simulateCommunication = () => {
      const messages = [
        { from: 'Second Brain', to: 'Questions AI', content: 'Knowledge graph updated', quantum: true },
        { from: 'Round AI', to: 'Second Brain', content: 'Audio transcription complete', quantum: false },
        { from: 'Flashcards AI', to: 'Questions AI', content: 'Difficulty analysis ready', quantum: true },
        { from: 'Questions AI', to: 'Flashcards AI', content: 'Generated 50 new questions', quantum: true }
      ]
      
      const randomMessage = messages[Math.floor(Math.random() * messages.length)]
      
      communicationLog.value.unshift({
        id: `msg-${Date.now()}`,
        ...randomMessage,
        timestamp: Date.now(),
        latency: Math.floor(Math.random() * 10) + 1,
        type: randomMessage.quantum ? 'quantum' : 'standard'
      })
      
      // Keep only last 20 messages
      if (communicationLog.value.length > 20) {
        communicationLog.value.pop()
      }
      
      // Auto-scroll log
      if (logContainer.value) {
        setTimeout(() => {
          logContainer.value.scrollTop = 0
        }, 100)
      }
    }
    
    // Draw holographic background
    const drawHolographicBackground = () => {
      if (!holoCanvas.value) return
      
      const canvas = holoCanvas.value
      const ctx = canvas.getContext('2d')
      
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      
      const time = Date.now() * 0.001
      
      // Clear with fade effect
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      
      // Draw holographic waves
      for (let i = 0; i < 5; i++) {
        ctx.strokeStyle = `hsla(${180 + i * 30}, 100%, 50%, 0.3)`
        ctx.lineWidth = 2
        ctx.beginPath()
        
        for (let x = 0; x < canvas.width; x += 10) {
          const y = canvas.height / 2 + 
                   Math.sin((x * 0.01) + time + i) * 50 + 
                   Math.sin((x * 0.02) + time * 1.5 + i) * 30
          
          if (x === 0) {
            ctx.moveTo(x, y)
          } else {
            ctx.lineTo(x, y)
          }
        }
        
        ctx.stroke()
      }
    }
    
    // Draw neural network visualization
    const drawNeuralNetwork = () => {
      if (!neuralSvg.value) return
      
      const svg = neuralSvg.value
      svg.innerHTML = ''
      
      const layers = [
        { x: 100, neurons: 4 },
        { x: 300, neurons: 6 },
        { x: 500, neurons: 4 },
        { x: 700, neurons: 3 }
      ]
      
      // Draw connections
      for (let i = 0; i < layers.length - 1; i++) {
        const currentLayer = layers[i]
        const nextLayer = layers[i + 1]
        
        for (let j = 0; j < currentLayer.neurons; j++) {
          for (let k = 0; k < nextLayer.neurons; k++) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line')
            line.setAttribute('x1', currentLayer.x)
            line.setAttribute('y1', 50 + j * 80)
            line.setAttribute('x2', nextLayer.x)
            line.setAttribute('y2', 50 + k * 80)
            line.setAttribute('stroke', '#00ffff')
            line.setAttribute('stroke-width', '0.5')
            line.setAttribute('opacity', '0.3')
            svg.appendChild(line)
          }
        }
      }
      
      // Draw neurons
      layers.forEach((layer, layerIndex) => {
        for (let i = 0; i < layer.neurons; i++) {
          const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle')
          circle.setAttribute('cx', layer.x)
          circle.setAttribute('cy', 50 + i * 80)
          circle.setAttribute('r', '20')
          circle.setAttribute('fill', '#000')
          circle.setAttribute('stroke', '#00ffff')
          circle.setAttribute('stroke-width', '2')
          
          // Add glow animation
          const animate = document.createElementNS('http://www.w3.org/2000/svg', 'animate')
          animate.setAttribute('attributeName', 'r')
          animate.setAttribute('values', '20;25;20')
          animate.setAttribute('dur', `${2 + Math.random() * 2}s`)
          animate.setAttribute('repeatCount', 'indefinite')
          circle.appendChild(animate)
          
          svg.appendChild(circle)
        }
      })
    }
    
    // Draw entanglement map
    const drawEntanglementMap = () => {
      if (!entanglementCanvas.value) return
      
      const canvas = entanglementCanvas.value
      const ctx = canvas.getContext('2d')
      
      canvas.width = 400
      canvas.height = 300
      
      const centerX = canvas.width / 2
      const centerY = canvas.height / 2
      const radius = 100
      
      // Clear canvas
      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      
      // Draw engine nodes
      engines.value.forEach((engine, index) => {
        const angle = (index / engines.value.length) * Math.PI * 2
        const x = centerX + Math.cos(angle) * radius
        const y = centerY + Math.sin(angle) * radius
        
        // Draw connections
        engine.connections.forEach(connectionId => {
          const targetIndex = engines.value.findIndex(e => e.id === connectionId)
          if (targetIndex !== -1) {
            const targetAngle = (targetIndex / engines.value.length) * Math.PI * 2
            const targetX = centerX + Math.cos(targetAngle) * radius
            const targetY = centerY + Math.sin(targetAngle) * radius
            
            ctx.strokeStyle = engine.color
            ctx.lineWidth = 2
            ctx.globalAlpha = 0.5
            ctx.beginPath()
            ctx.moveTo(x, y)
            ctx.lineTo(targetX, targetY)
            ctx.stroke()
            ctx.globalAlpha = 1
          }
        })
        
        // Draw node
        ctx.fillStyle = engine.color
        ctx.beginPath()
        ctx.arc(x, y, 15, 0, Math.PI * 2)
        ctx.fill()
        
        // Draw label
        ctx.fillStyle = '#fff'
        ctx.font = '12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(engine.name, x, y + 30)
      })
    }
    
    // Utility functions
    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      return date.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      })
    }
    
    const getEngineName = (engineId) => {
      const engine = engines.value.find(e => e.id === engineId)
      return engine ? engine.name : engineId
    }
    
    const getEngineColor = (engineId) => {
      const engine = engines.value.find(e => e.id === engineId)
      return engine ? engine.color : '#666'
    }
    
    const isConnected = (engineId, targetId) => {
      const engine = engines.value.find(e => e.id === engineId)
      return engine && engine.connections.includes(targetId)
    }
    
    const selectEngine = (engine) => {
      selectedEngine.value = engine
      
      // Generate mock operations
      engine.operations = Array(10).fill(null).map((_, i) => ({
        id: `op-${i}`,
        timestamp: Date.now() - i * 60000,
        type: ['Query', 'Process', 'Analyze', 'Generate'][Math.floor(Math.random() * 4)],
        status: Math.random() > 0.1 ? 'success' : 'error'
      }))
    }
    
    // Update intervals
    let holoInterval = null
    let metricsInterval = null
    let workflowInterval = null
    let commInterval = null
    let entanglementInterval = null
    
    onMounted(() => {
      initializeEngines()
      
      // Start animations
      holoInterval = setInterval(drawHolographicBackground, 50)
      metricsInterval = setInterval(updateEngineMetrics, 1000)
      workflowInterval = setInterval(simulateWorkflows, 5000)
      commInterval = setInterval(simulateCommunication, 3000)
      entanglementInterval = setInterval(drawEntanglementMap, 100)
      
      // Initial draws
      drawNeuralNetwork()
      updateEngineMetrics()
      simulateWorkflows()
      
      // Update performance metrics
      setInterval(() => {
        totalRequests.value += Math.floor(Math.random() * 100)
        avgResponseTime.value = Math.floor(Math.random() * 20) + 10
        quantumOperations.value += Math.floor(Math.random() * 50)
      }, 2000)
    })
    
    onUnmounted(() => {
      if (holoInterval) clearInterval(holoInterval)
      if (metricsInterval) clearInterval(metricsInterval)
      if (workflowInterval) clearInterval(workflowInterval)
      if (commInterval) clearInterval(commInterval)
      if (entanglementInterval) clearInterval(entanglementInterval)
    })
    
    return {
      // State
      engines,
      selectedEngine,
      systemMetrics,
      activeWorkflows,
      communicationLog,
      isSyncing,
      syncStatus,
      
      // Metrics
      totalRequests,
      avgResponseTime,
      quantumOperations,
      activeConnections,
      
      // Refs
      holoCanvas,
      neuralSvg,
      entanglementCanvas,
      detailChart,
      historyChart,
      logContainer,
      
      // Methods
      formatTime,
      getEngineName,
      getEngineColor,
      isConnected,
      selectEngine
    }
  }
}
</script>

<style scoped>
/* Base Container */
.ai-dashboard-ultra {
  position: relative;
  min-height: 100vh;
  background: #000;
  color: #fff;
  overflow-x: hidden;
}

/* Holographic Background */
.holo-background {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.holo-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
}

.holo-grid {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 10s linear infinite;
}

@keyframes grid-move {
  from { transform: translate(0, 0); }
  to { transform: translate(50px, 50px); }
}

/* Neural Particles */
.neural-particles {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 1;
}

.neural-particle {
  position: absolute;
  width: var(--size);
  height: var(--size);
  background: radial-gradient(circle, rgba(0, 255, 255, 0.8), transparent);
  border-radius: 50%;
  left: calc(var(--delay) * 5%);
  animation: float-particle var(--duration) infinite linear;
  animation-delay: var(--delay);
}

@keyframes float-particle {
  from {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) scale(1);
  }
  90% {
    opacity: 1;
  }
  to {
    transform: translateY(-100px) scale(0);
    opacity: 0;
  }
}

/* Dashboard Container */
.dashboard-container {
  position: relative;
  z-index: 10;
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

/* Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  padding: 1.5rem;
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.dashboard-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 2rem;
  margin: 0;
  background: linear-gradient(135deg, #00ffff, #ff00ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.title-icon {
  font-size: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.5));
}

.sync-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 20px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.sync-status.active {
  animation: pulse-sync 1s ease-in-out infinite;
}

@keyframes pulse-sync {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.sync-icon {
  width: 8px;
  height: 8px;
  background: #00ff00;
  border-radius: 50%;
  box-shadow: 0 0 10px #00ff00;
}

.quantum-status {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: #00ffff;
}

.quantum-indicator {
  width: 100px;
  height: 6px;
  background: rgba(0, 255, 255, 0.2);
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.quantum-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: var(--coherence);
  background: linear-gradient(90deg, #00ffff, #ff00ff);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  transition: width 0.6s ease;
}

/* Engines Grid */
.engines-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.engine-card {
  background: rgba(0, 20, 40, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.engine-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.5), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.engine-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 40px rgba(0, 255, 255, 0.3);
}

.engine-card:hover::before {
  opacity: 1;
  animation: rotate-border 3s linear infinite;
}

@keyframes rotate-border {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.engine-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.engine-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #000;
  position: relative;
}

.engine-icon::after {
  content: '';
  position: absolute;
  inset: -3px;
  background: inherit;
  border-radius: inherit;
  filter: blur(10px);
  opacity: 0.5;
  z-index: -1;
}

.engine-info h3 {
  margin: 0;
  font-size: 1.25rem;
}

.engine-status {
  font-size: 0.75rem;
  text-transform: uppercase;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.engine-status.active { 
  background: rgba(0, 255, 0, 0.2); 
  color: #00ff00;
}

.engine-status.initializing { 
  background: rgba(255, 255, 0, 0.2); 
  color: #ffff00;
}

/* Engine Metrics */
.engine-metrics {
  margin-bottom: 1rem;
}

.metric-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.metric-label {
  font-size: 0.75rem;
  color: #94a3b8;
  width: 60px;
}

.metric-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  transition: width 0.6s ease;
  box-shadow: 0 0 10px currentColor;
}

.metric-value {
  font-size: 0.75rem;
  font-weight: bold;
  width: 40px;
  text-align: right;
}

/* Engine Connections */
.engine-connections {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.connection-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.connection-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #666;
}

.connection-dot.active {
  background: #00ff00;
  box-shadow: 0 0 5px #00ff00;
}

/* Activity Sparkline */
.activity-sparkline {
  width: 100%;
  height: 30px;
  opacity: 0.7;
}

/* System Overview */
.system-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.overview-section {
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.overview-section h2 {
  font-size: 1.25rem;
  margin: 0 0 1.5rem;
  color: #00ffff;
}

/* Performance Grid */
.performance-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.perf-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.perf-card:hover {
  background: rgba(0, 255, 255, 0.1);
  transform: translateY(-2px);
}

.perf-icon {
  font-size: 2rem;
  filter: drop-shadow(0 0 5px rgba(0, 255, 255, 0.5));
}

.perf-value {
  font-size: 1.5rem;
  font-weight: bold;
  background: linear-gradient(135deg, #00ffff, #ff00ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.perf-label {
  font-size: 0.75rem;
  color: #94a3b8;
}

/* Neural Visualization */
.neural-viz {
  height: 400px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  padding: 1rem;
}

.neural-svg {
  width: 100%;
  height: 100%;
}

/* Entanglement Map */
.entanglement-map {
  height: 300px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.entanglement-canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Workflow Monitor */
.workflow-monitor {
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.workflow-monitor h2 {
  font-size: 1.25rem;
  margin: 0 0 1.5rem;
  color: #00ffff;
}

.workflow-list {
  display: grid;
  gap: 1rem;
}

.workflow-item {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.workflow-item.running {
  border-color: rgba(255, 255, 0, 0.3);
  animation: pulse-workflow 2s ease-in-out infinite;
}

@keyframes pulse-workflow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.workflow-item.completed {
  border-color: rgba(0, 255, 0, 0.3);
}

.workflow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.workflow-name {
  font-weight: bold;
}

.workflow-status {
  font-size: 0.75rem;
  text-transform: uppercase;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
}

.workflow-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00ffff, #ff00ff);
  transition: width 0.6s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: #94a3b8;
}

.workflow-engines {
  display: flex;
  gap: 0.5rem;
}

.workflow-engine {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  color: #000;
  font-weight: bold;
}

/* Communication Log */
.communication-log {
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
}

.communication-log h2 {
  font-size: 1.25rem;
  margin: 0 0 1.5rem;
  color: #00ffff;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.log-container::-webkit-scrollbar {
  width: 4px;
}

.log-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.log-container::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.5);
  border-radius: 2px;
}

.log-message {
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  animation: slide-in 0.3s ease-out;
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.log-message.quantum {
  border-color: rgba(255, 0, 255, 0.3);
  background: rgba(255, 0, 255, 0.05);
}

.message-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.message-from, .message-to {
  font-weight: bold;
}

.message-arrow {
  color: #00ffff;
}

.message-time {
  margin-left: auto;
  color: #94a3b8;
  font-size: 0.75rem;
}

.message-content {
  color: #e2e8f0;
  margin-bottom: 0.5rem;
}

.message-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: #94a3b8;
}

.quantum-badge {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #ff00ff, #00ffff);
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  font-weight: bold;
  color: #000;
  margin-right: 0.25rem;
}

/* Modal */
.engine-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: rgba(0, 20, 40, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 16px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.modal-header h2 {
  margin: 0;
  color: #00ffff;
}

.close-modal {
  background: none;
  border: none;
  color: #fff;
  font-size: 2rem;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-modal:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(90deg);
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}

.detail-charts, .performance-history {
  margin-bottom: 2rem;
}

.detail-chart, .history-chart {
  width: 100%;
  height: 200px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
}

.operations-log h3 {
  margin: 0 0 1rem;
  color: #00ffff;
}

.ops-list {
  display: grid;
  gap: 0.5rem;
}

.op-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  font-size: 0.875rem;
}

.op-time {
  color: #94a3b8;
}

.op-type {
  font-weight: bold;
}

.op-status {
  margin-left: auto;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.op-status.success {
  background: rgba(0, 255, 0, 0.2);
  color: #00ff00;
}

.op-status.error {
  background: rgba(255, 0, 0, 0.2);
  color: #ff0000;
}

/* Transitions */
.modal-enter-active, .modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from, .modal-leave-to {
  opacity: 0;
}

/* Responsive */
@media (max-width: 1200px) {
  .engines-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .performance-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .engines-grid {
    grid-template-columns: 1fr;
  }
  
  .system-overview {
    grid-template-columns: 1fr;
  }
  
  .dashboard-title {
    font-size: 1.5rem;
  }
}
</style>