// frontend-new/src/main.js
import { createApp } from 'vue';
import App from './App.vue';
import router from './router'; // Import the router
import store from './store';
import { createPinia } from 'pinia'; // Import Pinia
import axios from 'axios'; // Adiciona importação do axios
import './utils/axios-interceptor';

// EMERGENCY: Apply purple screen fix immediately
import './utils/emergencyPurpleFix';

// Debug app mounting
import './utils/debug-app';

import './styles/global.css';
import './styles/animations.css';
import './styles/variables.css';
import './styles/components.css'; // New import
import './styles/modern-design-system.css'
import './styles/dashboard-fix.css'; // Dashboard purple screen fix
import './styles/theme-variables.css'; // Theme system variables
import './styles/theme-overrides.css'; // Theme overrides for components

// Import Error Handler
import { ErrorHandlerPlugin } from './utils/errorHandler';

// Import Toast
import Toast, { TYPE } from "vue-toastification";
import "vue-toastification/dist/index.css";

// Font Awesome imports
import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

// Importação dos ícones
import { faUser, faLock, faSliders, faChartLine, faGear, faSave, faKey, faDownload,
  faTrash, faCamera, faCalendarCheck, faClock, faBrain, faMedal, faSearch,
  faThLarge, faList, faFilePdf, faVideo, faHeadphones, faImage, faFileAlt,
  faFileArchive, faEye, faArrowLeft, faChevronLeft, faChevronRight, faFile,
  faTh, faStar, faFolder, faFolderOpen, faLayerGroup, faThumbsUp, faCommentDots, faComments,
  faPaperPlane, faUpload, faCloudUploadAlt, faTimes, faInfoCircle, faClockRotateLeft,
  faTasks, faBookmark, faBookOpen, faLink, faCalendarAlt, faSort, faFilter,
  faFileCirclePlus, faPlus, faCheck, faEdit, faShare, faHome, faCalendarDays,
  faClone, faBook, faRobot, faBars, faMoon, faSun, faSignOutAlt, faSyncAlt,
  faExpandAlt, faCompressAlt, faClipboard, faClipboardCheck, faQuestionCircle,
  faGraduationCap, faMicrophone, faLanguage, faStop, faSpinner, faCopy,
  faExclamationCircle, faHeartbeat, faNotesMedical, faStethoscope, faBone,
  faCheckCircle, faExclamationTriangle, faMicroscope, faFlask, faVial, faXRay,
  faFileMedical, faFileMedicalAlt, faUserMd, faListOl, faCheckSquare, faPen,
  faMagic, faHandPointRight, faChevronDown, faCompass, faRocket, faInfinity,
  faArrowRight, faCog, faClipboardList, faHeart, faHistory, faLightbulb, 
  faPills, faSyringe, faStickyNote, faPlayCircle, faPlay, faPause, faFileLines,
  faBaby, faProcedures, faShareAlt, faCalculator, faLandmark, faGlobe, faAtom,
  faDna, faFire, faArrowUp, faArrowDown, faEllipsisV, faArchive, faSync,
  faBolt, faMinus, faUniversity, faBriefcase, faUserGraduate, faChartPie, faBullseye, faExpand, faTrophy, faCalendarWeek, faChartBar } from '@fortawesome/free-solid-svg-icons';
import { faStar as farStar } from '@fortawesome/free-regular-svg-icons';

//const createApp = Vue.createApp; // <- use for vue 3.4+

// Configuração global do axios
axios.defaults.baseURL = 'http://localhost:3000/api'; // Ajuste para a porta correta do seu backend
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.withCredentials = true;

// Interceptador para debugging
axios.interceptors.request.use(request => {
  console.log('Starting Request:', request);
  return request;
});

axios.interceptors.response.use(
  response => {
    console.log('Response:', response);
    return response;
  },
  error => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// Apply theme from localStorage on initial load
const applyStoredTheme = () => {
  const storedTheme = localStorage.getItem('theme') || 'dark';
  document.documentElement.setAttribute('data-theme', storedTheme);
};

// Call before mounting the app
applyStoredTheme();

// Create Pinia instance
const pinia = createPinia();

const app = createApp(App);

// Install error handler first (before other plugins)
app.use(ErrorHandlerPlugin, { router });

app.use(router);
app.use(store);
app.use(pinia); // Use Pinia

// Toast configuration
const toastOptions = {
  position: "top-right",
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: "button",
  toastDefaults: {
    [TYPE.ERROR]: {
      timeout: 10000
    },
    [TYPE.SUCCESS]: {
      timeout: 3000
    }
  }
};

// Register Toast plugin
app.use(Toast, toastOptions);

// Monitorar status de conexão global
const updateOnlineStatus = () => {
  const isOnline = navigator.onLine;
  console.log(`[App] Status de conexão: ${isOnline ? 'online' : 'offline'}`);
  store.dispatch('calendar/setOnlineStatus', isOnline);
};

// Inicializar status online/offline
updateOnlineStatus();

// Adicionar ouvintes de eventos globais
window.addEventListener('online', updateOnlineStatus);
window.addEventListener('offline', updateOnlineStatus);

// Tentar sincronizar eventos pendentes quando a aplicação iniciar
if (navigator.onLine) {
  const pendingEvents = JSON.parse(localStorage.getItem('pending_events') || '[]');
  if (pendingEvents.length > 0) {
    console.log(`[App] Sincronizando ${pendingEvents.length} eventos pendentes na inicialização`);
    store.commit('calendar/LOAD_PENDING_EVENTS', pendingEvents);
    store.dispatch('calendar/syncPendingEvents').catch(error => {
      console.error('[App] Erro ao sincronizar eventos pendentes:', error);
    });
  }
}

// Error handling is now managed by ErrorHandlerPlugin

// Add all icons to the library
library.add(
  faUser, faLock, faSliders, faChartLine, faGear, faSave, faKey, faDownload,
  faTrash, faCamera, faCalendarCheck, faClock, faBrain, faMedal, faSearch,
  faThLarge, faList, faFilePdf, faVideo, faHeadphones, faImage, faFileAlt,
  faFileArchive, faEye, faArrowLeft, faChevronLeft, faChevronRight, faFile,
  faTh, faStar, faFolder, faFolderOpen, faLayerGroup, faThumbsUp, faCommentDots, faComments,
  faPaperPlane, faUpload, faCloudUploadAlt, faTimes, faInfoCircle, faClockRotateLeft,
  faTasks, faBookmark, faBookOpen, faLink, faCalendarAlt, faSort, faFilter,
  faFileCirclePlus, faPlus, faCheck, faEdit, faShare, faHome, faCalendarDays,
  faClone, faBook, faRobot, faBars, faMoon, faSun, faSignOutAlt, faSyncAlt,
  faExpandAlt, faCompressAlt, faClipboard, faClipboardCheck, faQuestionCircle,
  faGraduationCap, faMicrophone, faLanguage, faStop, faSpinner, faCopy,
  faExclamationCircle, faHeartbeat, faNotesMedical, faStethoscope, faBone,
  faCheckCircle, faExclamationTriangle, faMicroscope, faFlask, faVial, faXRay,
  faFileMedical, faFileMedicalAlt, faUserMd, faListOl, faCheckSquare, faPen,
  faMagic, faHandPointRight, faChevronDown, faCompass, faRocket, faInfinity,
  faArrowRight, faCog, faClipboardList, faHeart, faHistory, faLightbulb, 
  faPills, faSyringe, faStickyNote, faPlayCircle, faPlay, faPause, faFileLines,
  faBaby, faProcedures, faShareAlt, faCalculator, faLandmark, faGlobe, faAtom,
  faDna, faFire, faArrowUp, faArrowDown, faEllipsisV, faArchive, faSync,
  faBolt, faMinus, faUniversity, faBriefcase, faUserGraduate, faChartPie, farStar,
  faBullseye, faExpand, faTrophy, faCalendarWeek, faChartBar
);

// Register Font Awesome component
app.component('font-awesome-icon', FontAwesomeIcon);

app.mount('#app');