// Safe Component Mixin
// Provides safety features for all Vue components

export default {
  data() {
    return {
      _isMounted: false,
      _isDestroyed: false,
      _pendingTimeouts: [],
      _pendingIntervals: [],
      _pendingAnimationFrames: [],
      _abortControllers: []
    }
  },
  
  created() {
    // Initialize safe refs
    this._initializeSafeRefs()
  },
  
  mounted() {
    this._isMounted = true
    
    // Safe mounted hook
    this.safeOnMounted?.()
  },
  
  beforeUnmount() {
    this._isDestroyed = true
    
    // Clean up all pending operations
    this._cleanupPendingOperations()
    
    // Safe cleanup hook
    this.safeOnCleanup?.()
  },
  
  methods: {
    // Initialize safe refs
    _initializeSafeRefs() {
      // This will be overridden by components
    },
    
    // Safe setTimeout wrapper
    safeSetTimeout(callback, delay) {
      if (this._isDestroyed) return null
      
      const timeoutId = setTimeout(() => {
        if (!this._isDestroyed) {
          try {
            callback()
          } catch (error) {
            console.error('Error in timeout callback:', error)
            this.$errorHandler?.logError({
              type: 'timeout-error',
              message: error.message,
              component: this.$options.name
            })
          }
        }
        
        // Remove from pending timeouts
        const index = this._pendingTimeouts.indexOf(timeoutId)
        if (index > -1) {
          this._pendingTimeouts.splice(index, 1)
        }
      }, delay)
      
      this._pendingTimeouts.push(timeoutId)
      return timeoutId
    },
    
    // Safe setInterval wrapper
    safeSetInterval(callback, delay) {
      if (this._isDestroyed) return null
      
      const intervalId = setInterval(() => {
        if (!this._isDestroyed) {
          try {
            callback()
          } catch (error) {
            console.error('Error in interval callback:', error)
            this.$errorHandler?.logError({
              type: 'interval-error',
              message: error.message,
              component: this.$options.name
            })
          }
        }
      }, delay)
      
      this._pendingIntervals.push(intervalId)
      return intervalId
    },
    
    // Safe requestAnimationFrame wrapper
    safeRequestAnimationFrame(callback) {
      if (this._isDestroyed) return null
      
      const frameId = requestAnimationFrame(() => {
        if (!this._isDestroyed) {
          try {
            callback()
          } catch (error) {
            console.error('Error in animation frame:', error)
            this.$errorHandler?.logError({
              type: 'animation-frame-error',
              message: error.message,
              component: this.$options.name
            })
          }
        }
        
        // Remove from pending frames
        const index = this._pendingAnimationFrames.indexOf(frameId)
        if (index > -1) {
          this._pendingAnimationFrames.splice(index, 1)
        }
      })
      
      this._pendingAnimationFrames.push(frameId)
      return frameId
    },
    
    // Safe nextTick wrapper
    safeNextTick(callback) {
      if (this._isDestroyed) return Promise.resolve()
      
      return this.$nextTick(() => {
        if (!this._isDestroyed) {
          try {
            callback()
          } catch (error) {
            console.error('Error in nextTick callback:', error)
            this.$errorHandler?.logError({
              type: 'next-tick-error',
              message: error.message,
              component: this.$options.name
            })
          }
        }
      })
    },
    
    // Safe fetch wrapper
    async safeFetch(url, options = {}) {
      if (this._isDestroyed) return null
      
      const controller = new AbortController()
      this._abortControllers.push(controller)
      
      try {
        const response = await fetch(url, {
          ...options,
          signal: controller.signal
        })
        
        // Remove from abort controllers
        const index = this._abortControllers.indexOf(controller)
        if (index > -1) {
          this._abortControllers.splice(index, 1)
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        return response
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('Fetch aborted')
          return null
        }
        
        console.error('Fetch error:', error)
        this.$errorHandler?.logError({
          type: 'fetch-error',
          message: error.message,
          component: this.$options.name,
          url
        })
        
        throw error
      }
    },
    
    // Safe DOM query
    safeQuerySelector(selector, root = this.$el) {
      if (!root || this._isDestroyed) return null
      
      try {
        return root.querySelector(selector)
      } catch (error) {
        console.error('Query selector error:', error)
        return null
      }
    },
    
    // Safe ref access
    safeRef(refName) {
      if (this._isDestroyed) return null
      
      const ref = this.$refs[refName]
      
      // Handle array refs (v-for)
      if (Array.isArray(ref)) {
        return ref.length > 0 ? ref : null
      }
      
      return ref || null
    },
    
    // Safe emit wrapper
    safeEmit(event, ...args) {
      if (this._isDestroyed) return
      
      try {
        this.$emit(event, ...args)
      } catch (error) {
        console.error('Emit error:', error)
        this.$errorHandler?.logError({
          type: 'emit-error',
          message: error.message,
          component: this.$options.name,
          event
        })
      }
    },
    
    // Clean up all pending operations
    _cleanupPendingOperations() {
      // Clear all timeouts
      this._pendingTimeouts.forEach(id => clearTimeout(id))
      this._pendingTimeouts = []
      
      // Clear all intervals
      this._pendingIntervals.forEach(id => clearInterval(id))
      this._pendingIntervals = []
      
      // Cancel all animation frames
      this._pendingAnimationFrames.forEach(id => cancelAnimationFrame(id))
      this._pendingAnimationFrames = []
      
      // Abort all fetch requests
      this._abortControllers.forEach(controller => controller.abort())
      this._abortControllers = []
    },
    
    // Safe watch wrapper
    safeWatch(source, callback, options) {
      if (this._isDestroyed) return () => {}
      
      const unwatch = this.$watch(source, (...args) => {
        if (!this._isDestroyed) {
          try {
            callback(...args)
          } catch (error) {
            console.error('Watch callback error:', error)
            this.$errorHandler?.logError({
              type: 'watch-error',
              message: error.message,
              component: this.$options.name
            })
          }
        }
      }, options)
      
      return unwatch
    },
    
    // Safe computed getter
    safeGet(obj, path, defaultValue = null) {
      if (!obj) return defaultValue
      
      const keys = path.split('.')
      let result = obj
      
      for (const key of keys) {
        if (result && typeof result === 'object' && key in result) {
          result = result[key]
        } else {
          return defaultValue
        }
      }
      
      return result
    },
    
    // Safe array access
    safeArrayAccess(array, index, defaultValue = null) {
      if (!Array.isArray(array) || index < 0 || index >= array.length) {
        return defaultValue
      }
      return array[index]
    },
    
    // Debounce wrapper
    debounce(func, wait) {
      let timeout
      
      const debounced = (...args) => {
        if (this._isDestroyed) return
        
        const later = () => {
          timeout = null
          if (!this._isDestroyed) {
            try {
              func.apply(this, args)
            } catch (error) {
              console.error('Debounced function error:', error)
              this.$errorHandler?.logError({
                type: 'debounce-error',
                message: error.message,
                component: this.$options.name
              })
            }
          }
        }
        
        clearTimeout(timeout)
        timeout = this.safeSetTimeout(later, wait)
      }
      
      debounced.cancel = () => {
        clearTimeout(timeout)
        timeout = null
      }
      
      return debounced
    },
    
    // Throttle wrapper
    throttle(func, limit) {
      let inThrottle
      
      return (...args) => {
        if (this._isDestroyed) return
        
        if (!inThrottle) {
          try {
            func.apply(this, args)
          } catch (error) {
            console.error('Throttled function error:', error)
            this.$errorHandler?.logError({
              type: 'throttle-error',
              message: error.message,
              component: this.$options.name
            })
          }
          
          inThrottle = true
          this.safeSetTimeout(() => {
            inThrottle = false
          }, limit)
        }
      }
    }
  }
}