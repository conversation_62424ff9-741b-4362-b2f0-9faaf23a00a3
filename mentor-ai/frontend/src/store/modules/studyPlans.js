// store/modules/studyPlans.js
import axios from 'axios'

const api = axios.create({
  baseURL: 'http://localhost:8000/api',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true
})

const state = {
  plans: [],
  currentPlan: null,
  sessions: [],
  loading: false,
  error: null
}

const getters = {
  getAllPlans: state => state.plans,
  getActivePlans: state => state.plans.filter(plan => plan.is_active),
  getCurrentPlan: state => state.currentPlan,
  getPlanById: state => id => state.plans.find(plan => plan.id === id),
  getTotalPlans: state => state.plans.length,
  getCompletedPlansCount: state => state.plans.filter(plan => plan.progress === 100).length,
  getAverageProgress: state => {
    if (state.plans.length === 0) return 0
    const totalProgress = state.plans.reduce((sum, plan) => sum + plan.progress, 0)
    return Math.round(totalProgress / state.plans.length)
  },
  getCurrentSessions: state => state.sessions
}

const actions = {
  async fetchPlans({ commit }) {
    commit('SET_LOADING', true)
    try {
      const response = await api.get('/study-plans/')
      commit('SET_PLANS', response.data)
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async fetchPlan({ commit }, planId) {
    commit('SET_LOADING', true)
    try {
      const response = await api.get(`/study-plans/${planId}/`)
      commit('SET_CURRENT_PLAN', response.data)
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async createPlan({ commit }, planData) {
    commit('SET_LOADING', true)
    try {
      const response = await api.post('/study-plans/', planData)
      commit('ADD_PLAN', response.data)
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async updatePlan({ commit }, { planId, updates }) {
    commit('SET_LOADING', true)
    try {
      const response = await api.patch(`/study-plans/${planId}/`, updates)
      commit('UPDATE_PLAN', response.data)
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async deletePlan({ commit }, planId) {
    commit('SET_LOADING', true)
    try {
      await api.delete(`/study-plans/${planId}/`)
      commit('DELETE_PLAN', planId)
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async addTask({ commit }, { planId, taskData }) {
    try {
      const response = await api.post(`/study-plans/${planId}/add_task/`, taskData)
      commit('ADD_TASK_TO_PLAN', { planId, task: response.data })
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },

  async updateTask({ commit }, { planId, taskId, updates }) {
    try {
      const response = await api.patch(`/study-plans/${planId}/update_task/`, {
        task_id: taskId,
        ...updates
      })
      commit('UPDATE_TASK_IN_PLAN', { planId, task: response.data })
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },

  async deleteTask({ commit }, { planId, taskId }) {
    try {
      await api.delete(`/study-plans/${planId}/delete_task/`, {
        data: { task_id: taskId }
      })
      commit('DELETE_TASK_FROM_PLAN', { planId, taskId })
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },

  async toggleTaskComplete({ commit }, { taskId }) {
    try {
      const response = await api.post(`/study-tasks/${taskId}/toggle_complete/`)
      commit('UPDATE_TASK_COMPLETION', response.data)
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },

  async reorderTasks({ commit }, taskOrders) {
    try {
      await api.post('/study-tasks/reorder/', { task_orders: taskOrders })
      commit('REORDER_TASKS', taskOrders)
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },

  async createEventFromTask({ commit, dispatch }, { planId, taskId, startTime, endTime }) {
    try {
      const response = await api.post(`/study-plans/${planId}/create_event_from_task/`, {
        task_id: taskId,
        start: startTime,
        end: endTime
      })
      
      // Add event to calendar store
      await dispatch('calendar/addEvent', response.data, { root: true })
      
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },

  async fetchPlanAnalytics({ commit }, planId) {
    try {
      const response = await api.get(`/study-plans/${planId}/analytics/`)
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },

  async startStudySession({ commit }, sessionData) {
    try {
      const response = await api.post('/study-sessions/', sessionData)
      commit('ADD_SESSION', response.data)
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },

  async endStudySession({ commit }, { sessionId, productivityScore, notes }) {
    try {
      const response = await api.post(`/study-sessions/${sessionId}/end_session/`, {
        productivity_score: productivityScore,
        notes
      })
      commit('UPDATE_SESSION', response.data)
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  },

  async fetchSessions({ commit }, planId) {
    try {
      const response = await api.get('/study-sessions/', {
        params: { study_plan: planId }
      })
      commit('SET_SESSIONS', response.data)
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.message)
      throw error
    }
  }
}

const mutations = {
  SET_LOADING(state, loading) {
    state.loading = loading
  },

  SET_ERROR(state, error) {
    state.error = error
  },

  SET_PLANS(state, plans) {
    state.plans = plans
  },

  SET_CURRENT_PLAN(state, plan) {
    state.currentPlan = plan
  },

  ADD_PLAN(state, plan) {
    state.plans.push(plan)
  },

  UPDATE_PLAN(state, updatedPlan) {
    const index = state.plans.findIndex(plan => plan.id === updatedPlan.id)
    if (index !== -1) {
      state.plans.splice(index, 1, updatedPlan)
    }
    if (state.currentPlan?.id === updatedPlan.id) {
      state.currentPlan = updatedPlan
    }
  },

  DELETE_PLAN(state, planId) {
    state.plans = state.plans.filter(plan => plan.id !== planId)
    if (state.currentPlan?.id === planId) {
      state.currentPlan = null
    }
  },

  ADD_TASK_TO_PLAN(state, { planId, task }) {
    const plan = state.plans.find(p => p.id === planId)
    if (plan) {
      if (!plan.tasks) plan.tasks = []
      plan.tasks.push(task)
      plan.total_tasks = plan.tasks.length
      plan.completed_tasks = plan.tasks.filter(t => t.completed).length
      plan.progress = Math.round((plan.completed_tasks / plan.total_tasks) * 100)
    }
    if (state.currentPlan?.id === planId) {
      if (!state.currentPlan.tasks) state.currentPlan.tasks = []
      state.currentPlan.tasks.push(task)
    }
  },

  UPDATE_TASK_IN_PLAN(state, { planId, task }) {
    const plan = state.plans.find(p => p.id === planId)
    if (plan && plan.tasks) {
      const taskIndex = plan.tasks.findIndex(t => t.id === task.id)
      if (taskIndex !== -1) {
        plan.tasks.splice(taskIndex, 1, task)
        plan.completed_tasks = plan.tasks.filter(t => t.completed).length
        plan.progress = Math.round((plan.completed_tasks / plan.total_tasks) * 100)
      }
    }
    if (state.currentPlan?.id === planId && state.currentPlan.tasks) {
      const taskIndex = state.currentPlan.tasks.findIndex(t => t.id === task.id)
      if (taskIndex !== -1) {
        state.currentPlan.tasks.splice(taskIndex, 1, task)
      }
    }
  },

  DELETE_TASK_FROM_PLAN(state, { planId, taskId }) {
    const plan = state.plans.find(p => p.id === planId)
    if (plan && plan.tasks) {
      plan.tasks = plan.tasks.filter(t => t.id !== taskId)
      plan.total_tasks = plan.tasks.length
      plan.completed_tasks = plan.tasks.filter(t => t.completed).length
      plan.progress = plan.total_tasks > 0 ? Math.round((plan.completed_tasks / plan.total_tasks) * 100) : 0
    }
    if (state.currentPlan?.id === planId && state.currentPlan.tasks) {
      state.currentPlan.tasks = state.currentPlan.tasks.filter(t => t.id !== taskId)
    }
  },

  UPDATE_TASK_COMPLETION(state, updatedTask) {
    // Update in all plans
    state.plans.forEach(plan => {
      if (plan.tasks) {
        const taskIndex = plan.tasks.findIndex(t => t.id === updatedTask.id)
        if (taskIndex !== -1) {
          plan.tasks.splice(taskIndex, 1, updatedTask)
          plan.completed_tasks = plan.tasks.filter(t => t.completed).length
          plan.progress = Math.round((plan.completed_tasks / plan.total_tasks) * 100)
        }
      }
    })
    
    // Update in current plan
    if (state.currentPlan?.tasks) {
      const taskIndex = state.currentPlan.tasks.findIndex(t => t.id === updatedTask.id)
      if (taskIndex !== -1) {
        state.currentPlan.tasks.splice(taskIndex, 1, updatedTask)
      }
    }
  },

  REORDER_TASKS(state, taskOrders) {
    // Update task orders in relevant plans
    taskOrders.forEach(({ id, order }) => {
      state.plans.forEach(plan => {
        if (plan.tasks) {
          const task = plan.tasks.find(t => t.id === id)
          if (task) {
            task.order = order
          }
        }
      })
      
      if (state.currentPlan?.tasks) {
        const task = state.currentPlan.tasks.find(t => t.id === id)
        if (task) {
          task.order = order
        }
      }
    })
    
    // Sort tasks by order
    state.plans.forEach(plan => {
      if (plan.tasks) {
        plan.tasks.sort((a, b) => a.order - b.order)
      }
    })
    
    if (state.currentPlan?.tasks) {
      state.currentPlan.tasks.sort((a, b) => a.order - b.order)
    }
  },

  ADD_SESSION(state, session) {
    state.sessions.push(session)
  },

  UPDATE_SESSION(state, updatedSession) {
    const index = state.sessions.findIndex(s => s.id === updatedSession.id)
    if (index !== -1) {
      state.sessions.splice(index, 1, updatedSession)
    }
  },

  SET_SESSIONS(state, sessions) {
    state.sessions = sessions
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}