// store/modules/revisions.js
import axios from 'axios'

// API service setup
// API service setup - Development only
const api = axios.create({
  baseURL: 'http://localhost:8000/api', // Use explicit URL during development
  headers: {
    'Content-Type': 'application/json',
  }
})

const state = {
  stats: [],
  summary: null,
  theoryStudies: [],
  firstContacts: [],
  spacedRevisions: [],
  performances: []
}

const getters = {
  getStatsBySubject: (state) => (subjectId) => {
    return state.stats.filter(stat => stat.subject === subjectId)
  },
  getAllStats: (state) => state.stats,
  getSummary: (state) => state.summary,
  getTheoryStudies: state => state.theoryStudies,
  getFirstContacts: state => state.firstContacts,
  getSpacedRevisions: state => state.spacedRevisions,
  getPerformances: state => state.performances,
  
  getTodayFirstContacts: state => {
    const today = new Date().toDateString();
    return state.firstContacts.filter(c => 
      new Date(c.date).toDateString() === today && !c.completed
    );
  },
  
  getPendingRevisions: state => {
    const today = new Date();
    return state.spacedRevisions.filter(r => 
      new Date(r.date) <= today && !r.completed
    );
  },
  
  getAveragePerformance: state => {
    if (state.performances.length === 0) return 0;
    const sum = state.performances.reduce((acc, p) => acc + p.percentage, 0);
    return Math.round(sum / state.performances.length);
  }
}

const actions = {
  async fetchStats({ commit }) {
    try {
      const response = await api.get('/stats/')
      commit('SET_STATS', response.data)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  },
  
  async fetchSummary({ commit }, period = 'month') {
    try {
      const response = await api.get(`/stats/summary/?period=${period}`)
      commit('SET_SUMMARY', response.data)
      return response.data
    } catch (error) {
      console.error('Error fetching summary:', error)
      throw error
    }
  },
  
  async registerTheoretical({ commit }, data) {
    try {
      const response = await api.post('/stats/register_theoretical/', data)
      commit('ADD_STAT', response.data)
      return response.data
    } catch (error) {
      console.error('Error registering theoretical revision:', error)
      throw error
    }
  },
  
  async registerPractical({ commit }, data) {
    try {
      const response = await api.post('/stats/register_practical/', data)
      commit('ADD_STAT', response.data)
      return response.data
    } catch (error) {
      console.error('Error registering practical revision:', error)
      throw error
    }
  },
  
  addTheoryStudy({ commit }, study) {
    commit('ADD_THEORY_STUDY', study);
    this.dispatch('revisions/saveToLocalStorage');
  },
  
  addFirstContact({ commit }, contact) {
    commit('ADD_FIRST_CONTACT', contact);
    this.dispatch('revisions/saveToLocalStorage');
  },
  
  addSpacedRevision({ commit }, revision) {
    commit('ADD_SPACED_REVISION', revision);
    this.dispatch('revisions/saveToLocalStorage');
  },
  
  addPerformance({ commit }, performance) {
    commit('ADD_PERFORMANCE', performance);
    this.dispatch('revisions/saveToLocalStorage');
  },
  
  completeFirstContact({ commit }, contactId) {
    commit('COMPLETE_FIRST_CONTACT', contactId);
    this.dispatch('revisions/saveToLocalStorage');
  },
  
  completeRevision({ commit }, revisionId) {
    commit('COMPLETE_REVISION', revisionId);
    this.dispatch('revisions/saveToLocalStorage');
  },
  
  loadRevisions({ commit }) {
    const savedData = localStorage.getItem('spacedRevisionsData');
    if (savedData) {
      try {
        const data = JSON.parse(savedData);
        commit('SET_REVISIONS_DATA', data);
      } catch (error) {
        console.error('Error loading revisions data:', error);
      }
    }
  },
  
  saveToLocalStorage({ state }) {
    const data = {
      theoryStudies: state.theoryStudies,
      firstContacts: state.firstContacts,
      spacedRevisions: state.spacedRevisions,
      performances: state.performances
    };
    localStorage.setItem('spacedRevisionsData', JSON.stringify(data));
  }
}

const mutations = {
  SET_STATS(state, stats) {
    state.stats = stats
  },
  
  SET_SUMMARY(state, summary) {
    state.summary = summary
  },
  
  ADD_STAT(state, stat) {
    state.stats.push(stat)
  },
  
  UPDATE_STAT(state, updatedStat) {
    const index = state.stats.findIndex(s => s.id === updatedStat.id)
    if (index !== -1) {
      state.stats.splice(index, 1, updatedStat)
    }
  },
  
  ADD_THEORY_STUDY(state, study) {
    state.theoryStudies.push(study);
  },
  
  ADD_FIRST_CONTACT(state, contact) {
    state.firstContacts.push(contact);
  },
  
  ADD_SPACED_REVISION(state, revision) {
    state.spacedRevisions.push(revision);
  },
  
  ADD_PERFORMANCE(state, performance) {
    state.performances.push(performance);
  },
  
  COMPLETE_FIRST_CONTACT(state, contactId) {
    const contact = state.firstContacts.find(c => c.id === contactId);
    if (contact) {
      contact.completed = true;
    }
  },
  
  COMPLETE_REVISION(state, revisionId) {
    const revision = state.spacedRevisions.find(r => r.id === revisionId);
    if (revision) {
      revision.completed = true;
    }
  },
  
  SET_REVISIONS_DATA(state, data) {
    state.theoryStudies = data.theoryStudies || [];
    state.firstContacts = data.firstContacts || [];
    state.spacedRevisions = data.spacedRevisions || [];
    state.performances = data.performances || [];
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}