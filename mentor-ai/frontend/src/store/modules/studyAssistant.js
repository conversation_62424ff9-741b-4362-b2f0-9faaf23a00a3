// Store Vuex para o StudyAssistantUltra
import StudyAssistantAIService from '@/services/StudyAssistantAIService';

const state = {
  // Status da IA
  aiStatus: 'online',
  isAnalyzing: false,
  
  // Dados do usuário
  userStats: {
    streak: 0,
    level: 1,
    xp: 0,
    totalStudyHours: 0,
    sessionsCompleted: 0,
  },
  
  // Análise Neural
  neuralAnalysis: null,
  performanceData: null,
  
  // Velocidade de Aprendizado
  learningVelocity: {
    current: 1.0,
    prediction: '1.2x',
    history: [],
  },
  
  // Recomendações
  recommendations: [],
  
  // Conquistas
  achievements: [],
  unlockedAchievements: [],
  
  // Sessão Ativa
  activeSession: null,
  sessionMetrics: {
    startTime: null,
    focusScore: 0,
    topicsStudied: [],
    questionsAnswered: 0,
  },
  
  // Planos de Estudo
  studyPlans: [],
  activePlan: null,
  
  // Configurações
  preferences: {
    studyMode: 'balanced',
    notificationsEnabled: true,
    aiAssistanceLevel: 'high',
    theme: 'neural',
  },
  
  // Cache de dados
  lastAnalysisUpdate: null,
  dataCache: {},
};

const getters = {
  isAIOnline: (state) => state.aiStatus === 'online',
  
  currentStreak: (state) => state.userStats.streak,
  
  userLevel: (state) => state.userStats.level,
  
  totalXP: (state) => state.userStats.xp,
  
  hasActiveSession: (state) => state.activeSession !== null,
  
  topRecommendations: (state) => state.recommendations.slice(0, 3),
  
  recentAchievements: (state) => {
    return state.unlockedAchievements
      .sort((a, b) => new Date(b.unlockedAt) - new Date(a.unlockedAt))
      .slice(0, 5);
  },
  
  learningTrend: (state) => {
    if (!state.learningVelocity.history || state.learningVelocity.history.length < 2) {
      return 'stable';
    }
    
    const recent = state.learningVelocity.history.slice(-5);
    const avg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    const lastValue = recent[recent.length - 1];
    
    if (lastValue > avg * 1.1) return 'increasing';
    if (lastValue < avg * 0.9) return 'decreasing';
    return 'stable';
  },
  
  needsAnalysisUpdate: (state) => {
    if (!state.lastAnalysisUpdate) return true;
    
    const hoursSinceUpdate = (Date.now() - state.lastAnalysisUpdate) / (1000 * 60 * 60);
    return hoursSinceUpdate > 1; // Atualizar a cada hora
  },
};

const mutations = {
  SET_AI_STATUS(state, status) {
    state.aiStatus = status;
  },
  
  SET_ANALYZING(state, isAnalyzing) {
    state.isAnalyzing = isAnalyzing;
  },
  
  UPDATE_USER_STATS(state, stats) {
    state.userStats = { ...state.userStats, ...stats };
  },
  
  SET_NEURAL_ANALYSIS(state, analysis) {
    state.neuralAnalysis = analysis;
    state.lastAnalysisUpdate = Date.now();
  },
  
  SET_PERFORMANCE_DATA(state, data) {
    state.performanceData = data;
  },
  
  UPDATE_LEARNING_VELOCITY(state, velocity) {
    state.learningVelocity = { ...state.learningVelocity, ...velocity };
    
    if (velocity.current !== undefined) {
      state.learningVelocity.history.push(velocity.current);
      // Manter apenas os últimos 30 registros
      if (state.learningVelocity.history.length > 30) {
        state.learningVelocity.history.shift();
      }
    }
  },
  
  SET_RECOMMENDATIONS(state, recommendations) {
    state.recommendations = recommendations;
  },
  
  SET_ACHIEVEMENTS(state, achievements) {
    state.achievements = achievements;
    state.unlockedAchievements = achievements.filter(a => a.unlockedAt);
  },
  
  UNLOCK_ACHIEVEMENT(state, achievementId) {
    const achievement = state.achievements.find(a => a.id === achievementId);
    if (achievement && !achievement.unlockedAt) {
      achievement.unlockedAt = new Date().toISOString();
      state.unlockedAchievements.push(achievement);
      
      // Adicionar XP da conquista
      if (achievement.xpReward) {
        state.userStats.xp += achievement.xpReward;
      }
    }
  },
  
  START_SESSION(state, sessionData) {
    state.activeSession = {
      id: `session_${Date.now()}`,
      startTime: new Date().toISOString(),
      ...sessionData,
    };
    state.sessionMetrics = {
      startTime: Date.now(),
      focusScore: 0,
      topicsStudied: [],
      questionsAnswered: 0,
    };
  },
  
  UPDATE_SESSION_METRICS(state, metrics) {
    state.sessionMetrics = { ...state.sessionMetrics, ...metrics };
  },
  
  END_SESSION(state) {
    if (state.activeSession) {
      const duration = (Date.now() - state.sessionMetrics.startTime) / (1000 * 60 * 60);
      state.userStats.totalStudyHours += duration;
      state.userStats.sessionsCompleted += 1;
    }
    
    state.activeSession = null;
    state.sessionMetrics = {
      startTime: null,
      focusScore: 0,
      topicsStudied: [],
      questionsAnswered: 0,
    };
  },
  
  ADD_STUDY_PLAN(state, plan) {
    state.studyPlans.push(plan);
  },
  
  SET_ACTIVE_PLAN(state, planId) {
    state.activePlan = state.studyPlans.find(p => p.id === planId);
  },
  
  UPDATE_PREFERENCES(state, preferences) {
    state.preferences = { ...state.preferences, ...preferences };
  },
  
  CACHE_DATA(state, { key, data }) {
    state.dataCache[key] = {
      data,
      timestamp: Date.now(),
    };
  },
  
  INCREMENT_STREAK(state) {
    state.userStats.streak += 1;
  },
  
  RESET_STREAK(state) {
    state.userStats.streak = 0;
  },
  
  ADD_XP(state, amount) {
    state.userStats.xp += amount;
    
    // Calcular nível baseado em XP
    const xpPerLevel = 1000;
    const newLevel = Math.floor(state.userStats.xp / xpPerLevel) + 1;
    
    if (newLevel > state.userStats.level) {
      state.userStats.level = newLevel;
      // Pode disparar uma notificação de level up aqui
    }
  },
};

const actions = {
  async initializeStudyAssistant({ commit, dispatch }) {
    try {
      commit('SET_AI_STATUS', 'connecting');
      
      // Carregar dados do localStorage
      const savedStats = localStorage.getItem('studyAssistantStats');
      if (savedStats) {
        commit('UPDATE_USER_STATS', JSON.parse(savedStats));
      }
      
      // Carregar preferências
      const savedPreferences = localStorage.getItem('studyAssistantPreferences');
      if (savedPreferences) {
        commit('UPDATE_PREFERENCES', JSON.parse(savedPreferences));
      }
      
      // Verificar e atualizar streak
      await dispatch('checkDailyStreak');
      
      // Carregar conquistas
      await dispatch('loadAchievements');
      
      // Realizar análise inicial
      await dispatch('performNeuralAnalysis');
      
      commit('SET_AI_STATUS', 'online');
    } catch (error) {
      console.error('Erro ao inicializar StudyAssistant:', error);
      commit('SET_AI_STATUS', 'error');
    }
  },
  
  async performNeuralAnalysis({ commit, state }) {
    if (state.isAnalyzing) return;
    
    try {
      commit('SET_ANALYZING', true);
      
      const userId = localStorage.getItem('userId') || 'demo_user';
      const analysis = await StudyAssistantAIService.analyzeNeuralPerformance(userId);
      
      commit('SET_NEURAL_ANALYSIS', analysis);
      
      // Atualizar velocidade de aprendizado
      if (analysis.learningVelocity) {
        commit('UPDATE_LEARNING_VELOCITY', analysis.learningVelocity);
      }
      
      // Obter recomendações baseadas na análise
      const recommendations = await StudyAssistantAIService.getAIRecommendations({
        performance: analysis.performance,
        weaknesses: analysis.weaknesses,
      });
      
      commit('SET_RECOMMENDATIONS', recommendations);
      
    } catch (error) {
      console.error('Erro na análise neural:', error);
    } finally {
      commit('SET_ANALYZING', false);
    }
  },
  
  async generateNeuralStudyPlan({ commit }, params) {
    try {
      const plan = await StudyAssistantAIService.generateNeuralStudyPlan(params);
      
      commit('ADD_STUDY_PLAN', {
        ...plan,
        id: plan.planId,
        createdAt: new Date().toISOString(),
      });
      
      commit('SET_ACTIVE_PLAN', plan.planId);
      
      return plan;
    } catch (error) {
      console.error('Erro ao gerar plano neural:', error);
      throw error;
    }
  },
  
  async startStudySession({ commit, dispatch }, { topic, mode }) {
    commit('START_SESSION', { topic, mode });
    
    // Iniciar monitoramento de foco
    dispatch('monitorFocus');
    
    // Incrementar XP por iniciar sessão
    commit('ADD_XP', 10);
  },
  
  async endStudySession({ commit, state, dispatch }) {
    if (!state.activeSession) return;
    
    const sessionDuration = (Date.now() - state.sessionMetrics.startTime) / (1000 * 60);
    
    // Calcular XP baseado na duração e desempenho
    const baseXP = Math.floor(sessionDuration * 2);
    const focusBonus = Math.floor(state.sessionMetrics.focusScore * 50);
    const totalXP = baseXP + focusBonus;
    
    commit('ADD_XP', totalXP);
    commit('END_SESSION');
    
    // Salvar estatísticas
    dispatch('saveStats');
    
    // Verificar conquistas
    dispatch('checkAchievements');
  },
  
  async monitorFocus({ commit, state }) {
    if (!state.activeSession) return;
    
    // Simular análise de foco
    const focusData = await StudyAssistantAIService.analyzeFocus(state.sessionMetrics);
    
    commit('UPDATE_SESSION_METRICS', {
      focusScore: focusData.score,
    });
    
    // Continuar monitorando se a sessão ainda estiver ativa
    if (state.activeSession) {
      setTimeout(() => {
        this.dispatch('studyAssistant/monitorFocus');
      }, 30000); // Verificar a cada 30 segundos
    }
  },
  
  async loadAchievements({ commit }) {
    try {
      const userId = localStorage.getItem('userId') || 'demo_user';
      const achievements = await StudyAssistantAIService.getAchievements(userId);
      
      commit('SET_ACHIEVEMENTS', achievements);
    } catch (error) {
      console.error('Erro ao carregar conquistas:', error);
    }
  },
  
  async checkAchievements({ state, commit }) {
    // Verificar conquistas baseadas nas estatísticas atuais
    
    // Conquista: Primeira Sessão
    if (state.userStats.sessionsCompleted === 1) {
      commit('UNLOCK_ACHIEVEMENT', 'first_session');
    }
    
    // Conquista: Streak de 7 dias
    if (state.userStats.streak >= 7) {
      commit('UNLOCK_ACHIEVEMENT', 'week_streak');
    }
    
    // Conquista: 10 horas de estudo
    if (state.userStats.totalStudyHours >= 10) {
      commit('UNLOCK_ACHIEVEMENT', 'study_10_hours');
    }
    
    // Conquista: Nível 5
    if (state.userStats.level >= 5) {
      commit('UNLOCK_ACHIEVEMENT', 'level_5');
    }
  },
  
  async checkDailyStreak({ commit, state }) {
    const lastStudyDate = localStorage.getItem('lastStudyDate');
    const today = new Date().toDateString();
    
    if (lastStudyDate) {
      const lastDate = new Date(lastStudyDate);
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (lastDate.toDateString() === yesterday.toDateString()) {
        // Estudou ontem, manter streak
        localStorage.setItem('lastStudyDate', today);
      } else if (lastDate.toDateString() !== today) {
        // Não estudou ontem, resetar streak
        commit('RESET_STREAK');
      }
    } else {
      // Primeira vez
      localStorage.setItem('lastStudyDate', today);
    }
  },
  
  saveStats({ state }) {
    localStorage.setItem('studyAssistantStats', JSON.stringify(state.userStats));
  },
  
  savePreferences({ state }) {
    localStorage.setItem('studyAssistantPreferences', JSON.stringify(state.preferences));
  },
  
  updatePreferences({ commit, dispatch }, preferences) {
    commit('UPDATE_PREFERENCES', preferences);
    dispatch('savePreferences');
  },
  
  async optimizeMemory({ commit }, { topicId, learningData }) {
    try {
      const optimization = await StudyAssistantAIService.optimizeMemory(topicId, learningData);
      
      commit('CACHE_DATA', {
        key: `memory_opt_${topicId}`,
        data: optimization,
      });
      
      return optimization;
    } catch (error) {
      console.error('Erro ao otimizar memória:', error);
      throw error;
    }
  },
  
  async generate3DConceptMap({ commit }, topic) {
    try {
      const conceptMap = await StudyAssistantAIService.generate3DConceptMap(topic);
      
      commit('CACHE_DATA', {
        key: `concept_map_${topic}`,
        data: conceptMap,
      });
      
      return conceptMap;
    } catch (error) {
      console.error('Erro ao gerar mapa conceitual:', error);
      throw error;
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};