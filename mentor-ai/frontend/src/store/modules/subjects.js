const state = {
  subjects: []
};

const getters = {
  allSubjects: state => state.subjects,
  getSubjectById: state => id => state.subjects.find(s => s.id === id)
};

const actions = {
  fetchSubjects({ commit, state }) {
    // Se já temos disciplinas no estado, retorná-las
    if (state.subjects.length > 0) {
      console.log('Retornando disciplinas existentes:', state.subjects);
      return Promise.resolve(state.subjects);
    }
    
    // Caso contrário, inicializar com dados mockados
    const mockSubjects = [
      { id: '1', name: 'Anatomia', color: '#e63946' },
      { id: '2', name: 'Fisiologia', color: '#457b9d' },
      { id: '3', name: 'Bioquímica', color: '#1d3557' },
      { id: '4', name: 'Farmacologia', color: '#f59e0b' },
      { id: '5', name: 'Patologia', color: '#22c55e' }
    ];
    
    console.log('Inicializando com disciplinas mockadas:', mockSubjects);
    commit('SET_SUBJECTS', mockSubjects);
    return Promise.resolve(mockSubjects);
  },
  
  addSubject({ commit, state }, subject) {
    console.log('Action addSubject chamada com:', subject);
    commit('ADD_SUBJECT', subject);
    console.log('Estado após adicionar:', state.subjects);
    return Promise.resolve(subject);
  },
  
  updateSubject({ commit }, subject) {
    commit('UPDATE_SUBJECT', subject);
  },
  
  deleteSubject({ commit }, id) {
    commit('DELETE_SUBJECT', id);
  }
};

const mutations = {
  SET_SUBJECTS(state, subjects) {
    console.log('Mutation SET_SUBJECTS:', subjects);
    state.subjects = subjects;
  },
  
  ADD_SUBJECT(state, subject) {
    console.log('Mutation ADD_SUBJECT:', subject);
    console.log('Estado antes:', [...state.subjects]);
    state.subjects.push(subject);
    console.log('Estado depois:', [...state.subjects]);
  },
  
  UPDATE_SUBJECT(state, updatedSubject) {
    const index = state.subjects.findIndex(s => s.id === updatedSubject.id);
    if (index !== -1) {
      state.subjects.splice(index, 1, updatedSubject);
    }
  },
  
  DELETE_SUBJECT(state, id) {
    state.subjects = state.subjects.filter(s => s.id !== id);
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};