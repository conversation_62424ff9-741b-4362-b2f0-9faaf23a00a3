// store/modules/gamification.js
const state = {
  user: {
    level: 1,
    xp: 0,
    currentStreak: 0,
    bestStreak: 0,
    achievements: [],
    badges: [],
    totalXP: 0
  },
  
  stats: {
    totalReviews: 0,
    totalQuestions: 0,
    averageScore: 0,
    nightStudies: 0,
    earlyStudies: 0,
    lastScore: 0,
    reviewCount: 0,
    comebackAfterBreak: false
  },
  
  dailyChallenges: [],
  leaderboard: [],
  rewards: []
}

const getters = {
  userStats: (state) => state.stats,
  currentStreak: (state) => state.user.currentStreak,
  userLevel: (state) => state.user.level,
  userId: (state) => state.user.id || 'default_user',
  
  unlockedAchievements: (state) => {
    return state.user.achievements.filter(a => a.unlocked)
  },
  
  progressToNextLevel: (state) => {
    const xpBase = 100
    const xpMultiplier = 1.5
    const nextLevelXP = Math.floor(xpBase * Math.pow(xpMultiplier, state.user.level - 1))
    return {
      current: state.user.xp,
      required: nextLevelXP,
      percentage: (state.user.xp / nextLevelXP) * 100
    }
  }
}

const mutations = {
  SET_USER_DATA(state, userData) {
    state.user = { ...state.user, ...userData }
  },
  
  UPDATE_XP(state, { xp, level }) {
    state.user.xp = xp
    state.user.level = level
    state.user.totalXP += xp
  },
  
  UPDATE_STREAK(state, { current, best }) {
    state.user.currentStreak = current
    state.user.bestStreak = best
  },
  
  ADD_ACHIEVEMENT(state, achievement) {
    state.user.achievements.push(achievement)
  },
  
  UPDATE_STATS(state, stats) {
    state.stats = { ...state.stats, ...stats }
  },
  
  SET_DAILY_CHALLENGES(state, challenges) {
    state.dailyChallenges = challenges
  },
  
  UPDATE_CHALLENGE_PROGRESS(state, { challengeId, progress }) {
    const challenge = state.dailyChallenges.find(c => c.id === challengeId)
    if (challenge) {
      challenge.current = progress
      challenge.completed = progress >= challenge.target
    }
  },
  
  SET_LEADERBOARD(state, leaderboard) {
    state.leaderboard = leaderboard
  },
  
  ADD_REWARD(state, reward) {
    state.rewards.push(reward)
  }
}

const actions = {
  async loadUserData({ commit }) {
    try {
      // Em produção, isso seria uma chamada à API
      const savedData = localStorage.getItem('gamificationData')
      if (savedData) {
        const userData = JSON.parse(savedData)
        commit('SET_USER_DATA', userData)
      }
      
      // Gerar desafios diários
      const challenges = [
        {
          id: 'daily_reviews',
          title: 'Complete 5 revisões',
          icon: 'tasks',
          target: 5,
          current: 0,
          reward: 50,
          completed: false
        },
        {
          id: 'perfect_session',
          title: 'Uma sessão perfeita (100%)',
          icon: 'trophy',
          target: 1,
          current: 0,
          reward: 100,
          completed: false
        },
        {
          id: 'study_time',
          title: 'Estude por 2 horas',
          icon: 'clock',
          target: 120,
          current: 0,
          reward: 75,
          completed: false
        }
      ]
      
      commit('SET_DAILY_CHALLENGES', challenges)
      
      return true
    } catch (error) {
      console.error('Error loading user data:', error)
      return false
    }
  },
  
  async updateXP({ commit }, { xp, level }) {
    commit('UPDATE_XP', { xp, level })
    
    // Salvar no localStorage (em produção seria API)
    const userData = {
      xp,
      level,
      lastUpdated: new Date()
    }
    localStorage.setItem('gamificationData', JSON.stringify(userData))
  },
  
  async updateStreak({ commit }, { current, best }) {
    commit('UPDATE_STREAK', { current, best })
  },
  
  async unlockAchievement({ commit }, achievement) {
    commit('ADD_ACHIEVEMENT', achievement)
    
    // Notificar outros módulos se necessário
    return achievement
  },
  
  async updateStats({ commit }, newStats) {
    commit('UPDATE_STATS', newStats)
  },
  
  async updateChallengeProgress({ commit }, { challengeId, progress }) {
    commit('UPDATE_CHALLENGE_PROGRESS', { challengeId, progress })
  },
  
  async fetchLeaderboard({ commit }, { period = 'weekly' }) {
    try {
      // Simular dados do leaderboard
      const leaderboard = [
        { rank: 1, userId: 'user1', name: 'João Silva', xp: 2500, level: 15, avatar: null },
        { rank: 2, userId: 'user2', name: 'Maria Santos', xp: 2300, level: 14, avatar: null },
        { rank: 3, userId: 'user3', name: 'Pedro Oliveira', xp: 2100, level: 13, avatar: null },
        { rank: 4, userId: 'current', name: 'Você', xp: 1800, level: 10, avatar: null, isCurrentUser: true },
        { rank: 5, userId: 'user5', name: 'Ana Costa', xp: 1600, level: 9, avatar: null }
      ]
      
      commit('SET_LEADERBOARD', leaderboard)
      return leaderboard
    } catch (error) {
      console.error('Error fetching leaderboard:', error)
      throw error
    }
  },
  
  async claimReward({ commit, state }, rewardId) {
    try {
      // Verificar se o usuário pode reivindicar a recompensa
      const reward = state.rewards.find(r => r.id === rewardId)
      
      if (!reward || reward.claimed) {
        throw new Error('Recompensa não disponível')
      }
      
      // Marcar como reivindicada
      reward.claimed = true
      reward.claimedAt = new Date()
      
      return reward
    } catch (error) {
      console.error('Error claiming reward:', error)
      throw error
    }
  },
  
  async trackStudySession({ commit, dispatch }, sessionData) {
    const { duration, timeOfDay, performance } = sessionData
    
    // Atualizar estatísticas
    const stats = {
      lastScore: performance,
      reviewCount: state.stats.reviewCount + 1,
      totalReviews: state.stats.totalReviews + 1
    }
    
    // Verificar estudos noturnos/matinais
    const hour = new Date(timeOfDay).getHours()
    if (hour >= 23 || hour < 5) {
      stats.nightStudies = (state.stats.nightStudies || 0) + 1
    } else if (hour >= 5 && hour < 7) {
      stats.earlyStudies = (state.stats.earlyStudies || 0) + 1
    }
    
    commit('UPDATE_STATS', stats)
    
    // Atualizar progresso dos desafios
    await dispatch('updateChallengeProgress', {
      challengeId: 'daily_reviews',
      progress: state.stats.reviewCount
    })
    
    if (performance === 100) {
      await dispatch('updateChallengeProgress', {
        challengeId: 'perfect_session',
        progress: 1
      })
    }
    
    await dispatch('updateChallengeProgress', {
      challengeId: 'study_time',
      progress: state.stats.totalStudyTime || 0 + duration
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}