const state = {
  // User Progress
  userLevel: 1,
  userExperience: 0,
  totalStudyTime: 0,
  currentStreak: 0,
  longestStreak: 0,
  
  // Session Data
  currentSession: {
    type: 'focus',
    startTime: null,
    duration: 25 * 60,
    timeRemaining: 25 * 60,
    isActive: false,
    completedPomodoros: 0
  },
  
  // Study Goals
  goals: [],
  
  // Notes
  notes: [],
  currentNoteId: null,
  
  // Tasks
  tasks: {
    todo: [],
    inProgress: [],
    completed: []
  },
  
  // Analytics
  dailyStats: {},
  weeklyStats: {},
  monthlyStats: {},
  
  // Achievements
  achievements: [],
  unlockedAchievements: [],
  
  // Settings
  settings: {
    ambientMode: false,
    musicVolume: 70,
    ambientPreset: 'lofi',
    notifications: true,
    darkMode: true,
    autoStartBreaks: true,
    autoStartPomodoros: false
  },
  
  // Resources
  savedResources: [],
  resourceProgress: {}
}

const getters = {
  getUserLevel: state => state.userLevel,
  getUserExperience: state => state.userExperience,
  getExperienceForNextLevel: state => state.userLevel * 300,
  getExperienceProgress: state => {
    const needed = state.userLevel * 300
    return (state.userExperience / needed) * 100
  },
  
  getCurrentSession: state => state.currentSession,
  getFormattedTime: state => {
    const minutes = Math.floor(state.currentSession.timeRemaining / 60)
    const seconds = state.currentSession.timeRemaining % 60
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  },
  
  getAllTasks: state => state.tasks,
  getTotalTasks: state => {
    return Object.values(state.tasks).reduce((total, category) => total + category.length, 0)
  },
  getCompletedTasksCount: state => state.tasks.completed.length,
  
  getActiveGoals: state => state.goals.filter(goal => !goal.completed),
  getCompletedGoals: state => state.goals.filter(goal => goal.completed),
  
  getCurrentNote: state => {
    if (!state.currentNoteId) return null
    return state.notes.find(note => note.id === state.currentNoteId)
  },
  
  getDailyStats: state => date => state.dailyStats[date] || null,
  getWeeklyStats: state => state.weeklyStats,
  
  getUnlockedAchievements: state => state.unlockedAchievements,
  getSettings: state => state.settings
}

const mutations = {
  // User Progress
  ADD_EXPERIENCE(state, amount) {
    state.userExperience += amount
    
    // Check for level up
    const experienceNeeded = state.userLevel * 300
    if (state.userExperience >= experienceNeeded) {
      state.userLevel++
      state.userExperience -= experienceNeeded
    }
  },
  
  UPDATE_STREAK(state) {
    const today = new Date().toDateString()
    const lastActive = localStorage.getItem('lastActiveDate')
    
    if (lastActive === today) return
    
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    
    if (lastActive === yesterday.toDateString()) {
      state.currentStreak++
    } else {
      state.currentStreak = 1
    }
    
    if (state.currentStreak > state.longestStreak) {
      state.longestStreak = state.currentStreak
    }
    
    localStorage.setItem('lastActiveDate', today)
  },
  
  // Session Management
  START_SESSION(state, sessionType) {
    state.currentSession.type = sessionType
    state.currentSession.startTime = Date.now()
    state.currentSession.isActive = true
    
    switch(sessionType) {
      case 'focus':
        state.currentSession.duration = 25 * 60
        break
      case 'study':
        state.currentSession.duration = 45 * 60
        break
      case 'review':
        state.currentSession.duration = 15 * 60
        break
      case 'break':
        state.currentSession.duration = 5 * 60
        break
    }
    
    state.currentSession.timeRemaining = state.currentSession.duration
  },
  
  UPDATE_SESSION_TIME(state) {
    if (state.currentSession.timeRemaining > 0) {
      state.currentSession.timeRemaining--
    }
  },
  
  PAUSE_SESSION(state) {
    state.currentSession.isActive = false
  },
  
  COMPLETE_SESSION(state) {
    state.currentSession.isActive = false
    
    if (state.currentSession.type !== 'break') {
      state.currentSession.completedPomodoros++
      state.totalStudyTime += state.currentSession.duration / 60
      
      // Update daily stats
      const today = new Date().toDateString()
      if (!state.dailyStats[today]) {
        state.dailyStats[today] = {
          studyTime: 0,
          sessionsCompleted: 0,
          tasksCompleted: 0
        }
      }
      state.dailyStats[today].studyTime += state.currentSession.duration / 60
      state.dailyStats[today].sessionsCompleted++
    }
  },
  
  RESET_SESSION(state) {
    state.currentSession.timeRemaining = state.currentSession.duration
    state.currentSession.isActive = false
  },
  
  // Goals
  ADD_GOAL(state, goal) {
    state.goals.push({
      id: Date.now(),
      ...goal,
      createdAt: new Date(),
      completed: false,
      progress: 0
    })
  },
  
  UPDATE_GOAL_PROGRESS(state, { goalId, progress }) {
    const goal = state.goals.find(g => g.id === goalId)
    if (goal) {
      goal.progress = progress
      if (progress >= goal.target) {
        goal.completed = true
        goal.completedAt = new Date()
      }
    }
  },
  
  DELETE_GOAL(state, goalId) {
    const index = state.goals.findIndex(g => g.id === goalId)
    if (index > -1) {
      state.goals.splice(index, 1)
    }
  },
  
  // Notes
  CREATE_NOTE(state, note) {
    const newNote = {
      id: Date.now(),
      title: note.title || 'Nova Nota',
      content: note.content || '',
      tags: note.tags || [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
    state.notes.push(newNote)
    state.currentNoteId = newNote.id
  },
  
  UPDATE_NOTE(state, { noteId, updates }) {
    const note = state.notes.find(n => n.id === noteId)
    if (note) {
      Object.assign(note, updates)
      note.updatedAt = new Date()
    }
  },
  
  DELETE_NOTE(state, noteId) {
    const index = state.notes.findIndex(n => n.id === noteId)
    if (index > -1) {
      state.notes.splice(index, 1)
      if (state.currentNoteId === noteId) {
        state.currentNoteId = null
      }
    }
  },
  
  SET_CURRENT_NOTE(state, noteId) {
    state.currentNoteId = noteId
  },
  
  // Tasks
  ADD_TASK(state, { category, task }) {
    state.tasks[category].push({
      id: Date.now(),
      ...task,
      createdAt: new Date()
    })
  },
  
  MOVE_TASK(state, { taskId, fromCategory, toCategory }) {
    const taskIndex = state.tasks[fromCategory].findIndex(t => t.id === taskId)
    if (taskIndex > -1) {
      const task = state.tasks[fromCategory].splice(taskIndex, 1)[0]
      state.tasks[toCategory].push(task)
      
      // Update stats if task is completed
      if (toCategory === 'completed') {
        const today = new Date().toDateString()
        if (!state.dailyStats[today]) {
          state.dailyStats[today] = {
            studyTime: 0,
            sessionsCompleted: 0,
            tasksCompleted: 0
          }
        }
        state.dailyStats[today].tasksCompleted++
      }
    }
  },
  
  UPDATE_TASK(state, { category, taskId, updates }) {
    const task = state.tasks[category].find(t => t.id === taskId)
    if (task) {
      Object.assign(task, updates)
    }
  },
  
  DELETE_TASK(state, { category, taskId }) {
    const index = state.tasks[category].findIndex(t => t.id === taskId)
    if (index > -1) {
      state.tasks[category].splice(index, 1)
    }
  },
  
  // Achievements
  UNLOCK_ACHIEVEMENT(state, achievement) {
    if (!state.unlockedAchievements.find(a => a.id === achievement.id)) {
      state.unlockedAchievements.push({
        ...achievement,
        unlockedAt: new Date()
      })
    }
  },
  
  // Settings
  UPDATE_SETTINGS(state, settings) {
    Object.assign(state.settings, settings)
  },
  
  // Resources
  SAVE_RESOURCE(state, resource) {
    if (!state.savedResources.find(r => r.id === resource.id)) {
      state.savedResources.push(resource)
    }
  },
  
  UPDATE_RESOURCE_PROGRESS(state, { resourceId, progress }) {
    state.resourceProgress[resourceId] = progress
  },
  
  // Load state from localStorage
  LOAD_STATE(state) {
    const savedState = localStorage.getItem('studyEnvironmentState')
    if (savedState) {
      const parsedState = JSON.parse(savedState)
      Object.assign(state, parsedState)
    }
  }
}

const actions = {
  initializeEnvironment({ commit }) {
    commit('LOAD_STATE')
    commit('UPDATE_STREAK')
  },
  
  startSession({ commit }, sessionType) {
    commit('START_SESSION', sessionType)
  },
  
  tickTimer({ commit, state }) {
    if (state.currentSession.isActive) {
      commit('UPDATE_SESSION_TIME')
      
      if (state.currentSession.timeRemaining === 0) {
        commit('COMPLETE_SESSION')
        commit('ADD_EXPERIENCE', 50)
        
        // Check for achievements
        if (state.currentSession.completedPomodoros === 1) {
          commit('UNLOCK_ACHIEVEMENT', {
            id: 'first_session',
            title: 'Primeira Sessão!',
            description: 'Complete sua primeira sessão de estudo',
            icon: 'fas fa-medal',
            points: 100
          })
        }
        
        return true // Session completed
      }
    }
    return false
  },
  
  pauseSession({ commit }) {
    commit('PAUSE_SESSION')
  },
  
  resetSession({ commit }) {
    commit('RESET_SESSION')
  },
  
  createGoal({ commit }, goal) {
    commit('ADD_GOAL', goal)
    commit('ADD_EXPERIENCE', 20)
  },
  
  updateGoalProgress({ commit }, payload) {
    commit('UPDATE_GOAL_PROGRESS', payload)
    
    // Check if goal was completed
    const goal = state.goals.find(g => g.id === payload.goalId)
    if (goal && goal.completed) {
      commit('ADD_EXPERIENCE', 100)
      commit('UNLOCK_ACHIEVEMENT', {
        id: 'goal_completed',
        title: 'Meta Alcançada!',
        description: 'Complete uma meta de estudo',
        icon: 'fas fa-trophy',
        points: 200
      })
    }
  },
  
  saveNote({ commit, state }) {
    if (state.currentNoteId) {
      commit('ADD_EXPERIENCE', 10)
    }
  },
  
  createTask({ commit }, { category, task }) {
    commit('ADD_TASK', { category, task })
  },
  
  moveTask({ commit }, payload) {
    commit('MOVE_TASK', payload)
    
    if (payload.toCategory === 'completed') {
      commit('ADD_EXPERIENCE', 15)
    }
  },
  
  saveState({ state }) {
    localStorage.setItem('studyEnvironmentState', JSON.stringify(state))
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}