// Removed SpacedRepetitionServiceUltra import

const state = {
  // Dados de Performance
  performanceMetrics: {
    totalStudyTime: 0,
    totalCards: 0,
    masteredCards: 0,
    averageAccuracy: 0,
    currentStreak: 0,
    longestStreak: 0,
    lastStudyDate: null
  },
  
  // Taxas de Retenção
  retentionRates: {
    shortTerm: 0,
    mediumTerm: 0,
    longTerm: 0,
    overall: 0
  },
  
  // Dados por Categoria
  categoryAnalytics: [],
  
  // Histórico de Sessões
  studySessions: [],
  reviewHistory: [],
  
  // Ranking e Comparações
  userRanking: {
    position: 1,
    total: 100,
    percentile: 0,
    badges: []
  },
  
  // Metas e Objetivos
  studyGoals: [],
  achievements: [],
  
  // Insights de IA
  aiInsights: [],
  recommendations: [],
  
  // Dados de Visualização
  chartData: {
    progress: [],
    retention: [],
    speed: [],
    categories: []
  },
  
  // Estado de Carregamento
  loading: {
    metrics: false,
    insights: false,
    charts: false
  },
  
  // Cache e Timestamps
  lastUpdated: null,
  cacheExpiry: 3600000 // 1 hora
};

const getters = {
  // Métricas Principais
  masteryLevel: (state) => {
    if (state.performanceMetrics.totalCards === 0) return 0;
    return Math.round((state.performanceMetrics.masteredCards / state.performanceMetrics.totalCards) * 100);
  },
  
  studyTimeFormatted: (state) => {
    const minutes = state.performanceMetrics.totalStudyTime;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  },
  
  // Tendências
  performanceTrend: (state) => {
    const recentData = state.chartData.progress.slice(-7);
    if (recentData.length < 2) return 'stable';
    
    const trend = recentData[recentData.length - 1] - recentData[0];
    if (trend > 5) return 'improving';
    if (trend < -5) return 'declining';
    return 'stable';
  },
  
  // Categorias Fracas
  weakCategories: (state) => {
    return state.categoryAnalytics
      .filter(cat => cat.accuracy < 60)
      .sort((a, b) => a.accuracy - b.accuracy)
      .slice(0, 3);
  },
  
  // Metas Ativas
  activeGoals: (state) => {
    return state.studyGoals.filter(goal => !goal.completed);
  },
  
  // Conquistas Recentes
  recentAchievements: (state) => {
    return state.achievements
      .filter(ach => ach.unlocked)
      .sort((a, b) => new Date(b.unlockedDate) - new Date(a.unlockedDate))
      .slice(0, 5);
  },
  
  // Necessita Atualização
  needsUpdate: (state) => {
    if (!state.lastUpdated) return true;
    return Date.now() - state.lastUpdated > state.cacheExpiry;
  }
};

const mutations = {
  // Atualizar Métricas
  SET_PERFORMANCE_METRICS(state, metrics) {
    state.performanceMetrics = { ...state.performanceMetrics, ...metrics };
  },
  
  SET_RETENTION_RATES(state, rates) {
    state.retentionRates = rates;
  },
  
  SET_CATEGORY_ANALYTICS(state, categories) {
    state.categoryAnalytics = categories;
  },
  
  // Histórico
  SET_STUDY_SESSIONS(state, sessions) {
    state.studySessions = sessions;
  },
  
  SET_REVIEW_HISTORY(state, history) {
    state.reviewHistory = history;
  },
  
  ADD_REVIEW(state, review) {
    state.reviewHistory.push(review);
  },
  
  // Ranking
  SET_USER_RANKING(state, ranking) {
    state.userRanking = ranking;
  },
  
  // Metas e Conquistas
  SET_STUDY_GOALS(state, goals) {
    state.studyGoals = goals;
  },
  
  UPDATE_GOAL(state, { goalId, updates }) {
    const index = state.studyGoals.findIndex(g => g.id === goalId);
    if (index !== -1) {
      state.studyGoals[index] = { ...state.studyGoals[index], ...updates };
    }
  },
  
  ADD_GOAL(state, goal) {
    state.studyGoals.push(goal);
  },
  
  SET_ACHIEVEMENTS(state, achievements) {
    state.achievements = achievements;
  },
  
  UNLOCK_ACHIEVEMENT(state, achievementId) {
    const achievement = state.achievements.find(a => a.id === achievementId);
    if (achievement) {
      achievement.unlocked = true;
      achievement.unlockedDate = new Date().toISOString();
    }
  },
  
  // Insights e Recomendações
  SET_AI_INSIGHTS(state, insights) {
    state.aiInsights = insights;
  },
  
  SET_RECOMMENDATIONS(state, recommendations) {
    state.recommendations = recommendations;
  },
  
  // Dados de Gráficos
  SET_CHART_DATA(state, { type, data }) {
    state.chartData[type] = data;
  },
  
  // Estados de Carregamento
  SET_LOADING(state, { type, value }) {
    state.loading[type] = value;
  },
  
  // Cache
  SET_LAST_UPDATED(state) {
    state.lastUpdated = Date.now();
  }
};

const actions = {
  // Carregar Análise Completa
  async fetchAnalytics({ commit, getters, rootState }) {
    if (!getters.needsUpdate) return;
    
    const userId = rootState.auth?.user?.id || 'demo';
    
    commit('SET_LOADING', { type: 'metrics', value: true });
    
    try {
      // Analytics service removed - returning mock data
      const analytics = { totalCards: 0, masteredCards: 0, averageAccuracy: 0 };
      
      // Atualizar métricas principais
      commit('SET_PERFORMANCE_METRICS', {
        totalStudyTime: analytics.totalMinutes,
        currentStreak: analytics.streakDays
      });
      
      commit('SET_RETENTION_RATES', analytics.retentionRates);
      commit('SET_USER_RANKING', analytics.ranking);
      
      // Gerar e salvar insights
      // Analytics service removed - returning mock insights
      const insights = [];
      commit('SET_AI_INSIGHTS', insights);
      
      commit('SET_LAST_UPDATED');
    } catch (error) {
      console.error('Erro ao carregar analytics:', error);
    } finally {
      commit('SET_LOADING', { type: 'metrics', value: false });
    }
  },
  
  // Carregar Dados de Categoria
  async fetchCategoryAnalytics({ commit, rootState }) {
    const userId = rootState.auth?.user?.id || 'demo';
    
    try {
      // Analytics service removed - returning mock categories
      const categories = [];
      commit('SET_CATEGORY_ANALYTICS', categories);
    } catch (error) {
      console.error('Erro ao carregar análise de categorias:', error);
    }
  },
  
  // Carregar Histórico de Revisões
  async fetchReviewHistory({ commit, rootState }) {
    const userId = rootState.auth?.user?.id || 'demo';
    
    try {
      // Simular dados por enquanto
      const mockHistory = generateMockReviewHistory();
      commit('SET_REVIEW_HISTORY', mockHistory);
    } catch (error) {
      console.error('Erro ao carregar histórico:', error);
    }
  },
  
  // Adicionar Nova Revisão
  async addReview({ commit, dispatch }, reviewData) {
    commit('ADD_REVIEW', {
      ...reviewData,
      date: new Date().toISOString(),
      id: Date.now().toString()
    });
    
    // Recalcular métricas após nova revisão
    await dispatch('updateMetrics');
  },
  
  // Atualizar Métricas em Tempo Real
  async updateMetrics({ state, commit }) {
    const recentReviews = state.reviewHistory.slice(-100);
    const correctCount = recentReviews.filter(r => r.correct).length;
    const accuracy = recentReviews.length > 0 ? (correctCount / recentReviews.length) * 100 : 0;
    
    commit('SET_PERFORMANCE_METRICS', {
      averageAccuracy: Math.round(accuracy)
    });
  },
  
  // Carregar Metas
  async fetchGoals({ commit, rootState }) {
    const userId = rootState.auth?.user?.id || 'demo';
    
    try {
      // Simular metas por enquanto
      const mockGoals = [
        {
          id: '1',
          title: 'Dominar Anatomia',
          description: 'Completar todos os cards de Anatomia com 90%+ de acerto',
          icon: 'fas fa-bone',
          color: '#6366f1',
          progress: 78,
          target: 100,
          deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          completed: false
        },
        {
          id: '2',
          title: 'Maratona de Estudos',
          description: 'Estudar 200 horas este mês',
          icon: 'fas fa-clock',
          color: '#10b981',
          progress: 65,
          target: 200,
          deadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          completed: false
        }
      ];
      
      commit('SET_STUDY_GOALS', mockGoals);
    } catch (error) {
      console.error('Erro ao carregar metas:', error);
    }
  },
  
  // Criar Nova Meta
  async createGoal({ commit }, goalData) {
    const newGoal = {
      ...goalData,
      id: Date.now().toString(),
      progress: 0,
      completed: false,
      createdAt: new Date().toISOString()
    };
    
    commit('ADD_GOAL', newGoal);
  },
  
  // Atualizar Progresso da Meta
  async updateGoalProgress({ commit }, { goalId, progress }) {
    commit('UPDATE_GOAL', {
      goalId,
      updates: {
        progress,
        completed: progress >= 100
      }
    });
  },
  
  // Carregar Conquistas
  async fetchAchievements({ commit }) {
    try {
      // Simular conquistas por enquanto
      const mockAchievements = [
        {
          id: '1',
          name: 'Primeira Semana',
          description: 'Complete 7 dias consecutivos de estudo',
          icon: 'fas fa-calendar-week',
          unlocked: true,
          unlockedDate: '2024-01-15',
          rarity: 'common',
          points: 10
        },
        {
          id: '2',
          name: 'Mestre dos Cards',
          description: 'Revise 1000 flashcards',
          icon: 'fas fa-layer-group',
          unlocked: true,
          unlockedDate: '2024-02-01',
          rarity: 'rare',
          points: 25
        },
        {
          id: '3',
          name: 'Perfeccionista',
          description: 'Alcance 95% de taxa de acerto em uma sessão',
          icon: 'fas fa-bullseye',
          unlocked: false,
          rarity: 'epic',
          points: 50,
          progress: 87
        }
      ];
      
      commit('SET_ACHIEVEMENTS', mockAchievements);
    } catch (error) {
      console.error('Erro ao carregar conquistas:', error);
    }
  },
  
  // Verificar e Desbloquear Conquistas
  async checkAchievements({ state, commit }) {
    // Verificar conquistas baseadas nas métricas atuais
    state.achievements.forEach(achievement => {
      if (achievement.unlocked) return;
      
      let shouldUnlock = false;
      
      // Lógica de desbloqueio baseada no ID
      switch (achievement.id) {
        case '3': // Perfeccionista
          if (state.performanceMetrics.averageAccuracy >= 95) {
            shouldUnlock = true;
          }
          break;
      }
      
      if (shouldUnlock) {
        commit('UNLOCK_ACHIEVEMENT', achievement.id);
      }
    });
  },
  
  // Gerar Insights com IA
  async generateInsights({ state, commit }) {
    commit('SET_LOADING', { type: 'insights', value: true });
    
    try {
      // Analytics service removed - returning mock insights
      const insights = [];
      commit('SET_AI_INSIGHTS', insights);
    } catch (error) {
      console.error('Erro ao gerar insights:', error);
    } finally {
      commit('SET_LOADING', { type: 'insights', value: false });
    }
  },
  
  // Exportar Relatório Completo
  async exportReport({ state, rootState }) {
    const userId = rootState.auth?.user?.id || 'demo';
    
    // Analytics service removed - returning mock report
    const report = {
      userId,
      ...state.performanceMetrics,
      reviewHistory: state.reviewHistory
    };
    
    // Download do relatório
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `relatorio_desempenho_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    return report;
  }
};

// Função auxiliar para gerar dados mock
function generateMockReviewHistory() {
  const history = [];
  const categories = ['Anatomia', 'Fisiologia', 'Farmacologia', 'Patologia'];
  const now = new Date();
  
  for (let i = 0; i < 500; i++) {
    const daysAgo = Math.floor(Math.random() * 90);
    const date = new Date(now);
    date.setDate(date.getDate() - daysAgo);
    
    history.push({
      id: i.toString(),
      cardId: `card-${Math.floor(Math.random() * 200)}`,
      date: date.toISOString(),
      correct: Math.random() > 0.2,
      responseTime: Math.floor(Math.random() * 30000) + 5000,
      difficulty: ['easy', 'medium', 'hard'][Math.floor(Math.random() * 3)],
      category: categories[Math.floor(Math.random() * categories.length)]
    });
  }
  
  return history.sort((a, b) => new Date(a.date) - new Date(b.date));
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};