import { ref, computed, watch } from 'vue'
import { useStore } from 'vuex'

export function useGamification() {
  const store = useStore()
  
  // Estado local
  const userLevel = ref(1)
  const currentXP = ref(0)
  const achievements = ref([])
  const currentStreak = ref(0)
  const bestStreak = ref(0)
  
  // Configuração de níveis
  const levelConfig = {
    xpBase: 100,
    xpMultiplier: 1.5,
    maxLevel: 100
  }
  
  // Calcular XP necessário para próximo nível
  const nextLevelXP = computed(() => {
    return Math.floor(levelConfig.xpBase * Math.pow(levelConfig.xpMultiplier, userLevel.value - 1))
  })
  
  // Progresso até próximo nível
  const xpProgress = computed(() => {
    return (currentXP.value / nextLevelXP.value) * 100
  })
  
  // Título do nível
  const levelTitle = computed(() => {
    const titles = {
      1: 'Iniciante',
      5: 'Aprendiz',
      10: 'Estuda<PERSON>',
      20: 'Acadêmico',
      30: 'Especialista',
      40: 'Expert',
      50: 'Mestre',
      60: 'Grão-Mestre',
      70: 'Sábio',
      80: 'Iluminado',
      90: 'Lendário',
      100: 'Transcendente'
    }
    
    const level = userLevel.value
    const titleKeys = Object.keys(titles).map(Number).sort((a, b) => b - a)
    
    for (const key of titleKeys) {
      if (level >= key) return titles[key]
    }
    
    return 'Iniciante'
  })
  
  // Sistema de conquistas
  const achievementsList = [
    // Conquistas de sequência
    {
      id: 'first_week',
      title: 'Primeira Semana',
      description: 'Complete 7 dias consecutivos de estudo',
      icon: 'calendar-week',
      rarity: 'common',
      condition: (stats) => stats.streak >= 7,
      reward: 100
    },
    {
      id: 'month_warrior',
      title: 'Guerreiro do Mês',
      description: '30 dias consecutivos de dedicação',
      icon: 'shield-alt',
      rarity: 'rare',
      condition: (stats) => stats.streak >= 30,
      reward: 500
    },
    {
      id: 'century_legend',
      title: 'Lenda Centenária',
      description: '100 dias ininterruptos de estudo',
      icon: 'crown',
      rarity: 'legendary',
      condition: (stats) => stats.streak >= 100,
      reward: 2000
    },
    
    // Conquistas de desempenho
    {
      id: 'perfect_score',
      title: 'Perfeição',
      description: 'Alcance 100% em uma revisão',
      icon: 'star',
      rarity: 'common',
      condition: (stats) => stats.lastScore === 100,
      reward: 50
    },
    {
      id: 'consistent_excellence',
      title: 'Excelência Consistente',
      description: 'Mantenha média acima de 90% por 10 revisões',
      icon: 'chart-line',
      rarity: 'epic',
      condition: (stats) => stats.averageScore >= 90 && stats.reviewCount >= 10,
      reward: 750
    },
    
    // Conquistas de volume
    {
      id: 'hundred_reviews',
      title: 'Centurião',
      description: 'Complete 100 revisões',
      icon: 'tasks',
      rarity: 'rare',
      condition: (stats) => stats.totalReviews >= 100,
      reward: 300
    },
    {
      id: 'thousand_questions',
      title: 'Mil Questões',
      description: 'Responda 1000 questões',
      icon: 'question-circle',
      rarity: 'epic',
      condition: (stats) => stats.totalQuestions >= 1000,
      reward: 1000
    },
    
    // Conquistas especiais
    {
      id: 'night_owl',
      title: 'Coruja Noturna',
      description: 'Estude após as 23h por 5 dias',
      icon: 'moon',
      rarity: 'rare',
      condition: (stats) => stats.nightStudies >= 5,
      reward: 200
    },
    {
      id: 'early_bird',
      title: 'Madrugador',
      description: 'Estude antes das 6h por 5 dias',
      icon: 'sun',
      rarity: 'rare',
      condition: (stats) => stats.earlyStudies >= 5,
      reward: 200
    },
    {
      id: 'comeback_king',
      title: 'Rei do Retorno',
      description: 'Retome os estudos após 7 dias de pausa',
      icon: 'undo',
      rarity: 'rare',
      condition: (stats) => stats.comebackAfterBreak,
      reward: 150
    }
  ]
  
  // Atualizar XP
  const updateXP = (amount) => {
    currentXP.value += amount
    
    // Verificar level up
    while (currentXP.value >= nextLevelXP.value) {
      currentXP.value -= nextLevelXP.value
      userLevel.value++
      
      // Disparar evento de level up
      onLevelUp()
    }
    
    // Salvar no store
    store.dispatch('gamification/updateXP', { xp: currentXP.value, level: userLevel.value })
  }
  
  // Verificar conquistas
  const checkAchievements = (eventType, eventData = {}) => {
    const userStats = getUserStats()
    
    achievementsList.forEach(achievement => {
      // Se já foi desbloqueada, pular
      if (achievements.value.find(a => a.id === achievement.id)) return
      
      // Verificar condição
      if (achievement.condition(userStats)) {
        unlockAchievement(achievement)
      }
    })
  }
  
  // Desbloquear conquista
  const unlockAchievement = (achievement) => {
    const unlockedAchievement = {
      ...achievement,
      unlocked: true,
      unlockedAt: new Date(),
      isNew: true
    }
    
    achievements.value.push(unlockedAchievement)
    
    // Adicionar XP da recompensa
    updateXP(achievement.reward)
    
    // Notificar usuário
    showAchievementNotification(achievement)
    
    // Salvar no store
    store.dispatch('gamification/unlockAchievement', unlockedAchievement)
    
    // Remover flag "isNew" após 5 segundos
    setTimeout(() => {
      const index = achievements.value.findIndex(a => a.id === achievement.id)
      if (index !== -1) {
        achievements.value[index].isNew = false
      }
    }, 5000)
  }
  
  // Atualizar sequência
  const updateStreak = (studied = true) => {
    if (studied) {
      currentStreak.value++
      if (currentStreak.value > bestStreak.value) {
        bestStreak.value = currentStreak.value
      }
    } else {
      // Verificar se quebrou uma sequência longa
      if (currentStreak.value >= 7) {
        checkAchievements('streak_broken', { previousStreak: currentStreak.value })
      }
      currentStreak.value = 0
    }
    
    store.dispatch('gamification/updateStreak', {
      current: currentStreak.value,
      best: bestStreak.value
    })
  }
  
  // Desafios diários
  const generateDailyChallenges = () => {
    const challenges = [
      {
        id: 'daily_reviews',
        title: 'Complete 5 revisões',
        icon: 'tasks',
        target: 5,
        current: 0,
        reward: 50
      },
      {
        id: 'perfect_session',
        title: 'Uma sessão perfeita (100%)',
        icon: 'trophy',
        target: 1,
        current: 0,
        reward: 100
      },
      {
        id: 'study_time',
        title: 'Estude por 2 horas',
        icon: 'clock',
        target: 120,
        current: 0,
        reward: 75
      }
    ]
    
    // Escolher 3 desafios aleatórios
    return challenges.sort(() => Math.random() - 0.5).slice(0, 3)
  }
  
  // Helpers
  const getUserStats = () => {
    // Obter estatísticas do store
    const stats = store.getters['gamification/userStats']
    return {
      streak: currentStreak.value,
      bestStreak: bestStreak.value,
      level: userLevel.value,
      totalXP: currentXP.value + (userLevel.value - 1) * levelConfig.xpBase,
      ...stats
    }
  }
  
  const onLevelUp = () => {
    // Efeitos visuais e sonoros podem ser adicionados aqui
    console.log(`Level up! Você agora é nível ${userLevel.value}`)
    
    // Verificar conquistas relacionadas a nível
    checkAchievements('level_up', { newLevel: userLevel.value })
  }
  
  const showAchievementNotification = (achievement) => {
    // Implementar notificação visual
    console.log(`Conquista desbloqueada: ${achievement.title}`)
  }
  
  // Sistema de rankings
  const getRankings = async (type = 'weekly') => {
    try {
      // Em produção, isso seria uma chamada à API
      const rankings = [
        { rank: 1, user: 'João Silva', xp: 2500, level: 15 },
        { rank: 2, user: 'Maria Santos', xp: 2300, level: 14 },
        { rank: 3, user: 'Pedro Oliveira', xp: 2100, level: 13 },
        // ... mais usuários
      ]
      
      return rankings
    } catch (error) {
      console.error('Error fetching rankings:', error)
      return []
    }
  }
  
  // Sistema de recompensas
  const claimReward = async (rewardId) => {
    try {
      // Verificar se a recompensa pode ser reivindicada
      const reward = await store.dispatch('gamification/claimReward', rewardId)
      
      if (reward) {
        // Aplicar recompensa
        if (reward.type === 'xp') {
          updateXP(reward.amount)
        } else if (reward.type === 'badge') {
          // Adicionar badge ao perfil
        }
        
        return { success: true, reward }
      }
      
      return { success: false, error: 'Recompensa não disponível' }
    } catch (error) {
      console.error('Error claiming reward:', error)
      return { success: false, error: error.message }
    }
  }
  
  // Inicializar dados do usuário
  const initializeUserData = async () => {
    try {
      const userData = await store.dispatch('gamification/loadUserData')
      
      if (userData) {
        userLevel.value = userData.level || 1
        currentXP.value = userData.xp || 0
        currentStreak.value = userData.currentStreak || 0
        bestStreak.value = userData.bestStreak || 0
        achievements.value = userData.achievements || []
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    }
  }
  
  return {
    // Estado
    userLevel,
    currentXP,
    nextLevelXP,
    xpProgress,
    levelTitle,
    achievements,
    currentStreak,
    bestStreak,
    
    // Métodos
    updateXP,
    checkAchievements,
    updateStreak,
    generateDailyChallenges,
    getRankings,
    claimReward,
    initializeUserData
  }
}