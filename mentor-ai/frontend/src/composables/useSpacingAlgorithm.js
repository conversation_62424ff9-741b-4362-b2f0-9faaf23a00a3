import { ref, computed } from 'vue'

// Algoritmo de espaçamento ultra avançado baseado em múltiplos fatores
export function useSpacingAlgorithm() {
  // Fatores de ajuste
  const factors = ref({
    difficulty: 2.5,
    performance: 1.0,
    mood: 1.0,
    energy: 1.0,
    timeOfDay: 1.0,
    consistency: 1.0
  })

  // Histórico de desempenho
  const performanceHistory = ref([])

  // Calcular próximo intervalo com algoritmo adaptativo
  const calculateNextInterval = ({
    previousInterval = 1,
    performance,
    complexity,
    mood = 'neutral',
    energy = 3,
    timeOfDay = new Date().getHours()
  }) => {
    // Fator base de desempenho
    let performanceFactor = 1.0
    if (performance >= 90) performanceFactor = 2.5
    else if (performance >= 80) performanceFactor = 2.0
    else if (performance >= 70) performanceFactor = 1.5
    else if (performance >= 60) performanceFactor = 1.2
    else if (performance >= 50) performanceFactor = 0.8
    else performanceFactor = 0.5

    // Fator de complexidade
    const complexityFactor = 1 + ((5 - complexity) * 0.2)

    // Fator de humor
    const moodFactors = {
      excellent: 1.2,
      good: 1.1,
      neutral: 1.0,
      tired: 0.9,
      stressed: 0.8
    }
    const moodFactor = moodFactors[mood] || 1.0

    // Fator de energia (1-5)
    const energyFactor = 0.7 + (energy * 0.1)

    // Fator de hora do dia (melhor pela manhã)
    let timeFactor = 1.0
    if (timeOfDay >= 6 && timeOfDay <= 10) timeFactor = 1.2
    else if (timeOfDay >= 14 && timeOfDay <= 16) timeFactor = 0.9
    else if (timeOfDay >= 22 || timeOfDay <= 5) timeFactor = 0.8

    // Calcular intervalo final
    const baseInterval = previousInterval * performanceFactor
    const adjustedInterval = baseInterval * complexityFactor * moodFactor * energyFactor * timeFactor

    // Limitar intervalo entre 1 e 365 dias
    return Math.max(1, Math.min(365, Math.round(adjustedInterval)))
  }

  // Gerar curva de retenção personalizada
  const getRetentionCurve = (initialRetention = 100, intervals = []) => {
    const curve = []
    let currentRetention = initialRetention

    for (let day = 0; day <= 365; day++) {
      // Modelo de decaimento exponencial modificado
      const decayRate = 0.1 // Taxa base de esquecimento
      const timeDecay = Math.exp(-decayRate * day)
      
      // Boost de retenção nas revisões
      let reviewBoost = 0
      intervals.forEach(reviewDay => {
        if (day > reviewDay) {
          const daysSinceReview = day - reviewDay
          reviewBoost += 20 * Math.exp(-0.1 * daysSinceReview)
        }
      })

      currentRetention = Math.min(100, initialRetention * timeDecay + reviewBoost)
      curve.push({
        day,
        retention: Math.max(0, currentRetention)
      })
    }

    return curve
  }

  // Análise preditiva de desempenho
  const predictPerformance = (subjectData) => {
    const { reviewHistory, complexity, tags } = subjectData
    
    if (reviewHistory.length < 3) {
      return { confidence: 'low', prediction: 70 }
    }

    // Calcular tendência
    const recentPerformances = reviewHistory.slice(-5).map(r => r.performance)
    const average = recentPerformances.reduce((a, b) => a + b, 0) / recentPerformances.length
    
    // Calcular desvio padrão
    const variance = recentPerformances.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / recentPerformances.length
    const stdDev = Math.sqrt(variance)

    // Determinar confiança
    let confidence = 'high'
    if (stdDev > 15) confidence = 'medium'
    if (stdDev > 25) confidence = 'low'

    // Ajustar predição baseada em tendência
    const trend = recentPerformances[recentPerformances.length - 1] - recentPerformances[0]
    const prediction = Math.max(0, Math.min(100, average + (trend * 0.3)))

    return { confidence, prediction: Math.round(prediction) }
  }

  // Otimizar agenda de revisões
  const optimizeSchedule = (subjects) => {
    // Agrupar por prioridade e urgência
    const prioritized = subjects.map(subject => {
      const daysUntilReview = Math.ceil((new Date(subject.nextReview) - new Date()) / (1000 * 60 * 60 * 24))
      const urgency = daysUntilReview <= 0 ? 5 : Math.max(1, 5 - daysUntilReview)
      
      return {
        ...subject,
        urgency,
        priority: urgency * (subject.importance || 3)
      }
    }).sort((a, b) => b.priority - a.priority)

    // Distribuir ao longo do dia
    const schedule = []
    const optimalSessionDuration = 25 // Pomodoro
    let currentTime = new Date()
    currentTime.setHours(8, 0, 0, 0) // Começar às 8h

    prioritized.forEach(subject => {
      schedule.push({
        ...subject,
        scheduledTime: new Date(currentTime),
        duration: optimalSessionDuration
      })
      currentTime = new Date(currentTime.getTime() + (optimalSessionDuration + 5) * 60000) // +5 min de pausa
    })

    return schedule
  }

  return {
    calculateNextInterval,
    getRetentionCurve,
    predictPerformance,
    optimizeSchedule,
    factors
  }
}