// Theme composable for managing dark/light mode across the application
import { ref, watch, onMounted } from 'vue'

// Theme state
const isDarkMode = ref(true)
const themeTransition = ref(false)

// Theme colors
const themes = {
  dark: {
    // Primary colors
    background: '#0a0a0a',
    surface: '#1a1a1a',
    surfaceLight: '#2a2a2a',
    
    // Text colors
    text: '#ffffff',
    textSecondary: '#a0a0a0',
    textMuted: '#666666',
    
    // Accent colors
    primary: '#00ffff',
    secondary: '#ff00ff',
    accent: '#ffff00',
    success: '#00ff88',
    warning: '#ffaa00',
    error: '#ff4444',
    
    // Border colors
    border: 'rgba(255, 255, 255, 0.1)',
    borderHover: 'rgba(255, 255, 255, 0.2)',
    
    // Glass effects
    glass: 'rgba(255, 255, 255, 0.05)',
    glassHover: 'rgba(255, 255, 255, 0.1)',
    
    // Shadows
    shadow: 'rgba(0, 0, 0, 0.5)',
    glow: 'rgba(0, 255, 255, 0.5)'
  },
  light: {
    // Primary colors
    background: '#ffffff',
    surface: '#f8f9fa',
    surfaceLight: '#e9ecef',
    
    // Text colors
    text: '#212529',
    textSecondary: '#6c757d',
    textMuted: '#adb5bd',
    
    // Accent colors
    primary: '#0066cc',
    secondary: '#6610f2',
    accent: '#fd7e14',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    
    // Border colors
    border: 'rgba(0, 0, 0, 0.1)',
    borderHover: 'rgba(0, 0, 0, 0.2)',
    
    // Glass effects
    glass: 'rgba(255, 255, 255, 0.7)',
    glassHover: 'rgba(255, 255, 255, 0.9)',
    
    // Shadows
    shadow: 'rgba(0, 0, 0, 0.1)',
    glow: 'rgba(0, 102, 204, 0.3)'
  }
}

// Get current theme
const currentTheme = () => themes[isDarkMode.value ? 'dark' : 'light']

// Apply theme to CSS variables
const applyTheme = () => {
  const theme = currentTheme()
  const root = document.documentElement
  
  // Enable transition after initial load
  if (themeTransition.value) {
    root.style.transition = 'background-color 0.3s ease, color 0.3s ease'
  }
  
  // Apply CSS variables
  Object.entries(theme).forEach(([key, value]) => {
    root.style.setProperty(`--theme-${key}`, value)
  })
  
  // Apply data attribute for component-specific styling
  root.setAttribute('data-theme', isDarkMode.value ? 'dark' : 'light')
  
  // Update meta theme-color
  const metaThemeColor = document.querySelector('meta[name="theme-color"]')
  if (metaThemeColor) {
    metaThemeColor.content = theme.background
  } else {
    const meta = document.createElement('meta')
    meta.name = 'theme-color'
    meta.content = theme.background
    document.head.appendChild(meta)
  }
}

// Toggle theme
const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value
  localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light')
  applyTheme()
}

// Initialize theme
const initTheme = () => {
  // Check localStorage
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    isDarkMode.value = savedTheme === 'dark'
  } else {
    // Check system preference
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    isDarkMode.value = prefersDark
  }
  
  applyTheme()
  
  // Enable transitions after initial load
  setTimeout(() => {
    themeTransition.value = true
  }, 100)
}

// Watch for system theme changes
const watchSystemTheme = () => {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  mediaQuery.addEventListener('change', (e) => {
    if (!localStorage.getItem('theme')) {
      isDarkMode.value = e.matches
      applyTheme()
    }
  })
}

// Export composable
export function useTheme() {
  onMounted(() => {
    initTheme()
    watchSystemTheme()
  })
  
  // Watch theme changes
  watch(isDarkMode, () => {
    applyTheme()
  })
  
  return {
    isDarkMode,
    currentTheme,
    toggleTheme,
    themes
  }
}