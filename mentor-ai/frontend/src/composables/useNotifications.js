import { ref, computed } from 'vue'
import { useStore } from 'vuex'

export function useNotifications() {
  const store = useStore()
  
  // Estado local
  const notifications = ref([])
  const permission = ref('default')
  const soundEnabled = ref(true)
  const smartScheduling = ref(true)
  
  // Inicializar notificações
  const initializeNotifications = async () => {
    // Verificar se o navegador suporta notificações
    if (!('Notification' in window)) {
      console.warn('Este navegador não suporta notificações desktop')
      return false
    }
    
    // Verificar permissão atual
    permission.value = Notification.permission
    
    // Solicitar permissão se necessário
    if (permission.value === 'default') {
      const result = await Notification.requestPermission()
      permission.value = result
    }
    
    return permission.value === 'granted'
  }
  
  // Agendar notificação
  const scheduleNotification = async ({
    title,
    body,
    scheduledFor,
    data = {},
    actions = []
  }) => {
    try {
      // Calcular delay
      const now = new Date()
      const delay = scheduledFor.getTime() - now.getTime()
      
      if (delay < 0) {
        console.warn('Tentativa de agendar notificação no passado')
        return null
      }
      
      // Criar notificação
      const notification = {
        id: generateNotificationId(),
        title,
        body,
        scheduledFor,
        data,
        actions,
        status: 'scheduled'
      }
      
      // Se for para agora, mostrar imediatamente
      if (delay < 1000) {
        showNotification(notification)
      } else {
        // Agendar para mais tarde
        notification.timeoutId = setTimeout(() => {
          if (shouldShowNotification(notification)) {
            showNotification(notification)
          }
        }, delay)
        
        notifications.value.push(notification)
      }
      
      // Salvar no store
      await store.dispatch('notifications/scheduleNotification', notification)
      
      return notification.id
    } catch (error) {
      console.error('Error scheduling notification:', error)
      return null
    }
  }
  
  // Mostrar notificação
  const showNotification = async (notificationData) => {
    if (permission.value !== 'granted') return
    
    try {
      // Criar notificação nativa
      const notification = new Notification(notificationData.title, {
        body: notificationData.body,
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        tag: notificationData.id,
        data: notificationData.data,
        requireInteraction: notificationData.requireInteraction || false,
        actions: notificationData.actions || []
      })
      
      // Tocar som se habilitado
      if (soundEnabled.value) {
        playNotificationSound()
      }
      
      // Handlers de eventos
      notification.onclick = (event) => {
        event.preventDefault()
        handleNotificationClick(notificationData)
        notification.close()
      }
      
      notification.onclose = () => {
        updateNotificationStatus(notificationData.id, 'closed')
      }
      
      notification.onerror = (error) => {
        console.error('Notification error:', error)
        updateNotificationStatus(notificationData.id, 'error')
      }
      
      // Atualizar status
      updateNotificationStatus(notificationData.id, 'shown')
      
      // Auto-fechar após 10 segundos (se não requerer interação)
      if (!notificationData.requireInteraction) {
        setTimeout(() => notification.close(), 10000)
      }
      
    } catch (error) {
      console.error('Error showing notification:', error)
    }
  }
  
  // Cancelar notificação agendada
  const cancelNotification = (notificationId) => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    
    if (index !== -1) {
      const notification = notifications.value[index]
      
      // Cancelar timeout se existir
      if (notification.timeoutId) {
        clearTimeout(notification.timeoutId)
      }
      
      // Remover da lista
      notifications.value.splice(index, 1)
      
      // Atualizar store
      store.dispatch('notifications/cancelNotification', notificationId)
    }
  }
  
  // Sistema de notificações inteligentes
  const shouldShowNotification = (notification) => {
    if (!smartScheduling.value) return true
    
    const now = new Date()
    const hour = now.getHours()
    
    // Não notificar durante horário de sono (23h - 7h)
    if (hour >= 23 || hour < 7) {
      // Reagendar para 8h
      const tomorrow = new Date(now)
      tomorrow.setDate(tomorrow.getDate() + (hour >= 23 ? 1 : 0))
      tomorrow.setHours(8, 0, 0, 0)
      
      rescheduleNotification(notification, tomorrow)
      return false
    }
    
    // Verificar se usuário está em sessão de estudo
    const isStudying = store.getters['study/isInSession']
    if (isStudying && notification.data.type !== 'urgent') {
      // Adiar por 30 minutos
      const later = new Date(now.getTime() + 30 * 60000)
      rescheduleNotification(notification, later)
      return false
    }
    
    return true
  }
  
  // Reagendar notificação
  const rescheduleNotification = (notification, newTime) => {
    cancelNotification(notification.id)
    
    scheduleNotification({
      ...notification,
      scheduledFor: newTime
    })
  }
  
  // Notificações pré-definidas
  const notificationTemplates = {
    reviewReminder: (subject, time) => ({
      title: '🧠 Hora da Revisão!',
      body: `É hora de revisar: ${subject}`,
      scheduledFor: time,
      data: { type: 'review', subject },
      actions: [
        { action: 'start', title: 'Iniciar Revisão' },
        { action: 'postpone', title: 'Adiar 30min' }
      ]
    }),
    
    streakReminder: () => ({
      title: '🔥 Mantenha sua Sequência!',
      body: 'Não esqueça de estudar hoje para manter sua sequência',
      scheduledFor: getTodayAt(20, 0), // 20h
      data: { type: 'streak' }
    }),
    
    achievementUnlocked: (achievement) => ({
      title: '🏆 Conquista Desbloqueada!',
      body: achievement.title,
      scheduledFor: new Date(),
      data: { type: 'achievement', achievementId: achievement.id },
      requireInteraction: true
    }),
    
    studyBreak: (duration = 15) => ({
      title: '☕ Hora da Pausa',
      body: `Você está estudando há muito tempo. Faça uma pausa de ${duration} minutos`,
      scheduledFor: new Date(),
      data: { type: 'break', duration },
      actions: [
        { action: 'start-break', title: 'Iniciar Pausa' },
        { action: 'skip', title: 'Continuar Estudando' }
      ]
    }),
    
    dailySummary: (stats) => ({
      title: '📊 Resumo Diário',
      body: `Você estudou ${stats.hours}h e completou ${stats.reviews} revisões hoje!`,
      scheduledFor: getTodayAt(22, 0), // 22h
      data: { type: 'summary', stats }
    })
  }
  
  // Agendar notificações recorrentes
  const scheduleRecurringNotifications = () => {
    // Lembrete de sequência diário
    if (store.getters['gamification/currentStreak'] > 0) {
      const reminder = notificationTemplates.streakReminder()
      scheduleNotification(reminder)
    }
    
    // Resumo diário
    const todayStats = store.getters['study/todayStats']
    if (todayStats.hours > 0) {
      const summary = notificationTemplates.dailySummary(todayStats)
      scheduleNotification(summary)
    }
  }
  
  // Handlers
  const handleNotificationClick = (notification) => {
    const { type, ...data } = notification.data
    
    switch (type) {
      case 'review':
        // Navegar para página de revisão
        window.location.href = `/revision-scheduler?subject=${data.subject}`
        break
        
      case 'achievement':
        // Mostrar modal de conquista
        store.dispatch('ui/showAchievementModal', data.achievementId)
        break
        
      case 'streak':
        // Navegar para dashboard
        window.location.href = '/'
        break
        
      default:
        console.log('Notification clicked:', notification)
    }
  }
  
  const playNotificationSound = () => {
    try {
      const audio = new Audio('/sounds/notification.mp3')
      audio.volume = 0.5
      audio.play()
    } catch (error) {
      console.error('Error playing notification sound:', error)
    }
  }
  
  // Helpers
  const generateNotificationId = () => {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  const updateNotificationStatus = (id, status) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.status = status
      store.dispatch('notifications/updateNotificationStatus', { id, status })
    }
  }
  
  const getTodayAt = (hour, minute) => {
    const date = new Date()
    date.setHours(hour, minute, 0, 0)
    
    // Se já passou, agendar para amanhã
    if (date < new Date()) {
      date.setDate(date.getDate() + 1)
    }
    
    return date
  }
  
  // Análise de engajamento
  const getNotificationStats = () => {
    const shown = notifications.value.filter(n => n.status === 'shown').length
    const clicked = notifications.value.filter(n => n.status === 'clicked').length
    const scheduled = notifications.value.filter(n => n.status === 'scheduled').length
    
    return {
      total: notifications.value.length,
      shown,
      clicked,
      scheduled,
      clickRate: shown > 0 ? (clicked / shown * 100).toFixed(1) : 0
    }
  }
  
  // Configurações
  const updateSettings = (settings) => {
    if (settings.soundEnabled !== undefined) {
      soundEnabled.value = settings.soundEnabled
    }
    
    if (settings.smartScheduling !== undefined) {
      smartScheduling.value = settings.smartScheduling
    }
    
    store.dispatch('notifications/updateSettings', settings)
  }
  
  return {
    // Estado
    notifications,
    permission,
    soundEnabled,
    smartScheduling,
    
    // Métodos principais
    initializeNotifications,
    scheduleNotification,
    cancelNotification,
    
    // Templates
    notificationTemplates,
    
    // Utilidades
    scheduleRecurringNotifications,
    getNotificationStats,
    updateSettings
  }
}