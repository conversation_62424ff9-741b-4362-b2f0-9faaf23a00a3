// Quick Fix Utility
// Apply this to any component experiencing Chart.js or lifecycle errors

export const QuickFixMixin = {
  data() {
    return {
      _quickFixMounted: false,
      _quickFixDestroyed: false,
      _quickFixTimeouts: []
    }
  },
  
  mounted() {
    this._quickFixMounted = true
  },
  
  beforeUnmount() {
    this._quickFixDestroyed = true
    this._quickFixTimeouts.forEach(id => clearTimeout(id))
    this._quickFixTimeouts = []
  },
  
  methods: {
    // Safe chart creation
    $safeCreateChart(canvasRef, chartKey, createFn) {
      if (this._quickFixDestroyed) return
      
      // Get canvas element
      let canvas = this.$refs[canvasRef]
      
      // Handle array refs
      if (Array.isArray(canvas)) {
        canvas = canvas[0]
      }
      
      if (!canvas || !canvas.getContext) {
        console.warn(`Canvas ${canvasRef} not ready`)
        return
      }
      
      try {
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          console.error(`Could not get 2D context for ${canvasRef}`)
          return
        }
        
        // Destroy existing chart
        if (this.charts && this.charts[chartKey]) {
          try {
            this.charts[chartKey].destroy()
          } catch (e) {
            console.warn('Error destroying chart:', e)
          }
          this.charts[chartKey] = null
        }
        
        // Create new chart
        const chart = createFn(ctx)
        if (this.charts) {
          this.charts[chartKey] = chart
        }
        
        return chart
      } catch (error) {
        console.error(`Error creating chart ${chartKey}:`, error)
        return null
      }
    },
    
    // Safe timeout
    $safeTimeout(fn, delay) {
      if (this._quickFixDestroyed) return
      
      const id = setTimeout(() => {
        if (!this._quickFixDestroyed) {
          try {
            fn()
          } catch (error) {
            console.error('Timeout error:', error)
          }
        }
      }, delay)
      
      this._quickFixTimeouts.push(id)
      return id
    },
    
    // Safe chart update
    $safeUpdateCharts() {
      if (!this._quickFixMounted || this._quickFixDestroyed) {
        console.log('Component not ready for chart updates')
        return
      }
      
      // Destroy all charts safely
      if (this.charts) {
        Object.keys(this.charts).forEach(key => {
          if (this.charts[key]) {
            try {
              this.charts[key].destroy()
            } catch (e) {
              console.warn(`Error destroying chart ${key}:`, e)
            }
            this.charts[key] = null
          }
        })
      }
      
      // Wait before creating new charts
      this.$safeTimeout(() => {
        if (this.updateCharts) {
          this.updateCharts()
        }
      }, 200)
    }
  }
}

// Apply Quick Fix to existing component
export function applyQuickFix(component) {
  // Backup original methods
  const originalMounted = component.mounted
  const originalBeforeUnmount = component.beforeUnmount || component.beforeDestroy
  const originalMethods = component.methods || {}
  
  // Apply mixin
  component.mixins = component.mixins || []
  component.mixins.push(QuickFixMixin)
  
  // Wrap mounted
  component.mounted = function() {
    console.log('QuickFix: Wrapping mounted hook')
    
    // Call mixin mounted first
    QuickFixMixin.mounted.call(this)
    
    // Delay original mounted
    this.$safeTimeout(() => {
      if (originalMounted) {
        originalMounted.call(this)
      }
    }, 300)
  }
  
  // Wrap beforeUnmount
  component.beforeUnmount = function() {
    console.log('QuickFix: Wrapping beforeUnmount hook')
    
    // Call mixin cleanup first
    QuickFixMixin.beforeUnmount.call(this)
    
    // Then original
    if (originalBeforeUnmount) {
      originalBeforeUnmount.call(this)
    }
  }
  
  // Enhance updateCharts if exists
  if (originalMethods.updateCharts) {
    const originalUpdateCharts = originalMethods.updateCharts
    
    component.methods.updateCharts = function() {
      console.log('QuickFix: Wrapping updateCharts')
      
      if (!this._quickFixMounted || this._quickFixDestroyed) {
        console.log('QuickFix: Skipping chart update - component not ready')
        return
      }
      
      // Use safe update
      this.$safeUpdateCharts()
    }
  }
  
  return component
}

// Usage example:
// import { applyQuickFix } from '@/utils/quickFix'
// export default applyQuickFix({
//   name: 'MyComponent',
//   // ... rest of component
// })