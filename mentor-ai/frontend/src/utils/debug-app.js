// Debug script to check if app is mounting correctly
console.log('=== APP DEBUG ===');
console.log('App mounting initiated');

// Check if app element exists
const appElement = document.getElementById('app');
if (appElement) {
  console.log('✓ App element found:', appElement);
  console.log('App element innerHTML:', appElement.innerHTML.substring(0, 200) + '...');
} else {
  console.error('✗ App element not found!');
}

// Check Vue instance
window.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded');
  setTimeout(() => {
    const vueApp = document.querySelector('#app').__vue_app__;
    if (vueApp) {
      console.log('✓ Vue app instance found');
    } else {
      console.error('✗ Vue app instance not found');
    }
  }, 1000);
});

// Check for any display:none or visibility issues
if (appElement) {
  const computedStyle = window.getComputedStyle(appElement);
  console.log('App element styles:', {
    display: computedStyle.display,
    visibility: computedStyle.visibility,
    opacity: computedStyle.opacity,
    width: computedStyle.width,
    height: computedStyle.height
  });
}

export default {
  debugApp() {
    console.log('Debug function called');
  }
};