// Emergency Purple Screen Fix
// This runs immediately to prevent any purple overlays

export function applyEmergencyPurpleFix() {
  console.log('Applying emergency purple screen fix...')
  
  // Inject critical CSS immediately
  const style = document.createElement('style')
  style.id = 'emergency-purple-fix'
  style.innerHTML = `
    /* EMERGENCY: Kill all purple overlays */
    
    /* Remove all gradient backgrounds */
    * {
      background-image: none !important;
    }
    
    /* Force transparency on suspicious elements */
    [style*="gradient"],
    [style*="#764ba2"],
    [style*="purple"],
    [class*="overlay"] {
      background: transparent !important;
      display: none !important;
    }
    
    /* Ensure content is visible */
    .performance-dashboard,
    .performance-dashboard * {
      opacity: 1 !important;
      visibility: visible !important;
      filter: none !important;
      isolation: isolate !important;
    }
    
    /* Kill any full-screen overlays */
    body > div[style*="position: fixed"],
    body > div[style*="position: absolute"] {
      pointer-events: none !important;
      background: transparent !important;
    }
    
    /* Force main content on top */
    .performance-dashboard {
      position: relative !important;
      z-index: 10000 !important;
      background: white !important;
    }
    
    /* Remove pseudo-element overlays */
    *::before,
    *::after {
      background-image: none !important;
      opacity: 1 !important;
      z-index: -1 !important;
    }
    
    /* White background for all cards */
    .card,
    .dashboard-header,
    .overview-section,
    .content-grid {
      background-color: white !important;
      position: relative !important;
      z-index: 100 !important;
    }
  `
  
  // Insert at the very beginning of head to override everything
  const head = document.head || document.getElementsByTagName('head')[0]
  if (head.firstChild) {
    head.insertBefore(style, head.firstChild)
  } else {
    head.appendChild(style)
  }
  
  // DOM cleanup function
  const cleanupDOM = () => {
    // Find all elements with suspicious styles
    const suspiciousElements = document.querySelectorAll([
      '[style*="gradient"]',
      '[style*="#764ba2"]',
      '[style*="purple"]',
      '[style*="118, 75, 162"]',
      '.overlay',
      '[class*="overlay"]'
    ].join(','))
    
    suspiciousElements.forEach(el => {
      const rect = el.getBoundingClientRect()
      const styles = window.getComputedStyle(el)
      
      // Check if it's a large overlay
      if (rect.width > window.innerWidth * 0.5 && rect.height > window.innerHeight * 0.5) {
        console.warn('Removing suspicious overlay:', el)
        el.remove()
      } else if (styles.background.includes('gradient') || styles.background.includes('purple')) {
        el.style.cssText += '; background: transparent !important;'
      }
    })
    
    // Ensure no position:fixed overlays
    document.querySelectorAll('[style*="position: fixed"]').forEach(el => {
      const rect = el.getBoundingClientRect()
      if (rect.width >= window.innerWidth && rect.height >= window.innerHeight) {
        console.warn('Removing full-screen fixed element:', el)
        el.remove()
      }
    })
  }
  
  // Run cleanup immediately
  cleanupDOM()
  
  // Run cleanup after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', cleanupDOM)
  }
  
  // Run cleanup periodically for first 5 seconds
  let cleanupCount = 0
  const cleanupInterval = setInterval(() => {
    cleanupDOM()
    cleanupCount++
    if (cleanupCount > 10) {
      clearInterval(cleanupInterval)
    }
  }, 500)
  
  console.log('Emergency purple screen fix applied!')
}

// Auto-apply on import
applyEmergencyPurpleFix()

// Export for manual use
export default applyEmergencyPurpleFix