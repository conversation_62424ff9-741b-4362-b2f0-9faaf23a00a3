// Chart.js-like implementation for RevisionSystemUltra
export class Chart {
  constructor(canvas, config) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')
    this.config = config
    this.animationId = null
    
    // Set canvas size
    this.resize()
    window.addEventListener('resize', () => this.resize())
    
    // Initialize chart
    this.init()
  }
  
  resize() {
    const rect = this.canvas.getBoundingClientRect()
    this.canvas.width = rect.width * window.devicePixelRatio
    this.canvas.height = rect.height * window.devicePixelRatio
    this.ctx.scale(window.devicePixelRatio, window.devicePixelRatio)
  }
  
  init() {
    if (this.config.type === 'doughnut') {
      this.drawDoughnutChart()
    } else if (this.config.type === 'line') {
      this.drawLineChart()
    } else if (this.config.type === 'bar') {
      this.drawBarChart()
    }
  }
  
  drawDoughnutChart() {
    const centerX = this.canvas.width / (2 * window.devicePixelRatio)
    const centerY = this.canvas.height / (2 * window.devicePixelRatio)
    const radius = Math.min(centerX, centerY) - 20
    const innerRadius = radius * 0.6
    
    const data = this.config.data.datasets[0].data
    const colors = this.config.data.datasets[0].backgroundColor
    const total = data.reduce((a, b) => a + b, 0)
    
    let currentAngle = -Math.PI / 2
    
    data.forEach((value, index) => {
      const sliceAngle = (value / total) * 2 * Math.PI
      
      // Draw slice
      this.ctx.beginPath()
      this.ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
      this.ctx.arc(centerX, centerY, innerRadius, currentAngle + sliceAngle, currentAngle, true)
      this.ctx.closePath()
      this.ctx.fillStyle = colors[index]
      this.ctx.fill()
      
      currentAngle += sliceAngle
    })
    
    // Draw center text if specified
    if (this.config.options?.plugins?.center?.text) {
      this.ctx.fillStyle = '#f1f5f9'
      this.ctx.font = 'bold 24px Inter'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText(this.config.options.plugins.center.text, centerX, centerY)
    }
  }
  
  drawLineChart() {
    const padding = 40
    const width = this.canvas.width / window.devicePixelRatio - padding * 2
    const height = this.canvas.height / window.devicePixelRatio - padding * 2
    
    const datasets = this.config.data.datasets
    const labels = this.config.data.labels
    const maxValue = Math.max(...datasets.flatMap(d => d.data))
    const minValue = Math.min(...datasets.flatMap(d => d.data))
    
    // Draw grid
    this.ctx.strokeStyle = 'rgba(148, 163, 184, 0.1)'
    this.ctx.lineWidth = 1
    
    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = padding + (height * i) / 5
      this.ctx.beginPath()
      this.ctx.moveTo(padding, y)
      this.ctx.lineTo(padding + width, y)
      this.ctx.stroke()
    }
    
    // Draw datasets
    datasets.forEach((dataset, datasetIndex) => {
      const points = dataset.data.map((value, index) => ({
        x: padding + (width * index) / (labels.length - 1),
        y: padding + height - ((value - minValue) / (maxValue - minValue)) * height
      }))
      
      // Draw line
      this.ctx.beginPath()
      this.ctx.strokeStyle = dataset.borderColor
      this.ctx.lineWidth = 3
      this.ctx.lineJoin = 'round'
      
      points.forEach((point, index) => {
        if (index === 0) {
          this.ctx.moveTo(point.x, point.y)
        } else {
          this.ctx.lineTo(point.x, point.y)
        }
      })
      
      this.ctx.stroke()
      
      // Draw points
      points.forEach(point => {
        this.ctx.beginPath()
        this.ctx.arc(point.x, point.y, 4, 0, Math.PI * 2)
        this.ctx.fillStyle = dataset.backgroundColor
        this.ctx.fill()
        this.ctx.strokeStyle = dataset.borderColor
        this.ctx.stroke()
      })
    })
  }
  
  drawBarChart() {
    const padding = 40
    const width = this.canvas.width / window.devicePixelRatio - padding * 2
    const height = this.canvas.height / window.devicePixelRatio - padding * 2
    
    const data = this.config.data.datasets[0].data
    const labels = this.config.data.labels
    const colors = this.config.data.datasets[0].backgroundColor
    const maxValue = Math.max(...data)
    
    const barWidth = width / (data.length * 1.5)
    const spacing = barWidth * 0.5
    
    data.forEach((value, index) => {
      const barHeight = (value / maxValue) * height
      const x = padding + index * (barWidth + spacing)
      const y = padding + height - barHeight
      
      // Draw bar
      this.ctx.fillStyle = colors[index] || colors
      this.ctx.fillRect(x, y, barWidth, barHeight)
      
      // Draw value on top
      this.ctx.fillStyle = '#f1f5f9'
      this.ctx.font = '12px Inter'
      this.ctx.textAlign = 'center'
      this.ctx.fillText(value, x + barWidth / 2, y - 5)
      
      // Draw label
      this.ctx.fillStyle = '#94a3b8'
      this.ctx.font = '11px Inter'
      this.ctx.fillText(labels[index], x + barWidth / 2, padding + height + 15)
    })
  }
  
  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
  }
}

// 3D Visualization utilities
export class NeuralNetwork3D {
  constructor(canvas) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')
    this.nodes = []
    this.connections = []
    this.rotation = { x: 0, y: 0, z: 0 }
    this.animationId = null
    
    this.init()
  }
  
  init() {
    // Create nodes in 3D space
    const layers = [5, 8, 6, 4]
    const layerSpacing = 200
    
    layers.forEach((nodeCount, layerIndex) => {
      for (let i = 0; i < nodeCount; i++) {
        const angle = (i / nodeCount) * Math.PI * 2
        const radius = 100
        
        this.nodes.push({
          x: layerIndex * layerSpacing - (layers.length * layerSpacing) / 2,
          y: Math.sin(angle) * radius,
          z: Math.cos(angle) * radius,
          layer: layerIndex,
          activation: Math.random()
        })
      }
    })
    
    // Create connections
    for (let i = 0; i < layers.length - 1; i++) {
      const currentLayer = this.nodes.filter(n => n.layer === i)
      const nextLayer = this.nodes.filter(n => n.layer === i + 1)
      
      currentLayer.forEach(node1 => {
        nextLayer.forEach(node2 => {
          if (Math.random() > 0.3) {
            this.connections.push({
              from: node1,
              to: node2,
              weight: Math.random()
            })
          }
        })
      })
    }
    
    this.animate()
  }
  
  project3D(point) {
    // Simple 3D to 2D projection
    const distance = 800
    const centerX = this.canvas.width / 2
    const centerY = this.canvas.height / 2
    
    // Apply rotation
    const cos = Math.cos
    const sin = Math.sin
    
    // Rotate around Y axis
    const x1 = point.x * cos(this.rotation.y) - point.z * sin(this.rotation.y)
    const z1 = point.x * sin(this.rotation.y) + point.z * cos(this.rotation.y)
    
    // Rotate around X axis
    const y2 = point.y * cos(this.rotation.x) - z1 * sin(this.rotation.x)
    const z2 = point.y * sin(this.rotation.x) + z1 * cos(this.rotation.x)
    
    // Project to 2D
    const scale = distance / (distance + z2)
    const x2d = centerX + x1 * scale
    const y2d = centerY + y2 * scale
    
    return { x: x2d, y: y2d, scale }
  }
  
  animate() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
    
    // Update rotation
    this.rotation.y += 0.005
    this.rotation.x = Math.sin(Date.now() * 0.0001) * 0.3
    
    // Draw connections
    this.connections.forEach(conn => {
      const from2d = this.project3D(conn.from)
      const to2d = this.project3D(conn.to)
      
      this.ctx.beginPath()
      this.ctx.moveTo(from2d.x, from2d.y)
      this.ctx.lineTo(to2d.x, to2d.y)
      this.ctx.strokeStyle = `rgba(99, 102, 241, ${conn.weight * 0.3})`
      this.ctx.lineWidth = conn.weight * 2
      this.ctx.stroke()
    })
    
    // Draw nodes
    this.nodes.forEach(node => {
      const projected = this.project3D(node)
      
      // Pulsing activation
      node.activation = Math.sin(Date.now() * 0.001 + node.x) * 0.5 + 0.5
      
      this.ctx.beginPath()
      this.ctx.arc(projected.x, projected.y, 8 * projected.scale, 0, Math.PI * 2)
      this.ctx.fillStyle = `rgba(139, 92, 246, ${node.activation})`
      this.ctx.fill()
      
      this.ctx.strokeStyle = 'rgba(99, 102, 241, 0.8)'
      this.ctx.lineWidth = 2
      this.ctx.stroke()
    })
    
    this.animationId = requestAnimationFrame(() => this.animate())
  }
  
  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
  }
}

// Schedule Visualization
export class ScheduleVisualization {
  constructor(canvas) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')
    this.revisions = []
    this.zoom = 1
    this.offset = { x: 0, y: 0 }
    
    this.init()
  }
  
  init() {
    this.canvas.width = this.canvas.offsetWidth
    this.canvas.height = this.canvas.offsetHeight
    
    // Add mouse interactions
    this.setupInteractions()
  }
  
  setupInteractions() {
    let isDragging = false
    let lastPos = { x: 0, y: 0 }
    
    this.canvas.addEventListener('mousedown', (e) => {
      isDragging = true
      lastPos = { x: e.clientX, y: e.clientY }
    })
    
    this.canvas.addEventListener('mousemove', (e) => {
      if (isDragging) {
        this.offset.x += e.clientX - lastPos.x
        this.offset.y += e.clientY - lastPos.y
        lastPos = { x: e.clientX, y: e.clientY }
        this.render()
      }
    })
    
    this.canvas.addEventListener('mouseup', () => {
      isDragging = false
    })
    
    this.canvas.addEventListener('wheel', (e) => {
      e.preventDefault()
      const delta = e.deltaY * -0.001
      this.zoom = Math.max(0.5, Math.min(3, this.zoom + delta))
      this.render()
    })
  }
  
  addRevisions(revisions) {
    this.revisions = revisions
    this.render()
  }
  
  render() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
    
    const centerX = this.canvas.width / 2 + this.offset.x
    const centerY = this.canvas.height / 2 + this.offset.y
    
    // Draw timeline
    this.ctx.strokeStyle = 'rgba(99, 102, 241, 0.3)'
    this.ctx.lineWidth = 2
    this.ctx.beginPath()
    this.ctx.moveTo(50, centerY)
    this.ctx.lineTo(this.canvas.width - 50, centerY)
    this.ctx.stroke()
    
    // Draw revisions
    this.revisions.forEach((revision, index) => {
      const x = centerX + index * 150 * this.zoom
      const y = centerY
      
      // Draw connection to timeline
      this.ctx.beginPath()
      this.ctx.moveTo(x, y)
      this.ctx.lineTo(x, y - 50)
      this.ctx.strokeStyle = 'rgba(99, 102, 241, 0.5)'
      this.ctx.stroke()
      
      // Draw revision node
      const radius = 30 * this.zoom
      this.ctx.beginPath()
      this.ctx.arc(x, y - 80, radius, 0, Math.PI * 2)
      
      // Gradient fill
      const gradient = this.ctx.createRadialGradient(x, y - 80, 0, x, y - 80, radius)
      gradient.addColorStop(0, 'rgba(139, 92, 246, 0.8)')
      gradient.addColorStop(1, 'rgba(99, 102, 241, 0.4)')
      this.ctx.fillStyle = gradient
      this.ctx.fill()
      
      // Draw text
      this.ctx.fillStyle = '#f1f5f9'
      this.ctx.font = `${12 * this.zoom}px Inter`
      this.ctx.textAlign = 'center'
      this.ctx.fillText(revision.title, x, y - 80)
      
      // Draw date
      this.ctx.fillStyle = '#94a3b8'
      this.ctx.font = `${10 * this.zoom}px Inter`
      this.ctx.fillText(revision.date, x, y - 110)
    })
  }
  
  zoomIn() {
    this.zoom = Math.min(3, this.zoom * 1.2)
    this.render()
  }
  
  zoomOut() {
    this.zoom = Math.max(0.5, this.zoom / 1.2)
    this.render()
  }
  
  resetView() {
    this.zoom = 1
    this.offset = { x: 0, y: 0 }
    this.render()
  }
}

// Heatmap Generator
export function generateHeatmapData() {
  const data = []
  for (let week = 0; week < 52; week++) {
    for (let day = 0; day < 7; day++) {
      data.push({
        week,
        day,
        intensity: Math.floor(Math.random() * 5),
        date: new Date(2024, 0, 1 + week * 7 + day)
      })
    }
  }
  return data
}

// Performance Metrics Calculator
export function calculatePerformanceMetrics(sessions) {
  const accuracy = sessions.reduce((sum, s) => sum + s.accuracy, 0) / sessions.length
  const speed = sessions.reduce((sum, s) => sum + s.speed, 0) / sessions.length
  const consistency = calculateConsistency(sessions.map(s => s.score))
  const focusTime = sessions.reduce((sum, s) => sum + s.duration, 0) / sessions.length
  
  return {
    accuracy: Math.round(accuracy),
    speed: Math.round(speed * 10) / 10,
    consistency: Math.round(consistency),
    focusTime: Math.round(focusTime)
  }
}

function calculateConsistency(scores) {
  if (scores.length < 2) return 100
  
  const mean = scores.reduce((a, b) => a + b, 0) / scores.length
  const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length
  const stdDev = Math.sqrt(variance)
  
  return Math.max(0, 100 - (stdDev / mean) * 100)
}

// Export all utilities
export default {
  Chart,
  NeuralNetwork3D,
  ScheduleVisualization,
  generateHeatmapData,
  calculatePerformanceMetrics
}