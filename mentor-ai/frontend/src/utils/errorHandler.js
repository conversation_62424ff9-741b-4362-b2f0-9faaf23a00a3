// Global Error Handler for Vue Application
// This module provides comprehensive error handling across the entire application

import { h } from 'vue'

class ErrorHandler {
  constructor() {
    this.errors = []
    this.maxErrors = 50
    this.errorCallbacks = []
  }

  // Initialize global error handlers
  init(app, router) {
    this.app = app
    this.router = router
    
    // Vue error handler
    app.config.errorHandler = (err, instance, info) => {
      this.handleVueError(err, instance, info)
    }
    
    // Vue warning handler (development only)
    if (process.env.NODE_ENV !== 'production') {
      app.config.warnHandler = (msg, instance, trace) => {
        console.warn('Vue Warning:', msg, trace)
      }
    }
    
    // Global unhandled promise rejection
    window.addEventListener('unhandledrejection', event => {
      this.handlePromiseRejection(event)
    })
    
    // Global error event
    window.addEventListener('error', event => {
      this.handleGlobalError(event)
    })
    
    // Router error handler
    router.onError((error) => {
      this.handleRouterError(error)
    })
  }
  
  // Handle Vue component errors
  handleVueError(err, instance, info) {
    const error = {
      type: 'vue-error',
      message: err.message,
      stack: err.stack,
      component: instance?.$options.name || 'Unknown',
      info: info,
      timestamp: new Date().toISOString()
    }
    
    this.logError(error)
    
    // Special handling for common errors
    if (err.message.includes('Cannot read properties of null')) {
      this.handleNullError(err, instance)
    } else if (err.message.includes('Chart')) {
      this.handleChartError(err, instance)
    }
  }
  
  // Handle null reference errors
  handleNullError(err, instance) {
    console.error('Null reference error detected:', err.message)
    
    // Try to recover by re-initializing the component
    if (instance && instance.$options.name) {
      console.log(`Attempting to recover component: ${instance.$options.name}`)
      
      // Force update after next tick
      instance.$nextTick(() => {
        instance.$forceUpdate()
      })
    }
  }
  
  // Handle Chart.js specific errors
  handleChartError(err, instance) {
    console.error('Chart error detected:', err.message)
    
    // If it's a chart component, try to reinitialize charts
    if (instance && instance.charts) {
      console.log('Attempting to reinitialize charts...')
      
      // Destroy all existing charts
      Object.keys(instance.charts).forEach(key => {
        try {
          if (instance.charts[key]) {
            instance.charts[key].destroy()
            instance.charts[key] = null
          }
        } catch (e) {
          console.error('Error destroying chart:', e)
        }
      })
      
      // Clear charts object
      instance.charts = {}
      
      // Try to recreate charts after a delay
      setTimeout(() => {
        if (instance.updateCharts) {
          instance.updateCharts()
        }
      }, 500)
    }
  }
  
  // Handle promise rejections
  handlePromiseRejection(event) {
    const error = {
      type: 'unhandled-promise',
      message: event.reason?.message || event.reason,
      promise: event.promise,
      timestamp: new Date().toISOString()
    }
    
    this.logError(error)
    
    // Prevent default browser behavior
    event.preventDefault()
  }
  
  // Handle global errors
  handleGlobalError(event) {
    const error = {
      type: 'global-error',
      message: event.message,
      filename: event.filename,
      line: event.lineno,
      column: event.colno,
      error: event.error,
      timestamp: new Date().toISOString()
    }
    
    this.logError(error)
    
    // Prevent default browser behavior
    event.preventDefault()
  }
  
  // Handle router errors
  handleRouterError(error) {
    const routeError = {
      type: 'router-error',
      message: error.message,
      stack: error.stack,
      from: this.router.currentRoute.value.path,
      timestamp: new Date().toISOString()
    }
    
    this.logError(routeError)
    
    // Redirect to a safe route if necessary
    if (error.message.includes('Failed to fetch dynamically imported module')) {
      console.log('Module loading error, refreshing page...')
      window.location.reload()
    }
  }
  
  // Log error and notify callbacks
  logError(error) {
    // Add to errors array (with size limit)
    this.errors.unshift(error)
    if (this.errors.length > this.maxErrors) {
      this.errors.pop()
    }
    
    // Log to console in development
    if (process.env.NODE_ENV !== 'production') {
      console.error('[ErrorHandler]', error)
    }
    
    // Notify all registered callbacks
    this.errorCallbacks.forEach(callback => {
      try {
        callback(error)
      } catch (e) {
        console.error('Error in error callback:', e)
      }
    })
    
    // Send to error tracking service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToErrorTracking(error)
    }
  }
  
  // Register error callback
  onError(callback) {
    this.errorCallbacks.push(callback)
    
    // Return unsubscribe function
    return () => {
      const index = this.errorCallbacks.indexOf(callback)
      if (index > -1) {
        this.errorCallbacks.splice(index, 1)
      }
    }
  }
  
  // Get recent errors
  getRecentErrors() {
    return [...this.errors]
  }
  
  // Clear error history
  clearErrors() {
    this.errors = []
  }
  
  // Send to error tracking service (placeholder)
  sendToErrorTracking(error) {
    // Implement your error tracking service integration here
    // e.g., Sentry, Rollbar, etc.
  }
}

// Create singleton instance
const errorHandler = new ErrorHandler()

// Vue plugin
export const ErrorHandlerPlugin = {
  install(app, options = {}) {
    // Make error handler available globally
    app.config.globalProperties.$errorHandler = errorHandler
    app.provide('errorHandler', errorHandler)
    
    // Initialize with app and router
    if (options.router) {
      errorHandler.init(app, options.router)
    }
    
    // Global error boundary component
    app.component('ErrorBoundary', {
      name: 'ErrorBoundary',
      props: {
        fallback: {
          type: String,
          default: 'Ocorreu um erro. Por favor, recarregue a página.'
        }
      },
      data() {
        return {
          hasError: false,
          error: null
        }
      },
      errorCaptured(err, instance, info) {
        this.hasError = true
        this.error = err
        errorHandler.handleVueError(err, instance, info)
        return false // Prevent error propagation
      },
      render() {
        if (this.hasError) {
          return h('div', {
            class: 'error-boundary-fallback',
            style: {
              padding: '20px',
              margin: '20px',
              backgroundColor: '#fee',
              border: '1px solid #fcc',
              borderRadius: '8px',
              textAlign: 'center'
            }
          }, [
            h('h3', '⚠️ Erro'),
            h('p', this.fallback),
            h('button', {
              onClick: () => {
                this.hasError = false
                this.error = null
                this.$forceUpdate()
              },
              style: {
                marginTop: '10px',
                padding: '8px 16px',
                backgroundColor: '#667eea',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }
            }, 'Tentar Novamente')
          ])
        }
        return this.$slots.default()
      }
    })
  }
}

export default errorHandler