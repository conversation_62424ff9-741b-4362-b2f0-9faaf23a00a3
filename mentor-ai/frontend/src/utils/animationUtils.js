// Advanced Animation Utilities for RevisionSystemUltra

// GSAP-like animation engine
export class AnimationEngine {
  constructor() {
    this.animations = new Map()
    this.rafId = null
    this.startTime = null
  }

  // Animate element properties
  to(element, duration, properties) {
    const animation = {
      element,
      duration: duration * 1000, // Convert to milliseconds
      properties,
      startValues: {},
      endValues: {},
      easing: properties.ease || 'easeInOut',
      onComplete: properties.onComplete,
      startTime: null
    }

    // Get initial values
    Object.keys(properties).forEach(prop => {
      if (prop === 'ease' || prop === 'onComplete') return
      
      if (prop === 'transform') {
        // Handle transform properties
        animation.startValues[prop] = this.getTransform(element)
        animation.endValues[prop] = properties[prop]
      } else {
        const currentValue = parseFloat(getComputedStyle(element)[prop]) || 0
        animation.startValues[prop] = currentValue
        animation.endValues[prop] = parseFloat(properties[prop])
      }
    })

    const id = Symbol()
    this.animations.set(id, animation)
    
    if (!this.rafId) {
      this.startTime = performance.now()
      this.animate()
    }
    
    return id
  }

  // Animation loop
  animate(currentTime) {
    if (!this.startTime) this.startTime = currentTime
    
    this.animations.forEach((animation, id) => {
      if (!animation.startTime) animation.startTime = currentTime
      
      const elapsed = currentTime - animation.startTime
      const progress = Math.min(elapsed / animation.duration, 1)
      const easedProgress = this.getEasing(animation.easing)(progress)
      
      // Update properties
      Object.keys(animation.startValues).forEach(prop => {
        const start = animation.startValues[prop]
        const end = animation.endValues[prop]
        
        if (prop === 'transform') {
          this.updateTransform(animation.element, start, end, easedProgress)
        } else {
          const current = start + (end - start) * easedProgress
          animation.element.style[prop] = current + (prop === 'opacity' ? '' : 'px')
        }
      })
      
      // Check if complete
      if (progress >= 1) {
        this.animations.delete(id)
        if (animation.onComplete) animation.onComplete()
      }
    })
    
    if (this.animations.size > 0) {
      this.rafId = requestAnimationFrame((time) => this.animate(time))
    } else {
      this.rafId = null
    }
  }

  // Easing functions
  getEasing(type) {
    const easings = {
      linear: t => t,
      easeIn: t => t * t,
      easeOut: t => t * (2 - t),
      easeInOut: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
      easeInCubic: t => t * t * t,
      easeOutCubic: t => (--t) * t * t + 1,
      easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
      easeInQuart: t => t * t * t * t,
      easeOutQuart: t => 1 - (--t) * t * t * t,
      easeInOutQuart: t => t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t,
      elastic: t => {
        if (t === 0 || t === 1) return t
        const p = 0.3
        const s = p / 4
        return Math.pow(2, -10 * t) * Math.sin((t - s) * (2 * Math.PI) / p) + 1
      },
      bounce: t => {
        if (t < 1 / 2.75) {
          return 7.5625 * t * t
        } else if (t < 2 / 2.75) {
          return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75
        } else if (t < 2.5 / 2.75) {
          return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375
        } else {
          return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375
        }
      }
    }
    return easings[type] || easings.easeInOut
  }

  // Transform helpers
  getTransform(element) {
    const transform = getComputedStyle(element).transform
    if (transform === 'none') {
      return { translateX: 0, translateY: 0, scale: 1, rotate: 0 }
    }
    // Parse transform matrix
    // Simplified for demonstration
    return { translateX: 0, translateY: 0, scale: 1, rotate: 0 }
  }

  updateTransform(element, start, end, progress) {
    const current = {}
    Object.keys(start).forEach(key => {
      current[key] = start[key] + (end[key] - start[key]) * progress
    })
    
    const transformString = `
      translateX(${current.translateX || 0}px) 
      translateY(${current.translateY || 0}px) 
      scale(${current.scale || 1}) 
      rotate(${current.rotate || 0}deg)
    `
    element.style.transform = transformString
  }
}

// Particle System
export class ParticleSystem {
  constructor(container, options = {}) {
    this.container = container
    this.options = {
      particleCount: options.particleCount || 50,
      colors: options.colors || ['#6366f1', '#8b5cf6', '#a855f7'],
      speed: options.speed || 1,
      size: options.size || { min: 2, max: 6 },
      ...options
    }
    this.particles = []
    this.init()
  }

  init() {
    for (let i = 0; i < this.options.particleCount; i++) {
      this.createParticle()
    }
    this.animate()
  }

  createParticle() {
    const particle = document.createElement('div')
    particle.className = 'animation-particle'
    
    const size = Math.random() * (this.options.size.max - this.options.size.min) + this.options.size.min
    const color = this.options.colors[Math.floor(Math.random() * this.options.colors.length)]
    
    Object.assign(particle.style, {
      position: 'absolute',
      width: size + 'px',
      height: size + 'px',
      backgroundColor: color,
      borderRadius: '50%',
      pointerEvents: 'none',
      left: Math.random() * 100 + '%',
      top: Math.random() * 100 + '%',
      opacity: Math.random() * 0.6 + 0.2
    })
    
    this.container.appendChild(particle)
    
    this.particles.push({
      element: particle,
      x: parseFloat(particle.style.left),
      y: parseFloat(particle.style.top),
      vx: (Math.random() - 0.5) * this.options.speed,
      vy: (Math.random() - 0.5) * this.options.speed,
      size,
      opacity: parseFloat(particle.style.opacity)
    })
  }

  animate() {
    this.particles.forEach(particle => {
      particle.x += particle.vx
      particle.y += particle.vy
      
      // Bounce off edges
      if (particle.x <= 0 || particle.x >= 100) particle.vx *= -1
      if (particle.y <= 0 || particle.y >= 100) particle.vy *= -1
      
      // Update position
      particle.element.style.left = particle.x + '%'
      particle.element.style.top = particle.y + '%'
      
      // Pulsing effect
      const pulse = Math.sin(Date.now() * 0.001) * 0.2 + 0.8
      particle.element.style.opacity = particle.opacity * pulse
    })
    
    requestAnimationFrame(() => this.animate())
  }

  destroy() {
    this.particles.forEach(particle => {
      particle.element.remove()
    })
    this.particles = []
  }
}

// Morphing Text Animation
export class TextMorph {
  constructor(element, texts, options = {}) {
    this.element = element
    this.texts = texts
    this.textIndex = 0
    this.options = {
      duration: options.duration || 2000,
      delay: options.delay || 3000,
      ...options
    }
    this.init()
  }

  init() {
    this.morph()
    setInterval(() => this.morph(), this.options.delay)
  }

  morph() {
    const currentText = this.texts[this.textIndex]
    const nextIndex = (this.textIndex + 1) % this.texts.length
    const nextText = this.texts[nextIndex]
    
    this.animateText(currentText, nextText)
    this.textIndex = nextIndex
  }

  animateText(from, to) {
    const fromChars = from.split('')
    const toChars = to.split('')
    const maxLength = Math.max(fromChars.length, toChars.length)
    const duration = this.options.duration
    const startTime = Date.now()
    
    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      let result = ''
      for (let i = 0; i < maxLength; i++) {
        const fromChar = fromChars[i] || ''
        const toChar = toChars[i] || ''
        
        if (Math.random() < progress) {
          result += toChar
        } else {
          result += fromChar
        }
      }
      
      this.element.textContent = result
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    
    animate()
  }
}

// Ripple Effect
export class RippleEffect {
  constructor(element) {
    this.element = element
    this.init()
  }

  init() {
    this.element.style.position = 'relative'
    this.element.style.overflow = 'hidden'
    
    this.element.addEventListener('click', (e) => this.createRipple(e))
  }

  createRipple(event) {
    const ripple = document.createElement('div')
    const rect = this.element.getBoundingClientRect()
    const size = Math.max(rect.width, rect.height)
    const x = event.clientX - rect.left - size / 2
    const y = event.clientY - rect.top - size / 2
    
    Object.assign(ripple.style, {
      position: 'absolute',
      width: size + 'px',
      height: size + 'px',
      left: x + 'px',
      top: y + 'px',
      borderRadius: '50%',
      background: 'rgba(255, 255, 255, 0.6)',
      transform: 'scale(0)',
      animation: 'ripple-animation 0.6s ease-out',
      pointerEvents: 'none'
    })
    
    this.element.appendChild(ripple)
    
    setTimeout(() => ripple.remove(), 600)
  }
}

// Parallax Effect
export class ParallaxEffect {
  constructor(elements, options = {}) {
    this.elements = elements
    this.options = {
      speed: options.speed || 0.5,
      offset: options.offset || 0,
      ...options
    }
    this.init()
  }

  init() {
    window.addEventListener('scroll', () => this.update())
    window.addEventListener('resize', () => this.update())
    this.update()
  }

  update() {
    const scrollY = window.pageYOffset
    
    this.elements.forEach(element => {
      const rect = element.getBoundingClientRect()
      const speed = element.dataset.parallaxSpeed || this.options.speed
      const offset = element.dataset.parallaxOffset || this.options.offset
      
      const yPos = -(scrollY - rect.top - offset) * speed
      
      element.style.transform = `translateY(${yPos}px)`
    })
  }
}

// Hover 3D Effect
export class Hover3D {
  constructor(element, options = {}) {
    this.element = element
    this.options = {
      maxRotation: options.maxRotation || 15,
      perspective: options.perspective || 1000,
      scale: options.scale || 1.05,
      ...options
    }
    this.init()
  }

  init() {
    this.element.style.transition = 'transform 0.3s ease-out'
    this.element.style.transformStyle = 'preserve-3d'
    
    this.element.addEventListener('mousemove', (e) => this.handleMouseMove(e))
    this.element.addEventListener('mouseleave', () => this.handleMouseLeave())
  }

  handleMouseMove(event) {
    const rect = this.element.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    const centerX = rect.width / 2
    const centerY = rect.height / 2
    
    const rotateX = ((y - centerY) / centerY) * -this.options.maxRotation
    const rotateY = ((x - centerX) / centerX) * this.options.maxRotation
    
    this.element.style.transform = `
      perspective(${this.options.perspective}px)
      rotateX(${rotateX}deg)
      rotateY(${rotateY}deg)
      scale(${this.options.scale})
    `
  }

  handleMouseLeave() {
    this.element.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) scale(1)'
  }
}

// Stagger Animation
export function staggerAnimation(elements, animation, staggerDelay = 100) {
  elements.forEach((element, index) => {
    setTimeout(() => {
      animation(element)
    }, index * staggerDelay)
  })
}

// CSS for animations
export const animationStyles = `
@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes float-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotate-in {
  from {
    opacity: 0;
    transform: rotate(-180deg) scale(0.8);
  }
  to {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}

.animate-float-up {
  animation: float-up 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.6s ease-out forwards;
}

.animate-rotate-in {
  animation: rotate-in 0.6s ease-out forwards;
}

.animate-stagger {
  opacity: 0;
}

.animate-stagger.animate {
  animation: float-up 0.6s ease-out forwards;
}
`

// Export all utilities
export default {
  AnimationEngine,
  ParticleSystem,
  TextMorph,
  RippleEffect,
  ParallaxEffect,
  Hover3D,
  staggerAnimation,
  animationStyles
}