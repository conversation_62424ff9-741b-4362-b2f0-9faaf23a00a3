import axios from 'axios'

const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:8000'

const flashcardsService = {
  /**
   * Gera flashcards a partir do conteúdo fornecido
   * @param {string} content - Conteúdo para gerar flashcards
   * @param {object} options - Opções de geração
   * @returns {Promise<Array>} Array de flashcards gerados
   */
  async generateFlashcards(content, options) {
    try {
      const response = await axios.post(`${API_URL}/api/flashcards/generate`, {
        content,
        quantity: options.quantity || 10,
        difficulty: options.difficulty || 'medium',
        type: options.type || 'qa',
        language: 'pt-BR'
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      return response.data.flashcards || this.generateMockFlashcards(content, options)
    } catch (error) {
      console.error('Erro ao gerar flashcards:', error)
      // Fallback para geração local se API falhar
      return this.generateMockFlashcards(content, options)
    }
  },

  /**
   * Busca conteúdo de uma URL
   * @param {string} url - URL para buscar conteúdo
   * @returns {Promise<string>} Conteúdo extraído
   */
  async fetchUrlContent(url) {
    try {
      const response = await axios.post(`${API_URL}/api/flashcards/fetch-url`, {
        url
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      return response.data.content
    } catch (error) {
      console.error('Erro ao buscar conteúdo da URL:', error)
      throw new Error('Não foi possível buscar conteúdo da URL')
    }
  },

  /**
   * Processa arquivos enviados
   * @param {Array<File>} files - Arquivos para processar
   * @returns {Promise<string>} Conteúdo combinado dos arquivos
   */
  async processFiles(files) {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })

    try {
      const response = await axios.post(`${API_URL}/api/flashcards/process-files`, formData, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'multipart/form-data'
        }
      })
      
      return response.data.content
    } catch (error) {
      console.error('Erro ao processar arquivos:', error)
      // Fallback para processamento local
      return this.processFilesLocally(files)
    }
  },

  /**
   * Processa arquivos localmente (fallback)
   * @param {Array<File>} files - Arquivos para processar
   * @returns {Promise<string>} Conteúdo combinado
   */
  async processFilesLocally(files) {
    const contents = []
    
    for (const file of files) {
      if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
        const text = await file.text()
        contents.push(text)
      } else if (file.name.endsWith('.md')) {
        const text = await file.text()
        contents.push(text)
      }
    }
    
    return contents.join('\n\n')
  },

  /**
   * Gera flashcards mock (fallback)
   * @param {string} content - Conteúdo base
   * @param {object} options - Opções de geração
   * @returns {Array} Flashcards gerados
   */
  generateMockFlashcards(content, options) {
    const flashcards = []
    const sentences = content.match(/[^.!?]+[.!?]+/g) || []
    const concepts = this.extractConcepts(content)
    
    const quantity = Math.min(options.quantity, sentences.length)
    
    for (let i = 0; i < quantity; i++) {
      let card = {}
      
      switch (options.type) {
        case 'qa':
          card = this.generateQACard(sentences[i % sentences.length], concepts)
          break
        case 'cloze':
          card = this.generateClozeCard(sentences[i % sentences.length])
          break
        case 'definition':
          card = this.generateDefinitionCard(concepts[i % concepts.length])
          break
        case 'mixed':
          const types = ['qa', 'cloze', 'definition']
          const randomType = types[i % types.length]
          if (randomType === 'qa') {
            card = this.generateQACard(sentences[i % sentences.length], concepts)
          } else if (randomType === 'cloze') {
            card = this.generateClozeCard(sentences[i % sentences.length])
          } else {
            card = this.generateDefinitionCard(concepts[i % concepts.length])
          }
          break
        default:
          card = this.generateQACard(sentences[i % sentences.length], concepts)
      }
      
      flashcards.push(card)
    }
    
    return flashcards
  },

  /**
   * Extrai conceitos importantes do texto
   * @param {string} text - Texto para análise
   * @returns {Array} Lista de conceitos
   */
  extractConcepts(text) {
    // Regex para encontrar palavras capitalizadas e termos médicos comuns
    const capitalizedWords = text.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*/g) || []
    const medicalTerms = text.match(/\b(diagnóstico|tratamento|sintoma|doença|patologia|anatomia|fisiologia|farmacologia|procedimento|exame|cirurgia|terapia|medicamento|prognóstico|etiologia|epidemiologia)\b/gi) || []
    
    // Combina e remove duplicatas
    const concepts = [...new Set([...capitalizedWords, ...medicalTerms])]
    
    return concepts.filter(concept => concept.length > 3)
  },

  /**
   * Gera um card de pergunta e resposta
   * @param {string} sentence - Sentença base
   * @param {Array} concepts - Lista de conceitos
   * @returns {object} Flashcard Q&A
   */
  generateQACard(sentence, concepts) {
    if (!sentence) {
      return {
        front: 'O que é medicina?',
        back: 'Medicina é a ciência dedicada ao estudo da vida, à prevenção e ao tratamento das doenças.'
      }
    }

    // Remove pontuação final
    const cleanSentence = sentence.trim().replace(/[.!?]+$/, '')
    
    // Tenta identificar o conceito principal na sentença
    const mainConcept = concepts.find(concept => 
      cleanSentence.toLowerCase().includes(concept.toLowerCase())
    ) || 'isso'
    
    return {
      front: `O que você sabe sobre ${mainConcept}?`,
      back: cleanSentence
    }
  },

  /**
   * Gera um card com lacunas
   * @param {string} sentence - Sentença base
   * @returns {object} Flashcard com lacuna
   */
  generateClozeCard(sentence) {
    if (!sentence) {
      return {
        front: 'A _____ é fundamental para o diagnóstico correto.',
        back: 'A anamnese é fundamental para o diagnóstico correto.'
      }
    }

    const words = sentence.trim().split(' ')
    if (words.length < 3) {
      return {
        front: sentence,
        back: sentence
      }
    }
    
    // Seleciona uma palavra importante para ocultar
    const importantWordIndex = words.findIndex(word => 
      word.length > 4 && /^[A-Z]/.test(word)
    ) || Math.floor(words.length / 2)
    
    const hiddenWord = words[importantWordIndex]
    words[importantWordIndex] = '_____'
    
    return {
      front: words.join(' '),
      back: sentence.trim()
    }
  },

  /**
   * Gera um card de definição
   * @param {string} concept - Conceito para definir
   * @returns {object} Flashcard de definição
   */
  generateDefinitionCard(concept) {
    if (!concept) {
      return {
        front: 'Defina: Homeostase',
        back: 'Homeostase é a capacidade do organismo de manter o equilíbrio interno constante, independentemente das alterações do meio externo.'
      }
    }

    // Definições simplificadas para demonstração
    const definitions = {
      'diagnóstico': 'Processo de identificação de uma doença através da análise de sintomas, exames e histórico do paciente',
      'tratamento': 'Conjunto de medidas terapêuticas aplicadas para curar ou aliviar uma doença',
      'sintoma': 'Manifestação subjetiva de uma doença, percebida pelo paciente',
      'patologia': 'Estudo das doenças, suas causas, processos e efeitos',
      'anatomia': 'Ciência que estuda a estrutura e organização dos seres vivos',
      'fisiologia': 'Estudo das funções e processos vitais dos organismos',
      'farmacologia': 'Ciência que estuda os medicamentos e seus efeitos no organismo'
    }
    
    const definition = definitions[concept.toLowerCase()] || 
      `${concept} é um conceito importante na área médica que requer estudo aprofundado.`
    
    return {
      front: `Defina: ${concept}`,
      back: definition
    }
  },

  /**
   * Salva decks no servidor
   * @param {Array} decks - Decks para salvar
   * @returns {Promise<boolean>} Sucesso da operação
   */
  async saveDecks(decks) {
    try {
      await axios.post(`${API_URL}/api/flashcards/decks`, {
        decks
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      return true
    } catch (error) {
      console.error('Erro ao salvar decks:', error)
      return false
    }
  },

  /**
   * Carrega decks do servidor
   * @returns {Promise<Array>} Lista de decks
   */
  async loadDecks() {
    try {
      const response = await axios.get(`${API_URL}/api/flashcards/decks`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      return response.data.decks || []
    } catch (error) {
      console.error('Erro ao carregar decks:', error)
      return []
    }
  },

  /**
   * Atualiza estatísticas de estudo
   * @param {string} cardId - ID do card
   * @param {number} rating - Avaliação (1-5)
   * @returns {Promise<void>}
   */
  async updateCardStats(cardId, rating) {
    try {
      await axios.post(`${API_URL}/api/flashcards/cards/${cardId}/review`, {
        rating,
        reviewedAt: new Date()
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
    } catch (error) {
      console.error('Erro ao atualizar estatísticas:', error)
    }
  }
}

export default flashcardsService