import axios from 'axios';

// API Base Configuration
const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://localhost:8000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Request interceptor for authentication
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API Services
export const apiServices = {
  // Authentication
  auth: {
    login: (credentials) => api.post('/auth/login', credentials),
    register: (userData) => api.post('/auth/register', userData),
    logout: () => api.post('/auth/logout'),
    refreshToken: () => api.post('/auth/refresh'),
    getProfile: () => api.get('/auth/profile'),
    updateProfile: (data) => api.put('/auth/profile', data)
  },

  // AI Assistant Services
  aiAssistant: {
    analyzeDocument: async (file) => {
      const formData = new FormData();
      formData.append('file', file);
      return api.post('/ai/assistant/analyze', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },
    analyzeText: (text) => api.post('/ai/assistant/analyze-text', { text }),
    generateQuestions: (config) => api.post('/ai/assistant/generate-questions', config),
    exportQuestions: (questions, format) => api.post('/ai/assistant/export', { questions, format }),
  },

  // Ultra Question Generator
  ultraQuestions: {
    uploadFiles: async (files) => {
      const formData = new FormData();
      files.forEach(file => formData.append('files', file));
      return api.post('/ai/ultra/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },
    generateQuestions: (content, config) => {
      return api.post('/ai/ultra/generate', { content, config }, {
        responseType: 'stream'
      });
    },
    analyzeQuality: (questions) => api.post('/ai/ultra/analyze-quality', { questions }),
    getSpecialties: () => api.get('/ai/ultra/specialties'),
    getQuestionTypes: () => api.get('/ai/ultra/question-types')
  },

  // Flashcards
  flashcards: {
    getDecks: () => api.get('/flashcards/decks'),
    createDeck: (deck) => api.post('/flashcards/decks', deck),
    updateDeck: (id, deck) => api.put(`/flashcards/decks/${id}`, deck),
    deleteDeck: (id) => api.delete(`/flashcards/decks/${id}`),
    getCards: (deckId) => api.get(`/flashcards/decks/${deckId}/cards`),
    createCard: (deckId, card) => api.post(`/flashcards/decks/${deckId}/cards`, card),
    updateCard: (deckId, cardId, card) => api.put(`/flashcards/decks/${deckId}/cards/${cardId}`, card),
    deleteCard: (deckId, cardId) => api.delete(`/flashcards/decks/${deckId}/cards/${cardId}`),
    recordReview: (cardId, performance) => api.post(`/flashcards/cards/${cardId}/review`, { performance }),
    getStudySession: (deckId) => api.get(`/flashcards/decks/${deckId}/study-session`),
    getStatistics: () => api.get('/flashcards/statistics')
  },

  // Analytics
  analytics: {
    getDashboard: () => api.get('/analytics/dashboard'),
    getStudyProgress: (period = '7d') => api.get(`/analytics/study-progress?period=${period}`),
    getPerformanceMetrics: () => api.get('/analytics/performance'),
    getHeatmap: (year, month) => api.get(`/analytics/heatmap?year=${year}&month=${month}`),
    getInsights: () => api.get('/analytics/insights'),
    exportReport: (format = 'pdf') => api.get(`/analytics/export?format=${format}`, {
      responseType: 'blob'
    })
  },

  // Resources
  resources: {
    getAll: (filters = {}) => api.get('/resources', { params: filters }),
    getById: (id) => api.get(`/resources/${id}`),
    create: (resource) => api.post('/resources', resource),
    update: (id, resource) => api.put(`/resources/${id}`, resource),
    delete: (id) => api.delete(`/resources/${id}`),
    search: (query) => api.get('/resources/search', { params: { q: query } })
  },

  // Study Plans
  studyPlans: {
    getActive: () => api.get('/study-plans/active'),
    getAll: () => api.get('/study-plans'),
    create: (plan) => api.post('/study-plans', plan),
    update: (id, plan) => api.put(`/study-plans/${id}`, plan),
    delete: (id) => api.delete(`/study-plans/${id}`),
    markComplete: (id, taskId) => api.post(`/study-plans/${id}/tasks/${taskId}/complete`)
  },

  // Calendar & Revisions
  calendar: {
    getEvents: (start, end) => api.get('/calendar/events', { params: { start, end } }),
    createEvent: (event) => api.post('/calendar/events', event),
    updateEvent: (id, event) => api.put(`/calendar/events/${id}`, event),
    deleteEvent: (id) => api.delete(`/calendar/events/${id}`),
    getRevisions: () => api.get('/calendar/revisions'),
    scheduleRevision: (data) => api.post('/calendar/revisions', data)
  },

  // Exams & Simulations
  exams: {
    getAll: () => api.get('/exams'),
    getById: (id) => api.get(`/exams/${id}`),
    start: (examId) => api.post(`/exams/${examId}/start`),
    submitAnswer: (examId, questionId, answer) => api.post(`/exams/${examId}/questions/${questionId}/answer`, { answer }),
    finish: (examId) => api.post(`/exams/${examId}/finish`),
    getResults: (examId) => api.get(`/exams/${examId}/results`),
    getHistory: () => api.get('/exams/history')
  },

  // Settings
  settings: {
    get: () => api.get('/settings'),
    update: (settings) => api.put('/settings', settings),
    getNotificationPreferences: () => api.get('/settings/notifications'),
    updateNotificationPreferences: (prefs) => api.put('/settings/notifications', prefs)
  }
};

// Mock implementations for testing
if (process.env.NODE_ENV === 'development' || !API_BASE_URL.startsWith('http')) {
  console.warn('⚠️ Using mock API implementations for testing');
  
  // Override methods with mock implementations
  apiServices.aiAssistant.analyzeDocument = async (file) => {
    await new Promise(resolve => setTimeout(resolve, 1500));
    return {
      data: {
        topics: [
          { name: "Cardiologia", relevance: 0.9 },
          { name: "Farmacologia", relevance: 0.7 }
        ],
        complexity: 75,
        recommendations: ["Adicione mais casos clínicos"],
        summary: "Documento analisado com sucesso"
      }
    };
  };

  apiServices.flashcards.getDecks = async () => {
    return {
      data: [
        {
          id: 1,
          name: "Cardiologia Básica",
          cardCount: 45,
          dueCount: 12,
          lastStudied: new Date().toISOString()
        },
        {
          id: 2,
          name: "Farmacologia",
          cardCount: 78,
          dueCount: 23,
          lastStudied: new Date().toISOString()
        }
      ]
    };
  };

  apiServices.analytics.getDashboard = async () => {
    return {
      data: {
        totalCards: 523,
        cardsStudiedToday: 45,
        studyStreak: 15,
        averageAccuracy: 0.82,
        weeklyProgress: [10, 15, 12, 20, 18, 25, 30]
      }
    };
  };
}

export default api;