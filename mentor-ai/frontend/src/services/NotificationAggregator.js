import { format, isToday, isTomorrow, isPast, addMinutes, differenceInMinutes, startOfDay, endOfDay } from 'date-fns'
import { ptBR } from 'date-fns/locale'

class NotificationAggregator {
  constructor(store) {
    this.store = store
    this.checkInterval = null
    this.lastCheck = new Date()
    this.sentNotifications = new Set() // Track sent notifications
    this.loadSentNotifications()
  }
  
  // Load previously sent notifications from localStorage
  loadSentNotifications() {
    try {
      const saved = localStorage.getItem('sent_notifications')
      if (saved) {
        this.sentNotifications = new Set(JSON.parse(saved))
      }
    } catch (error) {
      console.error('Error loading sent notifications:', error)
    }
  }
  
  // Save sent notifications to localStorage
  saveSentNotifications() {
    try {
      localStorage.setItem('sent_notifications', JSON.stringify([...this.sentNotifications]))
    } catch (error) {
      console.error('Error saving sent notifications:', error)
    }
  }
  
  // Generate unique key for notification
  generateNotificationKey(type, source, data) {
    const baseKey = `${type}-${source}`
    
    switch (source) {
      case 'calendar':
        return `${baseKey}-${data.eventId}-${format(new Date(), 'yyyy-MM-dd')}`
      case 'studyPlans':
        return `${baseKey}-${data.taskId || data.planId}-${format(new Date(), 'yyyy-MM-dd')}`
      case 'flashcards':
        return `${baseKey}-${data.deck || 'all'}-${format(new Date(), 'yyyy-MM-dd')}`
      case 'revisions':
        return `${baseKey}-${data.revisionId || data.subject}-${format(new Date(), 'yyyy-MM-dd')}`
      case 'exams':
        return `${baseKey}-${data.examId}`
      case 'performance':
        return `${baseKey}-${data.streak || data.goalId}-${format(new Date(), 'yyyy-MM-dd')}`
      default:
        return `${baseKey}-${JSON.stringify(data)}-${format(new Date(), 'yyyy-MM-dd')}`
    }
  }
  
  // Check if notification was already sent
  wasNotificationSent(key) {
    return this.sentNotifications.has(key)
  }
  
  // Mark notification as sent
  markNotificationAsSent(key) {
    this.sentNotifications.add(key)
    this.saveSentNotifications()
    
    // Clean old notifications (older than 7 days)
    this.cleanOldNotifications()
  }
  
  // Clean notifications older than 7 days
  cleanOldNotifications() {
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const cutoffDate = format(sevenDaysAgo, 'yyyy-MM-dd')
    
    const cleaned = new Set()
    this.sentNotifications.forEach(key => {
      const parts = key.split('-')
      const dateStr = parts[parts.length - 1]
      if (dateStr > cutoffDate) {
        cleaned.add(key)
      }
    })
    
    this.sentNotifications = cleaned
    this.saveSentNotifications()
  }

  // Inicializa o agregador
  start() {
    // Verifica notificações a cada 5 minutos para evitar duplicações
    this.checkInterval = setInterval(() => {
      this.checkAllSources()
    }, 300000) // 5 minutos

    // Primeira verificação após 2 segundos para dar tempo de carregar dados
    setTimeout(() => {
      this.checkAllSources()
    }, 2000)
  }

  stop() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }

  // Verifica todas as fontes de dados
  async checkAllSources() {
    const now = new Date()
    
    // 1. Verificar eventos do calendário
    await this.checkCalendarEvents(now)
    
    // 2. Verificar tarefas dos planos de estudo
    await this.checkStudyPlanTasks(now)
    
    // 3. Verificar revisões agendadas
    await this.checkScheduledRevisions(now)
    
    // 4. Verificar flashcards pendentes
    await this.checkFlashcards(now)
    
    // 5. Verificar provas próximas
    await this.checkUpcomingExams(now)
    
    // 6. Verificar métricas de performance
    await this.checkPerformanceAlerts(now)
    
    // 7. Gerar resumo diário (às 7h da manhã)
    if (now.getHours() === 7) {
      const todayKey = `daily-summary-${format(now, 'yyyy-MM-dd')}`
      if (!this.wasNotificationSent(todayKey)) {
        await this.generateDailySummary(now)
        this.markNotificationAsSent(todayKey)
      }
    }
    
    this.lastCheck = now
  }

  // 1. EVENTOS DO CALENDÁRIO
  async checkCalendarEvents(now) {
    const events = this.store.state.calendar?.events || []
    
    events.forEach(event => {
      const eventStart = new Date(event.start)
      const minutesUntilEvent = differenceInMinutes(eventStart, now)
      
      // Notificação 30 minutos antes
      if (minutesUntilEvent >= 29 && minutesUntilEvent <= 31) {
        this.createNotification({
          type: 'reminder',
          priority: 'high',
          source: 'calendar',
          title: '⏰ Evento em 30 minutos',
          message: `${event.title} começa às ${format(eventStart, 'HH:mm')}`,
          data: { eventId: event.id },
          actions: [
            {
              label: 'Ver Detalhes',
              action: 'calendar/viewEvent',
              params: { id: event.id }
            }
          ]
        })
      }
      
      // Notificação para revisões do dia
      if (event.isRevision && isToday(eventStart) && !event.completed) {
        this.createNotification({
          type: 'deadline',
          priority: 'high',
          source: 'calendar',
          title: '📚 Revisão Programada',
          message: `Revisão de ${event.subject} agendada para hoje`,
          data: { eventId: event.id, subject: event.subject },
          persistent: true,
          actions: [
            {
              label: 'Iniciar Revisão',
              action: 'calendar/startRevision',
              params: { id: event.id }
            }
          ]
        })
      }
      
      // Alertar eventos passados não concluídos
      if (isPast(eventStart) && !event.completed && event.priority === 'Alta') {
        this.createNotification({
          type: 'warning',
          priority: 'urgent',
          source: 'calendar',
          title: '⚠️ Evento Atrasado',
          message: `${event.title} estava agendado para ${format(eventStart, 'dd/MM HH:mm')}`,
          data: { eventId: event.id },
          requiresAction: true
        })
      }
    })
  }

  // 2. TAREFAS DOS PLANOS DE ESTUDO
  async checkStudyPlanTasks(now) {
    const plans = this.store.state.studyPlans?.plans || []
    const tasks = this.store.state.studyPlans?.tasks || []
    
    // Verificar tarefas com prazo
    tasks.forEach(task => {
      if (task.due_date && !task.completed) {
        const dueDate = new Date(task.due_date)
        
        // Tarefas vencendo hoje
        if (isToday(dueDate)) {
          this.createNotification({
            type: 'deadline',
            priority: 'high',
            source: 'studyPlans',
            title: '📋 Tarefa vence hoje',
            message: task.title,
            data: { taskId: task.id, planId: task.plan_id },
            persistent: true,
            actions: [
              {
                label: 'Ver Tarefa',
                action: 'studyPlans/viewTask',
                params: { id: task.id }
              }
            ]
          })
        }
        
        // Tarefas vencendo amanhã
        if (isTomorrow(dueDate)) {
          this.createNotification({
            type: 'reminder',
            priority: 'medium',
            source: 'studyPlans',
            title: '📅 Tarefa para amanhã',
            message: `${task.title} vence amanhã`,
            data: { taskId: task.id }
          })
        }
        
        // Tarefas atrasadas
        if (isPast(dueDate) && task.priority === 'high') {
          this.createNotification({
            type: 'warning',
            priority: 'urgent',
            source: 'studyPlans',
            title: '🚨 Tarefa atrasada',
            message: `${task.title} venceu em ${format(dueDate, 'dd/MM')}`,
            data: { taskId: task.id },
            requiresAction: true
          })
        }
      }
    })
    
    // Verificar planos inativos
    plans.forEach(plan => {
      if (plan.is_active && plan.last_activity) {
        const lastActivity = new Date(plan.last_activity)
        const daysSinceActivity = differenceInMinutes(now, lastActivity) / (60 * 24)
        
        if (daysSinceActivity > 7) {
          this.createNotification({
            type: 'insight',
            priority: 'low',
            source: 'studyPlans',
            title: '💡 Plano inativo',
            message: `"${plan.name}" não tem atividade há ${Math.floor(daysSinceActivity)} dias`,
            data: { planId: plan.id }
          })
        }
      }
    })
  }

  // 3. REVISÕES AGENDADAS
  async checkScheduledRevisions(now) {
    const revisions = this.store.state.revisions?.scheduledRevisions || []
    const settings = this.store.state.revisions?.settings || {}
    
    // Verificar horário ótimo de estudo
    if (settings.energyPeakStart && settings.energyPeakEnd) {
      const currentHour = now.getHours()
      const peakStart = parseInt(settings.energyPeakStart)
      const peakEnd = parseInt(settings.energyPeakEnd)
      
      if (currentHour === peakStart) {
        this.createNotification({
          type: 'insight',
          priority: 'medium',
          source: 'revisions',
          title: '🌟 Horário Ótimo de Estudo',
          message: 'Este é seu melhor horário para estudar baseado em seu cronótipo',
          data: { peakStart, peakEnd }
        })
      }
    }
    
    // Verificar revisões do dia
    revisions.forEach(revision => {
      const revisionDate = new Date(revision.date)
      
      if (isToday(revisionDate) && revision.progress < 100) {
        this.createNotification({
          type: 'deadline',
          priority: revision.difficulty === 'Difícil' ? 'high' : 'medium',
          source: 'revisions',
          title: '🔄 Revisão agendada',
          message: `Revisão de ${revision.subject} (${revision.difficulty})`,
          data: { revisionId: revision.id, subject: revision.subject },
          persistent: true,
          actions: [
            {
              label: 'Iniciar',
              action: 'revisions/startRevision',
              params: { id: revision.id }
            }
          ]
        })
      }
    })
  }

  // 4. FLASHCARDS
  async checkFlashcards(now) {
    const cards = this.store.state.flashcards?.cards || []
    const dueToday = cards.filter(card => {
      const dueDate = new Date(card.nextReview || card.dueDate)
      return isToday(dueDate) && !card.reviewed
    })
    
    if (dueToday.length > 0) {
      // Agrupar por baralho
      const byDeck = dueToday.reduce((acc, card) => {
        const deck = card.deck || 'Geral'
        if (!acc[deck]) acc[deck] = []
        acc[deck].push(card)
        return acc
      }, {})
      
      Object.entries(byDeck).forEach(([deck, cards]) => {
        this.createNotification({
          type: 'reminder',
          priority: 'medium',
          source: 'flashcards',
          title: '🎴 Flashcards para revisar',
          message: `${cards.length} cartões de ${deck} aguardando revisão`,
          data: { deck, cardIds: cards.map(c => c.id) },
          persistent: true,
          actions: [
            {
              label: 'Revisar Agora',
              action: 'flashcards/startSession',
              params: { deck }
            }
          ]
        })
      })
    }
    
    // Alertar sobre cartões com baixa retenção
    const weakCards = cards.filter(card => 
      card.memoryStrength < 0.5 && card.reviewCount > 2
    )
    
    if (weakCards.length > 5) {
      this.createNotification({
        type: 'warning',
        priority: 'medium',
        source: 'flashcards',
        title: '⚠️ Tópicos precisam reforço',
        message: `${weakCards.length} cartões com baixa retenção`,
        data: { cardIds: weakCards.map(c => c.id) },
        actions: [
          {
            label: 'Revisar Tópicos Fracos',
            action: 'flashcards/focusWeakCards',
            params: {}
          }
        ]
      })
    }
  }

  // 5. PROVAS E SIMULADOS
  async checkUpcomingExams(now) {
    const exams = this.store.state.provasSimulados?.provas || []
    
    exams.forEach(exam => {
      const examDate = new Date(exam.data)
      const daysUntilExam = differenceInMinutes(examDate, now) / (60 * 24)
      
      // Prova em 7 dias
      if (daysUntilExam <= 7 && daysUntilExam > 6) {
        this.createNotification({
          type: 'reminder',
          priority: 'medium',
          source: 'exams',
          title: '📝 Prova próxima',
          message: `${exam.disciplina} em 1 semana (${format(examDate, 'dd/MM')})`,
          data: { examId: exam.id }
        })
      }
      
      // Prova em 3 dias
      if (daysUntilExam <= 3 && daysUntilExam > 2) {
        this.createNotification({
          type: 'deadline',
          priority: 'high',
          source: 'exams',
          title: '📝 Prova em 3 dias!',
          message: `${exam.disciplina} - ${exam.tipo}`,
          data: { examId: exam.id },
          persistent: true
        })
      }
      
      // Prova amanhã
      if (isTomorrow(examDate)) {
        this.createNotification({
          type: 'warning',
          priority: 'urgent',
          source: 'exams',
          title: '🚨 Prova AMANHÃ!',
          message: `${exam.disciplina} - ${exam.tipo} às ${format(examDate, 'HH:mm')}`,
          data: { examId: exam.id },
          persistent: true,
          requiresAction: true
        })
      }
    })
  }

  // 6. ALERTAS DE PERFORMANCE
  async checkPerformanceAlerts(now) {
    const metrics = this.store.state.performance?.metrics || {}
    const goals = this.store.state.performance?.goals || []
    
    // Verificar streak em risco
    if (metrics.currentStreak > 0) {
      const lastStudy = new Date(metrics.lastStudyDate)
      const hoursSinceStudy = differenceInMinutes(now, lastStudy) / 60
      
      if (hoursSinceStudy > 20 && hoursSinceStudy < 24) {
        this.createNotification({
          type: 'warning',
          priority: 'high',
          source: 'performance',
          title: '🔥 Streak em risco!',
          message: `Estude hoje para manter seu streak de ${metrics.currentStreak} dias`,
          data: { streak: metrics.currentStreak },
          requiresAction: true
        })
      }
    }
    
    // Verificar metas próximas
    goals.forEach(goal => {
      if (goal.active && goal.progress >= 80 && goal.progress < 100) {
        this.createNotification({
          type: 'achievement',
          priority: 'medium',
          source: 'performance',
          title: '🎯 Meta quase alcançada!',
          message: `${goal.name} está ${goal.progress}% completa`,
          data: { goalId: goal.id }
        })
      }
    })
  }

  // 7. RESUMO DIÁRIO
  async generateDailySummary(now) {
    const today = startOfDay(now)
    const todayEnd = endOfDay(now)
    
    // Coletar todas as atividades do dia
    const summary = {
      events: [],
      tasks: [],
      revisions: [],
      flashcards: 0,
      exams: []
    }
    
    // Eventos do calendário
    const events = this.store.state.calendar?.events || []
    summary.events = events.filter(e => {
      const eventDate = new Date(e.start)
      return eventDate >= today && eventDate <= todayEnd
    })
    
    // Tarefas
    const tasks = this.store.state.studyPlans?.tasks || []
    summary.tasks = tasks.filter(t => {
      if (!t.due_date) return false
      const dueDate = new Date(t.due_date)
      return isToday(dueDate) && !t.completed
    })
    
    // Flashcards
    const cards = this.store.state.flashcards?.cards || []
    summary.flashcards = cards.filter(c => {
      const dueDate = new Date(c.nextReview || c.dueDate)
      return isToday(dueDate)
    }).length
    
    // Criar notificação de resumo
    const totalActivities = summary.events.length + summary.tasks.length + summary.flashcards
    
    if (totalActivities > 0) {
      let message = '📊 Resumo do seu dia:\n'
      
      if (summary.events.length > 0) {
        message += `\n📅 ${summary.events.length} evento(s)`
      }
      
      if (summary.tasks.length > 0) {
        message += `\n📋 ${summary.tasks.length} tarefa(s)`
      }
      
      if (summary.flashcards > 0) {
        message += `\n🎴 ${summary.flashcards} flashcard(s)`
      }
      
      this.createNotification({
        type: 'insight',
        priority: 'medium',
        source: 'system',
        title: '☀️ Bom dia! Aqui está seu dia',
        message,
        data: summary,
        actions: [
          {
            label: 'Ver Calendário',
            action: 'router/push',
            params: { path: '/calendar' }
          }
        ]
      })
    }
  }

  // Helpers
  createNotification(notification) {
    // Generate unique key for this notification
    const notificationKey = this.generateNotificationKey(
      notification.type,
      notification.source,
      notification.data || {}
    )
    
    // Check if already sent
    if (this.wasNotificationSent(notificationKey)) {
      return // Don't send duplicate
    }
    
    // Also check if notification with same content already exists in the store
    const existingNotifications = this.store.state.notifications?.items || []
    const isDuplicate = existingNotifications.some(n => 
      n.source === notification.source &&
      n.title === notification.title &&
      n.message === notification.message &&
      n.data?.eventId === notification.data?.eventId &&
      n.data?.taskId === notification.data?.taskId &&
      n.data?.examId === notification.data?.examId
    )
    
    if (isDuplicate) {
      return // Don't send duplicate
    }
    
    const fullNotification = {
      id: notification.id || `${notification.source}-${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
      read: false,
      important: notification.priority === 'high' || notification.priority === 'urgent',
      category: this.getCategoryFromSource(notification.source),
      ...notification
    }
    
    this.store.dispatch('notifications/addCenterNotification', fullNotification)
    
    // Mark as sent
    this.markNotificationAsSent(notificationKey)
  }

  notificationExists(id) {
    const notifications = this.store.state.notifications?.items || []
    return notifications.some(n => n.id === id)
  }

  getCategoryFromSource(source) {
    const sourceMap = {
      calendar: 'Calendário',
      studyPlans: 'Planos de Estudo',
      flashcards: 'Flashcards',
      revisions: 'Revisões',
      exams: 'Provas',
      performance: 'Desempenho',
      system: 'Sistema'
    }
    return sourceMap[source] || 'Outros'
  }
}

export default NotificationAggregator