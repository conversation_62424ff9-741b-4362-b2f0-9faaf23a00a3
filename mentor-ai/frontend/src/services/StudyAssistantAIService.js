// Serviço de IA para o StudyAssistantUltra
import axios from 'axios';

const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://localhost:8001/api';

class StudyAssistantAIService {
  constructor() {
    this.apiClient = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Interceptor para adicionar token de autenticação
    this.apiClient.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
  }

  // Análise de Desempenho Neural
  async analyzeNeuralPerformance(userId) {
    try {
      const response = await this.apiClient.post('/study-assistant/neural-analysis', {
        userId,
        timestamp: new Date().toISOString(),
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao analisar desempenho neural:', error);
      // Retornar dados simulados em caso de erro
      return this.getMockNeuralAnalysis();
    }
  }

  // Gerar Plano de Estudo Neural
  async generateNeuralStudyPlan(params) {
    try {
      const response = await this.apiClient.post('/study-assistant/neural-plan', params);
      return response.data;
    } catch (error) {
      console.error('Erro ao gerar plano neural:', error);
      return this.getMockNeuralPlan(params);
    }
  }

  // Otimizar Memória
  async optimizeMemory(topicId, learningData) {
    try {
      const response = await this.apiClient.post('/study-assistant/memory-optimize', {
        topicId,
        learningData,
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao otimizar memória:', error);
      return this.getMockMemoryOptimization();
    }
  }

  // Gerar Mapa Conceitual 3D
  async generate3DConceptMap(topic) {
    try {
      const response = await this.apiClient.post('/study-assistant/concept-map-3d', {
        topic,
        depth: 3,
        connections: true,
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao gerar mapa conceitual:', error);
      return this.getMock3DConceptMap(topic);
    }
  }

  // Análise de Zona de Fluxo
  async analyzeFlowZone(sessionData) {
    try {
      const response = await this.apiClient.post('/study-assistant/flow-zone', sessionData);
      return response.data;
    } catch (error) {
      console.error('Erro ao analisar zona de fluxo:', error);
      return this.getMockFlowZoneAnalysis();
    }
  }

  // Obter Recomendações de IA
  async getAIRecommendations(userProfile) {
    try {
      const response = await this.apiClient.post('/study-assistant/recommendations', userProfile);
      return response.data;
    } catch (error) {
      console.error('Erro ao obter recomendações:', error);
      return this.getMockRecommendations();
    }
  }

  // Análise de Velocidade de Aprendizado
  async analyzeLearningVelocity(learningHistory) {
    try {
      const response = await this.apiClient.post('/study-assistant/learning-velocity', {
        history: learningHistory,
        timeframe: 'last_30_days',
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao analisar velocidade:', error);
      return this.getMockLearningVelocity();
    }
  }

  // Gamificação - Obter Conquistas
  async getAchievements(userId) {
    try {
      const response = await this.apiClient.get(`/study-assistant/achievements/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao obter conquistas:', error);
      return this.getMockAchievements();
    }
  }

  // Análise de Foco
  async analyzeFocus(sessionMetrics) {
    try {
      const response = await this.apiClient.post('/study-assistant/focus-analysis', sessionMetrics);
      return response.data;
    } catch (error) {
      console.error('Erro ao analisar foco:', error);
      return this.getMockFocusAnalysis();
    }
  }

  // ========== MÉTODOS DE DADOS SIMULADOS ==========

  getMockNeuralAnalysis() {
    return {
      performance: {
        overall: 85,
        trends: [
          { date: '2024-01-01', score: 75 },
          { date: '2024-01-08', score: 78 },
          { date: '2024-01-15', score: 82 },
          { date: '2024-01-22', score: 85 },
        ],
      },
      strengths: [
        'Anatomia Cardiovascular',
        'Farmacologia Básica',
        'Diagnóstico Diferencial',
      ],
      weaknesses: [
        'Neurologia Complexa',
        'Endocrinologia Avançada',
      ],
      neuralPatterns: {
        retention: 0.82,
        recall: 0.78,
        application: 0.85,
      },
    };
  }

  getMockNeuralPlan(params) {
    const topics = {
      cardiology: 'Cardiologia',
      neurology: 'Neurologia',
      endocrinology: 'Endocrinologia',
    };

    return {
      planId: `plan_${Date.now()}`,
      duration: `${params.weeks} semanas`,
      dailyTime: `${params.hoursPerDay} horas`,
      modules: [
        {
          week: 1,
          focus: topics[params.focus] || 'Medicina Geral',
          activities: [
            { type: 'teoria', duration: 60, topic: 'Fundamentos' },
            { type: 'prática', duration: 45, topic: 'Casos Clínicos' },
            { type: 'revisão', duration: 30, topic: 'Flashcards' },
          ],
        },
      ],
      aiOptimizations: {
        bestStudyTimes: ['08:00-10:00', '14:00-16:00'],
        breakPattern: 'Pomodoro 25/5',
        reviewCycle: 'Espaçada: 1, 3, 7, 21 dias',
      },
    };
  }

  getMockMemoryOptimization() {
    return {
      optimizationScore: 92,
      techniques: [
        {
          name: 'Palácio da Memória',
          effectiveness: 0.88,
          description: 'Associe conceitos a locais familiares',
        },
        {
          name: 'Mnemônicos Médicos',
          effectiveness: 0.85,
          description: 'Use acrônimos e associações',
        },
        {
          name: 'Mapas Mentais',
          effectiveness: 0.82,
          description: 'Conecte conceitos visualmente',
        },
      ],
      retentionPrediction: {
        day1: 0.95,
        day7: 0.82,
        day30: 0.75,
        day90: 0.68,
      },
    };
  }

  getMock3DConceptMap(topic) {
    return {
      nodes: [
        { id: 1, label: topic, level: 0, connections: 5 },
        { id: 2, label: 'Subtópico 1', level: 1, connections: 3 },
        { id: 3, label: 'Subtópico 2', level: 1, connections: 4 },
        { id: 4, label: 'Conceito A', level: 2, connections: 2 },
        { id: 5, label: 'Conceito B', level: 2, connections: 2 },
      ],
      edges: [
        { from: 1, to: 2, strength: 0.9 },
        { from: 1, to: 3, strength: 0.85 },
        { from: 2, to: 4, strength: 0.7 },
        { from: 3, to: 5, strength: 0.75 },
      ],
      complexity: 0.72,
      recommendedPath: [1, 2, 4, 3, 5],
    };
  }

  getMockFlowZoneAnalysis() {
    return {
      currentState: 'optimal',
      flowScore: 0.87,
      factors: {
        challenge: 0.82,
        skill: 0.85,
        focus: 0.90,
        feedback: 0.88,
      },
      recommendations: [
        'Aumentar ligeiramente a dificuldade',
        'Manter sessões de 25-30 minutos',
        'Usar música lo-fi para concentração',
      ],
      predictedDuration: 45,
    };
  }

  getMockRecommendations() {
    return [
      {
        id: 1,
        type: 'study',
        priority: 'alta',
        title: 'Revisar Farmacologia Cardiovascular',
        reason: 'Detectamos queda de 15% na retenção',
        estimatedTime: 30,
        icon: 'heart',
      },
      {
        id: 2,
        type: 'practice',
        priority: 'média',
        title: 'Resolver 10 questões de Neurologia',
        reason: 'Fortalecer pontos fracos identificados',
        estimatedTime: 45,
        icon: 'brain',
      },
      {
        id: 3,
        type: 'break',
        priority: 'alta',
        title: 'Fazer pausa de 15 minutos',
        reason: 'Fadiga cognitiva detectada',
        estimatedTime: 15,
        icon: 'pause',
      },
    ];
  }

  getMockLearningVelocity() {
    return {
      current: 1.2,
      average: 1.0,
      trend: 'increasing',
      prediction: '1.3x em 2 semanas',
      factors: {
        consistency: 0.9,
        quality: 0.85,
        engagement: 0.88,
      },
      topAccelerators: [
        'Revisão espaçada',
        'Prática ativa',
        'Ensino entre pares',
      ],
    };
  }

  getMockAchievements() {
    return [
      {
        id: 1,
        name: 'Maratonista do Conhecimento',
        description: 'Estudou por 7 dias consecutivos',
        icon: 'trophy',
        unlockedAt: '2024-01-15',
        rarity: 'rare',
        xpReward: 500,
      },
      {
        id: 2,
        name: 'Mestre dos Flashcards',
        description: 'Revisou 1000 flashcards',
        icon: 'cards',
        unlockedAt: '2024-01-10',
        rarity: 'epic',
        xpReward: 750,
      },
      {
        id: 3,
        name: 'Velocidade Neural',
        description: 'Alcançou 1.5x de velocidade de aprendizado',
        icon: 'lightning',
        unlockedAt: null,
        rarity: 'legendary',
        xpReward: 1000,
        progress: 0.8,
      },
    ];
  }

  getMockFocusAnalysis() {
    return {
      score: 0.82,
      pattern: 'deep_focus',
      distractions: 3,
      optimalDuration: 25,
      timeline: [
        { minute: 0, focus: 0.5 },
        { minute: 5, focus: 0.7 },
        { minute: 10, focus: 0.85 },
        { minute: 15, focus: 0.9 },
        { minute: 20, focus: 0.88 },
        { minute: 25, focus: 0.75 },
      ],
      suggestions: [
        'Desativar notificações durante estudo',
        'Usar técnica Pomodoro',
        'Ambiente com menos estímulos visuais',
      ],
    };
  }
}

export default new StudyAssistantAIService();