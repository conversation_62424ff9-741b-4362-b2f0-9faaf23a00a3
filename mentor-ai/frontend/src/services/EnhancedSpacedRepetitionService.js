/**
 * EnhancedSpacedRepetitionService.js
 * Next-generation spaced repetition with LSTM neural networks and online learning
 * Implements cutting-edge memory optimization algorithms
 */

import { differenceInDays, addDays, format } from 'date-fns';

// Conditional TensorFlow import with fallback
let tf = null;
try {
  tf = require('@tensorflow/tfjs');
} catch (error) {
  console.warn('TensorFlow.js not available. Using fallback algorithm.');
}

class EnhancedSpacedRepetitionService {
  constructor() {
    this.model = null;
    this.isModelLoaded = false;
    this.userModel = null; // Personal adaptation model
    this.tfAvailable = !!tf;
    
    // Advanced configuration
    this.config = {
      lstm: {
        sequenceLength: 10,
        hiddenUnits: 128,
        dropout: 0.2
      },
      learning: {
        rate: 0.001,
        batchSize: 32,
        epochs: 5
      },
      memory: {
        consolidationThreshold: 0.85,
        interferenceDecay: 0.05,
        circadianImpact: 0.15
      }
    };
    
    // Initialize models
    this.initializeModels();
  }

  /**
   * Initialize LSTM models for memory prediction
   */
  async initializeModels() {
    if (!this.tfAvailable) {
      console.log('Using fallback spaced repetition algorithm');
      this.isModelLoaded = false;
      return;
    }
    
    try {
      // Build main LSTM model
      this.model = await this.buildLSTMModel();
      
      // Load pre-trained weights if available
      const weightsUrl = '/models/spaced-repetition-lstm/model.json';
      try {
        await this.model.loadWeights(weightsUrl);
        console.log('Pre-trained LSTM model loaded');
      } catch (e) {
        console.log('Using fresh LSTM model');
      }
      
      // Initialize user adaptation model
      this.userModel = await this.buildUserAdaptationModel();
      
      this.isModelLoaded = true;
    } catch (error) {
      console.error('Model initialization error:', error);
      // Fallback to traditional algorithm
      this.isModelLoaded = false;
    }
  }

  /**
   * Build LSTM model architecture
   */
  async buildLSTMModel() {
    if (!tf) return null;
    
    const model = tf.sequential({
      layers: [
        // Input layer
        tf.layers.lstm({
          units: this.config.lstm.hiddenUnits,
          returnSequences: true,
          inputShape: [this.config.lstm.sequenceLength, 8], // 8 features per timestep
          dropout: this.config.lstm.dropout,
          recurrentDropout: this.config.lstm.dropout
        }),
        
        // Second LSTM layer
        tf.layers.lstm({
          units: 64,
          returnSequences: false,
          dropout: this.config.lstm.dropout
        }),
        
        // Attention mechanism
        tf.layers.dense({
          units: 32,
          activation: 'relu',
          kernelRegularizer: tf.regularizers.l2({ l2: 0.01 })
        }),
        
        // Dropout for regularization
        tf.layers.dropout({
          rate: this.config.lstm.dropout
        }),
        
        // Output layer - predicts retention probability
        tf.layers.dense({
          units: 1,
          activation: 'sigmoid'
        })
      ]
    });
    
    // Compile with advanced optimizer
    model.compile({
      optimizer: tf.train.adam(this.config.learning.rate),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy', 'precision', 'recall']
    });
    
    return model;
  }

  /**
   * Build user-specific adaptation model
   */
  async buildUserAdaptationModel() {
    if (!tf) return null;
    
    const model = tf.sequential({
      layers: [
        // Embedding layer for user features
        tf.layers.dense({
          units: 16,
          activation: 'relu',
          inputShape: [5] // User profile features
        }),
        
        // Personalization layer
        tf.layers.dense({
          units: 8,
          activation: 'relu'
        }),
        
        // Output adjustment factors
        tf.layers.dense({
          units: 3, // Interval multiplier, difficulty adjustment, time preference
          activation: 'linear'
        })
      ]
    });
    
    model.compile({
      optimizer: tf.train.adam(0.0001),
      loss: 'meanSquaredError'
    });
    
    return model;
  }

  /**
   * Calculate next review with LSTM prediction
   */
  async calculateNextReview(card, reviewHistory, userProfile) {
    if (!this.isModelLoaded) {
      // Fallback to enhanced traditional algorithm
      return this.calculateNextReviewTraditional(card, reviewHistory);
    }
    
    try {
      // Prepare sequence data for LSTM
      const sequence = this.prepareSequenceData(card, reviewHistory);
      
      // Get LSTM prediction
      const retentionPrediction = await this.predictRetention(sequence);
      
      // Get user-specific adjustments
      const userAdjustments = await this.getUserAdjustments(userProfile);
      
      // Calculate optimal interval
      const baseInterval = this.calculateOptimalInterval(
        retentionPrediction,
        card.difficulty,
        reviewHistory.length
      );
      
      // Apply user adjustments
      const adjustedInterval = this.applyUserAdjustments(
        baseInterval,
        userAdjustments,
        userProfile
      );
      
      // Consider circadian rhythms
      const finalInterval = this.optimizeForCircadianRhythm(
        adjustedInterval,
        userProfile.preferredStudyTime
      );
      
      return {
        nextReviewDate: addDays(new Date(), finalInterval),
        interval: finalInterval,
        confidence: retentionPrediction,
        optimalTimeOfDay: this.getOptimalReviewTime(userProfile),
        memoryStrength: this.calculateMemoryStrength(retentionPrediction, reviewHistory)
      };
    } catch (error) {
      console.error('LSTM prediction error:', error);
      return this.calculateNextReviewTraditional(card, reviewHistory);
    }
  }

  /**
   * Prepare sequence data for LSTM input
   */
  prepareSequenceData(card, reviewHistory) {
    if (!tf) return null;
    
    const sequence = [];
    const maxHistory = this.config.lstm.sequenceLength;
    
    // Pad or trim history to sequence length
    const relevantHistory = reviewHistory.slice(-maxHistory);
    
    // Add padding if needed
    while (relevantHistory.length < maxHistory) {
      relevantHistory.unshift({
        timestamp: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        correct: false,
        responseTime: 0,
        confidence: 0
      });
    }
    
    // Extract features for each timestep
    relevantHistory.forEach((review, index) => {
      const features = [
        card.difficulty / 5, // Normalized difficulty
        review.correct ? 1 : 0, // Correctness
        Math.min(review.responseTime / 60, 1), // Normalized response time
        review.confidence || 0, // User confidence
        this.getTimeOfDayFactor(new Date(review.timestamp)), // Circadian factor
        index / maxHistory, // Position in sequence
        this.calculateInterferenceScore(review.timestamp, relevantHistory), // Interference
        this.getStudyLoadFactor(review.timestamp) // Cognitive load
      ];
      
      sequence.push(features);
    });
    
    return tf.tensor3d([sequence]);
  }

  /**
   * Predict retention probability using LSTM
   */
  async predictRetention(sequenceTensor) {
    const prediction = await this.model.predict(sequenceTensor);
    const retentionProbability = await prediction.data();
    
    // Cleanup tensors
    sequenceTensor.dispose();
    prediction.dispose();
    
    return retentionProbability[0];
  }

  /**
   * Get user-specific adjustments
   */
  async getUserAdjustments(userProfile) {
    if (!this.userModel || !tf) {
      return { intervalMultiplier: 1, difficultyAdjust: 0, timePreference: 0 };
    }
    
    const userFeatures = tf.tensor2d([[
      userProfile.studyStreak / 100, // Normalized streak
      userProfile.averageAccuracy || 0.7,
      userProfile.dailyStudyTime / 120, // Normalized to 2 hours
      userProfile.totalCardsReviewed / 10000, // Normalized
      userProfile.preferredDifficulty / 5
    ]]);
    
    const adjustments = await this.userModel.predict(userFeatures);
    const adjustmentData = await adjustments.data();
    
    userFeatures.dispose();
    adjustments.dispose();
    
    return {
      intervalMultiplier: 0.5 + adjustmentData[0] * 1.5, // 0.5x to 2x
      difficultyAdjust: adjustmentData[1] * 2 - 1, // -1 to +1
      timePreference: adjustmentData[2]
    };
  }

  /**
   * Calculate optimal interval based on retention prediction
   */
  calculateOptimalInterval(retentionProbability, difficulty, reviewCount) {
    // Target retention rate
    const targetRetention = 0.9;
    
    // Base interval calculation using forgetting curve
    const baseInterval = -Math.log(targetRetention) / Math.log(retentionProbability);
    
    // Adjust for difficulty
    const difficultyFactor = 2.5 - (difficulty - 3) * 0.4;
    
    // Progressive interval increase
    const progressiveFactor = Math.pow(1.3, Math.min(reviewCount, 8));
    
    // Calculate final interval
    let interval = baseInterval * difficultyFactor * progressiveFactor;
    
    // Apply bounds
    interval = Math.max(1, Math.min(interval, 365));
    
    return Math.round(interval);
  }

  /**
   * Apply user-specific adjustments to interval
   */
  applyUserAdjustments(baseInterval, adjustments, userProfile) {
    let interval = baseInterval * adjustments.intervalMultiplier;
    
    // Adjust for user's learning pace
    if (userProfile.learningPace === 'fast') {
      interval *= 0.8;
    } else if (userProfile.learningPace === 'slow') {
      interval *= 1.2;
    }
    
    // Consider user's schedule
    if (userProfile.busyDays && userProfile.busyDays.length > 0) {
      interval = this.avoidBusyDays(interval, userProfile.busyDays);
    }
    
    return Math.round(interval);
  }

  /**
   * Optimize interval for circadian rhythms
   */
  optimizeForCircadianRhythm(interval, preferredTime) {
    const targetDate = addDays(new Date(), interval);
    const targetHour = targetDate.getHours();
    
    // Check if target time aligns with preferred study time
    const preferredHour = parseInt(preferredTime?.split(':')[0] || '9');
    const hourDifference = Math.abs(targetHour - preferredHour);
    
    // Adjust interval to align with preferred time
    if (hourDifference > 6) {
      // Shift by a day if too far from preferred time
      interval += targetHour < preferredHour ? 1 : -1;
    }
    
    return interval;
  }

  /**
   * Calculate memory strength based on review history
   */
  calculateMemoryStrength(retentionProbability, reviewHistory) {
    const correctReviews = reviewHistory.filter(r => r.correct).length;
    const totalReviews = reviewHistory.length;
    
    const accuracy = totalReviews > 0 ? correctReviews / totalReviews : 0;
    const consistency = this.calculateConsistency(reviewHistory);
    
    // Weighted combination
    const strength = (
      retentionProbability * 0.5 +
      accuracy * 0.3 +
      consistency * 0.2
    );
    
    return Math.round(strength * 100);
  }

  /**
   * Calculate review consistency
   */
  calculateConsistency(reviewHistory) {
    if (reviewHistory.length < 2) return 0;
    
    const intervals = [];
    for (let i = 1; i < reviewHistory.length; i++) {
      const interval = differenceInDays(
        new Date(reviewHistory[i].timestamp),
        new Date(reviewHistory[i-1].timestamp)
      );
      intervals.push(interval);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => {
      return sum + Math.pow(interval - avgInterval, 2);
    }, 0) / intervals.length;
    
    // Lower variance = higher consistency
    return Math.exp(-variance / 100);
  }

  /**
   * Calculate interference score from other reviews
   */
  calculateInterferenceScore(timestamp, history) {
    let interference = 0;
    const currentTime = new Date(timestamp).getTime();
    
    history.forEach(review => {
      if (review.timestamp !== timestamp) {
        const timeDiff = Math.abs(currentTime - new Date(review.timestamp).getTime());
        const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
        
        // Interference decays exponentially with time
        interference += Math.exp(-daysDiff / 7);
      }
    });
    
    return Math.min(interference / history.length, 1);
  }

  /**
   * Get cognitive load factor for a given time
   */
  getStudyLoadFactor(timestamp) {
    // This would ideally check user's calendar and study history
    // For now, return a normalized value
    const hour = new Date(timestamp).getHours();
    
    // Lower load during optimal study hours
    if (hour >= 9 && hour <= 11 || hour >= 15 && hour <= 17) {
      return 0.7;
    }
    
    return 1.0;
  }

  /**
   * Get time of day factor for circadian optimization
   */
  getTimeOfDayFactor(date) {
    const hour = date.getHours();
    
    // Optimal times based on research
    const factors = {
      5: 0.6, 6: 0.7, 7: 0.8, 8: 0.9,
      9: 1.0, 10: 1.0, 11: 0.95,
      12: 0.8, 13: 0.75, 14: 0.8,
      15: 0.9, 16: 0.95, 17: 0.9,
      18: 0.85, 19: 0.8, 20: 0.75,
      21: 0.7, 22: 0.6, 23: 0.5
    };
    
    return factors[hour] || 0.5;
  }

  /**
   * Get optimal review time for user
   */
  getOptimalReviewTime(userProfile) {
    const preferredTime = userProfile.preferredStudyTime || '09:00';
    const [hour, minute] = preferredTime.split(':').map(Number);
    
    // Adjust based on user's chronotype
    let optimalHour = hour;
    if (userProfile.chronotype === 'morning') {
      optimalHour = Math.max(6, hour - 1);
    } else if (userProfile.chronotype === 'evening') {
      optimalHour = Math.min(22, hour + 1);
    }
    
    return `${String(optimalHour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`;
  }

  /**
   * Avoid scheduling on busy days
   */
  avoidBusyDays(interval, busyDays) {
    const targetDate = addDays(new Date(), interval);
    const dayOfWeek = targetDate.getDay();
    
    if (busyDays.includes(dayOfWeek)) {
      // Find next available day
      for (let shift = 1; shift <= 3; shift++) {
        const newDate = addDays(targetDate, shift);
        if (!busyDays.includes(newDate.getDay())) {
          return interval + shift;
        }
      }
    }
    
    return interval;
  }

  /**
   * Update model with user feedback (online learning)
   */
  async updateModelWithFeedback(card, review, actualRetention) {
    if (!this.isModelLoaded || !review.sequence || !tf) return;
    
    try {
      // Prepare training data
      const x = tf.tensor3d([review.sequence]);
      const y = tf.tensor2d([[actualRetention ? 1 : 0]]);
      
      // Fine-tune model with new data
      await this.model.fit(x, y, {
        epochs: 1,
        verbose: 0
      });
      
      // Update user model if applicable
      if (this.userModel && review.userFeatures) {
        const userX = tf.tensor2d([review.userFeatures]);
        const userY = tf.tensor2d([[
          review.intervalAccuracy,
          review.difficultyAccuracy,
          review.timeAccuracy
        ]]);
        
        await this.userModel.fit(userX, userY, {
          epochs: 1,
          verbose: 0
        });
        
        userX.dispose();
        userY.dispose();
      }
      
      // Cleanup
      x.dispose();
      y.dispose();
      
      console.log('Model updated with user feedback');
    } catch (error) {
      console.error('Model update error:', error);
    }
  }

  /**
   * Traditional algorithm fallback
   */
  calculateNextReviewTraditional(card, reviewHistory) {
    const lastReview = reviewHistory[reviewHistory.length - 1];
    const correct = lastReview?.correct ?? true;
    
    let interval = card.interval || 1;
    let easeFactor = card.easeFactor || 2.5;
    
    if (correct) {
      if (reviewHistory.length === 1) {
        interval = 1;
      } else if (reviewHistory.length === 2) {
        interval = 6;
      } else {
        interval = Math.round(interval * easeFactor);
      }
      
      // Adjust ease factor
      easeFactor = Math.max(1.3, easeFactor + 0.1);
    } else {
      interval = 1;
      easeFactor = Math.max(1.3, easeFactor - 0.2);
    }
    
    return {
      nextReviewDate: addDays(new Date(), interval),
      interval,
      easeFactor,
      confidence: correct ? 0.8 : 0.4,
      memoryStrength: Math.min(100, (reviewHistory.filter(r => r.correct).length / reviewHistory.length) * 100)
    };
  }

  /**
   * Generate learning insights using AI
   */
  async generateLearningInsights(userStats, recentReviews) {
    const insights = [];
    
    // Performance trend analysis
    const trend = this.analyzeTrend(recentReviews);
    if (trend.direction === 'declining' && trend.significance > 0.2) {
      insights.push({
        type: 'warning',
        title: 'Performance Decline Detected',
        message: `Your accuracy has decreased by ${Math.round(trend.change * 100)}% over the last week.`,
        recommendation: 'Consider reducing daily card load or taking more breaks.',
        priority: 'high'
      });
    }
    
    // Optimal time analysis
    const optimalTimes = this.analyzeOptimalTimes(recentReviews);
    if (optimalTimes.bestHour !== null) {
      insights.push({
        type: 'tip',
        title: 'Best Study Time',
        message: `You perform ${Math.round(optimalTimes.improvement * 100)}% better when studying at ${optimalTimes.bestHour}:00.`,
        recommendation: 'Try to schedule your reviews during this time.',
        priority: 'medium'
      });
    }
    
    // Difficulty calibration
    const difficultyAnalysis = this.analyzeDifficulty(userStats);
    if (difficultyAnalysis.recommendation) {
      insights.push({
        type: 'suggestion',
        title: 'Difficulty Adjustment',
        message: difficultyAnalysis.message,
        recommendation: difficultyAnalysis.recommendation,
        priority: 'low'
      });
    }
    
    return insights;
  }

  /**
   * Analyze performance trend
   */
  analyzeTrend(reviews) {
    if (reviews.length < 14) {
      return { direction: 'stable', change: 0, significance: 0 };
    }
    
    const recent = reviews.slice(-7);
    const previous = reviews.slice(-14, -7);
    
    const recentAccuracy = recent.filter(r => r.correct).length / recent.length;
    const previousAccuracy = previous.filter(r => r.correct).length / previous.length;
    
    const change = recentAccuracy - previousAccuracy;
    const direction = change > 0.05 ? 'improving' : change < -0.05 ? 'declining' : 'stable';
    
    return {
      direction,
      change,
      significance: Math.abs(change)
    };
  }

  /**
   * Analyze optimal study times
   */
  analyzeOptimalTimes(reviews) {
    const hourlyPerformance = {};
    
    reviews.forEach(review => {
      const hour = new Date(review.timestamp).getHours();
      if (!hourlyPerformance[hour]) {
        hourlyPerformance[hour] = { correct: 0, total: 0 };
      }
      hourlyPerformance[hour].total++;
      if (review.correct) {
        hourlyPerformance[hour].correct++;
      }
    });
    
    let bestHour = null;
    let bestAccuracy = 0;
    let avgAccuracy = 0;
    let totalHours = 0;
    
    Object.entries(hourlyPerformance).forEach(([hour, stats]) => {
      if (stats.total >= 5) { // Minimum sample size
        const accuracy = stats.correct / stats.total;
        avgAccuracy += accuracy;
        totalHours++;
        
        if (accuracy > bestAccuracy) {
          bestAccuracy = accuracy;
          bestHour = parseInt(hour);
        }
      }
    });
    
    avgAccuracy = totalHours > 0 ? avgAccuracy / totalHours : 0;
    const improvement = avgAccuracy > 0 ? (bestAccuracy - avgAccuracy) / avgAccuracy : 0;
    
    return { bestHour, improvement };
  }

  /**
   * Analyze difficulty calibration
   */
  analyzeDifficulty(userStats) {
    const { averageAccuracy, preferredDifficulty } = userStats;
    
    if (averageAccuracy > 0.9 && preferredDifficulty < 4) {
      return {
        message: 'You\'re consistently performing well.',
        recommendation: 'Consider increasing difficulty to accelerate learning.'
      };
    } else if (averageAccuracy < 0.6 && preferredDifficulty > 2) {
      return {
        message: 'Cards may be too challenging.',
        recommendation: 'Try reducing difficulty to build confidence.'
      };
    }
    
    return { recommendation: null };
  }

  /**
   * Export model for sharing/backup
   */
  async exportModel() {
    if (!this.isModelLoaded || !tf) return null;
    
    try {
      const modelData = await this.model.save(tf.io.withSaveHandler(async artifacts => {
        return {
          modelTopology: artifacts.modelTopology,
          weightSpecs: artifacts.weightSpecs,
          weightData: Array.from(new Uint8Array(artifacts.weightData))
        };
      }));
      
      return {
        version: '2.0',
        timestamp: new Date().toISOString(),
        modelData,
        config: this.config
      };
    } catch (error) {
      console.error('Model export error:', error);
      return null;
    }
  }

  /**
   * Import model from backup
   */
  async importModel(exportedData) {
    if (!exportedData || exportedData.version !== '2.0' || !tf) {
      throw new Error('Invalid or incompatible model data, or TensorFlow not available');
    }
    
    try {
      // Reconstruct model from exported data
      const modelData = exportedData.modelData;
      const weightData = new Uint8Array(modelData.weightData).buffer;
      
      this.model = await tf.loadLayersModel(tf.io.fromMemory(
        modelData.modelTopology,
        modelData.weightSpecs,
        weightData
      ));
      
      // Recompile model
      this.model.compile({
        optimizer: tf.train.adam(this.config.learning.rate),
        loss: 'binaryCrossentropy',
        metrics: ['accuracy', 'precision', 'recall']
      });
      
      this.isModelLoaded = true;
      console.log('Model imported successfully');
    } catch (error) {
      console.error('Model import error:', error);
      throw error;
    }
  }
}

// Export singleton instance
export default new EnhancedSpacedRepetitionService();