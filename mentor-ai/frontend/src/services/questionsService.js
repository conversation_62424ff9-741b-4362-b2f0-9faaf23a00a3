import axios from 'axios'
import aiQuestionEngine from './aiQuestionEngine'

const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:8000'

const questionsService = {
  /**
   * Gera questões a partir do conteúdo fornecido
   * @param {string|object} content - Conteúdo ou tópico para gerar questões
   * @param {object} options - Opções de geração
   * @returns {Promise<Array>} Array de questões geradas
   */
  async generateQuestions(content, options) {
    try {
      const payload = typeof content === 'string' 
        ? { content, ...options }
        : { ...content, ...options }

      const response = await axios.post(`${API_URL}/api/questions/generate`, payload, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      return response.data.questions || this.generateMockQuestions(content, options)
    } catch (error) {
      console.error('Erro ao gerar questões:', error)
      // Fallback para geração local se API falhar
      return this.generateMockQuestions(content, options)
    }
  },

  /**
   * Processa arquivos enviados para extrair conteúdo
   * @param {Array<File>} files - Arquivos para processar
   * @returns {Promise<string>} Conteúdo combinado dos arquivos
   */
  async processFiles(files) {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })

    try {
      const response = await axios.post(`${API_URL}/api/questions/process-files`, formData, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'multipart/form-data'
        }
      })
      
      return response.data.content
    } catch (error) {
      console.error('Erro ao processar arquivos:', error)
      // Fallback para processamento local
      return this.processFilesLocally(files)
    }
  },

  /**
   * Processa arquivos localmente (fallback)
   * @param {Array<File>} files - Arquivos para processar
   * @returns {Promise<string>} Conteúdo combinado
   */
  async processFilesLocally(files) {
    const contents = []
    
    for (const file of files) {
      if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
        const text = await file.text()
        contents.push(text)
      } else if (file.name.endsWith('.md')) {
        const text = await file.text()
        contents.push(text)
      }
    }
    
    return contents.join('\n\n')
  },

  /**
   * Gera questões mock (fallback)
   * @param {string|object} content - Conteúdo base
   * @param {object} options - Opções de geração
   * @returns {Array} Questões geradas
   */
  async generateMockQuestions(content, options) {
    // Se há conteúdo textual, usa o gerador inteligente
    if (typeof content === 'string' && content.trim().length > 50) {
      return await this.generateIntelligentQuestions(content, options)
    }
    
    // Caso contrário, usa templates
    const questions = []
    const quantity = options.quantity || 10
    
    // Templates de questões por tipo de prova
    const templates = {
      residencia: this.getResidenciaTemplates(),
      revalida: this.getRevalidaTemplates(),
      enem: this.getEnemTemplates(),
      concurso: this.getConcursoTemplates(),
      board: this.getBoardTemplates()
    }
    
    const selectedTemplates = templates[options.examType] || templates.residencia
    
    for (let i = 0; i < quantity; i++) {
      const template = selectedTemplates[i % selectedTemplates.length]
      const question = this.generateQuestionFromTemplate(template, options)
      questions.push(question)
    }
    
    return questions
  },

  /**
   * ULTRA-THINKING AI: Gera questões inteligentes baseadas no conteúdo
   * @param {string} content - Conteúdo para análise
   * @param {object} options - Opções de geração
   * @returns {Array} Questões geradas contextualmente
   */
  async generateIntelligentQuestions(content, options) {
    try {
      // Usar o novo AI Question Engine ultra-avançado
      const ultraQuestions = await aiQuestionEngine.generateUltraQuestions(content, options)
      
      // Se o engine avançado retornar questões, usá-las
      if (ultraQuestions && ultraQuestions.length > 0) {
        return ultraQuestions.map((q, index) => ({
          ...q,
          id: Date.now() + index,
          userAnswer: null
        }))
      }
    } catch (error) {
      console.warn('Fallback para geração tradicional:', error)
    }
    
    // Fallback para o método tradicional se necessário
    // 1. Análise profunda do conteúdo
    const analysis = this.analyzeContent(content)
    
    // 2. Extração de conceitos-chave e relações
    const concepts = this.extractKeyConcepts(analysis)
    
    // 3. Identificação de tópicos médicos
    const medicalTopics = this.identifyMedicalTopics(analysis)
    
    // 4. Construção de questões contextuais
    const questions = []
    const quantity = options.quantity || 10
    
    for (let i = 0; i < quantity; i++) {
      const questionType = this.selectQuestionType(i, options)
      const question = this.buildContextualQuestion(
        concepts,
        medicalTopics,
        analysis,
        questionType,
        options
      )
      
      // Validação de qualidade
      if (this.validateQuestionQuality(question)) {
        questions.push(question)
      } else {
        // Tenta gerar novamente se a qualidade não for satisfatória
        i--
      }
    }
    
    return questions
  },

  /**
   * Analisa profundamente o conteúdo fornecido
   * @param {string} content - Conteúdo para análise
   * @returns {object} Análise estruturada
   */
  analyzeContent(content) {
    const sentences = content.match(/[^.!?]+[.!?]+/g) || []
    const paragraphs = content.split(/\n\n+/)
    const words = content.toLowerCase().split(/\s+/)
    
    // Análise de frequência de termos
    const termFrequency = {}
    words.forEach(word => {
      const cleanWord = word.replace(/[^a-záàâãéèêíïóôõöúçñ]/gi, '')
      if (cleanWord.length > 3) {
        termFrequency[cleanWord] = (termFrequency[cleanWord] || 0) + 1
      }
    })
    
    // Identificação de números, percentuais e métricas
    const numbers = content.match(/\d+([.,]\d+)?%?/g) || []
    const metrics = content.match(/\d+\s*(mg|mL|mmHg|bpm|anos|dias|horas|minutos|segundos|cm|mm|kg|g|UI|mEq\/L)/gi) || []
    
    // Detecção de definições e explicações
    const definitions = sentences.filter(s => 
      s.match(/(é|são|define-se|caracteriza-se|consiste em|significa|representa)/i)
    )
    
    // Identificação de relações causa-efeito
    const causalRelations = sentences.filter(s => 
      s.match(/(porque|devido|causa|consequência|resulta|leva a|provoca|induz|portanto|logo)/i)
    )
    
    // Detecção de procedimentos e protocolos
    const procedures = sentences.filter(s => 
      s.match(/(deve-se|recomenda-se|indica-se|administrar|realizar|proceder|etapa|passo|primeiro|depois|em seguida)/i)
    )
    
    // Identificação de contraindicações e cuidados
    const contraindications = sentences.filter(s => 
      s.match(/(não deve|contraindicado|evitar|cuidado|atenção|exceto|nunca|jamais)/i)
    )
    
    return {
      sentences,
      paragraphs,
      termFrequency,
      numbers,
      metrics,
      definitions,
      causalRelations,
      procedures,
      contraindications,
      totalWords: words.length,
      complexityScore: this.calculateComplexity(content)
    }
  },

  /**
   * Calcula a complexidade do texto
   * @param {string} content - Conteúdo
   * @returns {number} Score de complexidade
   */
  calculateComplexity(content) {
    const words = content.split(/\s+/)
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length
    const sentences = content.match(/[.!?]+/g) || []
    const avgSentenceLength = words.length / (sentences.length || 1)
    
    return (avgWordLength * 0.5) + (avgSentenceLength * 0.5)
  },

  /**
   * Extrai conceitos-chave usando análise semântica
   * @param {object} analysis - Análise do conteúdo
   * @returns {Array} Conceitos principais
   */
  extractKeyConcepts(analysis) {
    const concepts = []
    
    // Termos mais frequentes (top 20)
    const sortedTerms = Object.entries(analysis.termFrequency)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
    
    // Termos médicos importantes
    const medicalTerms = [
      'diagnóstico', 'tratamento', 'sintoma', 'doença', 'patologia',
      'anatomia', 'fisiologia', 'farmacologia', 'procedimento', 'exame',
      'cirurgia', 'terapia', 'medicamento', 'prognóstico', 'etiologia',
      'epidemiologia', 'fisiopatologia', 'manifestação', 'indicação',
      'contraindicação', 'complicação', 'prevenção', 'reabilitação',
      'anamnese', 'semiologia', 'propedêutica', 'conduta', 'protocolo',
      'infecção', 'inflamação', 'neoplasia', 'trauma', 'síndrome',
      'distúrbio', 'alteração', 'disfunção', 'insuficiência', 'hipertensão',
      'diabetes', 'câncer', 'infarto', 'acidente', 'vascular', 'cerebral'
    ]
    
    // Filtra termos médicos relevantes
    sortedTerms.forEach(([term, frequency]) => {
      if (medicalTerms.some(medical => term.includes(medical) || medical.includes(term))) {
        concepts.push({
          term,
          frequency,
          type: 'medical',
          importance: frequency * 2 // Peso maior para termos médicos
        })
      } else if (frequency > 2) {
        concepts.push({
          term,
          frequency,
          type: 'general',
          importance: frequency
        })
      }
    })
    
    // Adiciona métricas e números importantes
    analysis.metrics.forEach(metric => {
      concepts.push({
        term: metric,
        frequency: 1,
        type: 'metric',
        importance: 3 // Métricas são sempre importantes
      })
    })
    
    return concepts.sort((a, b) => b.importance - a.importance)
  },

  /**
   * Identifica tópicos médicos específicos no conteúdo
   * @param {object} analysis - Análise do conteúdo
   * @returns {Array} Tópicos médicos identificados
   */
  identifyMedicalTopics(analysis) {
    const topics = []
    const content = analysis.sentences.join(' ').toLowerCase()
    
    const topicPatterns = {
      cardiologia: ['cardíaco', 'coração', 'infarto', 'arritmia', 'hipertensão', 'ecg', 'coronária', 'valva', 'miocárdio'],
      pneumologia: ['pulmão', 'respiratório', 'asma', 'dpoc', 'pneumonia', 'tuberculose', 'dispneia', 'tosse', 'expectoração'],
      neurologia: ['neurológico', 'cérebro', 'avc', 'epilepsia', 'cefaleia', 'convulsão', 'parkinson', 'alzheimer', 'demência'],
      gastroenterologia: ['gastrointestinal', 'estômago', 'intestino', 'fígado', 'hepatite', 'cirrose', 'úlcera', 'refluxo'],
      endocrinologia: ['diabetes', 'tireoide', 'hormônio', 'insulina', 'glicemia', 'hipotireoidismo', 'hipertireoidismo'],
      nefrologia: ['renal', 'rim', 'nefro', 'diálise', 'creatinina', 'ureia', 'proteinúria', 'hematúria'],
      hematologia: ['sangue', 'anemia', 'leucemia', 'plaqueta', 'coagulação', 'hemograma', 'transfusão'],
      infectologia: ['infecção', 'vírus', 'bactéria', 'fungo', 'antibiótico', 'febre', 'sepse', 'imunização'],
      reumatologia: ['artrite', 'artrose', 'lupus', 'reumatoide', 'articulação', 'autoimune', 'vasculite'],
      oncologia: ['câncer', 'tumor', 'neoplasia', 'metástase', 'quimioterapia', 'radioterapia', 'oncológico'],
      pediatria: ['criança', 'infantil', 'pediátrico', 'neonatal', 'lactente', 'adolescente', 'vacinação', 'crescimento'],
      ginecologia: ['ginecológico', 'útero', 'ovário', 'menstrual', 'gravidez', 'gestante', 'pré-natal', 'parto'],
      psiquiatria: ['psiquiátrico', 'depressão', 'ansiedade', 'esquizofrenia', 'bipolar', 'psicose', 'antidepressivo'],
      ortopedia: ['ortopédico', 'fratura', 'osso', 'articulação', 'coluna', 'joelho', 'ombro', 'trauma'],
      dermatologia: ['pele', 'dermatológico', 'eczema', 'psoríase', 'melanoma', 'acne', 'alergia', 'dermatite'],
      oftalmologia: ['olho', 'visão', 'catarata', 'glaucoma', 'retina', 'conjuntivite', 'miopia', 'oftalmológico'],
      otorrinolaringologia: ['ouvido', 'nariz', 'garganta', 'sinusite', 'otite', 'rinite', 'amigdalite', 'surdez']
    }
    
    Object.entries(topicPatterns).forEach(([topic, keywords]) => {
      const matches = keywords.filter(keyword => content.includes(keyword))
      if (matches.length > 0) {
        topics.push({
          name: topic,
          confidence: matches.length / keywords.length,
          matchedTerms: matches
        })
      }
    })
    
    return topics.sort((a, b) => b.confidence - a.confidence)
  },

  /**
   * Seleciona o tipo de questão baseado no índice e opções
   * @param {number} index - Índice da questão
   * @param {object} options - Opções
   * @returns {string} Tipo de questão
   */
  selectQuestionType(index, options) {
    const types = []
    
    if (options.style === 'direto') {
      types.push('direct', 'factual', 'definition')
    } else if (options.style === 'caso_clinico') {
      types.push('clinical_case', 'scenario', 'diagnosis')
    } else if (options.style === 'interpretacao') {
      types.push('interpretation', 'analysis', 'comparison')
    } else if (options.style === 'associacao') {
      types.push('association', 'correlation', 'cause_effect')
    } else {
      // Misto - varia os tipos
      types.push('direct', 'clinical_case', 'interpretation', 'association')
    }
    
    return types[index % types.length]
  },

  /**
   * Constrói uma questão contextual baseada na análise
   * @param {Array} concepts - Conceitos extraídos
   * @param {Array} topics - Tópicos médicos
   * @param {object} analysis - Análise completa
   * @param {string} questionType - Tipo de questão
   * @param {object} options - Opções
   * @returns {object} Questão gerada
   */
  buildContextualQuestion(concepts, topics, analysis, questionType, options) {
    let question = {}
    
    switch (questionType) {
      case 'direct':
      case 'factual':
        question = this.buildFactualQuestion(concepts, analysis, options)
        break
      case 'definition':
        question = this.buildDefinitionQuestion(concepts, analysis, options)
        break
      case 'clinical_case':
      case 'scenario':
        question = this.buildClinicalCaseQuestion(concepts, topics, analysis, options)
        break
      case 'diagnosis':
        question = this.buildDiagnosisQuestion(concepts, topics, analysis, options)
        break
      case 'interpretation':
      case 'analysis':
        question = this.buildInterpretationQuestion(concepts, analysis, options)
        break
      case 'comparison':
        question = this.buildComparisonQuestion(concepts, analysis, options)
        break
      case 'association':
      case 'correlation':
        question = this.buildAssociationQuestion(concepts, analysis, options)
        break
      case 'cause_effect':
        question = this.buildCauseEffectQuestion(concepts, analysis, options)
        break
      default:
        question = this.buildFactualQuestion(concepts, analysis, options)
    }
    
    // Adiciona metadados
    question.topic = topics[0]?.name || 'Medicina Geral'
    question.difficulty = options.difficulty
    question.style = options.style
    question.examType = options.examType
    
    // Gera explicação contextual
    if (options.includeExplanation) {
      question.explanation = this.generateContextualExplanation(question, analysis)
    }
    
    // Adiciona referências relevantes
    if (options.includeReferences) {
      question.references = this.generateContextualReferences(question, topics)
    }
    
    return question
  },

  /**
   * Constrói questão factual direta
   */
  buildFactualQuestion(concepts, analysis, options) {
    const importantConcept = concepts[0]
    const relatedSentence = analysis.sentences.find(s => 
      s.toLowerCase().includes(importantConcept.term)
    ) || analysis.sentences[0]
    
    // Extrai informação factual
    const facts = this.extractFactsFromSentence(relatedSentence)
    
    const question = {
      text: `Em relação a ${importantConcept.term}, é correto afirmar que:`,
      alternatives: this.generateFactualAlternatives(facts, analysis),
      correctAnswer: 0 // Será randomizado depois
    }
    
    return this.randomizeAlternatives(question)
  },

  /**
   * Constrói questão de definição
   */
  buildDefinitionQuestion(concepts, analysis, options) {
    const definitionSentence = analysis.definitions[0] || analysis.sentences[0]
    const concept = concepts.find(c => definitionSentence.toLowerCase().includes(c.term)) || concepts[0]
    
    const question = {
      text: `Qual a definição correta de ${concept.term}?`,
      alternatives: this.generateDefinitionAlternatives(concept, analysis),
      correctAnswer: 0
    }
    
    return this.randomizeAlternatives(question)
  },

  /**
   * Constrói questão de caso clínico
   */
  buildClinicalCaseQuestion(concepts, topics, analysis, options) {
    // Constrói um caso clínico baseado nos dados do texto
    const clinicalData = this.extractClinicalData(analysis)
    const caseDescription = this.generateClinicalCase(clinicalData, concepts)
    
    const question = {
      text: caseDescription + '\n\nQual a conduta mais apropriada?',
      alternatives: this.generateClinicalAlternatives(clinicalData, topics, analysis),
      correctAnswer: 0
    }
    
    return this.randomizeAlternatives(question)
  },

  /**
   * Constrói questão de diagnóstico
   */
  buildDiagnosisQuestion(concepts, topics, analysis, options) {
    const symptoms = this.extractSymptoms(analysis)
    const signs = this.extractClinicalSigns(analysis)
    
    const question = {
      text: `Paciente apresenta ${symptoms.join(', ')}. ${signs.length > 0 ? 'Ao exame: ' + signs.join(', ') + '.' : ''} Qual o diagnóstico mais provável?`,
      alternatives: this.generateDiagnosisAlternatives(symptoms, signs, topics, analysis),
      correctAnswer: 0
    }
    
    return this.randomizeAlternatives(question)
  },

  /**
   * Constrói questão de interpretação
   */
  buildInterpretationQuestion(concepts, analysis, options) {
    const dataPoint = analysis.metrics[0] || analysis.numbers[0] || '10 mg'
    const context = analysis.sentences.find(s => s.includes(dataPoint)) || analysis.sentences[0]
    
    const question = {
      text: `${context}\n\nComo interpretar esse achado?`,
      alternatives: this.generateInterpretationAlternatives(dataPoint, context, analysis),
      correctAnswer: 0
    }
    
    return this.randomizeAlternatives(question)
  },

  /**
   * Constrói questão de comparação
   */
  buildComparisonQuestion(concepts, analysis, options) {
    const concept1 = concepts[0]
    const concept2 = concepts[1] || { term: 'placebo' }
    
    const question = {
      text: `Comparando ${concept1.term} com ${concept2.term}, é correto afirmar:`,
      alternatives: this.generateComparisonAlternatives(concept1, concept2, analysis),
      correctAnswer: 0
    }
    
    return this.randomizeAlternatives(question)
  },

  /**
   * Constrói questão de associação
   */
  buildAssociationQuestion(concepts, analysis, options) {
    const associations = this.findAssociations(concepts, analysis)
    
    const question = {
      text: `Qual a correta associação entre os seguintes elementos?`,
      alternatives: this.generateAssociationAlternatives(associations, analysis),
      correctAnswer: 0
    }
    
    return this.randomizeAlternatives(question)
  },

  /**
   * Constrói questão de causa e efeito
   */
  buildCauseEffectQuestion(concepts, analysis, options) {
    const causalRelation = analysis.causalRelations[0] || 
      `O uso de ${concepts[0].term} pode resultar em alterações significativas`
    
    const question = {
      text: `${causalRelation}\n\nQual o mecanismo envolvido?`,
      alternatives: this.generateCauseEffectAlternatives(causalRelation, concepts, analysis),
      correctAnswer: 0
    }
    
    return this.randomizeAlternatives(question)
  },

  /**
   * Funções auxiliares para geração de alternativas
   */
  generateFactualAlternatives(facts, analysis) {
    // Implementação que gera alternativas baseadas em fatos extraídos
    const alternatives = []
    const correctFact = facts[0] || 'É uma condição médica importante'
    alternatives.push(correctFact)
    
    // Gera distratores plausíveis
    for (let i = 0; i < 4; i++) {
      alternatives.push(this.generatePlausibleDistractor(correctFact, analysis))
    }
    
    return alternatives
  },

  generateDefinitionAlternatives(concept, analysis) {
    const alternatives = []
    const correctDef = analysis.definitions.find(d => 
      d.toLowerCase().includes(concept.term)
    ) || `${concept.term} é um conceito fundamental na medicina`
    
    alternatives.push(this.cleanDefinition(correctDef))
    
    // Gera definições incorretas mas plausíveis
    for (let i = 0; i < 4; i++) {
      alternatives.push(this.generateSimilarDefinition(concept, analysis))
    }
    
    return alternatives
  },

  generateClinicalAlternatives(clinicalData, topics, analysis) {
    const alternatives = []
    const procedures = analysis.procedures.map(p => this.extractProcedure(p))
    
    // Alternativa correta baseada no texto
    alternatives.push(procedures[0] || 'Realizar exame clínico completo e solicitar exames complementares')
    
    // Distratores baseados em condutas médicas comuns
    const commonProcedures = [
      'Iniciar antibioticoterapia empírica de amplo espectro',
      'Solicitar internação hospitalar para observação',
      'Prescrever analgesia e reavaliar em 24 horas',
      'Encaminhar para avaliação especializada urgente',
      'Realizar procedimento invasivo imediato',
      'Aguardar evolução clínica antes de intervir',
      'Iniciar corticoterapia em dose plena'
    ]
    
    for (let i = 0; i < 4; i++) {
      alternatives.push(commonProcedures[i % commonProcedures.length])
    }
    
    return alternatives
  },

  generateDiagnosisAlternatives(symptoms, signs, topics, analysis) {
    const alternatives = []
    const primaryTopic = topics[0]?.name || 'medicina geral'
    
    // Diagnóstico correto baseado nos sintomas
    const correctDiagnosis = this.inferDiagnosis(symptoms, signs, analysis)
    alternatives.push(correctDiagnosis)
    
    // Diagnósticos diferenciais
    const differentials = this.getDifferentialDiagnoses(primaryTopic, symptoms)
    alternatives.push(...differentials.slice(0, 4))
    
    return alternatives
  },

  /**
   * Funções de extração de dados
   */
  extractFactsFromSentence(sentence) {
    // Extrai fatos e afirmações da sentença
    const facts = []
    const patterns = [
      /(.+) é (.+)/,
      /(.+) são (.+)/,
      /(.+) caracteriza-se por (.+)/,
      /(.+) apresenta (.+)/,
      /(.+) possui (.+)/
    ]
    
    patterns.forEach(pattern => {
      const match = sentence.match(pattern)
      if (match) {
        facts.push(match[0])
      }
    })
    
    return facts.length > 0 ? facts : [sentence]
  },

  extractClinicalData(analysis) {
    return {
      age: analysis.numbers.find(n => n.includes('anos')) || '45 anos',
      gender: analysis.sentences.join(' ').match(/(homem|mulher|masculino|feminino)/i)?.[0] || 'masculino',
      mainComplaint: this.extractMainComplaint(analysis),
      duration: analysis.metrics.find(m => m.match(/dias|horas|semanas|meses/)) || '3 dias',
      vitalSigns: this.extractVitalSigns(analysis)
    }
  },

  extractSymptoms(analysis) {
    const symptomKeywords = [
      'dor', 'febre', 'tosse', 'dispneia', 'náusea', 'vômito', 'cefaleia',
      'tontura', 'fraqueza', 'mal-estar', 'sudorese', 'calafrios', 'prurido',
      'edema', 'palpitações', 'síncope', 'parestesia', 'disfagia', 'astenia'
    ]
    
    const symptoms = []
    analysis.sentences.forEach(sentence => {
      symptomKeywords.forEach(keyword => {
        if (sentence.toLowerCase().includes(keyword)) {
          symptoms.push(keyword)
        }
      })
    })
    
    return [...new Set(symptoms)]
  },

  extractClinicalSigns(analysis) {
    const signs = []
    const signPatterns = [
      /\d+\/\d+\s*mmHg/,
      /\d+\s*bpm/,
      /\d+°C/,
      /\d+\s*irpm/,
      /SpO2\s*\d+%/
    ]
    
    signPatterns.forEach(pattern => {
      const matches = analysis.sentences.join(' ').match(pattern)
      if (matches) {
        signs.push(matches[0])
      }
    })
    
    return signs
  },

  /**
   * Randomiza as alternativas e ajusta o índice da resposta correta
   */
  randomizeAlternatives(question) {
    const alternatives = [...question.alternatives]
    const correctAnswer = alternatives[0] // Primeira é sempre a correta inicialmente
    
    // Embaralha as alternativas
    for (let i = alternatives.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [alternatives[i], alternatives[j]] = [alternatives[j], alternatives[i]]
    }
    
    // Encontra o novo índice da resposta correta
    question.correctAnswer = alternatives.indexOf(correctAnswer)
    question.alternatives = alternatives
    
    return question
  },

  /**
   * Gera explicação contextual para a questão
   */
  generateContextualExplanation(question, analysis) {
    const relevantSentences = analysis.sentences.filter(s => 
      question.alternatives.some(alt => 
        s.toLowerCase().includes(alt.toLowerCase().substring(0, 20))
      )
    )
    
    const explanation = `A alternativa ${String.fromCharCode(65 + question.correctAnswer)} está correta. ${relevantSentences[0] || ''} Este conceito é fundamental para compreender ${question.topic} e sua aplicação clínica.`
    
    return explanation
  },

  /**
   * Gera referências contextuais
   */
  generateContextualReferences(question, topics) {
    const topicReferences = {
      cardiologia: ['Braunwald - Tratado de Doenças Cardiovasculares', 'Diretrizes da Sociedade Brasileira de Cardiologia'],
      pneumologia: ['Murray & Nadel - Textbook of Respiratory Medicine', 'Diretrizes SBPT'],
      neurologia: ['Adams and Victor - Principles of Neurology', 'Tratado de Neurologia da ABN'],
      pediatria: ['Nelson - Tratado de Pediatria', 'Diretrizes da SBP'],
      ginecologia: ['Williams - Obstetrícia', 'Diretrizes FEBRASGO']
    }
    
    const primaryTopic = topics[0]?.name || 'medicina geral'
    const references = topicReferences[primaryTopic] || [
      'Harrison - Medicina Interna',
      'Goldman-Cecil Medicine'
    ]
    
    return references
  },

  /**
   * Valida a qualidade da questão gerada
   */
  validateQuestionQuality(question) {
    // Verifica se tem todos os campos necessários
    if (!question.text || !question.alternatives || question.alternatives.length !== 5) {
      return false
    }
    
    // Verifica se as alternativas são distintas
    const uniqueAlternatives = new Set(question.alternatives)
    if (uniqueAlternatives.size !== 5) {
      return false
    }
    
    // Verifica comprimento mínimo
    if (question.text.length < 20) {
      return false
    }
    
    // Verifica se tem resposta correta válida
    if (question.correctAnswer < 0 || question.correctAnswer > 4) {
      return false
    }
    
    return true
  },

  /**
   * Funções auxiliares adicionais
   */
  generatePlausibleDistractor(correctFact, analysis) {
    // Gera distratores modificando elementos do fato correto
    const modifications = [
      'sempre', 'nunca', 'raramente', 'frequentemente',
      'aumenta', 'diminui', 'não altera', 'pode alterar',
      'é contraindicado', 'é indicado', 'deve ser evitado'
    ]
    
    const randomMod = modifications[Math.floor(Math.random() * modifications.length)]
    return correctFact.replace(/é|são|apresenta|possui/i, randomMod)
  },

  cleanDefinition(definition) {
    // Remove pontuação excessiva e formata a definição
    return definition
      .replace(/[.!?]+$/, '')
      .replace(/\s+/g, ' ')
      .trim()
  },

  generateSimilarDefinition(concept, analysis) {
    const templates = [
      `${concept.term} é um processo patológico caracterizado por alterações funcionais`,
      `${concept.term} representa uma condição clínica de origem multifatorial`,
      `${concept.term} consiste em um distúrbio metabólico com repercussões sistêmicas`,
      `${concept.term} define-se como uma síndrome com manifestações variadas`
    ]
    
    return templates[Math.floor(Math.random() * templates.length)]
  },

  extractProcedure(sentence) {
    // Extrai a ação/procedimento principal da sentença
    const match = sentence.match(/(deve-se|recomenda-se|indica-se|realizar|administrar)(.+)/i)
    return match ? match[0] : sentence
  },

  inferDiagnosis(symptoms, signs, analysis) {
    // Infere diagnóstico baseado nos sintomas e sinais
    const symptomString = symptoms.join(' ')
    
    if (symptomString.includes('dor torácica') && symptomString.includes('dispneia')) {
      return 'Síndrome coronariana aguda'
    } else if (symptomString.includes('febre') && symptomString.includes('tosse')) {
      return 'Pneumonia adquirida na comunidade'
    } else if (symptomString.includes('cefaleia') && signs.some(s => s.includes('mmHg'))) {
      return 'Crise hipertensiva'
    }
    
    // Diagnóstico genérico baseado no primeiro sintoma
    return `Síndrome relacionada a ${symptoms[0] || 'alterações clínicas'}`
  },

  getDifferentialDiagnoses(topic, symptoms) {
    const differentials = {
      cardiologia: ['Angina instável', 'Pericardite', 'Embolia pulmonar', 'Dissecção aórtica', 'Miocardite'],
      pneumologia: ['Asma', 'DPOC exacerbada', 'Bronquite aguda', 'Derrame pleural', 'Pneumotórax'],
      neurologia: ['AVC isquêmico', 'AVC hemorrágico', 'Meningite', 'Enxaqueca', 'Epilepsia'],
      gastroenterologia: ['Úlcera péptica', 'Pancreatite', 'Colecistite', 'Apendicite', 'Doença de Crohn']
    }
    
    return differentials[topic] || [
      'Processo inflamatório agudo',
      'Distúrbio metabólico',
      'Síndrome infecciosa',
      'Alteração funcional',
      'Doença autoimune'
    ]
  },

  extractMainComplaint(analysis) {
    const complaints = [
      'dor', 'desconforto', 'mal-estar', 'alteração', 'sintomas'
    ]
    
    for (const sentence of analysis.sentences) {
      for (const complaint of complaints) {
        if (sentence.toLowerCase().includes(complaint)) {
          return sentence.split('.')[0]
        }
      }
    }
    
    return 'Quadro clínico há 3 dias'
  },

  extractVitalSigns(analysis) {
    return {
      bp: analysis.metrics.find(m => m.includes('mmHg')) || '120/80 mmHg',
      hr: analysis.metrics.find(m => m.includes('bpm')) || '80 bpm',
      temp: analysis.metrics.find(m => m.includes('°C')) || '36.5°C',
      rr: analysis.metrics.find(m => m.includes('irpm')) || '16 irpm',
      spo2: analysis.metrics.find(m => m.includes('SpO2')) || 'SpO2 98%'
    }
  },

  generateClinicalCase(clinicalData, concepts) {
    const { age, gender, mainComplaint, duration, vitalSigns } = clinicalData
    
    return `Paciente de ${age}, sexo ${gender}, procura atendimento com queixa de ${mainComplaint} há ${duration}. ` +
           `Ao exame: PA ${vitalSigns.bp}, FC ${vitalSigns.hr}, Tax ${vitalSigns.temp}, ${vitalSigns.spo2}. ` +
           `${concepts[0] ? `Refere ainda ${concepts[0].term}.` : ''}`
  },

  generateInterpretationAlternatives(dataPoint, context, analysis) {
    const alternatives = [
      `Valor dentro dos limites da normalidade para a faixa etária`,
      `Achado compatível com processo patológico em evolução`,
      `Resultado indicativo de necessidade de investigação adicional`,
      `Alteração significativa que requer intervenção imediata`,
      `Variação fisiológica sem relevância clínica`
    ]
    
    // Ajusta a primeira alternativa baseada no contexto
    if (context.includes('elevad') || context.includes('aumentad')) {
      alternatives[0] = `Elevação significativa indicando processo agudo`
    } else if (context.includes('reduzi') || context.includes('diminuí')) {
      alternatives[0] = `Redução importante sugerindo comprometimento funcional`
    }
    
    return alternatives
  },

  generateComparisonAlternatives(concept1, concept2, analysis) {
    return [
      `${concept1.term} apresenta maior eficácia que ${concept2.term} em estudos controlados`,
      `Ambos são equivalentes em termos de eficácia e segurança`,
      `${concept2.term} é superior em pacientes com comorbidades`,
      `A escolha depende do perfil individual do paciente`,
      `${concept1.term} está contraindicado quando ${concept2.term} é utilizado`
    ]
  },

  generateAssociationAlternatives(associations, analysis) {
    return [
      'Relação direta de causa e efeito estabelecida',
      'Associação estatística sem causalidade comprovada',
      'Correlação inversa documentada em estudos',
      'Ausência de relação significativa',
      'Associação controversa na literatura atual'
    ]
  },

  generateCauseEffectAlternatives(causalRelation, concepts, analysis) {
    return [
      'Mecanismo mediado por receptores específicos',
      'Processo inflamatório com liberação de citocinas',
      'Alteração metabólica com repercussão sistêmica',
      'Disfunção endotelial e ativação plaquetária',
      'Mecanismo multifatorial ainda não completamente elucidado'
    ]
  },

  findAssociations(concepts, analysis) {
    const associations = []
    
    // Procura por padrões de associação nas sentenças
    analysis.sentences.forEach(sentence => {
      if (sentence.includes('associa') || sentence.includes('relaciona') || sentence.includes('correlaciona')) {
        associations.push(sentence)
      }
    })
    
    return associations
  },

  /**
   * Gera uma questão a partir de um template
   * @param {object} template - Template da questão
   * @param {object} options - Opções de geração
   * @returns {object} Questão gerada
   */
  generateQuestionFromTemplate(template, options) {
    const question = {
      ...template,
      difficulty: options.difficulty,
      correctAnswer: Math.floor(Math.random() * 5) // 0-4 for A-E
    }

    // Add explanation if requested
    if (options.includeExplanation) {
      question.explanation = this.generateExplanation(question)
    }

    // Add references if requested
    if (options.includeReferences) {
      question.references = this.generateReferences(question)
    }

    // Add image if requested and applicable
    if (options.includeImages && Math.random() > 0.7) {
      question.image = this.generateMockImage(question)
    }

    return question
  },

  /**
   * Gera explicação para uma questão
   * @param {object} question - Questão
   * @returns {string} Explicação
   */
  generateExplanation(question) {
    const explanations = {
      facil: `A resposta correta é a alternativa ${String.fromCharCode(65 + question.correctAnswer)}. Este conceito é fundamental e amplamente abordado na literatura médica básica.`,
      medio: `A alternativa ${String.fromCharCode(65 + question.correctAnswer)} está correta. É importante compreender as nuances deste tópico, pois ele é frequentemente cobrado em provas de residência médica.`,
      dificil: `A resposta correta é ${String.fromCharCode(65 + question.correctAnswer)}. Esta questão exige conhecimento aprofundado e capacidade de integração de conceitos complexos.`,
      expert: `Alternativa ${String.fromCharCode(65 + question.correctAnswer)}. Esta questão de nível avançado requer não apenas conhecimento teórico, mas também experiência clínica e raciocínio diagnóstico refinado.`
    }
    
    return explanations[question.difficulty] || explanations.medio
  },

  /**
   * Gera referências para uma questão
   * @param {object} question - Questão
   * @returns {Array} Referências
   */
  generateReferences(question) {
    return [
      'Harrison\'s Principles of Internal Medicine, 21st Edition',
      'Goldman-Cecil Medicine, 26th Edition',
      'UpToDate - Clinical Decision Support',
      'New England Journal of Medicine - Recent Articles'
    ].slice(0, Math.floor(Math.random() * 3) + 1)
  },

  /**
   * Gera uma imagem mock para questão
   * @param {object} question - Questão
   * @returns {string} URL da imagem
   */
  generateMockImage(question) {
    // Em produção, isso retornaria URLs reais de imagens médicas
    return `/api/placeholder/400/300?text=${encodeURIComponent(question.topic || 'Medical Image')}`
  },

  /**
   * Templates de questões para Residência Médica
   * @returns {Array} Array de templates
   */
  getResidenciaTemplates() {
    return [
      {
        text: 'Paciente de 45 anos, sexo masculino, apresenta dor torácica opressiva há 2 horas, irradiada para membro superior esquerdo. ECG mostra supradesnivelamento de ST em DII, DIII e aVF. Qual o diagnóstico mais provável?',
        alternatives: [
          'Infarto agudo do miocárdio de parede anterior',
          'Infarto agudo do miocárdio de parede inferior',
          'Angina instável',
          'Pericardite aguda',
          'Dissecção aórtica'
        ],
        topic: 'Cardiologia'
      },
      {
        text: 'Mulher de 28 anos com amenorreia há 8 semanas, beta-hCG positivo, apresenta sangramento vaginal e dor pélvica unilateral intensa. PA: 90/60 mmHg, FC: 120 bpm. Qual a conduta inicial mais apropriada?',
        alternatives: [
          'Solicitar ultrassonografia pélvica ambulatorial',
          'Prescrever progesterona e repouso domiciliar',
          'Acesso venoso calibroso e reposição volêmica',
          'Curetagem uterina imediata',
          'Antibioticoterapia de amplo espectro'
        ],
        topic: 'Ginecologia e Obstetrícia'
      },
      {
        text: 'Criança de 3 anos apresenta febre há 5 dias, exantema polimorfo, hiperemia conjuntival bilateral, língua em framboesa e edema de mãos. Qual o diagnóstico e a principal complicação a ser prevenida?',
        alternatives: [
          'Escarlatina - glomerulonefrite',
          'Doença de Kawasaki - aneurisma coronariano',
          'Sarampo - panencefalite esclerosante',
          'Rubéola - artrite crônica',
          'Eritema infeccioso - anemia aplástica'
        ],
        topic: 'Pediatria'
      },
      {
        text: 'Paciente diabético tipo 2, em uso de metformina e glibenclamida, apresenta confusão mental, sudorese e tremores. Glicemia capilar: 45 mg/dL. Após administração de glicose IV, qual a melhor conduta?',
        alternatives: [
          'Alta hospitalar após melhora dos sintomas',
          'Suspender apenas a glibenclamida',
          'Internação para observação por 24-48h',
          'Trocar metformina por insulina',
          'Solicitar tomografia de crânio'
        ],
        topic: 'Endocrinologia'
      },
      {
        text: 'Homem de 60 anos, tabagista, com tosse produtiva matinal há 3 meses e dois episódios de expectoração hemoptoica. Radiografia de tórax mostra nódulo de 3cm em lobo superior direito. Qual o próximo exame?',
        alternatives: [
          'Broncoscopia com biópsia',
          'Tomografia computadorizada de tórax',
          'PET-CT de corpo inteiro',
          'Punção transtorácica guiada',
          'Citologia de escarro'
        ],
        topic: 'Pneumologia'
      }
    ]
  },

  /**
   * Templates de questões para Revalida
   * @returns {Array} Array de templates
   */
  getRevalidaTemplates() {
    return [
      {
        text: 'No contexto do Sistema Único de Saúde (SUS) brasileiro, qual princípio determina que todos os cidadãos têm direito ao acesso às ações e serviços de saúde, sem discriminação?',
        alternatives: [
          'Integralidade',
          'Universalidade',
          'Equidade',
          'Descentralização',
          'Hierarquização'
        ],
        topic: 'Saúde Pública'
      },
      {
        text: 'Gestante de 32 semanas apresenta pressão arterial de 160/110 mmHg, proteinúria de 3+ e cefaleia intensa. Qual medicamento anti-hipertensivo é contraindicado nesta situação?',
        alternatives: [
          'Metildopa',
          'Hidralazina',
          'Nifedipina',
          'Enalapril',
          'Labetalol'
        ],
        topic: 'Obstetrícia'
      },
      {
        text: 'Paciente com cirrose hepática apresenta confusão mental, asterixis e hálito adocicado. Amônia sérica elevada. Qual o tratamento de primeira linha?',
        alternatives: [
          'Neomicina oral',
          'Lactulose',
          'Rifaximina',
          'Metronidazol',
          'L-ornitina L-aspartato'
        ],
        topic: 'Gastroenterologia'
      },
      {
        text: 'Criança de 2 anos com história de infecções respiratórias recorrentes e diarreia crônica. Teste do suor: 75 mEq/L. Qual o diagnóstico?',
        alternatives: [
          'Asma brônquica',
          'Imunodeficiência primária',
          'Fibrose cística',
          'Doença celíaca',
          'Síndrome de má absorção'
        ],
        topic: 'Pediatria'
      },
      {
        text: 'Segundo o Código de Ética Médica brasileiro, em qual situação o médico pode quebrar o sigilo profissional?',
        alternatives: [
          'A pedido do cônjuge do paciente',
          'Por solicitação do empregador',
          'Em caso de notificação compulsória de doença',
          'Para fins de pesquisa científica',
          'Mediante pagamento de honorários'
        ],
        topic: 'Ética Médica'
      }
    ]
  },

  /**
   * Templates de questões para ENEM
   * @returns {Array} Array de templates
   */
  getEnemTemplates() {
    return [
      {
        text: 'O sistema cardiovascular humano é composto por um órgão central bombeador e uma rede de vasos. Sobre a circulação sanguínea, é correto afirmar que:',
        alternatives: [
          'As artérias sempre transportam sangue oxigenado',
          'O sangue venoso é sempre rico em CO2',
          'A circulação pulmonar leva sangue oxigenado aos pulmões',
          'As veias pulmonares transportam sangue rico em O2',
          'O átrio esquerdo recebe sangue da circulação sistêmica'
        ],
        topic: 'Fisiologia'
      },
      {
        text: 'A descoberta dos antibióticos revolucionou a medicina. Sobre o uso racional desses medicamentos, analise a afirmação correta:',
        alternatives: [
          'Antibióticos são eficazes contra todas as infecções',
          'Devem ser interrompidos assim que os sintomas melhoram',
          'O uso inadequado pode levar à resistência bacteriana',
          'São indicados para tratamento de gripes e resfriados',
          'Quanto maior a dose, mais rápida a cura'
        ],
        topic: 'Farmacologia'
      },
      {
        text: 'O DNA é a molécula responsável pela hereditariedade. Em relação à sua estrutura e função:',
        alternatives: [
          'É formado por uma fita simples de nucleotídeos',
          'As bases nitrogenadas se pareiam aleatoriamente',
          'A timina pareia com a guanina',
          'A adenina pareia com a timina',
          'Está presente apenas no núcleo celular'
        ],
        topic: 'Biologia Molecular'
      },
      {
        text: 'As vacinas são importantes ferramentas de prevenção. Seu mecanismo de ação baseia-se em:',
        alternatives: [
          'Destruir diretamente os agentes patogênicos',
          'Estimular a produção de antibióticos naturais',
          'Induzir memória imunológica',
          'Fortalecer as barreiras físicas do corpo',
          'Aumentar a temperatura corporal'
        ],
        topic: 'Imunologia'
      },
      {
        text: 'O Sistema Nervoso coordena as funções do organismo. Sobre os neurônios e a transmissão nervosa:',
        alternatives: [
          'O impulso nervoso é sempre bidirecional',
          'Os dendritos transmitem impulsos para fora do neurônio',
          'A sinapse é o local de contato entre neurônios',
          'O axônio recebe os estímulos nervosos',
          'A mielina diminui a velocidade de condução'
        ],
        topic: 'Neurociência'
      }
    ]
  },

  /**
   * Templates de questões para Concurso Público
   * @returns {Array} Array de templates
   */
  getConcursoTemplates() {
    return [
      {
        text: 'De acordo com a Lei 8.080/90, compete à direção nacional do SUS, EXCETO:',
        alternatives: [
          'Formular, avaliar e apoiar políticas de alimentação e nutrição',
          'Participar na formulação e implementação de políticas de saneamento básico',
          'Executar serviços de vigilância sanitária de portos e aeroportos',
          'Gerir laboratórios públicos de saúde e hemocentros',
          'Estabelecer normas e executar vigilância sanitária'
        ],
        topic: 'Legislação SUS'
      },
      {
        text: 'Na Estratégia Saúde da Família, qual é a população máxima recomendada por equipe?',
        alternatives: [
          '2.000 pessoas',
          '3.000 pessoas',
          '4.000 pessoas',
          '5.000 pessoas',
          '6.000 pessoas'
        ],
        topic: 'Atenção Primária'
      },
      {
        text: 'Segundo a Política Nacional de Humanização (PNH), é dispositivo de humanização:',
        alternatives: [
          'Prontuário eletrônico',
          'Protocolo clínico',
          'Acolhimento com classificação de risco',
          'Prescrição médica digitalizada',
          'Sistema de agendamento online'
        ],
        topic: 'Políticas de Saúde'
      },
      {
        text: 'No contexto da Vigilância Epidemiológica, qual doença NÃO é de notificação compulsória imediata?',
        alternatives: [
          'Cólera',
          'Febre amarela',
          'Tuberculose',
          'Botulismo',
          'Raiva humana'
        ],
        topic: 'Vigilância em Saúde'
      },
      {
        text: 'O financiamento do SUS é responsabilidade:',
        alternatives: [
          'Exclusiva da União',
          'Exclusiva dos Estados',
          'Exclusiva dos Municípios',
          'Das três esferas de governo',
          'Da iniciativa privada'
        ],
        topic: 'Gestão do SUS'
      }
    ]
  },

  /**
   * Templates de questões para Board Exam
   * @returns {Array} Array de templates
   */
  getBoardTemplates() {
    return [
      {
        text: 'A 45-year-old man presents with sudden onset chest pain and dyspnea. D-dimer is elevated. Which imaging study would be most appropriate to confirm pulmonary embolism?',
        alternatives: [
          'Chest X-ray',
          'CT pulmonary angiography',
          'Ventilation-perfusion scan',
          'Echocardiography',
          'MRI chest'
        ],
        topic: 'Emergency Medicine'
      },
      {
        text: 'In a patient with chronic kidney disease, which medication requires dose adjustment?',
        alternatives: [
          'Atorvastatin',
          'Metformin',
          'Warfarin',
          'Levothyroxine',
          'Omeprazole'
        ],
        topic: 'Nephrology'
      },
      {
        text: 'A neonate presents with projectile vomiting at 3 weeks of age. Physical exam reveals a palpable "olive" in the epigastrium. What is the diagnosis?',
        alternatives: [
          'Gastroesophageal reflux',
          'Pyloric stenosis',
          'Intussusception',
          'Malrotation with volvulus',
          'Duodenal atresia'
        ],
        topic: 'Pediatric Surgery'
      },
      {
        text: 'Which tumor marker is most specific for hepatocellular carcinoma?',
        alternatives: [
          'CEA',
          'CA 19-9',
          'AFP',
          'CA 125',
          'PSA'
        ],
        topic: 'Oncology'
      },
      {
        text: 'In the management of acute stroke, the time window for IV thrombolysis with tPA is:',
        alternatives: [
          'Within 1 hour',
          'Within 3 hours',
          'Within 4.5 hours',
          'Within 6 hours',
          'Within 24 hours'
        ],
        topic: 'Neurology'
      }
    ]
  },

  /**
   * Salva simulados no servidor
   * @param {Array} exams - Simulados para salvar
   * @returns {Promise<boolean>} Sucesso da operação
   */
  async saveExams(exams) {
    try {
      await axios.post(`${API_URL}/api/questions/exams`, {
        exams
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      return true
    } catch (error) {
      console.error('Erro ao salvar simulados:', error)
      return false
    }
  },

  /**
   * Carrega simulados do servidor
   * @returns {Promise<Array>} Lista de simulados
   */
  async loadExams() {
    try {
      const response = await axios.get(`${API_URL}/api/questions/exams`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      return response.data.exams || []
    } catch (error) {
      console.error('Erro ao carregar simulados:', error)
      return []
    }
  },

  /**
   * Atualiza estatísticas de desempenho
   * @param {string} examId - ID do simulado
   * @param {object} results - Resultados do simulado
   * @returns {Promise<void>}
   */
  async updateStatistics(examId, results) {
    try {
      await axios.post(`${API_URL}/api/questions/statistics`, {
        examId,
        results,
        completedAt: new Date()
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
    } catch (error) {
      console.error('Erro ao atualizar estatísticas:', error)
    }
  },

  /**
   * Exporta questões para PDF
   * @param {Array} questions - Questões para exportar
   * @param {object} options - Opções de exportação
   * @returns {Promise<Blob>} PDF blob
   */
  async exportToPDF(questions, options = {}) {
    try {
      const response = await axios.post(`${API_URL}/api/questions/export-pdf`, {
        questions,
        options
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        responseType: 'blob'
      })
      
      return response.data
    } catch (error) {
      console.error('Erro ao exportar PDF:', error)
      throw error
    }
  }
}

export default questionsService