// AI Assistant Service for real-time AI responses
import axios from 'axios';

class AIAssistantService {
  constructor() {
    // You can use OpenAI, Anthropic, or any other AI API
    // For now, I'll set up the structure for OpenAI API
    this.apiKey = process.env.VUE_APP_OPENAI_API_KEY || '';
    this.apiUrl = 'https://api.openai.com/v1/chat/completions';
    
    // Alternative: Use your backend as a proxy to keep API key secure
    this.backendUrl = process.env.VUE_APP_API_URL || 'http://localhost:8000';
  }

  // Method to get AI response from OpenAI
  async getAIResponse(userMessage, context = '') {
    try {
      // Option 1: Direct API call (not recommended for production - exposes API key)
      if (this.apiKey && process.env.NODE_ENV === 'development') {
        const response = await axios.post(
          this.apiUrl,
          {
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: `Você é um assistente de estudos especializado em técnicas de aprendizagem, 
                         organização de estudos e métodos de memorização. Responda em português brasileiro 
                         de forma clara, útil e motivadora. Forneça dicas práticas e exemplos quando possível.
                         ${context ? `Contexto adicional: ${context}` : ''}`
              },
              {
                role: 'user',
                content: userMessage
              }
            ],
            temperature: 0.7,
            max_tokens: 500
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.apiKey}`
            }
          }
        );

        return response.data.choices[0].message.content;
      }

      // Option 2: Use backend proxy (recommended for production)
      const token = localStorage.getItem('token');
      const headers = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const response = await axios.post(`${this.backendUrl}/api/ai-assistant`, {
        message: userMessage,
        context: context
      }, { headers });

      return response.data.response;

    } catch (error) {
      console.error('AI Assistant Error:', error);
      
      // Fallback to a helpful error message
      if (error.response?.status === 429) {
        return 'Desculpe, muitas requisições foram feitas. Por favor, aguarde um momento e tente novamente.';
      } else if (error.response?.status === 401) {
        return 'Erro de autenticação com o serviço de IA. Por favor, verifique as configurações.';
      } else {
        return 'Desculpe, não consegui processar sua pergunta no momento. Por favor, tente novamente ou reformule sua questão.';
      }
    }
  }

  // Method to get AI response using a free alternative (for demo purposes)
  async getAIResponseDemo(userMessage) {
    // This is a simplified response system for demonstration
    // In production, you would use a real AI API
    
    const lowerMessage = userMessage.toLowerCase();
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // More intelligent keyword matching and response generation
    const responses = {
      // Study planning
      'cronograma|plano|organizar|planejamento': {
        keywords: ['semanal', 'diário', 'mensal', 'personalizado'],
        base: 'Vou criar um plano de estudos personalizado para você.',
        details: {
          'semanal': 'Para um cronograma semanal eficiente:\n\n1. **Avalie seu tempo disponível**: Quantas horas por dia você pode dedicar?\n2. **Distribua as matérias**: Alterne entre fáceis e difíceis\n3. **Defina blocos de estudo**: 50-90 minutos por sessão\n4. **Inclua revisões**: 20% do tempo para revisar\n5. **Seja flexível**: Deixe espaços para ajustes\n\nQuer que eu crie um modelo específico para sua rotina?',
          'default': 'Para criar um cronograma eficaz:\n\n1. **Mapeie suas matérias e prioridades**\n2. **Identifique seus horários mais produtivos**\n3. **Use a técnica de blocos de tempo**\n4. **Inclua pausas estratégicas**\n5. **Reserve tempo para revisão**\n\nQual é sua maior dificuldade na organização dos estudos?'
        }
      },
      
      // Memory techniques
      'memorizar|memória|decorar|lembrar|esquecer': {
        keywords: ['técnica', 'método', 'dicas', 'melhorar'],
        base: 'Existem várias técnicas comprovadas de memorização.',
        details: {
          'técnica': 'Técnicas avançadas de memorização:\n\n**1. Palácio da Memória**: Associe informações a locais familiares\n**2. Chunking**: Agrupe informações em blocos menores\n**3. Mnemonics**: Crie acrônimos e associações\n**4. Dual Coding**: Combine visual e verbal\n**5. Elaborative Rehearsal**: Conecte com conhecimento prévio\n\nQual tipo de conteúdo você precisa memorizar?',
          'default': 'Para melhorar sua memorização:\n\n**Curto prazo**:\n• Use repetição espaçada (1h, 1d, 3d, 7d, 30d)\n• Crie mapas mentais coloridos\n• Ensine o conteúdo para alguém\n\n**Longo prazo**:\n• Conecte com experiências pessoais\n• Use múltiplos sentidos ao estudar\n• Durma bem (consolida memórias)\n\nQual sua maior dificuldade com memorização?'
        }
      },
      
      // Focus and concentration
      'foco|concentração|distração|atenção': {
        keywords: ['melhorar', 'técnica', 'problema', 'ajuda'],
        base: 'Vamos trabalhar sua concentração e foco.',
        details: {
          'melhorar': 'Para turbinar seu foco:\n\n**Ambiente**:\n• Lugar fixo de estudo\n• Celular no modo avião\n• Fones com ruído branco\n\n**Técnicas**:\n• Pomodoro: 25min foco + 5min pausa\n• Deep Work: 90min sem interrupções\n• Timeboxing: horários definidos\n\n**Hábitos**:\n• Medite 10min antes de estudar\n• Exercícios físicos regulares\n• Alimentação balanceada\n\nQual seu maior desafio com concentração?',
          'default': 'Estratégias para melhor concentração:\n\n1. **Elimine distrações**: Ambiente limpo e organizado\n2. **Use a regra dos 2 minutos**: Comece pequeno\n3. **Pratique mindfulness**: 5min de respiração\n4. **Ciclos de energia**: Estude nos horários de pico\n5. **Recompensas**: Celebre pequenas vitórias\n\nEm que momento do dia você se concentra melhor?'
        }
      }
    };
    
    // Find matching response category
    for (const [pattern, config] of Object.entries(responses)) {
      if (new RegExp(pattern).test(lowerMessage)) {
        // Check for specific keywords within the category
        let selectedResponse = config.details.default;
        
        for (const keyword of config.keywords) {
          if (lowerMessage.includes(keyword)) {
            selectedResponse = config.details[keyword] || config.details.default;
            break;
          }
        }
        
        return selectedResponse;
      }
    }
    
    // If no pattern matches, provide a general helpful response
    return `Entendi sua pergunta sobre "${userMessage}". 

Como seu assistente de estudos, posso ajudar com:

📚 **Organização**: Cronogramas e planos de estudo
🧠 **Memorização**: Técnicas científicas comprovadas  
🎯 **Foco**: Métodos para concentração profunda
⏰ **Produtividade**: Gestão de tempo e energia
💪 **Motivação**: Estratégias para manter consistência
😰 **Ansiedade**: Técnicas de relaxamento

Por favor, seja mais específico sobre o que você precisa, e eu poderei dar conselhos mais direcionados!`;
  }
}

export default new AIAssistantService();