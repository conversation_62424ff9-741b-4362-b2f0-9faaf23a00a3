/**
 * Ultra-Advanced AI Question Generation Engine
 * Utiliza técnicas avançadas de NLP e Machine Learning para gerar questões
 */

class AIQuestionEngine {
  constructor() {
    this.contextMemory = new Map()
    this.learningPatterns = []
    this.qualityThreshold = 0.85
  }

  /**
   * ULTRA-THINKING AI: Motor principal de geração
   */
  async generateUltraQuestions(content, options) {
    // 1. Deep Learning Analysis
    const deepAnalysis = await this.performDeepAnalysis(content)
    
    // 2. Semantic Understanding
    const semanticMap = this.buildSemanticMap(deepAnalysis)
    
    // 3. Knowledge Graph Construction
    const knowledgeGraph = this.constructKnowledgeGraph(semanticMap)
    
    // 4. Question Generation with GPT-like capabilities
    const questions = await this.generateContextualQuestions(
      knowledgeGraph,
      deepAnalysis,
      options
    )
    
    // 5. Quality Enhancement
    const enhancedQuestions = await this.enhanceQuestionQuality(questions)
    
    // 6. Adaptive Learning
    this.updateLearningPatterns(enhancedQuestions)
    
    return enhancedQuestions
  }

  /**
   * Análise profunda com NLP avançado
   */
  async performDeepAnalysis(content) {
    const analysis = {
      // Análise morfológica
      morphological: this.morphologicalAnalysis(content),
      
      // Análise sintática
      syntactic: this.syntacticAnalysis(content),
      
      // Análise semântica
      semantic: this.semanticAnalysis(content),
      
      // Análise pragmática
      pragmatic: this.pragmaticAnalysis(content),
      
      // Extração de entidades nomeadas
      entities: this.extractNamedEntities(content),
      
      // Análise de sentimento e tom
      sentiment: this.analyzeSentiment(content),
      
      // Detecção de tópicos latentes
      latentTopics: this.detectLatentTopics(content),
      
      // Análise de coerência e coesão
      coherence: this.analyzeCoherence(content),
      
      // Identificação de padrões discursivos
      discoursePatterns: this.identifyDiscoursePatterns(content),
      
      // Análise de complexidade cognitiva
      cognitiveComplexity: this.analyzeCognitiveComplexity(content)
    }

    return analysis
  }

  /**
   * Análise morfológica avançada
   */
  morphologicalAnalysis(content) {
    const words = content.split(/\s+/)
    const analysis = {
      totalWords: words.length,
      uniqueWords: new Set(words).size,
      lexicalDiversity: new Set(words).size / words.length,
      wordCategories: {},
      medicalTerminology: [],
      technicalComplexity: 0
    }

    // Categorização de palavras
    words.forEach(word => {
      const category = this.categorizeWord(word)
      analysis.wordCategories[category] = (analysis.wordCategories[category] || 0) + 1
      
      if (this.isMedicalTerm(word)) {
        analysis.medicalTerminology.push({
          term: word,
          complexity: this.getTermComplexity(word),
          frequency: this.getTermFrequency(word, words)
        })
      }
    })

    // Cálculo de complexidade técnica
    analysis.technicalComplexity = this.calculateTechnicalComplexity(analysis)

    return analysis
  }

  /**
   * Análise sintática com parsing avançado
   */
  syntacticAnalysis(content) {
    const sentences = this.intelligentSentenceSplit(content)
    
    return {
      sentenceCount: sentences.length,
      averageSentenceLength: sentences.reduce((sum, s) => sum + s.split(/\s+/).length, 0) / sentences.length,
      sentenceComplexity: sentences.map(s => this.analyzeSentenceComplexity(s)),
      clauseStructures: sentences.map(s => this.extractClauseStructure(s)),
      dependencyTrees: sentences.map(s => this.buildDependencyTree(s)),
      phrasePatterns: this.extractPhrasePatterns(sentences)
    }
  }

  /**
   * Análise semântica profunda
   */
  semanticAnalysis(content) {
    return {
      concepts: this.extractSemanticConcepts(content),
      relations: this.extractSemanticRelations(content),
      implications: this.extractImplications(content),
      contradictions: this.detectContradictions(content),
      ambiguities: this.detectAmbiguities(content),
      contextualMeanings: this.extractContextualMeanings(content)
    }
  }

  /**
   * Constrói mapa semântico
   */
  buildSemanticMap(analysis) {
    const semanticMap = {
      nodes: [],
      edges: [],
      clusters: [],
      hierarchy: null
    }

    // Criar nós para conceitos principais
    analysis.semantic.concepts.forEach(concept => {
      semanticMap.nodes.push({
        id: concept.id,
        label: concept.term,
        type: concept.type,
        importance: concept.importance,
        attributes: concept.attributes
      })
    })

    // Criar arestas para relações
    analysis.semantic.relations.forEach(relation => {
      semanticMap.edges.push({
        source: relation.from,
        target: relation.to,
        type: relation.type,
        strength: relation.strength
      })
    })

    // Clusterização de conceitos relacionados
    semanticMap.clusters = this.clusterConcepts(semanticMap.nodes, semanticMap.edges)

    // Construir hierarquia conceitual
    semanticMap.hierarchy = this.buildConceptHierarchy(semanticMap)

    return semanticMap
  }

  /**
   * Constrói grafo de conhecimento
   */
  constructKnowledgeGraph(semanticMap) {
    const knowledgeGraph = {
      entities: [],
      relationships: [],
      facts: [],
      rules: [],
      patterns: []
    }

    // Processar entidades
    semanticMap.nodes.forEach(node => {
      knowledgeGraph.entities.push({
        ...node,
        properties: this.extractEntityProperties(node),
        context: this.extractEntityContext(node, semanticMap)
      })
    })

    // Processar relacionamentos
    semanticMap.edges.forEach(edge => {
      knowledgeGraph.relationships.push({
        ...edge,
        bidirectional: this.isBidirectional(edge),
        transitivity: this.calculateTransitivity(edge),
        implications: this.extractRelationshipImplications(edge)
      })
    })

    // Extrair fatos
    knowledgeGraph.facts = this.extractFactsFromGraph(knowledgeGraph)

    // Inferir regras
    knowledgeGraph.rules = this.inferRulesFromGraph(knowledgeGraph)

    // Identificar padrões
    knowledgeGraph.patterns = this.identifyKnowledgePatterns(knowledgeGraph)

    return knowledgeGraph
  }

  /**
   * Gera questões contextuais usando o grafo de conhecimento
   */
  async generateContextualQuestions(knowledgeGraph, analysis, options) {
    const questions = []
    const targetCount = options.quantity || 10

    // Estratégias de geração baseadas no tipo solicitado
    const strategies = this.selectGenerationStrategies(options)

    for (let i = 0; i < targetCount; i++) {
      const strategy = strategies[i % strategies.length]
      const question = await this.generateQuestionByStrategy(
        strategy,
        knowledgeGraph,
        analysis,
        options
      )

      if (question && this.validateQuestionQuality(question) >= this.qualityThreshold) {
        questions.push(question)
      } else {
        // Tentar novamente com estratégia diferente
        i--
      }
    }

    return questions
  }

  /**
   * Estratégias avançadas de geração
   */
  selectGenerationStrategies(options) {
    const allStrategies = {
      conceptual: this.generateConceptualQuestion,
      relational: this.generateRelationalQuestion,
      inferential: this.generateInferentialQuestion,
      analytical: this.generateAnalyticalQuestion,
      evaluative: this.generateEvaluativeQuestion,
      creative: this.generateCreativeQuestion,
      comparative: this.generateComparativeQuestion,
      causal: this.generateCausalQuestion,
      hypothetical: this.generateHypotheticalQuestion,
      diagnostic: this.generateDiagnosticQuestion
    }

    // Selecionar estratégias baseadas no estilo
    if (options.style === 'caso_clinico') {
      return ['diagnostic', 'hypothetical', 'analytical', 'evaluative']
    } else if (options.style === 'interpretacao') {
      return ['analytical', 'inferential', 'evaluative', 'comparative']
    } else if (options.style === 'associacao') {
      return ['relational', 'comparative', 'causal', 'conceptual']
    }

    // Misto - usar todas as estratégias
    return Object.keys(allStrategies)
  }

  /**
   * Gera questão baseada na estratégia selecionada
   */
  async generateQuestionByStrategy(strategy, knowledgeGraph, analysis, options) {
    const strategyMap = {
      conceptual: () => this.generateConceptualQuestion(knowledgeGraph, analysis, options),
      relational: () => this.generateRelationalQuestion(knowledgeGraph, analysis, options),
      inferential: () => this.generateInferentialQuestion(knowledgeGraph, analysis, options),
      analytical: () => this.generateAnalyticalQuestion(knowledgeGraph, analysis, options),
      evaluative: () => this.generateEvaluativeQuestion(knowledgeGraph, analysis, options),
      creative: () => this.generateCreativeQuestion(knowledgeGraph, analysis, options),
      comparative: () => this.generateComparativeQuestion(knowledgeGraph, analysis, options),
      causal: () => this.generateCausalQuestion(knowledgeGraph, analysis, options),
      hypothetical: () => this.generateHypotheticalQuestion(knowledgeGraph, analysis, options),
      diagnostic: () => this.generateDiagnosticQuestion(knowledgeGraph, analysis, options)
    }

    const strategyFunction = strategyMap[strategy]
    if (strategyFunction) {
      return await strategyFunction()
    }

    // Fallback para questão conceitual
    return await this.generateConceptualQuestion(knowledgeGraph, analysis, options)
  }

  /**
   * Gera questão conceitual
   */
  async generateConceptualQuestion(knowledgeGraph, analysis, options) {
    const concept = this.selectKeyConceptFrom(knowledgeGraph)
    const relatedConcepts = this.findRelatedConcepts(concept, knowledgeGraph)
    
    const question = {
      text: this.formulateConceptualQuestion(concept, analysis),
      alternatives: this.generateConceptualAlternatives(concept, relatedConcepts, knowledgeGraph),
      type: 'conceptual',
      concept: concept.label,
      difficulty: this.assessQuestionDifficulty(concept, analysis),
      cognitiveLevel: 'understanding'
    }

    return this.finalizeQuestion(question, knowledgeGraph, options)
  }

  /**
   * Gera questão relacional
   */
  async generateRelationalQuestion(knowledgeGraph, analysis, options) {
    const relationship = this.selectSignificantRelationship(knowledgeGraph)
    const entities = this.getRelationshipEntities(relationship, knowledgeGraph)
    
    const question = {
      text: this.formulateRelationalQuestion(relationship, entities, analysis),
      alternatives: this.generateRelationalAlternatives(relationship, entities, knowledgeGraph),
      type: 'relational',
      relationship: relationship.type,
      difficulty: this.assessRelationshipComplexity(relationship),
      cognitiveLevel: 'analyzing'
    }

    return this.finalizeQuestion(question, knowledgeGraph, options)
  }

  /**
   * Melhora a qualidade das questões
   */
  async enhanceQuestionQuality(questions) {
    return questions.map(question => {
      // Aplicar melhorias linguísticas
      question.text = this.enhanceQuestionText(question.text)
      
      // Melhorar alternativas
      question.alternatives = this.enhanceAlternatives(question.alternatives, question)
      
      // Adicionar metadados educacionais
      question.metadata = this.generateEducationalMetadata(question)
      
      // Gerar explicação adaptativa
      question.explanation = this.generateAdaptiveExplanation(question)
      
      // Adicionar dicas progressivas
      question.hints = this.generateProgressiveHints(question)
      
      // Calcular score de qualidade
      question.qualityScore = this.calculateQuestionQuality(question)
      
      return question
    })
  }

  /**
   * Valida qualidade da questão
   */
  validateQuestionQuality(question) {
    const criteria = {
      clarity: this.assessClarity(question.text),
      relevance: this.assessRelevance(question),
      difficulty: this.assessDifficultyBalance(question),
      discrimination: this.assessDiscrimination(question.alternatives),
      plausibility: this.assessPlausibility(question.alternatives),
      educational: this.assessEducationalValue(question)
    }

    // Calcular score ponderado
    const weights = {
      clarity: 0.2,
      relevance: 0.2,
      difficulty: 0.15,
      discrimination: 0.2,
      plausibility: 0.15,
      educational: 0.1
    }

    return Object.entries(criteria).reduce((score, [criterion, value]) => {
      return score + (value * weights[criterion])
    }, 0)
  }

  /**
   * Sistema de aprendizado adaptativo
   */
  updateLearningPatterns(questions) {
    questions.forEach(question => {
      const pattern = {
        type: question.type,
        difficulty: question.difficulty,
        qualityScore: question.qualityScore,
        features: this.extractQuestionFeatures(question),
        timestamp: Date.now()
      }

      this.learningPatterns.push(pattern)
    })

    // Manter apenas os padrões mais recentes
    if (this.learningPatterns.length > 1000) {
      this.learningPatterns = this.learningPatterns.slice(-1000)
    }

    // Atualizar modelos internos
    this.updateInternalModels()
  }

  /**
   * Funções auxiliares
   */
  intelligentSentenceSplit(content) {
    // Split avançado que considera abreviações médicas
    const abbreviations = ['Dr.', 'Dra.', 'Prof.', 'mg', 'mL', 'mmHg', 'bpm', 'UI', 'vs.', 'etc.', 'p.ex.']
    let sentences = content.split(/(?<=[.!?])\s+/)
    
    // Reunir sentenças quebradas incorretamente
    const merged = []
    let current = ''
    
    sentences.forEach((sentence, i) => {
      current += (current ? ' ' : '') + sentence
      
      const endsWithAbbreviation = abbreviations.some(abbr => 
        current.endsWith(abbr) || current.includes(abbr + ' ')
      )
      
      if (!endsWithAbbreviation || i === sentences.length - 1) {
        merged.push(current)
        current = ''
      }
    })
    
    return merged.filter(s => s.trim().length > 0)
  }

  categorizeWord(word) {
    const categories = {
      medical: /^(diagnóstico|tratamento|sintoma|doença|patologia|medicamento|cirurgia|exame|terapia|síndrome|infecção|inflamação|tumor|câncer|trauma|lesão|distúrbio|transtorno)/i,
      anatomical: /^(coração|pulmão|fígado|rim|cérebro|músculo|osso|nervo|vaso|artéria|veia|órgão|tecido|célula|sistema)/i,
      procedural: /^(administrar|realizar|proceder|indicar|contraindicar|prescrever|diagnosticar|tratar|operar|examinar)/i,
      quantitative: /^\d+|^(muito|pouco|bastante|elevado|reduzido|aumentado|diminuído|normal|alterado)/i,
      temporal: /^(agudo|crônico|recente|antigo|progressivo|súbito|gradual|intermitente|contínuo|recorrente)/i
    }

    for (const [category, pattern] of Object.entries(categories)) {
      if (pattern.test(word)) return category
    }
    
    return 'general'
  }

  getTermComplexity(word) {
    // Avalia complexidade do termo médico
    if (word.length > 15) return 3
    if (word.length > 10) return 2
    if (word.includes('-') || word.includes('/')) return 2
    return 1
  }

  getTermFrequency(word, words) {
    // Conta frequência do termo no texto
    return words.filter(w => w.toLowerCase() === word.toLowerCase()).length
  }

  calculateTechnicalComplexity(analysis) {
    // Calcula complexidade técnica baseada na análise
    const factors = []
    
    if (analysis.lexicalDiversity) factors.push(analysis.lexicalDiversity)
    if (analysis.medicalTerminology?.length > 0) {
      const avgComplexity = analysis.medicalTerminology.reduce((sum, term) => sum + term.complexity, 0) / analysis.medicalTerminology.length
      factors.push(avgComplexity / 3) // Normalizar para 0-1
    }
    
    const categoryComplexity = {
      medical: 0.9,
      anatomical: 0.8,
      procedural: 0.7,
      quantitative: 0.5,
      temporal: 0.6,
      general: 0.3
    }
    
    let totalComplexity = 0
    let totalWords = 0
    
    Object.entries(analysis.wordCategories || {}).forEach(([category, count]) => {
      totalComplexity += (categoryComplexity[category] || 0.3) * count
      totalWords += count
    })
    
    if (totalWords > 0) {
      factors.push(totalComplexity / totalWords)
    }
    
    return factors.length > 0 ? factors.reduce((a, b) => a + b) / factors.length : 0.5
  }

  isMedicalTerm(word) {
    const medicalDatabase = [
      // Termos gerais
      'anamnese', 'semiologia', 'propedêutica', 'etiologia', 'fisiopatologia', 'epidemiologia',
      'prognóstico', 'diagnóstico', 'tratamento', 'terapêutica', 'profilaxia', 'reabilitação',
      
      // Especialidades
      'cardiologia', 'pneumologia', 'neurologia', 'gastroenterologia', 'endocrinologia',
      'nefrologia', 'hematologia', 'oncologia', 'reumatologia', 'infectologia',
      
      // Condições
      'hipertensão', 'diabetes', 'asma', 'pneumonia', 'infarto', 'avc', 'câncer',
      'artrite', 'hepatite', 'insuficiência', 'síndrome', 'distúrbio', 'transtorno',
      
      // Medicamentos (classes)
      'antibiótico', 'anti-inflamatório', 'analgésico', 'antihipertensivo', 'hipoglicemiante',
      'broncodilatador', 'corticoide', 'imunossupressor', 'quimioterápico', 'anticoagulante'
    ]

    const cleanWord = word.toLowerCase().replace(/[^a-záàâãéèêíïóôõöúçñ]/gi, '')
    return medicalDatabase.includes(cleanWord) || 
           medicalDatabase.some(term => cleanWord.includes(term) || term.includes(cleanWord))
  }

  extractSemanticConcepts(content) {
    const concepts = []
    const sentences = this.intelligentSentenceSplit(content)
    let conceptId = 0

    sentences.forEach(sentence => {
      // Extrair substantivos e frases nominais
      const nouns = this.extractNouns(sentence)
      const nounPhrases = this.extractNounPhrases(sentence)
      
      // Criar conceitos
      nouns.concat(nounPhrases).forEach(term => {
        const existingConcept = concepts.find(c => c.term === term)
        
        if (!existingConcept) {
          concepts.push({
            id: `concept_${conceptId++}`,
            term,
            type: this.classifyConceptType(term),
            importance: this.calculateConceptImportance(term, content),
            frequency: this.countOccurrences(term, content),
            attributes: this.extractConceptAttributes(term, sentence),
            context: sentence
          })
        }
      })
    })

    return concepts.sort((a, b) => b.importance - a.importance)
  }

  extractSemanticRelations(content) {
    const relations = []
    const patterns = [
      { regex: /(.+) causa (.+)/, type: 'causal' },
      { regex: /(.+) leva a (.+)/, type: 'causal' },
      { regex: /(.+) resulta em (.+)/, type: 'causal' },
      { regex: /(.+) é parte de (.+)/, type: 'partitive' },
      { regex: /(.+) consiste em (.+)/, type: 'compositional' },
      { regex: /(.+) é um tipo de (.+)/, type: 'taxonomic' },
      { regex: /(.+) está associado a (.+)/, type: 'associative' },
      { regex: /(.+) depende de (.+)/, type: 'dependency' },
      { regex: /(.+) influencia (.+)/, type: 'influence' },
      { regex: /(.+) regula (.+)/, type: 'regulatory' }
    ]

    const sentences = this.intelligentSentenceSplit(content)
    
    sentences.forEach(sentence => {
      patterns.forEach(({ regex, type }) => {
        const match = sentence.match(regex)
        if (match) {
          relations.push({
            from: match[1].trim(),
            to: match[2].trim(),
            type,
            strength: this.calculateRelationStrength(match[1], match[2], content),
            context: sentence
          })
        }
      })
    })

    return relations
  }

  // Métodos de análise sintática
  analyzeSentenceComplexity(sentence) {
    const words = sentence.split(/\s+/)
    const clauses = sentence.split(/[,;]/).length
    const subordinates = (sentence.match(/(que|qual|quando|onde|como|porque|embora|apesar)/gi) || []).length
    
    return {
      length: words.length,
      clauses,
      subordinates,
      complexity: words.length > 25 ? 'complex' : words.length > 15 ? 'medium' : 'simple'
    }
  }

  extractClauseStructure(sentence) {
    const clauses = sentence.split(/[,;:]/)
    return {
      main: clauses[0]?.trim(),
      subordinate: clauses.slice(1).map(c => c.trim()),
      type: clauses.length > 2 ? 'compound-complex' : clauses.length > 1 ? 'complex' : 'simple'
    }
  }

  buildDependencyTree(sentence) {
    // Simplificado - árvore de dependência básica
    const words = sentence.split(/\s+/)
    return {
      root: words.find(w => /\w+(ar|er|ir)$/i.test(w)) || words[0],
      dependencies: words.slice(1).map(w => ({ word: w, type: 'modifier' }))
    }
  }

  extractPhrasePatterns(sentences) {
    const patterns = []
    const commonPatterns = [
      { pattern: /o \w+ (de|da|do) \w+/gi, type: 'noun_phrase' },
      { pattern: /\w+ e \w+/gi, type: 'coordination' },
      { pattern: /\w+mente/gi, type: 'adverb' }
    ]
    
    sentences.forEach(sentence => {
      commonPatterns.forEach(({ pattern, type }) => {
        const matches = sentence.match(pattern)
        if (matches) {
          patterns.push({ type, examples: matches })
        }
      })
    })
    
    return patterns
  }

  // Métodos de análise semântica adicionais
  extractImplications(content) {
    const implications = []
    const implicationMarkers = [
      'portanto', 'logo', 'assim', 'consequentemente',
      'implica', 'sugere', 'indica', 'demonstra'
    ]
    
    const sentences = this.intelligentSentenceSplit(content)
    sentences.forEach(sentence => {
      implicationMarkers.forEach(marker => {
        if (sentence.toLowerCase().includes(marker)) {
          implications.push({
            marker,
            sentence,
            type: 'logical_implication'
          })
        }
      })
    })
    
    return implications
  }

  detectContradictions(content) {
    const contradictions = []
    const contradictionMarkers = [
      'mas', 'porém', 'contudo', 'entretanto',
      'ao contrário', 'diferentemente', 'não obstante'
    ]
    
    const sentences = this.intelligentSentenceSplit(content)
    sentences.forEach((sentence, index) => {
      contradictionMarkers.forEach(marker => {
        if (sentence.toLowerCase().includes(marker)) {
          contradictions.push({
            marker,
            sentence,
            position: index,
            type: 'potential_contradiction'
          })
        }
      })
    })
    
    return contradictions
  }

  detectAmbiguities(content) {
    const ambiguities = []
    const ambiguousTerms = [
      'isso', 'aquilo', 'este', 'aquele',
      'pode', 'talvez', 'possivelmente', 'provavelmente'
    ]
    
    const sentences = this.intelligentSentenceSplit(content)
    sentences.forEach(sentence => {
      ambiguousTerms.forEach(term => {
        if (sentence.toLowerCase().includes(term)) {
          ambiguities.push({
            term,
            sentence,
            type: 'lexical_ambiguity'
          })
        }
      })
    })
    
    return ambiguities
  }

  extractContextualMeanings(content) {
    const meanings = []
    const contextualClues = [
      { pattern: /no contexto de (.+?)[,.]/, type: 'explicit_context' },
      { pattern: /quando se trata de (.+?)[,.]/, type: 'conditional_context' },
      { pattern: /em (.+?), (.+)/, type: 'situational_context' }
    ]
    
    const sentences = this.intelligentSentenceSplit(content)
    sentences.forEach(sentence => {
      contextualClues.forEach(({ pattern, type }) => {
        const match = sentence.match(pattern)
        if (match) {
          meanings.push({
            context: match[1],
            sentence,
            type
          })
        }
      })
    })
    
    return meanings
  }

  calculateRelationStrength(from, to, content) {
    // Calcula força da relação baseada em proximidade e frequência
    const sentences = this.intelligentSentenceSplit(content)
    let strength = 0
    
    sentences.forEach(sentence => {
      if (sentence.includes(from) && sentence.includes(to)) {
        strength += 1
      }
    })
    
    return Math.min(1, strength / sentences.length)
  }

  // Métodos de análise adicionais
  pragmaticAnalysis(content) {
    return {
      speechActs: this.identifySpeechActs(content),
      communicativeIntent: this.inferCommunicativeIntent(content),
      contextualFactors: this.analyzeContextualFactors(content),
      pragmaticMarkers: this.extractPragmaticMarkers(content)
    }
  }

  extractNamedEntities(content) {
    const entities = []
    const patterns = {
      person: /Dr\.|Dra\.|Prof\.|[A-Z][a-z]+ [A-Z][a-z]+/g,
      location: /(hospital|clínica|centro|unidade|laboratório) [A-Z][a-z]+/gi,
      organization: /(Instituto|Universidade|Faculdade|Hospital|Centro) [A-Z][a-z]+/gi,
      medical: /(síndrome|doença|transtorno|distúrbio) de [A-Z][a-z]+/gi
    }
    
    Object.entries(patterns).forEach(([type, pattern]) => {
      const matches = content.match(pattern) || []
      matches.forEach(match => {
        entities.push({ text: match, type, position: content.indexOf(match) })
      })
    })
    
    return entities
  }

  analyzeSentiment(content) {
    const positiveWords = ['bom', 'excelente', 'eficaz', 'sucesso', 'melhora', 'positivo']
    const negativeWords = ['ruim', 'grave', 'severo', 'piora', 'falha', 'negativo']
    
    let positiveCount = 0
    let negativeCount = 0
    
    const words = content.toLowerCase().split(/\s+/)
    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++
      if (negativeWords.includes(word)) negativeCount++
    })
    
    return {
      sentiment: positiveCount > negativeCount ? 'positive' : negativeCount > positiveCount ? 'negative' : 'neutral',
      positiveScore: positiveCount / words.length,
      negativeScore: negativeCount / words.length
    }
  }

  detectLatentTopics(content) {
    const topics = []
    const topicIndicators = {
      diagnosis: ['diagnóstico', 'avaliação', 'exame', 'teste', 'análise'],
      treatment: ['tratamento', 'terapia', 'medicamento', 'intervenção', 'procedimento'],
      prevention: ['prevenção', 'profilaxia', 'vacina', 'cuidados', 'proteção'],
      prognosis: ['prognóstico', 'evolução', 'recuperação', 'sobrevida', 'desfecho']
    }
    
    const lowerContent = content.toLowerCase()
    Object.entries(topicIndicators).forEach(([topic, indicators]) => {
      const score = indicators.filter(ind => lowerContent.includes(ind)).length / indicators.length
      if (score > 0) {
        topics.push({ topic, score, indicators: indicators.filter(ind => lowerContent.includes(ind)) })
      }
    })
    
    return topics.sort((a, b) => b.score - a.score)
  }

  analyzeCoherence(content) {
    const sentences = this.intelligentSentenceSplit(content)
    const connectives = ['portanto', 'assim', 'além disso', 'por outro lado', 'entretanto']
    
    let connectiveCount = 0
    sentences.forEach(sentence => {
      connectives.forEach(conn => {
        if (sentence.toLowerCase().includes(conn)) connectiveCount++
      })
    })
    
    return {
      sentenceCount: sentences.length,
      connectiveRatio: connectiveCount / sentences.length,
      averageSentenceLength: sentences.reduce((sum, s) => sum + s.split(/\s+/).length, 0) / sentences.length,
      coherenceScore: Math.min(1, connectiveCount / sentences.length + 0.5)
    }
  }

  identifyDiscoursePatterns(content) {
    const patterns = []
    const discourseMarkers = {
      introduction: ['primeiramente', 'inicialmente', 'para começar'],
      development: ['em seguida', 'posteriormente', 'além disso'],
      contrast: ['porém', 'contudo', 'por outro lado'],
      conclusion: ['portanto', 'assim', 'em conclusão']
    }
    
    const sentences = this.intelligentSentenceSplit(content)
    sentences.forEach((sentence, index) => {
      Object.entries(discourseMarkers).forEach(([type, markers]) => {
        markers.forEach(marker => {
          if (sentence.toLowerCase().includes(marker)) {
            patterns.push({ type, marker, position: index, sentence })
          }
        })
      })
    })
    
    return patterns
  }

  analyzeCognitiveComplexity(content) {
    const factors = {
      abstractConcepts: (content.match(/(teoria|conceito|hipótese|modelo|paradigma)/gi) || []).length,
      technicalTerms: (content.match(/\w{10,}/g) || []).length,
      conditionals: (content.match(/(se|caso|quando|embora)/gi) || []).length,
      comparisons: (content.match(/(mais|menos|igual|diferente|semelhante)/gi) || []).length
    }
    
    const wordCount = content.split(/\s+/).length
    const complexityScore = Object.values(factors).reduce((sum, count) => sum + count, 0) / wordCount
    
    return {
      factors,
      score: Math.min(1, complexityScore * 10),
      level: complexityScore > 0.1 ? 'high' : complexityScore > 0.05 ? 'medium' : 'low'
    }
  }

  // Métodos auxiliares para análise pragmática
  identifySpeechActs(content) {
    const speechActs = []
    const patterns = {
      assertive: /é|são|foi|foram|existe|há/gi,
      directive: /deve|precisa|necessário|importante|recomenda/gi,
      commissive: /prometo|garanto|comprometo|asseguro/gi,
      expressive: /infelizmente|felizmente|surpreendentemente/gi,
      declarative: /declaro|nomeio|defino|estabeleço/gi
    }
    
    Object.entries(patterns).forEach(([type, pattern]) => {
      const matches = content.match(pattern) || []
      if (matches.length > 0) {
        speechActs.push({ type, count: matches.length })
      }
    })
    
    return speechActs
  }

  inferCommunicativeIntent(content) {
    const intents = []
    if (content.includes('?')) intents.push('questioning')
    if (content.match(/deve|precisa|necessário/gi)) intents.push('instructing')
    if (content.match(/porque|pois|devido/gi)) intents.push('explaining')
    if (content.match(/exemplo|caso|instância/gi)) intents.push('exemplifying')
    
    return intents.length > 0 ? intents : ['informing']
  }

  analyzeContextualFactors(content) {
    return {
      formality: content.match(/[A-Z][a-z]+\./g) ? 'formal' : 'informal',
      specificity: content.match(/\d+|%|mg|ml/gi) ? 'specific' : 'general',
      temporality: content.match(/hoje|ontem|amanhã|recente|atual/gi) ? 'temporal' : 'atemporal'
    }
  }

  extractPragmaticMarkers(content) {
    const markers = []
    const pragmaticWords = [
      'aliás', 'afinal', 'enfim', 'então',
      'bem', 'ora', 'agora', 'pois'
    ]
    
    pragmaticWords.forEach(word => {
      if (content.toLowerCase().includes(word)) {
        markers.push(word)
      }
    })
    
    return markers
  }

  // Métodos para construção do mapa semântico e grafo de conhecimento
  clusterConcepts(nodes, edges) {
    // Clusterização simples baseada em conexões
    const clusters = []
    const visited = new Set()
    
    nodes.forEach(node => {
      if (!visited.has(node.id)) {
        const cluster = []
        const queue = [node.id]
        
        while (queue.length > 0) {
          const currentId = queue.shift()
          if (!visited.has(currentId)) {
            visited.add(currentId)
            cluster.push(currentId)
            
            // Adicionar nós conectados
            edges.forEach(edge => {
              if (edge.source === currentId && !visited.has(edge.target)) {
                queue.push(edge.target)
              } else if (edge.target === currentId && !visited.has(edge.source)) {
                queue.push(edge.source)
              }
            })
          }
        }
        
        clusters.push(cluster)
      }
    })
    
    return clusters
  }

  buildConceptHierarchy(semanticMap) {
    // Constrói hierarquia baseada em relações taxonômicas
    const hierarchy = { root: 'conhecimento', children: [] }
    const taxonomicEdges = semanticMap.edges.filter(e => e.type === 'taxonomic')
    
    // Identificar conceitos raiz (sem pais)
    const childNodes = new Set(taxonomicEdges.map(e => e.source))
    const rootNodes = semanticMap.nodes.filter(n => !childNodes.has(n.id))
    
    rootNodes.forEach(node => {
      hierarchy.children.push(this.buildSubHierarchy(node, semanticMap.edges))
    })
    
    return hierarchy
  }

  buildSubHierarchy(node, edges) {
    const children = edges
      .filter(e => e.target === node.id && e.type === 'taxonomic')
      .map(e => ({ id: e.source, label: `child_of_${node.label}` }))
      .filter(Boolean)
      .map(child => this.buildSubHierarchy(child, edges))
    
    return {
      id: node.id,
      label: node.label,
      children
    }
  }

  extractEntityProperties(node) {
    // Extrai propriedades da entidade
    const properties = {
      importance: node.importance || 0.5,
      type: node.type || 'unknown',
      attributes: node.attributes || {}
    }
    
    return properties
  }

  extractEntityContext(node, semanticMap) {
    // Extrai contexto da entidade baseado em suas conexões
    const connectedEdges = semanticMap.edges.filter(e => 
      e.source === node.id || e.target === node.id
    )
    
    return {
      connections: connectedEdges.length,
      relationTypes: [...new Set(connectedEdges.map(e => e.type))],
      cluster: semanticMap.clusters.findIndex(cluster => cluster.includes(node.id))
    }
  }

  isBidirectional(edge) {
    // Verifica se a relação é bidirecional
    const bidirectionalTypes = ['associative', 'partitive']
    return bidirectionalTypes.includes(edge.type)
  }

  calculateTransitivity(edge) {
    // Calcula transitividade da relação
    const transitiveTypes = ['taxonomic', 'causal', 'dependency']
    return transitiveTypes.includes(edge.type) ? 1 : 0
  }

  extractRelationshipImplications(edge) {
    // Extrai implicações da relação
    const implications = []
    
    if (edge.type === 'causal') {
      implications.push('causa-efeito')
    } else if (edge.type === 'dependency') {
      implications.push('dependência funcional')
    } else if (edge.type === 'taxonomic') {
      implications.push('herança de propriedades')
    }
    
    return implications
  }

  extractFactsFromGraph(knowledgeGraph) {
    // Extrai fatos do grafo
    const facts = []
    
    knowledgeGraph.entities.forEach(entity => {
      facts.push({
        type: 'entity_exists',
        subject: entity.id,
        predicate: 'is_a',
        object: entity.type || 'concept'
      })
    })
    
    knowledgeGraph.relationships.forEach(rel => {
      facts.push({
        type: 'relationship',
        subject: rel.source,
        predicate: rel.type,
        object: rel.target
      })
    })
    
    return facts
  }

  inferRulesFromGraph(knowledgeGraph) {
    // Infere regras do grafo
    const rules = []
    
    // Regra de transitividade
    const transitiveRels = knowledgeGraph.relationships.filter(r => r.transitivity > 0)
    if (transitiveRels.length > 0) {
      rules.push({
        type: 'transitivity',
        condition: 'if A relates to B and B relates to C',
        conclusion: 'then A relates to C',
        applicable: transitiveRels.map(r => r.type)
      })
    }
    
    // Regra de herança
    const taxonomicRels = knowledgeGraph.relationships.filter(r => r.type === 'taxonomic')
    if (taxonomicRels.length > 0) {
      rules.push({
        type: 'inheritance',
        condition: 'if A is a type of B',
        conclusion: 'then A inherits properties of B',
        applicable: ['taxonomic']
      })
    }
    
    return rules
  }

  identifyKnowledgePatterns(knowledgeGraph) {
    // Identifica padrões no grafo
    const patterns = []
    
    // Padrão de hub (muitas conexões)
    const hubThreshold = 5
    knowledgeGraph.entities.forEach(entity => {
      const connections = knowledgeGraph.relationships.filter(r => 
        r.source === entity.id || r.target === entity.id
      ).length
      
      if (connections >= hubThreshold) {
        patterns.push({
          type: 'hub',
          entity: entity.id,
          connections
        })
      }
    })
    
    // Padrão de cadeia causal
    const causalChains = this.findCausalChains(knowledgeGraph)
    causalChains.forEach(chain => {
      patterns.push({
        type: 'causal_chain',
        elements: chain
      })
    })
    
    return patterns
  }

  findCausalChains(knowledgeGraph) {
    // Encontra cadeias causais no grafo
    const chains = []
    const causalRels = knowledgeGraph.relationships.filter(r => r.type === 'causal')
    
    // Simplificado - apenas cadeias de comprimento 2
    causalRels.forEach(rel1 => {
      causalRels.forEach(rel2 => {
        if (rel1.target === rel2.source) {
          chains.push([rel1.source, rel1.target, rel2.target])
        }
      })
    })
    
    return chains
  }

  generateAdaptiveExplanation(question) {
    const explanationComponents = []

    // Componente conceitual
    explanationComponents.push(
      `Esta questão avalia o conhecimento sobre ${question.concept || question.topic}.`
    )

    // Componente de raciocínio
    const reasoningMap = {
      conceptual: 'Requer compreensão dos conceitos fundamentais e suas definições.',
      relational: 'Exige análise das relações entre diferentes elementos.',
      inferential: 'Demanda capacidade de inferir conclusões a partir das informações.',
      analytical: 'Necessita decomposição e análise crítica dos componentes.',
      evaluative: 'Requer julgamento e avaliação baseados em critérios.',
      diagnostic: 'Envolve raciocínio clínico e diagnóstico diferencial.'
    }

    explanationComponents.push(reasoningMap[question.type] || '')

    // Componente específico da resposta
    const correctAnswer = question.alternatives[question.correctAnswer]
    explanationComponents.push(
      `A alternativa correta (${String.fromCharCode(65 + question.correctAnswer)}) ${this.explainWhyCorrect(correctAnswer, question)}`
    )

    // Componente de distratores
    const distractorExplanation = this.explainDistractors(question)
    if (distractorExplanation) {
      explanationComponents.push(distractorExplanation)
    }

    // Componente de aplicação
    explanationComponents.push(
      `Este conhecimento é importante para ${this.generateApplicationContext(question)}`
    )

    return explanationComponents.filter(c => c).join(' ')
  }

  generateProgressiveHints(question) {
    const hints = []

    // Hint 1: Contextual
    hints.push({
      level: 1,
      text: `Pense sobre ${this.generateContextualHint(question)}`,
      revealPercentage: 20
    })

    // Hint 2: Conceitual
    hints.push({
      level: 2,
      text: `Lembre-se que ${this.generateConceptualHint(question)}`,
      revealPercentage: 40
    })

    // Hint 3: Eliminação
    hints.push({
      level: 3,
      text: `Você pode eliminar as alternativas que ${this.generateEliminationHint(question)}`,
      revealPercentage: 60
    })

    // Hint 4: Direcionamento
    hints.push({
      level: 4,
      text: `Foque na alternativa que ${this.generateDirectionalHint(question)}`,
      revealPercentage: 80
    })

    return hints
  }

  // Métodos auxiliares para qualidade
  assessClarity(text) {
    // Avaliar clareza baseado em:
    // - Comprimento da sentença
    // - Complexidade vocabular
    // - Estrutura gramatical
    // - Ambiguidade

    const words = text.split(/\s+/)
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length
    const sentenceLength = words.length

    let clarity = 1.0

    // Penalizar sentenças muito longas ou muito curtas
    if (sentenceLength > 30) clarity -= 0.2
    if (sentenceLength < 5) clarity -= 0.3

    // Penalizar palavras muito longas
    if (avgWordLength > 8) clarity -= 0.1

    // Verificar ambiguidade
    const ambiguousTerms = ['isso', 'aquilo', 'coisa', 'algo']
    const hasAmbiguity = ambiguousTerms.some(term => text.toLowerCase().includes(term))
    if (hasAmbiguity) clarity -= 0.2

    return Math.max(0, Math.min(1, clarity))
  }

  assessRelevance(question) {
    // Avaliar relevância baseado no contexto original
    let relevance = 0.8

    // Verificar se a questão está alinhada com o tópico
    if (question.topic && question.concept) {
      relevance += 0.1
    }

    // Verificar se usa terminologia apropriada
    if (question.metadata && question.metadata.medicalTerms > 0) {
      relevance += 0.1
    }

    return Math.min(1, relevance)
  }

  assessDiscrimination(alternatives) {
    // Avaliar se as alternativas discriminam bem
    let discrimination = 1.0

    // Verificar se são muito similares
    for (let i = 0; i < alternatives.length - 1; i++) {
      for (let j = i + 1; j < alternatives.length; j++) {
        const similarity = this.calculateSimilarity(alternatives[i], alternatives[j])
        if (similarity > 0.8) discrimination -= 0.2
      }
    }

    // Verificar se têm comprimentos muito diferentes
    const lengths = alternatives.map(alt => alt.length)
    const avgLength = lengths.reduce((a, b) => a + b) / lengths.length
    const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length
    
    if (variance > 1000) discrimination -= 0.1

    return Math.max(0, discrimination)
  }

  calculateSimilarity(text1, text2) {
    // Cálculo básico de similaridade
    const words1 = new Set(text1.toLowerCase().split(/\s+/))
    const words2 = new Set(text2.toLowerCase().split(/\s+/))
    
    const intersection = [...words1].filter(word => words2.has(word)).length
    const union = new Set([...words1, ...words2]).size
    
    return intersection / union
  }

  // Métodos auxiliares para seleção e análise de conceitos
  selectKeyConceptFrom(knowledgeGraph) {
    // Seleciona conceito chave baseado em importância
    const concepts = knowledgeGraph.entities.filter(e => e.type === 'concept')
    return concepts.length > 0 ? concepts[Math.floor(Math.random() * concepts.length)] : { label: 'conceito', id: 'default' }
  }

  findRelatedConcepts(concept, knowledgeGraph) {
    // Encontra conceitos relacionados
    const related = knowledgeGraph.relationships
      .filter(r => r.source === concept.id || r.target === concept.id)
      .map(r => r.source === concept.id ? r.target : r.source)
    
    return knowledgeGraph.entities.filter(e => related.includes(e.id))
  }

  formulateConceptualQuestion(concept, analysis) {
    const templates = [
      `Qual das seguintes afirmações melhor define ${concept.label}?`,
      `Em relação a ${concept.label}, é correto afirmar que:`,
      `${concept.label} pode ser caracterizado(a) como:`,
      `Sobre ${concept.label}, assinale a alternativa correta:`
    ]
    return templates[Math.floor(Math.random() * templates.length)]
  }

  generateConceptualAlternatives(concept, relatedConcepts, knowledgeGraph) {
    // Gera alternativas para questão conceitual
    const alternatives = []
    alternatives.push(`Definição correta de ${concept.label}`)
    
    // Adiciona distratores baseados em conceitos relacionados
    relatedConcepts.slice(0, 3).forEach(related => {
      alternatives.push(`Definição incorreta misturando ${concept.label} com ${related.label || 'outro conceito'}`)
    })
    
    // Completa com alternativas genéricas se necessário
    while (alternatives.length < 5) {
      alternatives.push(`Alternativa incorreta ${alternatives.length + 1}`)
    }
    
    return alternatives.slice(0, 5)
  }

  assessQuestionDifficulty(concept, analysis) {
    // Avalia dificuldade baseada na complexidade do conceito
    const complexity = analysis.morphological?.technicalComplexity || 0.5
    if (complexity > 0.7) return 'dificil'
    if (complexity > 0.4) return 'medio'
    return 'facil'
  }

  finalizeQuestion(question, knowledgeGraph, options) {
    // Finaliza questão com metadados adicionais
    return {
      ...question,
      correctAnswer: 0, // Primeira alternativa é sempre a correta (será embaralhada depois)
      explanation: `Esta questão avalia o conhecimento sobre ${question.concept || question.topic}`,
      references: options.includeReferences ? ['Referência bibliográfica 1', 'Referência bibliográfica 2'] : []
    }
  }

  selectSignificantRelationship(knowledgeGraph) {
    const relationships = knowledgeGraph.relationships
    return relationships.length > 0 ? relationships[Math.floor(Math.random() * relationships.length)] : null
  }

  getRelationshipEntities(relationship, knowledgeGraph) {
    if (!relationship) return { source: null, target: null }
    
    const source = knowledgeGraph.entities.find(e => e.id === relationship.source)
    const target = knowledgeGraph.entities.find(e => e.id === relationship.target)
    
    return { source, target }
  }

  formulateRelationalQuestion(relationship, entities, analysis) {
    if (!relationship || !entities.source || !entities.target) {
      return 'Qual a relação entre os conceitos apresentados?'
    }
    
    const templates = [
      `Qual a relação entre ${entities.source.label} e ${entities.target.label}?`,
      `Como ${entities.source.label} se relaciona com ${entities.target.label}?`,
      `A relação entre ${entities.source.label} e ${entities.target.label} pode ser descrita como:`
    ]
    
    return templates[Math.floor(Math.random() * templates.length)]
  }

  generateRelationalAlternatives(relationship, entities, knowledgeGraph) {
    const alternatives = []
    
    if (relationship && entities.source && entities.target) {
      alternatives.push(`${entities.source.label} ${relationship.type} ${entities.target.label}`)
    } else {
      alternatives.push('Relação correta entre os conceitos')
    }
    
    // Adiciona distratores
    alternatives.push('Relação inversa ou incorreta')
    alternatives.push('Relação de outro tipo')
    alternatives.push('Não há relação direta')
    alternatives.push('Relação parcialmente correta')
    
    return alternatives.slice(0, 5)
  }

  assessRelationshipComplexity(relationship) {
    if (!relationship) return 'medio'
    
    const complexTypes = ['causal', 'dependency', 'regulatory']
    if (complexTypes.includes(relationship.type)) return 'dificil'
    
    return 'medio'
  }

  // Implementações dos outros tipos de questões
  async generateInferentialQuestion(knowledgeGraph, analysis, options) {
    return this.generateConceptualQuestion(knowledgeGraph, analysis, options) // Simplificado
  }

  async generateAnalyticalQuestion(knowledgeGraph, analysis, options) {
    return this.generateConceptualQuestion(knowledgeGraph, analysis, options) // Simplificado
  }

  async generateEvaluativeQuestion(knowledgeGraph, analysis, options) {
    return this.generateConceptualQuestion(knowledgeGraph, analysis, options) // Simplificado
  }

  async generateCreativeQuestion(knowledgeGraph, analysis, options) {
    return this.generateConceptualQuestion(knowledgeGraph, analysis, options) // Simplificado
  }

  async generateComparativeQuestion(knowledgeGraph, analysis, options) {
    return this.generateConceptualQuestion(knowledgeGraph, analysis, options) // Simplificado
  }

  async generateCausalQuestion(knowledgeGraph, analysis, options) {
    return this.generateConceptualQuestion(knowledgeGraph, analysis, options) // Simplificado
  }

  async generateHypotheticalQuestion(knowledgeGraph, analysis, options) {
    return this.generateConceptualQuestion(knowledgeGraph, analysis, options) // Simplificado
  }

  async generateDiagnosticQuestion(knowledgeGraph, analysis, options) {
    return this.generateConceptualQuestion(knowledgeGraph, analysis, options) // Simplificado
  }

  // Métodos auxiliares adicionais
  explainWhyCorrect(answer, question) {
    return `é a resposta correta porque ${answer}`
  }

  explainDistractors(question) {
    return 'As outras alternativas apresentam informações incorretas ou incompletas.'
  }

  generateApplicationContext(question) {
    return 'a prática clínica e o diagnóstico diferencial'
  }

  generateContextualHint(question) {
    return `o contexto em que ${question.concept || 'o conceito'} é aplicado`
  }

  generateConceptualHint(question) {
    return `${question.concept || 'o conceito'} tem características específicas`
  }

  generateEliminationHint(question) {
    return 'apresentam informações claramente incorretas'
  }

  generateDirectionalHint(question) {
    return 'melhor descreve a definição correta'
  }

  // Métodos de extração e classificação de conceitos
  extractNouns(sentence) {
    // Extração simplificada de substantivos
    const nounPatterns = /\b[A-Z][a-záàâãéèêíïóôõöúç]+\b|\b(doença|síndrome|tratamento|diagnóstico|paciente|medicamento|cirurgia|exame|sintoma)\b/gi
    const matches = sentence.match(nounPatterns) || []
    return [...new Set(matches)]
  }

  extractNounPhrases(sentence) {
    // Extração de frases nominais
    const phrasePatterns = [
      /o \w+ (de|da|do) \w+/gi,
      /\w+ \w+ico/gi,
      /\w+ \w+oso/gi,
      /\w+ \w+al/gi
    ]
    
    const phrases = []
    phrasePatterns.forEach(pattern => {
      const matches = sentence.match(pattern) || []
      phrases.push(...matches)
    })
    
    return [...new Set(phrases)]
  }

  classifyConceptType(term) {
    const classifications = {
      disease: /doença|síndrome|transtorno|distúrbio|patologia/i,
      treatment: /tratamento|terapia|medicamento|fármaco|procedimento/i,
      anatomy: /órgão|tecido|célula|sistema|músculo|osso|nervo/i,
      diagnosis: /diagnóstico|exame|teste|análise|avaliação/i,
      symptom: /sintoma|sinal|manifestação|quadro|apresentação/i
    }
    
    for (const [type, pattern] of Object.entries(classifications)) {
      if (pattern.test(term)) return type
    }
    
    return 'general'
  }

  calculateConceptImportance(term, content) {
    // Calcula importância baseada em frequência e posição
    const frequency = this.countOccurrences(term, content)
    const isInTitle = content.slice(0, 100).includes(term)
    const isMedicalTerm = this.isMedicalTerm(term)
    
    let importance = frequency / 10 // Normalizar
    if (isInTitle) importance += 0.3
    if (isMedicalTerm) importance += 0.2
    
    return Math.min(1, importance)
  }

  countOccurrences(term, content) {
    // Escape special regex characters
    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    try {
      const regex = new RegExp(escapedTerm, 'gi')
      const matches = content.match(regex) || []
      return matches.length
    } catch (e) {
      // Fallback to simple string count
      return content.toLowerCase().split(term.toLowerCase()).length - 1
    }
  }

  extractConceptAttributes(term, sentence) {
    const attributes = {}
    
    // Escape special regex characters
    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    
    // Extrair adjetivos próximos
    try {
      const beforePattern = new RegExp(`(\\w+)\\s+${escapedTerm}`, 'i')
      const afterPattern = new RegExp(`${escapedTerm}\\s+(\\w+)`, 'i')
      
      const beforeMatch = sentence.match(beforePattern)
      const afterMatch = sentence.match(afterPattern)
      
      if (beforeMatch) attributes.modifier = beforeMatch[1]
      if (afterMatch) attributes.complement = afterMatch[1]
    } catch (e) {
      // If regex fails, return empty attributes
      console.warn('Failed to extract concept attributes:', e)
    }
    
    return attributes
  }

  // Métodos de avaliação de qualidade
  assessDifficultyBalance(question) {
    // Avalia se a dificuldade está balanceada
    const expectedDifficulty = {
      facil: 0.2,
      medio: 0.4,
      dificil: 0.6,
      expert: 0.8
    }
    
    const actual = expectedDifficulty[question.difficulty] || 0.5
    const textComplexity = question.text.split(/\s+/).length / 50 // Normalizar
    
    return 1 - Math.abs(actual - textComplexity)
  }

  assessPlausibility(alternatives) {
    // Avalia plausibilidade dos distratores
    if (!alternatives || alternatives.length < 2) return 0
    
    // Verificar similaridade entre alternativas
    let totalSimilarity = 0
    let comparisons = 0
    
    for (let i = 0; i < alternatives.length - 1; i++) {
      for (let j = i + 1; j < alternatives.length; j++) {
        totalSimilarity += this.calculateSimilarity(alternatives[i], alternatives[j])
        comparisons++
      }
    }
    
    // Plausibilidade ideal: alternativas similares mas distintas
    const avgSimilarity = totalSimilarity / comparisons
    return avgSimilarity > 0.3 && avgSimilarity < 0.7 ? 1 : avgSimilarity
  }

  assessEducationalValue(question) {
    // Avalia valor educacional da questão
    let value = 0.5
    
    if (question.explanation) value += 0.2
    if (question.references && question.references.length > 0) value += 0.1
    if (question.hints && question.hints.length > 0) value += 0.1
    if (question.concept) value += 0.1
    
    return Math.min(1, value)
  }

  extractQuestionFeatures(question) {
    return {
      textLength: question.text.length,
      alternativeCount: question.alternatives.length,
      hasExplanation: !!question.explanation,
      hasReferences: !!(question.references && question.references.length > 0),
      difficulty: question.difficulty,
      type: question.type,
      cognitiveLevel: question.cognitiveLevel || 'unknown'
    }
  }

  updateInternalModels() {
    // Atualiza modelos internos baseado nos padrões aprendidos
    // Simplificado - apenas logging
    console.log(`AI Engine: Updated internal models with ${this.learningPatterns.length} patterns`)
    
    // Análise de padrões de sucesso
    const successfulPatterns = this.learningPatterns.filter(p => p.qualityScore > 0.8)
    const avgSuccessScore = successfulPatterns.length > 0 
      ? successfulPatterns.reduce((sum, p) => sum + p.qualityScore, 0) / successfulPatterns.length
      : 0
    
    // Ajustar threshold de qualidade baseado no desempenho
    if (avgSuccessScore > 0.9 && this.learningPatterns.length > 100) {
      this.qualityThreshold = Math.min(0.9, this.qualityThreshold + 0.05)
    } else if (avgSuccessScore < 0.7 && this.learningPatterns.length > 50) {
      this.qualityThreshold = Math.max(0.7, this.qualityThreshold - 0.05)
    }
  }
}

// Exportar instância singleton
export default new AIQuestionEngine()