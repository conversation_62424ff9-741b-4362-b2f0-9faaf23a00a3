/* Global CSS Fixes for Layout Issues */

/* Box-sizing reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Prevent horizontal overflow */
html {
  overflow-x: hidden;
  width: 100%;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  min-height: 100vh;
}

/* Main app container */
#app {
  width: 100%;
  overflow-x: hidden;
  position: relative;
}

/* Fix for all containers */
.container,
.header-container,
.content-container,
.dashboard-container {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Fix absolute positioned elements */
[style*="position: absolute"] {
  max-width: 100vw;
}

/* Fix for all sections */
section,
article,
main,
header,
footer,
nav {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Fix grid and flex containers */
.grid,
.flex,
[class*="grid"],
[class*="flex"] {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Fix images and media */
img,
video,
svg,
canvas {
  max-width: 100%;
  height: auto;
}

/* Fix overflow on common elements */
.card,
.panel,
.modal,
.dropdown {
  max-width: 100%;
  box-sizing: border-box;
}

/* Responsive table fix */
table {
  max-width: 100%;
  overflow-x: auto;
  display: block;
}

/* Fix for z-index stacking */
.fixed-header {
  z-index: 9999 !important;
}

.dropdown,
.modal,
.overlay {
  z-index: 10000 !important;
}

/* Utility classes for overflow control */
.overflow-hidden {
  overflow: hidden !important;
}

.overflow-x-hidden {
  overflow-x: hidden !important;
}

.overflow-y-auto {
  overflow-y: auto !important;
}

/* Fix for performance dashboard specific issues */
.performance-ultra {
  width: 100% !important;
  max-width: 100vw !important;
  overflow-x: hidden !important;
}

.performance-ultra > * {
  max-width: 100% !important;
}

/* Fix gradient spheres positioning */
.gradient-sphere {
  max-width: 500px !important;
  max-height: 500px !important;
}

.gradient-sphere.sphere-1 {
  right: -250px !important;
}

.gradient-sphere.sphere-2 {
  left: -250px !important;
}

.gradient-sphere.sphere-3 {
  right: -250px !important;
}

/* Fix header alignment */
.ultra-header {
  width: 100% !important;
  max-width: 100vw !important;
}

.header-container {
  width: 100% !important;
  padding-left: 24px !important;
  padding-right: 24px !important;
}

/* Fix stat cards and grids */
.stats-grid,
.analytics-grid {
  width: 100% !important;
  padding: 0 !important;
  gap: 20px !important;
}

.stat-card,
.analytics-card {
  width: 100% !important;
  min-width: 0 !important;
}

/* Mobile responsive fixes */
@media (max-width: 768px) {
  .header-container {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
  
  .stats-grid,
  .analytics-grid {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }
  
  .gradient-sphere {
    display: none !important;
  }
}

/* Print media fix */
@media print {
  body {
    overflow: visible !important;
  }
}