/* Performance Page Layout Fixes */
.performance-view {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
}

/* Ensure header stays properly positioned */
.performance-view .app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 9999;
}

/* Content spacing to account for fixed header */
.performance-content {
  padding-top: 80px;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  position: relative;
}

/* Fix any container width issues */
.performance-dashboard {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Ensure all sections respect viewport boundaries */
.dashboard-header,
.dashboard-content,
.analytics-grid,
.achievements-section,
.goals-section {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Fix grid layouts */
.stats-grid,
.analytics-grid {
  display: grid;
  width: 100%;
  box-sizing: border-box;
  gap: 24px;
}

/* Responsive container padding */
@media (max-width: 1400px) {
  .performance-dashboard {
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .performance-content {
    padding-top: 72px;
  }
  
  .performance-dashboard {
    padding: 0 16px;
  }
}

/* Fix z-index stacking */
.app-header {
  z-index: 9999 !important;
}

.navigation-dropdown,
.user-dropdown {
  z-index: 10000 !important;
}

/* Ensure modals and overlays stack properly */
.modal-overlay,
.dropdown-overlay {
  z-index: 10001 !important;
}