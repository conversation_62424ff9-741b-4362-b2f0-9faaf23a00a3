# Mentor AI - Plataforma Unificada

## 🚀 Acesso Rápido

Para acessar a plataforma unificada localmente:

1. **Abra o arquivo de teste:**
   ```
   frontend/test-unified-platform.html
   ```

2. **Ou execute o servidor de desenvolvimento Vue:**
   ```bash
   cd frontend
   npm run dev
   ```
   
   Depois acesse: `http://localhost:5173/platform`

## 📋 Funcionalidades Integradas

### 1. **Dashboard Principal**
- Estatísticas em tempo real
- Atividades recentes
- Métricas de progresso
- Acesso rápido a todas as ferramentas

### 2. **AI Assistant Advanced**
- Upload de múltiplos arquivos (PDF, DOCX, TXT, Imagens)
- Análise inteligente de conteúdo
- Extração de conceitos-chave
- Geração de questões com IA

### 3. **Ultra Question Generator**
- Processamento multimodal avançado
- Análise de qualidade de questões
- Suporte a especialidades médicas
- Geração em tempo real com streaming

### 4. **Flashcards Inteligentes**
- Sistema de repetição espaçada
- Análise neural de retenção
- Estatísticas detalhadas
- Modo de estudo adaptativo

### 5. **Analytics Dashboard**
- Visualizações interativas
- Métricas de desempenho
- Insights de aprendizado
- Exportação de relatórios

## 🛠️ Arquitetura Técnica

### Frontend
- **Framework:** Vue 3 com Composition API
- **Roteamento:** Vue Router 4
- **Estado:** Vuex 4
- **UI:** CSS customizado com design system próprio
- **Gráficos:** Chart.js
- **HTTP:** Axios

### Backend (Mockado para teste)
- **AI Assistant Service:** Análise de documentos e geração de questões
- **Ultra Question Service:** Processamento avançado com NLP
- **Flashcards Service:** Algoritmos de spaced repetition
- **Analytics Service:** Agregação e análise de dados

### Microserviços (Docker)
```yaml
- API Gateway (Kong)
- Flashcards Service
- AI Service
- User Service
- Analytics Service
- Databases: MongoDB, PostgreSQL, Redis, ClickHouse
- Event Streaming: Kafka
- Observability: Prometheus, Grafana, Jaeger, ELK
```

## 🔧 Configuração Local

### 1. Instalar Dependências
```bash
cd frontend
npm install
```

### 2. Variáveis de Ambiente
Crie um arquivo `.env.local`:
```env
VUE_APP_API_URL=http://localhost:8000/api
VUE_APP_MOCK_MODE=true
```

### 3. Executar em Modo de Desenvolvimento
```bash
npm run dev
```

### 4. Build para Produção
```bash
npm run build
```

## 📱 Recursos da Interface

### Navegação Principal
- **Tabs no Header:** Acesso direto a cada seção
- **Botão Flutuante (+):** Ações rápidas
- **Notificações:** Sistema de toasts
- **Perfil de Usuário:** Configurações rápidas

### Dashboard
- Cards de estatísticas animados
- Gráficos de progresso
- Lista de atividades recentes
- Métricas em tempo real

### Configurações
- Modelo de IA preferido
- Idioma da plataforma
- Especialidade médica
- Notificações
- Privacidade

## 🎨 Design System

### Cores Principais
```css
--primary: #6366f1;
--secondary: #8b5cf6;
--success: #10b981;
--danger: #ef4444;
--warning: #f59e0b;
--dark: #0f172a;
```

### Componentes Reutilizáveis
- Cards interativos
- Botões com gradientes
- Inputs customizados
- Modais e diálogos
- Sistema de notificações

## 🚦 Status dos Serviços

O sistema inclui indicadores visuais de status:
- 🟢 **Verde:** Serviço ativo
- 🟡 **Amarelo:** Modo mock/simulado
- 🔴 **Vermelho:** Serviço indisponível

## 📊 Dados de Demonstração

A versão de teste inclui:
- 1247 questões geradas
- 523 flashcards ativos
- 89 documentos processados
- 15 dias de streak de estudo

## 🔐 Segurança

- Autenticação JWT
- Interceptadores Axios
- Sanitização de inputs
- CORS configurado
- Rate limiting (produção)

## 📈 Performance

- Lazy loading de componentes
- Cache de requisições
- Otimização de bundle
- Service workers (PWA)
- Compressão de assets

## 🐛 Troubleshooting

### Erro de CORS
Configure o proxy no `vite.config.js`:
```js
proxy: {
  '/api': {
    target: 'http://localhost:8000',
    changeOrigin: true
  }
}
```

### Componentes não carregando
Verifique os imports no arquivo HTML de teste ou no main.js

### Performance lenta
- Ative o modo de produção
- Reduza o tamanho dos chunks
- Implemente code splitting

## 🚀 Próximos Passos

1. **Integração com Backend Real**
   - Conectar aos microserviços
   - Implementar autenticação real
   - Configurar WebSockets

2. **Melhorias de UI/UX**
   - Animações mais fluidas
   - Modo escuro/claro
   - Responsividade mobile

3. **Funcionalidades Avançadas**
   - Colaboração em tempo real
   - Gamificação
   - Integração com LMS

## 📝 Licença

Propriedade de Mentor AI Team - Todos os direitos reservados.