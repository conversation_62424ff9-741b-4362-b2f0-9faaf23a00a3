<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MentorAI - Complete App Overview</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            padding: 40px 0;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .status-bar {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        
        .status-item.active {
            border-color: #28a745;
            background: #f0fff4;
        }
        
        .status-icon {
            font-size: 1.5em;
        }
        
        .status-item.active .status-icon {
            color: #28a745;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .feature-title {
            font-size: 1.5em;
            margin-bottom: 10px;
            color: #333;
        }
        
        .feature-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .feature-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 0.9em;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #e9ecef;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #dee2e6;
        }
        
        .quick-actions {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .action-btn {
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            color: #333;
        }
        
        .action-btn:hover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: translateY(-3px);
        }
        
        .action-btn i {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
            color: #667eea;
        }
        
        .loading {
            text-align: center;
            color: white;
            padding: 20px;
        }
        
        .error {
            background: #fff5f5;
            border: 2px solid #fed7d7;
            color: #c53030;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-graduation-cap"></i> MentorAI</h1>
            <p>Sistema Completo de Aprendizado Médico com IA</p>
        </div>
        
        <div class="status-bar">
            <h2 style="margin-bottom: 15px;"><i class="fas fa-server"></i> Status dos Serviços</h2>
            <div class="status-grid" id="statusGrid">
                <div class="loading">Verificando serviços...</div>
            </div>
        </div>
        
        <div class="features-grid">
            <!-- Flashcards -->
            <div class="feature-card">
                <i class="fas fa-layer-group feature-icon"></i>
                <h3 class="feature-title">Flashcards Inteligentes</h3>
                <p class="feature-description">
                    Sistema de flashcards com algoritmo de repetição espaçada potencializado por IA neural.
                    Atualmente com <span id="flashcardCount">0</span> baralhos disponíveis.
                </p>
                <div class="feature-links">
                    <a href="/flashcards" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir App
                    </a>
                    <a href="/test-flashcards.html" class="btn btn-secondary">
                        <i class="fas fa-vial"></i> Testar API
                    </a>
                </div>
            </div>
            
            <!-- Calendar -->
            <div class="feature-card">
                <i class="fas fa-calendar-alt feature-icon"></i>
                <h3 class="feature-title">Calendário de Estudos</h3>
                <p class="feature-description">
                    Organize suas sessões de estudo e acompanhe seu progresso com o calendário integrado.
                </p>
                <div class="feature-links">
                    <a href="/calendar" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir Calendário
                    </a>
                </div>
            </div>
            
            <!-- AI Tools -->
            <div class="feature-card">
                <i class="fas fa-robot feature-icon"></i>
                <h3 class="feature-title">Ferramentas de IA</h3>
                <p class="feature-description">
                    Suite completa de ferramentas de IA incluindo gerador de questões, assistente e análise.
                </p>
                <div class="feature-links">
                    <a href="/ai-tools" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> AI Tools
                    </a>
                    <a href="/platform" class="btn btn-secondary">
                        <i class="fas fa-th"></i> Plataforma
                    </a>
                </div>
            </div>
            
            <!-- Progress -->
            <div class="feature-card">
                <i class="fas fa-chart-line feature-icon"></i>
                <h3 class="feature-title">Dashboard de Progresso</h3>
                <p class="feature-description">
                    Visualize seu desempenho com gráficos detalhados e insights personalizados por IA.
                </p>
                <div class="feature-links">
                    <a href="/progress" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Ver Progresso
                    </a>
                </div>
            </div>
            
            <!-- Second Brain -->
            <div class="feature-card">
                <i class="fas fa-brain feature-icon"></i>
                <h3 class="feature-title">Second Brain</h3>
                <p class="feature-description">
                    Chat inteligente com IA para tirar dúvidas e explorar conceitos médicos complexos.
                </p>
                <div class="feature-links">
                    <a href="/second-brain" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Abrir Chat
                    </a>
                </div>
            </div>
            
            <!-- Resources -->
            <div class="feature-card">
                <i class="fas fa-book feature-icon"></i>
                <h3 class="feature-title">Recursos de Estudo</h3>
                <p class="feature-description">
                    Biblioteca completa de materiais, resumos e notas organizadas por especialidade.
                </p>
                <div class="feature-links">
                    <a href="/recursos" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Ver Recursos
                    </a>
                </div>
            </div>
        </div>
        
        <div class="quick-actions">
            <h2><i class="fas fa-bolt"></i> Ações Rápidas</h2>
            <div class="action-grid">
                <a href="/flashcards" class="action-btn">
                    <i class="fas fa-play-circle"></i>
                    Iniciar Revisão
                </a>
                <button class="action-btn" onclick="createQuickDeck()">
                    <i class="fas fa-plus-circle"></i>
                    Criar Baralho
                </button>
                <button class="action-btn" onclick="testAllAPIs()">
                    <i class="fas fa-network-wired"></i>
                    Testar APIs
                </button>
                <a href="/profile" class="action-btn">
                    <i class="fas fa-user-circle"></i>
                    Meu Perfil
                </a>
                <a href="/settings" class="action-btn">
                    <i class="fas fa-cog"></i>
                    Configurações
                </a>
                <button class="action-btn" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i>
                    Atualizar
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // Check services status
        async function checkServices() {
            const services = [
                { name: 'PostgreSQL', url: null, icon: 'fas fa-database' },
                { name: 'Django', url: 'http://localhost:8003', icon: 'fas fa-server' },
                { name: 'FastAPI', url: 'http://localhost:8001/health', icon: 'fas fa-bolt' },
                { name: 'Frontend', url: 'http://localhost:8082', icon: 'fas fa-window-maximize' },
                { name: 'Flashcards API', url: 'http://localhost:8001/api/flashcards/decks/', icon: 'fas fa-layer-group' }
            ];
            
            const statusGrid = document.getElementById('statusGrid');
            statusGrid.innerHTML = '';
            
            for (const service of services) {
                const statusItem = document.createElement('div');
                statusItem.className = 'status-item';
                
                let isActive = false;
                if (service.url) {
                    try {
                        const response = await fetch(service.url);
                        isActive = response.ok;
                    } catch (error) {
                        isActive = false;
                    }
                } else {
                    // PostgreSQL - assume it's running if other services work
                    isActive = true;
                }
                
                if (isActive) statusItem.classList.add('active');
                
                statusItem.innerHTML = `
                    <i class="${service.icon} status-icon"></i>
                    <div>
                        <strong>${service.name}</strong><br>
                        <small>${isActive ? 'Online' : 'Offline'}</small>
                    </div>
                `;
                
                statusGrid.appendChild(statusItem);
            }
            
            // Load flashcard count
            try {
                const response = await fetch('http://localhost:8001/api/flashcards/decks/');
                const decks = await response.json();
                document.getElementById('flashcardCount').textContent = decks.length;
            } catch (error) {
                console.error('Error loading flashcards:', error);
            }
        }
        
        async function createQuickDeck() {
            const name = prompt('Nome do novo baralho:');
            if (!name) return;
            
            try {
                const response = await fetch('http://localhost:8001/api/flashcards/decks/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        name,
                        description: 'Criado via Quick Action',
                        color: '#' + Math.floor(Math.random()*16777215).toString(16)
                    })
                });
                
                if (response.ok) {
                    alert('Baralho criado com sucesso!');
                    checkServices();
                } else {
                    alert('Erro ao criar baralho');
                }
            } catch (error) {
                alert('Erro: ' + error.message);
            }
        }
        
        async function testAllAPIs() {
            const apis = [
                { name: 'Health Check', url: 'http://localhost:8001/health' },
                { name: 'Flashcards', url: 'http://localhost:8001/api/flashcards/decks/' },
                { name: 'Django Admin', url: 'http://localhost:8003/admin/' }
            ];
            
            let results = 'Resultados dos testes:\n\n';
            
            for (const api of apis) {
                try {
                    const response = await fetch(api.url);
                    results += `✅ ${api.name}: ${response.ok ? 'OK' : 'Erro'} (Status: ${response.status})\n`;
                } catch (error) {
                    results += `❌ ${api.name}: Falha na conexão\n`;
                }
            }
            
            alert(results);
        }
        
        // Check services on load
        checkServices();
        
        // Refresh every 30 seconds
        setInterval(checkServices, 30000);
    </script>
</body>
</html>