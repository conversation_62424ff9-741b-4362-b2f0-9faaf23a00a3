<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Direto - Gerador Ultra AI</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #6366f1;
            margin-bottom: 30px;
        }
        .test-links {
            background: #1e293b;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        .link-item {
            display: block;
            padding: 15px 25px;
            margin: 10px 0;
            background: #334155;
            color: #f1f5f9;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .link-item:hover {
            background: #6366f1;
            transform: translateX(10px);
        }
        .status {
            background: #1e293b;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        .status-item {
            margin: 10px 0;
            padding: 10px;
            background: #334155;
            border-radius: 6px;
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .info { color: #3b82f6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Teste Direto - Gerador de Questões Ultra AI</h1>
        
        <div class="status">
            <h2>Status dos Serviços</h2>
            <div id="status-container">
                <div class="status-item">Verificando serviços...</div>
            </div>
        </div>

        <div class="test-links">
            <h2>Links de Acesso Direto</h2>
            <a href="/" class="link-item">🏠 Home (Requer Login)</a>
            <a href="/login" class="link-item">🔐 Página de Login</a>
            <a href="/ai-tools/question-generator" class="link-item">🤖 Gerador de Questões Standard</a>
            <a href="/ai-tools/question-generator-ultra" class="link-item">⚡ Gerador de Questões Ultra AI</a>
            <a href="/test-ultra-questions.html" class="link-item">🧪 Teste HTML Ultra Questions</a>
            <a href="/test-ultra.html" class="link-item">🧪 Teste HTML Ultra</a>
        </div>

        <div class="test-links">
            <h2>Teste de API</h2>
            <button onclick="testAPI()" style="padding: 10px 20px; background: #6366f1; color: white; border: none; border-radius: 6px; cursor: pointer;">
                Testar API Backend
            </button>
            <div id="api-result" style="margin-top: 20px;"></div>
        </div>
    </div>

    <script>
        // Verificar status dos serviços
        async function checkStatus() {
            const statusContainer = document.getElementById('status-container');
            statusContainer.innerHTML = '';

            // Verificar Frontend
            try {
                const response = await fetch('/');
                statusContainer.innerHTML += '<div class="status-item success">✅ Frontend: Rodando na porta 8082</div>';
            } catch (error) {
                statusContainer.innerHTML += '<div class="status-item error">❌ Frontend: Erro ao conectar</div>';
            }

            // Verificar Backend
            try {
                const response = await fetch('http://localhost:8001/health');
                const data = await response.json();
                statusContainer.innerHTML += '<div class="status-item success">✅ Backend: ' + JSON.stringify(data.status) + '</div>';
            } catch (error) {
                statusContainer.innerHTML += '<div class="status-item error">❌ Backend: Erro ao conectar na porta 8001</div>';
            }

            // Verificar rota Ultra Questions
            try {
                const response = await fetch('http://localhost:8001/api/ultra-questions/test');
                if (response.ok) {
                    statusContainer.innerHTML += '<div class="status-item success">✅ API Ultra Questions: Disponível</div>';
                } else {
                    statusContainer.innerHTML += '<div class="status-item info">ℹ️ API Ultra Questions: Status ' + response.status + '</div>';
                }
            } catch (error) {
                statusContainer.innerHTML += '<div class="status-item error">❌ API Ultra Questions: Não disponível</div>';
            }
        }

        // Testar API
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<div class="status-item">Testando API...</div>';

            try {
                const response = await fetch('http://localhost:8001/api/ultra-questions/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        topic: "Cardiologia",
                        difficulty: "intermediate",
                        quantity: 1,
                        ai_model: "gpt-4-turbo"
                    })
                });

                const data = await response.json();
                resultDiv.innerHTML = '<div class="status-item success">✅ API respondeu: <pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
            } catch (error) {
                resultDiv.innerHTML = '<div class="status-item error">❌ Erro na API: ' + error.message + '</div>';
            }
        }

        // Verificar status ao carregar
        checkStatus();
        
        // Atualizar status a cada 5 segundos
        setInterval(checkStatus, 5000);
    </script>
</body>
</html>