<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuestAI - Teste Direto</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .input-area {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1em;
            resize: vertical;
            min-height: 150px;
        }
        
        .input-area:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .option-card {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option-card:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option-card.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .generate-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 10px;
            font-size: 1.2em;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
        }
        
        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 10px;
        }
        
        .question-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .question-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .question-number {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .question-type {
            background: #e0e0e0;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        
        .question-text {
            font-size: 1.1em;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .options {
            margin-left: 20px;
        }
        
        .option {
            padding: 10px;
            margin-bottom: 10px;
            background: #f5f5f5;
            border-radius: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .option.correct {
            background: #d4edda;
            border: 1px solid #28a745;
        }
        
        .explanation {
            margin-top: 15px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-brain"></i> QuestAI - Teste Direto</h1>
            <p>Gerador Inteligente de Questões Médicas</p>
        </div>
        
        <div class="content">
            <!-- Input Section -->
            <div class="section">
                <h2><i class="fas fa-file-medical"></i> Conteúdo para Gerar Questões</h2>
                <textarea id="contentInput" class="input-area" placeholder="Cole ou digite o conteúdo médico aqui...">
A insuficiência cardíaca é uma síndrome clínica complexa resultante de qualquer alteração estrutural ou funcional do coração que prejudique sua capacidade de enchimento ou ejeção de sangue. Os principais sintomas incluem dispneia, fadiga e retenção de líquidos. O diagnóstico é baseado em critérios clínicos, exames laboratoriais como BNP/NT-proBNP, e ecocardiografia. O tratamento inclui medidas não farmacológicas e uso de medicamentos como IECA/BRA, betabloqueadores, antagonistas da aldosterona e diuréticos.
                </textarea>
            </div>
            
            <!-- Question Types -->
            <div class="section">
                <h2><i class="fas fa-list"></i> Tipos de Questão</h2>
                <div class="options-grid" id="questionTypes">
                    <div class="option-card selected" data-type="multiple">
                        <i class="fas fa-list-ul"></i> Múltipla Escolha
                    </div>
                    <div class="option-card" data-type="true-false">
                        <i class="fas fa-check-double"></i> Verdadeiro/Falso
                    </div>
                    <div class="option-card" data-type="clinical">
                        <i class="fas fa-user-md"></i> Caso Clínico
                    </div>
                </div>
            </div>
            
            <!-- Difficulty -->
            <div class="section">
                <h2><i class="fas fa-signal"></i> Dificuldade</h2>
                <div class="options-grid" id="difficulty">
                    <div class="option-card" data-level="basic">
                        <i class="fas fa-seedling"></i> Básico
                    </div>
                    <div class="option-card selected" data-level="intermediate">
                        <i class="fas fa-fire"></i> Intermediário
                    </div>
                    <div class="option-card" data-level="advanced">
                        <i class="fas fa-rocket"></i> Avançado
                    </div>
                </div>
            </div>
            
            <!-- Generate Button -->
            <button id="generateBtn" class="generate-btn">
                <i class="fas fa-magic"></i> Gerar Questões com IA
            </button>
            
            <!-- Loading -->
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>Gerando questões inteligentes...</p>
            </div>
            
            <!-- Results -->
            <div id="results" class="results" style="display: none;">
                <h2>Questões Geradas</h2>
                <div class="stats" id="stats"></div>
                <div id="questionsList"></div>
            </div>
        </div>
    </div>
    
    <script>
        // Selected options
        let selectedTypes = ['multiple'];
        let selectedDifficulty = 'intermediate';
        
        // Event listeners for option cards
        document.querySelectorAll('#questionTypes .option-card').forEach(card => {
            card.addEventListener('click', function() {
                this.classList.toggle('selected');
                const type = this.dataset.type;
                
                if (this.classList.contains('selected')) {
                    if (!selectedTypes.includes(type)) {
                        selectedTypes.push(type);
                    }
                } else {
                    selectedTypes = selectedTypes.filter(t => t !== type);
                }
                
                // Ensure at least one type is selected
                if (selectedTypes.length === 0) {
                    this.classList.add('selected');
                    selectedTypes.push(type);
                }
            });
        });
        
        document.querySelectorAll('#difficulty .option-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('#difficulty .option-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedDifficulty = this.dataset.level;
            });
        });
        
        // Generate button
        document.getElementById('generateBtn').addEventListener('click', async function() {
            const content = document.getElementById('contentInput').value.trim();
            
            if (!content) {
                alert('Por favor, adicione conteúdo para gerar questões.');
                return;
            }
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            this.disabled = true;
            
            try {
                const response = await fetch('http://localhost:8001/api/quest-ai/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: content,
                        content_type: 'text',
                        question_types: selectedTypes,
                        difficulty: selectedDifficulty,
                        count: 5,
                        specialty: 'cardiology',
                        include_explanations: true,
                        include_citations: true
                    })
                });
                
                if (!response.ok) {
                    throw new Error('Erro ao gerar questões');
                }
                
                const data = await response.json();
                displayResults(data);
                
            } catch (error) {
                console.error('Erro:', error);
                alert('Erro ao gerar questões. Verifique se o servidor está rodando.');
            } finally {
                document.getElementById('loading').style.display = 'none';
                this.disabled = false;
            }
        });
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            const statsDiv = document.getElementById('stats');
            const questionsDiv = document.getElementById('questionsList');
            
            // Show stats
            statsDiv.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${data.questions.length}</div>
                    <div class="stat-label">Questões</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${data.metadata.specialty}</div>
                    <div class="stat-label">Especialidade</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${data.metadata.difficulty}</div>
                    <div class="stat-label">Dificuldade</div>
                </div>
            `;
            
            // Show questions
            questionsDiv.innerHTML = '';
            data.questions.forEach((question, index) => {
                const questionCard = document.createElement('div');
                questionCard.className = 'question-card';
                
                let optionsHtml = '';
                if (question.options) {
                    optionsHtml = '<div class="options">';
                    question.options.forEach((opt, i) => {
                        optionsHtml += `
                            <div class="option ${opt.correct ? 'correct' : ''}">
                                <strong>${String.fromCharCode(65 + i)})</strong> ${opt.text}
                                ${opt.correct ? '<i class="fas fa-check" style="color: #28a745; margin-left: auto;"></i>' : ''}
                            </div>
                        `;
                    });
                    optionsHtml += '</div>';
                }
                
                let answerHtml = '';
                if (question.type === 'true-false') {
                    answerHtml = `
                        <div style="margin: 15px 0;">
                            <strong>Resposta:</strong> 
                            <span style="color: ${question.answer ? '#28a745' : '#dc3545'}; font-weight: bold;">
                                ${question.answer ? 'Verdadeiro' : 'Falso'}
                            </span>
                        </div>
                    `;
                } else if (question.type === 'clinical' && question.answer) {
                    answerHtml = `
                        <div style="margin: 15px 0; padding: 15px; background: #e8f5e9; border-radius: 5px;">
                            <strong>Resposta:</strong><br>
                            ${question.answer}
                        </div>
                    `;
                }
                
                questionCard.innerHTML = `
                    <div class="question-header">
                        <span class="question-number">#${index + 1}</span>
                        <span class="question-type">${question.type}</span>
                        <span style="margin-left: auto; color: #666;">
                            <i class="fas fa-signal"></i> ${question.difficulty}
                        </span>
                    </div>
                    <div class="question-text">${question.text}</div>
                    ${optionsHtml}
                    ${answerHtml}
                    ${question.explanation ? `
                        <div class="explanation">
                            <i class="fas fa-lightbulb"></i> <strong>Explicação:</strong> ${question.explanation}
                        </div>
                    ` : ''}
                    ${question.citations ? `
                        <div style="margin-top: 10px; color: #666; font-size: 0.9em;">
                            <i class="fas fa-book"></i> ${question.citations}
                        </div>
                    ` : ''}
                `;
                
                questionsDiv.appendChild(questionCard);
            });
            
            resultsDiv.style.display = 'block';
        }
        
        // Test with sample data
        function testWithSampleData() {
            const sampleData = {
                questions: [
                    {
                        type: 'multiple',
                        difficulty: 'intermediate',
                        text: 'Qual dos seguintes medicamentos é considerado de primeira linha no tratamento da insuficiência cardíaca com fração de ejeção reduzida?',
                        options: [
                            { text: 'Enalapril', correct: true },
                            { text: 'Digoxina', correct: false },
                            { text: 'Amiodarona', correct: false },
                            { text: 'Verapamil', correct: false }
                        ],
                        explanation: 'Os IECA como o enalapril são medicamentos de primeira linha no tratamento da IC com FE reduzida.',
                        citations: 'Diretrizes Brasileiras de Insuficiência Cardíaca, 2023'
                    }
                ],
                metadata: {
                    total_questions: 1,
                    specialty: 'cardiology',
                    difficulty: 'intermediate'
                }
            };
            
            displayResults(sampleData);
        }
    </script>
</body>
</html>