<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuestAI - Demonstração Completa</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f0f1e;
            color: #e0e0e0;
            line-height: 1.6;
        }
        
        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }
        
        .glow-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(80px);
            opacity: 0.4;
            animation: float 20s infinite ease-in-out;
        }
        
        .orb-1 {
            width: 600px;
            height: 600px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            top: -200px;
            left: -200px;
            animation-duration: 25s;
        }
        
        .orb-2 {
            width: 400px;
            height: 400px;
            background: linear-gradient(135deg, #f093fb, #f5576c);
            bottom: -100px;
            right: -100px;
            animation-duration: 20s;
            animation-delay: 5s;
        }
        
        .orb-3 {
            width: 500px;
            height: 500px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-duration: 30s;
            animation-delay: 10s;
        }
        
        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            25% { transform: translate(50px, -50px) scale(1.1); }
            50% { transform: translate(-30px, 30px) scale(0.9); }
            75% { transform: translate(40px, 20px) scale(1.05); }
        }
        
        /* Header */
        .header {
            background: rgba(15, 15, 30, 0.9);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 30px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logo-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            color: white;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .logo h1 {
            font-size: 2.5em;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header-stats {
            display: flex;
            gap: 30px;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.8em;
            font-weight: 700;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #888;
        }
        
        /* Main Container */
        .container {
            max-width: 1400px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        /* Hero Section */
        .hero {
            text-align: center;
            padding: 60px 0;
            margin-bottom: 60px;
        }
        
        .hero h2 {
            font-size: 3em;
            font-weight: 800;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #fff, #ccc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .hero p {
            font-size: 1.3em;
            color: #aaa;
            max-width: 800px;
            margin: 0 auto 40px;
        }
        
        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
        }
        
        .cta-btn {
            padding: 15px 40px;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .cta-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .cta-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }
        
        .cta-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .cta-secondary:hover {
            background: rgba(255, 255, 255, 0.15);
        }
        
        /* Demo Section */
        .demo-section {
            background: rgba(20, 20, 40, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
        }
        
        .section-header {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .section-title i {
            color: #667eea;
        }
        
        .section-subtitle {
            color: #aaa;
            font-size: 1.1em;
        }
        
        /* Input Area */
        .input-controls {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .text-input {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            color: white;
            font-size: 1.1em;
            min-height: 150px;
            resize: vertical;
        }
        
        .text-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }
        
        .input-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .option-group {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
        }
        
        .option-group h4 {
            margin-bottom: 10px;
            color: #667eea;
        }
        
        .option-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .option-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }
        
        .option-checkbox input {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .generate-btn {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 20px;
            border-radius: 15px;
            font-size: 1.2em;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .generate-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }
        
        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .spinner {
            width: 24px;
            height: 24px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top-color: white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Analysis Display */
        .analysis-container {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.3);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .analysis-item {
            text-align: center;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }
        
        .analysis-value {
            font-size: 2em;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .analysis-label {
            color: #aaa;
            font-size: 0.9em;
        }
        
        .entity-container {
            margin-top: 20px;
        }
        
        .entity-group {
            margin-bottom: 15px;
        }
        
        .entity-label {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .entity-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .entity-tag {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            background: rgba(102, 126, 234, 0.2);
            border: 1px solid rgba(102, 126, 234, 0.3);
        }
        
        /* Questions Display */
        .questions-container {
            display: grid;
            gap: 20px;
        }
        
        .question-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s;
        }
        
        .question-card:hover {
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
        }
        
        .question-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .question-number {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 8px;
            font-weight: 700;
        }
        
        .question-type {
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .question-difficulty {
            margin-left: auto;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .question-text {
            font-size: 1.2em;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .options-list {
            display: grid;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .option-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            transition: all 0.2s;
        }
        
        .option-item.correct {
            border-color: #22c55e;
            background: rgba(34, 197, 94, 0.1);
        }
        
        .option-letter {
            width: 35px;
            height: 35px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
        }
        
        .option-item.correct .option-letter {
            background: #22c55e;
            color: white;
        }
        
        .explanation-box {
            background: rgba(243, 159, 0, 0.1);
            border: 1px solid rgba(243, 159, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            display: flex;
            gap: 15px;
        }
        
        .explanation-box i {
            color: #f39f00;
            font-size: 20px;
            flex-shrink: 0;
        }
        
        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .feature-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 36px;
            color: #667eea;
        }
        
        .feature-title {
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .feature-description {
            color: #aaa;
            line-height: 1.6;
        }
        
        /* Examples Section */
        .examples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .example-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .example-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .example-title {
            font-weight: 700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .example-title i {
            color: #667eea;
        }
        
        .example-content {
            color: #aaa;
            font-size: 0.95em;
            line-height: 1.5;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
            }
            
            .hero h2 {
                font-size: 2em;
            }
            
            .cta-buttons {
                flex-direction: column;
                width: 100%;
                max-width: 300px;
                margin: 0 auto;
            }
            
            .input-controls {
                grid-template-columns: 1fr;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .examples-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="bg-animation">
        <div class="glow-orb orb-1"></div>
        <div class="glow-orb orb-2"></div>
        <div class="glow-orb orb-3"></div>
    </div>
    
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h1>QuestAI</h1>
            </div>
            <div class="header-stats">
                <div class="stat">
                    <div class="stat-value" id="totalQuestions">2,847</div>
                    <div class="stat-label">Questões Geradas</div>
                </div>
                <div class="stat">
                    <div class="stat-value">98.5%</div>
                    <div class="stat-label">Precisão</div>
                </div>
                <div class="stat">
                    <div class="stat-value">4.9</div>
                    <div class="stat-label">Avaliação</div>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <div class="container">
        <!-- Hero Section -->
        <section class="hero">
            <h2>Geração Inteligente de Questões Médicas</h2>
            <p>
                Transforme qualquer conteúdo médico em questões de alta qualidade com nossa IA avançada.
                Análise profunda, contextualização precisa e distractores inteligentes.
            </p>
            <div class="cta-buttons">
                <a href="#demo" class="cta-btn cta-primary">
                    <i class="fas fa-magic"></i>
                    Experimentar Agora
                </a>
                <a href="#features" class="cta-btn cta-secondary">
                    <i class="fas fa-info-circle"></i>
                    Como Funciona
                </a>
            </div>
        </section>
        
        <!-- Demo Section -->
        <section id="demo" class="demo-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-flask"></i>
                    Demonstração Interativa
                </h3>
                <p class="section-subtitle">
                    Cole ou digite seu conteúdo médico e veja a mágica acontecer
                </p>
            </div>
            
            <!-- Examples -->
            <div class="examples-grid">
                <div class="example-card" onclick="loadExample('cardiology')">
                    <div class="example-title">
                        <i class="fas fa-heart"></i>
                        Cardiologia
                    </div>
                    <div class="example-content">
                        Insuficiência cardíaca, fibrilação atrial, infarto do miocárdio...
                    </div>
                </div>
                <div class="example-card" onclick="loadExample('neurology')">
                    <div class="example-title">
                        <i class="fas fa-brain"></i>
                        Neurologia
                    </div>
                    <div class="example-content">
                        AVC isquêmico, epilepsia, doença de Parkinson...
                    </div>
                </div>
                <div class="example-card" onclick="loadExample('pediatrics')">
                    <div class="example-title">
                        <i class="fas fa-baby"></i>
                        Pediatria
                    </div>
                    <div class="example-content">
                        Bronquiolite, vacinação, desenvolvimento infantil...
                    </div>
                </div>
            </div>
            
            <!-- Input Area -->
            <div class="input-controls">
                <textarea 
                    id="contentInput" 
                    class="text-input" 
                    placeholder="Cole ou digite o conteúdo médico aqui..."
                ></textarea>
                
                <div class="input-options">
                    <div class="option-group">
                        <h4>Tipos de Questão</h4>
                        <div class="option-list">
                            <label class="option-checkbox">
                                <input type="checkbox" value="multiple" checked>
                                <span>Múltipla Escolha</span>
                            </label>
                            <label class="option-checkbox">
                                <input type="checkbox" value="true-false" checked>
                                <span>Verdadeiro/Falso</span>
                            </label>
                            <label class="option-checkbox">
                                <input type="checkbox" value="clinical">
                                <span>Caso Clínico</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="option-group">
                        <h4>Quantidade</h4>
                        <input type="number" id="questionCount" value="5" min="1" max="20" 
                               style="width: 100%; padding: 8px; background: rgba(0,0,0,0.3); 
                                      border: 1px solid rgba(255,255,255,0.1); border-radius: 5px; 
                                      color: white; text-align: center;">
                    </div>
                </div>
                
                <button id="generateBtn" class="generate-btn" onclick="generateQuestions()">
                    <i class="fas fa-magic"></i>
                    <span>Gerar Questões Inteligentes</span>
                </button>
            </div>
            
            <!-- Results Area -->
            <div id="resultsArea" style="display: none;">
                <!-- Analysis Display -->
                <div id="analysisDisplay"></div>
                
                <!-- Questions Display -->
                <div id="questionsDisplay"></div>
            </div>
        </section>
        
        <!-- Features Section -->
        <section id="features" class="demo-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-star"></i>
                    Recursos Avançados
                </h3>
                <p class="section-subtitle">
                    Tecnologia de ponta para criar questões médicas de alta qualidade
                </p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <h4 class="feature-title">Análise Profunda</h4>
                    <p class="feature-description">
                        Extração inteligente de conceitos, entidades médicas e relações 
                        complexas do conteúdo fornecido.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-random"></i>
                    </div>
                    <h4 class="feature-title">Distractores Inteligentes</h4>
                    <p class="feature-description">
                        Geração de alternativas plausíveis baseadas em confusões comuns 
                        e termos relacionados.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h4 class="feature-title">Pedagogicamente Otimizado</h4>
                    <p class="feature-description">
                        Questões alinhadas com metodologias de ensino médico e 
                        taxonomia de Bloom.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4 class="feature-title">Níveis de Dificuldade</h4>
                    <p class="feature-description">
                        Ajuste automático da complexidade baseado no conteúdo e 
                        objetivos de aprendizagem.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <h4 class="feature-title">Contextualização Precisa</h4>
                    <p class="feature-description">
                        Questões que mantêm fidelidade ao conteúdo original com 
                        referências diretas.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="feature-title">Validação de Qualidade</h4>
                    <p class="feature-description">
                        Sistema de confiança que garante questões precisas e 
                        educacionalmente valiosas.
                    </p>
                </div>
            </div>
        </section>
    </div>
    
    <script>
        // Example contents
        const examples = {
            cardiology: `A insuficiência cardíaca é uma síndrome clínica complexa resultante de qualquer alteração estrutural ou funcional do coração que prejudique sua capacidade de enchimento ou ejeção de sangue. Os principais sintomas incluem dispneia, fadiga e retenção de líquidos. O diagnóstico é baseado em critérios clínicos, exames laboratoriais como BNP/NT-proBNP, e ecocardiografia. O tratamento inclui medidas não farmacológicas e uso de medicamentos como IECA/BRA, betabloqueadores, antagonistas da aldosterona e diuréticos. A classificação funcional da NYHA divide os pacientes em quatro classes baseadas na limitação de atividade física.`,
            
            neurology: `O acidente vascular cerebral isquêmico resulta da interrupção do fluxo sanguíneo cerebral, geralmente por trombose ou embolia. Os sintomas incluem hemiparesia súbita, afasia, disartria, hemianopsia e alteração do nível de consciência. O diagnóstico requer neuroimagem urgente com TC ou RM. O tratamento na fase aguda inclui trombólise com alteplase dentro de 4,5 horas do início dos sintomas em pacientes elegíveis, e trombectomia mecânica até 24 horas em casos selecionados. A prevenção secundária envolve antiagregação plaquetária com AAS ou clopidogrel.`,
            
            pediatrics: `A bronquiolite viral aguda é a principal causa de hospitalização em lactentes, sendo o vírus sincicial respiratório (VSR) o agente mais comum. Manifesta-se com coriza, tosse, taquipneia, sibilos e esforço respiratório. O diagnóstico é clínico, não sendo necessários exames laboratoriais de rotina. O tratamento é principalmente de suporte, incluindo hidratação adequada, oxigenioterapia se saturação <92%, e aspiração nasal. Broncodilatadores e corticoides não são recomendados rotineiramente.`
        };
        
        function loadExample(specialty) {
            document.getElementById('contentInput').value = examples[specialty];
        }
        
        let isGenerating = false;
        
        async function generateQuestions() {
            if (isGenerating) return;
            
            const content = document.getElementById('contentInput').value.trim();
            if (!content) {
                alert('Por favor, adicione conteúdo médico para gerar questões.');
                return;
            }
            
            // Get selected question types
            const checkboxes = document.querySelectorAll('.option-checkbox input:checked');
            const questionTypes = Array.from(checkboxes).map(cb => cb.value);
            
            if (questionTypes.length === 0) {
                alert('Por favor, selecione pelo menos um tipo de questão.');
                return;
            }
            
            const count = parseInt(document.getElementById('questionCount').value);
            
            // Update button state
            const btn = document.getElementById('generateBtn');
            const originalContent = btn.innerHTML;
            btn.innerHTML = '<div class="spinner"></div><span>Gerando questões...</span>';
            btn.disabled = true;
            isGenerating = true;
            
            // Clear previous results
            document.getElementById('resultsArea').style.display = 'none';
            
            try {
                const response = await fetch('http://localhost:8001/api/quest-ai/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: content,
                        content_type: 'text',
                        question_types: questionTypes,
                        difficulty: 'intermediate',
                        count: count,
                        include_explanations: true,
                        include_citations: false
                    })
                });
                
                if (!response.ok) {
                    throw new Error('Erro ao gerar questões');
                }
                
                const data = await response.json();
                displayResults(data);
                
                // Update stats
                const currentTotal = parseInt(document.getElementById('totalQuestions').textContent.replace(',', ''));
                document.getElementById('totalQuestions').textContent = (currentTotal + data.questions.length).toLocaleString();
                
            } catch (error) {
                console.error('Erro:', error);
                alert('Erro ao gerar questões. Verifique se o servidor está rodando.');
            } finally {
                btn.innerHTML = originalContent;
                btn.disabled = false;
                isGenerating = false;
            }
        }
        
        function displayResults(data) {
            // Show results area
            document.getElementById('resultsArea').style.display = 'block';
            
            // Display analysis
            displayAnalysis(data.content_analysis, data.metadata);
            
            // Display questions
            displayQuestions(data.questions);
            
            // Smooth scroll to results
            document.getElementById('resultsArea').scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
        
        function displayAnalysis(analysis, metadata) {
            const analysisHtml = `
                <div class="analysis-container">
                    <h4 style="margin-bottom: 20px; font-size: 1.3em;">
                        <i class="fas fa-chart-line"></i> Análise do Conteúdo
                    </h4>
                    
                    <div class="analysis-grid">
                        <div class="analysis-item">
                            <div class="analysis-value">${metadata.content_stats.word_count}</div>
                            <div class="analysis-label">Palavras</div>
                        </div>
                        <div class="analysis-item">
                            <div class="analysis-value">${metadata.content_stats.concepts_found}</div>
                            <div class="analysis-label">Conceitos</div>
                        </div>
                        <div class="analysis-item">
                            <div class="analysis-value">${metadata.content_stats.entities_extracted}</div>
                            <div class="analysis-label">Entidades</div>
                        </div>
                        <div class="analysis-item">
                            <div class="analysis-value">${analysis.complexity}</div>
                            <div class="analysis-label">Complexidade</div>
                        </div>
                    </div>
                    
                    <div class="entity-container">
                        ${Object.entries(analysis.main_entities).map(([type, entities]) => {
                            if (entities.length === 0) return '';
                            return `
                                <div class="entity-group">
                                    <div class="entity-label">${getEntityTypeLabel(type)}</div>
                                    <div class="entity-tags">
                                        ${entities.map(entity => `<span class="entity-tag">${entity}</span>`).join('')}
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `;
            
            document.getElementById('analysisDisplay').innerHTML = analysisHtml;
        }
        
        function displayQuestions(questions) {
            const questionsHtml = questions.map((question, index) => {
                let contentHtml = '';
                
                if (question.type === 'multiple' && question.options) {
                    contentHtml = `
                        <div class="options-list">
                            ${question.options.map((option, i) => `
                                <div class="option-item ${option.correct ? 'correct' : ''}">
                                    <span class="option-letter">${String.fromCharCode(65 + i)}</span>
                                    <span>${option.text}</span>
                                    ${option.correct ? '<i class="fas fa-check" style="margin-left: auto; color: #22c55e;"></i>' : ''}
                                </div>
                            `).join('')}
                        </div>
                    `;
                } else if (question.type === 'true-false') {
                    contentHtml = `
                        <div style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 10px; margin-bottom: 20px;">
                            <strong>Resposta:</strong> 
                            <span style="color: ${question.answer ? '#22c55e' : '#ef4444'}; font-weight: 700; font-size: 1.1em;">
                                ${question.answer ? 'Verdadeiro' : 'Falso'}
                            </span>
                        </div>
                    `;
                } else if (question.type === 'clinical' && question.answer) {
                    contentHtml = `
                        <div style="padding: 20px; background: rgba(0,0,0,0.3); border-radius: 10px; margin-bottom: 20px;">
                            <strong>Resposta Esperada:</strong><br>
                            <p style="margin-top: 10px; line-height: 1.6;">${question.answer}</p>
                        </div>
                    `;
                }
                
                return `
                    <div class="question-card">
                        <div class="question-header">
                            <span class="question-number">#${index + 1}</span>
                            <span class="question-type">${getQuestionTypeName(question.type)}</span>
                            <span class="question-difficulty">
                                <i class="fas fa-signal"></i>
                                ${question.difficulty}
                            </span>
                        </div>
                        
                        <p class="question-text">${question.text}</p>
                        
                        ${contentHtml}
                        
                        ${question.explanation ? `
                            <div class="explanation-box">
                                <i class="fas fa-lightbulb"></i>
                                <div>
                                    <strong>Explicação:</strong><br>
                                    ${question.explanation}
                                </div>
                            </div>
                        ` : ''}
                        
                        ${question.source_reference ? `
                            <div style="margin-top: 10px; padding: 10px; background: rgba(102,126,234,0.1); 
                                        border-radius: 5px; font-size: 0.9em; color: #aaa; font-style: italic;">
                                <i class="fas fa-quote-left"></i> ${question.source_reference}
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
            
            document.getElementById('questionsDisplay').innerHTML = `
                <h4 style="margin-bottom: 20px; font-size: 1.3em;">
                    <i class="fas fa-list-check"></i> Questões Geradas
                </h4>
                <div class="questions-container">
                    ${questionsHtml}
                </div>
            `;
        }
        
        function getEntityTypeLabel(type) {
            const labels = {
                'conditions': 'Condições',
                'medications': 'Medicamentos',
                'symptoms': 'Sintomas',
                'procedures': 'Procedimentos',
                'anatomy': 'Anatomia'
            };
            return labels[type] || type;
        }
        
        function getQuestionTypeName(type) {
            const names = {
                'multiple': 'Múltipla Escolha',
                'true-false': 'Verdadeiro/Falso',
                'clinical': 'Caso Clínico',
                'open': 'Dissertativa'
            };
            return names[type] || type;
        }
        
        // Auto-update stats
        setInterval(() => {
            const statElements = document.querySelectorAll('.stat-value');
            statElements.forEach(el => {
                if (el.id !== 'totalQuestions') {
                    const current = parseFloat(el.textContent);
                    const change = (Math.random() - 0.5) * 0.1;
                    const newValue = Math.max(0, current + change);
                    el.textContent = newValue.toFixed(1);
                }
            });
        }, 5000);
    </script>
</body>
</html>