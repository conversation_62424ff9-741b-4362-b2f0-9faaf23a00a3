<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MentorAI - Demonstração Interativa</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0a;
            color: #fff;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            position: relative;
        }
        
        .logo {
            font-size: 5em;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradient 3s ease infinite;
            margin-bottom: 20px;
        }
        
        @keyframes gradient {
            0%, 100% { filter: hue-rotate(0deg); }
            50% { filter: hue-rotate(30deg); }
        }
        
        .live-status {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid #22c55e;
            padding: 10px 20px;
            border-radius: 30px;
            margin-bottom: 30px;
        }
        
        .live-dot {
            width: 10px;
            height: 10px;
            background: #22c55e;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(34, 197, 94, 0); }
            100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .demo-title {
            font-size: 2em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .demo-icon {
            font-size: 1.5em;
            color: #3b82f6;
        }
        
        .deck-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .deck-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }
        
        .deck-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .deck-card:hover::before {
            opacity: 1;
        }
        
        .deck-card:hover {
            transform: translateY(-5px) scale(1.02);
            border-color: #3b82f6;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
        }
        
        .deck-name {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .deck-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            font-size: 0.9em;
            color: #94a3b8;
        }
        
        .action-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border: none;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            text-decoration: none;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.4);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin: 30px 0;
        }
        
        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .metric-label {
            color: #64748b;
            margin-top: 5px;
        }
        
        .console {
            background: #000;
            border: 1px solid #333;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .console-line {
            margin: 5px 0;
            opacity: 0;
            animation: fadeIn 0.5s forwards;
        }
        
        .console-line.success { color: #22c55e; }
        .console-line.info { color: #3b82f6; }
        .console-line.warning { color: #f59e0b; }
        
        @keyframes fadeIn {
            to { opacity: 1; }
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s;
        }
        
        .feature-card:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: #3b82f6;
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
            color: #3b82f6;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <h1>MentorAI - Sistema de Aprendizado Médico</h1>
            <div class="live-status">
                <div class="live-dot"></div>
                <span>Sistema 100% Operacional</span>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">
                <i class="fas fa-chart-line demo-icon"></i>
                Métricas em Tempo Real
            </h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="totalDecks">5</div>
                    <div class="metric-label">Baralhos</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="totalCards">16</div>
                    <div class="metric-label">Flashcards</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="activeUsers">1</div>
                    <div class="metric-label">Usuário Ativo</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">100%</div>
                    <div class="metric-label">Uptime</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">
                <i class="fas fa-layer-group demo-icon"></i>
                Baralhos Disponíveis
                <div class="loading" id="loadingDecks" style="display: none;"></div>
            </h2>
            <div class="deck-showcase" id="deckShowcase">
                <!-- Decks serão carregados aqui -->
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">
                <i class="fas fa-terminal demo-icon"></i>
                Console do Sistema
            </h2>
            <div class="console" id="console">
                <div class="console-line success">[OK] PostgreSQL conectado na porta 5432</div>
                <div class="console-line success">[OK] Django Admin rodando na porta 8003</div>
                <div class="console-line success">[OK] FastAPI rodando na porta 8001</div>
                <div class="console-line success">[OK] Frontend Vue.js rodando na porta 8082</div>
                <div class="console-line info">[INFO] Sistema de flashcards carregado</div>
                <div class="console-line info">[INFO] IA Neural ativada para repetição espaçada</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">
                <i class="fas fa-rocket demo-icon"></i>
                Ações Rápidas
            </h2>
            <div class="action-panel">
                <button class="action-btn" onclick="createNewDeck()">
                    <i class="fas fa-plus"></i>
                    Criar Novo Baralho
                </button>
                <a href="/flashcards" class="action-btn">
                    <i class="fas fa-play"></i>
                    Iniciar Revisão
                </a>
                <button class="action-btn" onclick="testAPI()">
                    <i class="fas fa-vial"></i>
                    Testar API
                </button>
                <a href="/ai-tools" class="action-btn">
                    <i class="fas fa-robot"></i>
                    Ferramentas IA
                </a>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">
                <i class="fas fa-th demo-icon"></i>
                Funcionalidades Principais
            </h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <i class="fas fa-brain feature-icon"></i>
                    <h3>Second Brain</h3>
                    <p>Chat inteligente com IA</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-calendar-alt feature-icon"></i>
                    <h3>Calendário</h3>
                    <p>Organize seus estudos</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-chart-line feature-icon"></i>
                    <h3>Dashboard</h3>
                    <p>Acompanhe seu progresso</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-robot feature-icon"></i>
                    <h3>AI Tools</h3>
                    <p>Ferramentas avançadas</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-book feature-icon"></i>
                    <h3>Recursos</h3>
                    <p>Material de estudo</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-cog feature-icon"></i>
                    <h3>Configurações</h3>
                    <p>Personalize o sistema</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Carregar decks
        async function loadDecks() {
            const showcase = document.getElementById('deckShowcase');
            const loading = document.getElementById('loadingDecks');
            
            loading.style.display = 'inline-block';
            showcase.innerHTML = '';
            
            try {
                const response = await fetch('http://localhost:8001/api/flashcards/decks/');
                const decks = await response.json();
                
                let totalCards = 0;
                
                decks.forEach((deck, index) => {
                    totalCards += deck.card_count || 0;
                    
                    const deckCard = document.createElement('div');
                    deckCard.className = 'deck-card';
                    deckCard.innerHTML = `
                        <div class="deck-name">${deck.name}</div>
                        <div style="color: #64748b; font-size: 0.9em; margin: 10px 0;">
                            ${deck.description || 'Sem descrição'}
                        </div>
                        <div class="deck-stats">
                            <span><i class="fas fa-layer-group"></i> ${deck.card_count} cards</span>
                            <span style="color: ${deck.color}">●</span>
                        </div>
                    `;
                    deckCard.style.animationDelay = `${index * 0.1}s`;
                    deckCard.onclick = () => window.open('/flashcards', '_blank');
                    showcase.appendChild(deckCard);
                });
                
                document.getElementById('totalDecks').textContent = decks.length;
                document.getElementById('totalCards').textContent = totalCards;
                
                addConsoleMessage('info', `[INFO] ${decks.length} baralhos carregados com sucesso`);
            } catch (error) {
                addConsoleMessage('warning', '[ERRO] Falha ao carregar baralhos: ' + error.message);
            } finally {
                loading.style.display = 'none';
            }
        }
        
        // Adicionar mensagem ao console
        function addConsoleMessage(type, message) {
            const console = document.getElementById('console');
            const line = document.createElement('div');
            line.className = `console-line ${type}`;
            line.textContent = message;
            console.appendChild(line);
            console.scrollTop = console.scrollHeight;
        }
        
        // Criar novo deck
        async function createNewDeck() {
            const name = prompt('Nome do novo baralho:');
            if (!name) return;
            
            try {
                const response = await fetch('http://localhost:8001/api/flashcards/decks/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        name,
                        description: 'Criado via demonstração interativa',
                        color: '#' + Math.floor(Math.random()*16777215).toString(16)
                    })
                });
                
                if (response.ok) {
                    addConsoleMessage('success', '[OK] Baralho criado com sucesso!');
                    loadDecks();
                }
            } catch (error) {
                addConsoleMessage('warning', '[ERRO] Falha ao criar baralho');
            }
        }
        
        // Testar API
        async function testAPI() {
            addConsoleMessage('info', '[INFO] Testando endpoints da API...');
            
            try {
                const health = await fetch('http://localhost:8001/health');
                if (health.ok) {
                    addConsoleMessage('success', '[OK] Health check passou');
                }
                
                const decks = await fetch('http://localhost:8001/api/flashcards/decks/');
                if (decks.ok) {
                    addConsoleMessage('success', '[OK] Endpoint de flashcards respondendo');
                }
                
                addConsoleMessage('success', '[OK] Todos os testes passaram!');
            } catch (error) {
                addConsoleMessage('warning', '[ERRO] Falha nos testes: ' + error.message);
            }
        }
        
        // Atualizar a cada 5 segundos
        loadDecks();
        setInterval(loadDecks, 5000);
        
        // Animação inicial
        setTimeout(() => {
            addConsoleMessage('info', '[INFO] Auto-refresh ativado (5s)');
        }, 2000);
    </script>
</body>
</html>