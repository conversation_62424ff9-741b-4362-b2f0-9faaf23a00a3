<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MentorAI - System Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f0f1e;
            color: #fff;
            overflow-x: hidden;
        }
        
        .dashboard {
            min-height: 100vh;
            background: radial-gradient(circle at 20% 50%, #1a1a3e 0%, #0f0f1e 40%);
            position: relative;
        }
        
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #667eea;
            border-radius: 50%;
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            from {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            to {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }
        
        .header {
            text-align: center;
            padding: 40px 20px;
            position: relative;
            z-index: 10;
        }
        
        .logo {
            font-size: 4em;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 0 20px;
            max-width: 1200px;
            margin: 0 auto 40px;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }
        
        .status-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
        }
        
        .status-card:hover::before {
            animation: shine 0.5s ease-in-out;
        }
        
        @keyframes shine {
            0% { transform: rotate(45deg) translateY(-100%); }
            100% { transform: rotate(45deg) translateY(100%); }
        }
        
        .status-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .status-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }
        
        .status-card.active .status-icon {
            color: #4ade80;
            animation: spin 2s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .status-title {
            font-size: 1.2em;
            margin-bottom: 10px;
            color: #e2e8f0;
        }
        
        .status-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .features {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(10px);
            transition: all 0.3s;
            cursor: pointer;
            text-decoration: none;
            color: #fff;
            display: block;
        }
        
        .feature-card:hover {
            transform: scale(1.02);
            background: rgba(255, 255, 255, 0.08);
            border-color: #667eea;
        }
        
        .feature-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .feature-icon {
            font-size: 2em;
            color: #667eea;
        }
        
        .feature-title {
            font-size: 1.3em;
            flex: 1;
        }
        
        .feature-status {
            width: 10px;
            height: 10px;
            background: #4ade80;
            border-radius: 50%;
            animation: blink 2s infinite;
        }
        
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        
        .feature-description {
            color: #94a3b8;
            line-height: 1.6;
        }
        
        .metrics {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 20px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .metric-row {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .metric {
            text-align: center;
            flex: 1;
            min-width: 150px;
        }
        
        .metric-value {
            font-size: 3em;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .metric-label {
            color: #94a3b8;
            margin-top: 5px;
        }
        
        .live-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 20px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            gap: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .live-dot {
            width: 12px;
            height: 12px;
            background: #4ade80;
            border-radius: 50%;
            animation: pulse-dot 2s infinite;
        }
        
        @keyframes pulse-dot {
            0% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(74, 222, 128, 0); }
            100% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0); }
        }
        
        .footer {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }
        
        .action-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 30px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="particles" id="particles"></div>
    
    <div class="dashboard">
        <div class="live-indicator">
            <div class="live-dot"></div>
            <span>Sistema Online</span>
        </div>
        
        <div class="header">
            <div class="logo">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <h1>MentorAI System Dashboard</h1>
            <p>Monitoramento em tempo real do sistema de aprendizado médico</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card active">
                <i class="fas fa-database status-icon"></i>
                <h3 class="status-title">PostgreSQL</h3>
                <p class="status-value">ONLINE</p>
            </div>
            
            <div class="status-card active">
                <i class="fas fa-server status-icon"></i>
                <h3 class="status-title">Django Admin</h3>
                <p class="status-value">PORT 8003</p>
            </div>
            
            <div class="status-card active">
                <i class="fas fa-bolt status-icon"></i>
                <h3 class="status-title">FastAPI</h3>
                <p class="status-value">PORT 8001</p>
            </div>
            
            <div class="status-card active">
                <i class="fas fa-window-maximize status-icon"></i>
                <h3 class="status-title">Frontend Vue</h3>
                <p class="status-value">PORT 8082</p>
            </div>
        </div>
        
        <div class="metrics">
            <div class="metric-row">
                <div class="metric">
                    <div class="metric-value" id="deckCount">3</div>
                    <div class="metric-label">Baralhos</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="cardCount">12</div>
                    <div class="metric-label">Flashcards</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="userCount">1</div>
                    <div class="metric-label">Usuários</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="uptime">100%</div>
                    <div class="metric-label">Uptime</div>
                </div>
            </div>
        </div>
        
        <div class="features">
            <h2 style="text-align: center; margin-bottom: 30px;">Funcionalidades Ativas</h2>
            
            <div class="feature-grid">
                <a href="/flashcards" class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-layer-group feature-icon"></i>
                        <h3 class="feature-title">Flashcards</h3>
                        <div class="feature-status"></div>
                    </div>
                    <p class="feature-description">
                        Sistema de flashcards com algoritmo de repetição espaçada e IA neural.
                    </p>
                </a>
                
                <a href="/calendar" class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-calendar-alt feature-icon"></i>
                        <h3 class="feature-title">Calendário</h3>
                        <div class="feature-status"></div>
                    </div>
                    <p class="feature-description">
                        Organize suas sessões de estudo e acompanhe seu progresso.
                    </p>
                </a>
                
                <a href="/second-brain" class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-brain feature-icon"></i>
                        <h3 class="feature-title">Second Brain</h3>
                        <div class="feature-status"></div>
                    </div>
                    <p class="feature-description">
                        Chat inteligente com IA para explorar conceitos médicos.
                    </p>
                </a>
                
                <a href="/ai-tools" class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-robot feature-icon"></i>
                        <h3 class="feature-title">AI Tools</h3>
                        <div class="feature-status"></div>
                    </div>
                    <p class="feature-description">
                        Suite completa de ferramentas de inteligência artificial.
                    </p>
                </a>
                
                <a href="/progress" class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-chart-line feature-icon"></i>
                        <h3 class="feature-title">Dashboard</h3>
                        <div class="feature-status"></div>
                    </div>
                    <p class="feature-description">
                        Visualize seu desempenho com análises detalhadas.
                    </p>
                </a>
                
                <a href="/platform" class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-th feature-icon"></i>
                        <h3 class="feature-title">Plataforma</h3>
                        <div class="feature-status"></div>
                    </div>
                    <p class="feature-description">
                        Acesso unificado a todas as ferramentas do sistema.
                    </p>
                </a>
            </div>
        </div>
        
        <div style="text-align: center; margin: 40px 0;">
            <a href="/app-overview.html" class="action-button">
                <i class="fas fa-th-large"></i> Ver Overview Completo
            </a>
            <a href="/test-flashcards.html" class="action-button">
                <i class="fas fa-vial"></i> Testar APIs
            </a>
            <a href="http://localhost:8001/docs" class="action-button">
                <i class="fas fa-book"></i> Documentação API
            </a>
        </div>
        
        <div class="footer">
            <p>&copy; 2025 MentorAI - Sistema de Aprendizado Médico Inteligente</p>
            <p style="margin-top: 10px;">
                <i class="fas fa-check-circle" style="color: #4ade80;"></i> 
                Todos os sistemas operacionais
            </p>
        </div>
    </div>
    
    <script>
        // Create particles
        const particlesContainer = document.getElementById('particles');
        for (let i = 0; i < 50; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 20 + 's';
            particle.style.animationDuration = (Math.random() * 10 + 20) + 's';
            particlesContainer.appendChild(particle);
        }
        
        // Update metrics
        async function updateMetrics() {
            try {
                const response = await fetch('http://localhost:8001/api/flashcards/decks/');
                const decks = await response.json();
                
                let totalCards = 0;
                decks.forEach(deck => {
                    totalCards += deck.card_count || 0;
                });
                
                document.getElementById('deckCount').textContent = decks.length;
                document.getElementById('cardCount').textContent = totalCards;
            } catch (error) {
                console.error('Error updating metrics:', error);
            }
        }
        
        // Update metrics every 10 seconds
        updateMetrics();
        setInterval(updateMetrics, 10000);
        
        // Animate numbers on load
        const animateValue = (element, start, end, duration) => {
            const range = end - start;
            const increment = range / (duration / 16);
            let current = start;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= end) {
                    current = end;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 16);
        };
        
        // Animate on page load
        window.addEventListener('load', () => {
            const metrics = document.querySelectorAll('.metric-value');
            metrics.forEach(metric => {
                const value = parseInt(metric.textContent);
                if (!isNaN(value)) {
                    animateValue(metric, 0, value, 1000);
                }
            });
        });
    </script>
</body>
</html>