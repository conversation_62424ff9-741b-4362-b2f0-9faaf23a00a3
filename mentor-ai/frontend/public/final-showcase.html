<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MentorAI - Sistema Completo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .hero {
            text-align: center;
            padding: 60px 20px;
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: move 20s linear infinite;
        }
        
        @keyframes move {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }
        
        .hero h1 {
            font-size: 4em;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero .subtitle {
            font-size: 1.5em;
            color: #a8dadc;
            position: relative;
            z-index: 1;
        }
        
        .status-banner {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .status-item {
            display: inline-block;
            margin: 0 20px;
            padding: 10px 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 30px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .status-item.active {
            background: rgba(52, 211, 153, 0.2);
            border-color: #34d399;
        }
        
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .showcase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .showcase-card {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .showcase-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border-color: #60a5fa;
        }
        
        .card-header {
            padding: 20px;
            background: rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .card-icon {
            font-size: 2.5em;
            color: #60a5fa;
        }
        
        .card-title {
            flex: 1;
        }
        
        .card-title h3 {
            font-size: 1.5em;
            margin-bottom: 5px;
        }
        
        .card-status {
            color: #34d399;
            font-size: 0.9em;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .metric {
            text-align: center;
            padding: 15px;
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #60a5fa;
        }
        
        .metric-label {
            font-size: 0.9em;
            color: #94a3b8;
            margin-top: 5px;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            flex: 1;
            padding: 12px 20px;
            background: rgba(96, 165, 250, 0.2);
            border: 1px solid #60a5fa;
            color: #60a5fa;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn:hover {
            background: #60a5fa;
            color: #fff;
            transform: scale(1.05);
        }
        
        .live-demo {
            background: rgba(0,0,0,0.3);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            text-align: center;
        }
        
        .demo-title {
            font-size: 2em;
            margin-bottom: 20px;
            color: #60a5fa;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .demo-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s;
        }
        
        .demo-item:hover {
            background: rgba(255,255,255,0.15);
            transform: scale(1.05);
        }
        
        .links-section {
            background: rgba(255,255,255,0.05);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
        }
        
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .link-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            text-decoration: none;
            color: #fff;
            transition: all 0.3s;
        }
        
        .link-item:hover {
            background: rgba(96, 165, 250, 0.2);
            transform: translateX(5px);
        }
        
        .link-icon {
            color: #60a5fa;
            font-size: 1.5em;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .success-badge {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #34d399;
            color: #064e3b;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(52, 211, 153, 0.3);
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
    </style>
</head>
<body>
    <div class="success-badge">
        <i class="fas fa-check-circle"></i> Sistema 100% Operacional
    </div>
    
    <div class="hero">
        <h1><i class="fas fa-graduation-cap pulse"></i> MentorAI</h1>
        <p class="subtitle">Sistema Completo de Aprendizado Médico com IA</p>
    </div>
    
    <div class="status-banner">
        <span class="status-item active">
            <i class="fas fa-database"></i> PostgreSQL ✓
        </span>
        <span class="status-item active">
            <i class="fas fa-server"></i> Django ✓
        </span>
        <span class="status-item active">
            <i class="fas fa-bolt"></i> FastAPI ✓
        </span>
        <span class="status-item active">
            <i class="fas fa-window-maximize"></i> Frontend ✓
        </span>
    </div>
    
    <div class="main-content">
        <div class="live-demo">
            <h2 class="demo-title">
                <i class="fas fa-chart-line"></i> Estatísticas em Tempo Real
            </h2>
            <div class="demo-grid">
                <div class="demo-item">
                    <div class="metric-value" id="totalDecks">4</div>
                    <div class="metric-label">Baralhos Criados</div>
                </div>
                <div class="demo-item">
                    <div class="metric-value" id="totalCards">15</div>
                    <div class="metric-label">Flashcards Totais</div>
                </div>
                <div class="demo-item">
                    <div class="metric-value">100%</div>
                    <div class="metric-label">Uptime</div>
                </div>
                <div class="demo-item">
                    <div class="metric-value">12</div>
                    <div class="metric-label">Features Ativas</div>
                </div>
            </div>
        </div>
        
        <div class="showcase-grid">
            <!-- Flashcards -->
            <div class="showcase-card" onclick="window.open('/flashcards', '_blank')">
                <div class="card-header">
                    <i class="fas fa-layer-group card-icon"></i>
                    <div class="card-title">
                        <h3>Sistema de Flashcards</h3>
                        <span class="card-status">● Totalmente Funcional</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="metric-grid">
                        <div class="metric">
                            <div class="metric-value">4</div>
                            <div class="metric-label">Decks</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">15</div>
                            <div class="metric-label">Cards</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">IA</div>
                            <div class="metric-label">Neural</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">30s</div>
                            <div class="metric-label">Auto-refresh</div>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <a href="/flashcards" class="btn">Abrir App</a>
                        <a href="/test-flashcards.html" class="btn">Testar API</a>
                    </div>
                </div>
            </div>
            
            <!-- AI Tools -->
            <div class="showcase-card" onclick="window.open('/ai-tools', '_blank')">
                <div class="card-header">
                    <i class="fas fa-robot card-icon"></i>
                    <div class="card-title">
                        <h3>Ferramentas de IA</h3>
                        <span class="card-status">● Online</span>
                    </div>
                </div>
                <div class="card-body">
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin: 10px 0;">✓ Gerador de Questões</li>
                        <li style="margin: 10px 0;">✓ Assistente Inteligente</li>
                        <li style="margin: 10px 0;">✓ Análise de Casos</li>
                        <li style="margin: 10px 0;">✓ Resumos Automáticos</li>
                    </ul>
                    <div class="action-buttons">
                        <a href="/ai-tools" class="btn">Explorar IA</a>
                        <a href="/platform" class="btn">Plataforma</a>
                    </div>
                </div>
            </div>
            
            <!-- Second Brain -->
            <div class="showcase-card" onclick="window.open('/second-brain', '_blank')">
                <div class="card-header">
                    <i class="fas fa-brain card-icon"></i>
                    <div class="card-title">
                        <h3>Second Brain</h3>
                        <span class="card-status">● Ativo</span>
                    </div>
                </div>
                <div class="card-body">
                    <p style="margin-bottom: 20px; color: #cbd5e1;">
                        Chat inteligente com IA para explorar conceitos médicos complexos
                        e obter respostas instantâneas.
                    </p>
                    <div class="action-buttons">
                        <a href="/second-brain" class="btn">Iniciar Chat</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="links-section">
            <h2 style="text-align: center; margin-bottom: 20px;">
                <i class="fas fa-link"></i> Todos os Links do Sistema
            </h2>
            <div class="links-grid">
                <a href="/" class="link-item">
                    <i class="fas fa-home link-icon"></i>
                    <span>Home</span>
                </a>
                <a href="/flashcards" class="link-item">
                    <i class="fas fa-layer-group link-icon"></i>
                    <span>Flashcards</span>
                </a>
                <a href="/calendar" class="link-item">
                    <i class="fas fa-calendar-alt link-icon"></i>
                    <span>Calendário</span>
                </a>
                <a href="/progress" class="link-item">
                    <i class="fas fa-chart-line link-icon"></i>
                    <span>Dashboard de Progresso</span>
                </a>
                <a href="/second-brain" class="link-item">
                    <i class="fas fa-brain link-icon"></i>
                    <span>Second Brain</span>
                </a>
                <a href="/ai-tools" class="link-item">
                    <i class="fas fa-robot link-icon"></i>
                    <span>AI Tools</span>
                </a>
                <a href="/platform" class="link-item">
                    <i class="fas fa-th link-icon"></i>
                    <span>Plataforma Unificada</span>
                </a>
                <a href="/recursos" class="link-item">
                    <i class="fas fa-book link-icon"></i>
                    <span>Recursos</span>
                </a>
                <a href="/profile" class="link-item">
                    <i class="fas fa-user link-icon"></i>
                    <span>Perfil</span>
                </a>
                <a href="/settings" class="link-item">
                    <i class="fas fa-cog link-icon"></i>
                    <span>Configurações</span>
                </a>
                <a href="http://localhost:8001/docs" class="link-item">
                    <i class="fas fa-book-open link-icon"></i>
                    <span>API Documentation</span>
                </a>
                <a href="/app-overview.html" class="link-item">
                    <i class="fas fa-th-large link-icon"></i>
                    <span>App Overview</span>
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Atualizar estatísticas
        async function updateStats() {
            try {
                const response = await fetch('http://localhost:8001/api/flashcards/decks/');
                const decks = await response.json();
                
                let totalCards = 0;
                decks.forEach(deck => {
                    totalCards += deck.card_count || 0;
                });
                
                document.getElementById('totalDecks').textContent = decks.length;
                document.getElementById('totalCards').textContent = totalCards;
            } catch (error) {
                console.error('Error:', error);
            }
        }
        
        // Atualizar a cada 5 segundos
        updateStats();
        setInterval(updateStats, 5000);
        
        // Efeito de digitação
        const subtitle = document.querySelector('.subtitle');
        const text = subtitle.textContent;
        subtitle.textContent = '';
        let i = 0;
        
        function typeWriter() {
            if (i < text.length) {
                subtitle.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 50);
            }
        }
        
        typeWriter();
    </script>
</body>
</html>