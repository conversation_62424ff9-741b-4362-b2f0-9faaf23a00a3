<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuestAI Inteligente - Teste Avançado</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }
        
        .header .version {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.4em;
            font-weight: normal;
        }
        
        .content {
            padding: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }
        
        @media (max-width: 1024px) {
            .content {
                grid-template-columns: 1fr;
            }
        }
        
        .input-section, .output-section {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .section-header h2 {
            color: #333;
            font-size: 1.5em;
        }
        
        .section-header i {
            color: #667eea;
            font-size: 1.3em;
        }
        
        .input-area {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1em;
            resize: vertical;
            min-height: 200px;
            font-family: inherit;
        }
        
        .input-area:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .option-card {
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            font-size: 0.9em;
        }
        
        .option-card:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .option-card.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .generate-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
        }
        
        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .analysis-box {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .analysis-header {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .entity-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .entity-tag {
            background: #e8f0ff;
            color: #4a5568;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85em;
        }
        
        .entity-tag.medication {
            background: #ffeaa7;
            color: #d63031;
        }
        
        .entity-tag.condition {
            background: #dfe6e9;
            color: #2d3436;
        }
        
        .entity-tag.symptom {
            background: #a29bfe;
            color: white;
        }
        
        .entity-tag.procedure {
            background: #55efc4;
            color: #00b894;
        }
        
        .question-card {
            background: white;
            border: 1px solid #e0e0e0;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            transition: all 0.3s;
        }
        
        .question-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .question-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .question-number {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .question-type {
            background: #e0e0e0;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        
        .confidence-score {
            margin-left: auto;
            font-size: 0.85em;
            color: #666;
        }
        
        .confidence-score .score {
            font-weight: bold;
            color: #22c55e;
        }
        
        .question-text {
            font-size: 1.1em;
            margin-bottom: 15px;
            line-height: 1.6;
            color: #2d3436;
        }
        
        .options {
            margin-left: 20px;
        }
        
        .option {
            padding: 12px;
            margin-bottom: 10px;
            background: #f5f5f5;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.2s;
        }
        
        .option:hover {
            background: #e8f0ff;
        }
        
        .option.correct {
            background: #d4edda;
            border: 1px solid #28a745;
        }
        
        .option-letter {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .option.correct .option-letter {
            background: #28a745;
        }
        
        .explanation {
            margin-top: 15px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }
        
        .source-reference {
            margin-top: 10px;
            padding: 10px;
            background: #e8f0ff;
            border-radius: 5px;
            font-size: 0.85em;
            color: #4a5568;
            font-style: italic;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            text-align: center;
            padding: 15px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
            font-size: 0.9em;
        }
        
        .examples {
            margin-top: 20px;
            padding: 20px;
            background: #f0f4ff;
            border-radius: 10px;
        }
        
        .example-btn {
            background: white;
            border: 1px solid #667eea;
            color: #667eea;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.2s;
        }
        
        .example-btn:hover {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-brain"></i> 
                QuestAI Inteligente
                <span class="version">v2.0</span>
            </h1>
            <p>Geração Avançada de Questões com Análise Profunda de Conteúdo</p>
        </div>
        
        <div class="content">
            <!-- Input Section -->
            <div class="input-section">
                <div class="section-header">
                    <i class="fas fa-file-medical"></i>
                    <h2>Conteúdo Médico</h2>
                </div>
                
                <textarea id="contentInput" class="input-area" placeholder="Cole ou digite o conteúdo médico aqui...">A insuficiência cardíaca é uma síndrome clínica complexa resultante de qualquer alteração estrutural ou funcional do coração que prejudique sua capacidade de enchimento ou ejeção de sangue. Os principais sintomas incluem dispneia, fadiga e retenção de líquidos. O diagnóstico é baseado em critérios clínicos, exames laboratoriais como BNP/NT-proBNP, e ecocardiografia. O tratamento inclui medidas não farmacológicas e uso de medicamentos como IECA/BRA, betabloqueadores, antagonistas da aldosterona e diuréticos. A classificação funcional da NYHA divide os pacientes em quatro classes baseadas na limitação de atividade física. Complicações incluem arritmias, tromboembolismo e síndrome cardiorrenal.</textarea>
                
                <div class="examples">
                    <strong>Exemplos de conteúdo:</strong>
                    <button class="example-btn" onclick="loadExample('cardio')">Cardiologia</button>
                    <button class="example-btn" onclick="loadExample('neuro')">Neurologia</button>
                    <button class="example-btn" onclick="loadExample('pediatrics')">Pediatria</button>
                </div>
                
                <!-- Question Types -->
                <h3 style="margin: 20px 0 10px;">Tipos de Questão</h3>
                <div class="options-grid" id="questionTypes">
                    <div class="option-card selected" data-type="multiple">
                        <i class="fas fa-list-ul"></i> Múltipla Escolha
                    </div>
                    <div class="option-card selected" data-type="true-false">
                        <i class="fas fa-check-double"></i> Verdadeiro/Falso
                    </div>
                    <div class="option-card" data-type="clinical">
                        <i class="fas fa-user-md"></i> Caso Clínico
                    </div>
                    <div class="option-card" data-type="open">
                        <i class="fas fa-align-left"></i> Dissertativa
                    </div>
                </div>
                
                <!-- Difficulty -->
                <h3 style="margin: 20px 0 10px;">Dificuldade</h3>
                <div class="options-grid" id="difficulty">
                    <div class="option-card" data-level="basic">
                        <i class="fas fa-seedling"></i> Básico
                    </div>
                    <div class="option-card selected" data-level="intermediate">
                        <i class="fas fa-fire"></i> Intermediário
                    </div>
                    <div class="option-card" data-level="advanced">
                        <i class="fas fa-rocket"></i> Avançado
                    </div>
                </div>
                
                <!-- Generate Button -->
                <button id="generateBtn" class="generate-btn" style="margin-top: 20px;">
                    <i class="fas fa-magic"></i> Gerar Questões Inteligentes
                </button>
            </div>
            
            <!-- Output Section -->
            <div class="output-section">
                <div class="section-header">
                    <i class="fas fa-chart-line"></i>
                    <h2>Análise e Resultados</h2>
                </div>
                
                <!-- Loading -->
                <div id="loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Analisando conteúdo e gerando questões inteligentes...</p>
                </div>
                
                <!-- Content Analysis -->
                <div id="contentAnalysis" style="display: none;">
                    <div class="analysis-box">
                        <div class="analysis-header">
                            <i class="fas fa-microscope"></i>
                            Análise do Conteúdo
                        </div>
                        <div id="analysisContent"></div>
                    </div>
                </div>
                
                <!-- Stats -->
                <div id="stats" class="stats-grid" style="display: none;"></div>
                
                <!-- Questions -->
                <div id="questionsList"></div>
            </div>
        </div>
    </div>
    
    <script>
        // Selected options
        let selectedTypes = ['multiple', 'true-false'];
        let selectedDifficulty = 'intermediate';
        
        // Example contents
        const examples = {
            cardio: `A fibrilação atrial é a arritmia cardíaca sustentada mais comum, caracterizada por ativação atrial desorganizada com consequente deterioração da função mecânica atrial. Os principais fatores de risco incluem idade avançada, hipertensão arterial, insuficiência cardíaca, doença valvar e diabetes mellitus. O diagnóstico é confirmado por eletrocardiograma mostrando intervalos RR irregulares e ausência de ondas P distintas. O tratamento inclui controle de frequência com betabloqueadores ou bloqueadores de canal de cálcio, controle de ritmo com antiarrítmicos como amiodarona, e anticoagulação para prevenção de AVC usando warfarina ou NOACs. A ablação por cateter pode ser considerada em casos refratários.`,
            
            neuro: `O acidente vascular cerebral isquêmico resulta da interrupção do fluxo sanguíneo cerebral, geralmente por trombose ou embolia. Os sintomas incluem hemiparesia súbita, afasia, disartria, hemianopsia e alteração do nível de consciência. O diagnóstico requer neuroimagem urgente com TC ou RM. O tratamento na fase aguda inclui trombólise com alteplase dentro de 4,5 horas do início dos sintomas em pacientes elegíveis, e trombectomia mecânica até 24 horas em casos selecionados. A prevenção secundária envolve antiagregação plaquetária com AAS ou clopidogrel, controle de fatores de risco como hipertensão e dislipidemia, e investigação etiológica para identificar fonte embólica.`,
            
            pediatrics: `A bronquiolite viral aguda é a principal causa de hospitalização em lactentes, sendo o vírus sincicial respiratório (VSR) o agente mais comum. Manifesta-se com coriza, tosse, taquipneia, sibilos e esforço respiratório. O diagnóstico é clínico, não sendo necessários exames laboratoriais de rotina. O tratamento é principalmente de suporte, incluindo hidratação adequada, oxigenioterapia se saturação <92%, e aspiração nasal. Broncodilatadores e corticoides não são recomendados rotineiramente. A prevenção com palivizumabe está indicada para grupos de alto risco como prematuros extremos e cardiopatas. A maioria dos casos evolui com resolução completa em 7-10 dias.`
        };
        
        function loadExample(type) {
            document.getElementById('contentInput').value = examples[type];
        }
        
        // Event listeners for option cards
        document.querySelectorAll('#questionTypes .option-card').forEach(card => {
            card.addEventListener('click', function() {
                this.classList.toggle('selected');
                const type = this.dataset.type;
                
                if (this.classList.contains('selected')) {
                    if (!selectedTypes.includes(type)) {
                        selectedTypes.push(type);
                    }
                } else {
                    selectedTypes = selectedTypes.filter(t => t !== type);
                }
                
                // Ensure at least one type is selected
                if (selectedTypes.length === 0) {
                    this.classList.add('selected');
                    selectedTypes.push(type);
                }
            });
        });
        
        document.querySelectorAll('#difficulty .option-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('#difficulty .option-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedDifficulty = this.dataset.level;
            });
        });
        
        // Generate button
        document.getElementById('generateBtn').addEventListener('click', async function() {
            const content = document.getElementById('contentInput').value.trim();
            
            if (!content) {
                alert('Por favor, adicione conteúdo para gerar questões.');
                return;
            }
            
            // Clear previous results
            document.getElementById('contentAnalysis').style.display = 'none';
            document.getElementById('stats').style.display = 'none';
            document.getElementById('questionsList').innerHTML = '';
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            this.disabled = true;
            
            try {
                const response = await fetch('http://localhost:8001/api/quest-ai/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: content,
                        content_type: 'text',
                        question_types: selectedTypes,
                        difficulty: selectedDifficulty,
                        count: 5,
                        include_explanations: true,
                        include_citations: true
                    })
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || 'Erro ao gerar questões');
                }
                
                const data = await response.json();
                displayResults(data);
                
            } catch (error) {
                console.error('Erro:', error);
                alert('Erro ao gerar questões: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
                this.disabled = false;
            }
        });
        
        function displayResults(data) {
            // Display content analysis
            displayAnalysis(data.content_analysis);
            
            // Display stats
            displayStats(data.metadata);
            
            // Display questions
            displayQuestions(data.questions);
        }
        
        function displayAnalysis(analysis) {
            const analysisDiv = document.getElementById('analysisContent');
            
            let html = `
                <p><strong>Resumo:</strong> ${analysis.summary || 'Análise do conteúdo médico fornecido.'}</p>
                <p><strong>Complexidade:</strong> <span style="color: #667eea; font-weight: bold;">${analysis.complexity}</span></p>
                <p><strong>Tópicos identificados:</strong> ${analysis.key_topics.join(', ')}</p>
            `;
            
            // Display entities
            if (analysis.main_entities) {
                html += '<div class="entity-tags">';
                
                for (const [type, entities] of Object.entries(analysis.main_entities)) {
                    entities.forEach(entity => {
                        let className = 'entity-tag';
                        if (type === 'medications') className += ' medication';
                        else if (type === 'conditions') className += ' condition';
                        else if (type === 'symptoms') className += ' symptom';
                        else if (type === 'procedures') className += ' procedure';
                        
                        html += `<span class="${className}">${entity}</span>`;
                    });
                }
                
                html += '</div>';
            }
            
            analysisDiv.innerHTML = html;
            document.getElementById('contentAnalysis').style.display = 'block';
        }
        
        function displayStats(metadata) {
            const statsDiv = document.getElementById('stats');
            
            statsDiv.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${metadata.total_questions}</div>
                    <div class="stat-label">Questões</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${metadata.content_stats.word_count}</div>
                    <div class="stat-label">Palavras</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${metadata.content_stats.concepts_found}</div>
                    <div class="stat-label">Conceitos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${metadata.content_stats.entities_extracted}</div>
                    <div class="stat-label">Entidades</div>
                </div>
            `;
            
            statsDiv.style.display = 'grid';
        }
        
        function displayQuestions(questions) {
            const questionsDiv = document.getElementById('questionsList');
            
            questionsDiv.innerHTML = '<h3 style="margin: 20px 0;">Questões Geradas Inteligentemente</h3>';
            
            questions.forEach((question, index) => {
                const questionCard = document.createElement('div');
                questionCard.className = 'question-card';
                
                let optionsHtml = '';
                if (question.options) {
                    optionsHtml = '<div class="options">';
                    question.options.forEach((opt, i) => {
                        optionsHtml += `
                            <div class="option ${opt.correct ? 'correct' : ''}">
                                <span class="option-letter">${String.fromCharCode(65 + i)}</span>
                                <span>${opt.text}</span>
                                ${opt.correct ? '<i class="fas fa-check" style="color: #28a745; margin-left: auto;"></i>' : ''}
                            </div>
                        `;
                    });
                    optionsHtml += '</div>';
                }
                
                let answerHtml = '';
                if (question.type === 'true-false') {
                    answerHtml = `
                        <div style="margin: 15px 0;">
                            <strong>Resposta:</strong> 
                            <span style="color: ${question.answer ? '#28a745' : '#dc3545'}; font-weight: bold;">
                                ${question.answer ? 'Verdadeiro' : 'Falso'}
                            </span>
                        </div>
                    `;
                } else if (question.type === 'clinical' && question.answer) {
                    answerHtml = `
                        <div style="margin: 15px 0; padding: 15px; background: #e8f5e9; border-radius: 8px;">
                            <strong>Resposta:</strong><br>
                            ${question.answer}
                        </div>
                    `;
                } else if (question.type === 'open' && question.answer) {
                    answerHtml = `
                        <div style="margin: 15px 0; padding: 15px; background: #f3e5f5; border-radius: 8px;">
                            <strong>Resposta Modelo:</strong><br>
                            ${question.answer}
                        </div>
                    `;
                }
                
                questionCard.innerHTML = `
                    <div class="question-header">
                        <span class="question-number">#${index + 1}</span>
                        <span class="question-type">${getTypeName(question.type)}</span>
                        ${question.confidence_score ? `
                            <span class="confidence-score">
                                Confiança: <span class="score">${Math.round(question.confidence_score * 100)}%</span>
                            </span>
                        ` : ''}
                    </div>
                    <div class="question-text">${question.text}</div>
                    ${optionsHtml}
                    ${answerHtml}
                    ${question.explanation ? `
                        <div class="explanation">
                            <i class="fas fa-lightbulb"></i> <strong>Explicação:</strong> ${question.explanation}
                        </div>
                    ` : ''}
                    ${question.source_reference ? `
                        <div class="source-reference">
                            <i class="fas fa-quote-left"></i> ${question.source_reference}
                        </div>
                    ` : ''}
                `;
                
                questionsDiv.appendChild(questionCard);
            });
        }
        
        function getTypeName(type) {
            const types = {
                'multiple': 'Múltipla Escolha',
                'true-false': 'Verdadeiro/Falso',
                'clinical': 'Caso Clínico',
                'open': 'Dissertativa'
            };
            return types[type] || type;
        }
    </script>
</body>
</html>