<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thanos Ultra - Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .container {
            text-align: center;
            padding: 2rem;
        }
        .logo {
            font-size: 5rem;
            color: #6366f1;
            margin-bottom: 2rem;
        }
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .links {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        a {
            padding: 1rem 2rem;
            background: #6366f1;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            transition: background 0.2s;
        }
        a:hover {
            background: #4f46e5;
        }
        .status {
            margin-top: 3rem;
            padding: 1rem;
            background: #1e293b;
            border-radius: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">∞</div>
        <h1>Thanos Ultra</h1>
        <p>Advanced Document Processing & AI System</p>
        
        <div class="links">
            <a href="http://localhost:8082/#/thanos-ultra">Open Thanos Ultra</a>
            <a href="http://localhost:8001/docs">API Documentation</a>
            <a href="http://localhost:8001/api/thanos/health">Health Check</a>
        </div>
        
        <div class="status" id="status">
            Checking system status...
        </div>
    </div>
    
    <script>
        // Check API health
        fetch('http://localhost:8001/api/thanos/health')
            .then(res => res.json())
            .then(data => {
                document.getElementById('status').innerHTML = `
                    <h3>System Status</h3>
                    <p>✅ API: ${data.status}</p>
                    <p>✅ Version: ${data.version}</p>
                    <p>✅ Database: ${data.services.database}</p>
                    <p>✅ Redis: ${data.services.redis}</p>
                    <p>✅ Embeddings: ${data.services.embeddings}</p>
                `;
            })
            .catch(err => {
                document.getElementById('status').innerHTML = `
                    <h3>System Status</h3>
                    <p>❌ API is not responding</p>
                    <p>Please check if the backend is running</p>
                `;
            });
    </script>
</body>
</html>