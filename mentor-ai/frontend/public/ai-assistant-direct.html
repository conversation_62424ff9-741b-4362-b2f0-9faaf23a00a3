<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant Advanced - Direct Access</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #0f172a;
            color: #f1f5f9;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .container {
            text-align: center;
            padding: 2rem;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .loading {
            margin: 2rem 0;
        }
        
        .spinner {
            width: 60px;
            height: 60px;
            margin: 0 auto;
            border: 4px solid #334155;
            border-top: 4px solid #6366f1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .message {
            font-size: 1.25rem;
            margin: 1rem 0;
            color: #94a3b8;
        }
        
        .links {
            margin-top: 2rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }
        
        .link {
            display: inline-block;
            padding: 1rem 2rem;
            background: #6366f1;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s;
            min-width: 300px;
        }
        
        .link:hover {
            background: #8b5cf6;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
        }
        
        .secondary {
            background: #334155;
        }
        
        .secondary:hover {
            background: #475569;
        }
        
        .info {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid #6366f1;
            border-radius: 8px;
            max-width: 500px;
            margin: 2rem auto 0;
        }
        
        .info h3 {
            margin: 0 0 0.5rem 0;
            color: #6366f1;
        }
        
        .info p {
            margin: 0.5rem 0;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI Assistant Advanced</h1>
        
        <div class="loading">
            <div class="spinner"></div>
            <p class="message">Redirecting to AI Assistant Advanced...</p>
        </div>
        
        <div class="links">
            <a href="/#/ai-tools/ai-assistant-advanced" class="link">
                🧠 Open AI Assistant Advanced
            </a>
            <a href="/#/ai-tools/question-generator" class="link secondary">
                📝 Open Question Generator
            </a>
            <a href="/#/ai-tools" class="link secondary">
                🛠️ View All AI Tools
            </a>
        </div>
        
        <div class="info">
            <h3>ℹ️ Access Information</h3>
            <p><strong>Frontend:</strong> Running on port 8082 ✅</p>
            <p><strong>Backend:</strong> Running on port 8001 ✅</p>
            <p><strong>Direct URL:</strong> http://localhost:8082/#/ai-tools/ai-assistant-advanced</p>
            <p><strong>Note:</strong> Use the hash (#) in the URL for proper routing</p>
        </div>
    </div>
    
    <script>
        // Auto-redirect after 2 seconds
        setTimeout(() => {
            window.location.href = '/#/ai-tools/ai-assistant-advanced';
        }, 2000);
        
        // Check backend status
        fetch('http://localhost:8001/health')
            .then(response => response.json())
            .then(data => {
                console.log('Backend status:', data);
            })
            .catch(error => {
                console.error('Backend might not be running:', error);
            });
    </script>
</body>
</html>