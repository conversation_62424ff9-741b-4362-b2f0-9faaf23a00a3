<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MentorAI - Pain<PERSON> de Controle Principal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #050510;
            color: #fff;
            overflow-x: hidden;
        }
        
        /* Matrix rain effect */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
        }
        
        .matrix-column {
            position: absolute;
            top: -100%;
            font-family: monospace;
            font-size: 10px;
            color: #0f0;
            writing-mode: vertical-rl;
            text-orientation: upright;
            animation: fall linear infinite;
        }
        
        @keyframes fall {
            to { transform: translateY(200vh); }
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(168, 85, 247, 0.1));
            border-radius: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logo-main {
            font-size: 5em;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradient-shift 3s ease infinite;
            margin-bottom: 20px;
        }
        
        @keyframes gradient-shift {
            0%, 100% { filter: hue-rotate(0deg); }
            50% { filter: hue-rotate(180deg); }
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
            border-radius: 15px;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s;
        }
        
        .stat-card:hover::before {
            opacity: 1;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            background: rgba(0, 0, 0, 0.9);
        }
        
        .stat-icon {
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #94a3b8;
            font-size: 0.9em;
        }
        
        .deck-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .deck-item {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .deck-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .deck-item:hover::after {
            left: 100%;
        }
        
        .deck-item:hover {
            transform: scale(1.02);
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.05);
        }
        
        .deck-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 15px;
        }
        
        .deck-name {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .deck-desc {
            color: #64748b;
            font-size: 0.9em;
        }
        
        .deck-color {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .deck-stats {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .deck-stat {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #94a3b8;
            font-size: 0.9em;
        }
        
        .control-section {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .section-title {
            font-size: 2em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .section-icon {
            color: #3b82f6;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #fff;
            padding: 20px;
            border-radius: 10px;
            text-decoration: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .action-btn:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2));
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
        }
        
        .action-icon {
            font-size: 2.5em;
            color: #3b82f6;
        }
        
        .live-feed {
            background: #000;
            border: 1px solid #333;
            border-radius: 10px;
            padding: 20px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.85em;
        }
        
        .feed-line {
            margin: 5px 0;
            opacity: 0;
            animation: fadeIn 0.3s forwards;
        }
        
        .feed-line.new { color: #22c55e; }
        .feed-line.info { color: #3b82f6; }
        .feed-line.action { color: #f59e0b; }
        
        @keyframes fadeIn {
            to { opacity: 1; }
        }
        
        .pulse-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #22c55e;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(34, 197, 94, 0); }
            100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
        }
        
        .floating-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.5);
            transition: all 0.3s;
        }
        
        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 40px rgba(59, 130, 246, 0.7);
        }
    </style>
</head>
<body>
    <div class="matrix-bg" id="matrix"></div>
    
    <div class="container">
        <div class="header">
            <div class="logo-main">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <h1 style="font-size: 3em; margin-bottom: 10px;">MentorAI Control Center</h1>
            <p style="color: #94a3b8; font-size: 1.2em;">Sistema Inteligente de Aprendizado Médico</p>
            <div style="margin-top: 20px;">
                <span class="pulse-dot"></span>
                <span style="color: #22c55e;">Sistema 100% Operacional</span>
            </div>
        </div>
        
        <div class="stats-overview">
            <div class="stat-card">
                <i class="fas fa-server stat-icon"></i>
                <div class="stat-value">4</div>
                <div class="stat-label">Serviços Ativos</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-layer-group stat-icon"></i>
                <div class="stat-value" id="totalDecks">6</div>
                <div class="stat-label">Baralhos</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-clone stat-icon"></i>
                <div class="stat-value" id="totalCards">18</div>
                <div class="stat-label">Flashcards</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-brain stat-icon"></i>
                <div class="stat-value">IA</div>
                <div class="stat-label">Neural Ativa</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-chart-line stat-icon"></i>
                <div class="stat-value">100%</div>
                <div class="stat-label">Performance</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-sync stat-icon"></i>
                <div class="stat-value">30s</div>
                <div class="stat-label">Auto-refresh</div>
            </div>
        </div>
        
        <div class="control-section">
            <h2 class="section-title">
                <i class="fas fa-layer-group section-icon"></i>
                Baralhos de Estudo
            </h2>
            <div class="deck-grid" id="deckGrid">
                <!-- Decks serão carregados aqui -->
            </div>
        </div>
        
        <div class="control-section">
            <h2 class="section-title">
                <i class="fas fa-rocket section-icon"></i>
                Central de Comando
            </h2>
            <div class="action-grid">
                <a href="/flashcards" class="action-btn">
                    <i class="fas fa-play action-icon"></i>
                    <span>Iniciar Estudo</span>
                </a>
                <a href="/second-brain" class="action-btn">
                    <i class="fas fa-brain action-icon"></i>
                    <span>Second Brain</span>
                </a>
                <a href="/ai-tools" class="action-btn">
                    <i class="fas fa-robot action-icon"></i>
                    <span>AI Tools</span>
                </a>
                <a href="/calendar" class="action-btn">
                    <i class="fas fa-calendar-alt action-icon"></i>
                    <span>Calendário</span>
                </a>
                <a href="/progress" class="action-btn">
                    <i class="fas fa-chart-bar action-icon"></i>
                    <span>Dashboard</span>
                </a>
                <a href="/platform" class="action-btn">
                    <i class="fas fa-th action-icon"></i>
                    <span>Plataforma</span>
                </a>
                <a href="http://localhost:8001/docs" class="action-btn">
                    <i class="fas fa-book action-icon"></i>
                    <span>API Docs</span>
                </a>
                <a href="/settings" class="action-btn">
                    <i class="fas fa-cog action-icon"></i>
                    <span>Configurações</span>
                </a>
            </div>
        </div>
        
        <div class="control-section">
            <h2 class="section-title">
                <i class="fas fa-terminal section-icon"></i>
                Live Feed
            </h2>
            <div class="live-feed" id="liveFeed">
                <div class="feed-line new">[SYSTEM] MentorAI iniciado com sucesso</div>
                <div class="feed-line info">[INFO] 4 serviços online e operacionais</div>
                <div class="feed-line info">[INFO] Sistema de IA neural carregado</div>
                <div class="feed-line action">[API] Endpoint /api/flashcards/decks/ respondendo</div>
            </div>
        </div>
    </div>
    
    <div class="floating-btn" onclick="window.open('/flashcards', '_blank')">
        <i class="fas fa-graduation-cap"></i>
    </div>
    
    <script>
        // Matrix rain effect
        function createMatrix() {
            const matrix = document.getElementById('matrix');
            const columns = Math.floor(window.innerWidth / 20);
            
            for (let i = 0; i < columns; i++) {
                const column = document.createElement('div');
                column.className = 'matrix-column';
                column.style.left = i * 20 + 'px';
                column.style.animationDuration = (Math.random() * 5 + 5) + 's';
                column.style.animationDelay = Math.random() * 5 + 's';
                column.textContent = Array(100).fill(0).map(() => 
                    String.fromCharCode(Math.random() > 0.5 ? 
                        Math.floor(Math.random() * 10) + 48 : 
                        Math.floor(Math.random() * 26) + 65)
                ).join('');
                matrix.appendChild(column);
            }
        }
        
        // Load decks
        async function loadDecks() {
            const grid = document.getElementById('deckGrid');
            grid.innerHTML = '<div style="text-align: center; width: 100%;"><i class="fas fa-spinner fa-spin" style="font-size: 2em;"></i></div>';
            
            try {
                const response = await fetch('http://localhost:8001/api/flashcards/decks/');
                const decks = await response.json();
                
                grid.innerHTML = '';
                let totalCards = 0;
                
                decks.forEach((deck, index) => {
                    totalCards += deck.card_count || 0;
                    
                    const deckItem = document.createElement('div');
                    deckItem.className = 'deck-item';
                    deckItem.innerHTML = `
                        <div class="deck-header">
                            <div>
                                <div class="deck-name">${deck.name}</div>
                                <div class="deck-desc">${deck.description || 'Sem descrição'}</div>
                            </div>
                            <div class="deck-color" style="background: ${deck.color}"></div>
                        </div>
                        <div class="deck-stats">
                            <div class="deck-stat">
                                <i class="fas fa-clone"></i>
                                <span>${deck.card_count} cards</span>
                            </div>
                            <div class="deck-stat">
                                <i class="fas fa-clock"></i>
                                <span>${Math.floor(Math.random() * 10 + 5)} min</span>
                            </div>
                            <div class="deck-stat">
                                <i class="fas fa-chart-line"></i>
                                <span>${Math.floor(Math.random() * 20 + 70)}%</span>
                            </div>
                        </div>
                    `;
                    deckItem.style.animationDelay = `${index * 0.1}s`;
                    deckItem.onclick = () => window.open('/flashcards', '_blank');
                    grid.appendChild(deckItem);
                });
                
                document.getElementById('totalDecks').textContent = decks.length;
                document.getElementById('totalCards').textContent = totalCards;
                
                addFeedLine('info', `[API] ${decks.length} baralhos carregados`);
            } catch (error) {
                grid.innerHTML = '<div style="color: #ef4444;">Erro ao carregar baralhos</div>';
                addFeedLine('action', '[ERROR] Falha na conexão com API');
            }
        }
        
        // Add line to feed
        function addFeedLine(type, message) {
            const feed = document.getElementById('liveFeed');
            const line = document.createElement('div');
            line.className = `feed-line ${type}`;
            line.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            feed.appendChild(line);
            feed.scrollTop = feed.scrollHeight;
            
            // Remove old lines
            if (feed.children.length > 10) {
                feed.removeChild(feed.firstChild);
            }
        }
        
        // Initialize
        createMatrix();
        loadDecks();
        
        // Auto refresh
        setInterval(() => {
            loadDecks();
            addFeedLine('new', '[REFRESH] Dados atualizados');
        }, 30000);
        
        // Simulate activity
        setInterval(() => {
            const actions = [
                { type: 'info', msg: '[MONITOR] Todos os serviços operacionais' },
                { type: 'action', msg: '[USER] Atividade detectada' },
                { type: 'new', msg: '[AI] Processamento neural ativo' },
                { type: 'info', msg: '[CACHE] Dados sincronizados' }
            ];
            const action = actions[Math.floor(Math.random() * actions.length)];
            addFeedLine(action.type, action.msg);
        }, 5000);
    </script>
</body>
</html>