# ===== SOPHOS ACADEMY - CONFIGURAÇÕES APACHE =====
# Versão: 2.0
# Funcionalidades: Performance, Segurança, SEO, Compressão

# ===== CONFIGURAÇÕES DE PERFORMANCE =====

# Habilitar compressão GZIP
<IfModule mod_deflate.c>
    # Comprimir HTML, CSS, JavaScript, Text, XML e fontes
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Cache de arquivos estáticos
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # CSS
    ExpiresByType text/css "access plus 1 year"
    
    # JavaScript
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    
    # Imagens
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Fontes
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # Outros
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"
</IfModule>

# Headers de cache
<IfModule mod_headers.c>
    # Cache para arquivos estáticos
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|webp|ico|woff|woff2|ttf|otf)$">
        Header set Cache-Control "max-age=31536000, public, immutable"
    </FilesMatch>
    
    # Cache para HTML
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "max-age=3600, public"
    </FilesMatch>
    
    # Headers de segurança
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https:; frame-src 'self' https://www.youtube.com https://www.google.com;"
    
    # Remove server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ===== CONFIGURAÇÕES DE SEO =====

# URLs amigáveis e redirecionamentos
RewriteEngine On

# Redirecionar para HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Redirecionar para www (opcional - ajustar conforme necessário)
# RewriteCond %{HTTP_HOST} !^www\. [NC]
# RewriteRule ^(.*)$ https://www.%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remover trailing slash
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{THE_REQUEST} /+[^\s]*\s [NC]
RewriteRule ^(.*)/$  /$1 [R=301,L]

# Página padrão
DirectoryIndex index.html index.htm

# ===== CONFIGURAÇÕES DE SEGURANÇA =====

# Bloquear acesso a arquivos sensíveis
<FilesMatch "\.(log|txt|md|bak|backup|old|tmp|env|git|htaccess|htpasswd)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Bloquear acesso a diretórios específicos
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteRule ^(config|cache|temp|backup|logs)/ - [F,L]
</IfModule>

# Bloquear user agents maliciosos
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTP_USER_AGENT} (libwww-perl|wget|python|nikto|curl|scan|java|winhttp|clshttp|loader) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|"|\)|\(|%0A|%0D|%22|%27|%28|%3C|%3E|%00).*(libwww-perl|wget|python|nikto|curl|scan|java|winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Proteção contra hotlinking de imagens
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?sophosacademy\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|svg|webp)$ - [F,L]
</IfModule>

# Limitar tamanho de upload
LimitRequestBody 10485760

# ===== PÁGINAS DE ERRO PERSONALIZADAS =====
ErrorDocument 400 /error.html
ErrorDocument 401 /error.html
ErrorDocument 403 /error.html
ErrorDocument 404 /error.html
ErrorDocument 500 /error.html

# ===== TIPOS MIME =====
<IfModule mod_mime.c>
    # Web fonts
    AddType application/font-woff2 .woff2
    AddType application/font-woff .woff
    AddType application/vnd.ms-fontobject .eot
    AddType font/ttf .ttf
    AddType font/otf .otf
    
    # Outros tipos
    AddType image/webp .webp
    AddType text/css .css
    AddType application/javascript .js
</IfModule>

# ===== OTIMIZAÇÕES ESPECÍFICAS =====

# Desabilitar ETags
<IfModule mod_headers.c>
    Header unset ETag
</IfModule>
FileETag None

# Otimizar KeepAlive
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule>

# ===== REDIRECIONAMENTOS ESPECÍFICOS =====

# Redirecionar páginas antigas (se houver)
# Redirect 301 /old-page.html /new-page.html

# ===== CONFIGURAÇÕES PARA DESENVOLVIMENTO =====
<IfModule mod_env.c>
    SetEnv ENVIRONMENT production
</IfModule>

# ===== FIM DAS CONFIGURAÇÕES ===== 