/* Sophos Academy - CSS Customizado Otimizado */

/* ===== VARIÁVEIS CSS ===== */
:root {
  --primary-color: #42b983;
  --primary-dark: #2D9C6A;
  --secondary-color: #4e63d7;
  --accent-color: #f5365c;
  --text-dark: #333;
  --text-light: #666;
  --bg-light: #f8f9fa;
  --shadow-light: 0 5px 15px rgba(0,0,0,0.1);
  --shadow-medium: 0 10px 30px rgba(0,0,0,0.15);
  --border-radius: 12px;
  --transition: all 0.3s ease;
}

/* ===== MELHORIAS DE PERFORMANCE ===== */
* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
  loading: lazy;
}

/* ===== HEADER MELHORADO ===== */
.header .logo h1 a {
  color: var(--primary-color);
  font-size: 1.8rem;
  font-weight: 700;
  text-decoration: none;
  transition: var(--transition);
}

.header .logo h1 a:hover {
  color: var(--primary-dark);
}

.header .btn-getstarted {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 25px;
  padding: 12px 25px;
  color: #fff;
  font-weight: 600;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  border: none;
}

.header .btn-getstarted:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* ===== HERO SECTION OTIMIZADA ===== */
#hero {
  background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.4)), url('assets/img/vr3.jpg') center/cover no-repeat;
  color: #fff;
  padding: 120px 0 80px;
  position: relative;
  overflow: hidden;
}

#hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(66,185,131,0.1), rgba(78,99,215,0.1));
  pointer-events: none;
}

#hero h2 {
  font-size: clamp(2rem, 5vw, 3.5rem);
  margin-bottom: 20px;
  font-weight: 800;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

#hero .btn-getstarted {
  margin-top: 30px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 30px;
  padding: 15px 35px;
  color: #fff;
  font-weight: 600;
  font-size: 1.1rem;
  box-shadow: 0 8px 25px rgba(66,185,131,0.3);
  transition: var(--transition);
}

#hero .btn-getstarted:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(66,185,131,0.4);
}

/* ===== ICON BOXES MELHORADOS ===== */
.icon-box {
  text-align: center;
  padding: 30px 20px;
  background: rgba(255,255,255,0.95);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.icon-box:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-medium);
}

.icon-box i {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
  transition: var(--transition);
}

.icon-box:hover i {
  color: var(--primary-dark);
  transform: scale(1.1);
}

/* ===== TEMPLATE FEATURES CAROUSEL OTIMIZADO ===== */
#template-features {
  padding: 60px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

#template-features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(66,185,131,0.1)"/></svg>') repeat;
  pointer-events: none;
}

.feature-swiper {
  height: 400px;
  padding: 20px 0 60px;
  overflow: visible;
}

.feature-card {
  background: #ffffff;
  border-radius: 20px;
  padding: 30px 25px;
  height: 320px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.08);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  text-align: center;
  border: 1px solid rgba(66,185,131,0.1);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
  transition: left 0.5s ease;
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 20px 40px rgba(66,185,131,0.2);
  border-color: rgba(66,185,131,0.3);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 20px;
  color: var(--primary-color);
  font-size: 32px;
  transition: var(--transition);
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  box-shadow: 0 8px 25px rgba(66,185,131,0.3);
}

.feature-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-dark);
  transition: var(--transition);
}

.feature-card:hover h3 {
  color: var(--primary-color);
}

.feature-card p {
  font-size: 0.9rem;
  line-height: 1.6;
  color: var(--text-light);
  margin-bottom: 20px;
}

/* ===== SWIPER CUSTOMIZADO ===== */
.swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background: #ddd;
  opacity: 0.5;
  border-radius: 50%;
  transition: var(--transition);
}

.swiper-pagination-bullet-active {
  background: var(--primary-color);
  opacity: 1;
  transform: scale(1.2);
}

.swiper-button-next,
.swiper-button-prev {
  width: 45px;
  height: 45px;
  background: rgba(66,185,131,0.9);
  border-radius: 50%;
  color: white;
  transition: var(--transition);
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
  background: var(--primary-color);
  transform: scale(1.1);
}

/* ===== SERVICE CARDS MELHORADOS ===== */
.service-card {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.08);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(66,185,131,0.05), rgba(78,99,215,0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover::before {
  opacity: 1;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.service-card .icon {
  width: 90px;
  height: 90px;
  border-radius: 20px;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  position: relative;
  z-index: 2;
}

.service-card:hover .icon {
  transform: scale(1.1) rotate(-5deg);
}

.service-card .icon i {
  font-size: 40px;
  color: white;
}

.service-card h3 {
  font-size: 1.4rem;
  margin-bottom: 15px;
  font-weight: 700;
  color: var(--text-dark);
  position: relative;
  z-index: 2;
}

.service-card p {
  color: var(--text-light);
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 25px;
  position: relative;
  z-index: 2;
}

.service-card .btn-details {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  border-radius: 30px;
  padding: 10px 25px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: var(--transition);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.service-card .btn-details:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(66,185,131,0.3);
}

/* ===== CORES DOS TEMPLATES ===== */
.template-vestibular .icon {
  background: linear-gradient(135deg, #f5365c, #f56036);
}

.template-residencia .icon {
  background: linear-gradient(135deg, #5e72e4, #825ee4);
}

.template-criar .icon {
  background: linear-gradient(135deg, #2dce89, #26a69a);
}

.template-rplus .icon {
  background: linear-gradient(135deg, #11cdef, #1171ef);
}

.template-faculdade .icon {
  background: linear-gradient(135deg, #fb6340, #fbb140);
}

.template-notion .icon {
  background: linear-gradient(135deg, #4e63d7, #7f53ac);
}

.template-hub .icon {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

/* ===== CALL TO ACTION MELHORADO ===== */
.call-to-action {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  padding: 80px 0;
  color: #fff;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.call-to-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  pointer-events: none;
}

.call-to-action h3 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.call-to-action p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.cta-btn {
  background: white;
  color: var(--primary-color);
  padding: 15px 35px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  display: inline-block;
  transition: var(--transition);
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
  margin: 0 10px;
}

.cta-btn:hover {
  background: #f8f9fa;
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0,0,0,0.3);
}

.cta-btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.cta-btn-secondary:hover {
  background: white;
  color: var(--primary-color);
}

/* ===== FORMULÁRIO DE CONTATO MELHORADO ===== */
.contact form {
  background: #fff;
  padding: 40px;
  border-radius: 20px;
  box-shadow: var(--shadow-medium);
}

.contact .form-control {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 15px;
  font-size: 1rem;
  transition: var(--transition);
}

.contact .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(66,185,131,0.25);
}

.contact button[type="submit"] {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
  border-radius: 30px;
  padding: 15px 40px;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  transition: var(--transition);
  box-shadow: 0 8px 25px rgba(66,185,131,0.3);
}

.contact button[type="submit"]:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(66,185,131,0.4);
}

/* ===== CHATBOT MELHORADO ===== */
.ai-chatbot-container {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 350px;
  max-height: 500px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
  z-index: 1000;
  display: none;
  overflow: hidden;
}

.ai-chatbot-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-chatbot-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.close-chatbot {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: var(--transition);
}

.close-chatbot:hover {
  background: rgba(255,255,255,0.2);
}

.open-chatbot {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 1000;
  transition: var(--transition);
  box-shadow: 0 8px 25px rgba(66,185,131,0.3);
}

.open-chatbot:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 30px rgba(66,185,131,0.4);
}

/* ===== ANIMAÇÕES OTIMIZADAS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* ===== RESPONSIVIDADE MELHORADA ===== */
@media (max-width: 768px) {
  :root {
    font-size: 14px;
  }
  
  #hero {
    padding: 80px 0 60px;
  }
  
  #hero h2 {
    font-size: 2rem;
  }
  
  .feature-swiper {
    height: 350px;
  }
  
  .feature-card {
    height: 280px;
    padding: 25px 20px;
  }
  
  .service-card {
    padding: 30px 25px;
    margin-bottom: 30px;
  }
  
  .ai-chatbot-container {
    width: 300px;
    right: 10px;
    bottom: 80px;
  }
  
  .open-chatbot {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .feature-card {
    height: 250px;
    padding: 20px 15px;
  }
  
  .service-card {
    padding: 25px 20px;
  }
  
  .ai-chatbot-container {
    width: 280px;
    max-height: 400px;
  }
}

/* ===== MODO ESCURO ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --text-dark: #f8f9fa;
    --text-light: #adb5bd;
    --bg-light: #212529;
  }
  
  .feature-card {
    background: #343a40;
    color: var(--text-dark);
  }
  
  .service-card {
    background: linear-gradient(145deg, #343a40, #495057);
    color: var(--text-dark);
  }
  
  .ai-chatbot-container {
    background: #343a40;
    color: var(--text-dark);
  }
}

/* ===== OTIMIZAÇÕES DE PERFORMANCE ===== */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-load.loaded {
  opacity: 1;
}

/* Reduzir motion para usuários com preferência */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== MELHORIAS DE ACESSIBILIDADE ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles melhorados */
button:focus,
a:focus,
input:focus,
textarea:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== UTILITÁRIOS ===== */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.border-primary { border-color: var(--primary-color) !important; }

.shadow-sm { box-shadow: var(--shadow-light) !important; }
.shadow-md { box-shadow: var(--shadow-medium) !important; }

.rounded-lg { border-radius: var(--border-radius) !important; }
.rounded-xl { border-radius: 20px !important; } 