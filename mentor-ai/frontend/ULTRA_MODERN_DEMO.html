<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra Modern Calendar & Revision System - Demo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0f1b;
            color: #f1f5f9;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .demo-container {
            min-height: 100vh;
            position: relative;
        }

        /* Background Effects */
        .background-effects {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            overflow: hidden;
        }

        .gradient-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(80px);
            opacity: 0.3;
            animation: float 20s ease-in-out infinite;
        }

        .gradient-orb.orb-1 {
            width: 400px;
            height: 400px;
            background: radial-gradient(circle, #6366f1, #8b5cf6);
            top: -200px;
            left: -200px;
            animation-delay: 0s;
        }

        .gradient-orb.orb-2 {
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, #f59e0b, #fbbf24);
            top: 50%;
            right: -150px;
            animation-delay: -10s;
        }

        .gradient-orb.orb-3 {
            width: 500px;
            height: 500px;
            background: radial-gradient(circle, #22c55e, #06b6d4);
            bottom: -250px;
            left: 30%;
            animation-delay: -5s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(20px) rotate(240deg); }
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(99, 102, 241, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(99, 102, 241, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 60s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Header */
        .demo-header {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
            padding: 2rem;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .demo-title {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #f1f5f9, #6366f1);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }

        .demo-subtitle {
            font-size: 1.2rem;
            color: #94a3b8;
            margin-bottom: 2rem;
        }

        /* Feature Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            padding: 3rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .feature-card {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #f1f5f9;
            margin-bottom: 1rem;
        }

        .feature-description {
            color: #94a3b8;
            margin-bottom: 1.5rem;
            line-height: 1.7;
        }

        .feature-list {
            list-style: none;
            margin-bottom: 2rem;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
            color: #e2e8f0;
        }

        .feature-list li::before {
            content: '✨';
            font-size: 1rem;
        }

        /* Demo Buttons */
        .demo-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
            padding: 3rem 2rem;
        }

        .demo-btn {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .demo-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .demo-btn:hover::before {
            left: 100%;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .demo-btn.secondary {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(148, 163, 184, 0.2);
            color: #e2e8f0;
        }

        .demo-btn.secondary:hover {
            background: rgba(99, 102, 241, 0.1);
            border-color: #6366f1;
        }

        /* Stats Preview */
        .stats-preview {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(30, 41, 59, 0.5);
            border-radius: 12px;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: #6366f1;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #94a3b8;
            font-weight: 500;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .demo-title {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                padding: 2rem 1rem;
            }
            
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .demo-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Background Effects -->
        <div class="background-effects">
            <div class="gradient-orb orb-1"></div>
            <div class="gradient-orb orb-2"></div>
            <div class="gradient-orb orb-3"></div>
            <div class="grid-overlay"></div>
        </div>

        <!-- Header -->
        <header class="demo-header">
            <h1 class="demo-title">Ultra Modern Integration</h1>
            <p class="demo-subtitle">Calendário & Sistema de Revisões Perfeitamente Integrados</p>
            
            <!-- Stats Preview -->
            <div class="stats-preview">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">4</div>
                        <div class="stat-label">Componentes Criados</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">2</div>
                        <div class="stat-label">Páginas Modernizadas</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">100%</div>
                        <div class="stat-label">Integração Completa</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">∞</div>
                        <div class="stat-label">Possibilidades</div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Features Grid -->
        <div class="features-grid">
            <!-- UltraNavigation -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-compass"></i>
                </div>
                <h3 class="feature-title">UltraNavigation</h3>
                <p class="feature-description">
                    Sistema de navegação unificado que conecta perfeitamente as páginas de calendário e revisões.
                </p>
                <ul class="feature-list">
                    <li>Pills de navegação com indicadores ativos</li>
                    <li>Botões de ação rápida integrados</li>
                    <li>Dropdown com opções avançadas</li>
                    <li>Design responsivo e touch-friendly</li>
                </ul>
            </div>

            <!-- UltraStatsPanel -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="feature-title">UltraStatsPanel</h3>
                <p class="feature-description">
                    Painel de estatísticas avançado com métricas em tempo real e visualizações interativas.
                </p>
                <ul class="feature-list">
                    <li>Progress ring animado para progresso diário</li>
                    <li>Indicadores de sequência semanal</li>
                    <li>Gráfico de performance com tendências</li>
                    <li>Countdown timer para próxima revisão</li>
                </ul>
            </div>

            <!-- UltraPageTransition -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <h3 class="feature-title">UltraPageTransition</h3>
                <p class="feature-description">
                    Transições fluidas e contextuais entre páginas com overlays de loading elegantes.
                </p>
                <ul class="feature-list">
                    <li>Animações ultra-slide, fade e zoom</li>
                    <li>Overlays de loading contextuais</li>
                    <li>Progress bars com shimmer effects</li>
                    <li>Ícones e textos dinâmicos</li>
                </ul>
            </div>

            <!-- UltraDataSync -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-sync-alt"></i>
                </div>
                <h3 class="feature-title">UltraDataSync</h3>
                <p class="feature-description">
                    Sincronização inteligente de dados com detecção e resolução automática de conflitos.
                </p>
                <ul class="feature-list">
                    <li>Auto-sync em intervalos configuráveis</li>
                    <li>Detecção automática de conflitos</li>
                    <li>Interface para resolução manual</li>
                    <li>Status visual em tempo real</li>
                </ul>
            </div>

            <!-- Design System -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <h3 class="feature-title">Ultra Design System</h3>
                <p class="feature-description">
                    Sistema de design moderno com glassmorphism, gradientes e animações avançadas.
                </p>
                <ul class="feature-list">
                    <li>Paleta de cores ultra moderna</li>
                    <li>Efeitos glassmorphism avançados</li>
                    <li>Orbs gradientes flutuantes</li>
                    <li>Grid overlay animado</li>
                </ul>
            </div>

            <!-- Integration -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-puzzle-piece"></i>
                </div>
                <h3 class="feature-title">Integração Perfeita</h3>
                <p class="feature-description">
                    Conexão bidirecional entre calendário e revisões com dados sincronizados.
                </p>
                <ul class="feature-list">
                    <li>Navegação fluida entre páginas</li>
                    <li>Dados compartilhados e sincronizados</li>
                    <li>Ações rápidas integradas</li>
                    <li>Experiência unificada</li>
                </ul>
            </div>
        </div>

        <!-- Demo Buttons -->
        <div class="demo-buttons">
            <a href="/calendar" class="demo-btn">
                <i class="fas fa-calendar-alt"></i>
                <span>Ver Calendário</span>
            </a>
            <a href="/revision-scheduler" class="demo-btn">
                <i class="fas fa-brain"></i>
                <span>Sistema de Revisões</span>
            </a>
            <a href="ULTRA_MODERN_INTEGRATION_SUMMARY.md" class="demo-btn secondary">
                <i class="fas fa-file-alt"></i>
                <span>Documentação Completa</span>
            </a>
            <button onclick="window.location.reload()" class="demo-btn secondary">
                <i class="fas fa-redo"></i>
                <span>Recarregar Demo</span>
            </button>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate feature cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Initially hide cards
            document.querySelectorAll('.feature-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });

            // Add click effects to demo buttons
            document.querySelectorAll('.demo-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s ease-out;
                        pointer-events: none;
                    `;
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Add CSS for ripple animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
