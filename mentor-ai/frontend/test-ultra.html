<!DOCTYPE html>
<html>
<head>
    <title>Test Ultra Question Generator</title>
</head>
<body>
    <h1>Testing Ultra Question Generator</h1>
    <p>Access the application at:</p>
    <ul>
        <li><a href="http://localhost:8083/#/ai-tools/question-generator" target="_blank">Direct Link to Question Generator</a></li>
        <li><a href="http://localhost:8083/#/ai-tools" target="_blank">AI Tools Page</a></li>
        <li><a href="http://localhost:8083/" target="_blank">Home Page</a></li>
    </ul>
    
    <h2>Features Implemented:</h2>
    <ul>
        <li>✅ Multiple AI Model Selection (GPT-4, Claude 3, Gemini, MedicalLLM)</li>
        <li>✅ Drag & Drop File Upload</li>
        <li>✅ Medical Specialty Selection</li>
        <li>✅ Multiple Question Types</li>
        <li>✅ Real-time Generation with Streaming</li>
        <li>✅ Export Functionality</li>
        <li>✅ Modern UI with Animations</li>
    </ul>
    
    <h2>API Endpoints:</h2>
    <ul>
        <li>GET http://localhost:8001/api/ultra-questions/ai-models</li>
        <li>GET http://localhost:8001/api/ultra-questions/specialties</li>
        <li>GET http://localhost:8001/api/ultra-questions/question-types</li>
        <li>POST http://localhost:8001/api/ultra-questions/upload-and-analyze</li>
        <li>POST http://localhost:8001/api/ultra-questions/generate-questions</li>
    </ul>
    
    <script>
        // Test API endpoint
        fetch('http://localhost:8001/api/ultra-questions/ai-models')
            .then(res => res.json())
            .then(data => {
                console.log('AI Models:', data);
                document.body.innerHTML += '<h3>API Working! Models: ' + data.models.map(m => m.name).join(', ') + '</h3>';
            })
            .catch(err => {
                console.error('API Error:', err);
                document.body.innerHTML += '<h3 style="color: red;">API Error - Check if backend is running</h3>';
            });
    </script>
</body>
</html>