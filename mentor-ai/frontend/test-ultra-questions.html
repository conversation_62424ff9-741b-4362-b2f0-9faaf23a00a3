<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra Question Generator - Test Interface</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 30px;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            border-color: rgba(99, 102, 241, 0.3);
            box-shadow: 0 20px 40px rgba(99, 102, 241, 0.1);
        }
        
        .card h2 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #e2e8f0;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }
        
        .file-upload input[type=file] {
            position: absolute;
            left: -9999px;
        }
        
        .file-upload-label {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.05);
            border: 2px dashed rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .file-upload-label:hover {
            border-color: #6366f1;
            background: rgba(99, 102, 241, 0.05);
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.6;
        }
        
        .files-list {
            margin-top: 20px;
        }
        
        .file-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .remove-file {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #6366f1;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #94a3b8;
            margin-top: 4px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .loading.active {
            display: block;
        }
        
        .spinner {
            width: 48px;
            height: 48px;
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-top-color: #6366f1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .results {
            display: none;
        }
        
        .results.active {
            display: block;
        }
        
        .question-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 20px;
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .question-number {
            font-size: 1.125rem;
            font-weight: 600;
            color: #6366f1;
        }
        
        .difficulty-badge {
            padding: 4px 12px;
            background: rgba(99, 102, 241, 0.2);
            color: #6366f1;
            border-radius: 20px;
            font-size: 0.875rem;
        }
        
        .question-text {
            font-size: 1.125rem;
            line-height: 1.7;
            margin-bottom: 20px;
        }
        
        .options {
            list-style: none;
        }
        
        .option {
            background: rgba(255, 255, 255, 0.05);
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }
        
        .option:hover {
            background: rgba(255, 255, 255, 0.08);
        }
        
        .option.correct {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        
        .explanation {
            background: rgba(251, 191, 36, 0.1);
            border-left: 4px solid #fbbf24;
            padding: 16px;
            margin-top: 20px;
            border-radius: 8px;
        }
        
        .quality-score {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 20px;
            padding: 16px;
            background: rgba(99, 102, 241, 0.1);
            border-radius: 8px;
        }
        
        .score-label {
            font-weight: 600;
        }
        
        .score-bar {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #ef4444 0%, #fbbf24 50%, #10b981 100%);
            transition: width 0.5s ease;
        }
        
        .score-value {
            font-weight: 700;
            color: #6366f1;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Ultra Question Generator</h1>
            <p class="subtitle">Sistema avançado de geração de questões médicas com IA de última geração</p>
        </header>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="totalQuestions">0</div>
                <div class="stat-label">Questões Geradas</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgQuality">0%</div>
                <div class="stat-label">Qualidade Média</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="cacheHits">0</div>
                <div class="stat-label">Cache Hits</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="responseTime">0ms</div>
                <div class="stat-label">Tempo de Resposta</div>
            </div>
        </div>
        
        <div class="grid">
            <div class="card">
                <h2>
                    <div class="icon">📁</div>
                    Upload de Materiais
                </h2>
                
                <div class="file-upload">
                    <input type="file" id="fileInput" multiple accept=".pdf,.txt,.docx,.jpg,.png">
                    <label for="fileInput" class="file-upload-label">
                        <div class="upload-icon">📤</div>
                        <div>Arraste arquivos aqui ou clique para selecionar</div>
                        <div style="font-size: 0.875rem; color: #94a3b8; margin-top: 8px;">
                            Suporta PDF, DOCX, TXT, imagens
                        </div>
                    </label>
                </div>
                
                <div class="files-list" id="filesList"></div>
            </div>
            
            <div class="card">
                <h2>
                    <div class="icon">⚙️</div>
                    Configurações
                </h2>
                
                <div class="form-group">
                    <label for="aiModel">Modelo de IA</label>
                    <select id="aiModel">
                        <option value="gpt-4-turbo">GPT-4 Turbo (Premium)</option>
                        <option value="claude-3-opus">Claude 3 Opus (Preciso)</option>
                        <option value="gemini-pro">Gemini Pro (Visual)</option>
                        <option value="medical-llm">MedicalLLM (Especializado)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="specialty">Especialidade</label>
                    <select id="specialty">
                        <option value="cardiologia">Cardiologia</option>
                        <option value="neurologia">Neurologia</option>
                        <option value="pediatria">Pediatria</option>
                        <option value="cirurgia">Cirurgia</option>
                        <option value="clinica_medica">Clínica Médica</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="difficulty">Dificuldade (1-10)</label>
                    <input type="range" id="difficulty" min="1" max="10" value="5">
                    <div style="text-align: center; margin-top: 8px;">
                        <span id="difficultyValue">5</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="numQuestions">Número de Questões</label>
                    <input type="number" id="numQuestions" min="1" max="50" value="5">
                </div>
                
                <button class="btn" onclick="generateQuestions()" id="generateBtn">
                    <span>🎯</span>
                    Gerar Questões
                </button>
            </div>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Gerando questões com IA...</div>
            <div id="progress" style="margin-top: 10px; color: #94a3b8;"></div>
        </div>
        
        <div class="results" id="results">
            <h2 style="margin-bottom: 30px;">Questões Geradas</h2>
            <div id="questionsContainer"></div>
        </div>
    </div>
    
    <script>
        // File handling
        const fileInput = document.getElementById('fileInput');
        const filesList = document.getElementById('filesList');
        const uploadedFiles = [];
        
        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            files.forEach(file => {
                uploadedFiles.push(file);
                addFileToList(file);
            });
        });
        
        function addFileToList(file) {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span>${file.name} (${formatFileSize(file.size)})</span>
                <button class="remove-file" onclick="removeFile('${file.name}')">Remover</button>
            `;
            filesList.appendChild(fileItem);
        }
        
        function removeFile(filename) {
            const index = uploadedFiles.findIndex(f => f.name === filename);
            if (index > -1) {
                uploadedFiles.splice(index, 1);
                updateFilesList();
            }
        }
        
        function updateFilesList() {
            filesList.innerHTML = '';
            uploadedFiles.forEach(file => addFileToList(file));
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // Difficulty slider
        const difficultySlider = document.getElementById('difficulty');
        const difficultyValue = document.getElementById('difficultyValue');
        
        difficultySlider.addEventListener('input', (e) => {
            difficultyValue.textContent = e.target.value;
        });
        
        // Statistics
        let stats = {
            totalQuestions: 0,
            avgQuality: 0,
            cacheHits: 0,
            lastResponseTime: 0
        };
        
        function updateStats() {
            document.getElementById('totalQuestions').textContent = stats.totalQuestions;
            document.getElementById('avgQuality').textContent = Math.round(stats.avgQuality) + '%';
            document.getElementById('cacheHits').textContent = stats.cacheHits;
            document.getElementById('responseTime').textContent = stats.lastResponseTime + 'ms';
        }
        
        // Generate questions
        async function generateQuestions() {
            const generateBtn = document.getElementById('generateBtn');
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const progress = document.getElementById('progress');
            
            if (uploadedFiles.length === 0) {
                alert('Por favor, faça upload de pelo menos um arquivo');
                return;
            }
            
            generateBtn.disabled = true;
            loading.classList.add('active');
            results.classList.remove('active');
            
            const startTime = Date.now();
            
            try {
                // Simulate processing
                progress.textContent = 'Analisando arquivos...';
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                progress.textContent = 'Extraindo conceitos médicos...';
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                progress.textContent = 'Gerando questões com IA...';
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Generate mock questions
                const questions = generateMockQuestions();
                
                // Update stats
                stats.lastResponseTime = Date.now() - startTime;
                stats.totalQuestions += questions.length;
                const qualities = questions.map(q => q.quality || 85);
                stats.avgQuality = qualities.reduce((a, b) => a + b, 0) / qualities.length;
                updateStats();
                
                // Display results
                displayQuestions(questions);
                
            } catch (error) {
                alert('Erro ao gerar questões: ' + error.message);
            } finally {
                generateBtn.disabled = false;
                loading.classList.remove('active');
                results.classList.add('active');
            }
        }
        
        function generateMockQuestions() {
            const numQuestions = parseInt(document.getElementById('numQuestions').value);
            const specialty = document.getElementById('specialty').value;
            const difficulty = parseInt(document.getElementById('difficulty').value);
            const aiModel = document.getElementById('aiModel').value;
            
            const questions = [];
            
            for (let i = 1; i <= numQuestions; i++) {
                questions.push({
                    id: i,
                    text: `Questão ${i} sobre ${specialty}: Em relação aos conceitos fundamentais desta especialidade, qual das seguintes afirmações está correta?`,
                    options: [
                        'A) Primeira opção relacionada ao tema',
                        'B) Segunda opção com informação relevante',
                        'C) Terceira alternativa com dado importante',
                        'D) Quarta possibilidade a considerar',
                        'E) Quinta opção com resposta correta'
                    ],
                    correct: 4,
                    explanation: 'A resposta correta é E porque representa o conceito mais adequado segundo as diretrizes atuais.',
                    difficulty: difficulty,
                    quality: Math.random() * 30 + 70,
                    aiModel: aiModel
                });
            }
            
            return questions;
        }
        
        function displayQuestions(questions) {
            const container = document.getElementById('questionsContainer');
            container.innerHTML = '';
            
            questions.forEach((question, index) => {
                const questionCard = document.createElement('div');
                questionCard.className = 'question-card';
                questionCard.innerHTML = `
                    <div class="question-header">
                        <span class="question-number">Questão ${index + 1}</span>
                        <span class="difficulty-badge">Dificuldade: ${question.difficulty}/10</span>
                    </div>
                    <div class="question-text">${question.text}</div>
                    <ul class="options">
                        ${question.options.map((opt, i) => `
                            <li class="option ${i === question.correct ? 'correct' : ''}">
                                ${opt}
                            </li>
                        `).join('')}
                    </ul>
                    <div class="explanation">
                        <strong>Explicação:</strong> ${question.explanation}
                    </div>
                    <div class="quality-score">
                        <span class="score-label">Qualidade:</span>
                        <div class="score-bar">
                            <div class="score-fill" style="width: ${question.quality}%"></div>
                        </div>
                        <span class="score-value">${Math.round(question.quality)}%</span>
                    </div>
                `;
                container.appendChild(questionCard);
            });
        }
        
        // Initialize
        updateStats();
    </script>
</body>
</html>