<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mentor AI - Plataforma Unificada de Teste</title>
    
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #0f172a;
            color: #f1f5f9;
            overflow-x: hidden;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #0f172a;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        .loading-screen.loaded {
            opacity: 0;
            pointer-events: none;
        }

        .loading-logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 20px rgba(99, 102, 241, 0);
            }
        }

        .loading-text {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .loading-progress {
            width: 300px;
            height: 4px;
            background: #1e293b;
            border-radius: 2px;
            overflow: hidden;
        }

        .loading-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
            animation: loadingProgress 2s ease-in-out infinite;
        }

        @keyframes loadingProgress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        /* Quick Start Guide */
        .quick-start {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 16px;
            padding: 1.5rem;
            max-width: 400px;
            z-index: 1000;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s, opacity 0.3s;
        }

        .quick-start.hidden {
            transform: translateX(-120%);
            opacity: 0;
        }

        .quick-start-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .quick-start h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #f1f5f9;
        }

        .quick-start-close {
            background: none;
            border: none;
            color: #94a3b8;
            font-size: 1.25rem;
            cursor: pointer;
            transition: color 0.3s;
        }

        .quick-start-close:hover {
            color: #f1f5f9;
        }

        .quick-start-list {
            list-style: none;
        }

        .quick-start-list li {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            margin-bottom: 1rem;
            color: #94a3b8;
            font-size: 0.875rem;
            line-height: 1.6;
        }

        .quick-start-list i {
            color: #6366f1;
            margin-top: 0.2rem;
            flex-shrink: 0;
        }

        /* Environment Badge */
        .env-badge {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: #ef4444;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 600;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .env-badge i {
            font-size: 1rem;
        }

        /* Demo Data Notice */
        .demo-notice {
            position: fixed;
            top: 4rem;
            right: 1rem;
            background: #f59e0b;
            color: #0f172a;
            padding: 0.75rem 1.25rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            z-index: 999;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        /* System Status */
        .system-status {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 12px;
            padding: 1rem;
            font-size: 0.875rem;
            z-index: 999;
            min-width: 250px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            color: #94a3b8;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }

        .status-dot.warning {
            background: #f59e0b;
        }

        .status-dot.error {
            background: #ef4444;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .quick-start {
                left: 1rem;
                right: 1rem;
                max-width: none;
            }

            .demo-notice {
                top: 5rem;
            }

            .system-status {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-logo">
            <i class="fas fa-graduation-cap"></i>
        </div>
        <div class="loading-text">Inicializando Mentor AI Platform...</div>
        <div class="loading-progress">
            <div class="loading-progress-bar"></div>
        </div>
    </div>

    <!-- Vue App Container -->
    <div id="app"></div>

    <!-- Environment Badge -->
    <div class="env-badge">
        <i class="fas fa-flask"></i>
        <span>AMBIENTE DE TESTE LOCAL</span>
    </div>

    <!-- Demo Data Notice -->
    <div class="demo-notice">
        <strong>Dados de Demonstração:</strong> Esta é uma versão de teste com dados simulados. As funcionalidades de AI estão usando respostas mockadas.
    </div>

    <!-- Quick Start Guide -->
    <div class="quick-start" id="quickStart">
        <div class="quick-start-header">
            <h3>🚀 Guia Rápido</h3>
            <button class="quick-start-close" onclick="hideQuickStart()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <ul class="quick-start-list">
            <li>
                <i class="fas fa-check"></i>
                <span><strong>Dashboard:</strong> Visualize suas estatísticas e atividades recentes</span>
            </li>
            <li>
                <i class="fas fa-check"></i>
                <span><strong>AI Assistant:</strong> Faça upload de documentos e gere questões inteligentes</span>
            </li>
            <li>
                <i class="fas fa-check"></i>
                <span><strong>Ultra Questions:</strong> Use IA avançada para criar questões médicas</span>
            </li>
            <li>
                <i class="fas fa-check"></i>
                <span><strong>Flashcards:</strong> Estude com cartões inteligentes e spaced repetition</span>
            </li>
            <li>
                <i class="fas fa-check"></i>
                <span><strong>Analytics:</strong> Acompanhe seu progresso com análises detalhadas</span>
            </li>
        </ul>
        <p style="margin-top: 1rem; font-size: 0.75rem; color: #64748b;">
            💡 Dica: Use o botão + flutuante para ações rápidas!
        </p>
    </div>

    <!-- System Status -->
    <div class="system-status">
        <div class="status-item">
            <span class="status-label">Frontend</span>
            <div class="status-indicator">
                <span class="status-dot"></span>
                <span>Ativo</span>
            </div>
        </div>
        <div class="status-item">
            <span class="status-label">Backend API</span>
            <div class="status-indicator">
                <span class="status-dot warning"></span>
                <span>Mock Mode</span>
            </div>
        </div>
        <div class="status-item">
            <span class="status-label">AI Services</span>
            <div class="status-indicator">
                <span class="status-dot warning"></span>
                <span>Simulado</span>
            </div>
        </div>
        <div class="status-item">
            <span class="status-label">Database</span>
            <div class="status-indicator">
                <span class="status-dot"></span>
                <span>LocalStorage</span>
            </div>
        </div>
    </div>

    <!-- Vue 3 and Dependencies -->
    <script src="https://unpkg.com/vue@3.4.0/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vue-router@4.2.5/dist/vue-router.global.js"></script>
    <script src="https://unpkg.com/vuex@4.1.0/dist/vuex.global.js"></script>
    <script src="https://unpkg.com/axios@1.6.2/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.js"></script>

    <!-- Mock Services -->
    <script>
        // Mock AI Question Service
        window.AdvancedQuestionService = class {
            async analyzeDocument(file) {
                await new Promise(resolve => setTimeout(resolve, 1500));
                return {
                    topics: [
                        { name: "Cardiologia", relevance: 0.9 },
                        { name: "Farmacologia", relevance: 0.7 },
                        { name: "Fisiologia", relevance: 0.6 }
                    ],
                    complexity: 75,
                    recommendations: [
                        "Adicione mais casos clínicos",
                        "Inclua questões sobre diagnóstico diferencial",
                        "Considere adicionar imagens médicas"
                    ],
                    summary: "Documento analisado com sucesso. Conteúdo rico em conceitos de cardiologia.",
                    key_concepts: ["Insuficiência cardíaca", "Hipertensão", "Arritmias", "Infarto do miocárdio"]
                };
            }

            async analyzeText(text) {
                return this.analyzeDocument(null);
            }

            async generateQuestions(content, config) {
                await new Promise(resolve => setTimeout(resolve, 2000));
                const questions = [];
                const types = config.types || ['multiple-choice'];
                const levels = config.levels || ['intermediate'];
                
                for (let i = 0; i < config.quantity; i++) {
                    questions.push({
                        id: `q_${Date.now()}_${i}`,
                        type: types[i % types.length],
                        level: levels[i % levels.length],
                        text: `Questão ${i + 1}: Qual das seguintes opções melhor descreve o tratamento de primeira linha para hipertensão arterial sistêmica?`,
                        options: [
                            "Inibidores da ECA ou BRA",
                            "Beta-bloqueadores",
                            "Bloqueadores de canais de cálcio",
                            "Diuréticos tiazídicos"
                        ],
                        answer: "A",
                        explanation: "Os inibidores da ECA (como enalapril) ou BRA (como losartana) são considerados primeira linha no tratamento da hipertensão, especialmente em pacientes com diabetes ou doença renal crônica.",
                        reference: "Diretrizes Brasileiras de Hipertensão 2020"
                    });
                }

                return {
                    questions,
                    insights: {
                        coverage: {
                            "Cardiologia": 35,
                            "Farmacologia": 25,
                            "Clínica Médica": 20,
                            "Fisiologia": 15,
                            "Patologia": 5
                        },
                        clarity: 92,
                        relevance: 88,
                        balance: 85,
                        suggestions: [
                            "Adicione mais questões sobre emergências cardiovasculares",
                            "Inclua casos clínicos complexos",
                            "Considere questões com interpretação de ECG"
                        ]
                    }
                };
            }

            async exportQuestions(questions, format) {
                console.log(`Exportando ${questions.length} questões em formato ${format}`);
                alert(`Exportação simulada: ${questions.length} questões seriam exportadas em formato ${format.toUpperCase()}`);
            }
        };

        // Mock Ultra Question Service
        window.UltraQuestionService = class {
            async processFiles(files) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                return {
                    content: `Conteúdo processado de ${files.length} arquivo(s)`,
                    metadata: {
                        totalWords: 5420,
                        medicalTerms: ["hipertensão", "diabetes", "infarto", "arritmia"],
                        specialtyDetected: "cardiologia"
                    }
                };
            }

            async generateQuestions(content, config) {
                const response = [];
                for (let i = 0; i < 5; i++) {
                    response.push(JSON.stringify({
                        question: {
                            id: i + 1,
                            text: `Questão Ultra ${i + 1}: Paciente de 65 anos com histórico de...`,
                            type: config.questionTypes[0] || 'multiple_choice',
                            difficulty: config.difficulty || 5,
                            options: ["Opção A", "Opção B", "Opção C", "Opção D"],
                            correct: "A",
                            explanation: "Explicação detalhada da resposta...",
                            quality_analysis: {
                                overall_score: 0.85,
                                clarity: 0.9,
                                relevance: 0.88
                            }
                        },
                        progress: (i + 1) / 5
                    }));
                }
                return response;
            }
        };

        // Hide loading screen after a delay
        setTimeout(() => {
            document.getElementById('loadingScreen').classList.add('loaded');
        }, 2000);

        // Quick Start functions
        function hideQuickStart() {
            document.getElementById('quickStart').classList.add('hidden');
            localStorage.setItem('quickStartHidden', 'true');
        }

        // Check if quick start should be hidden
        if (localStorage.getItem('quickStartHidden') === 'true') {
            document.getElementById('quickStart').classList.add('hidden');
        }
    </script>

    <!-- Main Application Script -->
    <script type="module">
        import UnifiedPlatform from './src/components/UnifiedPlatform.vue';
        import AIAssistantAdvanced from './src/components/AIAssistantAdvanced.vue';
        import AIQuestionGeneratorUltra from './src/components/AIQuestionGeneratorUltra.vue';
        import FlashcardsPage from './src/components/FlashcardsPageElegant.vue';
        import AdvancedAnalyticsDashboard from './src/components/AdvancedAnalyticsDashboard.vue';

        const { createApp } = Vue;
        const { createRouter, createWebHistory } = VueRouter;
        const { createStore } = Vuex;

        // Vuex Store
        const store = createStore({
            modules: {
                auth: {
                    namespaced: true,
                    state: {
                        isAuthenticated: true,
                        user: {
                            id: 1,
                            name: 'Usuário Teste',
                            email: '<EMAIL>'
                        }
                    },
                    getters: {
                        isAuthenticated: state => state.isAuthenticated,
                        currentUser: state => state.user
                    }
                },
                flashcards: {
                    namespaced: true,
                    state: {
                        decks: [],
                        currentDeck: null
                    },
                    actions: {
                        async fetchDecks() {
                            // Mock implementation
                            return [];
                        }
                    }
                }
            }
        });

        // Router
        const router = createRouter({
            history: createWebHistory(),
            routes: [
                {
                    path: '/',
                    redirect: '/platform'
                },
                {
                    path: '/platform',
                    component: UnifiedPlatform
                }
            ]
        });

        // Create App
        const app = createApp({
            template: '<router-view></router-view>'
        });

        // Register components globally
        app.component('UnifiedPlatform', UnifiedPlatform);
        app.component('AIAssistantAdvanced', AIAssistantAdvanced);
        app.component('AIQuestionGeneratorUltra', AIQuestionGeneratorUltra);
        app.component('FlashcardsPage', FlashcardsPage);
        app.component('AdvancedAnalyticsDashboard', AdvancedAnalyticsDashboard);

        // Use plugins
        app.use(store);
        app.use(router);

        // Mount app
        app.mount('#app');

        // Log success
        console.log('✅ Mentor AI Platform iniciada com sucesso!');
        console.log('📍 Navegue para diferentes seções usando as abas no header');
        console.log('🚀 Use o botão + flutuante para ações rápidas');
    </script>
</body>
</html>