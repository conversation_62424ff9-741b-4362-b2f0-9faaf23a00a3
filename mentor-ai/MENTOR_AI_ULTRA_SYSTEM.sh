#!/bin/bash

# ===================================================================
# MENTOR AI - ULTRA SYSTEM SCRIPT INTEGRADO
# Versão: Ultra-4.0.0 (Titanic Plus Edition)
# ===================================================================

# Cores para melhorar a visualização
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
ORANGE='\033[0;33m'
BOLD='\033[1m'
NC='\033[0m'

# Definir diretórios principais
BASE_DIR="$(cd "$(dirname "$0")" && pwd)"
DJANGO_DIR="$BASE_DIR/backend/django_app"
FASTAPI_DIR="$BASE_DIR/backend/fastapi_app"
FRONTEND_DIR="$BASE_DIR/frontend"
LOG_DIR="$BASE_DIR/logs"
PID_FILE="$BASE_DIR/.ultra_pids"
TEMP_DIR="$BASE_DIR/temp"

# ===================================================================
# FUNÇÕES UTILITÁRIAS
# ===================================================================

show_header() {
    echo -e "${BLUE}=====================================================================${NC}"
    echo -e "${BOLD}${BLUE}||             MENTOR AI - ULTRA SYSTEM INTEGRADO             ||${NC}"
    echo -e "${BLUE}=====================================================================${NC}"
    echo -e "${PURPLE}Versão Ultra-4.0.0 (Titanic Plus Edition) - $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo -e "${BLUE}=====================================================================${NC}\n"
}

show_menu() {
    echo -e "${YELLOW}MENU DE OPERAÇÕES:${NC}"
    echo -e "${CYAN}1)${NC} Verificar status do sistema"
    echo -e "${CYAN}2)${NC} Iniciar todos os serviços"
    echo -e "${CYAN}3)${NC} Corrigir FastAPI (rápido)"
    echo -e "${CYAN}4)${NC} Corrigir FastAPI (limpeza completa)"
    echo -e "${CYAN}5)${NC} Parar todos os serviços"
    echo -e "${CYAN}6)${NC} Iniciar apenas o FastAPI"
    echo -e "${CYAN}7)${NC} Iniciar apenas o Django"
    echo -e "${CYAN}8)${NC} Iniciar apenas o Frontend"
    echo -e "${CYAN}9)${NC} Executar ultra.sh completo"
    echo -e "${CYAN}0)${NC} Sair"
    echo -e "${YELLOW}----------------------------------------${NC}"
    echo -ne "${BOLD}Escolha uma opção:${NC} "
    read -r option
    
    return $option
}

show_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

show_error() {
    echo -e "${RED}✗ $1${NC}"
}

show_warning() {
    echo -e "${ORANGE}⚠ $1${NC}"
}

show_info() {
    echo -e "${CYAN}ℹ $1${NC}"
}

# ===================================================================
# VERIFICAR STATUS DO SISTEMA
# ===================================================================

check_system_status() {
    echo -e "\n${BOLD}${YELLOW}VERIFICANDO STATUS DO SISTEMA MENTOR AI${NC}"
    echo -e "${YELLOW}=========================================${NC}"

    # Check PostgreSQL
    echo -ne "${BOLD}PostgreSQL:${NC} "
    if pg_isready -h localhost > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Rodando${NC}"
    else
        echo -e "${RED}❌ Parado${NC}"
    fi

    # Check Django
    echo -ne "${BOLD}Django (8003):${NC} "
    if curl -s http://localhost:8003/ > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Rodando${NC}"
    else
        echo -e "${RED}❌ Parado${NC}"
    fi

    # Check FastAPI
    echo -ne "${BOLD}FastAPI (8001):${NC} "
    if curl -s http://localhost:8001/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Rodando${NC}"
    else
        echo -e "${RED}❌ Parado${NC}"
    fi

    # Check Frontend
    echo -ne "${BOLD}Frontend (8082):${NC} "
    if curl -s http://localhost:8082 > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Rodando${NC}"
    else
        echo -e "${RED}❌ Parado${NC}"
    fi

    echo -e "\n${BOLD}${YELLOW}STATUS DOS SERVIÇOS DE IA:${NC}"
    echo -e "${YELLOW}-------------------------${NC}"
    
    # Check Second Brain
    echo -ne "${BOLD}Second Brain Chat:${NC} "
    if curl -s "http://localhost:8001/api/chat/test" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Disponível${NC}"
    else
        echo -e "${RED}❌ Indisponível${NC}"
    fi

    # Check Thanos
    echo -ne "${BOLD}Thanos AI:${NC} "
    if curl -s http://localhost:8001/api/thanos/health | grep -q "healthy" 2>/dev/null; then
        echo -e "${GREEN}✅ Disponível${NC}"
    else
        echo -e "${RED}❌ Indisponível${NC}"
    fi

    echo -e "\n${BOLD}${YELLOW}PONTOS DE ACESSO:${NC}"
    echo -e "${YELLOW}----------------${NC}"
    echo -e "${BOLD}Aplicação Principal:${NC} ${CYAN}http://localhost:8082${NC}"
    echo -e "${BOLD}Documentação API:${NC} ${CYAN}http://localhost:8001/docs${NC}"
    echo -e "${BOLD}Django Admin:${NC} ${CYAN}http://localhost:8003/admin/${NC}"
    echo -e "${BOLD}Second Brain:${NC} ${CYAN}http://localhost:8082/second-brain${NC}"
    echo -e "${BOLD}Thanos:${NC} ${CYAN}http://localhost:8082/thanos${NC}"

    echo -e "\n${BOLD}${YELLOW}PROCESSOS EM EXECUÇÃO:${NC}"
    echo -e "${YELLOW}---------------------${NC}"
    ps aux | grep -E "python.*manage.py|uvicorn|integrated_ai_main|npm.*serve" | grep -v grep | awk '{print "- " $11 " (PID: " $2 ")"}'
}

# ===================================================================
# PARAR TODOS OS SERVIÇOS
# ===================================================================

stop_all_services() {
    echo -e "\n${BOLD}${YELLOW}PARANDO TODOS OS SERVIÇOS MENTOR AI${NC}"
    echo -e "${YELLOW}===================================${NC}"
    
    # Kill ALL processes
    echo -e "${CYAN}Encerrando todos os processos...${NC}"
    pkill -f "python manage.py runserver" 2>/dev/null || true
    pkill -f "uvicorn" 2>/dev/null || true
    pkill -f "python.*main.py" 2>/dev/null || true
    pkill -f "integrated_ai_main" 2>/dev/null || true
    pkill -f "npm run serve" 2>/dev/null || true
    
    # Kill by PID file
    if [[ -f "$PID_FILE" ]]; then
        while read -r pid_line; do
            pid=$(echo "$pid_line" | cut -d':' -f2)
            if [[ -n "$pid" ]] && kill -0 "$pid" 2>/dev/null; then
                kill -9 "$pid" 2>/dev/null
                echo -e "${CYAN}ℹ Processo $pid encerrado${NC}"
            fi
        done < "$PID_FILE"
        rm -f "$PID_FILE"
    fi
    
    # Limpar arquivos de PID individuais
    rm -f "$BASE_DIR/.django.pid" "$BASE_DIR/.fastapi.pid" "$BASE_DIR/.frontend.pid"
    
    # Kill processes on specific ports
    for port in 8001 8003 8082; do
        if lsof -i:$port > /dev/null 2>&1; then
            echo -e "${YELLOW}⚠ Porta $port ainda em uso. Forçando liberação...${NC}"
            lsof -t -i:$port | xargs kill -9 2>/dev/null || true
        else
            echo -e "${GREEN}✓ Porta $port liberada${NC}"
        fi
    done
    
    show_success "Todos os serviços encerrados com sucesso"
}

# ===================================================================
# CORRIGIR FASTAPI (RÁPIDO)
# ===================================================================

fix_fastapi_quick() {
    echo -e "\n${BOLD}${YELLOW}CORRIGINDO FASTAPI (MODO RÁPIDO)${NC}"
    echo -e "${YELLOW}=============================${NC}"
    
    # Create necessary directories
    mkdir -p "$BASE_DIR/backend/logs"
    mkdir -p "$BASE_DIR/backend/fastapi_app/logs"
    mkdir -p "$LOG_DIR"
    
    # Kill existing FastAPI processes
    echo -e "${CYAN}Encerrando processos FastAPI existentes...${NC}"
    pkill -f "uvicorn" 2>/dev/null || true
    pkill -f "python.*main.py" 2>/dev/null || true
    pkill -f "integrated_ai_main" 2>/dev/null || true
    
    # Kill processes on port 8001
    if lsof -i:8001 > /dev/null 2>&1; then
        echo -e "${CYAN}Matando processos na porta 8001...${NC}"
        lsof -t -i:8001 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # Check if port is free
    if lsof -i:8001 > /dev/null 2>&1; then
        show_error "Porta 8001 ainda está em uso! Não foi possível liberar."
        lsof -i:8001
        return 1
    fi
    
    # Set environment variables
    export PYTHONPATH="$BASE_DIR/backend:$PYTHONPATH"
    export DATABASE_URL="postgresql://localhost/mentor_ai"
    
    # Go to FastAPI directory
    cd "$FASTAPI_DIR"
    
    # Check if main.py exists
    if [ ! -f "main.py" ]; then
        show_error "main.py não encontrado em $FASTAPI_DIR"
        return 1
    fi
    
    # Start FastAPI with uvicorn
    show_info "Iniciando FastAPI com uvicorn..."
    nohup uvicorn main:app --host 0.0.0.0 --port 8001 --reload > "$LOG_DIR/fastapi.log" 2>&1 &
    FASTAPI_PID=$!
    echo $FASTAPI_PID > "$BASE_DIR/.fastapi.pid"
    echo "fastapi:$FASTAPI_PID" >> "$PID_FILE"
    show_info "FastAPI PID: $FASTAPI_PID"
    
    sleep 5
    
    # Test if it's working
    echo -e "\n${CYAN}Testando FastAPI...${NC}"
    if curl -s http://localhost:8001/health > /dev/null; then
        show_success "FastAPI está rodando!"
        echo -e "${CYAN}Pontos de acesso:${NC}"
        echo -e "   - API Docs: ${CYAN}http://localhost:8001/docs${NC}"
        echo -e "   - Health: ${CYAN}http://localhost:8001/health${NC}"
        echo -e "   - Flashcards: ${CYAN}http://localhost:8001/api/flashcards${NC}"
        echo -e "   - Analytics: ${CYAN}http://localhost:8001/api/analytics${NC}"
        echo -e "   - Performance: ${CYAN}http://localhost:8001/api/performance${NC}"
        echo -e "\n${CYAN}Para ver logs:${NC} tail -f $LOG_DIR/fastapi.log"
    else
        show_error "FastAPI não está respondendo. Verificando logs:"
        tail -n 20 "$LOG_DIR/fastapi.log"
        echo -e "\n${YELLOW}Para ver logs completos:${NC} tail -f $LOG_DIR/fastapi.log"
        return 1
    fi
    
    return 0
}

# ===================================================================
# CORRIGIR FASTAPI (LIMPEZA COMPLETA)
# ===================================================================

fix_fastapi_full() {
    echo -e "\n${BOLD}${YELLOW}CORRIGINDO FASTAPI (LIMPEZA COMPLETA)${NC}"
    echo -e "${YELLOW}=================================${NC}"
    
    # Create necessary directories
    mkdir -p "$BASE_DIR/backend/logs"
    mkdir -p "$BASE_DIR/backend/fastapi_app/logs"
    mkdir -p "$LOG_DIR"
    
    # Kill ALL FastAPI/uvicorn processes
    echo -e "${CYAN}Parando todos os processos FastAPI...${NC}"
    pkill -f "uvicorn" 2>/dev/null || true
    pkill -f "python.*main.py" 2>/dev/null || true
    pkill -f "integrated_ai_main" 2>/dev/null || true
    
    # Kill processes on port 8001
    if lsof -i:8001 > /dev/null 2>&1; then
        echo -e "${CYAN}Matando processos na porta 8001...${NC}"
        lsof -t -i:8001 | xargs kill -9 2>/dev/null || true
    fi
    
    sleep 2
    
    # Check if port is free
    if lsof -i:8001 > /dev/null 2>&1; then
        show_error "Porta 8001 ainda está em uso!"
        lsof -i:8001
        return 1
    fi
    
    show_success "Porta 8001 está livre"
    
    # Go to backend directory
    cd "$BASE_DIR/backend"
    
    # Check for integrated_ai_main.py file
    if [ ! -f "integrated_ai_main.py" ]; then
        show_warning "integrated_ai_main.py não encontrado. Tentando criar um básico..."
        
        cat > integrated_ai_main.py << EOF
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
import sys
import os
from pathlib import Path
import datetime

# Add parent directory to Python path
sys.path.append(str(Path(__file__).parent))

# Configure logging to console only (avoid file issues)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("fastapi_app")

app = FastAPI(
    title="MentorAI Ultra API",
    description="API do sistema MentorAI Ultra Robust Edition with AI Integration",
    version="4.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    logger.info("Root endpoint accessed")
    return {
        "message": "MentorAI FastAPI with AI Integration", 
        "version": "4.0.0",
        "ai_services": ["second-brain", "thanos", "flashcards"]
    }

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "mentor-ai-fastapi"}

@app.get("/api/thanos/health")
async def thanos_health():
    logger.info("Thanos health check requested")
    return {
        "status": "healthy", 
        "version": "4.0.0",
        "services": {
            "database": "online",
            "embeddings": "ready",
            "llm": "ready"
        }
    }

@app.get("/api/chat/test")
async def test_chat():
    return "Test stream working at " + str(datetime.datetime.now())

# Import routers with error handling
try:
    from fastapi_app.routers import basic_router
    
    app.include_router(basic_router.router)
    logger.info("Basic router loaded successfully")
    
    # Try to load other routers
    try:
        from fastapi_app.routers import flashcards, second_brain, mentorship
        from fastapi_app.routers.thanos import router as thanos_router
        
        app.include_router(flashcards.router, prefix="/api", tags=["flashcards"])
        app.include_router(second_brain.router, prefix="/api", tags=["second-brain"])
        app.include_router(mentorship.router, prefix="/api", tags=["mentorship"])
        app.include_router(thanos_router, prefix="/api/thanos", tags=["thanos"])
        
        # Try to load chat router
        try:
            from fastapi_app.routers import chat
            app.include_router(chat.router, tags=["chat"])
            logger.info("Chat router loaded successfully")
        except ImportError:
            logger.warning("Chat router could not be loaded")
        
        logger.info("All routers loaded successfully")
    except ImportError as e:
        logger.warning(f"Some routers could not be loaded: {e}")
except Exception as e:
    logger.error(f"Error loading routers: {e}")

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8001))
    uvicorn.run("integrated_ai_main:app", host="0.0.0.0", port=port, reload=True)
EOF

        show_success "Arquivo integrated_ai_main.py criado"
    else
        show_success "Arquivo integrated_ai_main.py encontrado"
    fi
    
    # Ask user if they want to run in foreground or background
    echo -e "\n${YELLOW}Como deseja iniciar o FastAPI?${NC}"
    echo -e "${CYAN}1)${NC} Em foreground (para ver logs em tempo real)"
    echo -e "${CYAN}2)${NC} Em background (para continuar usando o terminal)"
    echo -ne "${BOLD}Escolha uma opção:${NC} "
    read -r run_option
    
    if [ "$run_option" = "1" ]; then
        echo -e "\n${CYAN}Iniciando FastAPI em modo foreground...${NC}"
        echo -e "${YELLOW}Pressione Ctrl+C para parar quando verificar que está funcionando${NC}"
        echo -e "${YELLOW}-------------------------------------------------${NC}"
        
        if [ -f "integrated_ai_main.py" ]; then
            echo -e "${CYAN}Usando integrated_ai_main.py...${NC}"
            python integrated_ai_main.py
        else
            cd fastapi_app
            echo -e "${CYAN}Usando main.py padrão...${NC}"
            python main.py
        fi
    else
        echo -e "\n${CYAN}Iniciando FastAPI em modo background...${NC}"
        
        if [ -f "integrated_ai_main.py" ]; then
            echo -e "${CYAN}Usando integrated_ai_main.py...${NC}"
            nohup python integrated_ai_main.py > "$LOG_DIR/fastapi_integrated.log" 2>&1 &
            FASTAPI_PID=$!
            echo $FASTAPI_PID > "$BASE_DIR/.fastapi.pid"
            echo "fastapi:$FASTAPI_PID" >> "$PID_FILE"
            show_success "FastAPI iniciado em background (PID: $FASTAPI_PID)"
            
            # Wait and check if it's still running
            sleep 5
            if kill -0 $FASTAPI_PID 2>/dev/null; then
                show_success "FastAPI está rodando corretamente"
                echo -e "${CYAN}Para ver logs:${NC} tail -f $LOG_DIR/fastapi_integrated.log"
            else
                show_error "FastAPI falhou ao iniciar. Verificando logs:"
                tail -n 20 "$LOG_DIR/fastapi_integrated.log"
            fi
        else
            cd fastapi_app
            echo -e "${CYAN}Usando main.py padrão...${NC}"
            nohup python main.py > "$LOG_DIR/fastapi_default.log" 2>&1 &
            FASTAPI_PID=$!
            echo $FASTAPI_PID > "$BASE_DIR/.fastapi.pid"
            echo "fastapi:$FASTAPI_PID" >> "$PID_FILE"
            show_success "FastAPI iniciado em background (PID: $FASTAPI_PID)"
            
            # Wait and check if it's still running
            sleep 5
            if kill -0 $FASTAPI_PID 2>/dev/null; then
                show_success "FastAPI está rodando corretamente"
                echo -e "${CYAN}Para ver logs:${NC} tail -f $LOG_DIR/fastapi_default.log"
            else
                show_error "FastAPI falhou ao iniciar. Verificando logs:"
                tail -n 20 "$LOG_DIR/fastapi_default.log"
            fi
        fi
    fi
}

# ===================================================================
# INICIAR DJANGO
# ===================================================================

start_django() {
    echo -e "\n${BOLD}${YELLOW}INICIANDO DJANGO${NC}"
    echo -e "${YELLOW}================${NC}"
    
    # Kill existing Django processes
    pkill -f "python manage.py runserver" 2>/dev/null || true
    
    # Check if port is in use
    if lsof -i:8003 > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠ Porta 8003 já está em uso. Tentando liberar...${NC}"
        lsof -t -i:8003 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # Go to Django directory
    cd "$DJANGO_DIR"
    
    # Start Django
    show_info "Iniciando servidor Django..."
    nohup python manage.py runserver 0.0.0.0:8003 > "$LOG_DIR/django.log" 2>&1 &
    DJANGO_PID=$!
    echo $DJANGO_PID > "$BASE_DIR/.django.pid"
    echo "django:$DJANGO_PID" >> "$PID_FILE"
    
    sleep 3
    
    # Check if it's running
    if kill -0 $DJANGO_PID 2>/dev/null; then
        show_success "Django iniciado com sucesso (PID: $DJANGO_PID)"
        echo -e "${CYAN}Django está disponível em:${NC} http://localhost:8003/"
        echo -e "${CYAN}Django Admin:${NC} http://localhost:8003/admin/"
        echo -e "${CYAN}Para ver logs:${NC} tail -f $LOG_DIR/django.log"
    else
        show_error "Django falhou ao iniciar. Verificando logs:"
        tail -n 20 "$LOG_DIR/django.log"
    fi
}

# ===================================================================
# INICIAR FRONTEND
# ===================================================================

start_frontend() {
    echo -e "\n${BOLD}${YELLOW}INICIANDO FRONTEND${NC}"
    echo -e "${YELLOW}=================${NC}"
    
    # Kill existing Frontend processes
    pkill -f "npm run serve" 2>/dev/null || true
    
    # Check if port is in use
    if lsof -i:8082 > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠ Porta 8082 já está em uso. Tentando liberar...${NC}"
        lsof -t -i:8082 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # Go to Frontend directory
    cd "$FRONTEND_DIR"
    
    # Start Frontend
    show_info "Iniciando servidor Frontend..."
    nohup npm run serve > "$LOG_DIR/frontend.log" 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$BASE_DIR/.frontend.pid"
    echo "frontend:$FRONTEND_PID" >> "$PID_FILE"
    
    sleep 5
    
    # Check if it's running
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        show_success "Frontend iniciado com sucesso (PID: $FRONTEND_PID)"
        echo -e "${CYAN}Frontend está disponível em:${NC} http://localhost:8082/"
        echo -e "${CYAN}Para ver logs:${NC} tail -f $LOG_DIR/frontend.log"
    else
        show_error "Frontend falhou ao iniciar. Verificando logs:"
        tail -n 20 "$LOG_DIR/frontend.log"
    fi
}

# ===================================================================
# INICIAR TODOS OS SERVIÇOS
# ===================================================================

start_all_services() {
    echo -e "\n${BOLD}${YELLOW}INICIANDO TODOS OS SERVIÇOS MENTOR AI${NC}"
    echo -e "${YELLOW}===================================${NC}"
    
    # Primeiro, parar todos os serviços para garantir limpeza
    stop_all_services
    
    # Iniciar PostgreSQL (se não estiver rodando)
    echo -ne "${BOLD}PostgreSQL:${NC} "
    if ! pg_isready -h localhost > /dev/null 2>&1; then
        echo -e "${YELLOW}Iniciando PostgreSQL...${NC}"
        brew services start postgresql@14 2>/dev/null || true
        sleep 3
        if pg_isready -h localhost > /dev/null 2>&1; then
            show_success "PostgreSQL iniciado com sucesso"
        else
            show_error "Falha ao iniciar PostgreSQL"
        fi
    else
        show_success "PostgreSQL já está rodando"
    fi
    
    # Iniciar Django
    start_django
    
    # Iniciar FastAPI
    fix_fastapi_quick
    
    # Iniciar Frontend
    start_frontend
    
    echo -e "\n${BOLD}${GREEN}TODOS OS SERVIÇOS MENTOR AI INICIADOS!${NC}"
    echo -e "${YELLOW}----------------------------------${NC}"
    echo -e "${CYAN}Acesse a aplicação em:${NC} http://localhost:8082/"
    echo -e "${CYAN}Documentação API:${NC} http://localhost:8001/docs"
    echo -e "${CYAN}Django Admin:${NC} http://localhost:8003/admin/"
}

# ===================================================================
# EXECUTAR ULTRA.SH COMPLETO
# ===================================================================

run_ultra_script() {
    echo -e "\n${BOLD}${YELLOW}EXECUTANDO ULTRA.SH COMPLETO${NC}"
    echo -e "${YELLOW}==========================${NC}"
    
    if [ -f "$BASE_DIR/ultra.sh" ]; then
        show_info "Executando script ultra.sh..."
        bash "$BASE_DIR/ultra.sh"
    else
        show_error "Script ultra.sh não encontrado em $BASE_DIR"
    fi
}

# ===================================================================
# FUNÇÃO PRINCIPAL
# ===================================================================

main() {
    # Criar diretórios necessários
    mkdir -p "$LOG_DIR" "$TEMP_DIR"
    mkdir -p "$BASE_DIR/backend/logs"
    mkdir -p "$BASE_DIR/backend/fastapi_app/logs"
    
    # Mostrar cabeçalho
    show_header
    
    # Menu principal
    while true; do
        show_menu
        option=$?
        
        case $option in
            1) check_system_status ;;
            2) start_all_services ;;
            3) fix_fastapi_quick ;;
            4) fix_fastapi_full ;;
            5) stop_all_services ;;
            6) fix_fastapi_quick ;;
            7) start_django ;;
            8) start_frontend ;;
            9) run_ultra_script ;;
            0) 
                echo -e "\n${GREEN}Obrigado por usar o MENTOR AI ULTRA SYSTEM! 👋${NC}"
                exit 0 
                ;;
            *) 
                echo -e "\n${RED}Opção inválida!${NC}"
                ;;
        esac
        
        echo -e "\n${YELLOW}Pressione ENTER para continuar...${NC}"
        read -r
    done
}

# Executar função principal
main 