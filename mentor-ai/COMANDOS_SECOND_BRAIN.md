# Comandos para Executar o Second Brain com Revisão Espaçada

## 1. Verificar Status dos Serviços

```bash
# Verificar se os serviços estão rodando
ps aux | grep -E "(django|fastapi|npm|node)" | grep -v grep
```

## 2. <PERSON><PERSON> (se necessário)

```bash
# Parar todos os serviços
./stop_all.sh

# Ou manualmente
pkill -f "python manage.py"
pkill -f "uvicorn"
pkill -f "npm run serve"
```

## 3. Iniciar Ser<PERSON>os

```bash
# Backend Django (porta 8003)
cd backend
python manage.py runserver 0.0.0.0:8003 &

# Backend FastAPI (porta 8001)
uvicorn fastapi_app.main:app --reload --port 8001 --host 0.0.0.0 &

# Frontend Vue (porta 8082)
cd ../frontend
npm run serve &
```

## 4. Testar o Sistema

### Via Terminal:
```bash
# Testar endpoint do Second Brain
curl -X POST http://localhost:8001/api/api/second_brain/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "acertei 75% das questões na revisão"}'
```

### Via Navegador:
```bash
# Abrir o Second Brain
open http://localhost:8082/second-brain
```

## 5. Verificar Logs

```bash
# Ver logs do frontend
tail -f frontend/npm-debug.log

# Ver logs do FastAPI
tail -f backend/fastapi.log

# Ver logs do Django
tail -f backend/django.log
```

## Mudanças Implementadas

### MockAIService.js
- Adicionada detecção de perguntas sobre revisão espaçada
- Implementado cálculo correto dos intervalos (2-60 dias)
- Resposta formatada com data e recomendações

### SecondBrain.vue
- Integrado MockAIService para processar respostas
- Mantida compatibilidade com streaming

## Teste de Revisão Espaçada

No chat do Second Brain, teste com estas mensagens:
- "Acertei 50% das questões" → Próxima revisão em 2 dias
- "Tive 75% de acertos" → Próxima revisão em 24 dias
- "Consegui 95% de aproveitamento" → Próxima revisão em 60 dias

## Script Completo de Reinicialização

```bash
#!/bin/bash
# Salve como restart_second_brain.sh

echo "Parando serviços..."
./stop_all.sh

echo "Aguardando..."
sleep 2

echo "Iniciando Django..."
cd backend
python manage.py runserver 0.0.0.0:8003 &
echo $! > ../.django.pid

echo "Iniciando FastAPI..."
uvicorn fastapi_app.main:app --reload --port 8001 --host 0.0.0.0 &
echo $! > ../.fastapi.pid

echo "Iniciando Frontend..."
cd ../frontend
npm run serve &
echo $! > ../.frontend.pid

echo "Serviços iniciados!"
echo "Second Brain disponível em: http://localhost:8082/second-brain"
```

## Troubleshooting

Se o sistema não calcular corretamente as revisões:
1. Verifique se o MockAIService.js foi atualizado
2. Limpe o cache do navegador (Cmd+Shift+R)
3. Verifique o console do navegador para erros
4. Confirme que está usando a porta correta (8082)